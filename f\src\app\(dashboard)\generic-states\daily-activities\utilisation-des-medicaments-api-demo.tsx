'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { UtilisationDesMedicamentsAPI } from './Utilisation_des_medicaments_api';

export default function UtilisationDesMedicamentsApiDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Période sélectionnée: Du ${query.start.toLocaleDateString()} au ${query.end.toLocaleDateString()}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
    const stateMessages: { [key: string]: string } = {
      'latest_prescriptions': 'Dernières prescriptions sélectionnées'
    };
    alert(stateMessages[state.name] || 'État sélectionné');
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    if (filter.showAdvancedFilter !== undefined) {
      alert(`Filtre avancé: ${filter.showAdvancedFilter ? 'Activé' : 'Désactivé'}`);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} de l'utilisation des médicaments en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression de l\'utilisation des médicaments en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'medicine_name_sum': 'Nom des médicaments'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function UtilisationDesMedicamentsApiLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={true}
          onQueryChange={(query) => console.log('Query:', query)}
          onStateChange={(state) => console.log('State:', state)}
          onFilterChange={(filter) => console.log('Filter:', filter)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
          onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function UtilisationDesMedicamentsApiWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    alert(`Utilisation des médicaments du ${startDate} au ${endDate}:\n- Paracétamol: 45 utilisations\n- Ibuprofène: 32 utilisations\n- Amoxicilline: 28 utilisations`);
  };

  const handleStateChange = (state: any) => {
    console.log('État avec données:', state);
    
    const stateData: { [key: string]: any } = {
      'latest_prescriptions': {
        title: 'Dernières prescriptions',
        data: 'Médicaments prescrits récemment, ordonnances en cours'
      }
    };
    
    const data = stateData[state.name];
    if (data) {
      alert(`${data.title}:\n${data.data}`);
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé:\n- Filtrage par nom commercial\n- Filtrage par date\n- Filtrage par patient');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} de l'utilisation des médicaments avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression de l\'utilisation des médicaments avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'medicine_name_sum': 'Tri des noms de médicaments'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode prescriptions
export function UtilisationDesMedicamentsApiPrescriptionsDemo() {
  const handleStateChange = (state: any) => {
    console.log('Mode prescriptions:', state);
    if (state.type === 'latest_prescriptions') {
      alert('Mode Dernières prescriptions activé:\n- Médicaments récemment prescrits\n- Ordonnances en cours\n- Prescriptions validées\n- Historique récent');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre prescriptions:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre prescriptions:\n- Nom commercial du médicament\n- Date de prescription\n- Patient prescrit');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={(query) => console.log('Query prescriptions:', query)}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données de prescriptions`)}
          onPrint={() => alert('Impression des données de prescriptions')}
          onSort={(columnId, direction) => console.log('Sort prescriptions:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function UtilisationDesMedicamentsApiErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    const diffDays = Math.abs(query.end - query.start) / (1000 * 60 * 60 * 24);
    if (diffDays > 365) {
      alert('Attention: Période trop longue (> 1 an). Les performances peuvent être affectées.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={(state) => console.log('State avec validation:', state)}
          onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
          onExport={(format) => {
            console.log(`Export ${format} avec validation`);
            if (confirm(`Êtes-vous sûr de vouloir exporter l'utilisation des médicaments en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onPrint={() => {
            console.log('Impression avec validation');
            if (confirm('Êtes-vous sûr de vouloir imprimer l\'utilisation des médicaments ?')) {
              alert('Impression en cours...');
            }
          }}
          onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données pharmaceutiques
export function UtilisationDesMedicamentsApiPharmaceuticalDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête pharmaceutique:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    alert(`Analyse pharmaceutique du ${startDate} au ${endDate}:\n- Antibiotiques: 15%\n- Antalgiques: 35%\n- Anti-inflammatoires: 20%\n- Autres: 30%`);
  };

  const handleStateChange = (state: any) => {
    console.log('État pharmaceutique:', state);
    if (state.type === 'latest_prescriptions') {
      alert('Analyse des dernières prescriptions:\n- Tendances de prescription\n- Médicaments les plus utilisés\n- Efficacité thérapeutique\n- Suivi des traitements');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre pharmaceutique:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre pharmaceutique:\n- Classe thérapeutique\n- Principe actif\n- Laboratoire pharmaceutique');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} de l'analyse pharmaceutique`)}
          onPrint={() => alert('Impression de l\'analyse pharmaceutique')}
          onSort={(columnId, direction) => console.log('Sort pharmaceutique:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec statistiques médicales
export function UtilisationDesMedicamentsApiMedicalStatsDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête statistiques médicales:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    alert(`Statistiques médicales du ${startDate} au ${endDate}:\n- Total prescriptions: 1,245\n- Patients traités: 892\n- Médicaments différents: 156\n- Coût moyen: 45.67€`);
  };

  const handleStateChange = (state: any) => {
    console.log('État statistiques:', state);
    if (state.type === 'latest_prescriptions') {
      alert('Statistiques des prescriptions:\n- Fréquence de prescription\n- Durée moyenne des traitements\n- Taux d\'observance\n- Effets indésirables');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <UtilisationDesMedicamentsAPI
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={(filter) => console.log('Filter statistiques:', filter)}
          onExport={(format) => alert(`Export ${format} des statistiques médicales`)}
          onPrint={() => alert('Impression des statistiques médicales')}
          onSort={(columnId, direction) => console.log('Sort statistiques:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}
