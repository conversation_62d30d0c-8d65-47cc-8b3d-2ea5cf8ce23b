


import { useTranslation } from '~/i18n/client';
import { useState, useCallback } from 'react';
import {
  useMantineColorScheme,
  Menu,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import {
  IconCircleHalf2,
  IconMoonStars,
  IconSunHigh,
} from "@tabler/icons-react";

const ICON_SIZE = 20;

const SwitchColorModes = () => {
  const { colorScheme, setColorScheme } = useMantineColorScheme();
  const { t } = useTranslation('menu');
  const [isChanging, setIsChanging] = useState(false);

  const handleColorSchemeChange = useCallback(async (newScheme: 'light' | 'dark' | 'auto') => {
    if (isChanging) return; // Prevent multiple rapid clicks

    setIsChanging(true);
    try {
      setColorScheme(newScheme);
      // Save to localStorage for persistence
      localStorage.setItem('theme', newScheme);
    } finally {
      // Reset after a short delay
      setTimeout(() => setIsChanging(false), 300);
    }
  }, [setColorScheme, isChanging]);

  return (
    <>
      <Menu shadow="lg" width={200} zIndex={1000010}>
        <Menu.Target>
          <Tooltip
            label={t("Switch-color-modes")}
            withArrow
           style={{color:"var(--mantine-color-text)"}} className="h-10   navBarButtonicon"
          >
            <ActionIcon
              //variant="light"
              className="h-10 bg-[var(--bg-SwitchColor)]  navBarButtonicon"
              style={{color:" light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-0))"}}
            >
              {colorScheme === "auto" ? (
                <IconCircleHalf2 size={ICON_SIZE} className=" navBarButtonicon "/>
              ) : colorScheme === "dark" ? (
                <IconMoonStars size={ICON_SIZE} className=" navBarButtonicon "/>
              ) : (
                <IconSunHigh size={ICON_SIZE} className=" navBarButtonicon "/>
              )}
            </ActionIcon>
          </Tooltip>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label tt="uppercase" ta="center" fw={600}>
          {t('color-modes')}
          </Menu.Label>
          <Menu.Item
            leftSection={<IconSunHigh size={16} />}
            onClick={() => handleColorSchemeChange("light")}
          >
            {/* Light */}
            {t('Light-modes')}
          </Menu.Item>
          <Menu.Item
            leftSection={<IconMoonStars size={16} />}
            onClick={() => handleColorSchemeChange("dark")}
          >
            {/* Dark */}
            {t('Dark-modes')}
          </Menu.Item>
          <Menu.Item
            leftSection={<IconCircleHalf2 size={16} />}
            onClick={() => handleColorSchemeChange("auto")}
          >
            {/* Auto */}
            Auto
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
      {/* <span className="-mx-3.5 text-[var(--bg-base-200)]">|</span> */}
    </>
  );
};

export default SwitchColorModes;
