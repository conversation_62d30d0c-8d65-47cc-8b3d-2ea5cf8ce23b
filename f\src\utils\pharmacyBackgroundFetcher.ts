/**
 * Pharmacy Background Data Fetcher
 * Handles background fetching and caching of pharmacy data
 * Provides real-time updates without blocking the UI
 */

import { pharmacyService, PharmacySummary } from '@/services/pharmacyService';

interface CacheEntry {
  data: PharmacySummary;
  timestamp: number;
  expiresAt: number;
}

interface FetchOptions {
  forceRefresh?: boolean;
  cacheTimeout?: number; // in milliseconds
  priority?: 'low' | 'normal' | 'high';
  dateRange?: { start: string; end: string };
}

interface BackgroundFetcherConfig {
  defaultCacheTimeout: number;
  maxConcurrentFetches: number;
  retryAttempts: number;
  retryDelay: number;
}

class PharmacyBackgroundFetcher {
  private cache = new Map<string, CacheEntry>();
  private activeFetches = new Map<string, Promise<PharmacySummary>>();
  private fetchQueue: Array<{ 
    cacheKey: string; 
    options: FetchOptions; 
    resolve: Function; 
    reject: Function 
  }> = [];
  private isProcessingQueue = false;
  
  private config: BackgroundFetcherConfig = {
    defaultCacheTimeout: 4 * 60 * 1000, // 4 minutes (pharmacy data changes moderately)
    maxConcurrentFetches: 3,
    retryAttempts: 3,
    retryDelay: 1500, // 1.5 seconds
  };

  private listeners = new Map<string, Set<(data: PharmacySummary) => void>>();

  /**
   * Get pharmacy data with background fetching
   */
  async getPharmacyData(options: FetchOptions = {}): Promise<PharmacySummary> {
    const {
      forceRefresh = false,
      cacheTimeout = this.config.defaultCacheTimeout,
      priority = 'normal',
      dateRange
    } = options;

    const cacheKey = this.generateCacheKey(dateRange);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        // Start background refresh if data is getting old
        const age = Date.now() - cached.timestamp;
        if (age > cacheTimeout * 0.7) { // Refresh when 70% of cache time has passed
          this.queueBackgroundFetch(cacheKey, { ...options, priority: 'low' });
        }
        return cached.data;
      }
    }

    // Check if already fetching
    const activeFetch = this.activeFetches.get(cacheKey);
    if (activeFetch) {
      return activeFetch;
    }

    // Queue the fetch
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options: { ...options, cacheTimeout }, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Subscribe to real-time updates for pharmacy data
   */
  subscribe(callback: (data: PharmacySummary) => void, dateRange?: { start: string; end: string }): () => void {
    const cacheKey = this.generateCacheKey(dateRange);
    
    if (!this.listeners.has(cacheKey)) {
      this.listeners.set(cacheKey, new Set());
    }
    
    this.listeners.get(cacheKey)!.add(callback);

    // Start background fetching for this date range
    this.queueBackgroundFetch(cacheKey, { priority: 'normal', dateRange });

    // Return unsubscribe function
    return () => {
      const keyListeners = this.listeners.get(cacheKey);
      if (keyListeners) {
        keyListeners.delete(callback);
        if (keyListeners.size === 0) {
          this.listeners.delete(cacheKey);
        }
      }
    };
  }

  /**
   * Preload pharmacy data for multiple date ranges
   */
  async preloadDateRanges(dateRanges: Array<{ start: string; end: string }>, options: FetchOptions = {}): Promise<void> {
    const promises = dateRanges.map(dateRange => 
      this.queueBackgroundFetch(
        this.generateCacheKey(dateRange), 
        { ...options, dateRange, priority: 'low' }
      )
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Clear cache for a specific date range or all data
   */
  clearCache(dateRange?: { start: string; end: string }): void {
    if (dateRange) {
      const cacheKey = this.generateCacheKey(dateRange);
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    averageAge: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const now = Date.now();
    let expiredCount = 0;
    let totalAge = 0;
    let oldestAge = 0;
    let newestAge = Infinity;
    
    for (const [, entry] of this.cache) {
      const age = now - entry.timestamp;
      if (entry.expiresAt < now) {
        expiredCount++;
      }
      totalAge += age;
      oldestAge = Math.max(oldestAge, age);
      newestAge = Math.min(newestAge, age);
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would need to track hits/misses
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
      oldestEntry: oldestAge,
      newestEntry: newestAge === Infinity ? 0 : newestAge,
    };
  }

  /**
   * Force refresh data and notify all subscribers
   */
  async refreshData(dateRange?: { start: string; end: string }): Promise<PharmacySummary> {
    const cacheKey = this.generateCacheKey(dateRange);
    this.clearCache(dateRange);
    const data = await this.getPharmacyData({ 
      forceRefresh: true, 
      priority: 'high',
      dateRange 
    });
    this.notifyListeners(cacheKey, data);
    return data;
  }

  /**
   * Get pharmacy dashboard summary
   */
  async getPharmacyDashboardData(dateRange?: { start: string; end: string }) {
    const data = await this.getPharmacyData({ dateRange });
    
    const totalMedications = data.medications.length;
    const activeMedications = data.medications.filter(m => m.status === 'active').length;
    const totalStockValue = data.inventory.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0);
    const lowStockItems = data.inventory.filter(item => item.current_stock <= item.minimum_stock).length;
    const expiredItems = data.inventory.filter(item => new Date(item.expiry_date) < new Date()).length;
    const pendingOrders = data.purchaseOrders.filter(order => order.status === 'sent' || order.status === 'confirmed').length;
    const monthlyRevenue = data.salesOrders.reduce((sum, order) => sum + order.total_amount, 0);

    return {
      totalMedications,
      activeMedications,
      totalStockValue,
      lowStockItems,
      expiredItems,
      pendingOrders,
      monthlyRevenue,
      stockTurnover: data.analytics.stock_turnover_rate,
      topSellingMedication: data.analytics.top_selling_medications[0]?.medication_name || 'N/A',
      bestSupplier: data.analytics.supplier_performance[0]?.supplier_name || 'N/A',
    };
  }

  /**
   * Get inventory alerts
   */
  async getInventoryAlerts(dateRange?: { start: string; end: string }) {
    const data = await this.getPharmacyData({ dateRange });
    
    const today = new Date();
    const nearExpiryDate = new Date();
    nearExpiryDate.setDate(today.getDate() + 30); // 30 days from now

    const lowStock = data.inventory.filter(item => item.current_stock <= item.minimum_stock);
    const expired = data.inventory.filter(item => new Date(item.expiry_date) < today);
    const nearExpiry = data.inventory.filter(item => {
      const expiryDate = new Date(item.expiry_date);
      return expiryDate > today && expiryDate <= nearExpiryDate;
    });

    return {
      lowStock,
      expired,
      nearExpiry,
      totalAlerts: lowStock.length + expired.length + nearExpiry.length,
    };
  }

  // Private methods

  private generateCacheKey(dateRange?: { start: string; end: string }): string {
    if (!dateRange) {
      return 'default';
    }
    return `${dateRange.start}_${dateRange.end}`;
  }

  private getCachedData(cacheKey: string): CacheEntry | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    const now = Date.now();
    if (entry.expiresAt < now) {
      this.cache.delete(cacheKey);
      return null;
    }

    return entry;
  }

  private async queueBackgroundFetch(cacheKey: string, options: FetchOptions): Promise<PharmacySummary> {
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.fetchQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.fetchQueue.length > 0 && this.activeFetches.size < this.config.maxConcurrentFetches) {
      // Sort queue by priority
      this.fetchQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.options.priority || 'normal'] - priorityOrder[a.options.priority || 'normal'];
      });

      const item = this.fetchQueue.shift();
      if (!item) break;

      const { cacheKey, options, resolve, reject } = item;

      // Skip if already fetching this cache key
      if (this.activeFetches.has(cacheKey)) {
        const existingFetch = this.activeFetches.get(cacheKey)!;
        existingFetch.then(resolve).catch(reject);
        continue;
      }

      // Start the fetch
      const fetchPromise = this.performFetch(cacheKey, options);
      this.activeFetches.set(cacheKey, fetchPromise);

      fetchPromise
        .then((data) => {
          resolve(data);
          this.notifyListeners(cacheKey, data);
        })
        .catch(reject)
        .finally(() => {
          this.activeFetches.delete(cacheKey);
          // Continue processing queue
          setTimeout(() => this.processQueue(), 0);
        });
    }

    this.isProcessingQueue = false;
  }

  private async performFetch(cacheKey: string, options: FetchOptions): Promise<PharmacySummary> {
    const { cacheTimeout = this.config.defaultCacheTimeout, dateRange } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const data = await pharmacyService.getPharmacySummary(dateRange);
        
        // Cache the result
        const now = Date.now();
        this.cache.set(cacheKey, {
          data,
          timestamp: now,
          expiresAt: now + cacheTimeout,
        });

        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError || new Error('Failed to fetch pharmacy data');
  }

  private notifyListeners(cacheKey: string, data: PharmacySummary): void {
    const listeners = this.listeners.get(cacheKey);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in pharmacy listener:', error);
        }
      });
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [cacheKey, entry] of this.cache) {
      if (entry.expiresAt < now) {
        this.cache.delete(cacheKey);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startPeriodicCleanup(interval: number = 120000): () => void { // 2 minutes
    const intervalId = setInterval(() => {
      this.cleanupCache();
    }, interval);

    return () => clearInterval(intervalId);
  }
}

// Create singleton instance
export const pharmacyBackgroundFetcher = new PharmacyBackgroundFetcher();

// Auto-start periodic cleanup
pharmacyBackgroundFetcher.startPeriodicCleanup();

export default pharmacyBackgroundFetcher;
