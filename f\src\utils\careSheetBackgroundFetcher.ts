/**
 * Care Sheet Background Data Fetcher
 * Handles background fetching and caching of care-sheet data
 * Provides real-time updates without blocking the UI
 */

import { careSheetService, CareSheetSummary } from '@/services/careSheetService';

interface CacheEntry {
  data: CareSheetSummary;
  timestamp: number;
  expiresAt: number;
}

interface FetchOptions {
  forceRefresh?: boolean;
  cacheTimeout?: number; // in milliseconds
  priority?: 'low' | 'normal' | 'high';
}

interface BackgroundFetcherConfig {
  defaultCacheTimeout: number;
  maxConcurrentFetches: number;
  retryAttempts: number;
  retryDelay: number;
}

class CareSheetBackgroundFetcher {
  private cache = new Map<string, CacheEntry>();
  private activeFetches = new Map<string, Promise<CareSheetSummary>>();
  private fetchQueue: Array<{ patientId: string; options: FetchOptions; resolve: Function; reject: Function }> = [];
  private isProcessingQueue = false;
  
  private config: BackgroundFetcherConfig = {
    defaultCacheTimeout: 5 * 60 * 1000, // 5 minutes
    maxConcurrentFetches: 3,
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  };

  private listeners = new Map<string, Set<(data: CareSheetSummary) => void>>();

  /**
   * Get care sheet data for a patient with background fetching
   */
  async getCareSheetData(patientId: string, options: FetchOptions = {}): Promise<CareSheetSummary> {
    const {
      forceRefresh = false,
      cacheTimeout = this.config.defaultCacheTimeout,
      priority = 'normal'
    } = options;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getCachedData(patientId);
      if (cached) {
        // Start background refresh if data is getting old
        const age = Date.now() - cached.timestamp;
        if (age > cacheTimeout * 0.7) { // Refresh when 70% of cache time has passed
          this.queueBackgroundFetch(patientId, { ...options, priority: 'low' });
        }
        return cached.data;
      }
    }

    // Check if already fetching
    const activeFetch = this.activeFetches.get(patientId);
    if (activeFetch) {
      return activeFetch;
    }

    // Queue the fetch
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ patientId, options: { ...options, cacheTimeout }, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Subscribe to real-time updates for a patient's care sheet data
   */
  subscribe(patientId: string, callback: (data: CareSheetSummary) => void): () => void {
    if (!this.listeners.has(patientId)) {
      this.listeners.set(patientId, new Set());
    }
    
    this.listeners.get(patientId)!.add(callback);

    // Start background fetching for this patient
    this.queueBackgroundFetch(patientId, { priority: 'normal' });

    // Return unsubscribe function
    return () => {
      const patientListeners = this.listeners.get(patientId);
      if (patientListeners) {
        patientListeners.delete(callback);
        if (patientListeners.size === 0) {
          this.listeners.delete(patientId);
        }
      }
    };
  }

  /**
   * Preload care sheet data for multiple patients
   */
  async preloadPatients(patientIds: string[], options: FetchOptions = {}): Promise<void> {
    const promises = patientIds.map(patientId => 
      this.queueBackgroundFetch(patientId, { ...options, priority: 'low' })
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Clear cache for a specific patient or all patients
   */
  clearCache(patientId?: string): void {
    if (patientId) {
      this.cache.delete(patientId);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    averageAge: number;
  } {
    const now = Date.now();
    let expiredCount = 0;
    let totalAge = 0;
    
    for (const [, entry] of this.cache) {
      if (entry.expiresAt < now) {
        expiredCount++;
      }
      totalAge += now - entry.timestamp;
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would need to track hits/misses
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
    };
  }

  /**
   * Force refresh data for a patient and notify all subscribers
   */
  async refreshPatientData(patientId: string): Promise<CareSheetSummary> {
    this.clearCache(patientId);
    const data = await this.getCareSheetData(patientId, { forceRefresh: true, priority: 'high' });
    this.notifyListeners(patientId, data);
    return data;
  }

  // Private methods

  private getCachedData(patientId: string): CacheEntry | null {
    const entry = this.cache.get(patientId);
    if (!entry) return null;

    const now = Date.now();
    if (entry.expiresAt < now) {
      this.cache.delete(patientId);
      return null;
    }

    return entry;
  }

  private async queueBackgroundFetch(patientId: string, options: FetchOptions): Promise<CareSheetSummary> {
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ patientId, options, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.fetchQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.fetchQueue.length > 0 && this.activeFetches.size < this.config.maxConcurrentFetches) {
      // Sort queue by priority
      this.fetchQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.options.priority || 'normal'] - priorityOrder[a.options.priority || 'normal'];
      });

      const item = this.fetchQueue.shift();
      if (!item) break;

      const { patientId, options, resolve, reject } = item;

      // Skip if already fetching this patient
      if (this.activeFetches.has(patientId)) {
        const existingFetch = this.activeFetches.get(patientId)!;
        existingFetch.then(resolve).catch(reject);
        continue;
      }

      // Start the fetch
      const fetchPromise = this.performFetch(patientId, options);
      this.activeFetches.set(patientId, fetchPromise);

      fetchPromise
        .then((data) => {
          resolve(data);
          this.notifyListeners(patientId, data);
        })
        .catch(reject)
        .finally(() => {
          this.activeFetches.delete(patientId);
          // Continue processing queue
          setTimeout(() => this.processQueue(), 0);
        });
    }

    this.isProcessingQueue = false;
  }

  private async performFetch(patientId: string, options: FetchOptions): Promise<CareSheetSummary> {
    const { cacheTimeout = this.config.defaultCacheTimeout } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const data = await careSheetService.getCareSheetSummary(patientId);
        
        // Cache the result
        const now = Date.now();
        this.cache.set(patientId, {
          data,
          timestamp: now,
          expiresAt: now + cacheTimeout,
        });

        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError || new Error('Failed to fetch care sheet data');
  }

  private notifyListeners(patientId: string, data: CareSheetSummary): void {
    const listeners = this.listeners.get(patientId);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in care sheet listener:', error);
        }
      });
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [patientId, entry] of this.cache) {
      if (entry.expiresAt < now) {
        this.cache.delete(patientId);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startPeriodicCleanup(interval: number = 60000): () => void {
    const intervalId = setInterval(() => {
      this.cleanupCache();
    }, interval);

    return () => clearInterval(intervalId);
  }
}

// Create singleton instance
export const careSheetBackgroundFetcher = new CareSheetBackgroundFetcher();

// Auto-start periodic cleanup
careSheetBackgroundFetcher.startPeriodicCleanup();

export default careSheetBackgroundFetcher;
