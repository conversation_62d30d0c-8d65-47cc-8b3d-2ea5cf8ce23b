'use client';

import { useState, useEffect } from 'react';

import { extractErrorMessage, logError } from '@/utils/errorUtils';
import { ApiError } from '@/services/errorService';
import { formatTableDate } from '@/utils/dateUtils';
import { clearDoctorCache, isCurrentUserDoctor, getCurrentDoctorId } from '@/utils/cacheUtils';
import { cleanupTestUsers } from '@/utils/cleanupTestData';
import UserManagementCard from '@/components/settings/UserManagementCard';
import {
  Paper,
  Title,
  Text,
  Button,
  Group,
  Tabs,
  Table,
  Badge,
  ActionIcon,
  Modal,
  TextInput,
  PasswordInput,
  Select,
  Stack,
  Alert,
  Progress,
  Card,
  SimpleGrid,
  List,
  ThemeIcon,
  Divider,
  Radio,

  FileInput,
  Avatar,
  Box,
  Center,
} from '@mantine/core';
import { IconUpload, IconPhoto } from '@tabler/icons-react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconUsers,
  IconUserPlus,
  IconTrash,
  IconEdit,
  IconAlertCircle,
  IconCheck,

  IconCrown,
} from '@tabler/icons-react';
import userManagementService, { UserAccount, SubscriptionPackage } from '@/services/userManagementService';
import mockUserManagementService from '@/services/mockUserManagementService';
import { frontendToBackendStatus } from '@/utils/statusConverter';

export default function UserManagementPage() {

  const [activeTab, setActiveTab] = useState<string | null>('users');
  const [users, setUsers] = useState<UserAccount[]>([]);
  const [subscriptionPackages, setSubscriptionPackages] = useState<SubscriptionPackage[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionPackage | null>(null);
  const [userCounts, setUserCounts] = useState({ assistants: 0, patients: 0, staff: 0 });
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<string>('');
  const [subscriptionPeriod, setSubscriptionPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const form = useForm({
    initialValues: {
      email: '',
      first_name: '',
      last_name: '',
      phone_number: '',
      password: '',
      confirm_password: '',
      user_type: 'assistant',
      status: 'active',
      profile_image: null as File | null,
      assigned_doctor: '', // Will be set in useEffect after component mounts
    },
    validate: {
      email: (value) => (/^\S+@\S+\.[a-zA-Z]{2,}$/.test(value) ? null : 'Invalid email address'),
      first_name: (value) => (value.length < 1 ? 'First name is required' : null),
      last_name: (value) => (value.length < 1 ? 'Last name is required' : null),
      password: (value) => (value.length < 8 ? 'Password must be at least 8 characters' : null),
      confirm_password: (value, values) =>
        value !== values.password ? 'Passwords do not match' : null,
    },
  });

  const editForm = useForm({
    initialValues: {
      email: '',
      first_name: '',
      last_name: '',
      phone_number: '',
      status: 'active' as 'active' | 'inactive' | 'pending',
      password: '', // Optional field for password reset
      confirm_password: '', // Confirmation field
      profile_image: null as File | null, // Field for profile image update
    },
    validate: {
      email: (value) => (/^\S+@\S+\.[a-zA-Z]{2,}$/.test(value) ? null : 'Invalid email address'),
      first_name: (value) => (value.length < 1 ? 'First name is required' : null),
      last_name: (value) => (value.length < 1 ? 'Last name is required' : null),
      // Password is optional, but if provided, must be at least 8 characters
      password: (value) => (value && value.length > 0 && value.length < 8 ? 'Password must be at least 8 characters' : null),
      // Confirm password is only required if password is provided
      confirm_password: (value, values) => {
        // If password is provided, confirm_password must match
        if (values.password && values.password.length > 0) {
          return value !== values.password ? 'Passwords do not match' : null;
        }
        // If no password is provided, confirm_password is not required
        return null;
      },
    },
  });

  // Set assigned_doctor after component mounts (client-side only)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Use the utility functions for better type safety
      const doctorId = getCurrentDoctorId();
      const isDoctor = isCurrentUserDoctor();

      // Only set assigned_doctor if the current user is a doctor
      if (isDoctor && doctorId) {
        form.setFieldValue('assigned_doctor', doctorId);
        console.log('Set assigned_doctor for doctor:', doctorId);

        // Clear any cached data for this doctor to ensure fresh data
        try {
          clearDoctorCache(doctorId);
          console.log('Cleared cache to prevent cross-doctor data contamination');

          // Remove all test users including Jane Doe assistant from mock data
          cleanupTestUsers();
          console.log('Cleaned up all test user data including Jane Doe assistant');
        } catch (error) {
          console.warn('Could not clear cache:', error);
        }
      } else {
        console.warn('Current user is not a doctor or doctorId not found. IsDoctor:', isDoctor, 'DoctorId:', doctorId);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount to avoid infinite loop

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        console.log('Fetching data for User Management page');

        // Use the mock service directly if mock data is enabled
        const service = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
          ? mockUserManagementService
          : userManagementService;

        console.log('Using service:', process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' ? 'MOCK' : 'REAL');

        const [usersData, packagesData, currentPkg, counts] = await Promise.all([
          service.getUserAccounts(),
          service.getSubscriptionPackages(),
          service.getCurrentSubscription(),
          service.getUserCounts()
        ]);

        console.log('Fetched user accounts:', usersData);

        // Filter to show assistants and staff
        const managedAccounts = usersData.filter(user =>
          user.user_type === 'assistant' || user.user_type === 'staff'
        );
        console.log('Filtered managed accounts:', managedAccounts);

        setUsers(managedAccounts);
        setSubscriptionPackages(packagesData);
        setCurrentSubscription(currentPkg);
        setUserCounts(counts);
        setSelectedPackage(currentPkg.id);
      } catch (error: unknown) {
        const apiError = error as ApiError;
        // Log the error with detailed information
        console.error('Error fetching user management data:', apiError);
        logError('fetching user management data', apiError);

        // Extract the error message
        const errorMessage = extractErrorMessage(apiError, 'Failed to load user management data');

        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreateUser = async (values: typeof form.values) => {
    try {
      setLoading(true);

      // Validate that passwords match
      if (values.password !== values.confirm_password) {
        notifications.show({
          title: 'Error',
          message: 'Passwords do not match',
          color: 'red',
        });
        setLoading(false);
        return;
      }

      // Check if email already exists
      try {
        const response = await fetch(`/api/auth/check-email/?email=${encodeURIComponent(values.email)}`);
        const data = await response.json();

        if (data.exists) {
          notifications.show({
            title: 'Error',
            message: 'A user with this email already exists',
            color: 'red',
          });
          setLoading(false);
          return;
        }
      } catch (emailCheckError) {
        console.error('Error checking email:', emailCheckError);
        // Continue with user creation even if email check fails
      }

      // Use the mock service directly if mock data is enabled
      const service = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
        ? mockUserManagementService
        : userManagementService;

      console.log('Using service for create:', process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' ? 'MOCK' : 'REAL');

      // Always use FormData to handle file uploads properly
      const formData = new FormData();

      // Add basic user data
      formData.append('email', values.email);
      formData.append('first_name', values.first_name);
      formData.append('last_name', values.last_name);
      formData.append('phone_number', values.phone_number || '');
      formData.append('password', values.password);
      formData.append('user_type', values.user_type);

      // Add status for backend processing
      formData.append('status', values.status);

      // Log status for debugging
      console.log(`Creating user with status: "${values.status}"`);

      // Normalize status value to handle string case variations
      let normalizedStatus: 'active' | 'inactive' | 'pending';

      if (typeof values.status === 'string') {
        const statusLower = values.status.toLowerCase();
        if (statusLower === 'active') {
          normalizedStatus = 'active';
        } else if (statusLower === 'pending') {
          normalizedStatus = 'pending';
        } else if (statusLower === 'inactive') {
          normalizedStatus = 'inactive';
        } else {
          console.warn(`Unknown status value: "${values.status}", defaulting to "inactive"`);
          normalizedStatus = 'inactive';
        }
      } else {
        console.warn(`Status is not a string: ${values.status}, defaulting to "inactive"`);
        normalizedStatus = 'inactive';
      }

      console.log(`Normalized status: "${values.status}" -> "${normalizedStatus}"`);

      // Use the status converter utility from our utils
      const { is_active, is_pending } = frontendToBackendStatus(normalizedStatus);

      // Add the converted status to the FormData
      formData.append('is_active', is_active.toString());
      formData.append('is_pending', is_pending.toString());

      console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);

      // Add profile image if provided
      if (values.profile_image) {
        formData.append('profile_image', values.profile_image);
      }

      // Add assigned_doctor for both staff and assistant
      if (values.assigned_doctor) {
        // Validate that the assigned_doctor is the current user (security check)
        const currentUserId = localStorage.getItem('userId');
        const currentUserType = localStorage.getItem('userType');

        if (currentUserType === 'doctor' && values.assigned_doctor === currentUserId) {
          formData.append('assigned_doctor', values.assigned_doctor);
          console.log('Adding assigned_doctor to FormData:', values.assigned_doctor);
        } else {
          console.error('Security violation: Attempting to assign assistant/staff to different doctor');
          notifications.show({
            title: 'Error',
            message: 'You can only create assistants and staff for yourself',
            color: 'red',
          });
          setLoading(false);
          return;
        }
      }

      // Call service with FormData
      // Use type assertion to tell TypeScript that the service accepts FormData
      const newUser = await (service.createUserAccount as (data: Partial<UserAccount> | FormData) => Promise<UserAccount | null>)(formData);

      if (newUser) {
        setUsers([...users, newUser]);
        setUserCounts({
          ...userCounts,
          assistants: values.user_type === 'assistant' ? userCounts.assistants + 1 : userCounts.assistants,
          patients: values.user_type === 'patient' ? userCounts.patients + 1 : userCounts.patients,
          staff: values.user_type === 'staff' ? userCounts.staff + 1 : userCounts.staff
        });

        notifications.show({
          title: 'Success',
          message: `${values.user_type.charAt(0).toUpperCase() + values.user_type.slice(1)} account created successfully. The user will need to change their password on first login.`,
          color: 'green',
        });

        setCreateModalOpen(false);
        form.reset();
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      // Log the error with detailed information
      logError('creating user', apiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(apiError, 'Failed to create user account');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = async (values: typeof editForm.values) => {
    if (!selectedUser) return;

    try {
      setLoading(true);

      console.log('Starting user update process for user:', selectedUser.id);
      console.log('Form values received:', JSON.stringify(values, null, 2));

      // Log the selected user for debugging
      console.log('Selected user for editing:', {
        id: selectedUser.id,
        email: selectedUser.email,
        user_type: selectedUser.user_type,
        status: selectedUser.status,
        profile_image_url: selectedUser.profile_image_url,
        profile_image_url_type: typeof selectedUser.profile_image_url,
        is_profile_image_url_null: selectedUser.profile_image_url === null,
        is_profile_image_url_undefined: selectedUser.profile_image_url === undefined,
        is_profile_image_url_string_null: selectedUser.profile_image_url === 'null',
        is_profile_image_url_string_undefined: selectedUser.profile_image_url === 'undefined'
      });

      // DIRECT MOCK IMPLEMENTATION - For immediate testing
      if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
        console.log('USING DIRECT MOCK IMPLEMENTATION');

        // Find the user in the current users array
        const userIndex = users.findIndex(user => user.id === selectedUser.id);
        if (userIndex === -1) {
          throw new Error(`User with ID ${selectedUser.id} not found in local state`);
        }

        // Create an updated user object
        const updatedUser: UserAccount = {
          ...users[userIndex],
          email: values.email,
          first_name: values.first_name,
          last_name: values.last_name,
          phone_number: values.phone_number || '',
          status: values.status
        };

        // Add password if provided
        if (values.password) {
          if (values.password !== values.confirm_password) {
            notifications.show({
              title: 'Error',
              message: 'Passwords do not match',
              color: 'red',
              icon: <IconAlertCircle size={16} />,
            });
            setLoading(false);
            return;
          }
        }

        // Update the users array
        const updatedUsers = [...users];
        updatedUsers[userIndex] = updatedUser;

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update state
        setUsers(updatedUsers);

        // Show success notification
        notifications.show({
          title: 'Success',
          message: 'User account updated successfully',
          color: 'green',
          icon: <IconCheck size={16} />,
        });

        // Close modal and reset state
        setEditModalOpen(false);
        setSelectedUser(null);
        setLoading(false);
        return;
      }

      // NORMAL IMPLEMENTATION - For when mock data is disabled
      console.log('Starting normal update implementation');

      // Check if we need to use FormData (for image upload) or regular JSON
      let updatedUser: UserAccount | null = null;

      if (values.profile_image) {
        console.log('Profile image provided, using FormData for update');

        // Create FormData for the update
        const formData = new FormData();

        // Add basic user data
        formData.append('email', values.email);
        formData.append('first_name', values.first_name);
        formData.append('last_name', values.last_name);
        formData.append('phone_number', values.phone_number || '');
        formData.append('status', values.status);

        // Log status for debugging
        console.log(`Editing user with status: "${values.status}"`);

        // Normalize status value to handle string case variations
        let normalizedStatus: 'active' | 'inactive' | 'pending';

        if (typeof values.status === 'string') {
          const statusLower = values.status.toLowerCase();
          if (statusLower === 'active') {
            normalizedStatus = 'active';
          } else if (statusLower === 'pending') {
            normalizedStatus = 'pending';
          } else if (statusLower === 'inactive') {
            normalizedStatus = 'inactive';
          } else {
            console.warn(`Unknown status value: "${values.status}", defaulting to "inactive"`);
            normalizedStatus = 'inactive';
          }
        } else {
          console.warn(`Status is not a string: ${values.status}, defaulting to "inactive"`);
          normalizedStatus = 'inactive';
        }

        console.log(`Normalized status: "${values.status}" -> "${normalizedStatus}"`);

        // Use the status converter utility from our utils
        const { is_active, is_pending } = frontendToBackendStatus(normalizedStatus);

        // Add the converted status to the FormData
        formData.append('is_active', is_active.toString());
        formData.append('is_pending', is_pending.toString());

        console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);
        formData.append('user_type', selectedUser.user_type); // Preserve the user type

        // Add profile image
        formData.append('profile_image', values.profile_image);

        // Add password if provided
        if (values.password) {
          console.log('Password provided, validating...');
          // Validate that passwords match
          if (values.password !== values.confirm_password) {
            console.log('Password validation failed: passwords do not match');
            notifications.show({
              title: 'Error',
              message: 'Passwords do not match',
              color: 'red',
              icon: <IconAlertCircle size={16} />,
            });
            setLoading(false);
            return;
          }

          console.log('Password validation passed, adding to FormData');
          formData.append('password', values.password);
          formData.append('password2', values.password); // Add confirmation password for backend
        }

        console.log('Calling userManagementService.updateUserAccount with FormData');
        // Use the regular updateUserAccount method but with FormData
        updatedUser = await userManagementService.updateUserAccount(selectedUser.id, formData as unknown as Partial<UserAccount>);
      } else {
        // Regular JSON update (no image)
        console.log('No profile image provided, using JSON for update');

        // Log status for debugging
        console.log(`Editing user with status: "${values.status}"`);

        // Normalize status value to handle string case variations
        let normalizedStatus: 'active' | 'inactive' | 'pending';

        if (typeof values.status === 'string') {
          const statusLower = values.status.toLowerCase();
          if (statusLower === 'active') {
            normalizedStatus = 'active';
          } else if (statusLower === 'pending') {
            normalizedStatus = 'pending';
          } else if (statusLower === 'inactive') {
            normalizedStatus = 'inactive';
          } else {
            console.warn(`Unknown status value: "${values.status}", defaulting to "inactive"`);
            normalizedStatus = 'inactive';
          }
        } else {
          console.warn(`Status is not a string: ${values.status}, defaulting to "inactive"`);
          normalizedStatus = 'inactive';
        }

        console.log(`Normalized status: "${values.status}" -> "${normalizedStatus}"`);

        // Use the status converter utility from our utils
        const { is_active, is_pending } = frontendToBackendStatus(normalizedStatus);

        console.log(`Status conversion: "${normalizedStatus}" -> is_active=${is_active}, is_pending=${is_pending}`);

        // Prepare update data
        const updateData: Partial<UserAccount> = {
          email: values.email,
          first_name: values.first_name,
          last_name: values.last_name,
          phone_number: values.phone_number || '',
          status: values.status,
          user_type: selectedUser.user_type, // Preserve the user type
          // Add the converted status flags
          is_active: is_active,
          is_pending: is_pending
        };

        console.log('Initial update data prepared:', JSON.stringify(updateData, null, 2));

        // Add password if provided
        if (values.password) {
          console.log('Password provided, validating...');
          // Validate that passwords match
          if (values.password !== values.confirm_password) {
            console.log('Password validation failed: passwords do not match');
            notifications.show({
              title: 'Error',
              message: 'Passwords do not match',
              color: 'red',
              icon: <IconAlertCircle size={16} />,
            });
            setLoading(false);
            return;
          }

          console.log('Password validation passed, adding to update data');
          // Add password to update data
          updateData.password = values.password;
        }

        console.log('Final update data being sent to service:', JSON.stringify({
          ...updateData,
          password: updateData.password ? '[REDACTED]' : undefined
        }, null, 2));

        console.log('Calling userManagementService.updateUserAccount with ID:', selectedUser.id);
        updatedUser = await userManagementService.updateUserAccount(selectedUser.id, updateData);
      }

      console.log('Response from updateUserAccount:', updatedUser);

      if (updatedUser && selectedUser) {
        console.log('Update successful, updating local state');

        // Update the users array with the updated user
        const updatedUsers = users.map(user =>
          user.id === selectedUser.id ? updatedUser : user
        );

        console.log('Updated users array:', updatedUsers);
        setUsers(updatedUsers);

        // Show success notification
        const successMessage = values.password
          ? `User account updated successfully. The user will need to change their password on next login.`
          : `User account updated successfully`;

        console.log('Showing success notification:', successMessage);
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: <IconCheck size={16} />,
        });

        // Close modal and reset state
        console.log('Closing edit modal and resetting selected user');
        setEditModalOpen(false);
        setSelectedUser(null);
      } else {
        console.log('Update failed: updatedUser is null or undefined');
        notifications.show({
          title: 'Error',
          message: 'Failed to update user account. No response from server.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      // Log the error with detailed information
      console.error('Error updating user:', apiError);
      logError('updating user', apiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(apiError, 'Failed to update user account');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) {
      console.error('Cannot delete user: No user selected');
      notifications.show({
        title: 'Error',
        message: 'No user selected for deletion',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
      return;
    }

    try {
      setLoading(true);
      console.log('Deleting user:', selectedUser);

      // Use the mock service directly if mock data is enabled
      const service = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
        ? mockUserManagementService
        : userManagementService;

      console.log('Using service for delete:', process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' ? 'MOCK' : 'REAL');
      console.log('User ID:', selectedUser.id);
      console.log('User Type:', selectedUser.user_type);

      // Pass both the ID and the user type to the service
      try {
        const success = await service.deleteUserAccount(selectedUser.id, selectedUser.user_type);

        if (success) {
          console.log('Delete successful, updating UI');

          // Update the users list
          setUsers(users.filter(user => user.id !== selectedUser.id));

          // Update the user counts
          setUserCounts({
            ...userCounts,
            assistants: selectedUser.user_type === 'assistant' ? userCounts.assistants - 1 : userCounts.assistants,
            patients: selectedUser.user_type === 'patient' ? userCounts.patients - 1 : userCounts.patients,
            staff: selectedUser.user_type === 'staff' ? userCounts.staff - 1 : userCounts.staff
          });

          // Show success notification
          notifications.show({
            title: 'Success',
            message: `User account deleted successfully`,
            color: 'green',
            icon: <IconCheck size={16} />,
          });

          // Close modal and reset state
          setDeleteModalOpen(false);
          setSelectedUser(null);
        } else {
          console.error('Delete operation returned false');
          notifications.show({
            title: 'Error',
            message: 'Failed to delete user account. The operation did not complete successfully.',
            color: 'red',
            icon: <IconAlertCircle size={16} />,
          });
        }
      } catch (error: unknown) {
        const serviceError = error as ApiError;
        console.error('Service error in deleteUserAccount:', serviceError);

        // Check for specific error types
        if (serviceError.response?.status === 403) {
          // Permission error
          notifications.show({
            title: 'Permission Denied',
            message: 'You do not have permission to delete this user.',
            color: 'red',
            icon: <IconAlertCircle size={16} />,
          });
        } else if (serviceError.response?.status === 404) {
          // Not found error - user might have been deleted already
          notifications.show({
            title: 'User Not Found',
            message: 'The user account could not be found. It may have been deleted already.',
            color: 'orange',
            icon: <IconAlertCircle size={16} />,
          });

          // Update the UI anyway to remove the user
          setUsers(users.filter(user => user.id !== selectedUser.id));
          setDeleteModalOpen(false);
          setSelectedUser(null);
        } else {
          // Generic error
          throw serviceError; // Re-throw to be caught by the outer catch block
        }
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      // Log the error with detailed information
      console.error('Error in handleDeleteUser:', apiError);
      logError('deleting user', apiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(apiError, 'Failed to delete user account');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeSubscription = async () => {
    try {
      setLoading(true);

      // Use the mock service directly if mock data is enabled
      const service = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
        ? mockUserManagementService
        : userManagementService;

      console.log('Using service for upgrade:', process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' ? 'MOCK' : 'REAL');

      const updatedPackage = await service.updateSubscription(selectedPackage);

      setCurrentSubscription(updatedPackage);
      setSubscriptionPackages(subscriptionPackages.map(pkg =>
        pkg.id === selectedPackage ? { ...pkg, is_current: true } : { ...pkg, is_current: false }
      ));

      notifications.show({
        title: 'Success',
        message: `Subscription upgraded to ${updatedPackage.name} package`,
        color: 'green',
      });

      setUpgradeModalOpen(false);
    } catch (error: unknown) {
      const apiError = error as ApiError;
      // Log the error with detailed information
      logError('upgrading subscription', apiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(apiError, 'Failed to upgrade subscription');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderUsersTab = () => {
    // We're already filtering for assistants in the fetchData function
    const assistantUsers = users;

    return (
      <>
        <Group justify="space-between" mb="md">
          <div>
            <Title order={3}>User Management</Title>
            <Text c="dimmed">Manage your assistants and staff accounts</Text>
          </div>
          <Button
            leftSection={<IconUserPlus size={16} />}
            onClick={() => {
              // Pre-set the user type to assistant
              form.setValues({
                ...form.values,
                user_type: 'assistant'
              });
              setCreateModalOpen(true);
            }}
          >
            Create User
          </Button>
        </Group>

        <Paper withBorder p="md" mb="xl">
          <Group justify="space-between" mb="md">
            <Title order={4}>Subscription Limits</Title>
            <Badge size="lg" color={currentSubscription?.name === 'Basic' ? 'blue' : currentSubscription?.name === 'Professional' ? 'violet' : 'green'}>
              {currentSubscription?.name} Plan
            </Badge>
          </Group>

          <Group grow mb="md">
            <div>
              <Group justify="space-between" mb={5}>
                <Text size="sm">Assistants</Text>
                <Text size="sm" fw={500}>{userCounts.assistants} / {currentSubscription?.max_assistants}</Text>
              </Group>
              <Progress
                value={(userCounts.assistants / (currentSubscription?.max_assistants || 1)) * 100}
                color={userCounts.assistants >= (currentSubscription?.max_assistants || 0) ? 'red' : 'blue'}
              />
            </div>

            <div>
              <Group justify="space-between" mb={5}>
                <Text size="sm">Staff</Text>
                <Text size="sm" fw={500}>{userCounts.staff} / {currentSubscription?.max_staff}</Text>
              </Group>
              <Progress
                value={(userCounts.staff / (currentSubscription?.max_staff || 1)) * 100}
                color={userCounts.staff >= (currentSubscription?.max_staff || 0) ? 'red' : 'indigo'}
              />
            </div>
          </Group>

          <Group mb="md">
            <div style={{ width: '100%' }}>
              <Group justify="space-between" mb={5}>
                <Text size="sm">Patients (Created via appointments)</Text>
                <Text size="sm" fw={500}>{userCounts.patients} / {currentSubscription?.max_patients}</Text>
              </Group>
              <Progress
                value={(userCounts.patients / (currentSubscription?.max_patients || 1)) * 100}
                color={userCounts.patients >= (currentSubscription?.max_patients || 0) ? 'red' : 'green'}
              />
              <Text size="xs" c="dimmed" mt={5}>
                Patient accounts are automatically created through the appointment booking process
              </Text>
            </div>
          </Group>

          {(userCounts.assistants >= (currentSubscription?.max_assistants || 0) ||
            userCounts.patients >= (currentSubscription?.max_patients || 0) ||
            userCounts.staff >= (currentSubscription?.max_staff || 0)) && (
            <Alert icon={<IconAlertCircle size={16} />} color="yellow" mb="md">
              You have reached the limit for your current subscription plan.
              <Button variant="subtle"  onClick={() => setUpgradeModalOpen(true)}>
                Upgrade Now
              </Button>
            </Alert>
          )}
        </Paper>

        <Paper withBorder p={2}>

          <Table.ScrollContainer minWidth={500} type="native">
       <Table striped highlightOnHover withTableBorder withColumnBorders>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Image</Table.Th>
            <Table.Th>Name</Table.Th>
            <Table.Th>Email</Table.Th>
            <Table.Th>Phone</Table.Th>
            <Table.Th>Type</Table.Th>
            <Table.Th>Status</Table.Th>
            <Table.Th>Created</Table.Th>
            <Table.Th>Actions</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
        {assistantUsers.length > 0 ? (
                assistantUsers.map((user: UserAccount) => (
                  <Table.Tr key={user.id}>
                    <Table.Td>
                      <Avatar
                        src={user.profile_image_url &&
                             user.profile_image_url !== 'null' &&
                             user.profile_image_url !== 'undefined' &&
                             user.profile_image_url !== null &&
                             user.profile_image_url !== undefined ?
                             user.profile_image_url : null}
                        size="md"
                        radius="xl"
                        color={user.user_type === 'assistant' ? 'blue' : 'violet'}
                      >
                        {user.first_name?.[0]}{user.last_name?.[0]}
                      </Avatar>
                    </Table.Td>
                    <Table.Td>{user.first_name} {user.last_name}</Table.Td>
                    <Table.Td>{user.email}</Table.Td>
                    <Table.Td>{user.phone_number || '-'}</Table.Td>
                    <Table.Td>
                      <Badge color={user.user_type === 'assistant' ? 'blue' : 'violet'}>
                        {user.user_type === 'assistant' ? 'Assistant' : 'Staff'}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={
                        user.status === 'active' ? 'green' :
                        user.status === 'pending' ? 'yellow' : 'red'
                      }>
                        {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>{formatTableDate(user.created_at)}</Table.Td>
                    <Table.Td>
                      <Group gap={5}>
                        <ActionIcon
                          color="blue"
                          onClick={() => {
                            setSelectedUser(user);
                            // Initialize the edit form with the selected user's data
                            console.log('User data for edit:', {
                              id: user.id,
                              email: user.email,
                              first_name: user.first_name,
                              last_name: user.last_name,
                              phone_number: user.phone_number,
                              status: user.status,
                              user_type: user.user_type
                            });

                            editForm.setValues({
                              email: user.email,
                              first_name: user.first_name,
                              last_name: user.last_name,
                              phone_number: user.phone_number || '',
                              status: user.status,
                              password: '',
                              confirm_password: '',
                              profile_image: null
                            });
                            console.log('Edit form initialized with:', {
                              email: user.email,
                              first_name: user.first_name,
                              last_name: user.last_name,
                              phone_number: user.phone_number || '',
                              status: user.status
                            });
                            setEditModalOpen(true);
                          }}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                        <ActionIcon
                          color="red"
                          onClick={() => {
                            setSelectedUser(user);
                            setDeleteModalOpen(true);
                          }}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))
              ) : (
                <Table.Tr>
                  <Table.Td colSpan={6}>
                    <Text ta="center" py="md" c="dimmed">No assistants found</Text>
                  </Table.Td>
                </Table.Tr>
              )}

      </Table.Tbody>
      </Table>
    </Table.ScrollContainer>
        </Paper>
      </>
    );
  };

  const renderSubscriptionTab = () => {
    return (
      <>
        <Group justify="space-between" mb="md">
          <div>
            <Title order={3}>Subscription Management 1</Title>
            <Text c="dimmed">Manage your subscription package</Text>
            
          </div>
          <Button
            leftSection={<IconCrown size={16} />}
            onClick={() => setUpgradeModalOpen(true)}
          >
            Upgrade Plan
          </Button>
        </Group>

        <Paper withBorder p="md" mb="xl">
          <Group justify="space-between">
            <div>
              <Title order={4}>Current Plan</Title>
              <Text size="xl" fw={700} c={
                currentSubscription?.name === 'Basic' ? 'blue' :
                currentSubscription?.name === 'Professional' ? 'violet' : 'green'
              }>
                {currentSubscription?.name}
              </Text>
            </div>
            <Badge size="xl" variant="filled" color={
              currentSubscription?.name === 'Basic' ? 'blue' :
              currentSubscription?.name === 'Professional' ? 'violet' : 'green'
            }>
              ${subscriptionPeriod === 'monthly' ? currentSubscription?.price_monthly : currentSubscription?.price_yearly}
              /{subscriptionPeriod === 'monthly' ? 'month' : 'year'}
            </Badge>
          </Group>

          <Divider my="md" />

          <Title order={5} mb="sm">Features</Title>
          <List spacing="xs" size="sm" mb="md">
            {currentSubscription?.features.map((feature, index) => (
              <List.Item
                key={index}
                icon={
                  <ThemeIcon color="green" size={20} radius="xl">
                    <IconCheck size={12} />
                  </ThemeIcon>
                }
              >
                {feature}
              </List.Item>
            ))}
          </List>

          <Divider my="md" />

          <Group justify="space-between">
            <div>
              <Text>Billing Period</Text>
              <Text size="sm" c="dimmed">
                {subscriptionPeriod === 'monthly' ? 'Monthly' : 'Annual'} billing
              </Text>
            </div>
            <Radio.Group
              value={subscriptionPeriod}
              onChange={(value) => setSubscriptionPeriod(value as 'monthly' | 'yearly')}
            >
              <Group mt="xs">
                <Radio value="monthly" label="Monthly" />
                <Radio value="yearly" label="Yearly (Save 15%)" />
              </Group>
            </Radio.Group>
          </Group>
        </Paper>

        <Title order={3} mb="md">Available Plans</Title>

        <SimpleGrid cols={3} spacing="lg">
          {subscriptionPackages.map((pkg) => (
            <Card key={pkg.id} shadow="sm" padding="lg" radius="md" withBorder>
              <Card.Section withBorder inheritPadding py="xs">
                <Group justify="space-between">
                  <Text fw={500}>{pkg.name}</Text>
                  {pkg.is_current && (
                    <Badge color="green">Current Plan</Badge>
                  )}
                </Group>
              </Card.Section>

              <Group justify="space-between" mt="md" mb="xs">
                <Text size="xl" fw={700}>
                  ${subscriptionPeriod === 'monthly' ? pkg.price_monthly : pkg.price_yearly}
                </Text>
                <Text size="sm" c="dimmed">
                  per {subscriptionPeriod === 'monthly' ? 'month' : 'year'}
                </Text>
              </Group>

              <Text size="sm" c="dimmed" mb="md">
                {pkg.max_patients} patients, {pkg.max_assistants} assistants
              </Text>

              <List spacing="xs" size="sm" mb="xl" center>
                {pkg.features.map((feature, index) => (
                  <List.Item
                    key={index}
                    icon={
                      <ThemeIcon color="green" size={20} radius="xl">
                        <IconCheck size={12} />
                      </ThemeIcon>
                    }
                  >
                    {feature}
                  </List.Item>
                ))}
              </List>

              <Button
                variant={pkg.is_current ? "light" : "filled"}
                color="blue"
                fullWidth
                mt="auto"
                disabled={pkg.is_current}
                onClick={() => {
                  setSelectedPackage(pkg.id);
                  setUpgradeModalOpen(true);
                }}
              >
                {pkg.is_current ? 'Current Plan' : 'Select Plan'}
              </Button>
            </Card>
          ))}
        </SimpleGrid>
      </>
    );
  };

  return (
  <>
      <Paper p="xl" radius="md" withBorder mb="10" w={"100%"}>
       
         
             <UserManagementCard />
           
        
      </Paper>
<Paper p="xl" radius="md" withBorder mb="60" w={"100%"}>
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="xl">
          <Tabs.Tab value="users" leftSection={<IconUsers size="0.8rem" />}>
            Users
          </Tabs.Tab>
          <Tabs.Tab value="subscription" leftSection={<IconCrown size="0.8rem" />}>
            Subscription
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="users">
          {renderUsersTab()}
        </Tabs.Panel>

        <Tabs.Panel value="subscription">
          {renderSubscriptionTab()}
        </Tabs.Panel>
      </Tabs>
</Paper>
      {/* Create Assistant Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title={`Create New ${form.values.user_type === 'staff' ? 'Staff Member' : 'Assistant'}`}
        size="md"
      >
        <form onSubmit={form.onSubmit(handleCreateUser)}>
          <Stack>
            <Select
              label="User Type"
              required
              data={[
                { value: 'assistant', label: 'Assistant (Doctor in Training)' },
                { value: 'staff', label: 'Staff (Administrative)' }
              ]}
              defaultValue="assistant"
              {...form.getInputProps('user_type')}
            />

            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              {...form.getInputProps('email')}
            />

            <Group grow>
              <TextInput
                label="First Name"
                placeholder="John"
                required
                {...form.getInputProps('first_name')}
              />

              <TextInput
                label="Last Name"
                placeholder="Doe"
                required
                {...form.getInputProps('last_name')}
              />
            </Group>

            <TextInput
              label="Phone Number"
              placeholder="+****************"
              {...form.getInputProps('phone_number')}
            />

            <Select
              label="Status"
              placeholder="Select status"
              data={[
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
                { value: 'pending', label: 'Pending' }
              ]}
              value={form.values.status}
              onChange={(value) => form.setFieldValue('status', value as 'active' | 'inactive' | 'pending')}
              required
            />

            <PasswordInput
              label="Password"
              placeholder="Create a password"
              required
              {...form.getInputProps('password')}
            />

            <PasswordInput
              label="Confirm Password"
              placeholder="Confirm password"
              required
              {...form.getInputProps('confirm_password')}
            />

            <Box>
              <Text size="sm" fw={500} mb={5}>Profile Image</Text>
              <Group align="flex-start">
                <Center style={{ width: 100, height: 100, border: '1px dashed #ced4da', borderRadius: '50%' }}>
                  {form.values.profile_image ? (
                    <Avatar
                      src={form.values.profile_image ? URL.createObjectURL(form.values.profile_image) : null}
                      size={90}
                      radius="xl"
                    />
                  ) : (
                    <IconPhoto size={40} color="#ced4da" />
                  )}
                </Center>
                <Box style={{ flex: 1 }}>
                  <FileInput
                    placeholder="Upload image"
                    accept="image/png,image/jpeg,image/jpg"
                    leftSection={<IconUpload size={14} />}
                    {...form.getInputProps('profile_image')}
                  />
                  <Text size="xs" c="dimmed" mt={5}>
                    Upload a profile picture for the assistant. Recommended size: 200x200px.
                  </Text>
                </Box>
              </Group>
            </Box>

            <Alert color="blue" icon={<IconAlertCircle size={16} />}>
              {form.values.user_type === 'assistant' ? (
                <>
                  An email will be sent to the assistant with instructions to activate their account.
                  They will be automatically assigned to you as their supervising doctor.
                </>
              ) : (
                <>
                  An email will be sent to the staff member with instructions to activate their account.
                </>
              )}
            </Alert>

            <Group justify="flex-start">
              <Button variant="outline" onClick={() => setCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                Create {form.values.user_type === 'staff' ? 'Staff Member' : 'Assistant'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        opened={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title="Edit User"
        size="md"
      >
        <form onSubmit={(e) => {
          e.preventDefault();
          console.log('Form submitted with values:', editForm.values);
          handleEditUser(editForm.values);
        }}>
          <Stack>
            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              {...editForm.getInputProps('email')}
            />

            <Group grow>
              <TextInput
                label="First Name"
                placeholder="John"
                required
                {...editForm.getInputProps('first_name')}
              />

              <TextInput
                label="Last Name"
                placeholder="Doe"
                required
                {...editForm.getInputProps('last_name')}
              />
            </Group>

            <TextInput
              label="Phone Number"
              placeholder="+****************"
              required
              {...editForm.getInputProps('phone_number')}
            />

            <Select
              label="Status"
              placeholder="Select status"
              data={[
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
                { value: 'pending', label: 'Pending' }
              ]}
              required
              {...editForm.getInputProps('status')}
            />

            <Box>
              <Text size="sm" fw={500} mb={5}>Profile Image</Text>
              <Group align="flex-start">
                <Center style={{ width: 100, height: 100, border: '1px dashed #ced4da', borderRadius: '50%' }}>
                  {selectedUser?.profile_image_url &&
                   selectedUser.profile_image_url !== 'null' &&
                   selectedUser.profile_image_url !== 'undefined' &&
                   selectedUser.profile_image_url !== null &&
                   selectedUser.profile_image_url !== undefined ? (
                    <Avatar
                      src={selectedUser.profile_image_url}
                      size={90}
                      radius="xl"
                      alt={`${selectedUser.first_name} ${selectedUser.last_name}`}
                      color={selectedUser.user_type === 'assistant' ? 'blue' : 'violet'}
                    >
                      {selectedUser.first_name?.[0]}{selectedUser.last_name?.[0]}
                    </Avatar>
                  ) : editForm.values.profile_image ? (
                    <Avatar
                      src={URL.createObjectURL(editForm.values.profile_image)}
                      size={90}
                      radius="xl"
                      alt={selectedUser ? `${selectedUser.first_name} ${selectedUser.last_name}` : 'User'}
                      color={selectedUser?.user_type === 'assistant' ? 'blue' : 'violet'}
                    >
                      {selectedUser?.first_name?.[0]}{selectedUser?.last_name?.[0]}
                    </Avatar>
                  ) : (
                    <IconPhoto size={40} color="#ced4da" />
                  )}
                </Center>
                <Box style={{ flex: 1 }}>
                  <FileInput
                    placeholder="Upload new image"
                    accept="image/png,image/jpeg,image/jpg"
                    leftSection={<IconUpload size={14} />}
                    {...editForm.getInputProps('profile_image')}
                  />
                  <Text size="xs" c="dimmed" mt={5}>
                    Upload a new profile picture. Leave empty to keep the current image.
                  </Text>
                </Box>
              </Group>
            </Box>

            <Divider my="md" label="Reset Password" labelPosition="center" />

            <PasswordInput
              label="New Password"
              placeholder="Leave blank to keep current password"
              {...editForm.getInputProps('password')}
            />

            <PasswordInput
              label="Confirm New Password"
              placeholder="Confirm new password"
              {...editForm.getInputProps('confirm_password')}
            />

            <Alert color="blue" icon={<IconAlertCircle size={16} />}>
              If you set a new password, the user will be required to change it on their next login.
            </Alert>

            <Group justify="flex-start">
              <Button variant="outline" onClick={() => setEditModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                Save Changes
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Delete User Modal */}
      <Modal
        opened={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete User"
        size="md"
      >
        <Text mb="md">
          Are you sure you want to delete the user account for <strong>{selectedUser?.first_name} {selectedUser?.last_name}</strong>?
          This action cannot be undone.
        </Text>

        {selectedUser?.user_type === 'assistant' || selectedUser?.user_type === 'staff' ? (
          <Alert color="red" icon={<IconAlertCircle size={16} />} mb="md">
            <Text fw={700}>Important:</Text>
            <Text>Deleting this {selectedUser?.user_type === 'assistant' ? 'assistant' : 'staff member'} will reduce your count, but you will not be able to create a new one until your subscription package is renewed. This is to prevent abuse of the subscription limits.</Text>
          </Alert>
        ) : null}

        <Group justify="flex-start">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button color="red" onClick={handleDeleteUser} loading={loading}>
            Delete
          </Button>
        </Group>
      </Modal>

      {/* Upgrade Subscription Modal */}
      <Modal
        opened={upgradeModalOpen}
        onClose={() => setUpgradeModalOpen(false)}
        title="Upgrade Subscription"
        size="md"
      >
        <Stack>
          <Text>
            You are about to upgrade to the <strong>
              {subscriptionPackages.find(pkg => pkg.id === selectedPackage)?.name}
            </strong> plan.
          </Text>

          <Radio.Group
            label="Billing Period"
            value={subscriptionPeriod}
            onChange={(value) => setSubscriptionPeriod(value as 'monthly' | 'yearly')}
          >
            <Group mt="xs">
              <Radio value="monthly" label="Monthly" />
              <Radio value="yearly" label="Yearly (Save 15%)" />
            </Group>
          </Radio.Group>

          <Paper withBorder p="md">
            <Group justify="space-between">
              <Text>Plan</Text>
              <Text>{subscriptionPackages.find(pkg => pkg.id === selectedPackage)?.name}</Text>
            </Group>

            <Group justify="space-between" mt="xs">
              <Text>Price</Text>
              <Text fw={700}>
                ${subscriptionPeriod === 'monthly'
                  ? subscriptionPackages.find(pkg => pkg.id === selectedPackage)?.price_monthly
                  : subscriptionPackages.find(pkg => pkg.id === selectedPackage)?.price_yearly
                }/{subscriptionPeriod === 'monthly' ? 'month' : 'year'}
              </Text>
            </Group>
          </Paper>

          <Alert color="blue" icon={<IconAlertCircle size={16} />}>
            Your subscription will be upgraded immediately. You will be charged the difference for the current billing period.
          </Alert>

          <Group justify="flex-start">
            <Button variant="outline" onClick={() => setUpgradeModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpgradeSubscription} loading={loading}>
              Confirm Upgrade
            </Button>
          </Group>
        </Stack>
      </Modal>
   </>
  );
}
