"use client";
import React from 'react'
import { Center, Stack, Tooltip, UnstyledButton,} from '@mantine/core';
import SimpleBar from "simplebar-react";
import { useRouter, usePathname } from 'next/navigation';
import {
  IconUsers,
  IconSettings,
  IconLogout,
  IconCreditCard,
  IconLicense,
  IconMessageCircle,
  IconFileInvoice,
  IconFileText,
  IconPackage,
  IconDeviceDesktopAnalytics,
  IconFingerprint,
  IconHome2,
  IconSwitchHorizontal,
  IconReceiptDollar,
  IconUserBitcoin,
  IconCalendarUser,
  IconReplaceUser,
  IconFiles,
    type IconProps,
  } from '@tabler/icons-react';
  import classes from '~/styles/layout.module.css';

interface NavbarLinkProps {
    icon: React.ComponentType<IconProps>;
    label: string;
    active?: boolean;
    onClick?: () => void;
    href?: string;
  }
  function NavbarLink({ icon: Icon, label, active, onClick, href }: NavbarLinkProps) {
    return (
      <Tooltip label={label} position="right" transitionProps={{ duration: 0 }} withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
        <UnstyledButton
          component="a"
          href={href}
          onClick={(event) => {
            if (onClick) {
              event.preventDefault();
              onClick();
            }
          }}
          className={classes.link}
          data-active={active || undefined}
        >
          <Icon size={20} stroke={1.5} />
        </UnstyledButton>
      </Tooltip>
    );
  }
  const mockdata = [
    { icon: IconHome2, label: 'Home', href: '/home' },
    { icon: IconUsers,label: 'Patients',  href: '/patient'},
    { icon: IconCalendarUser, label: 'Visites', href: '/visits' }, 
    { icon: IconReceiptDollar, label: 'Recettes', href: '/payment' },
    { icon: IconFiles, label: 'Comptes rendus', href: '/medical-report/list' },
    { icon: IconUserBitcoin, label: 'Mutuelles', href: '/care-sheet/' },
    { icon: IconFileInvoice, label: 'Facturation', href: '/medical-report/billing/contract' },
    { icon: IconDeviceDesktopAnalytics, label: 'États', href: '/generic-states/' },

    { icon: IconPackage  , label: 'Pharmacy',href: '/pharmacy' },
    { icon: IconMessageCircle ,label: 'Messages',href: '/messaging',  },
    { icon: IconCreditCard ,label: 'Licence', href: '/Licence'  },
    { icon: IconFileText ,label: 'Documents', href: '/documents' }, 
     { icon: IconFingerprint, label: 'Sécurité', href: '/securite' },
     { icon: IconLicense,label: 'Ordonnances', href: '/prescriptions'  },
     { icon: IconReplaceUser, label: 'gestion des utilisateurs', href: '/user-management' },
    { icon: IconSettings, label: 'Parameters', href: '/parameters' },
  ];
  
  interface NavbarProps {
    toggleSidebar: () => void;
  }
  const Navbar = ({ toggleSidebar }: NavbarProps) => {
    const router = useRouter();
    const pathname = usePathname();

    // Function to determine if a route is active
    const isRouteActive = (href: string, currentPath: string): boolean => {
      // Handle exact matches
      if (href === currentPath) return true;

      // Handle dashboard root - if we're at /dashboard, highlight Home
      if (href === '/home' && (currentPath === '/' || currentPath === '/dashboard')) return true;

      // Handle nested routes - if current path starts with the href
      if (currentPath.startsWith(href) && href !== '/') return true;

      return false;
    };

    const link = mockdata.map((link, index) => (
      <div key={index}>
        <NavbarLink
          {...link}
          key={link.label}
          active={isRouteActive(link.href, pathname)}
          href={link.href}
          onClick={() => {
            router.push(link.href);
          }}
        />
      </div>
    ));
const handleLogout = async () => {
  try {
   // await authService.logout();
    // Redirect to the login page instead of the home page
    router.push('/login');
  } catch (error) {
    console.error('Error logging out:', error);
    // Even if there's an error, still try to redirect to login
    router.push('/login');
  }
};
  return (
    <>
    <Center style={{paddingTop:"8px",paddingRight:"6px",height:"56px",borderBottom: "1px solid var(--border-color)"}}>
     <Tooltip label={"Toggle Sidebar"} position="right" transitionProps={{ duration: 0 }}  withArrow className=" bg-[var(--tooltip-bg)] text-daisy">
        <UnstyledButton onClick={toggleSidebar} className={classes.link} >
            <svg xmlns="http://www.w3.org/2000/svg"
            width="22" height="22" viewBox="0 0 24 26"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="tabler-icon tabler-icon-menu-deep "
            style={{
            transform: "rotate(90deg)",
           }}>
           <path d="M4 10h16" />
       <path d="M4 14h16" />
       <path d="M4 18h16" />
       <path d="M9 22l3 3l3 -3" />
      <path d="M9 6l3 -3l3 3" />
              </svg>
        </UnstyledButton>
      </Tooltip>
 </Center>
  <div className={`${classes.navbarMain}  py-1 `}>
    <Stack justify="center" gap={0} className='h-[600px] bg-[var(---mantine-bg-color)] rounded px-[4px]'>
    <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] ">
       <div className='pr-[4px] bg-[var(--bg-nav-hover)] '>
          {link}
          </div>
      </SimpleBar>
    </Stack>
  </div>
    <div className={`py-1 px-[4px]`}>
      <Stack justify="center" gap={0} className='bg-[var(---mantine-bg-color)] rounded'>
      <div className='pr-[2px] bg-[var(--bg-nav-hover)] '>
    <NavbarLink  onClick={handleLogout}  icon={IconSwitchHorizontal} label="Change account" />
    <NavbarLink  onClick={handleLogout} icon={IconLogout} label="Logout" />
    </div>
  </Stack>
      </div>
  </>
  )
}

export default Navbar