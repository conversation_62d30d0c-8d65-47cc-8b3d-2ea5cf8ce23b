'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  Badge,
  Textarea,
  Table,
  ScrollArea,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Tooltip,
  Tabs,
  Radio,
  Pagination,
  Checkbox,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconList,
  IconFile,
  IconUpload,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  IconCurrencyEuro,
  IconClipboardList,
  IconX,
  IconCalculator,
} from '@tabler/icons-react';

interface InventaireItem {
  id: string;
  code: string;
  designation: string;
  qtePhysique: number;
  qteTheorique: number;
  qteEcart: number;
  pmp: number;
  pmpTheorique: number;
  pmpEcart: number;
  montant: number;
}

interface Inventaire {
  id: string;
  reference: string;
  date: Date | null;
  depot: string;
  responsable: string;
  items: InventaireItem[];
  total: number;
  status: 'draft' | 'validated' | 'processed';
}

export default function ListeInventaire() {
  const [inventaires, setInventaires] = useState<Inventaire[]>([]);
  const [currentInventaire, setCurrentInventaire] = useState<Inventaire>({
    id: '1',
    reference: '2',
    date: new Date('2022-09-16'),
    depot: 'Dépôt 1',
    responsable: '',
    items: [],
    total: 0,
    status: 'draft',
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [viewMode, setViewMode] = useState<'form' | 'list'>('form');

  const form = useForm({
    initialValues: {
      reference: currentInventaire.reference,
      date: currentInventaire.date,
      depot: currentInventaire.depot,
      responsable: currentInventaire.responsable,
    },
  });

  const itemForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      qtePhysique: 0,
      qteTheorique: 0,
      pmp: 0,
      pmpTheorique: 0,
    },
  });

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
    'Dépôt Principal',
    'Dépôt Secondaire',
  ];

  const responsables = [
    'Responsable A',
    'Responsable B',
    'Responsable C',
    'Responsable D',
  ];

  const calculateEcarts = (item: Partial<InventaireItem>) => {
    const qteEcart = (item.qtePhysique || 0) - (item.qteTheorique || 0);
    const pmpEcart = (item.pmp || 0) - (item.pmpTheorique || 0);
    const montant = (item.qtePhysique || 0) * (item.pmp || 0);
    
    return { qteEcart, pmpEcart, montant };
  };

  const calculateTotal = () => {
    const total = currentInventaire.items.reduce((sum, item) => sum + item.montant, 0);
    setCurrentInventaire(prev => ({
      ...prev,
      total,
    }));
  };

  const addItem = (values: typeof itemForm.values) => {
    const { qteEcart, pmpEcart, montant } = calculateEcarts(values);
    
    const newItem: InventaireItem = {
      id: Date.now().toString(),
      code: values.code,
      designation: values.designation,
      qtePhysique: values.qtePhysique,
      qteTheorique: values.qteTheorique,
      qteEcart,
      pmp: values.pmp,
      pmpTheorique: values.pmpTheorique,
      pmpEcart,
      montant,
    };

    setCurrentInventaire(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));

    itemForm.reset();
    close();
  };

  const removeItem = (id: string) => {
    setCurrentInventaire(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  const handleSave = (action: 'save' | 'validate') => {
    const updatedInventaire = {
      ...currentInventaire,
      ...form.values,
      status: action === 'save' ? 'draft' : 'validated',
    };

    setCurrentInventaire(updatedInventaire);
    
    const actionText = action === 'save' ? 'enregistré' : 'validé';
    notifications.show({
      title: 'Succès',
      message: `Inventaire ${actionText} avec succès`,
      color: 'green',
    });
  };

  React.useEffect(() => {
    calculateTotal();
  }, [currentInventaire.items]);

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconClipboardList size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Inventaire N°: {currentInventaire.reference}
          </Title>
        </Group>
        <Group>
          <Button
            variant={viewMode === 'form' ? 'filled' : 'outline'}
            leftSection={<IconFile size={16} />}
            onClick={() => setViewMode('form')}
          >
            Nouveau
          </Button>
          <Button
            variant={viewMode === 'list' ? 'filled' : 'outline'}
            leftSection={<IconList size={16} />}
            onClick={() => setViewMode('list')}
          >
            Liste des inventaires
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="Référence de l'inventaire"
                placeholder="Référence"
                {...form.getInputProps('reference')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Dépôt"
                placeholder="Sélectionner un dépôt"
                data={depots}
                rightSection={<IconX size={16} />}
                {...form.getInputProps('depot')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Responsable"
                placeholder="Nom du responsable"
                {...form.getInputProps('responsable')}
                required
                styles={{
                  label: {
                    color: '#ff6b6b',
                    fontWeight: 500,
                  }
                }}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Action Buttons */}
          <Group justify="flex-end" mb="md">
            <Button leftSection={<IconBarcode size={16} />} color="blue">
              Code à barres
            </Button>
            <Button leftSection={<IconShoppingCart size={16} />} color="blue">
              Article
            </Button>
            <Button leftSection={<IconMessageCircle size={16} />} color="blue">
              Commentaire
            </Button>
          </Group>

          {/* Items Table */}
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Code</Table.Th>
                  <Table.Th>Désignation</Table.Th>
                  <Table.Th>Qté Physique</Table.Th>
                  <Table.Th>Qté Théorique</Table.Th>
                  <Table.Th>Qté d'écart</Table.Th>
                  <Table.Th>PMP</Table.Th>
                  <Table.Th>PMP Théorique</Table.Th>
                  <Table.Th>PMP d'écart</Table.Th>
                  <Table.Th>Montant</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {currentInventaire.items.length === 0 ? (
                  <Table.Tr>
                    <Table.Td colSpan={10} className="text-center text-gray-500">
                      Aucun élément trouvé
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  currentInventaire.items.map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>{item.code}</Table.Td>
                      <Table.Td>{item.designation}</Table.Td>
                      <Table.Td>{item.qtePhysique.toFixed(2)}</Table.Td>
                      <Table.Td>{item.qteTheorique.toFixed(2)}</Table.Td>
                      <Table.Td 
                        className={item.qteEcart !== 0 ? (item.qteEcart > 0 ? 'text-green-600' : 'text-red-600') : ''}
                      >
                        {item.qteEcart.toFixed(2)}
                      </Table.Td>
                      <Table.Td>{item.pmp.toFixed(2)}</Table.Td>
                      <Table.Td>{item.pmpTheorique.toFixed(2)}</Table.Td>
                      <Table.Td 
                        className={item.pmpEcart !== 0 ? (item.pmpEcart > 0 ? 'text-green-600' : 'text-red-600') : ''}
                      >
                        {item.pmpEcart.toFixed(2)}
                      </Table.Td>
                      <Table.Td>{item.montant.toFixed(2)}</Table.Td>
                      <Table.Td>
                        <ActionIcon
                          color="red"
                          variant="subtle"
                          onClick={() => removeItem(item.id)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {/* Pagination */}
          <Group justify="space-between" mt="md">
            <Group>
              <Text size="sm">Page</Text>
              <Select
                size="sm"
                w={60}
                data={['1', '2', '3']}
                value={currentPage.toString()}
                onChange={(value) => setCurrentPage(parseInt(value || '1'))}
              />
              <Text size="sm">Lignes par Page</Text>
              <Select
                size="sm"
                w={60}
                data={['10', '25', '50']}
                value={itemsPerPage.toString()}
                onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
              />
              <Text size="sm">0 - 0 de 0</Text>
            </Group>
            <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
          </Group>

          {/* Total */}
          <Group justify="flex-end" mt="xl">
            <Text size="lg" fw={700}>
              Total : {currentInventaire.total.toFixed(2)}
            </Text>
          </Group>

          <Button
            leftSection={<IconPlus size={16} />}
            onClick={open}
            mt="md"
          >
            Ajouter un article
          </Button>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red">
              Annuler
            </Button>
            <Button leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="xl">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder="Code article"
                  {...itemForm.getInputProps('code')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Désignation"
                  placeholder="Désignation"
                  {...itemForm.getInputProps('designation')}
                  required
                />
              </Grid.Col>
            </Grid>
            
            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité Physique"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('qtePhysique')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité Théorique"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('qteTheorique')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité d'écart"
                  placeholder="0.00"
                  decimalScale={2}
                  value={(itemForm.values.qtePhysique || 0) - (itemForm.values.qteTheorique || 0)}
                  disabled
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="PMP"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('pmp')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="PMP Théorique"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('pmpTheorique')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="PMP d'écart"
                  placeholder="0.00"
                  decimalScale={2}
                  value={(itemForm.values.pmp || 0) - (itemForm.values.pmpTheorique || 0)}
                  disabled
                />
              </Grid.Col>
            </Grid>

            <NumberInput
              label="Montant"
              placeholder="0.00"
              decimalScale={2}
              value={(itemForm.values.qtePhysique || 0) * (itemForm.values.pmp || 0)}
              disabled
            />

            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
}
