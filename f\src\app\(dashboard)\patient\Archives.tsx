'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Tabs, Text, Loader, Center } from '@mantine/core';
import FilterList from './FilterList';
import StyleRulesTab from './StyleRulesTab';
import {NouvelleRegle} from './NouvelleRegle';
import { usePatients } from '@/hooks/usePatients';

import {
  Paper,
  Title,
  TextInput,
  Table,
  Group,
  ActionIcon,
  Checkbox,
  Select,
  Pagination,
  Badge,
  Menu,
  Tooltip,

} from '@mantine/core';
import {
  IconSearch,
  IconPrinter,
  IconChevronUp,
  IconChevronDown,
  IconFilter,
  IconCheck,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiFolderAccount,mdiReload ,mdiDotsVertical,mdiAccountPlus} from '@mdi/js';
// Interface pour les données des visites
interface VisiteData {
  id: number;
  date: string;
  nom: string;
  prenom: string;
  dateNaissance: string;
  age: string;
  cin: string;
  telephone: string;
  ville: string;
  assurance: string;
  selected?: boolean;
}
export interface StyleRule {
  uid: string;
  type: "ROW" | "COLUMN";
  target_column: string;
  condition: string;
  style: Record<string, string>;
}


export interface Column {
  id: string;
  label: string;
  field: string;
  isFilter?: boolean;
  order_by: string;
  is_shown: boolean;
}

export default function Archives() {
  const {
    patients,
    loading,
    error,
    totalCount,
    currentPage,
    itemsPerPage,
    totalPages,
    fetchPatientsByStatus,
    searchPatients,
    setPage,
    setItemsPerPage,
    refreshPatients
  } = usePatients({ status: 'archived' });

  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedVisites, setSelectedVisites] = useState<number[]>([]);
 const [tabIndex, setTabIndex] = useState<string | null>('0');
  const [visibleColumns, setVisibleColumns] = useState({
    date: true,
    nom: true,
    prenom: true,
    dateNaissance: true,
    age: true,
    cin: true,
    telephone: true,
    ville: true,
    assurance: true,
    montantTotal: false,
    dateEntree: false,
    motif: false,
    medecinTraitant: false,
  });
const [vm, setVm] = useState({
    styleRules: [],      // [] ou données initiales
    is_create: false,
    columns: [],
    mnModel: {},
    draftRule: {},
  });
  const [isCreate, setIsCreate] = useState(true);

const [columns, ] = useState<Column[]>([
  {
    id: "1",
    label: "Nom",
    field: "last_name",
    order_by: "last_name",
    is_shown: true,
  },
  {
    id: "2",
    label: "Prénom",
    field: "first_name",
    order_by: "first_name",
    is_shown: true,
  },
]);
const [styleRules, setStyleRules] = useState<StyleRule[]>([
  {
    uid: "rule-1",
    type: "COLUMN",
    target_column: "last_name",
    condition: "equals",
    style: { color: "#000", fontWeight: "bold" },
  },
]);
const handleCancel = () => {
  setIsCreate(false);
};

const handleSave = (rule: StyleRule) => {
  setStyleRules((prev) => [...prev, rule]);
};
  // Convert Patient data to VisiteData format for compatibility
  const visitesData: VisiteData[] = loading ? [] : patients.map((patient, index) => ({
    id: parseInt(patient.id) || index + 1,
    date: patient.created_at ? new Date(patient.created_at).toLocaleDateString('fr-FR') : '',
    nom: patient.last_name || '',
    prenom: patient.first_name || '',
    dateNaissance: patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString('fr-FR') : '',
    age: patient.age?.toString() || '',
    cin: patient.national_id_number || '',
    telephone: patient.phone_number || '',
    ville: patient.address || '',
    assurance: patient.insurance_company || '',
  }));

  // Handle search with backend
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim()) {
      searchPatients({ search: term, status: 'archived' });
    } else {
      fetchPatientsByStatus('archived');
    }
  };

  // Mock data for fallback (remove this once backend is fully connected)
 

  // Filtrer les données selon le terme de recherche
  const filteredVisites = visitesData.filter(visite => {
    const searchLower = searchTerm.toLowerCase();
    return (
      visite.nom.toLowerCase().includes(searchLower) ||
      visite.prenom.toLowerCase().includes(searchLower) ||
      visite.cin.toLowerCase().includes(searchLower) ||
      visite.telephone.toLowerCase().includes(searchLower) ||
      visite.ville.toLowerCase().includes(searchLower) ||
      visite.assurance.toLowerCase().includes(searchLower)
    );
  });

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Trier les données
  const sortedVisites = [...filteredVisites].sort((a, b) => {
    if (!sortField) return 0;

    const aValue = a[sortField as keyof VisiteData] || '';
    const bValue = b[sortField as keyof VisiteData] || '';

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Pagination
  const totalPagesLocal = Math.ceil(sortedVisites.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentVisites = sortedVisites.slice(startIndex, endIndex);

  // Fonction pour rendre l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <IconChevronUp size={14} /> :
      <IconChevronDown size={14} />;
  };

  // Gestion de la sélection
  const handleSelectVisite = (id: number) => {
    setSelectedVisites(prev =>
      prev.includes(id)
        ? prev.filter(visiteId => visiteId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedVisites.length === currentVisites.length) {
      setSelectedVisites([]);
    } else {
      setSelectedVisites(currentVisites.map(visite => visite.id));
    }
  };

  // Fonction pour obtenir le badge d'assurance
  const getAssuranceBadge = (assurance: string) => {
    if (!assurance) return null;

    const colors: { [key: string]: string } = {
      'CNOPS': 'blue',
      'CNSS': 'green',
      'AXA': 'orange',
      'RAMED': 'red',
    };

    return (
      <Badge
        color={colors[assurance] || 'gray'}
        variant="light"
        size="sm"
      >
        {assurance}
      </Badge>
    );
  };

  // Fonction pour basculer la visibilité des colonnes
  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev]
    }));
  };
 const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };
    
return (
  <>
    <div className="p-6 bg-gray-50 min-h-screen w-full">
      <Paper className="shadow-sm">
        {/* En-tête */}
        <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
          <Group justify="space-between" align="center">
            <Group gap="xs">
              <Icon path={mdiFolderAccount} size={1} />
              <Title order={4} className="text-white font-medium">
                 Liste des patients
              </Title>
            </Group>
            <Group gap="xs">
                 <Tooltip label="Recharger les colonnes">
              <ActionIcon variant="subtle" color="white">
                <Icon path={mdiReload} size={1} />
              </ActionIcon>
              </Tooltip>
              <Tooltip label="Exporter vers Excel">
              <ActionIcon variant="subtle" color="white">
                <IconPrinter size={18} />
              </ActionIcon>
              </Tooltip>
            {/* Menu de filtres des colonnes */}
            <Menu shadow="md" width={250}>
              <Menu.Target>
                <ActionIcon variant="subtle" color="gray" size="lg">
                    <Icon path={mdiDotsVertical} size={1} color="#ffffff" />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Label>Colonnes visibles</Menu.Label>
                <Menu.Item
                  leftSection={visibleColumns.date ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('date')}
                >
                  Date de visite
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.nom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('nom')}
                >
                  Nom
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.prenom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('prenom')}
                >
                  Prénom
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.dateNaissance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('dateNaissance')}
                >
                  Date de naissance
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.age ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('age')}
                >
                  Age
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.cin ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('cin')}
                >
                  CINE
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.telephone ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('telephone')}
                >
                  Téléphone
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.ville ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('ville')}
                >
                  Ville
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.assurance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('assurance')}
                >
                  Assurance
                </Menu.Item>

                <Menu.Divider />
                <Menu.Label>Colonnes supplémentaires</Menu.Label>
                <Menu.Item
                  leftSection={visibleColumns.montantTotal ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('montantTotal')}
                >
                  Montant total (DHS)
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.dateEntree ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('dateEntree')}
                >
                  Date d&apos;entrée
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.motif ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('motif')}
                >
                  Motif
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.medecinTraitant ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('medecinTraitant')}
                >
                  Médecin traitant
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
             
            </Group>
          </Group>
        </div>
 
        {/* Barre de recherche */}
        <div className="p-4 border-b border-gray-200">
          <Group justify="flex-start" align="center">
            <div>
              <ActionIcon variant="subtle" color="gray" size="lg" className="cursor-pointer"
              onClick={(event) => {
                event.preventDefault();
                toggleSidebar(); // Toggle sidebar visibility
              }}
              >
                  <IconFilter size={18} />
                </ActionIcon>
                  </div>
                <div  className="w-[93%] ">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className=" w-full"
            />
</div>
<div>
        <ActionIcon variant="subtle" color="gray" size="lg" className="cursor-pointer"
              component={Link} href={`/patient/add`}
              >
                  <Icon path={mdiAccountPlus} size={1} color={"#3799ce"} />
                </ActionIcon>
                </div>
          </Group>
        </div>

        
        
          <div className='flex mb-10 ' >
         <div className={isSidebarVisible ?  "w-[19%] mt-0": "hidden "}>
           {isSidebarVisible && (

        <Tabs value={tabIndex} onChange={setTabIndex}>
              <Tabs.List>
                <Tabs.Tab value="0">Filtre avancé</Tabs.Tab>
                <Tabs.Tab value="1">Règles de mise en forme</Tabs.Tab>
                <Tabs.Tab value="2">3</Tabs.Tab>
              </Tabs.List>
        
              <Tabs.Panel value="0" pt="xs">
                <FilterList />
              </Tabs.Panel>
        
              <Tabs.Panel value="1" pt="xs">
            <StyleRulesTab
              styleRules={vm.styleRules}
              isCreate={vm.is_create}
              onStartCreate={() => setVm((prev) => ({ ...prev, is_create: true }))}
              columns={vm.columns}      // ✅ maintenant reconnu
              model={vm.mnModel}
              draftRule={vm.draftRule}
            />
                <Tabs.Panel value="2" pt="xs">

                  {/* if click plus icon shwo tabs */}
             <NouvelleRegle
  columns={columns}
  styleRules={styleRules}
  isCreate={isCreate}
  onCancel={handleCancel}
  onSave={handleSave}
/>

         </Tabs.Panel>
        </Tabs.Panel>
              
            </Tabs>
 
  
        
        )}
        </div>
        {/* Tableau */}
        <div className={isSidebarVisible ?  "w-[80%]": "w-full "}>
         <div className="overflow-x-auto">
          <Table
            striped
            highlightOnHover
            withTableBorder={true}
            className="min-w-full"
             withColumnBorders
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr >
                <Table.Th className="w-12 ">
                  <Checkbox
                    checked={selectedVisites.length === currentVisites.length && currentVisites.length > 0}
                    indeterminate={selectedVisites.length > 0 && selectedVisites.length < currentVisites.length}
                    onChange={handleSelectAll}
                  />
                </Table.Th>
                {visibleColumns.date && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('date')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de visite
                      {renderSortIcon('date')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.nom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('nom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Nom
                      {renderSortIcon('nom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.prenom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('prenom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Prénom
                      {renderSortIcon('prenom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateNaissance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateNaissance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de naissance
                      {renderSortIcon('dateNaissance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.age && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('age')}
                  >
                    <Group gap="xs" justify="space-between">
                      Age
                      {renderSortIcon('age')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.cin && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('cin')}
                  >
                    <Group gap="xs" justify="space-between">
                      CIN
                      {renderSortIcon('cin')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.telephone && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('telephone')}
                  >
                    <Group gap="xs" justify="space-between">
                      Téléphone
                      {renderSortIcon('telephone')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.ville && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ville')}
                  >
                    <Group gap="xs" justify="space-between">
                      Ville
                      {renderSortIcon('ville')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.assurance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('assurance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Assurance
                      {renderSortIcon('assurance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.montantTotal && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('montantTotal')}
                  >
                    <Group gap="xs" justify="space-between">
                      Montant total (DHS)
                      {renderSortIcon('montantTotal')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateEntree && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateEntree')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date d&apos;entrée
                      {renderSortIcon('dateEntree')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.motif && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('motif')}
                  >
                    <Group gap="xs" justify="space-between">
                      Motif
                      {renderSortIcon('motif')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.medecinTraitant && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('medecinTraitant')}
                  >
                    <Group gap="xs" justify="space-between">
                      Médecin traitant
                      {renderSortIcon('medecinTraitant')}
                    </Group>
                  </Table.Th>
                )}
                <Table.Th className="w-42"></Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {currentVisites.map((visite) => (
                <Table.Tr key={visite.id} className="hover:bg-gray-50">
                  <Table.Td>
                    <Checkbox
                      checked={selectedVisites.includes(visite.id)}
                      onChange={() => handleSelectVisite(visite.id)}
                    />
                  </Table.Td>
                  {visibleColumns.date && (
                    <Table.Td className="font-medium">{visite.date}</Table.Td>
                  )}
                  {visibleColumns.nom && (
                    <Table.Td className="font-medium">{visite.nom}</Table.Td>
                  )}
                  {visibleColumns.prenom && (
                    <Table.Td>{visite.prenom}</Table.Td>
                  )}
                  {visibleColumns.dateNaissance && (
                    <Table.Td className="text-sm text-gray-600">{visite.dateNaissance}</Table.Td>
                  )}
                  {visibleColumns.age && (
                    <Table.Td className="text-sm text-gray-600">{visite.age}</Table.Td>
                  )}
                  {visibleColumns.cin && (
                    <Table.Td className="text-sm">{visite.cin || '-'}</Table.Td>
                  )}
                  {visibleColumns.telephone && (
                    <Table.Td className="text-sm">{visite.telephone || '-'}</Table.Td>
                  )}
                  {visibleColumns.ville && (
                    <Table.Td className="text-sm">{visite.ville}</Table.Td>
                  )}
                  {visibleColumns.assurance && (
                    <Table.Td>
                      {getAssuranceBadge(visite.assurance)}
                    </Table.Td>
                  )}
                  {visibleColumns.montantTotal && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.dateEntree && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.motif && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.medecinTraitant && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  <Table.Td w={"250px"}>
                   
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
        {/* Pagination */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <Group justify="space-between" align="center">
            <Group gap="xs">
              <Text size="sm" className="text-gray-600">
                Page
              </Text>
              <Select
                value={currentPage.toString()}
                onChange={(value) => setPage(Number(value))}
                data={Array.from({ length: totalPages }, (_, i) => ({
                  value: (i + 1).toString(),
                  label: (i + 1).toString(),
                }))}
                size="sm"
                className="w-20"
              />
              <Text size="sm" className="text-gray-600">
                Lignes par Page
              </Text>
              <Select
                value={itemsPerPage.toString()}
                onChange={(value) => {
                  setItemsPerPage(Number(value));
                  setPage(1);
                }}
                data={[
                  { value: '10', label: '10' },
                  { value: '15', label: '15' },
                  { value: '25', label: '25' },
                  { value: '50', label: '50' },
                ]}
                size="sm"
                className="w-20"
              />
              <Text size="sm" className="text-gray-600">
                1 - {Math.min(endIndex, sortedVisites.length)} de {sortedVisites.length}
              </Text>
            </Group>

            <Pagination
              value={currentPage}
              onChange={setPage}
              total={totalPagesLocal}
              size="sm"
              withEdges
            />
          </Group>
            </div>
            </div>
        </div>
      </Paper>
        
    </div>
   
    </>
  );
};



