"""
Serializers for the appointments app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.db import transaction
from .models import Appointment, DentistryAppointment, Appointment<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>V<PERSON><PERSON>, PresenceList, HistoryJournal

User = get_user_model()


class AppointmentSerializer(serializers.ModelSerializer):
    """Serializer for Appointment model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    patient_id = serializers.CharField(source='patient.id', read_only=True)
    doctor_name = serializers.SerializerMethodField()
    appointment_datetime = serializers.DateTimeField(read_only=True)
    is_past = serializers.BooleanField(read_only=True)
    is_today = serializers.BooleanField(read_only=True)

    # CRITICAL: Add missing patient fields from User model
    date_of_birth = serializers.SerializerMethodField()
    birth_date = serializers.SerializerMethodField()  # Add birth_date field
    age = serializers.SerializerMethodField()
    landline_number = serializers.SerializerMethodField()
    patient_email = serializers.SerializerMethodField()
    patient_phone = serializers.SerializerMethodField()
    patient_address = serializers.SerializerMethodField()
    patient_title = serializers.SerializerMethodField()
    # Add camelCase aliases for frontend compatibility
    fatherName = serializers.SerializerMethodField()
    motherName = serializers.SerializerMethodField()
    birthPlace = serializers.SerializerMethodField()
    bloodGroup = serializers.SerializerMethodField()

    def get_doctor_name(self, obj):
        """Get doctor name safely."""
        if obj.doctor:
            return f"Dr. {obj.doctor.first_name} {obj.doctor.last_name}".strip()
        elif obj.doctor_assigned:
            return f"Dr. {obj.doctor_assigned}".strip()
        return "Dr. Non assigné"

    def get_date_of_birth(self, obj):
        """Get patient date of birth."""
        return obj.patient.date_of_birth if obj.patient else None

    def get_birth_date(self, obj):
        """Get patient birth date (same as date_of_birth for compatibility)."""
        return obj.patient.date_of_birth if obj.patient else None

    def get_age(self, obj):
        """Get patient age."""
        return obj.patient.age if obj.patient else None

    def get_landline_number(self, obj):
        """Get patient landline number."""
        return obj.patient.landline_number if obj.patient else None

    def get_patient_email(self, obj):
        """Get patient email."""
        return obj.patient.email if obj.patient else None

    def get_patient_phone(self, obj):
        """Get patient phone from User model."""
        return obj.patient.phone_number if obj.patient and obj.patient.phone_number else None

    def get_patient_address(self, obj):
        """Get patient address from User model."""
        return obj.patient.address if obj.patient and obj.patient.address else None

    def get_patient_title(self, obj):
        """Get patient title from User model."""
        return obj.patient.title if obj.patient and hasattr(obj.patient, 'title') else None

    def get_fatherName(self, obj):
        """Get father name (camelCase alias for frontend)."""
        return obj.father_name if obj.father_name else None

    def get_motherName(self, obj):
        """Get mother name (camelCase alias for frontend)."""
        return obj.mother_name if obj.mother_name else None

    def get_birthPlace(self, obj):
        """Get birth place (camelCase alias for frontend)."""
        return obj.birth_place if obj.birth_place else None

    def get_bloodGroup(self, obj):
        """Get blood group (camelCase alias for frontend)."""
        return obj.blood_group if obj.blood_group else None
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'patient', 'doctor', 'doctor_or_assistant', 'patient_name', 'patient_id', 'doctor_name',
            'title', 'description', 'appointment_type', 'status', 'priority',
            'appointment_date', 'appointment_time', 'end_time', 'duration_minutes',
            'appointment_datetime', 'is_past', 'is_today',
            'room', 'resource_id', 'equipment_needed', 'notes', 'reason_for_visit', 'symptoms',
            'estimated_cost', 'insurance_covered',
            'reminder_sent', 'reminder_sent_at',
            'color', 'event_type', 'is_waiting_list', 'is_active', 'is_in_presentation_room', 'is_in_history_journal',
            'consultation_type', 'doctor_assigned', 'agenda',
            # CRITICAL: Include ALL patient fields that exist in Appointment model
            'gender', 'etat_civil', 'cin', 'social_security', 'profession', 'birth_place',
            'father_name', 'mother_name', 'blood_group', 'allergies',
            # CRITICAL: Include room and event fields for proper display
            'event_resource_id',
            # CRITICAL: Include fields that actually exist in the model
            'patient_title', 'reason_for_visit', 'symptoms',
            # CRITICAL: Add comment fields
            'comment', 'Commentairelistedattente',
            # CRITICAL: Add missing fields from User model (for patients)
            'patient_phone', 'patient_address',
            # CRITICAL: Add birth date, age, landline, email from User model
            'date_of_birth', 'birth_date', 'age', 'landline_number', 'patient_email',
            # CRITICAL: Add camelCase aliases for frontend compatibility
            'fatherName', 'motherName', 'birthPlace', 'bloodGroup',
            'created_at', 'updated_at', 'created_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'appointment_datetime', 'is_past', 'is_today']

    def create(self, validated_data):
        """Create appointment with current user as creator."""
        request = self.context.get('request')
        if request and request.user and request.user.is_authenticated:
            validated_data['created_by'] = request.user
        else:
            # For unauthenticated requests, don't set created_by
            validated_data.pop('created_by', None)
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """Update appointment with proper room and field mapping."""
        # CRITICAL: Handle room and resource_id mapping during updates
        resource_id = validated_data.get('resource_id')
        if resource_id:
            # Map resource_id to room field for admin display
            if resource_id == 'room-a':
                validated_data['room'] = 'Room A'
            elif resource_id == 'room-b':
                validated_data['room'] = 'Room B'
            else:
                validated_data['room'] = resource_id

            # Also set event_resource_id for consistency
            validated_data['event_resource_id'] = resource_id

            print(f"🏠 UPDATE ROOM MAPPING: resource_id='{resource_id}' → room='{validated_data['room']}'")

        # CRITICAL: Handle doctor_assigned field mapping to doctor_or_assistant during updates
        doctor_assigned = validated_data.pop('doctor_assigned', None)
        if doctor_assigned:
            try:
                # Try to find the doctor/assistant user by ID
                doctor_user = User.objects.get(id=doctor_assigned, user_type__in=['doctor', 'assistant'])
                validated_data['doctor_or_assistant'] = doctor_user
                # Also set the doctor field if it's a doctor
                if doctor_user.user_type == 'doctor':
                    validated_data['doctor'] = doctor_user
                print(f"✅ UPDATE: Set doctor_or_assistant from doctor_assigned: {doctor_user.get_full_name()}")
            except User.DoesNotExist:
                print(f"⚠️ UPDATE: Doctor/Assistant with ID {doctor_assigned} not found")
                # Keep the original value as a string for fallback
                validated_data['doctor_assigned'] = doctor_assigned

        # CRITICAL: Handle patient_title field properly
        patient_title = validated_data.get('patient_title')
        if patient_title:
            # Ensure patient_title is properly saved to the appointment
            print(f"✅ UPDATE: Setting patient_title: {patient_title}")
        
        # Call parent update method
        updated_instance = super().update(instance, validated_data)

        # Log the room mapping for debugging
        if resource_id:
            print(f"✅ UPDATE: Room fields updated - room: '{updated_instance.room}', resource_id: '{updated_instance.resource_id}', event_resource_id: '{updated_instance.event_resource_id}'")

        return updated_instance


class DentistryAppointmentSerializer(serializers.ModelSerializer):
    """Serializer for DentistryAppointment model."""
    
    appointment_details = AppointmentSerializer(source='appointment', read_only=True)
    
    class Meta:
        model = DentistryAppointment
        fields = [
            'id', 'appointment', 'appointment_details',
            'procedure_type', 'tooth_numbers', 'quadrant',
            'anesthesia_required', 'anesthesia_type', 'pre_medication', 'post_care_instructions',
            'follow_up_required', 'follow_up_date', 'follow_up_notes',
            'materials_used', 'lab_work_required', 'lab_instructions',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AppointmentReminderSerializer(serializers.ModelSerializer):
    """Serializer for AppointmentReminder model."""
    
    appointment_title = serializers.CharField(source='appointment.title', read_only=True)
    patient_name = serializers.CharField(source='appointment.patient.get_full_name', read_only=True)
    
    class Meta:
        model = AppointmentReminder
        fields = [
            'id', 'appointment', 'appointment_title', 'patient_name',
            'reminder_type', 'status', 'send_at', 'sent_at',
            'subject', 'message', 'attempts', 'error_message',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'sent_at', 'attempts', 'created_at', 'updated_at']


class AppointmentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating appointments with dentistry details."""

    # Optional dentistry details
    dentistry_details = DentistryAppointmentSerializer(required=False)

    # Patient creation fields (optional)
    patient_title = serializers.CharField(required=False, allow_blank=True, write_only=True)
    patient_first_name = serializers.CharField(required=False, write_only=True)
    patient_last_name = serializers.CharField(required=False, write_only=True)
    patient_email = serializers.EmailField(required=False, allow_blank=True, write_only=True)
    patient_phone = serializers.CharField(required=False, allow_blank=True, write_only=True)
    patient_address = serializers.CharField(required=False, allow_blank=True, write_only=True)
    # CRITICAL: Add missing patient fields to serializer
    birth_date = serializers.DateField(required=False, allow_null=True, write_only=True)
    age = serializers.IntegerField(required=False, allow_null=True, write_only=True)
    landline_number = serializers.CharField(required=False, allow_blank=True, write_only=True)
    
    # CRITICAL: Add doctor_assigned field to handle UUID values from frontend
    doctor_assigned = serializers.CharField(required=False, allow_blank=True, write_only=True)

    class Meta:
        model = Appointment
        fields = [
            'id',  # CRITICAL: Include ID field for frontend updates
            'patient', 'doctor', 'doctor_or_assistant', 'title', 'description', 'appointment_type', 'status', 'priority',
            'appointment_date', 'appointment_time', 'duration_minutes', 'consultation_duration',
            'room', 'equipment_needed', 'notes', 'reason_for_visit', 'symptoms',
            # CRITICAL: Add comment fields
            'comment', 'Commentairelistedattente',
            'estimated_cost', 'insurance_covered', 'dentistry_details',
            # Enhanced fields
            'color', 'event_type', 'is_waiting_list', 'is_active', 'is_in_presentation_room', 'is_in_history_journal', 'patient_phone',
            'patient_address', 'consultation_type', 'agenda', 'resource_id',
            # Patient creation fields
            'patient_title', 'patient_first_name', 'patient_last_name', 'patient_email',
            'patient_phone', 'patient_address',
            # CRITICAL: Add missing patient fields
            'birth_date', 'age', 'landline_number',
            # Additional patient fields
            'gender', 'etat_civil', 'cin', 'social_security', 'profession', 'birth_place',
            'father_name', 'mother_name', 'blood_group', 'allergies',
            # Event settings
            'event_resource_id', 'add_to_waiting_list', 'checked_appel_video',
            'checked_rappel_sms', 'checked_rappel_email',
            # Custom write-only fields for processing
            'doctor_assigned'
        ]
        read_only_fields = ['id']  # Make ID read-only
        extra_kwargs = {
            'patient': {'required': False}  # Make patient optional since we can create one
        }

    def create(self, validated_data):
        """Create appointment with optional dentistry details and patient creation."""
        from django.db import transaction

        dentistry_data = validated_data.pop('dentistry_details', None)

        # Extract patient creation data
        patient_email = validated_data.pop('patient_email', None)
        patient_title = validated_data.pop('patient_title', '')  # Extract but don't remove from validated_data yet
        patient_data = {
            'first_name': validated_data.pop('patient_first_name', ''),
            'last_name': validated_data.pop('patient_last_name', ''),
            'email': patient_email,  # Keep the original value, don't default to empty string
            'phone_number': validated_data.pop('patient_phone', ''),
            'address': validated_data.pop('patient_address', ''),
            # CRITICAL: Include gender and other patient fields - USE POP TO REMOVE FROM validated_data
            'gender': validated_data.pop('gender', ''),
            'title': patient_title,
            # CRITICAL: Add missing patient fields (fix field name mapping) - USE POP TO REMOVE FROM validated_data
            'date_of_birth': validated_data.pop('birth_date', None) or validated_data.pop('date_of_birth', None),
            'age': validated_data.pop('age', None),
            'landline_number': validated_data.pop('landline_number', '') or validated_data.pop('patient_landline', ''),
        }
        
        # CRITICAL: Set patient_title on appointment model from the extracted value
        if patient_title:
            validated_data['patient_title'] = patient_title



        with transaction.atomic():  # type: ignore
            # Initialize patient_user variable
            patient_user = None
            
            # Handle patient creation/lookup if no patient ID provided
            if 'patient' not in validated_data or not validated_data['patient']:
                if not patient_data['first_name'] or not patient_data['last_name']:
                    raise serializers.ValidationError({
                        'patient': 'Either provide a patient ID or patient first_name and last_name'
                    })

                # FIXED EMAIL PROCESSING LOGIC
                print(f"🔍 EMAIL PROCESSING: Starting email validation...")
                print(f"🔍 EMAIL PROCESSING: patient_data['email'] = {repr(patient_data['email'])}")

                # Check if we have a real email (not None, not empty string, not just whitespace)
                has_real_email = (
                    patient_data['email'] is not None and
                    isinstance(patient_data['email'], str) and
                    patient_data['email'].strip() != ''
                )

                print(f"🔍 EMAIL PROCESSING: has_real_email = {has_real_email}")

                if has_real_email:
                    real_email = patient_data['email'].strip()
                    print(f"📧 USING REAL EMAIL: '{real_email}'")

                    try:
                        # Try to find existing patient with this email
                        patient_user = User.objects.get(email=real_email, user_type='patient')
                        print(f"✅ Found existing patient with email: {patient_user.email}")

                        # CRITICAL: Update existing patient with new data if provided
                        updated = False
                        if patient_data.get('first_name') and patient_user.first_name != patient_data['first_name']:
                            patient_user.first_name = patient_data['first_name']
                            updated = True
                        if patient_data.get('last_name') and patient_user.last_name != patient_data['last_name']:
                            patient_user.last_name = patient_data['last_name']
                            updated = True
                        if patient_data.get('phone_number') and patient_user.phone_number != patient_data['phone_number']:
                            patient_user.phone_number = patient_data['phone_number']
                            updated = True
                        if patient_data.get('address') and patient_user.address != patient_data['address']:
                            patient_user.address = patient_data['address']
                            updated = True
                        if patient_data.get('gender') and patient_user.gender != patient_data['gender']:
                            patient_user.gender = patient_data['gender']
                            updated = True
                        if patient_data.get('title') and patient_user.title != patient_data['title']:
                            patient_user.title = patient_data['title']
                            updated = True
                        if patient_data.get('date_of_birth') and patient_user.date_of_birth != patient_data['date_of_birth']:
                            patient_user.date_of_birth = patient_data['date_of_birth']
                            updated = True
                        if patient_data.get('age') and patient_user.age != patient_data['age']:
                            patient_user.age = patient_data['age']
                            updated = True
                        if patient_data.get('landline_number') and patient_user.landline_number != patient_data['landline_number']:
                            patient_user.landline_number = patient_data['landline_number']
                            updated = True

                        if updated:
                            patient_user.save()
                            print(f"✅ Updated existing patient with new data")
                    except User.DoesNotExist:
                        try:
                            # Create new patient user with the REAL email
                            patient_user = User.objects.create_user(
                                email=real_email,  # Use the REAL email exactly as provided
                                first_name=patient_data['first_name'],
                                last_name=patient_data['last_name'],
                                phone_number=patient_data['phone_number'],
                                address=patient_data['address'],
                                user_type='patient',
                                password='temp123',  # Temporary password
                                # CRITICAL: Add gender and title to patient creation
                                gender=patient_data.get('gender', ''),
                                title=patient_data.get('title', ''),
                                # CRITICAL: Add missing patient fields (fix field name mapping)
                                date_of_birth=patient_data.get('date_of_birth', None),
                                age=patient_data.get('age', None),
                                landline_number=patient_data.get('landline_number', '')
                            )
                            print(f"✅ SUCCESS: Created new patient with REAL email: {patient_user.email}")
                        except Exception as e:
                            print(f"❌ Failed to create patient with real email '{real_email}': {e}")
                            # If email already exists but not as patient, try to find any user with this email
                            try:
                                patient_user = User.objects.get(email=real_email)
                                print(f"✅ Found existing user (non-patient) with email: {patient_user.email}")
                                # Convert to patient if needed
                                if patient_user.user_type != 'patient':
                                    patient_user.user_type = 'patient'
                                    patient_user.save()
                                    print(f"✅ Converted user to patient type")
                            except User.DoesNotExist:
                                raise serializers.ValidationError({
                                    'patient_email': f'Failed to create or find patient with email {real_email}: {str(e)}'
                                })
                else:
                    # Generate temporary email only when NO real email is provided
                    print(f"⚠️ NO REAL EMAIL PROVIDED - generating temporary email")
                    import uuid
                    # Generate unique email to avoid conflicts
                    base_email = f"{patient_data['first_name'].lower()}.{patient_data['last_name'].lower()}"
                    unique_id = uuid.uuid4().hex[:8]
                    temp_email = f"{base_email}.{unique_id}@temp.com"

                    # Ensure email is unique
                    counter = 1
                    while User.objects.filter(email=temp_email).exists():
                        temp_email = f"{base_email}.{unique_id}.{counter}@temp.com"
                        counter += 1

                    try:
                        patient_user = User.objects.create_user(
                            email=temp_email,
                            first_name=patient_data['first_name'],
                            last_name=patient_data['last_name'],
                            phone_number=patient_data['phone_number'],
                            address=patient_data['address'],
                            user_type='patient',
                            password='temp123',  # Temporary password
                            # CRITICAL: Add missing patient fields for temporary email patients too
                            gender=patient_data.get('gender', ''),
                            title=patient_data.get('title', ''),
                            date_of_birth=patient_data.get('date_of_birth', None),
                            age=patient_data.get('age', None),
                            landline_number=patient_data.get('landline_number', '')
                        )
                        print(f"✅ Created patient with temporary email: {patient_user.email}")
                    except Exception as e:
                        print(f"❌ Failed to create patient with temporary email: {e}")
                        raise serializers.ValidationError({
                            'patient': f'Failed to create patient: {str(e)}'
                        })

                validated_data['patient'] = patient_user

            # CRITICAL: Handle room and resource_id mapping
            resource_id = validated_data.get('resource_id')
            if resource_id:
                # Map resource_id to room field for admin display
                if resource_id == 'room-a':
                    validated_data['room'] = 'Room A'
                elif resource_id == 'room-b':
                    validated_data['room'] = 'Room B'
                else:
                    validated_data['room'] = resource_id

                # Also set event_resource_id for consistency
                validated_data['event_resource_id'] = resource_id

                print(f"🏠 ROOM MAPPING: resource_id='{resource_id}' → room='{validated_data['room']}'")

            # CRITICAL: Ensure gender is saved to Appointment model (copy from User model)
            if patient_user and patient_user.gender:
                validated_data['gender'] = patient_user.gender
                print(f"✅ Set appointment gender from user: {patient_user.gender}")
            elif patient_data.get('gender'):
                validated_data['gender'] = patient_data.get('gender')
                print(f"✅ Set appointment gender from form data: {patient_data.get('gender')}")
            else:
                validated_data['gender'] = 'Homme'  # Default value
                print(f"✅ Set appointment gender to default: Homme")

            # CRITICAL: Fields birth_date, age, landline_number are already popped during patient creation

            # CRITICAL: Handle doctor_assigned field mapping to doctor_or_assistant
            doctor_assigned = validated_data.pop('doctor_assigned', None)
            if doctor_assigned:
                try:
                    # Try to find the doctor/assistant user by ID
                    doctor_user = User.objects.get(id=doctor_assigned, user_type__in=['doctor', 'assistant'])
                    validated_data['doctor_or_assistant'] = doctor_user
                    # Also set the doctor field if it's a doctor
                    if doctor_user.user_type == 'doctor':
                        validated_data['doctor'] = doctor_user
                    print(f"✅ Set doctor_or_assistant from doctor_assigned: {doctor_user.get_full_name()}")
                except User.DoesNotExist:
                    print(f"⚠️ Doctor/Assistant with ID {doctor_assigned} not found")
                    # Keep the original value as a string for fallback
                    validated_data['doctor_assigned'] = doctor_assigned
            
            # Set creator (only if user is authenticated)
            request = self.context.get('request')
            if request and request.user and request.user.is_authenticated:
                validated_data['created_by'] = request.user
            else:
                # For unauthenticated requests, don't set created_by
                validated_data.pop('created_by', None)

            appointment = Appointment.objects.create(**validated_data)  # type: ignore

            # Create dentistry details if provided
            if dentistry_data:
                DentistryAppointment.objects.create(  # type: ignore
                    appointment=appointment,
                    **dentistry_data
                )

            return appointment


class AppointmentListSerializer(serializers.ModelSerializer):
    """Simplified serializer for appointment lists."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    doctor_name = serializers.SerializerMethodField()
    has_dentistry_details = serializers.SerializerMethodField()
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'patient', 'patient_name', 'doctor_name', 'title', 'appointment_type', 'status',
            'appointment_date', 'appointment_time', 'duration_minutes',
            'has_dentistry_details', 'created_at',
            # CRITICAL: Add room fields for frontend room mapping
            'resource_id', 'event_resource_id', 'room'
        ]
    
    def get_doctor_name(self, obj) -> str:
        """Get doctor name safely with enhanced fallback logic following memory requirements."""
        # Check doctor_or_assistant field first (new enhanced field)
        user = None
        if hasattr(obj, 'doctor_or_assistant') and obj.doctor_or_assistant:
            user = obj.doctor_or_assistant
        # Fallback to doctor field (legacy)
        elif obj.doctor:
            user = obj.doctor
        # Fallback to doctor_assigned text field
        elif obj.doctor_assigned:
            return f"Dr. {obj.doctor_assigned}".strip()
        else:
            return "Dr. Non assigné"
            
        # Multi-level fallback name formatting per memory requirements
        first_name: str = getattr(user, 'first_name', '').strip() if hasattr(user, 'first_name') else ''
        last_name: str = getattr(user, 'last_name', '').strip() if hasattr(user, 'last_name') else ''
        email: str = getattr(user, 'email', '').strip() if hasattr(user, 'email') else ''
        user_type: str = getattr(user, 'user_type', '').strip() if hasattr(user, 'user_type') else ''
        
        # Level 1: firstName + lastName
        if first_name and last_name:
            full_name = f"{first_name} {last_name}"
        # Level 2: firstName only
        elif first_name:
            full_name = first_name
        # Level 3: lastName only  
        elif last_name:
            full_name = last_name
        # Level 4: email prefix (before @)
        elif email and '@' in email:
            full_name = email.split('@')[0]
        # Level 5: default value
        else:
            full_name = "Utilisateur sans nom"
            
        # Add appropriate prefix based on user type
        if user_type == 'doctor':
            return f"Dr. {full_name}"
        elif user_type == 'assistant':
            return f"Assistant {full_name}"
        else:
            return f"Dr. {full_name}"

    def get_has_dentistry_details(self, obj):
        """Check if appointment has dentistry details."""
        return hasattr(obj, 'dentistry_details')


class DoctorPauseSerializer(serializers.ModelSerializer):
    """Serializer for DoctorPause model."""

    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    duration_minutes = serializers.IntegerField(read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = DoctorPause
        fields = [
            'id', 'doctor', 'doctor_name', 'title',
            'date_from', 'date_to', 'duration_minutes',
            'room', 'resource_id', 'color',
            'notes', 'is_recurring',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'duration_minutes', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Create pause with creator information and room mapping."""
        request = self.context.get('request')
        if request and request.user and request.user.is_authenticated:
            validated_data['created_by'] = request.user
            
        # Map room to resource_id for calendar integration
        room = validated_data.get('room')
        if room and not validated_data.get('resource_id'):
            if room == 'room-a':
                validated_data['resource_id'] = '1'
            elif room == 'room-b':
                validated_data['resource_id'] = '2'
            else:
                validated_data['resource_id'] = room
                
        return super().create(validated_data)


class PatientListSerializer(serializers.ModelSerializer):
    """Serializer for PatientList model."""
    
    patient_count = serializers.SerializerMethodField()
    assigned_staff_names = serializers.SerializerMethodField()
    
    class Meta:
        model = PatientList
        fields = [
            'id', 'name', 'description', 'list_type', 'is_active', 'is_public',
            'patients', 'patient_count', 'assigned_staff', 'assigned_staff_names',
            'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_patient_count(self, obj):
        return obj.patients.count()
    
    def get_assigned_staff_names(self, obj):
        return [staff.get_full_name() for staff in obj.assigned_staff.all()]


class ActiveVisitSerializer(serializers.ModelSerializer):
    """Serializer for ActiveVisit model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    staff_name = serializers.CharField(source='assigned_staff.get_full_name', read_only=True)
    wait_time_minutes = serializers.IntegerField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ActiveVisit
        fields = [
            'id', 'patient', 'patient_name', 'appointment', 'visit_status',
            'current_location', 'room', 'assigned_staff', 'staff_name',
            'check_in_time', 'status_change_time', 'estimated_duration',
            'priority', 'notes', 'wait_time_minutes', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'check_in_time', 'status_change_time', 'created_at', 'updated_at']


class PresenceListSerializer(serializers.ModelSerializer):
    """Serializer for PresenceList model."""
    
    staff_name = serializers.CharField(source='staff_member.get_full_name', read_only=True)
    is_current = serializers.BooleanField(read_only=True)
    duration_minutes = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = PresenceList
        fields = [
            'id', 'staff_member', 'staff_name', 'status', 'location', 'assigned_room',
            'start_time', 'end_time', 'is_available_for_patients', 'notes',
            'is_current', 'duration_minutes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'start_time', 'created_at', 'updated_at']


class HistoryJournalSerializer(serializers.ModelSerializer):
    """Serializer for HistoryJournal model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    staff_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    formatted_event_date = serializers.CharField(read_only=True)
    related_staff_names = serializers.SerializerMethodField()
    
    class Meta:
        model = HistoryJournal
        fields = [
            'id', 'patient', 'patient_name', 'appointment', 'entry_type', 'category',
            'title', 'description', 'summary', 'created_by', 'staff_name',
            'related_staff', 'related_staff_names', 'is_private', 'is_important',
            'related_document', 'related_attachment', 'event_date', 'formatted_event_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_related_staff_names(self, obj):
        return [staff.get_full_name() for staff in obj.related_staff.all()]
