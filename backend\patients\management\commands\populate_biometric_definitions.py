from django.core.management.base import BaseCommand
from patients.models import BiometricMeasureDefinition


class Command(BaseCommand):
    help = 'Populate sample biometric measure definitions for testing'

    def handle(self, *args, **options):
        self.stdout.write('Populating biometric measure definitions...')

        # Define biometric measurements
        definitions_data = [
            # Vital Signs
            ('temperature', 'Body Temperature', '°C', 'float', 'vital_signs', 35.0, 42.0, True, 1, 'Core body temperature'),
            ('heart_rate', 'Heart Rate', 'bpm', 'integer', 'vital_signs', 40, 200, True, 2, 'Resting heart rate'),
            ('blood_pressure_systolic', 'Blood Pressure (Systolic)', 'mmHg', 'integer', 'vital_signs', 70, 250, True, 3, 'Systolic blood pressure'),
            ('blood_pressure_diastolic', 'Blood Pressure (Diastolic)', 'mmHg', 'integer', 'vital_signs', 40, 150, True, 4, 'Diastolic blood pressure'),
            ('respiratory_rate', 'Respiratory Rate', '/min', 'integer', 'vital_signs', 8, 40, True, 5, 'Breaths per minute'),
            ('oxygen_saturation', 'Oxygen Saturation', '%', 'integer', 'vital_signs', 70, 100, True, 6, 'Blood oxygen saturation'),
            
            # Body Measurements
            ('height', 'Height', 'cm', 'float', 'body_measurements', 50.0, 250.0, True, 10, 'Patient height'),
            ('weight', 'Weight', 'kg', 'float', 'body_measurements', 1.0, 300.0, True, 11, 'Patient weight'),
            ('bmi', 'BMI', 'kg/m²', 'calculated', 'body_measurements', 10.0, 50.0, False, 12, 'Body Mass Index'),
            ('waist_circumference', 'Waist Circumference', 'cm', 'float', 'body_measurements', 30.0, 200.0, False, 13, 'Waist measurement'),
            ('hip_circumference', 'Hip Circumference', 'cm', 'float', 'body_measurements', 40.0, 200.0, False, 14, 'Hip measurement'),
            ('head_circumference', 'Head Circumference', 'cm', 'float', 'body_measurements', 30.0, 70.0, False, 15, 'Head circumference (pediatric)'),
            
            # Cardiovascular
            ('pulse_pressure', 'Pulse Pressure', 'mmHg', 'calculated', 'cardiovascular', 20, 100, False, 20, 'Difference between systolic and diastolic'),
            ('mean_arterial_pressure', 'Mean Arterial Pressure', 'mmHg', 'calculated', 'cardiovascular', 60, 120, False, 21, 'Average arterial pressure'),
            
            # Respiratory
            ('peak_flow', 'Peak Expiratory Flow', 'L/min', 'integer', 'respiratory', 100, 800, False, 30, 'Maximum airflow rate'),
            ('fev1', 'FEV1', 'L', 'float', 'respiratory', 0.5, 6.0, False, 31, 'Forced expiratory volume in 1 second'),
            
            # Neurological
            ('glasgow_coma_scale', 'Glasgow Coma Scale', 'points', 'integer', 'neurological', 3, 15, False, 40, 'Consciousness level assessment'),
            ('pain_scale', 'Pain Scale', '/10', 'integer', 'neurological', 0, 10, False, 41, 'Pain intensity rating'),
            
            # Laboratory (common bedside tests)
            ('blood_glucose', 'Blood Glucose', 'mg/dL', 'integer', 'laboratory', 20, 600, False, 50, 'Blood sugar level'),
            ('urine_specific_gravity', 'Urine Specific Gravity', '', 'float', 'laboratory', 1.000, 1.050, False, 51, 'Urine concentration'),
            
            # Other
            ('fluid_intake', 'Fluid Intake', 'mL', 'integer', 'other', 0, 5000, False, 60, 'Daily fluid intake'),
            ('urine_output', 'Urine Output', 'mL', 'integer', 'other', 0, 3000, False, 61, 'Daily urine output'),
            ('sleep_hours', 'Sleep Duration', 'hours', 'float', 'other', 0.0, 24.0, False, 62, 'Hours of sleep'),
        ]

        total_created = 0
        for name, label, unit, measurement_type, category, min_val, max_val, is_required, display_order, description in definitions_data:
            # Type ignore for basedpyright not recognizing objects attribute
            definition, created = BiometricMeasureDefinition.objects.get_or_create(  # type: ignore
                name=name,
                category=category,
                defaults={
                    'label': label,
                    'unit': unit,
                    'measurement_type': measurement_type,
                    'min_value': min_val,
                    'max_value': max_val,
                    'is_required': is_required,
                    'display_order': display_order,
                    'description': description,
                }
            )
            if created:
                total_created += 1
                self.stdout.write(f'Created: {label}')

        # Add calculation formulas for calculated fields
        try:
            # Type ignore for basedpyright not recognizing objects attribute
            bmi_def = BiometricMeasureDefinition.objects.get(name='bmi')  # type: ignore
            bmi_def.calculation_formula = 'weight / (height/100)^2'
            bmi_def.save()
            
            # Type ignore for basedpyright not recognizing objects attribute
            pulse_pressure_def = BiometricMeasureDefinition.objects.get(name='pulse_pressure')  # type: ignore
            pulse_pressure_def.calculation_formula = 'blood_pressure_systolic - blood_pressure_diastolic'
            pulse_pressure_def.save()
            
            # Type ignore for basedpyright not recognizing objects attribute
            map_def = BiometricMeasureDefinition.objects.get(name='mean_arterial_pressure')  # type: ignore
            map_def.calculation_formula = 'blood_pressure_diastolic + (pulse_pressure / 3)'
            map_def.save()
            
            self.stdout.write('Updated calculation formulas for calculated fields')
        except Exception as e:
            self.stdout.write(f'Warning: Could not update calculation formulas: {e}')

        # Type ignore for basedpyright not recognizing SUCCESS attribute
        self.stdout.write(  # type: ignore
            self.style.SUCCESS(  # type: ignore
                f'Successfully populated biometric definitions! Created {total_created} new definitions.'
            )
        )