# Generated manually for data migration

from django.db import migrations


def populate_doctor_or_assistant(apps, schema_editor):
    """Populate doctor_or_assistant field from existing doctor field."""
    Appointment = apps.get_model('appointments', 'Appointment')
    
    # Copy data from doctor field to doctor_or_assistant field
    for appointment in Appointment.objects.all():
        if appointment.doctor and not appointment.doctor_or_assistant:
            appointment.doctor_or_assistant = appointment.doctor
            appointment.save(update_fields=['doctor_or_assistant'])


def reverse_populate_doctor_or_assistant(apps, schema_editor):
    """Reverse the population (clear doctor_or_assistant field)."""
    Appointment = apps.get_model('appointments', 'Appointment')
    
    # Clear doctor_or_assistant field
    Appointment.objects.update(doctor_or_assistant=None)


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0003_add_doctor_or_assistant_field'),
    ]

    operations = [
        migrations.RunPython(
            populate_doctor_or_assistant,
            reverse_populate_doctor_or_assistant
        ),
    ]