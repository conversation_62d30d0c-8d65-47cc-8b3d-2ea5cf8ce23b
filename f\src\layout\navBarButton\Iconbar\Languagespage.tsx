
import React from 'react';
import { switchLocaleAction } from '~/actions/switch-locale';
import { useTranslation } from '~/i18n/client';
import Image from 'next/legacy/image'
import { Tooltip, Group,  Menu, ActionIcon } from "@mantine/core";
import {  IconLanguage } from "@tabler/icons-react";

const Languages = () => {

  const { i18n, t } = useTranslation('menu');
  const handleLocaleChange = (locale: string) => {
    switchLocaleAction(locale);
  };
  return (
    <>
      <Menu
        width={200}
        position="bottom-end"
        transitionProps={{ transition: "pop-top-right" }}
        withinPortal
        shadow="lg"
        zIndex={1000010}
     
      >
        <Menu.Target>
        
            <Group gap={7} pl={"1px"} >
            <Tooltip
            label={t('top-title-Langue')}
            withArrow
            style={{color:"var(--mantine-color-text)"}}
          >
              <ActionIcon
                variant="light"
                 className="h-10 w-10 rounded  navBarButtonicon"
              >
                <IconLanguage radius="xl" size={20} className=" Languages "/>
           
              </ActionIcon>
              </Tooltip>
            </Group>
         
        </Menu.Target>
        <Menu.Dropdown
       
        >
        {i18n.language == 'fr' ? null : (
          <>
          <div>
          <Menu.Item
            leftSection={
              <Image
                className="mr-2 rounded-full"
                src={"/flags/fr.svg"}
                alt="Image description"
                width={16}
                height={16}
              />
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('fr');
            }}
           
          >
          
             {t('menu-Fransh')}
             
          </Menu.Item>
          </div>
          <Menu.Divider />
          </>
        )}
        {i18n.language == 'en' ? null : (
          <>
          <Menu.Item
          
            leftSection={
              <>
                <Image
                   className="h-4 w-4 rounded-full ring-3  ring-white "
                  src={"/flags/us.svg"}
                  alt="Image description"
                  width={16}
                  height={16}
                />
              </>
             
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('en');
            }}
          >
           
             {t('menu-English')}
             
          </Menu.Item>
          <Menu.Divider />
          </>
        )}
        {i18n.language == 'ar' ? null : (
          <>
          <Menu.Item
            leftSection={
              <>
                <Image
                  className="mr-2 rounded-full"
                  src={"/flags/ma.svg"}
                  alt="Image description"
                  width={16}
                  height={16}
                />
              </>
          
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('ar');
            }}
          >
            {t('menu-Arabic')}
          </Menu.Item>
          </>
        )}
        </Menu.Dropdown>
      </Menu>
      {/* <span className="-mx-3.5 text-[var(--bg-base-200)]">|</span> */}
    </>
  );
};

export default Languages;
