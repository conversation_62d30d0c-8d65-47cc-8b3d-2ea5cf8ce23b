# Generated by Django 5.1.3 on 2025-08-17 15:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("appointments", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="appointment",
            name="agenda",
            field=models.CharField(
                blank=True, help_text="Agenda type", max_length=100, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="color",
            field=models.CharField(
                blank=True,
                help_text="Hex color code for calendar display",
                max_length=7,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="consultation_type",
            field=models.CharField(
                blank=True, help_text="Type of consultation", max_length=100, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="doctor_assigned",
            field=models.Char<PERSON><PERSON>(
                blank=True, help_text="Assigned doctor name", max_length=100, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="event_type",
            field=models.CharField(
                blank=True, help_text="Frontend event type", max_length=50, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="is_active",
            field=models.BooleanField(
                default=False,
                help_text="Is this appointment currently active/in progress",
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="is_waiting_list",
            field=models.BooleanField(
                default=False, help_text="Is this appointment in waiting list"
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="patient_address",
            field=models.TextField(blank=True, help_text="Patient address", null=True),
        ),
        migrations.AddField(
            model_name="appointment",
            name="patient_phone",
            field=models.CharField(
                blank=True, help_text="Patient phone number", max_length=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="resource_id",
            field=models.CharField(
                blank=True,
                help_text="Resource/Room ID for calendar",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="appointment",
            name="appointment_type",
            field=models.CharField(
                choices=[
                    ("consultation", "Consultation"),
                    ("follow_up", "Follow-up"),
                    ("emergency", "Emergency"),
                    ("urgence", "Urgence"),
                    ("routine_checkup", "Routine Checkup"),
                    ("controle", "Contrôle"),
                    ("procedure", "Procedure"),
                    ("surgery", "Surgery"),
                    ("chirurgie", "Chirurgie"),
                    ("cleaning", "Cleaning"),
                    ("visite_malade", "Visite de malade"),
                    ("visitor_counter", "Visitor Counter"),
                    ("re_diagnose", "Re-diagnose"),
                    ("other", "Other"),
                ],
                default="consultation",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="appointment",
            name="status",
            field=models.CharField(
                choices=[
                    ("scheduled", "Scheduled"),
                    ("confirmed", "Confirmed"),
                    ("in_progress", "In Progress"),
                    ("completed", "Completed"),
                    ("cancelled", "Cancelled"),
                    ("no_show", "No Show"),
                    ("rescheduled", "Rescheduled"),
                    ("visite_malade", "Visite de malade"),
                    ("visitor_counter", "Visitor Counter"),
                    ("re_diagnose", "Re-diagnose"),
                    ("waiting_list", "Liste d'attente"),
                ],
                default="scheduled",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="DoctorPause",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Title of the pause/break", max_length=200
                    ),
                ),
                (
                    "date_from",
                    models.DateTimeField(help_text="Start date and time of the pause"),
                ),
                (
                    "date_to",
                    models.DateTimeField(help_text="End date and time of the pause"),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about the pause",
                        null=True,
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(
                        default=False, help_text="Is this a recurring pause"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_pauses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        limit_choices_to={"user_type": "doctor"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pauses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Doctor Pause",
                "verbose_name_plural": "Doctor Pauses",
                "ordering": ["date_from"],
                "indexes": [
                    models.Index(
                        fields=["doctor", "date_from"],
                        name="appointment_doctor__6554be_idx",
                    ),
                    models.Index(
                        fields=["date_from", "date_to"],
                        name="appointment_date_fr_3ed9f6_idx",
                    ),
                ],
            },
        ),
    ]
