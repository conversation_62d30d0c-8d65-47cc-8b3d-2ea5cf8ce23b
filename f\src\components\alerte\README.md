# Composants Modaux de Dictionnaire

Ce dossier contient les composants séparés pour gérer les modaux de dictionnaire, extraits du fichier principal `FichePatient.tsx` pour une meilleure organisation et réutilisabilité.

## Structure des Composants

### 1. `AddModelModal.tsx`
Modal pour ajouter un nouveau modèle avec un titre personnalisé.

**Props :**
- `opened`: boolean - État d'ouverture du modal
- `onClose`: () => void - Fonction de fermeture
- `modelTitle`: string - Titre du modèle
- `setModelTitle`: (title: string) => void - Fonction pour modifier le titre
- `onSave`: () => void - Fonction de sauvegarde
- `onCancel`: () => void - Fonction d'annulation

### 2. `SavedModelsModal.tsx`
Modal pour afficher et gérer les modèles sauvegardés.

**Props :**
- `opened`: boolean - État d'ouverture du modal
- `savedModels`: SavedModel[] - Liste des modèles sauvegardés
- `onToggleModel`: (modelId: string) => void - Fonction pour sélectionner/désélectionner un modèle
- `onDeleteModel`: (modelId: string) => void - Fonction pour supprimer un modèle
- `onValidate`: () => void - Fonction de validation
- `onCancel`: () => void - Fonction d'annulation
- `onNewModel`: () => void - Fonction pour créer un nouveau modèle

### 3. `DictionaryTreeModal.tsx`
Modal pour afficher l'arbre du dictionnaire avec sélections multiples.

**Props :**
- `opened`: boolean - État d'ouverture du modal
- `exampleData`: TreeNode[] - Données de l'arbre
- `selectedNodes`: Set<string> - Nœuds sélectionnés
- `collapsedNodes`: Record<string, boolean> - Nœuds effondrés
- `onToggleNodeCollapse`: (nodeId: string) => void - Fonction pour effondrer/déplier
- `onToggleNodeSelection`: (nodeId: string) => void - Fonction pour sélectionner/désélectionner
- `onSelectAll`: () => void - Fonction pour tout sélectionner
- `onDeselectAll`: () => void - Fonction pour tout désélectionner
- `onAddModel`: () => void - Fonction pour ajouter comme modèle
- `onValidate`: () => void - Fonction de validation
- `onCancel`: () => void - Fonction d'annulation

### 4. `DictionaryModalsManager.tsx`
Composant principal qui gère tous les modaux et leurs interactions.

## Utilisation

```tsx
import { DictionaryModalsManager } from './components';

// Dans votre composant principal
<DictionaryModalsManager
  // États des modaux
  isAddModelModalOpen={showAddModel}
  isSavedModelsModalOpen={showModels}
  isDictionaryTreeModalOpen={isChoixMultipleModalOpen}
  
  // Données
  modelTitle={modelTitle}
  savedModels={savedModels}
  exampleData={exampleData}
  selectedNodes={selectedNodes}
  collapsedNodes={collapsedNodes}
  
  // Fonctions de gestion des états
  setModelTitle={setModelTitle}
  setIsAddModelModalOpen={setShowAddModel}
  setIsSavedModelsModalOpen={setShowModels}
  setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}
  
  // Fonctions de gestion des modèles
  onSaveModel={handleSaveModel}
  onToggleModel={handleToggleModel}
  onDeleteModel={handleDeleteModel}
  
  // Fonctions de gestion de l'arbre
  onToggleNodeCollapse={toggleNodeCollapse}
  onToggleNodeSelection={toggleNodeSelection}
  onSelectAll={selectAllNodes}
  onDeselectAll={deselectAllNodes}
  
  // Fonctions d'action
  onValidate={handleValidate}
  onCancel={handleCancel}
  getSelectedValues={getSelectedValues}
  
  // Composants
  TreeItemChoixMultiple={TreeItemChoixMultiple}
/>
```

## Avantages de la Séparation

1. **Réutilisabilité** : Chaque modal peut être utilisé indépendamment
2. **Maintenabilité** : Code plus facile à maintenir et déboguer
3. **Testabilité** : Chaque composant peut être testé séparément
4. **Performance** : Chargement conditionnel des composants
5. **Lisibilité** : Code principal plus propre et organisé

## Types

```tsx
interface SavedModel {
  id: string;
  title: string;
  selections: string[];
  selected?: boolean;
}

interface TreeNode {
  uid: string;
  value: string;
  nodes?: TreeNode[];
}
```
