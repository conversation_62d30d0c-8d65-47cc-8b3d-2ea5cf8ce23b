import React, { useState } from 'react';
// import Dental from "@/components/dental/Dental";
import Icon from '@mdi/react';
import { mdiViewGrid,mdiTooth,mdiCloudUpload,mdiMicrophone,mdiCameraAccount,mdiCamera,mdiFolderPlus,mdiFileTree,mdiLan,mdiDownloadNetwork,mdiFileReplace,mdiLanConnect,mdiDownloadNetworkOutline,mdiFilterCog,mdiSortCalendarAscending,mdiFolderCheck} from '@mdi/js';
import {
  Modal,
  Text,
  Group,
  Tabs,
  Button,
  Table,
  Badge,
  ActionIcon,
  Stack,
  Avatar,
  Paper,
  Grid,
 Tooltip,
 Divider,
} from '@mantine/core';
import {
  IconUser,
  IconPhoto,
  IconCalendar,
  IconChartLine,
  IconClipboard,
  IconFolder,
  IconEdit,
  IconTrash,
  IconDownload,
  IconCamera,
  IconUpload,
  IconPlus
} from '@tabler/icons-react';
import { AppointmentEvent } from '@/types/typesCalendarPatient';
interface PatientDetailsModalProps {
  opened: boolean;
  onClose: () => void;
  selectedEvent: AppointmentEvent | null;
  eventResourceId: number;
}
const PatientDetailsModal: React.FC<PatientDetailsModalProps> = ({
  opened,
  onClose,
  selectedEvent,
  //eventResourceId
}) => {
  const [activeTab, setActiveTab] = useState<string | null>('general');
  if (!opened || !selectedEvent) return null;
  const renderGeneralTab = () => (
    <div className="p-4">
      <div className="flex items-start gap-4 mb-6">
        <Avatar size={80} radius="md" color="blue">
          {selectedEvent.first_name?.[0]}{selectedEvent.last_name?.[0]}
        </Avatar>
        <div className="flex-1">
          <Text size="xl" fw={600} mb="xs">
            Mme {selectedEvent.first_name} {selectedEvent.last_name}
          </Text>
          <Text size="sm" c="dimmed" mb="sm">
            {selectedEvent.gender}, né le {selectedEvent.birth_date}, 29 ans 4 mois 17 jours, N° ---, Dernière visite ---
          </Text>

          <Grid gutter="md">
            <Grid.Col span={6}>
              <Text size="sm"><strong>CNIE:</strong> {selectedEvent.cin}</Text>
              <Text size="sm"><strong>Profession:</strong> Employé(e)</Text>
              <Text size="sm"><strong>Ville:</strong> CASABLANCA</Text>
              <Text size="sm"><strong>N° de dossier papier:</strong> ---</Text>
              <Text size="sm"><strong>Adresse:</strong> {selectedEvent.address}</Text>
              <Text size="sm"><strong>Commentaire:</strong> {selectedEvent.notes}</Text>
            </Grid.Col>
            <Grid.Col span={6}>
              <Text size="sm"><strong>Téléphone:</strong> {selectedEvent.phone_numbers}</Text>
              <Text size="sm"><strong>Médecin traitant:</strong> {selectedEvent.docteur}</Text>
              <Text size="sm"><strong>Assurance:</strong> {selectedEvent.socialSecurity}</Text>
              <Text size="sm"><strong>Contact d&apos;urgence:</strong> {selectedEvent.phone_numbers}</Text>
              <Text size="sm"><strong>État civil:</strong> {selectedEvent.etatCivil}</Text>
              <Text size="sm"><strong>Adressé par:</strong> {selectedEvent.address}</Text>
              <Text size="sm"><strong>Email:</strong>{selectedEvent.email}</Text>
            </Grid.Col>
          </Grid>
        </div>
      </div>

      {/* Colored status bars */}
      <div className="flex gap-2 mb-4">
        <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded text-sm">
          <span className="text-green-600">+</span>
          <span>0,00 DH</span>
        </div>
        <div className="flex items-center gap-2 px-3 py-1 bg-red-100 text-red-800 rounded text-sm">
          <span className="text-red-600">-</span>
          <span>0,00 DH</span>
        </div>
        <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded text-sm">
          <IconUser size={14} />
          <span>0,00 DH</span>
        </div>
        <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 text-gray-800 rounded text-sm">
          <span>0,00 DH</span>
        </div>
      </div>
    </div>
  );

  const renderImagesTab = () => (
    <div className="p-4">
      <div className="text-center py-8">
        <Text size="lg" fw={500} mb="md">Ajouter ...</Text>
        <Text size="sm" c="dimmed" mb="lg">Aucun élément trouvé.</Text>

        <div className="flex justify-center gap-4">
          <Button leftSection={<IconUpload size={16} />} variant="filled" color="blue">
            Importer DICOM
          </Button>
          <Button leftSection={<IconDownload size={16} />} variant="light" color="gray">
            Importer depuis FirePACS
          </Button>
          <Button leftSection={<IconCamera size={16} />} variant="filled" color="blue">
            Prendre des images
          </Button>
          <Button leftSection={<IconPlus size={16} />} variant="filled" color="green">
            Ajouter des images
          </Button>
        </div>
      </div>
    </div>
  );

  const renderCalendarTab = () => (
    <div className="p-4">
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Date du r...</Table.Th>
            <Table.Th>De</Table.Th>
            <Table.Th>à</Table.Th>
            <Table.Th>Docteur</Table.Th>
            <Table.Th>Motif</Table.Th>
            <Table.Th>Agenda</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th></Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <Table.Tr>
            <Table.Td>09/06/2025</Table.Td>
            <Table.Td>18:15</Table.Td>
            <Table.Td>18:30</Table.Td>
            <Table.Td>DEMO DEMO</Table.Td>
            <Table.Td>
              <Badge color="pink" variant="filled" size="sm">Consultation</Badge>
            </Table.Td>
            <Table.Td>
              <Badge color="teal" variant="filled" size="sm">Cabinet</Badge>
            </Table.Td>
            <Table.Td>
              <Badge color="orange" variant="filled" size="sm">Ratée</Badge>
            </Table.Td>
            <Table.Td>
              <Group gap="xs">
                <ActionIcon size="sm" variant="light" color="blue">
                  <IconEdit size={14} />
                </ActionIcon>
                <ActionIcon size="sm" variant="light" color="red">
                  <IconTrash size={14} />
                </ActionIcon>
              </Group>
            </Table.Td>
          </Table.Tr>
          <Table.Tr>
            <Table.Td>---</Table.Td>
            <Table.Td>---</Table.Td>
            <Table.Td>---</Table.Td>
            <Table.Td>DEMO DEMO</Table.Td>
            <Table.Td>
              <Badge color="pink" variant="filled" size="sm">Consultation</Badge>
            </Table.Td>
            <Table.Td>
              <Badge color="teal" variant="filled" size="sm">Cabinet</Badge>
            </Table.Td>
            <Table.Td>Liste d&apos;attente</Table.Td>
            <Table.Td>
              <Group gap="xs">
                <ActionIcon size="sm" variant="light" color="blue">
                  <IconEdit size={14} />
                </ActionIcon>
                <ActionIcon size="sm" variant="light" color="red">
                  <IconTrash size={14} />
                </ActionIcon>
              </Group>
            </Table.Td>
          </Table.Tr>
        </Table.Tbody>
      </Table>

      <div className="flex justify-between items-center mt-4">
        <Text size="sm" c="dimmed">Page 1 - 2 de 2</Text>
        <Group gap="xs">
          <ActionIcon variant="light" size="sm">
            &lt;
          </ActionIcon>
          <ActionIcon variant="light" size="sm">
            &gt;
          </ActionIcon>
        </Group>
      </div>
    </div>
  );

  const renderBiometricsTab = () => (
    <div className="p-4">
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Date</Table.Th>
            <Table.Th style={{ backgroundColor: '#666', color: 'white' }}>Biométrie</Table.Th>
            <Table.Th>Taille...</Table.Th>
            <Table.Th>IMC (...)</Table.Th>
            <Table.Th>Poul...</Table.Th>
            <Table.Th>T° (C°)</Table.Th>
            <Table.Th>T.A S...</Table.Th>
            <Table.Th>T.A D...</Table.Th>
            <Table.Th>SO2 (...)</Table.Th>
            <Table.Th>Commentaire</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <Table.Tr>
            <Table.Td colSpan={10} className="text-center py-8">
              <Text c="dimmed">Aucune biométrie à afficher</Text>
            </Table.Td>
          </Table.Tr>
        </Table.Tbody>
      </Table>
    </div>
  );

  const renderMedicalTab = () => (
    <div className="p-4">
      <Stack gap="md">
        <Paper p="md" withBorder>
          <Group gap="xs" mb="sm">
            <div className="w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
              <Text size="xs" c="white">!</Text>
            </div>
            <Text fw={500} c="orange">Allergies médicamenteuses</Text>
          </Group>
          <Text size="sm" c="dimmed">RAS</Text>
        </Paper>

        <Paper p="md" withBorder>
          <Group gap="xs" mb="sm">
            <div className="w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
              <Text size="xs" c="white">!</Text>
            </div>
            <Text fw={500} c="orange">Dernières prescriptions</Text>
          </Group>
          <Text size="sm" c="dimmed">RAS</Text>
        </Paper>
      </Stack>
    </div>
  );

  const renderFilesTab = () => (
    <div className="p-4">
      <div className="bg-yellow-50 p-8 text-center rounded-lg">
        {/* <Group gap="md" justify="center" mb="md">
          <IconFolder size={24} />
          <Text fw={500}>Pièces jointes</Text>
        </Group> */}

        <div className="flex justify-center gap-2 mb-6">
           <Tabs variant="outline" defaultValue="gallery">
      <Tabs.List justify="flex-end">
        <Tabs.Tab value="Pièces_jointes" leftSection={<Icon path={mdiFolderCheck} size={1}/>} mr="auto">
         Pièces jointes
        </Tabs.Tab>
        <Tooltip label="Dents" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }} withArrow >
        <Tabs.Tab value="Dents" leftSection={<Icon path={mdiTooth} size={1} />} style={{padding: "11px"}}>{/*<Icon path={mdiNotificationClearAll} size={1} />*/}
        <Divider orientation="vertical"size="sm" />
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Ajouter un fichier" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="Upload" leftSection={<Icon path={mdiCloudUpload} size={1} />}style={{padding: "11px"}}>
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Enregistrer un fichier audio" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="Microphone" leftSection={<Icon path={mdiMicrophone} size={1} />}style={{padding: "11px"}}>
        
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Prendre une photo" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
          <Tabs.Tab value="CameraAccount" leftSection={<Icon path={mdiCameraAccount} size={1} />}style={{padding: "11px"}}>
         
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Prendre plusieurs images" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="Camera" leftSection={<Icon path={mdiCamera} size={1} />}style={{padding: "11px"}}>
          
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Nouveau dossier" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="FolderPlus" leftSection={<Icon path={mdiFolderPlus} size={1}/>}style={{padding: "11px"}}>
        
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer depuis materiel interfacé" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
          <Tabs.Tab value="FileTree" leftSection={<Icon path={mdiFileTree} size={1} />}style={{padding: "11px"}}>
          
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer DICOM instance" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="Lan" leftSection={<Icon path={mdiLan} size={1} />}style={{padding: "11px"}}>
        
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer DICOM instance depuis FirePACS" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow disabled>
        <Tabs.Tab value="DownloadNetwork" leftSection={<Icon path={mdiDownloadNetwork} size={1} />}style={{padding: "11px"}}>
        
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer derniére examen interfacé" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
          <Tabs.Tab value="FileReplace" leftSection={<Icon path={mdiFileReplace} size={1} />}style={{padding: "11px"}}>
       
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer derniére DICOM instance" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="LanConnect" leftSection={<Icon path={mdiLanConnect} size={1} />}style={{padding: "11px"}}>

        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Importer derniére SICOM instance depuis FirePACS" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="DownloadNetworkOutline" leftSection={<Icon path={mdiDownloadNetworkOutline} size={1} />}style={{padding: "11px"}}>
        
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Afficher les fichiers qui appartient a l'élement en cours" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
          <Tabs.Tab value="FilterCog" leftSection={<Icon path={mdiFilterCog} size={1} />}style={{padding: "11px"}}>
     
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Filtrer par date" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="SortCalendarAscending" leftSection={<Icon path={mdiSortCalendarAscending} size={1} />}style={{padding: "11px"}}>
          
        </Tabs.Tab>
        </Tooltip>
        <Tooltip label="Changer la disposition" position="bottom" offset={{ mainAxis: 5, crossAxis: 0 }}withArrow>
        <Tabs.Tab value="settings" leftSection={<Icon path={mdiViewGrid} size={1} />}style={{padding: "11px"}}>
        {/* <Icon path={mdiViewList} size={1} /> */}
        </Tabs.Tab>
        </Tooltip>
      </Tabs.List>

      <Tabs.Panel value="gallery">
        Gallery tab content
      </Tabs.Panel>

      <Tabs.Panel value="messages">
        Messages tab content
      </Tabs.Panel>

      <Tabs.Panel value="settings">
        Settings tab content
      </Tabs.Panel>
    </Tabs>
          {/* <Button size="xs" variant="light">Pièces jointes</Button>
          <ActionIcon size="sm" variant="light"><IconDownload size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconUser size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconCamera size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconEdit size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light">T</ActionIcon>
          <ActionIcon size="sm" variant="light"><IconUpload size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconDownload size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconTrash size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light"><IconFolder size={14} /></ActionIcon>
          <ActionIcon size="sm" variant="light">⚙</ActionIcon> */}
        </div>

        <Text c="dimmed" size="lg">Aucun fichier trouvé</Text>
      </div>
    </div>
  );

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size="60%"
      title={
        <Group gap="sm">
          <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
            <IconClipboard size={16} color="white" />
          </div>
          <Text fw={600} c="white">Résumé ( {selectedEvent.first_name}-{selectedEvent.last_name})</Text>
        </Group>
      }
      styles={{
        header: {
          backgroundColor: '#1c7ed6',
          color: 'white'
        },
        title: {
          color: 'white'
        }
      }}
    >
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="general" leftSection={<IconUser size={16} />}>
            Général
          </Tabs.Tab>
          <Tabs.Tab value="dental" leftSection={<Icon path={mdiTooth} size={1} />}>
            Dentaire
          </Tabs.Tab>
          <Tabs.Tab value="images" leftSection={<IconPhoto size={16} />}>
            Images
          </Tabs.Tab>
          <Tabs.Tab value="calendar" leftSection={<IconCalendar size={16} />}>
            Calendrier
          </Tabs.Tab>
          <Tabs.Tab value="biometrics" leftSection={<IconChartLine size={16} />}>
            Biométrie
          </Tabs.Tab>
          <Tabs.Tab value="medical" leftSection={<IconClipboard size={16} />}>
            Médical
          </Tabs.Tab>
          <Tabs.Tab value="files" leftSection={<IconFolder size={16} />}>
            Fichiers
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="general">
          {renderGeneralTab()}
        </Tabs.Panel>

        <Tabs.Panel value="dental">
          <div className="p-4 text-center">
            {/* <Dental/> */}
          </div>
        </Tabs.Panel>

        <Tabs.Panel value="images">
          {renderImagesTab()}
        </Tabs.Panel>

        <Tabs.Panel value="calendar">
          {renderCalendarTab()}
        </Tabs.Panel>

        <Tabs.Panel value="biometrics">
          {renderBiometricsTab()}
        </Tabs.Panel>

        <Tabs.Panel value="medical">
          {renderMedicalTab()}
        </Tabs.Panel>

        <Tabs.Panel value="files">
          {renderFilesTab()}
        </Tabs.Panel>
      </Tabs>
    </Modal>
  );
};

export default PatientDetailsModal;
