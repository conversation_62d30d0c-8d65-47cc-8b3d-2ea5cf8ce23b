import { Button, Select, TextInput, Group, Stack, ActionIcon, Loader, Alert, Text } from '@mantine/core';
import { IconUserCircle, IconPlus } from '@tabler/icons-react';
import { useForm } from '@mantine/form';
import Icon from '@mdi/react';
import { mdiTrashCan } from '@mdi/js';
import { useState, useEffect } from 'react';
import { notifications } from '@mantine/notifications';
import patientService, { PatientRelation as DjangoPatientRelation } from '@/services/patientService';
type RelationType =
  | 'PARENT'
  | 'CHILD'
  | 'SIBLING'
  | 'SPOUSE'
  | 'COUSIN'
  | 'AUNT_UNCLE'
  | 'NIECE_NEPHEW'
  | 'GREAT_PARENT'
  | 'GRAND_CHILD'
  | 'EMPLOYER'
  | 'NURSE';

interface PatientRelation {
  id?: string;
  full_name: string;
  relation?: RelationType;
  patient_id?: string;
  related_patient_id?: string;
  created_at?: string;
}

interface RelationFormValues {
  relations: PatientRelation[];
}

interface RelationFormProps {
  patientId?: string;
  enableDjangoSync?: boolean;
  readOnly?: boolean;
}

const relationTypes = [
  { value: 'PARENT', label: 'Parent' },
  { value: 'CHILD', label: 'Enfant' },
  { value: 'SIBLING', label: 'Frère/Soeur' },
  { value: 'SPOUSE', label: 'Conjoint' },
  { value: 'COUSIN', label: 'Cousin/Cousine' },
  { value: 'AUNT_UNCLE', label: 'Oncle/Tante' },
  { value: 'NIECE_NEPHEW', label: 'Neveu/Nièce' },
  { value: 'GREAT_PARENT', label: 'Grand Parent' },
  { value: 'GRAND_CHILD', label: 'Petit Enfant' },
  { value: 'EMPLOYER', label: 'Employeur' },
  { value: 'NURSE', label: 'Nurse' },
];

export default function RelationForm({
  patientId,
  enableDjangoSync = false,
  readOnly = false
}: RelationFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<RelationFormValues>({
    initialValues: {
      relations: [],
    },
    validate: {
      relations: {
        relation: (value) =>
          !value ? 'Le type de relation est requis' : null,
      },
    },
  });

  // Load patient relations from Django when component mounts
  useEffect(() => {
    if (enableDjangoSync && patientId) {
      loadPatientRelations();
    }
  }, [enableDjangoSync, patientId]);

  const loadPatientRelations = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        console.warn('Django backend is not connected');
        return;
      }

      if (patientId) {
        // Load patient relations from Django
        const relations = await patientService.getPatientRelations(patientId);
        if (relations && relations.length > 0) {
          form.setValues({
            relations: relations.map((relation: DjangoPatientRelation) => ({
              id: relation.id,
              full_name: relation.related_patient_name || 'Unknown Patient',
              relation: relation.relation_type as RelationType,
              patient_id: relation.patient_id,
              related_patient_id: relation.related_patient_id,
              created_at: relation.created_at,
            }))
          });
        }
      }
    } catch (error) {
      console.error('Error loading patient relations:', error);
      setError('Failed to load patient relations');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (index: number) => {
    const relation = form.values.relations[index];

    if (enableDjangoSync && relation.id && patientId) {
      try {
        setLoading(true);

        // Delete from Django
        const result = await patientService.deletePatientRelation(patientId, relation.id);
        if (result) {
          notifications.show({
            title: 'Success',
            message: 'Relation deleted successfully',
            color: 'green',
          });
        }
      } catch (error) {
        console.error('Error deleting relation:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to delete relation',
          color: 'red',
        });
        return; // Don't remove from form if Django delete failed
      } finally {
        setLoading(false);
      }
    }

    form.removeListItem('relations', index);
  };

  const handleAddRelation = () => {
    form.insertListItem('relations', {
      full_name: '',
      relation: undefined,
    });
  };

  const handleSubmit = async (values: RelationFormValues) => {
    if (enableDjangoSync && patientId) {
      try {
        setLoading(true);

        // Save relations to Django
        for (const relation of values.relations) {
          if (!relation.id) {
            // Create new relation
            await patientService.addPatientRelation(patientId, {
              related_patient_name: relation.full_name,
              relation_type: relation.relation || 'PARENT',
              notes: `Added via relation form`
            });
          } else {
            // Update existing relation
            await patientService.updatePatientRelation(patientId, relation.id, {
              related_patient_name: relation.full_name,
              relation_type: relation.relation || 'PARENT',
            });
          }
        }

        notifications.show({
          title: 'Success',
          message: 'Relations saved successfully',
          color: 'green',
        });

        // Reload relations to get updated data
        await loadPatientRelations();
      } catch (error) {
        console.error('Error saving relations:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to save relations',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    } else {
      console.log('Submitting:', values);
    }
  };

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      {error && (
        <Alert color="red" mb="md">
          {error}
        </Alert>
      )}

      <Stack>
        {loading && form.values.relations.length === 0 ? (
          <Group justify="center" p="md">
            <Loader size="sm" />
            <Text size="sm" c="dimmed">Loading patient relations...</Text>
          </Group>
        ) : form.values.relations.length === 0 ? (
          <Text size="sm" c="dimmed" ta="center" p="md">
            {enableDjangoSync ? 'No relations found for this patient' : 'No relations added yet'}
          </Text>
        ) : (
          form.values.relations.map((item, index) => (
            <Group key={index} align="end" wrap="nowrap">
              <TextInput
                label="Patient"
                leftSection={<IconUserCircle />}
                value={item.full_name}
                onChange={(e) => form.setFieldValue(`relations.${index}.full_name`, e.currentTarget.value)}
                readOnly={readOnly || (enableDjangoSync && !!item.id)}
                w={'100%'}
                disabled={loading}
              />

              <Select
                label="Type de relation"
                data={relationTypes}
                required
                placeholder="Sélectionner"
                {...form.getInputProps(`relations.${index}.relation`)}
                w={'100%'}
                disabled={loading || readOnly}
              />

              <ActionIcon
                variant="filled"
                aria-label="Delete"
                color="red"
                mb={'6px'}
                onClick={() => handleDelete(index)}
                disabled={loading || readOnly}
                loading={loading}
              >
                <Icon path={mdiTrashCan} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
              </ActionIcon>
            </Group>
          ))
        )}
      </Stack>

      <Group mt="md" justify="space-between">
        <Button
          variant="light"
          onClick={handleAddRelation}
          disabled={loading || readOnly}
          leftSection={<IconPlus size={16} />}
        >
          Add Relation
        </Button>

        <Group>
          <Button
            variant="outline"
            color="red"
            onClick={() => {form.reset();}}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            disabled={!form.isValid() || loading}
            loading={loading}
          >
            {enableDjangoSync ? 'Save to Django' : 'Enregistrer'}
          </Button>
        </Group>
      </Group>
    </form>
  );
}
