'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  TextInput,
  Badge,
  Pagination,
  Select,
  Button,
  Box,
  Loader,
  Tooltip
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiMagnify,
  mdiClose,
  mdiFileDocument,
  mdiReceiptText
} from '@mdi/js';

// Types et interfaces
interface BillingFlowItem {
  id: string;
  createdAt: Date;
  patient: {
    name: string;
    fullName: string;
  };
  insurance?: string;
  source: {
    type: 'visit' | 'appointment' | 'consultation';
    label: string;
    color: string;
  };
  documentNumber?: string;
  status: {
    type: 'not_billed' | 'partially_billed' | 'fully_billed';
    label: string;
    color: string;
    icon: string;
  };
}

interface BillingFlowQuery {
  searchAll: string;
  startDate: Date | null;
  endDate: Date | null;
  page: number;
  limit: number;
}

interface BillingFlowProps {
  loading?: boolean;
  items?: BillingFlowItem[];
  total?: number;
  onQueryChange?: (query: BillingFlowQuery) => void;
  onInvoice?: (item: BillingFlowItem) => void;
}

export const FluxDeFaturation: React.FC<BillingFlowProps> = ({
  loading = false,
  items = [],
  total = 0,
  onQueryChange,
  onInvoice
}) => {
  // États locaux
  const [query, setQuery] = useState<BillingFlowQuery>({
    searchAll: '',
    startDate: null,
    endDate: null,
    page: 1,
    limit: 20
  });

  const [interval, setInterval] = useState<'today' | 'this_month' | null>(null);

  // Options de pagination
  const limitOptions = [
    { value: '5', label: '5' },
    { value: '15', label: '15' },
    { value: '20', label: '20' },
    { value: '50', label: '50' },
    { value: '100', label: '100' }
  ];

  // Données d'exemple
  const sampleItems: BillingFlowItem[] = [
    {
      id: '1',
      createdAt: new Date('2025-07-02T09:30:00'),
      patient: {
        name: 'KTKTKTKTKT ACHRA',
        fullName: 'KTKTKTKTKT ACHRA'
      },
      insurance: '',
      source: {
        type: 'visit',
        label: 'Visit',
        color: '#d717ec'
      },
      documentNumber: '',
      status: {
        type: 'not_billed',
        label: 'Non Facturé ou Partiellement facturé',
        color: 'red',
        icon: mdiFileDocument
      }
    }
  ];

  const displayItems = items.length > 0 ? items : sampleItems;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<BillingFlowQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleSearchChange = (value: string) => {
    handleQueryChange({ searchAll: value, page: 1 });
  };

  const handleDateChange = (field: 'startDate' | 'endDate', value: Date | string | null) => {
    const dateValue = typeof value === 'string' ? new Date(value) : value;
    handleQueryChange({ [field]: dateValue, page: 1 });
  };

  const handleIntervalChange = (newInterval: 'today' | 'this_month') => {
    const today = new Date();
    let startDate: Date;
    const endDate: Date = today;

    if (newInterval === 'today') {
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    } else { // this_month
      startDate = new Date(today.getFullYear(), today.getMonth(), 1);
    }

    setInterval(newInterval);
    handleQueryChange({ startDate, endDate, page: 1 });
  };

  const handleClearInterval = () => {
    setInterval(null);
    handleQueryChange({ startDate: null, endDate: null, page: 1 });
  };

  const handlePageChange = (page: number) => {
    handleQueryChange({ page });
  };

  const handleLimitChange = (limit: string | null) => {
    if (limit) {
      handleQueryChange({ limit: parseInt(limit), page: 1 });
    }
  };

  const handleInvoice = (item: BillingFlowItem) => {
    console.log('Facturer:', item);
    onInvoice?.(item);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const totalPages = Math.ceil(total / query.limit);
  const startItem = (query.page - 1) * query.limit + 1;
  const endItem = Math.min(query.page * query.limit, total);

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar de recherche */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md" wrap="wrap">
          {/* Recherche globale */}
          <TextInput
            placeholder="Rechercher"
            value={query.searchAll}
            onChange={(event) => handleSearchChange(event.currentTarget.value)}
            leftSection={<Icon path={mdiMagnify} size={0.8} />}
            style={{ flex: 1, minWidth: 200 }}
          />

          {/* Date début */}
          <DateInput
            label="Date début"
            value={query.startDate}
            onChange={(value) => handleDateChange('startDate', value)}
            style={{ width: 150 }}
          />

          {/* Date fin */}
          <DateInput
            label="Date Fin"
            value={query.endDate}
            onChange={(value) => handleDateChange('endDate', value)}
            style={{ width: 150 }}
          />

          {/* Boutons de filtre */}
          <Group gap="xs">
            <Button
              variant={interval === 'today' ? 'filled' : 'outline'}
              size="sm"
              onClick={() => handleIntervalChange('today')}
            >
              Aujourd'hui
            </Button>
            <Button
              variant={interval === 'this_month' ? 'filled' : 'outline'}
              size="sm"
              onClick={() => handleIntervalChange('this_month')}
            >
              Ce mois
            </Button>
            {interval && (
              <ActionIcon
                variant="outline"
                onClick={handleClearInterval}
                title="Effacer"
              >
                <Icon path={mdiClose} size={0.8} />
              </ActionIcon>
            )}
          </Group>
        </Group>
      </Paper>

      {/* Tableau */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Table striped highlightOnHover withTableBorder withColumnBorders>
            <Table.Thead>
              <Table.Tr>
                <Table.Th style={{ minWidth: 150 }}>
                  <Text size="sm" fw={500}>Date de création</Text>
                </Table.Th>
                <Table.Th style={{ minWidth: 200 }}>
                  <Text size="sm" fw={500}>Patient</Text>
                </Table.Th>
                <Table.Th style={{ minWidth: 150 }}>
                  <Text size="sm" fw={500}>Assurance</Text>
                </Table.Th>
                <Table.Th style={{ minWidth: 100, textAlign: 'center' }}>
                  <Text size="sm" fw={500}>Source</Text>
                </Table.Th>
                <Table.Th style={{ minWidth: 120, textAlign: 'center' }}>
                  <Text size="sm" fw={500}>N°. Document</Text>
                </Table.Th>
                <Table.Th style={{ minWidth: 100, textAlign: 'center' }}>
                  <Text size="sm" fw={500}>Status</Text>
                </Table.Th>
                <Table.Th style={{ width: 80 }}>
                  {/* Actions */}
                </Table.Th>
              </Table.Tr>
            </Table.Thead>

            <Table.Tbody>
              {displayItems.map((item) => (
                <Table.Tr key={item.id}>
                  <Table.Td>
                    <Text size="sm">{formatDate(item.createdAt)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm" fw={500}>{item.patient.fullName}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{item.insurance || ''}</Text>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Badge
                      color={item.source.color}
                      variant="filled"
                      size="sm"
                    >
                      {item.source.label}
                    </Badge>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Text size="sm">{item.documentNumber || ''}</Text>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Tooltip label={item.status.label}>
                      <ActionIcon
                        variant="subtle"
                        color={item.status.color}
                        size="sm"
                      >
                        <Icon path={item.status.icon} size={0.8} />
                      </ActionIcon>
                    </Tooltip>
                  </Table.Td>
                  <Table.Td>
                    <Tooltip label="Document de facturation">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => handleInvoice(item)}
                      >
                        <Icon path={mdiReceiptText} size={0.8} />
                      </ActionIcon>
                    </Tooltip>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        )}
      </Box>

      {/* Pagination */}
      <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
        <Group justify="space-between" align="center">
          <Group gap="md">
            <Text size="sm">Page</Text>
            <Pagination
              value={query.page}
              onChange={handlePageChange}
              total={totalPages}
              size="sm"
              disabled={loading}
            />
          </Group>

          <Group gap="md">
            <Text size="sm">Lignes par Page</Text>
            <Select
              value={query.limit.toString()}
              onChange={handleLimitChange}
              data={limitOptions}
              size="sm"
              style={{ width: 80 }}
              disabled={loading}
            />
          </Group>

          <Text size="sm" c="dimmed">
            {total > 0 ? `${startItem} - ${endItem} de ${total}` : '0 - 0 de 0'}
          </Text>
        </Group>
      </Paper>
    </Paper>
  );
};

export default FluxDeFaturation;