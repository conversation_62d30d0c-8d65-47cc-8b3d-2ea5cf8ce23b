'use client';
import React, { useState } from 'react';
import {
  Group,
  Table,
  Text,
  Card,
  Box,
  Checkbox,
  Button,
  Select,
  Pagination,
  ActionIcon,
  Tooltip,
  Indicator,
  Modal,
} from '@mantine/core';
import {
  IconPlus,
  IconPrinter,
  IconEye,
  IconLock,
} from '@tabler/icons-react';

// Import du composant Nouvelle_mutuelle_form
import Nouvelle_mutuelle_form from './Nouvelle_mutuelle_form';

// Interface pour les données de mutuelle
interface MutuelleData {
  id: number;
  date: string;
  organisme: string;
  patient: string;
  nomAssure: string;
  montant: number;
  etat: 'validee' | 'non-validee';
}

const Mutuelles = () => {
  // États pour les filtres et données
  const [searchTerm, ] = useState('');
  const [selectedMutuelles, setSelectedMutuelles] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isNouvelleMutuelleModalOpen, setIsNouvelleMutuelleModalOpen] = useState(false);

  // Données d'exemple pour les mutuelles
  const mutuellesData: MutuelleData[] = [
    {
      id: 1,
      date: 'Recherc...',
      organisme: 'Rechercher',
      patient: 'Rechercher',
      nomAssure: 'Rechercher',
      montant: 0,
      etat: 'non-validee'
    },
    {
      id: 2,
      date: '24/08/2022',
      organisme: 'CNSS',
      patient: 'ZZZ ZDS',
      nomAssure: 'ZZZ ZDS',
      montant: 7000.00,
      etat: 'validee'
    },
    {
      id: 3,
      date: '13/07/2022',
      organisme: 'CNSS',
      patient: 'EL KANBI HAMZA',
      nomAssure: 'EL KANBI HAMZA',
      montant: 6300.00,
      etat: 'validee'
    },
    {
      id: 4,
      date: '20/05/2022',
      organisme: 'CNSS',
      patient: 'ABIDI YASSINE',
      nomAssure: 'ABIDI YASSINE',
      montant: 4900.00,
      etat: 'validee'
    },
    {
      id: 5,
      date: '14/03/2022',
      organisme: 'CNSS',
      patient: 'Mr BELAISSA MOHAMED',
      nomAssure: 'Mr BELAISSA MOHAMED',
      montant: 3000.00,
      etat: 'non-validee'
    },
    {
      id: 6,
      date: '14/03/2022',
      organisme: 'CNSS',
      patient: 'HAMZA EL KANBI',
      nomAssure: 'HAMZA EL KANBI',
      montant: 800.00,
      etat: 'validee'
    },
    {
      id: 7,
      date: '05/10/2021',
      organisme: 'AXA',
      patient: 'RAFIK MOHAMED',
      nomAssure: 'RAFIK MOHAMED',
      montant: 500.00,
      etat: 'validee'
    },
    {
      id: 8,
      date: '16/09/2021',
      organisme: 'BANK AL MAGHREB',
      patient: 'NISSR TEST',
      nomAssure: 'NISSR TEST',
      montant: 13500.00,
      etat: 'validee'
    },
    {
      id: 9,
      date: '13/04/2021',
      organisme: 'CNOPS',
      patient: 'EL KANBI HAMZA',
      nomAssure: 'None None',
      montant: 10800.00,
      etat: 'validee'
    },
    {
      id: 10,
      date: '01/03/2021',
      organisme: 'CMIM',
      patient: 'Mr OUARHOU ANIS',
      nomAssure: 'Mr OUARHOU ANIS',
      montant: 2000.00,
      etat: 'validee'
    },
    {
      id: 11,
      date: '16/01/2021',
      organisme: 'CNOPS',
      patient: 'EL KANBI HAMZA',
      nomAssure: 'EL KANBI HAMZA',
      montant: 12100.00,
      etat: 'non-validee'
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredMutuelles = mutuellesData.filter(mutuelle =>
    mutuelle.organisme.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mutuelle.patient.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mutuelle.nomAssure.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mutuelle.date.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gestion de la sélection
  const handleSelectMutuelle = (id: number) => {
    setSelectedMutuelles(prev =>
      prev.includes(id)
        ? prev.filter(mutuelleId => mutuelleId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedMutuelles.length === filteredMutuelles.length) {
      setSelectedMutuelles([]);
    } else {
      setSelectedMutuelles(filteredMutuelles.map(mutuelle => mutuelle.id));
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredMutuelles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMutuelles = filteredMutuelles.slice(startIndex, endIndex);

  // Fonction pour obtenir la couleur de l'état
  const getEtatColor = (etat: string) => {
    return etat === 'validee' ? 'green' : 'red';
  };

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et bouton Nouvelle Mutuelle */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <Text size="lg" fw={600} className="text-white">
              📋 Mutuelles
            </Text>
          </Group>

          <Button
            size="sm"
            variant="filled"
            color="blue"
            leftSection={<IconPlus size={16} />}
            className="bg-blue-500 hover:bg-blue-600"
            onClick={() => setIsNouvelleMutuelleModalOpen(true)}
          >
            Nouvelle Mutuelle
          </Button>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedMutuelles.length === filteredMutuelles.length && filteredMutuelles.length > 0}
                  indeterminate={selectedMutuelles.length > 0 && selectedMutuelles.length < filteredMutuelles.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Organisme
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Patient
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom de l&apos;assuré
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Montant
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                État
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentMutuelles.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={7} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentMutuelles.map((mutuelle) => (
                <Table.Tr key={mutuelle.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      checked={selectedMutuelles.includes(mutuelle.id)}
                      onChange={() => handleSelectMutuelle(mutuelle.id)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {mutuelle.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {mutuelle.organisme}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {mutuelle.patient}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {mutuelle.nomAssure}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {mutuelle.montant.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Group justify="center" gap="xs">
                      <Indicator
                        color={getEtatColor(mutuelle.etat)}
                        size={12}
                        processing={false}
                      />
                      <Group gap="xs">
                        <Tooltip label="Imprimer">
                          <ActionIcon
                            variant="subtle"
                            color="gray"
                            size="sm"
                          >
                            <IconPrinter size={14} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Verrouiller">
                          <ActionIcon
                            variant="subtle"
                            color="gray"
                            size="sm"
                          >
                            <IconLock size={14} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Voir">
                          <ActionIcon
                            variant="subtle"
                            color="gray"
                            size="sm"
                          >
                            <IconEye size={14} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination et légende */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 10)}
              data={['10', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {filteredMutuelles.length > 0
                ? `0 - 0 de 0`
                : '0 - 0 de 0'
              }
            </Text>
            <Text size="sm" className="text-gray-600">
              K
            </Text>
          </Group>

          <Pagination
            total={totalPages || 1}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>

        {/* Légende */}
        <Group justify="flex-start" gap="md" className="mt-3">
          <Group gap="xs" align="center">
            <Indicator color="red" size={12} />
            <Text size="sm" className="text-gray-600">
              Mutuelle non validée
            </Text>
          </Group>
          <Group gap="xs" align="center">
            <Indicator color="green" size={12} />
            <Text size="sm" className="text-gray-600">
              Mutuelle validée
            </Text>
          </Group>
        </Group>
      </Card>

      {/* Modale pour la création de nouvelle mutuelle */}
      <Modal
        opened={isNouvelleMutuelleModalOpen}
        onClose={() => setIsNouvelleMutuelleModalOpen(false)}
        title="Nouvelle Mutuelle"
        size="95%"
        centered
        className="modal-nouvelle-mutuelle"
      >
        <Nouvelle_mutuelle_form />
      </Modal>
    </Box>
  );
};

export default Mutuelles;
