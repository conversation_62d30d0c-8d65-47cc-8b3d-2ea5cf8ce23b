# Generated manually for doctor_or_assistant field

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0002_appointment_agenda_appointment_color_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='doctor_or_assistant',
            field=models.ForeignKey(
                blank=True,
                help_text='Doctor or assistant responsible for this appointment',
                limit_choices_to={'user_type__in': ['doctor', 'assistant']},
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='staff_appointments',
                to=settings.AUTH_USER_MODEL
            ),
        ),
    ]