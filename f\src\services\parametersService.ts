/**
 * Parameters Service
 * Handles all system parameters and configuration data operations including:
 * - Basic system parameters (general, visits, flows, prescriptions, pharmacy, billing)
 * - User and actor management (specialties, profiles, users, contacts, technicians)
 * - Application configuration (forms, procedures, medications, reports)
 * - Data maintenance and backup settings
 * - Specialty module configuration
 * - Cloud platform settings
 */

// Types for Parameters data
export interface SystemParameter {
  id: string;
  category: string;
  subcategory: string;
  parameter_name: string;
  parameter_key: string;
  parameter_value: string | number | boolean;
  parameter_type: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description: string;
  is_required: boolean;
  is_editable: boolean;
  default_value: string | number | boolean;
  validation_rules?: string;
  created_at: string;
  updated_at: string;
  updated_by: string;
}

export interface UserProfile {
  id: string;
  profile_name: string;
  description: string;
  permissions: Permission[];
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Permission {
  id: string;
  module: string;
  action: string;
  resource: string;
  allowed: boolean;
}

export interface SystemUser {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile_id: string;
  profile_name: string;
  specialty_id?: string;
  specialty_name?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface Specialty {
  id: string;
  name: string;
  code: string;
  description: string;
  color: string;
  icon?: string;
  is_active: boolean;
  configuration: SpecialtyConfiguration;
  created_at: string;
  updated_at: string;
}

export interface SpecialtyConfiguration {
  forms_enabled: string[];
  procedures_enabled: string[];
  templates_enabled: string[];
  modules_enabled: string[];
  custom_fields: CustomField[];
}

export interface CustomField {
  id: string;
  field_name: string;
  field_type: string;
  field_label: string;
  is_required: boolean;
  options?: string[];
}

export interface Contact {
  id: string;
  type: 'supplier' | 'partner' | 'laboratory' | 'insurance' | 'other';
  name: string;
  company?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Technician {
  id: string;
  first_name: string;
  last_name: string;
  specialty: string;
  email?: string;
  phone?: string;
  certification_number?: string;
  certification_expiry?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ApplicationConfiguration {
  id: string;
  module: string;
  configuration_name: string;
  configuration_data: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DataBackup {
  id: string;
  backup_name: string;
  backup_type: 'full' | 'incremental' | 'differential';
  backup_date: string;
  file_size: number;
  file_path: string;
  status: 'completed' | 'in_progress' | 'failed';
  created_by: string;
  notes?: string;
}

export interface SystemAnalytics {
  id: number;
  report_date: string;
  total_users: number;
  active_users: number;
  total_specialties: number;
  total_parameters: number;
  system_uptime: number;
  last_backup_date: string;
  configuration_changes: ConfigurationChange[];
  user_activity: UserActivity[];
  system_performance: SystemPerformance;
}

export interface ConfigurationChange {
  parameter_name: string;
  old_value: string;
  new_value: string;
  changed_by: string;
  changed_at: string;
}

export interface UserActivity {
  user_id: string;
  user_name: string;
  login_count: number;
  last_activity: string;
  actions_performed: number;
}

export interface SystemPerformance {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  response_time: number;
  error_rate: number;
}

export interface ParametersSummary {
  reportDate: string;
  systemParameters: SystemParameter[];
  userProfiles: UserProfile[];
  systemUsers: SystemUser[];
  specialties: Specialty[];
  contacts: Contact[];
  technicians: Technician[];
  applicationConfigurations: ApplicationConfiguration[];
  dataBackups: DataBackup[];
  analytics: SystemAnalytics;
  lastUpdate: string;
}

class ParametersService {
  private baseURL = '/api/parameters';

  // System Parameters operations
  async getSystemParameters(category?: string, subcategory?: string): Promise<SystemParameter[]> {
    try {
      let url = `${this.baseURL}/system`;
      const params = new URLSearchParams();
      
      if (category) params.append('category', category);
      if (subcategory) params.append('subcategory', subcategory);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch system parameters: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching system parameters:', error);
      return this.getMockSystemParameters();
    }
  }

  async updateSystemParameter(parameterId: string, value: string | number | boolean): Promise<SystemParameter> {
    try {
      const response = await fetch(`${this.baseURL}/system/${parameterId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ parameter_value: value }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update system parameter: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error updating system parameter:', error);
      throw error;
    }
  }

  // User Profiles operations
  async getUserProfiles(): Promise<UserProfile[]> {
    try {
      const response = await fetch(`${this.baseURL}/profiles`);
      if (!response.ok) {
        throw new Error(`Failed to fetch user profiles: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching user profiles:', error);
      return this.getMockUserProfiles();
    }
  }

  // System Users operations
  async getSystemUsers(profileId?: string, specialtyId?: string): Promise<SystemUser[]> {
    try {
      let url = `${this.baseURL}/users`;
      const params = new URLSearchParams();
      
      if (profileId) params.append('profile_id', profileId);
      if (specialtyId) params.append('specialty_id', specialtyId);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch system users: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching system users:', error);
      return this.getMockSystemUsers();
    }
  }

  // Specialties operations
  async getSpecialties(): Promise<Specialty[]> {
    try {
      const response = await fetch(`${this.baseURL}/specialties`);
      if (!response.ok) {
        throw new Error(`Failed to fetch specialties: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching specialties:', error);
      return this.getMockSpecialties();
    }
  }

  // Contacts operations
  async getContacts(type?: string): Promise<Contact[]> {
    try {
      let url = `${this.baseURL}/contacts`;
      if (type) {
        url += `?type=${type}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch contacts: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return this.getMockContacts();
    }
  }

  // Technicians operations
  async getTechnicians(specialty?: string): Promise<Technician[]> {
    try {
      let url = `${this.baseURL}/technicians`;
      if (specialty) {
        url += `?specialty=${specialty}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch technicians: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching technicians:', error);
      return this.getMockTechnicians();
    }
  }

  // Application Configurations operations
  async getApplicationConfigurations(module?: string): Promise<ApplicationConfiguration[]> {
    try {
      let url = `${this.baseURL}/configurations`;
      if (module) {
        url += `?module=${module}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch application configurations: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching application configurations:', error);
      return this.getMockApplicationConfigurations();
    }
  }

  // Data Backups operations
  async getDataBackups(): Promise<DataBackup[]> {
    try {
      const response = await fetch(`${this.baseURL}/backups`);
      if (!response.ok) {
        throw new Error(`Failed to fetch data backups: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching data backups:', error);
      return this.getMockDataBackups();
    }
  }

  // Analytics operations
  async getSystemAnalytics(): Promise<SystemAnalytics> {
    try {
      const response = await fetch(`${this.baseURL}/analytics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch system analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching system analytics:', error);
      return this.getMockSystemAnalytics();
    }
  }

  // Get comprehensive parameters summary
  async getParametersSummary(): Promise<ParametersSummary> {
    try {
      const [systemParameters, userProfiles, systemUsers, specialties, contacts, technicians, applicationConfigurations, dataBackups, analytics] = await Promise.all([
        this.getSystemParameters(),
        this.getUserProfiles(),
        this.getSystemUsers(),
        this.getSpecialties(),
        this.getContacts(),
        this.getTechnicians(),
        this.getApplicationConfigurations(),
        this.getDataBackups(),
        this.getSystemAnalytics(),
      ]);

      return {
        reportDate: new Date().toISOString(),
        systemParameters,
        userProfiles,
        systemUsers,
        specialties,
        contacts,
        technicians,
        applicationConfigurations,
        dataBackups,
        analytics,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching parameters summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockSystemParameters(): SystemParameter[] {
    return [
      {
        id: '1',
        category: 'general',
        subcategory: 'application',
        parameter_name: 'Application Name',
        parameter_key: 'app_name',
        parameter_value: 'Medical Practice Management',
        parameter_type: 'string',
        description: 'Name of the application',
        is_required: true,
        is_editable: true,
        default_value: 'Medical Practice Management',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
        updated_by: 'admin',
      },
    ];
  }

  private getMockUserProfiles(): UserProfile[] {
    return [
      {
        id: '1',
        profile_name: 'Administrator',
        description: 'Full system access',
        permissions: [
          { id: '1', module: 'all', action: 'all', resource: 'all', allowed: true },
        ],
        is_active: true,
        is_default: false,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockSystemUsers(): SystemUser[] {
    return [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'System',
        last_name: 'Administrator',
        profile_id: '1',
        profile_name: 'Administrator',
        is_active: true,
        last_login: '2024-01-20T10:00:00Z',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockSpecialties(): Specialty[] {
    return [
      {
        id: '1',
        name: 'Médecine Générale',
        code: 'MG',
        description: 'Médecine générale et soins primaires',
        color: '#3B82F6',
        is_active: true,
        configuration: {
          forms_enabled: ['consultation', 'prescription'],
          procedures_enabled: ['examination', 'diagnosis'],
          templates_enabled: ['standard_consultation'],
          modules_enabled: ['calendar', 'patients', 'prescriptions'],
          custom_fields: [],
        },
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockContacts(): Contact[] {
    return [
      {
        id: '1',
        type: 'supplier',
        name: 'Fournisseur Médical',
        company: 'Medical Supply Co',
        email: '<EMAIL>',
        phone: '+33123456789',
        address: '123 Rue de la Santé',
        city: 'Paris',
        country: 'France',
        is_active: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockTechnicians(): Technician[] {
    return [
      {
        id: '1',
        first_name: 'Jean',
        last_name: 'Technicien',
        specialty: 'Radiologie',
        email: '<EMAIL>',
        phone: '+33123456789',
        certification_number: 'CERT-001',
        certification_expiry: '2025-12-31',
        is_active: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockApplicationConfigurations(): ApplicationConfiguration[] {
    return [
      {
        id: '1',
        module: 'calendar',
        configuration_name: 'Default Calendar Settings',
        configuration_data: {
          default_view: 'week',
          working_hours: { start: '08:00', end: '18:00' },
          appointment_duration: 30,
        },
        is_active: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockDataBackups(): DataBackup[] {
    return [
      {
        id: '1',
        backup_name: 'Daily Backup 2024-01-20',
        backup_type: 'full',
        backup_date: '2024-01-20T02:00:00Z',
        file_size: 1024000000, // 1GB
        file_path: '/backups/daily_backup_20240120.sql',
        status: 'completed',
        created_by: 'system',
        notes: 'Automated daily backup',
      },
    ];
  }

  private getMockSystemAnalytics(): SystemAnalytics {
    return {
      id: 1,
      report_date: '2024-01-20',
      total_users: 25,
      active_users: 20,
      total_specialties: 8,
      total_parameters: 150,
      system_uptime: 99.9,
      last_backup_date: '2024-01-20T02:00:00Z',
      configuration_changes: [
        {
          parameter_name: 'app_name',
          old_value: 'Old App Name',
          new_value: 'Medical Practice Management',
          changed_by: 'admin',
          changed_at: '2024-01-20T10:00:00Z',
        },
      ],
      user_activity: [
        {
          user_id: '1',
          user_name: 'admin',
          login_count: 15,
          last_activity: '2024-01-20T10:00:00Z',
          actions_performed: 150,
        },
      ],
      system_performance: {
        cpu_usage: 45.2,
        memory_usage: 68.5,
        disk_usage: 72.1,
        response_time: 250,
        error_rate: 0.1,
      },
    };
  }
}

export const parametersService = new ParametersService();
export default parametersService;
