"""
Enhanced appointment management views for frontend integration.
"""

from rest_framework import generics, status, permissions, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction, models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta
import logging

from appointments.models import Appointment, DoctorPause
from appointments.serializers import (
    AppointmentSerializer, 
    AppointmentCreateSerializer,
    AppointmentListSerializer,
    DoctorPauseSerializer
)
from users.models import Patient

User = get_user_model()
logger = logging.getLogger(__name__)


class AppointmentManagementViewSet(viewsets.ModelViewSet):
    """
    Enhanced appointment management with calendar integration.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.AllowAny]  # Adjust as needed
    
    def get_queryset(self):
        queryset = Appointment.objects.select_related('patient__user', 'doctor').all()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                appointment_date__gte=start_date,
                appointment_date__lte=end_date
            )
        
        # Filter by doctor
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by waiting list
        is_waiting_list = self.request.query_params.get('is_waiting_list')
        if is_waiting_list is not None:
            queryset = queryset.filter(is_waiting_list=is_waiting_list.lower() == 'true')
        
        return queryset.order_by('appointment_date', 'appointment_time')
    
    def get_serializer_class(self):
        if self.action == 'create':
            return AppointmentCreateSerializer
        elif self.action == 'list':
            return AppointmentListSerializer
        return AppointmentSerializer
    
    def create(self, request, *args, **kwargs):
        """Create appointment with enhanced validation."""
        try:
            with transaction.atomic():
                data = request.data.copy()

                # Handle patient creation/lookup
                patient_id = data.get('patient')
                if not patient_id:
                    # If no patient ID provided, we need patient info to create one
                    patient_data = {
                        'first_name': data.get('patient_first_name', ''),
                        'last_name': data.get('patient_last_name', ''),
                        'email': data.get('patient_email', ''),
                        'phone_number': data.get('patient_phone', ''),
                        'address': data.get('patient_address', ''),
                    }

                    if not patient_data['first_name'] or not patient_data['last_name']:
                        return Response(
                            {'error': 'Patient first_name and last_name are required'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # FIXED: Try to find existing patient by email or create new one
                    print(f"🔍 APPOINTMENT_MANAGEMENT_VIEWS: patient_data['email'] = {repr(patient_data['email'])}")

                    # Check if we have a real email (not None, not empty string, not just whitespace)
                    has_real_email = (
                        patient_data['email'] is not None and
                        isinstance(patient_data['email'], str) and
                        patient_data['email'].strip() != ''
                    )

                    print(f"🔍 APPOINTMENT_MANAGEMENT_VIEWS: has_real_email = {has_real_email}")

                    if has_real_email:
                        real_email = patient_data['email'].strip()
                        print(f"📧 APPOINTMENT_MANAGEMENT_VIEWS: USING REAL EMAIL: '{real_email}'")

                        try:
                            patient_user = User.objects.get(email=real_email, user_type='patient')
                            print(f"✅ APPOINTMENT_MANAGEMENT_VIEWS: Found existing patient: {patient_user.email}")
                        except User.DoesNotExist:
                            # Create new patient user with REAL email
                            patient_user = User.objects.create_user(
                                email=real_email,  # Use REAL email exactly as provided
                                first_name=patient_data['first_name'],
                                last_name=patient_data['last_name'],
                                phone_number=patient_data['phone_number'],
                                address=patient_data['address'],
                                user_type='patient',
                                password='temp123'
                            )
                            print(f"✅ APPOINTMENT_MANAGEMENT_VIEWS: Created patient with REAL email: {patient_user.email}")
                    else:
                        # Create new patient user with temporary email only when no real email provided
                        temp_email = f"{patient_data['first_name'].lower()}.{patient_data['last_name'].lower()}@temp.com"
                        print(f"⚠️ APPOINTMENT_MANAGEMENT_VIEWS: Creating patient with temp email: {temp_email}")

                        patient_user = User.objects.create_user(
                            email=temp_email,
                            first_name=patient_data['first_name'],
                            last_name=patient_data['last_name'],
                            phone_number=patient_data['phone_number'],
                            address=patient_data['address'],
                            user_type='patient',
                            password='temp123'
                        )

                    data['patient'] = patient_user.id

                serializer = self.get_serializer(data=data)
                serializer.is_valid(raise_exception=True)

                # Set creator if authenticated
                if request.user and request.user.is_authenticated:
                    serializer.validated_data['created_by'] = request.user

                appointment = serializer.save()

                # Return full appointment data
                response_serializer = AppointmentSerializer(appointment)
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating appointment: {str(e)}")
            return Response(
                {'error': f'Failed to create appointment: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def reschedule(self, request, pk=None):
        """Reschedule an appointment."""
        try:
            appointment = self.get_object()
            new_date = request.data.get('appointment_date')
            new_time = request.data.get('appointment_time')
            
            if not new_date or not new_time:
                return Response(
                    {'error': 'Both appointment_date and appointment_time are required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            with transaction.atomic():
                # Update appointment
                appointment.appointment_date = new_date
                appointment.appointment_time = new_time
                appointment.status = 'rescheduled'
                appointment.save()
                
                # Add note about rescheduling
                if appointment.notes:
                    appointment.notes += f"\n\nRescheduled on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
                else:
                    appointment.notes = f"Rescheduled on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
                appointment.save()
            
            serializer = self.get_serializer(appointment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error rescheduling appointment {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to reschedule appointment: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel an appointment."""
        try:
            appointment = self.get_object()
            reason = request.data.get('reason', 'No reason provided')
            
            with transaction.atomic():
                appointment.status = 'cancelled'
                if appointment.notes:
                    appointment.notes += f"\n\nCancelled on {timezone.now().strftime('%Y-%m-%d %H:%M')}: {reason}"
                else:
                    appointment.notes = f"Cancelled on {timezone.now().strftime('%Y-%m-%d %H:%M')}: {reason}"
                appointment.save()
            
            serializer = self.get_serializer(appointment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error cancelling appointment {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to cancel appointment: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark appointment as completed."""
        try:
            appointment = self.get_object()
            notes = request.data.get('notes', '')
            
            with transaction.atomic():
                appointment.status = 'completed'
                if notes:
                    if appointment.notes:
                        appointment.notes += f"\n\nCompleted notes: {notes}"
                    else:
                        appointment.notes = f"Completed notes: {notes}"
                appointment.save()
            
            serializer = self.get_serializer(appointment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error completing appointment {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to complete appointment: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def move_to_waiting_list(self, request, pk=None):
        """Move appointment to waiting list."""
        try:
            appointment = self.get_object()
            
            with transaction.atomic():
                appointment.is_waiting_list = True
                appointment.status = 'waiting_list'
                appointment.save()
            
            serializer = self.get_serializer(appointment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error moving appointment {pk} to waiting list: {str(e)}")
            return Response(
                {'error': f'Failed to move to waiting list: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def move_from_waiting_list(self, request, pk=None):
        """Move appointment from waiting list to calendar."""
        try:
            appointment = self.get_object()
            new_date = request.data.get('appointment_date')
            new_time = request.data.get('appointment_time')
            
            if not new_date or not new_time:
                return Response(
                    {'error': 'Both appointment_date and appointment_time are required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            with transaction.atomic():
                appointment.is_waiting_list = False
                appointment.status = 'scheduled'
                appointment.appointment_date = new_date
                appointment.appointment_time = new_time
                appointment.save()
            
            serializer = self.get_serializer(appointment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error moving appointment {pk} from waiting list: {str(e)}")
            return Response(
                {'error': f'Failed to move from waiting list: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def calendar_events(self, request):
        """Get appointments formatted for calendar display."""
        try:
            queryset = self.get_queryset()
            appointments = queryset.filter(is_waiting_list=False)
            
            events = []
            for appointment in appointments:
                # Combine date and time for start
                start_datetime = datetime.combine(
                    appointment.appointment_date, 
                    appointment.appointment_time
                )
                
                # Calculate end time
                end_datetime = start_datetime + timedelta(minutes=appointment.duration_minutes)
                
                events.append({
                    'id': str(appointment.id),
                    'title': f"{appointment.patient.first_name} {appointment.patient.last_name}",
                    'start': start_datetime.isoformat(),
                    'end': end_datetime.isoformat(),
                    'description': appointment.description or '',
                    'color': appointment.color or '#3b82f6',
                    'status': appointment.status,
                    'type': appointment.appointment_type,
                    'room': appointment.room,
                    'resourceId': appointment.resource_id,
                    'patient_phone': appointment.patient_phone or appointment.patient.phone_number,
                    'doctor': appointment.doctor.get_full_name() if appointment.doctor else '',
                })
            
            return Response({'events': events})
            
        except Exception as e:
            logger.error(f"Error getting calendar events: {str(e)}")
            return Response(
                {'error': 'Failed to get calendar events'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def waiting_list(self, request):
        """Get appointments in waiting list."""
        try:
            queryset = self.get_queryset()
            waiting_appointments = queryset.filter(is_waiting_list=True)
            
            serializer = AppointmentListSerializer(waiting_appointments, many=True)
            return Response({'waiting_list': serializer.data})
            
        except Exception as e:
            logger.error(f"Error getting waiting list: {str(e)}")
            return Response(
                {'error': 'Failed to get waiting list'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
