import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import Dictionnaire from './Dictionnaire'
import Liste_des_choix from "./Liste_des_choix"
import Catalogues_de_procedures from "./Catalogues_de_procedures"
import Procedures from "./Procedures"
import Organismes from "./Organismes"
import Conventions from './Conventions'
import BlocDeSaisie from './BlocDeSaisie'
import BlockDuScore from './BlockDuScore'
import Consultation from './Consultation'
import ModuleObservation from './ModuleObservation'
import Biometrie from './Biometrie'
import ComptesRendus from './ComptesRendus'
import Biologie from './Biologie'
import Diagnostic_Pathologies from './Diagnostic_Pathologies'
import Medicaments_Para from './Medicaments_Para'
import FamillesDesCertificats from './FamillesDesCertificats'
import EmailSMSConsentement from './E-mail_SMS_Consentement'
import Formulaires from './Formulaires'
import GestionDesModelsDediteurTexte from './GestionDesModelsDediteurTexte'
// Mapping des sous-onglets pour Configuration de l'application
const subtabMapping: { [key: string]: string } = {
  'Dictionnaire': 'Dictionnaire',
  'Liste_des_choix': 'Liste_des_choix',
  'Catalogues_de_procedures': 'Catalogues_de_procedures',
  'Procedures': 'Procedures',
  'Organismes': 'Organismes',
  'Conventions':'Conventions',
  'BlocDeSaisie':'BlocDeSaisie',
  'BlockDuScore':'BlockDuScore',
  'Consultation' :'Consultation',
  'ModuleObservation':'ModuleObservation',
  'Biometrie':'Biometrie',
  'ComptesRendus':'ComptesRendus',
  'Biologie':'Biologie',
  'Diagnostic_Pathologies':'Diagnostic_Pathologies',
  'Medicaments_Para':'Medicaments_Para',
  'FamillesDesCertificats':'FamillesDesCertificats',
  'EmailSMSConsentement':'EmailSMSConsentement',
  'Formulaires':'Formulaires',
  'GestionDesModelsDediteurTexte':'GestionDesModelsDediteurTexte',
};

const Configration_de_lapplication = () => {
  const [activeTab, setActiveTab] = useState('Dictionnaire');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'Dictionnaire')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="Dictionnaire" leftSection={<IconPhoto size={12} />}>
             Dictionnaire
           </Tabs.Tab>
           <Tabs.Tab value="Liste_des_choix" leftSection={<IconMessageCircle size={12} />}>
           Liste des choix
           </Tabs.Tab>
           <Tabs.Tab value="Catalogues_de_procedures" leftSection={<IconSettings size={12} />}>
           Catalogues de procédures
           </Tabs.Tab>
           <Tabs.Tab value="Procedures" leftSection={<IconSettings size={12} />}>
           Procédures
           </Tabs.Tab>
           <Tabs.Tab value="Organismes" leftSection={<IconSettings size={12} />}>
           Organismes
           </Tabs.Tab>
             <Tabs.Tab value="Conventions" leftSection={<IconSettings size={12} />}>
           Conventions
           </Tabs.Tab>
            <Tabs.Tab value="BlocDeSaisie" leftSection={<IconSettings size={12} />}>
           BlocDeSaisie
           </Tabs.Tab>
              <Tabs.Tab value="BlockDuScore" leftSection={<IconSettings size={12} />}>
           BlockDuScore
           </Tabs.Tab>
             <Tabs.Tab value="Consultation" leftSection={<IconSettings size={12} />}>
           Consultation
           </Tabs.Tab>
 <Tabs.Tab value="ModuleObservation" leftSection={<IconSettings size={12} />}>
           ModuleObservation
           </Tabs.Tab>
<Tabs.Tab value="Biometrie" leftSection={<IconSettings size={12} />}>
           Biometrie
           </Tabs.Tab>
<Tabs.Tab value="ComptesRendus" leftSection={<IconSettings size={12} />}>
           ComptesRendus
           </Tabs.Tab>

           <Tabs.Tab value="Biologie" leftSection={<IconSettings size={12} />}>
           Biologie
           </Tabs.Tab>
 <Tabs.Tab value="Diagnostic_Pathologies" leftSection={<IconSettings size={12} />}>
           Diagnostic_Pathologies
           </Tabs.Tab>
            <Tabs.Tab value="Médicaments_Para" leftSection={<IconSettings size={12} />}>
           Médicaments_Para
           </Tabs.Tab>
 <Tabs.Tab value="FamillesDesCertificats" leftSection={<IconSettings size={12} />}>
           FamillesDesCertificats
           </Tabs.Tab>

           <Tabs.Tab value="EmailSMSConsentement" leftSection={<IconSettings size={12} />}>
           EmailSMSConsentement
           </Tabs.Tab>
  <Tabs.Tab value="Formulaires" leftSection={<IconSettings size={12} />}>
           Formulaires
           </Tabs.Tab>
  <Tabs.Tab value="GestionDesModelsDediteurTexte" leftSection={<IconSettings size={12} />}>
           GestionDesModelsDediteurTexte
           </Tabs.Tab>

         </Tabs.List>
         <Tabs.Panel value="Dictionnaire" ml={20}>
           <Dictionnaire/>
         </Tabs.Panel>

         <Tabs.Panel value="Liste_des_choix" ml={20}>
           <Liste_des_choix/>
         </Tabs.Panel>

         <Tabs.Panel value="Catalogues_de_procedures" ml={20}>
           <Catalogues_de_procedures/>
         </Tabs.Panel>
                  <Tabs.Panel value="Procedures" ml={20}>
           <Procedures/>
            </Tabs.Panel>
            

         <Tabs.Panel value="Organismes" ml={20}>
           <Organismes/>
         </Tabs.Panel>
         <Tabs.Panel value="Conventions" ml={20}>
           <Conventions/>
         </Tabs.Panel>
          <Tabs.Panel value="BlocDeSaisie" ml={20}>
           <BlocDeSaisie/>
         </Tabs.Panel>
         <Tabs.Panel value="BlockDuScore" ml={20}>
           <BlockDuScore/>
         </Tabs.Panel>
         <Tabs.Panel value="Consultation" ml={20}>
           <Consultation/>
         </Tabs.Panel>
           <Tabs.Panel value="ModuleObservation" ml={20}>
           <ModuleObservation/>
         </Tabs.Panel>
            <Tabs.Panel value="Biometrie" ml={20}>
           <Biometrie/>
         </Tabs.Panel>
         
          <Tabs.Panel value="ComptesRendus" ml={20}>
           <ComptesRendus/>
         </Tabs.Panel>

 <Tabs.Panel value="Biologie" ml={20}>
           <Biologie/>
         </Tabs.Panel>
         
 <Tabs.Panel value="Diagnostic_Pathologies" ml={20}>
           <Diagnostic_Pathologies/>
         </Tabs.Panel>
          <Tabs.Panel value="Médicaments_Para" ml={20}>
           <Medicaments_Para/>
         </Tabs.Panel>

          <Tabs.Panel value="FamillesDesCertificats" ml={20}>
           <FamillesDesCertificats/>
         </Tabs.Panel>
<Tabs.Panel value="EmailSMSConsentement" ml={20}>
           <EmailSMSConsentement/>
         </Tabs.Panel>
         <Tabs.Panel value="Formulaires" ml={20}>
           <Formulaires/>
         </Tabs.Panel>
          <Tabs.Panel value="GestionDesModelsDediteurTexte" ml={20}>
           <GestionDesModelsDediteurTexte/>
         </Tabs.Panel>
       </Tabs>
  )
};


export default Configration_de_lapplication
