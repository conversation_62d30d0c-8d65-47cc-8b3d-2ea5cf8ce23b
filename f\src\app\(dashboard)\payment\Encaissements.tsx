'use client';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  Group,
  Button,
  Select,
  TextInput,
  Table,
  Text,
  Indicator,
  ActionIcon,
  Loader,
  <PERSON><PERSON>,
  Badge,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconChevronDown,
  IconChevronUp,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconAlertCircle,
  IconRefresh,
} from '@tabler/icons-react';
import Nouvel_encaissement_modal from './Nouvel_encaissement_modal';
import Icon from '@mdi/react';
import { mdiAccountCash, mdiReload } from '@mdi/js';
import { notifications } from '@mantine/notifications';
import { usePayment } from '@/hooks/usePayment';
import patientService from '@/services/patientService';
interface EncaissementData {
  id: string;
  date: string;
  patient: string;
  payeur: string;
  mode: string;
  montantEncaisse: number;
  montantConsomme: number;
  reliquat: number;
  // Additional backend fields
  patient_id?: string;
  payment_id?: string;
  invoice_id?: string;
  payment_method?: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'insurance' | 'other';
  status?: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  transaction_id?: string;
  reference_number?: string;
  notes?: string;
  doctor_name?: string;
  created_at?: string;
  updated_at?: string;
}

// Interface for payment statistics
interface PaymentStats {
  totalCollections: number;
  totalAmount: number;
  totalConsumed: number;
  totalRemaining: number;
  todayCollections: number;
  pendingPayments: number;
  completedPayments: number;
  paymentMethodBreakdown: Record<string, number>;
}
const Encaissements = () => {
  // États pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [docteur, setDocteur] = useState('');
  const [modePaiement, setModePaiement] = useState('');
  const [dateDebut, setDateDebut] = useState<Date | null>(new Date('2022-09-12'));
  const [dateFin, setDateFin] = useState<Date | null>(new Date('2022-09-18'));
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [isNouvelEncaissementModalOpen, setIsNouvelEncaissementModalOpen] = useState(false);

  // Backend State
  const [encaissements, setEncaissements] = useState<EncaissementData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [paymentStats, setPaymentStats] = useState<PaymentStats>({
    totalCollections: 0,
    totalAmount: 0,
    totalConsumed: 0,
    totalRemaining: 0,
    todayCollections: 0,
    pendingPayments: 0,
    completedPayments: 0,
    paymentMethodBreakdown: {}
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Use payment hook for backend integration
  const {
    paymentCollections,
    refreshAll
  } = usePayment({
    autoFetch: true,
    dataTypes: ['collections']
  });

  // Load payment collections from backend
  useEffect(() => {
    const loadPaymentData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active' && paymentCollections.length > 0) {
          console.log('💰 Loading payment collections from backend...');

          // Transform payment collections to encaissement format
          const encaissementData: EncaissementData[] = paymentCollections.map(payment => ({
            id: payment.id.toString(),
            date: new Date(payment.date).toLocaleDateString('fr-FR'),
            patient: payment.patient_name,
            payeur: payment.payeur || payment.patient_name,
            mode: getPaymentMethodLabel(payment.payment_method),
            montantEncaisse: payment.amount_collected,
            montantConsomme: payment.amount_consumed,
            reliquat: payment.remaining_balance,
            // Additional backend fields
            patient_id: payment.patient_id,
            payment_method: mapPaymentMethod(payment.payment_method),
            status: payment.status,
            notes: payment.notes,
            doctor_name: payment.doctor_name,
            created_at: payment.created_at
          }));

          setEncaissements(encaissementData);

          // Calculate statistics
          const stats: PaymentStats = {
            totalCollections: encaissementData.length,
            totalAmount: encaissementData.reduce((sum, item) => sum + item.montantEncaisse, 0),
            totalConsumed: encaissementData.reduce((sum, item) => sum + item.montantConsomme, 0),
            totalRemaining: encaissementData.reduce((sum, item) => sum + item.reliquat, 0),
            todayCollections: encaissementData.filter(item => isToday(item.date)).length,
            pendingPayments: encaissementData.filter(item => item.status === 'pending').length,
            completedPayments: encaissementData.filter(item => item.status === 'completed').length,
            paymentMethodBreakdown: encaissementData.reduce((acc, item) => {
              acc[item.mode] = (acc[item.mode] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)
          };

          setPaymentStats(stats);
          console.log('✅ Payment collections loaded:', encaissementData.length, 'Statistics:', stats);
        } else {
          // Fallback to empty data when Django is not connected
          setEncaissements([]);
          console.warn('⚠️ Django not connected, showing empty data');
        }
      } catch (error) {
        console.error('❌ Error loading payment collections:', error);
        setError('Failed to load payment collections from backend');
        setEncaissements([]);
      } finally {
        setLoading(false);
      }
    };

    loadPaymentData();
  }, [refreshTrigger, paymentCollections]); // Include paymentCollections in dependencies

  // Helper function to map payment method types
  const mapPaymentMethod = (method: string): 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'insurance' | 'other' => {
    const methodMap: Record<string, 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'insurance' | 'other'> = {
      'cash': 'cash',
      'card': 'credit_card',
      'transfer': 'bank_transfer',
      'check': 'check',
      'insurance': 'insurance'
    };
    return methodMap[method] || 'other';
  };

  // Helper function to convert payment method to French label
  const getPaymentMethodLabel = (method: string): string => {
    const methodLabels: Record<string, string> = {
      'cash': 'Espèces',
      'card': 'Carte',
      'transfer': 'Virement',
      'check': 'Chèque',
      'insurance': 'Assurance',
      'credit_card': 'Carte de crédit',
      'debit_card': 'Carte de débit',
      'bank_transfer': 'Virement',
      'other': 'Autre'
    };
    return methodLabels[method] || method;
  };

  // Helper function to check if date is today
  const isToday = (dateString: string): boolean => {
    const today = new Date().toLocaleDateString('fr-FR');
    return dateString === today;
  };

  // Refresh function
  const refreshPayments = () => {
    setRefreshTrigger(prev => prev + 1);
    refreshAll();
    notifications.show({
      title: 'Actualisation',
      message: 'Données des encaissements actualisées',
      color: 'blue',
    });
  };

  // Use backend data instead of mock data
  const encaissementsData = encaissements;

  // Filtrer les données selon les critères
  const filteredEncaissements = encaissementsData.filter(encaissement =>
    encaissement.patient.toLowerCase().includes(searchTerm.toLowerCase()) ||
    encaissement.payeur.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calcul de la pagination
  const totalPages = Math.ceil(filteredEncaissements.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentEncaissements = filteredEncaissements.slice(startIndex, startIndex + itemsPerPage);

  // Calcul des totaux
  const totalMontantEncaisse = filteredEncaissements.reduce((sum, item) => sum + item.montantEncaisse, 0);
  const totalMontantConsomme = filteredEncaissements.reduce((sum, item) => sum + item.montantConsomme, 0);
  const totalReliquat = filteredEncaissements.reduce((sum, item) => sum + item.reliquat, 0);

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Rendu de l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />;
  };
  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et bouton */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        // className="bg-white border-b border-gray-200"
         bg="blue.6" px="md" py="sm" w={'100%'}
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <Icon path={mdiAccountCash} size={1} color={'white'}/>
            <Text size="lg" fw={600} className="text-gray-800" c="white">
               Recettes
            </Text>
          </Group>

          <Button
            size="sm"
            variant="subtle"
            color="white"
            leftSection={<IconPlus size={16} />}
            className="bg-blue-500 hover:bg-blue-600"
            // onClick={() => setIsNouvelEncaissementModalOpen(true)}
            component='a'
            href='/patient/financial-statement/NouvelEncaissement'
          >
            Nouvel encaissement
          </Button>
        </Group>
      </Card>

      {/* Payment Statistics Dashboard */}
      {djangoStatus === 'connected' && (
        <Card shadow="none" padding="md" radius={0} className="bg-blue-50 border-b border-gray-200">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="lg">Payment Overview</Text>
            <Group gap="xs">
              <Button
                variant="light"
                size="xs"
                onClick={refreshPayments}
                loading={loading}
                leftSection={<Icon path={mdiReload} size={0.6} />}
              >
                Refresh
              </Button>
              <Text size="xs" c={djangoStatus === 'connected' ? 'green' : 'red'}>
                {djangoStatus === 'connected' ? 'Django Connected' : 'Django Disconnected'}
              </Text>
            </Group>
          </Group>

          <Group gap="md">
            {/* Total Collections */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Collections</Text>
              <Text fw={600} size="lg">{paymentStats.totalCollections}</Text>
            </Card>

            {/* Total Amount */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Amount</Text>
              <Text fw={600} size="lg" c="green">{paymentStats.totalAmount.toFixed(2)} DH</Text>
            </Card>

            {/* Total Consumed */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Consumed</Text>
              <Text fw={600} size="lg" c="blue">{paymentStats.totalConsumed.toFixed(2)} DH</Text>
            </Card>

            {/* Total Remaining */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Remaining Balance</Text>
              <Text fw={600} size="lg" c="orange">{paymentStats.totalRemaining.toFixed(2)} DH</Text>
            </Card>

            {/* Today's Collections */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Today&apos;s Collections</Text>
              <Text fw={600} size="lg" c={paymentStats.todayCollections > 0 ? 'green' : 'gray'}>
                {paymentStats.todayCollections}
              </Text>
            </Card>

            {/* Payment Status */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed" mb="xs">Payment Status</Text>
              <Group gap="xs">
                <Badge size="xs" color="green">{paymentStats.completedPayments} Completed</Badge>
                <Badge size="xs" color="orange">{paymentStats.pendingPayments} Pending</Badge>
              </Group>
            </Card>
          </Group>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card shadow="none" padding="md" radius={0} className="bg-white">
          <div className="text-center">
            <Loader size="md" />
            <Text size="sm" c="dimmed" mt="xs">Loading payment collections...</Text>
          </div>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red" className="m-4">
          {error}
        </Alert>
      )}

      {/* Contenu conditionnel selon l'onglet actif */}

        <>
          {/* Filtres */}
          <Card
            shadow="none"
            padding="md"
            radius={0}
            className="bg-white border-b border-gray-200"
          >
            <div className='w-full flex justify-between'>
<div className='w-[50%] flex justify-between '>
    {/* Recherche */}
              <TextInput
                placeholder="Rechercher"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftSection={<IconSearch size={16} />}
              w="58%"
                size="sm"
               
              />

              {/* Docteur */}
              <Select
                placeholder="Docteur"
                value={docteur}
                onChange={(value) => setDocteur(value || '')}
                data={['MEDECIN', 'Dr. SMITH', 'Dr. MARTIN']}
               w="20%"
                size="sm"
              />

              {/* Mode de paiement */}
              <Select
                placeholder="Mode de paiement"
                value={modePaiement}
                onChange={(value) => setModePaiement(value || '')}
                data={['Espèces', 'Carte', 'Chèque', 'Virement']}
                 w="20%"
                size="sm"
              />
</div>
<div className='w-[50%] flex justify-end '>
  
              {/* Date début */}
              <TextInput
                placeholder="Date début"
                value={dateDebut?.toLocaleDateString() || ''}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  setDateDebut(isNaN(date.getTime()) ? null : date);
                }}
                className="w-32 mr-4"
                size="sm"
              />

              {/* Date fin */}
              <TextInput
                placeholder="Date fin"
                value={dateFin?.toLocaleDateString() || ''}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  setDateFin(isNaN(date.getTime()) ? null : date);
                }}
                className="w-32 mr-4"
                size="sm"
              />

              {/* Boutons de période */}
              <Group gap={"1px"}>
                <Button
                  variant="filled"
                  color="#3799CE"
                  size="xs"
                  className="bg-blue-500 hover:bg-blue-600"
                  radius="xs"
                >
                  Aujourd&apos;hui
                </Button>
                <Button
                  variant="filled"
                  color="#3799CE"
                  size="xs"
                  radius="xs"
                   className="bg-blue-500 hover:bg-blue-600"
                >
                  Cette semaine
                </Button>
                <Button
                   variant="filled"
                  color="#3799CE"
                  size="xs"
                  radius="xs"
                   className="bg-blue-500 hover:bg-blue-600"
                >
                  Mois dernier
                </Button>
                <Button
                   variant="filled"
                  color="#3799CE"
                  size="xs"
                  radius="xs"
                   className="bg-blue-500 hover:bg-blue-600"
                >
                  Ce mois
                </Button>
                <Button
                   variant="filled"
                  color="red"
                  size="xs"
                  radius="xs"
                   className="bg-blue-500 hover:bg-blue-600"
                >
                  ✕
                </Button>
              </Group>
</div>
            </div>
          </Card>

          {/* Tableau des encaissements */}
          <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer"
                onClick={() => handleSort('date')}
              >
                <Group gap="xs" justify="space-between">
                  Date
                  {renderSortIcon('date')}
                </Group>
              </Table.Th>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer"
                onClick={() => handleSort('patient')}
              >
                <Group gap="xs" justify="space-between">
                  Patient / Payeur
                  {renderSortIcon('patient')}
                </Group>
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mode
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Montant encaissé
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Montant consommé
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Reliquat
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentEncaissements.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentEncaissements.map((encaissement) => (
                <Table.Tr key={encaissement.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.patient}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.mode}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.montantEncaisse.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.montantConsomme.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.reliquat.toFixed(2)}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec totaux et pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        {/* Ligne des totaux */}
        <div className="border-b border-gray-200 pb-2 mb-3">
          <Table>
            <Table.Tbody>
              <Table.Tr className="bg-gray-50">
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Date */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Patient */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Mode */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right font-medium text-sm">
                  {totalMontantEncaisse.toFixed(2)}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right font-medium text-sm">
                  {totalMontantConsomme.toFixed(2)}
                </Table.Td>
                <Table.Td className="text-right font-medium text-sm">
                  {totalReliquat.toFixed(2)}
                </Table.Td>
              </Table.Tr>
            </Table.Tbody>
          </Table>
        </div>

        {/* Légende et pagination */}
        <Group justify="space-between" align="center">
          <Group gap="md" align="center">
            {/* Légende des statuts */}
            <Group gap="sm" align="center">
              <Group gap="xs" align="center">
                <Indicator color="green" size={12} />
                <Text size="sm" className="text-gray-600">
                  Payé(e)
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="orange" size={12} />
                <Text size="sm" className="text-gray-600">
                  Réglé(e) Partiellement
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="red" size={12} />
                <Text size="sm" className="text-gray-600">
                  Non Réglé(e)
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="gray" size={12} />
                <Text size="sm" className="text-gray-600">
                  Dispensé(e)/Clôturé(e)
                </Text>
              </Group>
            </Group>
          </Group>

          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">Page</Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">Lignes par Page</Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 10)}
              data={['10', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredEncaissements.length)} de {filteredEncaissements.length}
            </Text>

            <Group gap="xs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(1)}
              >
                <IconChevronsLeft size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              >
                <IconChevronLeft size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              >
                <IconChevronRight size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(totalPages)}
              >
                <IconChevronsRight size={14} />
              </ActionIcon>
            </Group>
          </Group>
        </Group>
          </Card>

          {/* Modale pour le nouvel encaissement */}
          <Nouvel_encaissement_modal
            opened={isNouvelEncaissementModalOpen}
            onClose={() => setIsNouvelEncaissementModalOpen(false)}
          />
        </>
       
    </Box>
  );
};

export default Encaissements;
