'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  Tabs,
  NumberInput,
  Checkbox,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconSettings,
  IconSearch,
  IconPlus,
  IconMinus,
} from '@tabler/icons-react';

const Abonnement_form = () => {
  // États pour les données de l'abonnement
  const [numeroContrat, setNumeroContrat] = useState('2');
  const [numeroDossier, setNumeroDossier] = useState('');
  const [date, setDate] = useState<Date | null>(new Date('2022-09-16'));
  const [dateActivation, setDateActivation] = useState<Date | null>(new Date('2022-09-16'));
  const [demarrage, setDemarrage] = useState<Date | null>(new Date('2022-09-16'));
  const [patient, setPatient] = useState('ABADI SOUAD');
  const [plan, setPlan] = useState('');
  const [ville, setVille] = useState('');
  const [affectation, setAffectation] = useState('');
  const [tarification, setTarification] = useState('');
  const [technicien, setTechnicien] = useState('');
  const [modePaiement, setModePaiement] = useState('Espèce');
  const [payeur, setPayeur] = useState('Patient');
  const [conditionPaiement, setConditionPaiement] = useState('');
  const [cycleAbonnement, setCycleAbonnement] = useState('Renouvellement automatique jusqu\'à annulation');
  const [nombreCycle, setNombreCycle] = useState(1);
  const [organisme, setOrganisme] = useState('');
  const [essaiGratuit, setEssaiGratuit] = useState(0);
  const [realisationAutomatique, setRealisationAutomatique] = useState(0);
  const [activeTab, setActiveTab] = useState('details');

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <Title order={4} className="text-white font-medium">
              Abonnement N°: 2
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Paramètres">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Section gauche avec les informations de base */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-96 bg-white border-r border-gray-200"
        >
          <Stack gap="sm">
            {/* Première ligne : N° Contrat et N° dossier */}
            <Group grow>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  N° Contrat *
                </Text>
                <TextInput
                  value={numeroContrat}
                  onChange={(e) => setNumeroContrat(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  N° dossier
                </Text>
                <TextInput
                  value={numeroDossier}
                  onChange={(e) => setNumeroDossier(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </Group>

            {/* Deuxième ligne : Date et Date d'activation */}
            <Group grow>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Date *
                </Text>
                <DatePickerInput
                  value={date}
                  onChange={setDate}
                  size="xs"
                  className="w-full"
                  placeholder="16/09/2022"
                />
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Date d'activation *
                </Text>
                <DatePickerInput
                  value={dateActivation}
                  onChange={setDateActivation}
                  size="xs"
                  className="w-full"
                  placeholder="16/09/2022"
                />
              </div>
            </Group>

            {/* Troisième ligne : Démarrage le */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Démarrage le *
              </Text>
              <DatePickerInput
                value={demarrage}
                onChange={setDemarrage}
                size="xs"
                className="w-full"
                placeholder="16/09/2022"
              />
            </div>

            {/* Choisir un patient */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Choisir un patient
              </Text>
              <Group gap="xs">
                <TextInput
                  value={patient}
                  onChange={(e) => setPatient(e.target.value)}
                  size="xs"
                  className="flex-1"
                  placeholder="ABADI SOUAD"
                />
                <ActionIcon
                  variant="light"
                  color="blue"
                  size="sm"
                >
                  <IconSearch size={14} />
                </ActionIcon>
              </Group>
            </div>

            {/* Quatrième ligne : Plan et Tarification */}
            <Group grow>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Plan
                </Text>
                <Group gap="xs">
                  <Select
                    value={plan}
                    onChange={(value) => setPlan(value || '')}
                    size="xs"
                    className="flex-1"
                    data={[]}
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Tarification
                </Text>
                <Group gap="xs">
                  <Select
                    value={tarification}
                    onChange={(value) => setTarification(value || '')}
                    size="xs"
                    className="flex-1"
                    data={[]}
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Group>

            {/* Cinquième ligne : Ville et Technicien */}
            <Group grow>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Ville
                </Text>
                <Group gap="xs">
                  <Select
                    value={ville}
                    onChange={(value) => setVille(value || '')}
                    size="xs"
                    className="flex-1"
                    data={[]}
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-1">
                  Technicien
                </Text>
                <Group gap="xs">
                  <Select
                    value={technicien}
                    onChange={(value) => setTechnicien(value || '')}
                    size="xs"
                    className="flex-1"
                    data={[]}
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Group>

            {/* Affectation */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Affectation
              </Text>
              <Group gap="xs">
                <Select
                  value={affectation}
                  onChange={(value) => setAffectation(value || '')}
                  size="xs"
                  className="flex-1"
                  data={[]}
                />
                <ActionIcon
                  variant="light"
                  color="blue"
                  size="sm"
                >
                  <IconPlus size={14} />
                </ActionIcon>
              </Group>
            </div>
          </Stack>
        </Card>

        {/* Section droite avec les détails */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Section des options de paiement */}
          <Card
            shadow="none"
            padding="sm"
            radius={0}
            className="border-b border-gray-200"
          >
            <Group grow>
              {/* Mode de paiement */}
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Mode de paiement
                </Text>
                <Select
                  value={modePaiement}
                  onChange={(value) => setModePaiement(value || 'Espèce')}
                  size="xs"
                  data={['Espèce', 'Carte', 'Chèque', 'Virement']}
                />
              </div>

              {/* Payeur */}
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Payeur
                </Text>
                <Radio.Group
                  value={payeur}
                  onChange={setPayeur}
                  size="xs"
                >
                  <Group gap="md">
                    <Radio value="Patient" label="Patient" />
                    <Radio value="Tiers" label="Tiers payant" />
                  </Group>
                </Radio.Group>
              </div>

              {/* Condition de paiement */}
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Condition de paiement
                </Text>
                <Select
                  value={conditionPaiement}
                  onChange={(value) => setConditionPaiement(value || '')}
                  size="xs"
                  data={[]}
                />
              </div>
            </Group>

            {/* Cycle d'abonnement */}
            <div className="mt-4">
              <Text size="xs" fw={500} className="text-gray-700 mb-2">
                Cycle d'abonnement
              </Text>
              <Radio.Group
                value={cycleAbonnement}
                onChange={setCycleAbonnement}
                size="xs"
              >
                <Stack gap="xs">
                  <Radio 
                    value="Renouvellement automatique jusqu'à annulation" 
                    label="Renouvellement automatique jusqu'à annulation" 
                  />
                  <Radio 
                    value="Expire après des cycles" 
                    label="Expire après des cycles" 
                  />
                </Stack>
              </Radio.Group>
            </div>

            {/* Nombre de cycle et Organisme */}
            <Group grow className="mt-4">
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Nombre de cycle
                </Text>
                <NumberInput
                  value={nombreCycle}
                  onChange={(value) => setNombreCycle(Number(value) || 1)}
                  size="xs"
                  min={1}
                />
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Organisme
                </Text>
                <Select
                  value={organisme}
                  onChange={(value) => setOrganisme(value || '')}
                  size="xs"
                  data={[]}
                />
              </div>
            </Group>

            {/* Options supplémentaires */}
            <Group grow className="mt-4">
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Facturer chargé
                </Text>
                <Group gap="md">
                  <Text size="xs">An(s)</Text>
                  <NumberInput
                    value={1}
                    size="xs"
                    className="w-16"
                  />
                </Group>
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Essai gratuit(jours)
                </Text>
                <NumberInput
                  value={essaiGratuit}
                  onChange={(value) => setEssaiGratuit(Number(value) || 0)}
                  size="xs"
                />
              </div>
              <div>
                <Text size="xs" fw={500} className="text-gray-700 mb-2">
                  Réalisation automatique...
                </Text>
                <NumberInput
                  value={realisationAutomatique}
                  onChange={(value) => setRealisationAutomatique(Number(value) || 0)}
                  size="xs"
                />
              </div>
            </Group>
          </Card>

          {/* Section onglets */}
          <div className="flex-1 flex flex-col">
            <Tabs
              value={activeTab}
              onChange={(value) => setActiveTab(value || 'details')}
              className="flex-1 flex flex-col"
            >
              <Tabs.List className="bg-gray-100 border-b border-gray-200">
                <Tabs.Tab
                  value="details"
                  className="text-sm font-medium"
                >
                  Détails
                </Tabs.Tab>
                <Tabs.Tab
                  value="commentaires"
                  className="text-sm font-medium"
                >
                  Commentaires
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="details" className="flex-1 flex flex-col">
                <div className="flex-1 overflow-auto">
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                    className="h-full"
                  >
                    <Table.Thead className="bg-gray-50 sticky top-0">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Code
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Description
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-20">
                          Qté
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Prix
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Forfaitaire
                        </Table.Th>
                        <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Montant
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      <Table.Tr>
                        <Table.Td colSpan={6} className="text-center py-8">
                          <Text size="sm" className="text-gray-500">
                            Aucun élément trouvé
                          </Text>
                        </Table.Td>
                      </Table.Tr>
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Section pagination et total */}
                <div className="border-t border-gray-300 bg-gray-50 p-3">
                  <Group justify="space-between" align="center">
                    <Group gap="sm" align="center">
                      <Text size="sm" className="text-gray-600">Page</Text>
                      <Select
                        value={currentPage.toString()}
                        onChange={(value) => setCurrentPage(Number(value) || 1)}
                        data={['1']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                      <Select
                        value={itemsPerPage.toString()}
                        onChange={(value) => setItemsPerPage(Number(value) || 5)}
                        data={['5', '10', '20', '50']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                    </Group>

                    <Text size="sm" fw={600} className="text-gray-800">
                      Total : 0,00
                    </Text>
                  </Group>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="commentaires" className="flex-1 flex flex-col">
                <div className="flex-1 flex items-center justify-center">
                  <Text size="sm" className="text-gray-500">
                    Contenu des commentaires
                  </Text>
                </div>
              </Tabs.Panel>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-gray-50 border-t border-gray-200"
      >
        <Group justify="flex-end" gap="sm">
          <Button
            variant="outline"
            color="red"
            size="sm"
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="gray"
            size="sm"
          >
            Valider
          </Button>
          <Button
            variant="outline"
            color="blue"
            size="sm"
          >
            Enregistrer et quitter
          </Button>
          <Button
            color="blue"
            size="sm"
          >
            Enregistrer
          </Button>
        </Group>
      </Card>
    </Box>
  );
};

export default Abonnement_form;
