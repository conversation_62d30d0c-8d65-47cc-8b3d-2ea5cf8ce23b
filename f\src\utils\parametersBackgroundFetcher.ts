/**
 * Parameters Background Data Fetcher
 * Handles background fetching and caching of system parameters data
 * Provides real-time updates without blocking the UI
 */

import { parametersService, ParametersSummary } from '@/services/parametersService';

interface CacheEntry {
  data: ParametersSummary;
  timestamp: number;
  expiresAt: number;
}

interface FetchOptions {
  forceRefresh?: boolean;
  cacheTimeout?: number; // in milliseconds
  priority?: 'low' | 'normal' | 'high';
}

interface BackgroundFetcherConfig {
  defaultCacheTimeout: number;
  maxConcurrentFetches: number;
  retryAttempts: number;
  retryDelay: number;
}

class ParametersBackgroundFetcher {
  private cache = new Map<string, CacheEntry>();
  private activeFetches = new Map<string, Promise<ParametersSummary>>();
  private fetchQueue: Array<{ 
    cacheKey: string; 
    options: FetchOptions; 
    resolve: Function; 
    reject: Function 
  }> = [];
  private isProcessingQueue = false;
  
  private config: BackgroundFetcherConfig = {
    defaultCacheTimeout: 15 * 60 * 1000, // 15 minutes (parameters change infrequently)
    maxConcurrentFetches: 2,
    retryAttempts: 3,
    retryDelay: 2000, // 2 seconds
  };

  private listeners = new Map<string, Set<(data: ParametersSummary) => void>>();

  /**
   * Get parameters data with background fetching
   */
  async getParametersData(options: FetchOptions = {}): Promise<ParametersSummary> {
    const {
      forceRefresh = false,
      cacheTimeout = this.config.defaultCacheTimeout,
      priority = 'normal'
    } = options;

    const cacheKey = 'parameters_summary';

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        // Start background refresh if data is getting old
        const age = Date.now() - cached.timestamp;
        if (age > cacheTimeout * 0.8) { // Refresh when 80% of cache time has passed
          this.queueBackgroundFetch(cacheKey, { ...options, priority: 'low' });
        }
        return cached.data;
      }
    }

    // Check if already fetching
    const activeFetch = this.activeFetches.get(cacheKey);
    if (activeFetch) {
      return activeFetch;
    }

    // Queue the fetch
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options: { ...options, cacheTimeout }, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Subscribe to real-time updates for parameters data
   */
  subscribe(callback: (data: ParametersSummary) => void): () => void {
    const cacheKey = 'parameters_summary';
    
    if (!this.listeners.has(cacheKey)) {
      this.listeners.set(cacheKey, new Set());
    }
    
    this.listeners.get(cacheKey)!.add(callback);

    // Start background fetching
    this.queueBackgroundFetch(cacheKey, { priority: 'normal' });

    // Return unsubscribe function
    return () => {
      const keyListeners = this.listeners.get(cacheKey);
      if (keyListeners) {
        keyListeners.delete(callback);
        if (keyListeners.size === 0) {
          this.listeners.delete(cacheKey);
        }
      }
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    averageAge: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const now = Date.now();
    let expiredCount = 0;
    let totalAge = 0;
    let oldestAge = 0;
    let newestAge = Infinity;
    
    for (const [, entry] of this.cache) {
      const age = now - entry.timestamp;
      if (entry.expiresAt < now) {
        expiredCount++;
      }
      totalAge += age;
      oldestAge = Math.max(oldestAge, age);
      newestAge = Math.min(newestAge, age);
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would need to track hits/misses
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
      oldestEntry: oldestAge,
      newestEntry: newestAge === Infinity ? 0 : newestAge,
    };
  }

  /**
   * Force refresh data and notify all subscribers
   */
  async refreshData(): Promise<ParametersSummary> {
    const cacheKey = 'parameters_summary';
    this.clearCache();
    const data = await this.getParametersData({ 
      forceRefresh: true, 
      priority: 'high'
    });
    this.notifyListeners(cacheKey, data);
    return data;
  }

  /**
   * Get system health summary
   */
  async getSystemHealthData() {
    const data = await this.getParametersData();
    
    if (!data.analytics) {
      return {
        status: 'unknown',
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        uptime: 0,
        responseTime: 0,
        errorRate: 0,
        activeUsers: 0,
        totalUsers: 0,
        lastBackupDate: null,
      };
    }

    const { system_performance, system_uptime, active_users, total_users } = data.analytics;
    const { cpu_usage, memory_usage, disk_usage, response_time, error_rate } = system_performance;

    // Determine system health status
    let status: 'healthy' | 'warning' | 'critical' | 'unknown' = 'healthy';
    
    if (cpu_usage > 90 || memory_usage > 90 || disk_usage > 95 || error_rate > 5 || system_uptime < 95) {
      status = 'critical';
    } else if (cpu_usage > 70 || memory_usage > 80 || disk_usage > 85 || error_rate > 1 || system_uptime < 99) {
      status = 'warning';
    }

    const lastBackup = data.dataBackups
      .sort((a, b) => new Date(b.backup_date).getTime() - new Date(a.backup_date).getTime())[0];

    return {
      status,
      cpuUsage: cpu_usage,
      memoryUsage: memory_usage,
      diskUsage: disk_usage,
      uptime: system_uptime,
      responseTime: response_time,
      errorRate: error_rate,
      activeUsers: active_users,
      totalUsers: total_users,
      lastBackupDate: lastBackup?.backup_date || null,
    };
  }

  /**
   * Get system configuration summary
   */
  async getSystemConfigurationData() {
    const data = await this.getParametersData();
    
    const parametersByCategory = data.systemParameters.reduce((acc, param) => {
      if (!acc[param.category]) {
        acc[param.category] = [];
      }
      acc[param.category].push(param);
      return acc;
    }, {} as Record<string, any[]>);

    const activeUsers = data.systemUsers.filter(u => u.is_active);
    const activeSpecialties = data.specialties.filter(s => s.is_active);

    return {
      totalParameters: data.systemParameters.length,
      parametersByCategory,
      totalUsers: data.systemUsers.length,
      activeUsers: activeUsers.length,
      totalSpecialties: data.specialties.length,
      activeSpecialties: activeSpecialties.length,
      totalContacts: data.contacts.length,
      totalTechnicians: data.technicians.length,
      totalConfigurations: data.applicationConfigurations.length,
      recentBackups: data.dataBackups
        .sort((a, b) => new Date(b.backup_date).getTime() - new Date(a.backup_date).getTime())
        .slice(0, 5),
      configurationChanges: data.analytics?.configuration_changes || [],
    };
  }

  /**
   * Get user management summary
   */
  async getUserManagementData() {
    const data = await this.getParametersData();
    
    const usersByProfile = data.systemUsers.reduce((acc, user) => {
      if (!acc[user.profile_name]) {
        acc[user.profile_name] = [];
      }
      acc[user.profile_name].push(user);
      return acc;
    }, {} as Record<string, any[]>);

    const usersBySpecialty = data.systemUsers.reduce((acc, user) => {
      const specialty = user.specialty_name || 'No Specialty';
      if (!acc[specialty]) {
        acc[specialty] = [];
      }
      acc[specialty].push(user);
      return acc;
    }, {} as Record<string, any[]>);

    const recentActivity = data.analytics?.user_activity || [];

    return {
      totalUsers: data.systemUsers.length,
      activeUsers: data.systemUsers.filter(u => u.is_active).length,
      usersByProfile,
      usersBySpecialty,
      totalProfiles: data.userProfiles.length,
      recentActivity,
      lastLoginUsers: data.systemUsers
        .filter(u => u.last_login)
        .sort((a, b) => new Date(b.last_login!).getTime() - new Date(a.last_login!).getTime())
        .slice(0, 5),
    };
  }

  // Private methods

  private getCachedData(cacheKey: string): CacheEntry | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    const now = Date.now();
    if (entry.expiresAt < now) {
      this.cache.delete(cacheKey);
      return null;
    }

    return entry;
  }

  private async queueBackgroundFetch(cacheKey: string, options: FetchOptions): Promise<ParametersSummary> {
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.fetchQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.fetchQueue.length > 0 && this.activeFetches.size < this.config.maxConcurrentFetches) {
      // Sort queue by priority
      this.fetchQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.options.priority || 'normal'] - priorityOrder[a.options.priority || 'normal'];
      });

      const item = this.fetchQueue.shift();
      if (!item) break;

      const { cacheKey, options, resolve, reject } = item;

      // Skip if already fetching this cache key
      if (this.activeFetches.has(cacheKey)) {
        const existingFetch = this.activeFetches.get(cacheKey)!;
        existingFetch.then(resolve).catch(reject);
        continue;
      }

      // Start the fetch
      const fetchPromise = this.performFetch(cacheKey, options);
      this.activeFetches.set(cacheKey, fetchPromise);

      fetchPromise
        .then((data) => {
          resolve(data);
          this.notifyListeners(cacheKey, data);
        })
        .catch(reject)
        .finally(() => {
          this.activeFetches.delete(cacheKey);
          // Continue processing queue
          setTimeout(() => this.processQueue(), 0);
        });
    }

    this.isProcessingQueue = false;
  }

  private async performFetch(cacheKey: string, options: FetchOptions): Promise<ParametersSummary> {
    const { cacheTimeout = this.config.defaultCacheTimeout } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const data = await parametersService.getParametersSummary();
        
        // Cache the result
        const now = Date.now();
        this.cache.set(cacheKey, {
          data,
          timestamp: now,
          expiresAt: now + cacheTimeout,
        });

        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError || new Error('Failed to fetch parameters data');
  }

  private notifyListeners(cacheKey: string, data: ParametersSummary): void {
    const listeners = this.listeners.get(cacheKey);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in parameters listener:', error);
        }
      });
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [cacheKey, entry] of this.cache) {
      if (entry.expiresAt < now) {
        this.cache.delete(cacheKey);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startPeriodicCleanup(interval: number = 300000): () => void { // 5 minutes
    const intervalId = setInterval(() => {
      this.cleanupCache();
    }, interval);

    return () => clearInterval(intervalId);
  }

  /**
   * Monitor system health and trigger alerts
   */
  async monitorSystemHealth(): Promise<{
    alerts: Array<{
      type: 'warning' | 'critical';
      message: string;
      metric: string;
      value: number;
      threshold: number;
    }>;
    status: 'healthy' | 'warning' | 'critical';
  }> {
    const healthData = await this.getSystemHealthData();
    const alerts: Array<{
      type: 'warning' | 'critical';
      message: string;
      metric: string;
      value: number;
      threshold: number;
    }> = [];

    // Check CPU usage
    if (healthData.cpuUsage > 90) {
      alerts.push({
        type: 'critical',
        message: 'CPU usage is critically high',
        metric: 'cpu_usage',
        value: healthData.cpuUsage,
        threshold: 90,
      });
    } else if (healthData.cpuUsage > 70) {
      alerts.push({
        type: 'warning',
        message: 'CPU usage is high',
        metric: 'cpu_usage',
        value: healthData.cpuUsage,
        threshold: 70,
      });
    }

    // Check memory usage
    if (healthData.memoryUsage > 90) {
      alerts.push({
        type: 'critical',
        message: 'Memory usage is critically high',
        metric: 'memory_usage',
        value: healthData.memoryUsage,
        threshold: 90,
      });
    } else if (healthData.memoryUsage > 80) {
      alerts.push({
        type: 'warning',
        message: 'Memory usage is high',
        metric: 'memory_usage',
        value: healthData.memoryUsage,
        threshold: 80,
      });
    }

    // Check disk usage
    if (healthData.diskUsage > 95) {
      alerts.push({
        type: 'critical',
        message: 'Disk usage is critically high',
        metric: 'disk_usage',
        value: healthData.diskUsage,
        threshold: 95,
      });
    } else if (healthData.diskUsage > 85) {
      alerts.push({
        type: 'warning',
        message: 'Disk usage is high',
        metric: 'disk_usage',
        value: healthData.diskUsage,
        threshold: 85,
      });
    }

    // Check system uptime
    if (healthData.uptime < 95) {
      alerts.push({
        type: 'critical',
        message: 'System uptime is critically low',
        metric: 'uptime',
        value: healthData.uptime,
        threshold: 95,
      });
    } else if (healthData.uptime < 99) {
      alerts.push({
        type: 'warning',
        message: 'System uptime is low',
        metric: 'uptime',
        value: healthData.uptime,
        threshold: 99,
      });
    }

    // Check error rate
    if (healthData.errorRate > 5) {
      alerts.push({
        type: 'critical',
        message: 'Error rate is critically high',
        metric: 'error_rate',
        value: healthData.errorRate,
        threshold: 5,
      });
    } else if (healthData.errorRate > 1) {
      alerts.push({
        type: 'warning',
        message: 'Error rate is high',
        metric: 'error_rate',
        value: healthData.errorRate,
        threshold: 1,
      });
    }

    return {
      alerts,
      status: healthData.status,
    };
  }
}

// Create singleton instance
export const parametersBackgroundFetcher = new ParametersBackgroundFetcher();

// Auto-start periodic cleanup
parametersBackgroundFetcher.startPeriodicCleanup();

export default parametersBackgroundFetcher;
