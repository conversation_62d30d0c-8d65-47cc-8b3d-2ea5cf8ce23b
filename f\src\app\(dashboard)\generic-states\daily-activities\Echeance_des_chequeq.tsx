'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Radio,
  Menu,
  Alert
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiCalendarAlert,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface CheckDueDateColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface CheckDueDateQuery {
  start: Date;
  end: Date;
  date_field: 'due_date' | 'encasement_date';
}

interface CheckDueDateDisplayType {
  value: 'flat' | 'compact';
  label: string;
}

interface EcheanceDesChequeqProps {
  loading?: boolean;
  onQueryChange?: (query: CheckDueDateQuery) => void;
  onDisplayTypeChange?: (type: 'flat' | 'compact') => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const EcheanceDesChequeq: React.FC<EcheanceDesChequeqProps> = ({
  loading = false,
  onQueryChange,
  onDisplayTypeChange,
  onExport,
  onPrint,
  onSort
}) => {
  // États locaux
  const [query, setQuery] = useState<CheckDueDateQuery>({
    start: new Date(),
    end: new Date(),
    date_field: 'due_date'
  });

  const [displayType, setDisplayType] = useState<'flat' | 'compact'>('flat');
  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: CheckDueDateColumn[] = [
    { id: 'payable_title', label: 'Titre du payable', sortable: true },
    { id: 'payment_mode', label: 'Mode de paiement', sortable: true },
    { id: 'beneficiary', label: 'Bénéficiaire', sortable: true },
    { id: 'doctor', label: 'Médecin', sortable: true },
    { id: 'amount', label: 'Montant', sortable: true },
    { id: 'due_date', label: 'Date d\'échéance', sortable: true }
  ];

  // Options de recherche
  const searchOptions = [
    { value: 'due_date', label: 'Date d\'échéance' },
    { value: 'encasement_date', label: 'Date d\'Encaissement' }
  ];

  // Types d'affichage
  const displayTypes: CheckDueDateDisplayType[] = [
    { value: 'flat', label: 'Affichage aplatie' },
    { value: 'compact', label: 'Tableau croisé-dynamique' }
  ];

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<CheckDueDateQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleDisplayTypeChange = (type: 'flat' | 'compact') => {
    setDisplayType(type);
    onDisplayTypeChange?.(type);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiCalendarAlert} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Échéance des chèques</Text>
          </Group>
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md">
          {/* Chercher par */}
          <Box>
            <Text size="sm" fw={500} mb="xs">Chercher par</Text>
            <Radio.Group
              value={query.date_field}
              onChange={(value) => handleQueryChange({ date_field: value as 'due_date' | 'encasement_date' })}
            >
              <Group>
                {searchOptions.map((option) => (
                  <Radio
                    key={option.value}
                    value={option.value}
                    label={option.label}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>

          {/* Date picker "Du" */}
          <DateInput
            label="Du"
            value={query.start}
            onChange={(value) => value && handleQueryChange({ start: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          {/* Date picker "Au" */}
          <DateInput
            label="Au"
            value={query.end}
            onChange={(value) => value && handleQueryChange({ end: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          <Box style={{ flex: 1 }} />

          {/* Type d'affichage */}
          <Box>
            <Text size="sm" fw={500} mb="xs">Type d&apos;affichage</Text>
            <Radio.Group
              value={displayType}
              onChange={(value) => handleDisplayTypeChange(value as 'flat' | 'compact')}
            >
              <Group>
                {displayTypes.map((type) => (
                  <Radio
                    key={type.value}
                    value={type.value}
                    label={type.label}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>
        </Group>
      </Paper>

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <Menu shadow="md" width={250}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Format">
                      <Icon path={mdiTableEdit} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatLetterMatches} size={0.8} />}
                    >
                      Format de cellule
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatColorHighlight} size={0.8} />}
                    >
                      La mise en forme conditionnelle
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Champs">
                  <Icon path={mdiTableSettings} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Tableau des données */}
            <ScrollArea style={{ height: 'calc(100vh - 300px)' }}>
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    {columns.map((column) => (
                      <Table.Th
                        key={column.id}
                        style={{
                          minWidth: 120,
                          backgroundColor: '#f8f9fa',
                          cursor: column.sortable ? 'pointer' : 'default'
                        }}
                        onClick={() => column.sortable && handleSort(column.id)}
                      >
                        <Group gap="xs" justify="space-between">
                          <Text size="sm" fw={500}>{column.label}</Text>
                          {column.sortable && (
                            <Icon
                              path={getSortIcon(column.id) || mdiArrowUp}
                              size={0.6}
                              style={{
                                opacity: getSortIcon(column.id) ? 1 : 0.3
                              }}
                            />
                          )}
                        </Group>
                      </Table.Th>
                    ))}
                    {/* Colonnes vides supplémentaires */}
                    {Array.from({ length: 6 }, (_, index) => (
                      <Table.Th
                        key={`empty-col-${index}`}
                        style={{
                          minWidth: 100,
                          backgroundColor: '#f8f9fa'
                        }}
                      />
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {/* Ligne avec cellule grand total */}
                  <Table.Tr>
                    <Table.Td />
                    <Table.Td />
                    <Table.Td />
                    <Table.Td />
                    <Table.Td style={{ backgroundColor: '#e8f5e8', textAlign: 'center' }}>
                      <Text size="sm" c="dimmed">-</Text>
                    </Table.Td>
                    <Table.Td />
                    {/* Cellules vides */}
                    {Array.from({ length: 6 }, (_, index) => (
                      <Table.Td key={`empty-total-${index}`} />
                    ))}
                  </Table.Tr>

                  {/* Lignes vides pour remplir l'espace */}
                  {Array.from({ length: 20 }, (_, index) => (
                    <Table.Tr key={`empty-row-${index}`}>
                      {Array.from({ length: 12 }, (_, cellIndex) => (
                        <Table.Td key={cellIndex} style={{ height: 30 }} />
                      ))}
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Message d'état vide */}
            <Box
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1
              }}
            >
              <Alert
                icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                title="Aucune donnée disponible"
                color="gray"
                variant="light"
                style={{ maxWidth: 400 }}
              >
                <Text size="sm" c="dimmed">
                  Aucune échéance de chèques trouvée pour la période sélectionnée.
                  Veuillez vérifier les filtres ou sélectionner une autre période.
                </Text>
              </Alert>
            </Box>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default EcheanceDesChequeq;
