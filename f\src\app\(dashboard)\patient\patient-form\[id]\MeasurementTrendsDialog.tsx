import {
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Unstyled<PERSON><PERSON>on,
  rem,
  <PERSON><PERSON>,
  <PERSON><PERSON>,

  But<PERSON>
} from '@mantine/core';
import { IconChartLine, IconListDetails, IconAlertCircle, IconRefresh } from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { notifications } from '@mantine/notifications';
import patientService, { BiometricMeasureDefinition, PatientBiometricMeasurement } from '@/services/patientService';


interface MeasurementTrendData {
  id: string;
  name: string;
  unit?: string;
  data: number[];
  labels: string[];
  measurements: PatientBiometricMeasurement[];
}

interface Props {
  opened: boolean;
  onClose: () => void;
  patientId?: string;
}

export function MeasurementTrendsDialog({ opened, onClose, patientId }: Props) {
  const [currentItem, setCurrentItem] = useState<MeasurementTrendData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [trendData, setTrendData] = useState<MeasurementTrendData[]>([]);
  const [, setBiometricDefinitions] = useState<BiometricMeasureDefinition[]>([]);

  // Load biometric data when dialog opens
  useEffect(() => {
    if (opened && patientId) {
      loadBiometricTrends();
    }
  }, [opened, patientId]);

  const loadBiometricTrends = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        throw new Error('Django backend is not connected');
      }

      // Load biometric definitions
      const definitions = await patientService.getBiometricMeasureDefinitions();
      if (definitions) {
        setBiometricDefinitions(definitions);
      }

      if (patientId) {
        // Load patient measurements
        const measurements = await patientService.getPatientBiometricMeasurements(patientId);
        if (measurements) {
          // Group measurements by definition and create trend data
          const trendsMap = new Map<string, MeasurementTrendData>();

          measurements.forEach(measurement => {
            const defId = measurement.measure_definition.id;
            const defLabel = measurement.measure_definition.label;
            const defUnit = measurement.measure_definition.unit;

            if (!trendsMap.has(defId)) {
              trendsMap.set(defId, {
                id: defId,
                name: defLabel,
                unit: defUnit,
                data: [],
                labels: [],
                measurements: []
              });
            }

            const trend = trendsMap.get(defId)!;
            trend.measurements.push(measurement);
          });

          // Sort measurements by date and create chart data
          const trends: MeasurementTrendData[] = Array.from(trendsMap.values()).map(trend => {
            // Sort measurements by date
            const sortedMeasurements = trend.measurements.sort((a, b) =>
              new Date(a.measurement_date).getTime() - new Date(b.measurement_date).getTime()
            );

            return {
              ...trend,
              data: sortedMeasurements.map(m => parseFloat(m.value.toString()) || 0),
              labels: sortedMeasurements.map(m =>
                new Date(m.measurement_date).toLocaleDateString()
              ),
              measurements: sortedMeasurements
            };
          }).filter(trend => trend.data.length > 0); // Only include trends with data

          setTrendData(trends);

          // Auto-select first item if available
          if (trends.length > 0) {
            setCurrentItem(trends[0]);
          }
        }
      }
    } catch (error) {
      console.error('Error loading biometric trends:', error);
      setError('Failed to load biometric trends. Please try again.');
      notifications.show({
        title: 'Error',
        message: 'Failed to load biometric trends',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconChartLine size={20} />
          <Text fw={600}>Biometric Trends</Text>
        </Group>
      }
      position="right"
      size="xl"
    >
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} color="red" mb="md">
          {error}
        </Alert>
      )}

      <Group align="flex-start" gap="lg" mt="md">
        {/* Sidebar */}
        <ScrollArea w={250} h={500}>
          <Stack>
            <Group justify="space-between">
              <Group>
                <IconListDetails size={18} />
                <Text size="sm" fw={500}>Available Measurements</Text>
              </Group>
              <Button
                size="xs"
                variant="light"
                onClick={loadBiometricTrends}
                loading={loading}
                leftSection={<IconRefresh size={14} />}
              >
                Refresh
              </Button>
            </Group>

            {loading ? (
              <Group justify="center" p="md">
                <Loader size="sm" />
                <Text size="sm" c="dimmed">Loading trends...</Text>
              </Group>
            ) : trendData.length === 0 ? (
              <Text size="sm" c="dimmed" ta="center" p="md">
                No measurement data available for trends
              </Text>
            ) : (
              trendData.map((item) => (
                <UnstyledButton
                  key={item.id}
                  onClick={() => setCurrentItem(item)}
                  style={{
                    backgroundColor:
                      currentItem?.id === item.id ? '#e7f5ff' : 'transparent',
                    padding: rem(8),
                    borderRadius: rem(6),
                    textAlign: 'left',
                    width: '100%',
                  }}
                >
                  <Stack gap="xs">
                    <Text size="sm" fw={500}>{item.name}</Text>
                    <Text size="xs" c="dimmed">
                      {item.data.length} measurements
                      {item.unit && ` (${item.unit})`}
                    </Text>
                  </Stack>
                </UnstyledButton>
              ))
            )}
          </Stack>
        </ScrollArea>

        {/* Chart area */}
        <div style={{ flex: 1 }}>
          {!currentItem ? (
            <Group mt="lg" justify="center" align="center" gap="xs">
              <IconAlertCircle size={20} color="gray" />
              <Text c="dimmed">Select a measurement type to view trends</Text>
            </Group>
          ) : (
            <Stack>
              <Group justify="space-between">
                <Text fw={500} size="lg">
                  {currentItem.name} Trends
                  {currentItem.unit && ` (${currentItem.unit})`}
                </Text>
                <Text size="sm" c="dimmed">
                  {currentItem.data.length} data points
                </Text>
              </Group>

              <Line
                height={400}
                data={{
                  labels: currentItem.labels,
                  datasets: [
                    {
                      label: currentItem.name,
                      data: currentItem.data,
                      fill: false,
                      borderColor: '#339af0',
                      backgroundColor: '#339af0',
                      tension: 0.3,
                      pointRadius: 4,
                      pointHoverRadius: 6,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: true,
                      position: 'top' as const,
                    },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                    },
                  },
                  scales: {
                    x: {
                      display: true,
                      title: {
                        display: true,
                        text: 'Date'
                      }
                    },
                    y: {
                      display: true,
                      title: {
                        display: true,
                        text: currentItem.unit || 'Value'
                      },
                      beginAtZero: false,
                    },
                  },
                  interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false,
                  },
                }}
              />
            </Stack>
          )}
        </div>
      </Group>
    </Drawer>
  );
}
