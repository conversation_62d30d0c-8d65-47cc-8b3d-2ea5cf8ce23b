'use client';
import { useForm } from '@mantine/form';
import { useState } from 'react';
import { IconPalette } from '@tabler/icons-react';
import { Autocomplete } from '@mantine/core';
import { IconGripVertical } from '@tabler/icons-react';
import { 
    Button, Tabs, Select, Group, Title, ActionIcon, Stack, Paper, Grid,
    Modal, TextInput, ColorInput, MultiSelect, Text,Switch,ColorPicker,Table,Menu, 
    NumberInput,
    Textarea,
    Box,
    Radio,
    Divider,
  } from '@mantine/core';
import Icon from '@mdi/react';
import {  mdiRefresh, mdiPlus, mdiCalendarEdit ,mdiStar, mdiStarOutline,mdiDrag ,mdiPuzzlePlus ,mdiPencil, mdiDelete,
  mdiPlaylistPlus,mdiClose
 } from '@mdi/js';
interface AgendaForm {
    name: string;
    color: string;
    description: string;
    services: string[];
  }
interface DefaultSettings {
  physician: string;
  agenda: string;
  entry: {
    reason: string;
    waiting_room: string;
    consultation_room: string;
  };
  payment: {
    type: string;
    bank: string;
  };
}
type PrescriptionConfigValue = string | boolean | {
  formula: string;
  period_prefix: string;
  new_line: string;
};
interface ActesFormValues {
  name: string;
  color: string;
  description: string;
  services: string[];
  useAsResource: boolean;
}

const reasons = [
    "1er Consultation", "Changement D'élastique", "Chirurgie/Paro", "Collage",
    "Composite", "Consultation", "Contention", "Contrôle", "Depose Sutures",
    "Detartrage", "Devis", "Echographie", "Endodontie", "Formation",
    "Implantologie", "Obturation Canalaire", "Orthodontie", "PA ES Chassis/Monta",
    "PA Pose", "PC ESS Armature", "PC Scellement", "PC TP EMP", "Prophylaxie",
    "Urgent", "polissage"
  ].map(value => ({ value, label: value }));
  const banks = [
    "Aucune", "BCP", "BMCI", "BMCE", "AWB", "SGMB", "CDM", "BAM", "ABB", "ABM",
    "BAA", "CDG", "CFG", "CITI", "CIH", "FEC", "MFC", "UMB", "BANCO", "CAIXA",
    "UMNIA", "BTI", "BAY", "ASSAFA", "AAB", "CIB", "UPL", "HLD", "CAT"
  ].map(value => ({ value, label: value }));
  
  const paymentTypes = ["Aucune", "Chèque", "Espèce", "Traite"]
    .map(value => ({ value, label: value }));
    
const defaultPhysicians = [{ value: 'Dr. DEMO DEMO', label: 'Dr. DEMO DEMO' }];
const defaultAgendas = [{ value: 'Cabinet', label: 'Cabinet' }];
const rooms = [
  { value: 'SLT', label: 'SLT' },
  { value: 'FTL', label: 'FTL' }
];
// Add this interface
interface VisitLink {
    key: string;
    title: string;
    is_form?: boolean;
  }
 // Add these interfaces
interface ToolbarLink {
  id: string;
  title: string;
}

interface ToolbarSubmodule {
  id: string;
  name: string;
}
const dosageForms = [
    { value: 'tablet', label: 'Comprimé' },
    { value: 'syrup', label: 'Sirop' },
    { value: 'injection', label: 'Injectable' },
    // Add more dosage forms as needed
  ];
  type RoomType = 'WR' | 'CR';


interface roomFormValues {
  name: string;
  capacity: number;
  color: string;
  description: string;
  services: string[];
  type: RoomType;
}
export default function ParametresDeBase() {
    const [activeTab, setActiveTab] = useState<string>('general');
    const [isAgendaModalOpen, setIsAgendaModalOpen] = useState(false);
    const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
    const [isMotifModalOpen, setIsMotifModalOpen] = useState(false);
    const [isSltModalOpen, setIsSltModalOpen] = useState(false);
    const [ispaiementModalOpen, setIspaiementModalOpen] = useState(false);
    const [isFtlModalOpen, setIsFtlModalOpen] = useState(false);
    const [isBanqueModalOpen, setIsBanqueModalOpen] = useState(false);
    const [isActesModalOpen, setIsActesModalOpen] = useState(false);
    const [isEditeModalOpen, setIsEditeModalOpen] = useState(false);
    
    const [agendaForm, setAgendaForm] = useState<AgendaForm>({
      name: '',
      color: '#000000',
      description: '',
      services: []
    });
  const [settings, setSettings] = useState<DefaultSettings>({
    physician: 'Dr. DEMO DEMO',
    agenda: 'Cabinet',
    entry: {
      reason: 'Consultation',
      waiting_room: 'SLT',
      consultation_room: 'FTL'
    },
    payment: {
      type: 'Espèce',
      bank: 'Aucune'
    }
  });
  const handleSettingChange = (section: keyof DefaultSettings | 'entry' | 'payment', field: string, value: string) => {
    setSettings(prev => {
      if (section === 'entry' || section === 'payment') {
        return {
          ...prev,
          [section]: {
            ...prev[section],
            [field]: value
          }
        };
      }
      return {
        ...prev,
        [section]: value
      };
    });
  };
  const handleAgendaSubmit = () => {
    // Handle form submission here
    setIsAgendaModalOpen(false);
  };
  const handleTabChange = (value: string | null) => {
    if (value) {
        setActiveTab(value);
    }
};
//tabs.panal visit
const [visitLinks, ] = useState<VisitLink[]>([
    { key: 'dental', title: 'Dentaire' },
    { key: 'periodontics', title: 'Parodontie' },

    // ... add other links
    { key: 'Consultation', title: 'Consultation' },
    { key: 'Prescription', title: 'Prescription' },
    { key: 'Comptes rendus', title: 'Comptes rendus' },
    { key: 'Certificats et courriers', title: 'Certificats et courriers' },
    { key: 'État financier', title: 'État financier' },
    { key: 'Plan de soins', title: 'Plan de soins' },
    { key: 'Formulaires', title: 'Formulaires' },
   
  ]);
  const [favoriteLink, setFavoriteLink] = useState<string>('dental');
const [disabledLinks, setDisabledLinks] = useState<Record<string, boolean>>({});
//tabs.panal custom
const [toolbarLinks, ] = useState<ToolbarLink[]>([]);
const [toolbarSubmodules, ] = useState<ToolbarSubmodule[]>([]);
//tabs.panal flow
interface WorkflowView {
    id: string;
    name: string;
    is_system?: boolean;
  }
  
  interface WorkflowConfig {
    default: string;
    disabled: Record<string, boolean>;
  }
const [workflowViews, setWorkflowViews] = useState<WorkflowView[]>([
    { id: '1', name: 'Vue générale' }
  ]);
  const [workflowConfig, setWorkflowConfig] = useState<WorkflowConfig>({
    default: '',
    disabled: {}
  });
const handleWorkflowConfig = (type: 'default' | 'disabled', id?: string) => {
    if (type === 'default') {
      setWorkflowConfig(prev => ({ ...prev, default: prev.default === '1' ? '' : '1' }));
    } else if (type === 'disabled' && id) {
      setWorkflowConfig(prev => ({
        ...prev,
        disabled: {
          ...prev.disabled,
          [id]: !prev.disabled[id]
        }
      }));
    }
  };
  // Add these prescription
  interface PrescriptionConfig {
    default_values: {
      dosage_form: string;
    };
    use_bookmarked_db: boolean;
    advance_mp: boolean;
    stock_integration: boolean;
    arab_generator: {
      is_used: boolean;
      generator: {
        formula: string;
        period_prefix: string;
        new_line: string;
      }
    };
  }
  const [prescriptionConfig, setPrescriptionConfig] = useState<PrescriptionConfig>({
    default_values: { dosage_form: '' },
    use_bookmarked_db: false,
    advance_mp: false,
    stock_integration: false,
    arab_generator: {
      is_used: false,
      generator: {
        formula: '',
        period_prefix: '',
        new_line: ''
      }
    }
  });

  interface BillingConfig {
    defaultTab: string;
    generate_contract_consignment_doc: boolean;
    default_contract_view: string;
  }
  
  interface ContractState {
    id: string;
    name: string;
    color: string;
    label: string;
  }
  // tabs billin
  // Add these states
  const [billingConfig, setBillingConfig] = useState<BillingConfig>({
    defaultTab: '',
    generate_contract_consignment_doc: false,
    default_contract_view: ''
  });
  
  const [contractStates, setContractStates] = useState<ContractState[]>([
    { id: '1', name: 'Future', color: '#e935d1', label: '' },
    { id: '2', name: 'Résilié', color: '#30fb2d', label: '' },
    // ... add other states
    { id: '3', name: 'Désactivé pendant des cycles', color: '#30fb2d', label: 'Désactivé pendant des cycles' },
    { id: '4', name: 'Désactivé', color: '#4527a0', label: 'Désactivé' },
    { id: '5', name: 'Expiré', color: '#30fb2d', label: 'Expiré' },
    { id: '6', name: 'Activé', color: '#30fb2d', label: 'Activé' },

    { id: '7', name: 'Activé', color: '#30fb2d', label: 'Activé' },
    { id: '8', name: 'Brouillon', color: '#4527a0', label: 'Brouillon' },
    { id: '9', name: 'Paiement partiel', color: '#2489a0', label: 'Paiement partiel' },
    { id: '10', name: 'Retard de paiement', color: '#ebc264', label: 'Retard de paiement' },
  ])
  type NestedConfig = {
    [key: string]: string | boolean | NestedConfig;
  };
  const handlePrescriptionConfig = (path: string, value: PrescriptionConfigValue) => {
    setPrescriptionConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current: NestedConfig = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]] as NestedConfig;
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };
  const handleBillingConfig = (key: keyof BillingConfig, value: string | boolean) => {
    setBillingConfig(prev => ({ ...prev, [key]: value }));
  };
  type WorkflowFormValues = {
    name: string;
    agendas: string[];
    columns: {
      label: string;
      disabled: boolean;
    }[];
  };
  const workflowForm = useForm<WorkflowFormValues>({
    initialValues: {
      name: '',
      agendas: [],
      columns: [
        { label: 'Prochain rendez-vous', disabled: false },
        { label: 'RDV', disabled: false },
        { label: 'Patient', disabled: false },
        { label: 'Age', disabled: false },
        { label: 'Sexe', disabled: false },
        { label: 'Organisme', disabled: false },
        { label: 'État', disabled: false },
      ],
    },
    validate: {
      name: (value) => (value.trim() ? null : 'Le titre est requis'),
    },
  });
  const agendasOptions = [
    { value: '1', label: 'Cabinet' },
    { value: '2', label: 'Clinique' },
    // ...
  ];
  
  // function WorkflowForm() {
  //   const handleSubmit = (values: WorkflowFormValues) => {
  //     console.log(values);
  //   };
  // Add these functions for workflow management

  
  const handleRemoveWorkflow = (item: WorkflowView) => {
    if (!item.is_system) {
      setWorkflowViews(prev => prev.filter(view => view.id !== item.id));
    }
  };
  
  // Add these functions for managing links and modules
//   const handleAddVisitLink = (link: VisitLink) => {
//     setVisitLinks(prev => [...prev, link]);
//   };
  
//   const handleAddToolbarModule = (module: ToolbarSubmodule) => {
//     setToolbarLinks(prev => [...prev, { id: module.id, title: module.name }]);
//   };
  
  const handleColorChange = (color: string) => {
    setAgendaForm(prev => ({ ...prev, color }));
  };
  const RoomFormValues = useForm<RoomFormValues>({
    initialValues: {
      name: '',
      capacity: 0,
      color: '',
      description: '',
      services: [],
      type: 'WR',
    },

    validate: {
      name: (value) => (value ? null : 'Nom requis'),
      capacity: (value) => (value >= 0 ? null : 'Capacité invalide'),
      color: (value) => (!value ? 'Couleur requise' : null),
    },
  });

  const [isSubmitting, ] = useState(false);

 

  // ----------------
  interface PaymentFormValues {
    value: string;
    description: string;
  }
  const paymentForm  = useForm<PaymentFormValues>({
    initialValues: {
      value: '',
      description: '',
    },
    validate: {
      value: (v) => (v.trim().length > 0 ? null : 'Valeur requise'),
    },
  });

  const [loading, ] = useState(false);
  const [, setSubmittingPayment] = useState(false);
  const handlePaymentSubmit = (values: PaymentFormValues) => {
    setSubmittingPayment(true);
    console.log('Payment form submitted:', values);
    setTimeout(() => setSubmittingPayment(false), 1000);
  };
  // ------------------------------
  

 
  interface RoomFormValues {
    name: string;
    capacity: number;
    color: string;
    description: string;
    services: string[];
    type: 'WR' | 'CR';
  }
  
  const mockServiceOptions = [
    { label: 'Radiologie', value: 'radiologie' },
    { label: 'Cardiologie', value: 'cardiologie' },
    { label: 'Pédiatrie', value: 'pediatrie' },
  ];

  // ---------------------------
  interface DefaultBankFormValues {
    value: string;
    description: string;
  }
  const BankForm = useForm<DefaultBankFormValues>({
    initialValues: {
      value: '',
      description: '',
    },
    validate: {
      value: (v) => (v.trim().length > 0 ? null : 'La valeur est requise'),
    },
  });
  const [, setSubmittingBank] = useState(false);

const handleBankSubmit = (values: roomFormValues) => {
  setSubmittingBank(true);
  console.log('Room form submitted:', values);
  setTimeout(() => setSubmittingBank(false), 1000);
};
const [, setSubmittingActes] = useState(false);
const handleActesSubmit = (values: ActesFormValues) => {
  setSubmittingActes(true);
  console.log('Room form submitted:', values);
  setTimeout(() => setSubmittingBank(false), 1000);
};
const formActes = useForm<ActesFormValues>({
  initialValues: {
    name: '',
    color: '',
    description: '',
    services: [],
    useAsResource: true
  },
  validate: {
    name: (value) => (value.trim().length > 0 ? null : 'Name is required'),
    color: (value) => (value.trim().length > 0 ? null : 'Color is required'),
  }
});

// Le type de tes valeurs du formulaire
interface SalleFormValues {
  name: string;
}

// Définition du formulaire
const form = useForm<SalleFormValues>({
  initialValues: {
    name: '',
  },
  validate: {
    name: (value) => (value.trim() ? null : 'Le nom est requis'),
  },
});

// Fonction de soumission
const handleSubmit = (values: SalleFormValues) => {
  console.log('Salle à enregistrer:', values);
  // Appelle ici ta logique d’enregistrement
};
  return (
    <Paper p="md" w={"100%"} mb={30}>
      <Tabs value={activeTab} onChange={handleTabChange} >
        <Tabs.List>
          <Tabs.Tab value="general">Général</Tabs.Tab>
          <Tabs.Tab value="visit">Visite</Tabs.Tab>
          <Tabs.Tab value="custom">Module personalisés</Tabs.Tab>
          <Tabs.Tab value="flow">Flux</Tabs.Tab>
          <Tabs.Tab value="prescriptions">Prescriptions</Tabs.Tab>
          <Tabs.Tab value="billing">Facturation</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="general" pt="xl">
          <Grid>
            <Grid.Col span={4}>
              <Select
                label="Médecin par défaut"
                data={defaultPhysicians}
                value={settings.physician}
                onChange={(value) => handleSettingChange('physician', '', value || '')}
                searchable
                maxDropdownHeight={280}
                clearable
                placeholder="Rechercher..."
              />
            </Grid.Col>
            <Grid.Col span={12}>
              <Group grow>
              <Group>
              <Select
                w={"80%"}
                label="Agenda par défaut"
                data={defaultAgendas}
                value={settings.agenda}
                onChange={(value) => handleSettingChange('agenda', '', value || '')}
                searchable
                maxDropdownHeight={280}
                clearable
                placeholder="Rechercher..."
              />
            <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsAgendaModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
            </div>
            </Group>
            <Group>
                <Select
                 label="Salle d'attente"
                 data={rooms}
                 searchable
                 placeholder="Rechercher..."
                 maxDropdownHeight={280}
                 clearable
                 value={settings.entry.waiting_room}
                 onChange={(value) => handleSettingChange('entry', 'waiting_room', value || '')}
                 w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsSltModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
                <Group>
                <Select
                  label="Salle de consultation"
                  data={rooms}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.entry.consultation_room}
                  onChange={(value) => handleSettingChange('entry', 'consultation_room', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsFtlModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
              </Group>
            </Grid.Col>
     
            <Grid.Col span={12}>
              <Group grow>
              <Group>
                <Select
                  label="Motif par défaut"
                  data={reasons}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.entry.reason}
                  onChange={(value) => handleSettingChange('entry', 'reason', value || '')}
                  w={"80%"}
                />
                <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsMotifModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
            </Group>
            <Group>
                <Select
                  label="Mode de paiement par défaut"
                  data={paymentTypes}
                  searchable
            placeholder="Rechercher..."
                  maxDropdownHeight={280}
                  clearable
                  value={settings.payment.type}
                  onChange={(value) => handleSettingChange('payment', 'type', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIspaiementModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
                <Group>
                <Select
                  label="Banque par défaut"
                  data={banks}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.payment.bank}
                  onChange={(value) => handleSettingChange('payment', 'bank', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsBanqueModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
              </Group>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
        <Tabs.Panel value="visit" pt="xl">
      
        <Table striped highlightOnHover withTableBorder withColumnBorders >
          <Table.Thead>
            <Table.Tr>
              <Table.Th style={{ paddingLeft:'20px',textAlign: 'left',width: '84%' }}>Lien</Table.Th>
              <Table.Th style={{ textAlign: 'center' }}>Par défaut</Table.Th>
              <Table.Th>Désactivé</Table.Th>
              <Table.Th></Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {visitLinks.map((link) => (
              <Table.Tr key={link.key}>
                <Table.Td>{link.title}</Table.Td>
                <Table.Td style={{ textAlign: 'center' }}>
                  <ActionIcon
                    variant="subtle"
                    disabled={disabledLinks[link.key] || link.is_form}
                    onClick={() => setFavoriteLink(link.key)}
                  >
                    <Icon 
                      path={favoriteLink === link.key ? mdiStar : mdiStarOutline} 
                      size={1} 
                      color={favoriteLink === link.key ? 'var(--mantine-color-blue-6)' : undefined}
                    />
                  </ActionIcon>
                </Table.Td>
                <Table.Td>
                  <Switch
                    disabled={favoriteLink === link.key}
                    checked={disabledLinks[link.key] || false}
                    onChange={(event) => {
                      setDisabledLinks(prev => ({
                        ...prev,
                        [link.key]: event.currentTarget.checked
                      }));
                    }}
                  />
                </Table.Td>
                <Table.Td>
                  <ActionIcon variant="subtle">
                    <Icon path={mdiDrag} size={1} color="var(--mantine-color-red-6)" />
                  </ActionIcon>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
    
        </Tabs.Panel>
        <Tabs.Panel value="custom" pt="xl">
        <Paper p="md">
          <Table striped>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Lien</Table.Th>
                <Table.Th></Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {toolbarLinks.length === 0 ? (
                <Table.Tr>
                  <Table.Td colSpan={2}>
                    <Text ta="center">Aucun élément trouvé.</Text>
                  </Table.Td>
                </Table.Tr>
              ) : (
                toolbarLinks.map((link) => (
                  <Table.Tr key={link.id}>
                    <Table.Td>{link.title}</Table.Td>
                    <Table.Td></Table.Td>
                  </Table.Tr>
                ))
              )}
            </Table.Tbody>
          </Table>

          <Group justify="flex-start" mt="md">
            <Menu position="bottom-start">
              <Menu.Target>
                <Button 
                  leftSection={<Icon path={mdiPuzzlePlus} size={1} />}
                  disabled={toolbarSubmodules.length === 0}
                >
                  Ajouter un module
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                {toolbarSubmodules.map((module) => (
                  <Menu.Item key={module.id}>
                    {module.name}
                  </Menu.Item>
                ))}
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Paper>
       </Tabs.Panel>
<Tabs.Panel value="flow" pt="xl">
  <Paper p="md">
    <Table striped highlightOnHover withTableBorder withColumnBorders>
      <Table.Thead>
        <Table.Tr>
          <Table.Th style={{ paddingLeft:'20px',textAlign: 'left',width:'84%' }}>Nom de vue</Table.Th>
          <Table.Th style={{ textAlign: 'center' }}>Par défaut</Table.Th>
          <Table.Th style={{ textAlign: 'center' }}>Désactivé</Table.Th>
          <Table.Th></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {workflowViews.map((item) => (
          <Table.Tr key={item.id}>
            <Table.Td>{item.name}</Table.Td>
            <Table.Td style={{ textAlign: 'center' }}>
              <Switch
                checked={workflowConfig.default === '1'}
                onChange={() => handleWorkflowConfig('default')}
              />
            </Table.Td>
            <Table.Td style={{ textAlign: 'center' }}>
              <Switch
                checked={workflowConfig.disabled[item.id] || false}
                onChange={() => handleWorkflowConfig('disabled', item.id)}
              />
            </Table.Td>
            <Table.Td>
              <Group gap="xs">
                <ActionIcon variant="subtle" onClick={() => setIsEditeModalOpen(true)}>
                  <Icon path={mdiPencil} size={1} />
                </ActionIcon>
                <ActionIcon 
                  variant="subtle" 
                  color="red"
                  disabled={item.is_system}
                  onClick={() => handleRemoveWorkflow(item)}
                >
                  <Icon path={mdiDelete} size={1} />
                </ActionIcon>
              </Group>
            </Table.Td>
          </Table.Tr>
        ))}
      </Table.Tbody>
    </Table>
  </Paper>
</Tabs.Panel>
<Tabs.Panel value="prescriptions" pt="xl">
  <Paper p="md">
    <Title order={3}>Prescriptions</Title>
    <Grid>
      <Grid.Col span={4}>
        <Group>
        <Select
          label="Forme galénique - Unité"
          data={dosageForms}
          value={prescriptionConfig.default_values.dosage_form}
          onChange={(value) => handlePrescriptionConfig('default_values.dosage_form', value)}
          w={'80%'}
          searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
        />
          <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsBanqueModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
        </Group>
        <Switch 
          label="Base Medicament par défaut: Favories"
          checked={prescriptionConfig.use_bookmarked_db}
          onChange={(event) => handlePrescriptionConfig('use_bookmarked_db', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />
 <Switch 
          label="Utilisation Avancée (affichage des prix, presentation, ...)"
          checked={prescriptionConfig.use_bookmarked_db}
          onChange={(event) => handlePrescriptionConfig('use_bookmarked_db', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />
        <Switch
          label="Utilisation des produits Paramédicaux & Parapharmaceutiques"
          checked={prescriptionConfig.advance_mp}
          onChange={(event) => handlePrescriptionConfig('advance_mp', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />
      </Grid.Col>
      
      <Grid.Col span={8}>
        <Group grow>
          <TextInput
            label="Generateur de posologie (formule)"
            value={prescriptionConfig.arab_generator.generator.formula}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.formula', event.currentTarget.value)}
          />
          <TextInput
            label="Generateur de posologie (prefix de la periode)"
            value={prescriptionConfig.arab_generator.generator.period_prefix}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.period_prefix', event.currentTarget.value)}
          />
          <TextInput
            label="Generateur de posologie (debut de ligne)"
            value={prescriptionConfig.arab_generator.generator.new_line}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.new_line', event.currentTarget.value)}
          />
        </Group>

        <Switch
          label="Integration avec Pharmacy/Stock"
          checked={prescriptionConfig.stock_integration}
          onChange={(event) => handlePrescriptionConfig('stock_integration', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />

        <Switch
          label="Utilisation de la posologie en Arabe"
          checked={prescriptionConfig.arab_generator.is_used}
          onChange={(event) => handlePrescriptionConfig('arab_generator.is_used', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />
         <Switch
          label="Posologie en Arabe par défaut"
          checked={prescriptionConfig.arab_generator.is_used}
          onChange={(event) => handlePrescriptionConfig('arab_generator.is_used', event.currentTarget.checked)}
          mt="md"
          size="xs"
        />
      </Grid.Col>
      <Divider my="sm"size="lg" variant="dashed" color='red' />
    </Grid>
  </Paper>
</Tabs.Panel>
<Tabs.Panel value="billing" pt="xl">
  <Paper p="md">
    <Stack>
      <Select
        label="Onglet par défaut"
        data={[
          { value: 'invoice_list', label: 'Liste des factures' },
          { value: 'contract_list', label: 'Liste des contrats' },
          { value: 'billing_workflow', label: 'Flux de faturation' }
        ]}
        value={billingConfig.defaultTab}
        onChange={(value) => handleBillingConfig('defaultTab', value || '')}
      />

      <Switch
        label="Générer un bon de consignation à partir du contrat"
        checked={billingConfig.generate_contract_consignment_doc}
        onChange={(event) => handleBillingConfig('generate_contract_consignment_doc', event.currentTarget.checked)}
      />

      <Select
        label="Affichage par défaut"
        data={[
          { value: 'flattened_list', label: 'Aplatie' },
          { value: 'contract_default_list', label: 'Par défaut' }
        ]}
        value={billingConfig.default_contract_view}
        onChange={(value) => handleBillingConfig('default_contract_view', value || '')}
      />

      <Title order={3}>États des contracts</Title>
      
      <Table striped highlightOnHover withTableBorder withColumnBorders>
        <Table.Thead>
          <Table.Tr>
            <Table.Th style={{ paddingLeft:'20px',textAlign: 'left',width:'80%' }} >État</Table.Th>
            <Table.Th style={{ textAlign: 'center' }}>Couleur</Table.Th>
            <Table.Th style={{ textAlign: 'center' }}>Motif</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {contractStates.map((state) => (
            <Table.Tr key={state.id}>
              <Table.Td>{state.name}</Table.Td>
              <Table.Td>
              <Group>
                <ColorInput
                  value={state.color}
                  onChange={(color) => {
                    setContractStates(prev => 
                      prev.map(s => s.id === state.id ? { ...s, color } : s)
                    );
                  }}
                  withPicker
                  swatches={[
                    '#25262b', '#868e96', '#fa5252', '#e64980', '#be4bdb',
                    '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886'
                  ]}
                  w={"77%"}
                />
                  
            <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                onClick={() => setIsColorPickerModalOpen(true)}>
                <IconPalette stroke={2} />
            </ActionIcon>
            </Group>
              </Table.Td>
              <Table.Td>
                <TextInput
                  value={state.label}
                  onChange={(event) => {
                    setContractStates(prev => 
                      prev.map(s => s.id === state.id ? { ...s, label: event.currentTarget.value } : s)
                    );
                  }}
                />
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </Stack>
  </Paper>
</Tabs.Panel>
      </Tabs>
      <Modal
        opened={isAgendaModalOpen}
        onClose={() => setIsAgendaModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiCalendarEdit} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      >
        <form onSubmit={(e) => { e.preventDefault(); handleAgendaSubmit(); }}>
          <Stack  gap="md">
            <TextInput
              required
              label="Nom"
              value={agendaForm.name}
              onChange={(e) => setAgendaForm({ ...agendaForm, name: e.target.value })}
            />
           <Group>
            <ColorInput
                label="Couleur"
                value={agendaForm.color}
                onChange={(color) => setAgendaForm({ ...agendaForm, color })}
                w={"80%"}
            />
            <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                onClick={() => setIsColorPickerModalOpen(true)}>
                <IconPalette stroke={2} />
            </ActionIcon>
        </Group>
      
            <TextInput
              label="Description"
              value={agendaForm.description}
              onChange={(e) => setAgendaForm({ ...agendaForm, description: e.target.value })}
            />
            <MultiSelect
              label="Services désignés"
              data={[]} // Add your services data here
              value={agendaForm.services}
              onChange={(values) => setAgendaForm({ ...agendaForm, services: values })}
              searchable
              clearable
            />
             <Group justify="flex-start">
            <Switch
                defaultChecked
                label="Afficher comme ressource"
                size="xs"
                />
            </Group>
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsAgendaModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
      <Modal  
            opened={isColorPickerModalOpen}
            onClose={() => setIsColorPickerModalOpen(false)} 
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            withCloseButton={false}
            centered
        >
            <ColorPicker 
                format="hex" 
                value={agendaForm.color}
                onChange={handleColorChange}
                swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
            />
      </Modal>  
      <Modal opened={isMotifModalOpen}
        onClose={() => setIsMotifModalOpen(false)} withCloseButton={false}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlus} size={1} />
            <Text>Actes</Text>
          </Group>
        }
        >
        <form onSubmit={(e) => { e.preventDefault(); /* handle submit */ }}>
          <Stack gap="md">
            <Group grow>
              <TextInput
                required
                label="Code"
                maxLength={3}
                placeholder="Enter code"
                w="30%"
              />
              <TextInput
                required
                label="Déscription"
                placeholder="Enter description"
              />
            </Group>

            <Group grow>
              <TextInput
                type="number"
                label="Durée (min)"
                required
                min={0}
                w="30%"
                placeholder="Enter duration"
              />
              <ColorInput
                label="Couleur"
                placeholder="Pick a color"
              />
              <ColorInput
                label="Couleur rayée"
                placeholder="Pick a color"
              />
            </Group>

            <Divider my="sm" />
            <Group>
            <Select
              label="Agenda par défaut"
              data={defaultAgendas}
              placeholder="Select agenda"
            />
 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsActesModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
            </div>
            </Group>
            <Group justify="flex-end" mt="xl">
              <Button type="submit">
                Sauvegarder
              </Button>
              <Button 
                color="red" 
                onClick={() => setIsMotifModalOpen(false)}
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
      <Modal
      opened={isActesModalOpen}
      onClose={() => setIsActesModalOpen(false)}
      centered
      title={
        <Group gap="xs">
          <Icon path={mdiCalendarEdit} size={1} />
          <span>Agenda</span>
        </Group>
      }
      size="lg"
    >
      <form onSubmit={formActes.onSubmit(handleActesSubmit)}>
        <Stack gap="md">
          <Group grow>
            <TextInput
              label="Nom"
              required
              {...formActes.getInputProps('name')}
            />
            <ColorInput
              label="Couleur"
              placeholder="Choisir une couleur"
              {...formActes.getInputProps('color')}
            />
          </Group>

          <Group grow>
            <TextInput
              label="Description"
              {...formActes.getInputProps('description')}
            />
            <MultiSelect
              label="Services désignés"
              placeholder="Rechercher..."
              searchable
              data={[
                { value: 'service1', label: 'Service 1' },
                { value: 'service2', label: 'Service 2' },
                { value: 'service3', label: 'Service 3' },
              ]}
              {...formActes.getInputProps('services')}
            />
          </Group>

          <Switch
            label="Afficher comme ressource"
            {...formActes.getInputProps('useAsResource', { type: 'checkbox' })}
          />

          <Group justify='flex-end' mt="md">
            <Button variant="default" onClick={() => setIsActesModalOpen(false)}>
              Annuler
            </Button>
            <Button type="submit">Enregistrer</Button>
          </Group>
        </Stack>
      </form>
    </Modal>
      <Modal opened={isSltModalOpen}
        onClose={() => setIsSltModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered>
         <Box component="form" onSubmit={form.onSubmit(handleSubmit)} maw={600} mx="auto">
      <Group mb="md" align="center" justify='center'>
        <Group>
          <Icon path={mdiPlaylistPlus} size={1} />
          <Title order={2}>Ajout du salle</Title>
        </Group>
        <Button variant="subtle" size="sm" onClick={() => setIsSltModalOpen(false)}>
          <Icon path={mdiClose} size={1} />
        </Button>
      </Group>

      <Stack>
        <TextInput
          label="Nom"
          required
          {...form.getInputProps('name')}
        />

        <NumberInput
          label="Capacité"
          required
          min={0}
          {...form.getInputProps('capacity')}
        />

        <ColorInput
          label="Couleur"
          required
          {...form.getInputProps('color')}
        />

        <Textarea
          label="Description"
          {...form.getInputProps('description')}
        />

        <Select
          label="Services désignés"
          data={[
            { value: 'service1', label: 'Service 1' },
            { value: 'service2', label: 'Service 2' },
            // Remplace par fetch API
          ]}
          multiple
          searchable
          placeholder="Rechercher..."
          {...form.getInputProps('services')}
        />

        <Radio.Group
          name="type"
          label="Type"
          required
          {...form.getInputProps('type')}
        >
          <Group mt="xs">
            <Radio value="WR" label="Salle d'attente" />
            <Radio value="CR" label="Salle de consultation" />
          </Group>
        </Radio.Group>
      </Stack>

      <Group mt="xl" justify='center'>
        <Button color="red" variant="outline" onClick={() => setIsSltModalOpen(false)} >
          Annuler
        </Button>
        <Button type="submit" loading={isSubmitting}>
          Enregistrer
        </Button>
      </Group>
    </Box>
     
      </Modal>
      <Modal opened={ispaiementModalOpen}
        onClose={() => setIspaiementModalOpen(false)} withCloseButton={false} transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered>
        <Box component="form" onSubmit={paymentForm.onSubmit(handlePaymentSubmit)} maw={500} mx="auto">
      <Group mb="md" align="center" justify='center'>
        <Group>
          <Icon path={mdiPlaylistPlus} size={1} />
          <Title order={2}>Ajouter mode de paiement par défaut</Title>
        </Group>
        <Button variant="subtle" size="sm" onClick={() => setIspaiementModalOpen(false)}>
          <Icon path={mdiClose} size={1} />
        </Button>
      </Group>

      <Stack>
        <TextInput
          label="Valeur"
          required
          withAsterisk
          {...paymentForm.getInputProps('value')}
        />

        <TextInput
          label="Description"
          {...paymentForm.getInputProps('description')}
        />
      </Stack>

      <Group mt="xl" justify="flex-end">
        <Button variant="outline" color="gray" onClick={() => setIspaiementModalOpen(false)}>
          Annuler
        </Button>
        <Button type="submit" loading={loading}>
          Enregistrer
        </Button>
      </Group>
    </Box>
        <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIspaiementModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
      </Modal>
      <Modal opened={isBanqueModalOpen}
        onClose={() => setIsBanqueModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
        title={
          <Group gap="xs">
            <Icon path={mdiPlaylistPlus} size={1} />
            <span>Ajouter Banque par défaut</span>
          </Group>
        }
        >
       <form onSubmit={BankForm.onSubmit(handleBankSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Valeur"
            required
            placeholder="Valeur"
            {...BankForm.getInputProps('value')}
          />

          <TextInput
            label="Description"
            placeholder="Description (optionnelle)"
            {...BankForm.getInputProps('description')}
          />

<Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsBanqueModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
        </Stack>
      </form>
       
      </Modal>
      <Modal opened={isFtlModalOpen}
        onClose={() => setIsFtlModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
        title={
          <Group gap="xs">
            <Icon path={mdiPlaylistPlus} size={1} />
            <span>Ajout du salle</span>
          </Group>
        }
        >
       <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Nom"
            placeholder="Nom de la salle"
            required
            {...form.getInputProps('name')}
          />

          <NumberInput
            label="Capacité"
            required
            min={0}
            {...form.getInputProps('capacity')}
          />

          <ColorInput
            label="Couleur"
            placeholder="Choisir une couleur"
            {...form.getInputProps('color')}
          />

          <TextInput
            label="Description"
            {...form.getInputProps('description')}
          />

          <Select
            label="Services désignés"
            placeholder="Sélectionner un ou plusieurs services"
            data={mockServiceOptions}
            multiple
            searchable
            {...form.getInputProps('services')}
          />

          <Radio.Group
            label="Type"
            {...form.getInputProps('type')}
            required
          >
            <Group>
              <Radio value="WR" label="Salle d'attente" />
              <Radio value="CR" label="Salle de consultation" />
            </Group>
          </Radio.Group>

          <Group justify="flex-end">
              <Button variant="filled" type="submit">
              Enregistrer
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsFtlModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
        </Stack>
      </form>
       
      </Modal>
      <Modal
        opened={isEditeModalOpen}
        onClose={() => setIsEditeModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiCalendarEdit} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      >
       <form onSubmit={workflowForm.onSubmit(handleSubmit)}>
      <Stack gap="md">
        <Group grow>
          <TextInput
            label="Titre"
            required
            {...workflowForm.getInputProps('name')}
          />
          <MultiSelect
            label="Filtrer par agenda"
            data={agendasOptions}
            {...workflowForm.getInputProps('agendas')}
          />
        </Group>

        <Paper withBorder p="md" radius="md">
          <Table striped>
            <thead>
              <tr>
                <th>Titre</th>
                <th>Désactivé</th>
                <th>Déplacer</th>
              </tr>
            </thead>
            <tbody>
              {workflowForm.values.columns.map((column, index) => (
                <tr key={index}>
                  <td>{column.label}</td>
                  <td>
                    <Switch
                      checked={column.disabled}
                      onChange={(e) =>
                        workflowForm.setFieldValue(`columns.${index}.disabled`, e.currentTarget.checked)
                      }
                    />
                  </td>
                  <td>
                    <Button variant="light" size="xs">
                      <IconGripVertical size={16} />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Paper>
      </Stack>
    </form>
      </Modal>
    </Paper>
  );
}