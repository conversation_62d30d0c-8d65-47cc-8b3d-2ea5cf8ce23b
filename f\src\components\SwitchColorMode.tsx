"use client";

import { useTranslation } from '~/i18n/client';
import { useState, useCallback } from 'react';
import {
  useMantineColorScheme,
  Menu,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import Icon from '@mdi/react';
import { mdiWhiteBalanceSunny ,mdiMoonWaningCrescent,mdiCircleHalfFull} from '@mdi/js';
const SwitchColorMode = () => {
  const { t } = useTranslation('menu');
  const { colorScheme, setColorScheme } = useMantineColorScheme();
  const [isChanging, setIsChanging] = useState(false);

  const handleColorSchemeChange = useCallback(async (newScheme: 'light' | 'dark' | 'auto') => {
    if (isChanging) return; // Prevent multiple rapid clicks

    setIsChanging(true);
    try {
      setColorScheme(newScheme);
      // Save to localStorage for persistence
      localStorage.setItem('theme', newScheme);
    } finally {
      // Reset after a short delay
      setTimeout(() => setIsChanging(false), 300);
    }
  }, [setColorScheme, isChanging]);
 

  return (
    <>
      <Menu shadow="lg" width={200} zIndex={1000010}>
        <Menu.Target>
          <Tooltip withArrow style={{color:"var(--mantine-color-text)"}} label={t("Switch-color-modes")} className={" px-[10px] py-[6px] hover:bg-[var(--mantine-bg-hover)] rounded-md text-[var(--tooltip-text)] bg-[var(--tooltip-bg)]"}>
          {/* Switch color modes */}
            <ActionIcon
             variant="light"          
             className="h-10 w-10 bg-[var(--bg-SwitchColor)] text-[var(--mantine-color-dark-0)] focus:outline-none hover:bg-[var(---mantinelight-hover)] mb-4"
                          >
              {colorScheme === "auto" ? (
                <Icon path={mdiCircleHalfFull} size={1} />
              ) : colorScheme === "dark" ? (
                <Icon path={mdiMoonWaningCrescent}  size={1} />
              ) : (
                <Icon path={mdiWhiteBalanceSunny}  size={1} />
              )}
            </ActionIcon>
          </Tooltip>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label tt="uppercase" ta="center" fw={600}>
            {/* Select color modes */}
            {/* Sélectionner les modes de couleur */}
            {t('color-modes')}
          </Menu.Label>
          <Menu.Item
            leftSection={<Icon path={mdiWhiteBalanceSunny}  size={1} />}
            onClick={() => handleColorSchemeChange("light")}
          >
            {/* Light */}
            {/* Lumière */}
            {t('Light-modes')}
          </Menu.Item>
          <Menu.Item
            leftSection={<Icon path={mdiMoonWaningCrescent}  size={1} />}
            onClick={() => handleColorSchemeChange("dark")}
          >
            {t('Dark-modes')}
            {/* Dark */}
            {/* Sombre */}
          </Menu.Item>
          <Menu.Item
            leftSection={<Icon path={mdiCircleHalfFull} size={1} />}
            onClick={() => handleColorSchemeChange("auto")}
          >
            Auto
            {/* Auto */}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </>
  );
};

export default SwitchColorMode;