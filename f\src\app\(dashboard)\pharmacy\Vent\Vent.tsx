import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import BonL<PERSON>raison from'./BonLivraison'
import BonConsignation from'./BonConsignation'
import BonRetourConsignation from'./BonRetourConsignation'

// Mapping des sous-onglets pour Vente
const subtabMapping: { [key: string]: string } = {
  'bons-livraison': 'BonLivraison',
  'bons-consignation': 'BonConsignation',
  'retours-consignation': 'BonRetourConsignation'
};

const Vent = () => {
  const [activeTab, setActiveTab] = useState('BonLivraison');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'BonLivraison')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="BonLivraison" leftSection={<IconPhoto size={12} />}>
             Bon Livraison
           </Tabs.Tab>
           <Tabs.Tab value="BonConsignation" leftSection={<IconMessageCircle size={12} />}>
             Bon Consignation
           </Tabs.Tab>
           <Tabs.Tab value="BonRetourConsignation" leftSection={<IconSettings size={12} />}>
             Retour Consignation
           </Tabs.Tab>
         </Tabs.List>
   
         <Tabs.Panel value="BonLivraison" ml={20}>
           <BonLivraison/>
         </Tabs.Panel>
   
         <Tabs.Panel value="BonConsignation" ml={20}>
           <BonConsignation/>
         </Tabs.Panel>
   
         <Tabs.Panel value="BonRetourConsignation" ml={20}>
           <BonRetourConsignation/>
         </Tabs.Panel>
       </Tabs>
  )
}

export default Vent
