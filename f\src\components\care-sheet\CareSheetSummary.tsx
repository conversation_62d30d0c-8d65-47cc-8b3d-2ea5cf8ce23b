/**
 * Care Sheet Summary Component
 * Displays a summary of patient's care-sheet information including:
 * - <PERSON><PERSON><PERSON> (Insurance)
 * - Recent visits
 * - Procedures
 * - Pending devis
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  ActionIcon,
  Tooltip,
  Divider,
  <PERSON><PERSON>,
  Loader,
  <PERSON>ert,
} from '@mantine/core';
import {
  IconFileText,
  IconCalendarEvent,
  IconMedicalCross,
  IconCurrencyEuro,
  IconEye,
  IconEdit,
  IconRefresh,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useCareSheet } from '@/hooks/useCareSheet';

interface CareSheetSummaryProps {
  patientId: string;
  patientName?: string;
  compact?: boolean;
  showActions?: boolean;
  onViewDetails?: (section: 'mutuelles' | 'visits' | 'procedures' | 'devis') => void;
  onEditMutuelle?: (mutuelleId: number) => void;
  onRefresh?: () => void;
}

const CareSheetSummary: React.FC<CareSheetSummaryProps> = ({
  patientId,
  patientName,
  compact = false,
  showActions = true,
  onViewDetails,
  onEditMutuelle,
  onRefresh,
}) => {
  const {
    mutuelles,
    visits,
    procedures,
    devis,
    loading,
    error,
    refreshAll,
    getPatientCareSheetStats,
  } = useCareSheet({ patientId, autoFetch: true });

  const stats = getPatientCareSheetStats(patientId);

  const handleRefresh = () => {
    refreshAll(patientId);
    onRefresh?.();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'validee':
      case 'terminee':
      case 'accepte':
        return 'green';
      case 'en-attente':
      case 'planifiee':
      case 'envoye':
        return 'yellow';
      case 'non-validee':
      case 'annulee':
      case 'refuse':
        return 'red';
      case 'en-cours':
        return 'blue';
      default:
        return 'gray';
    }
  };

  if (loading) {
    return (
      <Card padding="md" radius="md" withBorder>
        <Group justify="center" p="xl">
          <Loader size="sm" />
          <Text size="sm" c="dimmed">Chargement des données de soins...</Text>
        </Group>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Group justify="space-between">
          <Text size="sm">{error}</Text>
          <Button size="xs" variant="light" onClick={handleRefresh}>
            Réessayer
          </Button>
        </Group>
      </Alert>
    );
  }

  return (
    <Card padding="md" radius="md" withBorder>
      <Group justify="space-between" mb="md">
        <Group gap="xs">
          <IconFileText size={20} />
          <Text fw={600} size="sm">
            Dossier de Soins {patientName && `- ${patientName}`}
          </Text>
        </Group>
        {showActions && (
          <Group gap="xs">
            <Tooltip label="Actualiser">
              <ActionIcon variant="light" size="sm" onClick={handleRefresh}>
                <IconRefresh size={14} />
              </ActionIcon>
            </Tooltip>
          </Group>
        )}
      </Group>

      {compact ? (
        // Compact view - just stats
        <Grid gutter="xs">
          <Grid.Col span={3}>
            <Group gap="xs">
              <IconCurrencyEuro size={16} color="blue" />
              <Text size="xs" c="dimmed">Mutuelles:</Text>
              <Badge size="xs" color="blue">{stats.totalMutuelles}</Badge>
            </Group>
          </Grid.Col>
          <Grid.Col span={3}>
            <Group gap="xs">
              <IconCalendarEvent size={16} color="green" />
              <Text size="xs" c="dimmed">Visites:</Text>
              <Badge size="xs" color="green">{stats.totalVisits}</Badge>
            </Group>
          </Grid.Col>
          <Grid.Col span={3}>
            <Group gap="xs">
              <IconMedicalCross size={16} color="orange" />
              <Text size="xs" c="dimmed">Procédures:</Text>
              <Badge size="xs" color="orange">{stats.totalProcedures}</Badge>
            </Group>
          </Grid.Col>
          <Grid.Col span={3}>
            <Group gap="xs">
              <IconFileText size={16} color="purple" />
              <Text size="xs" c="dimmed">Devis:</Text>
              <Badge size="xs" color="purple">{stats.totalDevis}</Badge>
            </Group>
          </Grid.Col>
        </Grid>
      ) : (
        // Full view
        <Stack gap="md">
          {/* Mutuelles Section */}
          <div>
            <Group justify="space-between" mb="xs">
              <Group gap="xs">
                <IconCurrencyEuro size={16} color="blue" />
                <Text size="sm" fw={500}>Mutuelles</Text>
                <Badge size="sm" color="blue">{mutuelles.length}</Badge>
              </Group>
              {showActions && onViewDetails && (
                <ActionIcon 
                  variant="light" 
                  size="sm" 
                  onClick={() => onViewDetails('mutuelles')}
                >
                  <IconEye size={14} />
                </ActionIcon>
              )}
            </Group>
            {mutuelles.length > 0 ? (
              <Stack gap="xs">
                {mutuelles.slice(0, 2).map((mutuelle) => (
                  <Group key={mutuelle.id} justify="space-between" p="xs" bg="gray.0" radius="sm">
                    <div>
                      <Text size="xs" fw={500}>{mutuelle.organisme}</Text>
                      <Text size="xs" c="dimmed">{mutuelle.numeroAffiliate}</Text>
                    </div>
                    <Group gap="xs">
                      <Badge size="xs" color={getStatusColor(mutuelle.etat)}>
                        {mutuelle.etat}
                      </Badge>
                      <Text size="xs" fw={500}>{mutuelle.montant}€</Text>
                      {showActions && onEditMutuelle && (
                        <ActionIcon 
                          variant="subtle" 
                          size="xs"
                          onClick={() => onEditMutuelle(mutuelle.id)}
                        >
                          <IconEdit size={12} />
                        </ActionIcon>
                      )}
                    </Group>
                  </Group>
                ))}
                {mutuelles.length > 2 && (
                  <Text size="xs" c="dimmed" ta="center">
                    +{mutuelles.length - 2} autres mutuelles
                  </Text>
                )}
              </Stack>
            ) : (
              <Text size="xs" c="dimmed" ta="center" p="xs">
                Aucune mutuelle enregistrée
              </Text>
            )}
          </div>

          <Divider />

          {/* Recent Visits Section */}
          <div>
            <Group justify="space-between" mb="xs">
              <Group gap="xs">
                <IconCalendarEvent size={16} color="green" />
                <Text size="sm" fw={500}>Visites Récentes</Text>
                <Badge size="sm" color="green">{visits.length}</Badge>
              </Group>
              {showActions && onViewDetails && (
                <ActionIcon 
                  variant="light" 
                  size="sm" 
                  onClick={() => onViewDetails('visits')}
                >
                  <IconEye size={14} />
                </ActionIcon>
              )}
            </Group>
            {visits.length > 0 ? (
              <Stack gap="xs">
                {visits.slice(0, 2).map((visit) => (
                  <Group key={visit.id} justify="space-between" p="xs" bg="green.0" radius="sm">
                    <div>
                      <Text size="xs" fw={500}>{visit.typeVisite}</Text>
                      <Text size="xs" c="dimmed">{new Date(visit.date).toLocaleDateString()}</Text>
                    </div>
                    <Group gap="xs">
                      <Badge size="xs" color={getStatusColor(visit.statut)}>
                        {visit.statut}
                      </Badge>
                      <Text size="xs" c="dimmed">{visit.docteur}</Text>
                    </Group>
                  </Group>
                ))}
                {visits.length > 2 && (
                  <Text size="xs" c="dimmed" ta="center">
                    +{visits.length - 2} autres visites
                  </Text>
                )}
              </Stack>
            ) : (
              <Text size="xs" c="dimmed" ta="center" p="xs">
                Aucune visite enregistrée
              </Text>
            )}
          </div>

          <Divider />

          {/* Quick Stats */}
          <Grid gutter="xs">
            <Grid.Col span={6}>
              <Group gap="xs">
                <IconMedicalCross size={14} color="orange" />
                <Text size="xs" c="dimmed">Procédures:</Text>
                <Badge size="xs" color="orange">{stats.totalProcedures}</Badge>
              </Group>
            </Grid.Col>
            <Grid.Col span={6}>
              <Group gap="xs">
                <IconFileText size={14} color="purple" />
                <Text size="xs" c="dimmed">Devis:</Text>
                <Badge size="xs" color="purple">{stats.totalDevis}</Badge>
              </Group>
            </Grid.Col>
          </Grid>

          {(stats.lastVisitDate || stats.nextAppointment) && (
            <>
              <Divider />
              <Grid gutter="xs">
                {stats.lastVisitDate && (
                  <Grid.Col span={6}>
                    <Text size="xs" c="dimmed">Dernière visite:</Text>
                    <Text size="xs" fw={500}>
                      {new Date(stats.lastVisitDate).toLocaleDateString()}
                    </Text>
                  </Grid.Col>
                )}
                {stats.nextAppointment && (
                  <Grid.Col span={6}>
                    <Text size="xs" c="dimmed">Prochain RDV:</Text>
                    <Text size="xs" fw={500}>
                      {new Date(stats.nextAppointment).toLocaleDateString()}
                    </Text>
                  </Grid.Col>
                )}
              </Grid>
            </>
          )}
        </Stack>
      )}
    </Card>
  );
};

export default CareSheetSummary;
