
"use client";
import { useState, useEffect } from "react";
import React from "react";


import { useSearchParams } from "next/navigation";
import MetaSeo from"./MetaSeo"

import Icon from '@mdi/react';
import { mdiCalendarOutline } from '@mdi/js';
import PharmacyPage from"./PharmacyPage"
import Inventaire from"./Inventaire/Inventaire"
import Pharmacie from"./Pharmacie/Pharmacie"
import Achat from"./Achat/Achat"
import Vent from"./Vent/Vent"

import OutilsDeMaintenance from"./OutilsDeMaintenance/OutilsDeMaintenance"

// import FicheArticle from"./FicheArticle"
// import Bon from"./Bon"
// import Facture from"./Facture"
// import Inventaire from"./Inventaire"
// import ListeDepots from"./ListeDepots"
// import Mouvement from"./Mouvement"
// import RecalculeDeStock from"./RecalculeDeStock"
// import Reglement from"./Reglement"

// import Traification from"./Traification"
// import TransformationN from"./TransformationN"

import "~/styles/tab.css";

// Mapping des paramètres URL vers les numéros d'onglets
const tabMapping: { [key: string]: number } = {
  'pharmacy': 1,
  'inventaire': 2,
  'pharmacie': 3,
  'achat': 4,
  'vente': 5,
  'maintenance': 6
};

function  AppointmentsPage() {
 
  const [toggleState, setToggleState] = useState(1);
  const searchParams = useSearchParams();

  // Effet pour lire les paramètres d'URL et définir l'onglet actif
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);
 

const icons = [
  { icon: <Icon path={mdiCalendarOutline} size={1}key="PharmacyPage" />, label: "Pharmacy" },
  { icon: <Icon path={mdiCalendarOutline} size={1}key="Inventaire" />, label: "Inventaire" },
  { icon: <Icon path={mdiCalendarOutline} size={1} key="Pharmacie" />, label: "Pharmacie" },
  { icon: <Icon path={mdiCalendarOutline} size={1} key="Achat" />, label: "Achat" },
  { icon: <Icon path={mdiCalendarOutline} size={1} key="Vent" />, label: "Vent" },
  { icon: <Icon path={mdiCalendarOutline} size={1} key="OutilsDeMaintenance" />, label: "OutilsDeMaintenance" },

    // { icon: <CalendarDays style={iconStyle} key="Bon" />, label: "Bon" },
  //   {
  //   icon: <CalendarDays style={iconStyle} key="Facture" />,
  //   label: "Facture",
  // },
  //  {
  //   icon: <CalendarDays style={iconStyle} key="Inventaire" />,
  //   label: "Inventaire",
  // },
  // {
  //   icon: <CalendarDays style={iconStyle} key="Bon" />,
  //   label: "Bon ",
  // },
  //   {
  //   icon: <CalendarDays style={iconStyle} key="FicheArticle" />,
  //   label: "FicheArticle"
  // },
  // {
  //   icon: <CalendarDays style={iconStyle} key="ListeDepots" />,
  //   label: "Liste Depots",
  // },
  //  {
  //   icon: <CalendarDays style={iconStyle} key="Mouvement" />,
  //   label: "Mouvement",
  // },
  //  {
  //   icon: <CalendarDays style={iconStyle} key="RecalculeDeStock" />,
  //   label: "RecalculeDeStock",
  // },
  //   {
  //   icon: <CalendarDays style={iconStyle} key="Reglement" />,
  //   label: "Reglement",
  // },
  //   {
  //   icon: <CalendarDays style={iconStyle} key="Traification" />,
  //   label: "Traification",
  // },
  //   {
  //   icon: <CalendarDays style={iconStyle} key="TransformationN" />,
  //   label: "TransformationN",
  // },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<PharmacyPage/> )
    case 2:
      return (<Inventaire/> )
    case 3:
      return (<Pharmacie/> )
    case 4:
      return (<Achat/> )
    case 5:
      return (<Vent/> )
    case 6:
      return (<OutilsDeMaintenance/> )

 
  //  case 7:
  //     return (<Bon/> )
    //   return (  <Facture/>)
  
    // case 3:
    //       return (<Inventaire/>);
    // case 4:
    //       return (<Bon/>);
    // case 5:
    //        return (<FicheArticle/>);
    // case 6:
    //        return (<ListeDepots/>);
    // case 7:
    //        return (<Mouvement/>);
    // case 8:
    //        return (<RecalculeDeStock/>);
    // case 9:
    //        return (<Reglement/>);
    // case 10:
    //        return (<Traification/>);
    // case 11:
    //        return (<TransformationN/>);
    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 