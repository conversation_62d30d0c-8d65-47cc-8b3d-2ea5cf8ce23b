'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  Textarea,
  Table,
  Modal,
  Tabs,
  Radio,
  Pagination,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconList,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  IconX,
  IconPackage,
  IconCurrencyEuro,
  IconEdit,
} from '@tabler/icons-react';

// Interface pour les articles du bon de réception
interface ArticleReception {
  id: string;
  nBC: string;
  nDP: string;
  code: string;
  designation: string;
  quantite: number;
  prix: number;
  tva: number;
  depot: string;
  montant: number;
}

// Interface pour le bon de réception
interface BonReception {
  numero: string;
  date: Date | null;
  modePrix: 'HT' | 'TTC';
  fournisseur: string;
  numeroDocument: string;
  affaire: string;
  commentaire: string;
  articles: ArticleReception[];
}

const Bon_de_reception = () => {
  // États pour la gestion du formulaire et de l'interface
  const [activeTab, setActiveTab] = useState<string | null>('details');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [articles, setArticles] = useState<ArticleReception[]>([]);

  // Données de référence
  const fournisseurs = [
    'Fournisseur A',
    'Fournisseur B',
    'Fournisseur C',
    'Fournisseur D',
  ];

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
  ];

  // Formulaire principal
  const form = useForm<BonReception>({
    initialValues: {
      numero: '9',
      date: new Date(),
      modePrix: 'HT',
      fournisseur: '',
      numeroDocument: '',
      affaire: '',
      commentaire: '',
      articles: [],
    },
  });

  // Formulaire pour ajouter un article
  const itemForm = useForm({
    initialValues: {
      nBC: '',
      nDP: '',
      code: '',
      designation: '',
      quantite: 1,
      prix: 0,
      tva: 20,
      depot: '',
    },
  });

  // Fonction pour ajouter un article
  const addItem = (values: typeof itemForm.values) => {
    const montant = values.quantite * values.prix;
    const newArticle: ArticleReception = {
      id: Date.now().toString(),
      nBC: values.nBC,
      nDP: values.nDP,
      code: values.code,
      designation: values.designation,
      quantite: values.quantite,
      prix: values.prix,
      tva: values.tva,
      depot: values.depot,
      montant: montant,
    };

    setArticles(prev => [...prev, newArticle]);
    itemForm.reset();
    close();

    notifications.show({
      title: 'Article ajouté',
      message: 'L\'article a été ajouté au bon de réception.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  // Fonction pour supprimer un article
  const removeItem = (id: string) => {
    setArticles(prev => prev.filter(item => item.id !== id));
    notifications.show({
      title: 'Article supprimé',
      message: 'L\'article a été supprimé du bon de réception.',
      color: 'red',
    });
  };

  // Calculs des totaux
  const montantHT = articles.reduce((sum, article) => sum + article.montant, 0);
  const montantTVA = articles.reduce((sum, article) => sum + (article.montant * article.tva / 100), 0);
  const totalCharges = 0; // À implémenter selon les besoins
  const montantTTC = montantHT + montantTVA + totalCharges;

  // Gestionnaires d'événements
  const handleSave = () => {
    notifications.show({
      title: 'Bon de réception enregistré',
      message: 'Le bon de réception a été enregistré avec succès.',
      color: 'green',
      icon: <IconDeviceFloppy size={16} />,
    });
  };

  const handleValidate = () => {
    notifications.show({
      title: 'Bon de réception validé',
      message: 'Le bon de réception a été validé avec succès.',
      color: 'blue',
      icon: <IconCheck size={16} />,
    });
  };

  const handleSaveAndExit = () => {
    notifications.show({
      title: 'Bon de réception enregistré',
      message: 'Le bon de réception a été enregistré et fermé.',
      color: 'green',
      icon: <IconDeviceFloppy size={16} />,
    });
  };

  const handleCancel = () => {
    notifications.show({
      title: 'Bon de réception annulé',
      message: 'Les modifications ont été annulées.',
      color: 'red',
      icon: <IconX size={16} />,
    });
  };

  return (
    <Paper p="xl" radius="md" withBorder w="100%">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group align="center">
          <IconPackage size={24} color="blue" />
          <Title order={3} c="blue">
            Bon de réception N°: {form.values.numero}
          </Title>
        </Group>
        <Group>
          <Button leftSection={<IconList size={16} />} variant="outline">
            Bons de réception
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Réception"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
                disabled
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="16/09/2022"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Text size="sm" fw={500} mb="xs">Mode de prix</Text>
              <Radio.Group
                value={form.values.modePrix}
                onChange={(value) => form.setFieldValue('modePrix', value as 'HT' | 'TTC')}
              >
                <Group>
                  <Radio value="HT" label="HT" />
                  <Radio value="TTC" label="TTC" />
                </Group>
              </Radio.Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Fournisseur"
                placeholder="Choisir un fournisseur"
                data={fournisseurs}
                searchable
                rightSection={<IconSearch size={16} />}
                {...form.getInputProps('fournisseur')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="N° Document"
                placeholder="Numéro de document"
                {...form.getInputProps('numeroDocument')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Affaire"
                placeholder="Sélectionner une affaire"
                data={[]}
                searchable
                {...form.getInputProps('affaire')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              {/* Action Buttons */}
              <Group mb="md" gap="xs">
                <Button
                  leftSection={<IconShoppingCart size={16} />}
                  variant="filled"
                  color="blue"
                  size="sm"
                >
                  Bons de commande
                </Button>
                <Button
                  leftSection={<IconPackage size={16} />}
                  variant="filled"
                  color="cyan"
                  size="sm"
                >
                  Bons de déposition
                </Button>
                <Button
                  leftSection={<IconBarcode size={16} />}
                  variant="filled"
                  color="orange"
                  size="sm"
                >
                  Code à barres
                </Button>
                <Button
                  leftSection={<IconEdit size={16} />}
                  variant="filled"
                  color="green"
                  size="sm"
                  onClick={open}
                >
                  Article
                </Button>
                <Button
                  leftSection={<IconCurrencyEuro size={16} />}
                  variant="filled"
                  color="purple"
                  size="sm"
                >
                  Charge
                </Button>
                <Button
                  leftSection={<IconMessageCircle size={16} />}
                  variant="filled"
                  color="gray"
                  size="sm"
                >
                  Commentaire
                </Button>
              </Group>

              {/* Table */}
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>N° BC</Table.Th>
                    <Table.Th>N° DP</Table.Th>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix</Table.Th>
                    <Table.Th>Tva</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Montant</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {articles.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={10} style={{ textAlign: 'center', padding: '2rem' }}>
                        <Text c="dimmed">Aucun élément trouvé</Text>
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    articles.map((article) => (
                      <Table.Tr key={article.id}>
                        <Table.Td>{article.nBC}</Table.Td>
                        <Table.Td>{article.nDP}</Table.Td>
                        <Table.Td>{article.code}</Table.Td>
                        <Table.Td>{article.designation}</Table.Td>
                        <Table.Td>{article.quantite}</Table.Td>
                        <Table.Td>{article.prix.toFixed(2)}</Table.Td>
                        <Table.Td>{article.tva}%</Table.Td>
                        <Table.Td>{article.depot}</Table.Td>
                        <Table.Td>{article.montant.toFixed(2)}</Table.Td>
                        <Table.Td>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeItem(article.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm">Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['1', '2', '3']}
                    value={currentPage.toString()}
                    onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  />
                  <Text size="sm">Lignes par Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['10', '25', '50']}
                    value={itemsPerPage.toString()}
                    onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
                  />
                  <Text size="sm">0 - 0 de 0</Text>
                </Group>
                <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
              </Group>
            </Tabs.Panel>

            <Tabs.Panel value="pieces" pt="md">
              <Text c="dimmed" mb="md">Aucune pièce jointe</Text>
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Comment Section */}
          <Grid>
            <Grid.Col span={12}>
              <Textarea
                label="Commentaire"
                placeholder="Commentaire général..."
                rows={3}
                {...form.getInputProps('commentaire')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="xl" />

          {/* Summary Section */}
          <Grid>
            <Grid.Col span={8}></Grid.Col>
            <Grid.Col span={4}>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text fw={500}>MONTANT HT</Text>
                  <Text>:</Text>
                  <Text fw={500}>{montantHT.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TVA</Text>
                  <Text>:</Text>
                  <Text fw={500}>{montantTVA.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>TOTALE DES CHARGES</Text>
                  <Text>:</Text>
                  <Text fw={500}>{totalCharges.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TTC</Text>
                  <Text>:</Text>
                  <Text fw={500}>{montantTTC.toFixed(2)}</Text>
                </Group>
              </Stack>
            </Grid.Col>
          </Grid>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red" onClick={handleCancel}>
              Annuler
            </Button>
            <Button color="gray" onClick={handleValidate}>
              Valider
            </Button>
            <Button color="blue" onClick={handleSaveAndExit}>
              Enregistrer et quitter
            </Button>
            <Button onClick={handleSave}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="lg">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="N° BC"
                  placeholder="Numéro BC"
                  {...itemForm.getInputProps('nBC')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="N° DP"
                  placeholder="Numéro DP"
                  {...itemForm.getInputProps('nDP')}
                />
              </Grid.Col>
            </Grid>
            <TextInput
              label="Code"
              placeholder="Code article"
              {...itemForm.getInputProps('code')}
              required
            />
            <TextInput
              label="Désignation"
              placeholder="Désignation"
              {...itemForm.getInputProps('designation')}
              required
            />
            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité"
                  placeholder="1"
                  {...itemForm.getInputProps('quantite')}
                  min={1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Prix"
                  placeholder="0.00"
                  {...itemForm.getInputProps('prix')}
                  min={0}
                  decimalScale={2}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="TVA (%)"
                  placeholder="20"
                  {...itemForm.getInputProps('tva')}
                  min={0}
                  max={100}
                  required
                />
              </Grid.Col>
            </Grid>
            <Select
              label="Dépôt"
              placeholder="Sélectionner un dépôt"
              data={depots}
              {...itemForm.getInputProps('depot')}
            />
            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
};

export default Bon_de_reception;
