'use client';
import React, { useState, useMemo } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  Pagination,
  ScrollArea,
  Badge,
  Text,
  Select,
  ActionIcon,
  Modal,
  Grid,
  Tabs,
  Card,
  Divider,
  Stack,
  NumberInput,
  Textarea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconSearch,
  IconTransfer,
  IconPlus,
  IconRefresh,
  IconSettings,
  IconDots,
  IconBarcode,
  IconMessageCircle,
  IconFileText,
  IconPaperclip,
  IconTrash,
  IconDeviceFloppy,
  IconCheck,
  IconX,
} from '@tabler/icons-react';

// Interface pour un échange inter-dépôts dans la liste
interface EchangeInterDepots {
  id: string;
  date: string;
  depotSource: string;
  depotDestination: string;
  etat: 'En cours' | 'Validé' | 'Annulé' | 'Brouillon';
}

// Interface pour un article dans l'échange
interface ArticleEchange {
  id: string;
  code: string;
  designation: string;
  qte: number;
  prix: number;
  tva: number;
  depotSource: string;
  depotDestination: string;
  montant: number;
}

// Interface pour le formulaire d'échange
interface EchangeForm {
  numero: string;
  date: Date | null;
  depotSource: string;
  depotDestination: string;
  commentaire: string;
  articles: ArticleEchange[];
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
}

const ListEchangeInterDepots = () => {
  // États pour la gestion de l'interface
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);

  // États pour la modale d'échange
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [activeTab, setActiveTab] = useState('details');
  const [articleModalOpened, { open: openArticleModal, close: closeArticleModal }] = useDisclosure(false);

  // Données des dépôts
  const depots = [
    { value: 'depot1', label: 'Dépôt 1' },
    { value: 'depot2', label: 'Dépôt 2' },
    { value: 'depot3', label: 'Dépôt 3' },
    { value: 'depot4', label: 'Dépôt 4' },
  ];

  // Formulaire principal pour l'échange
  const form = useForm({
    initialValues: {
      numero: '1',
      date: new Date(),
      depotSource: '',
      depotDestination: '',
      commentaire: '',
    },
    validate: {
      depotSource: (value) => (!value ? 'Le dépôt source est requis' : null),
      depotDestination: (value) => (!value ? 'Le dépôt de destination est requis' : null),
    },
  });

  // Formulaire pour ajouter un article
  const articleForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      qte: 1,
      prix: 0,
      tva: 20,
      depotSource: '',
      depotDestination: '',
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      designation: (value) => (!value ? 'La désignation est requise' : null),
      qte: (value) => (value <= 0 ? 'La quantité doit être positive' : null),
    },
  });

  // État pour les articles de l'échange
  const [articles, setArticles] = useState<ArticleEchange[]>([]);
  const [currentPageArticles, setCurrentPageArticles] = useState(1);
  const [itemsPerPageArticles] = useState(10);

  // Données d'exemple des échanges
  const echangesData: EchangeInterDepots[] = [
    {
      id: '1',
      date: '16/09/2024',
      depotSource: 'Dépôt 1',
      depotDestination: 'Dépôt 2',
      etat: 'En cours',
    },
    {
      id: '2',
      date: '15/09/2024',
      depotSource: 'Dépôt 2',
      depotDestination: 'Dépôt 3',
      etat: 'Validé',
    },
    {
      id: '3',
      date: '14/09/2024',
      depotSource: 'Dépôt 3',
      depotDestination: 'Dépôt 1',
      etat: 'Brouillon',
    },
  ];

  // Filtrage et recherche des données
  const filteredEchanges = useMemo(() => {
    return echangesData.filter(echange =>
      echange.date.toLowerCase().includes(searchQuery.toLowerCase()) ||
      echange.depotSource.toLowerCase().includes(searchQuery.toLowerCase()) ||
      echange.depotDestination.toLowerCase().includes(searchQuery.toLowerCase()) ||
      echange.etat.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  // Pagination
  const totalPages = Math.ceil(filteredEchanges.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentEchanges = filteredEchanges.slice(startIndex, endIndex);

  // Fonction pour obtenir la couleur du badge selon l'état
  const getEtatColor = (etat: string) => {
    switch (etat) {
      case 'En cours':
        return 'blue';
      case 'Validé':
        return 'green';
      case 'Annulé':
        return 'red';
      case 'Brouillon':
        return 'gray';
      default:
        return 'gray';
    }
  };

  // Calcul des montants
  const calculateMontants = (articles: ArticleEchange[]) => {
    const montantHT = articles.reduce((sum, article) => sum + article.montant, 0);
    const montantTVA = articles.reduce((sum, article) => sum + (article.montant * article.tva / 100), 0);
    const montantTTC = montantHT + montantTVA;
    return { montantHT, montantTVA, montantTTC };
  };

  // Ajouter un article
  const addArticle = (values: typeof articleForm.values) => {
    const montant = values.qte * values.prix;
    const newArticle: ArticleEchange = {
      id: Date.now().toString(),
      code: values.code,
      designation: values.designation,
      qte: values.qte,
      prix: values.prix,
      tva: values.tva,
      depotSource: values.depotSource,
      depotDestination: values.depotDestination,
      montant,
    };

    setArticles(prev => [...prev, newArticle]);
    articleForm.reset();
    closeArticleModal();

    notifications.show({
      title: 'Succès',
      message: 'Article ajouté avec succès',
      color: 'green',
    });
  };

  // Supprimer un article
  const removeArticle = (id: string) => {
    setArticles(prev => prev.filter(article => article.id !== id));
    notifications.show({
      title: 'Succès',
      message: 'Article supprimé avec succès',
      color: 'red',
    });
  };

  // Pagination des articles
  const totalPagesArticles = Math.ceil(articles.length / itemsPerPageArticles);
  const startIndexArticles = (currentPageArticles - 1) * itemsPerPageArticles;
  const endIndexArticles = startIndexArticles + itemsPerPageArticles;
  const currentArticles = articles.slice(startIndexArticles, endIndexArticles);

  // Calcul des montants actuels
  const { montantHT, montantTVA, montantTTC } = calculateMontants(articles);

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      <Paper p="md" withBorder className="w-full">
        {/* Header */}
        <Group justify="space-between" mb="lg" className="bg-blue-500 text-white p-4 rounded-t-md">
          <Group align="center">
            <IconTransfer size={24} color="white" />
            <Title order={3} c="white">
              Échange inter-dépôts
            </Title>
          </Group>
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              variant="filled"
              color="blue"
              className="bg-blue-600 hover:bg-blue-700"
              onClick={openModal}
            >
              Échange inter-dépôt
            </Button>
          </Group>
        </Group>

        {/* Search Bar */}
        <Group mb="md" className="bg-white p-4">
          <TextInput
            placeholder="Rechercher"
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.currentTarget.value)}
            className="flex-1"
          />
          <ActionIcon variant="subtle" color="gray">
            <IconRefresh size={16} />
          </ActionIcon>
          <ActionIcon variant="subtle" color="gray">
            <IconSettings size={16} />
          </ActionIcon>
          <ActionIcon variant="subtle" color="gray">
            <IconDots size={16} />
          </ActionIcon>
        </Group>

        {/* Table */}
        <ScrollArea>
          <Table striped highlightOnHover withTableBorder>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Date</Table.Th>
                <Table.Th>Dépôt source</Table.Th>
                <Table.Th>Dépôt de destination</Table.Th>
                <Table.Th>État</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {currentEchanges.length === 0 ? (
                <Table.Tr>
                  <Table.Td colSpan={4} style={{ textAlign: 'center', padding: '2rem' }}>
                    <Text c="dimmed">Aucun élément trouvé</Text>
                  </Table.Td>
                </Table.Tr>
              ) : (
                currentEchanges.map((echange) => (
                  <Table.Tr key={echange.id}>
                    <Table.Td>{echange.date}</Table.Td>
                    <Table.Td>{echange.depotSource}</Table.Td>
                    <Table.Td>{echange.depotDestination}</Table.Td>
                    <Table.Td>
                      <Badge color={getEtatColor(echange.etat)} variant="light" size="sm">
                        {echange.etat}
                      </Badge>
                    </Table.Td>
                  </Table.Tr>
                ))
              )}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {/* Pagination */}
        <Group justify="space-between" align="center" mt="md" className="bg-white p-4">
          <Group>
            <Text size="sm" c="dimmed">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(parseInt(value || '1'))}
              data={Array.from({ length: totalPages }, (_, i) => ({
                value: (i + 1).toString(),
                label: (i + 1).toString(),
              }))}
              size="sm"
              w={60}
            />
            <Text size="sm" c="dimmed">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              data={[
                { value: '15', label: '15' },
                { value: '25', label: '25' },
                { value: '50', label: '50' },
              ]}
              size="sm"
              w={60}
            />
            <Text size="sm" c="dimmed">
              0 - 0 de 0
            </Text>
          </Group>

          <Group>
            <Text size="sm" c="dimmed">K</Text>
            <ActionIcon variant="subtle" size="sm">
              <IconDots size={12} />
            </ActionIcon>
            <ActionIcon variant="subtle" size="sm">
              <IconDots size={12} />
            </ActionIcon>
            <Text size="sm" c="dimmed">N</Text>
          </Group>
        </Group>
      </Paper>

      {/* Modale pour créer un échange inter-dépôt */}
      <Modal
        opened={modalOpened}
        onClose={closeModal}
        title={
          <Group align="center">
            <IconTransfer size={24} color="blue" />
            <Title order={3} c="blue">
              Échange inter-dépôt
            </Title>
          </Group>
        }
        size="xl"
        centered
      >
        <form onSubmit={form.onSubmit(() => {})}>
          {/* Formulaire principal */}
          <Card withBorder mb="md">
            <Grid>
              <Grid.Col span={3}>
                <TextInput
                  label="N°"
                  placeholder="Numéro"
                  {...form.getInputProps('numero')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <DatePickerInput
                  label="Date"
                  placeholder="16/09/2024"
                  {...form.getInputProps('date')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <Select
                  label="Dépôt source"
                  placeholder="Dépôt 1"
                  data={depots}
                  {...form.getInputProps('depotSource')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <Select
                  label="Dépôt de destination"
                  placeholder="Sélectionner un dépôt"
                  data={depots}
                  {...form.getInputProps('depotDestination')}
                  required
                />
              </Grid.Col>
            </Grid>
          </Card>

          <Divider my="md" />

          {/* Onglets */}
          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              {/* Boutons d'action */}
              <Group justify="flex-end" mb="md">
                <Button
                  leftSection={<IconBarcode size={16} />}
                  variant="outline"
                  color="blue"
                >
                  Code à barres
                </Button>
                <Button
                  leftSection={<IconPlus size={16} />}
                  color="blue"
                  onClick={openArticleModal}
                >
                  Article
                </Button>
                <Button
                  leftSection={<IconMessageCircle size={16} />}
                  variant="outline"
                  color="gray"
                >
                  Commentaire
                </Button>
              </Group>

              {/* Tableau des articles */}
              <ScrollArea>
                <Table striped highlightOnHover withTableBorder>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Code</Table.Th>
                      <Table.Th>Désignation</Table.Th>
                      <Table.Th>Qté</Table.Th>
                      <Table.Th>Prix</Table.Th>
                      <Table.Th>Tva</Table.Th>
                      <Table.Th>Dépôt</Table.Th>
                      <Table.Th>Dépôt de destination</Table.Th>
                      <Table.Th>Montant</Table.Th>
                      <Table.Th>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {currentArticles.length === 0 ? (
                      <Table.Tr>
                        <Table.Td colSpan={9} style={{ textAlign: 'center', padding: '2rem' }}>
                          <Text c="dimmed">Aucun élément trouvé</Text>
                        </Table.Td>
                      </Table.Tr>
                    ) : (
                      currentArticles.map((article) => (
                        <Table.Tr key={article.id}>
                          <Table.Td>{article.code}</Table.Td>
                          <Table.Td>{article.designation}</Table.Td>
                          <Table.Td>{article.qte}</Table.Td>
                          <Table.Td>{article.prix.toFixed(2)}</Table.Td>
                          <Table.Td>{article.tva}%</Table.Td>
                          <Table.Td>
                            <Badge color="blue" variant="light" size="sm">
                              {depots.find(d => d.value === article.depotSource)?.label || article.depotSource}
                            </Badge>
                          </Table.Td>
                          <Table.Td>
                            <Badge color="green" variant="light" size="sm">
                              {depots.find(d => d.value === article.depotDestination)?.label || article.depotDestination}
                            </Badge>
                          </Table.Td>
                          <Table.Td>{article.montant.toFixed(2)}</Table.Td>
                          <Table.Td>
                            <ActionIcon
                              color="red"
                              variant="subtle"
                              onClick={() => removeArticle(article.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Table.Td>
                        </Table.Tr>
                      ))
                    )}
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Pagination des articles */}
              {totalPagesArticles > 1 && (
                <Group justify="center" mt="md">
                  <Pagination
                    value={currentPageArticles}
                    onChange={setCurrentPageArticles}
                    total={totalPagesArticles}
                    size="sm"
                  />
                </Group>
              )}
            </Tabs.Panel>

            <Tabs.Panel value="pieces-jointes" pt="md">
              <Text c="dimmed" ta="center" py="xl">
                Aucune pièce jointe
              </Text>
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          {/* Résumé des montants */}
          <Card withBorder mt="md">
            <Group justify="space-between" align="center">
              <Group>
                <Text size="sm" c="dimmed">
                  Page {currentPageArticles} sur {totalPagesArticles} - {articles.length} articles
                </Text>
              </Group>
              <Group>
                <Stack gap="xs" align="flex-end">
                  <Text size="sm">
                    <strong>MONTANT HT :</strong> {montantHT.toFixed(2)}
                  </Text>
                  <Text size="sm">
                    <strong>MONTANT TVA :</strong> {montantTVA.toFixed(2)}
                  </Text>
                  <Text size="sm">
                    <strong>MONTANT TTC :</strong> {montantTTC.toFixed(2)}
                  </Text>
                </Stack>
              </Group>
            </Group>
          </Card>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button variant="outline" color="red" onClick={closeModal}>
              Annuler
            </Button>
            <Button variant="outline" color="gray">
              Valider
            </Button>
            <Button
              leftSection={<IconDeviceFloppy size={16} />}
              color="blue"
            >
              Enregistrer et quitter
            </Button>
            <Button
              leftSection={<IconCheck size={16} />}
              color="green"
            >
              Enregistrer
            </Button>
          </Group>
        </form>
      </Modal>

      {/* Modale pour ajouter un article */}
      <Modal
        opened={articleModalOpened}
        onClose={closeArticleModal}
        title="Ajouter un article"
        size="lg"
      >
        <form onSubmit={articleForm.onSubmit(addArticle)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder="Code article"
                  {...articleForm.getInputProps('code')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Désignation"
                  placeholder="Désignation"
                  {...articleForm.getInputProps('designation')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité"
                  placeholder="1"
                  {...articleForm.getInputProps('qte')}
                  min={1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Prix"
                  placeholder="0.00"
                  decimalScale={2}
                  {...articleForm.getInputProps('prix')}
                  min={0}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="TVA (%)"
                  placeholder="20"
                  {...articleForm.getInputProps('tva')}
                  min={0}
                  max={100}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Dépôt source"
                  placeholder="Sélectionner un dépôt"
                  data={depots}
                  {...articleForm.getInputProps('depotSource')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Dépôt de destination"
                  placeholder="Sélectionner un dépôt"
                  data={depots}
                  {...articleForm.getInputProps('depotDestination')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Group justify="flex-end">
              <Button variant="outline" onClick={closeArticleModal}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default ListEchangeInterDepots;
