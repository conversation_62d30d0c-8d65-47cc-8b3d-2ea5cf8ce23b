'use client';
import React, { useState, } from 'react';
import FilterList from './FilterList';
import StyleRulesTab from './StyleRulesTab';
// import {NouvelleRegle} from './NouvelleRegle';
import {
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  TextInput,
  Menu,
  Checkbox,
  Button,
  Select,
  Pagination,
  Modal,
  Tabs,
} from '@mantine/core';
import {
  IconSearch,
  IconPrinter,
  IconEdit,
  IconCurrencyDollar,
  IconFileText,
  IconFilter,
  IconRefresh,
  IconAlertCircle,
  IconTrash,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

// Import billing hook
import { useBilling } from '@/hooks/useBilling';

// Import du composant MesFactures
import MesFactures from './Mes_facuers';

// import { Plus } from 'lucide-react';

// Types pour le composant  'Model2' | 'Model3' |
type SequenceModel = 'Model1' |  'all';
// import {
//   RotateCcw, 
//   FileSpreadsheet, 
//   MoreVertical, 
//   Check 
// } from 'lucide-react';

interface Column {
  id: string;
  label: string;
  isShown: boolean;
  isRequired: boolean;
}
// Interface pour les données de facture
interface FactureData {
  id: number;
  numeroFacture: string;
  date: string;
  beneficiaire: string;
  montant: number;
  assurance: string;
  modePaiement: string;
}
type MesFacturesList1Props = {
  toggleList: () => void;
};
const MesFacturesList = ({ toggleList }: MesFacturesList1Props) => {
  
   // État équivalent à vm.sequenceModel
    const [sequenceModel, setSequenceModel] = useState<SequenceModel>('Model1');
    
    // Données équivalentes à vm.sequenceModels
    const sequenceModels: SequenceModel[] = ['Model1', ]; // 'Model2', 'Model3'  Ajustez selon vos données réelles
  
    // Fonction équivalente à vm.handleSequenceChange()
    const handleSequenceChange = (value: SequenceModel): void => {
      setSequenceModel(value);
      // Ajoutez ici votre logique de traitement
      console.log('Modèle sélectionné:', value);
    };

    // Use billing hook for backend integration
    const {
      invoices,
      loading: billingLoading,
      error: billingError,
      refreshAll,
      deleteInvoice,
      updateInvoice,
    } = useBilling({
      autoFetch: true,
      dataTypes: ['invoices']
    });
  
   
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFactures, setSelectedFactures] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Données d'exemple correspondant à l'image
  const facturesData: FactureData[] = [
    {
      id: 6,
      numeroFacture: '6',
      date: '24/08/2022',
      beneficiaire: 'EL KANBI HAMZA',
      montant: 700.00,
      assurance: 'CNSS',
      modePaiement: 'Espèce'
    },
    {
      id: 5,
      numeroFacture: '5',
      date: '13/07/2022',
      beneficiaire: 'EL KANBI HAMZA',
      montant: 17600.00,
      assurance: 'CNSS',
      modePaiement: 'Espèce'
    },
    {
      id: 4,
      numeroFacture: '4',
      date: '12/07/2022',
      beneficiaire: 'AKONGA MBARGA JOSEPH',
      montant: 0.00,
      assurance: 'AXA',
      modePaiement: 'Espèce'
    },
    {
      id: 3,
      numeroFacture: '3',
      date: '12/07/2022',
      beneficiaire: 'AKONGA MBARGA JOSEPH',
      montant: 500.00,
      assurance: 'AXA',
      modePaiement: 'Espèce'
    },
    {
      id: 2,
      numeroFacture: '2',
      date: '17/09/2021',
      beneficiaire: 'NISSR TEST',
      montant: 1700.00,
      assurance: 'BANK AL MAGHREB',
      modePaiement: 'Espèce'
    },
    {
      id: 1,
      numeroFacture: '1',
      date: '01/03/2021',
      beneficiaire: 'Mr OUARAOU ANIS',
      montant: 2000.00,
      assurance: 'CMIM',
      modePaiement: 'Espèce'
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredFactures = facturesData.filter(facture =>
    facture.beneficiaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
    facture.numeroFacture.includes(searchTerm) ||
    facture.assurance.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gérer la sélection des factures
  const handleSelectFacture = (id: number) => {
    setSelectedFactures(prev =>
      prev.includes(id)
        ? prev.filter(factureId => factureId !== id)
        : [...prev, id]
    );
  };

  // Sélectionner/désélectionner toutes les factures
  const handleSelectAll = () => {
    if (selectedFactures.length === filteredFactures.length) {
      setSelectedFactures([]);
    } else {
      setSelectedFactures(filteredFactures.map(f => f.id));
    }
  };
// -------------------------part2----------------


  const [columns, setColumns] = useState<Column[]>([
    { id: 'invoice_number', label: 'N°. Facture', isShown: true, isRequired: true },
    { id: 'invoice_date', label: 'Date', isShown: true, isRequired: true },
    { id: 'invoice_beneficiary', label: 'Bénéficiaire', isShown: true, isRequired: true },
    { id: 'taxed_amount', label: 'Montant Total', isShown: true, isRequired: true },
    { id: 'insurance', label: 'Assurance', isShown: true, isRequired: false },
    { id: 'payment_mode', label: 'Mode de paiement', isShown: true, isRequired: false }
  ]);

  const handleReload = (): void => {
    console.log('Reload data');
  };

  const handleExportExcel = (): void => {
    console.log('Export Excel');
  };

  const toggleColumn = (columnId: string): void => {
    setColumns(columns.map(col => 
      col.id === columnId 
        ? { ...col, isShown: !col.isShown }
        : col
    ));
  };
   const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
  const toggleSidebar = () => {
        setIsSidebarVisible(!isSidebarVisible);
      };

       const [tabIndex, setTabIndex] = useState<string | null>('0');
       const [vm, setVm] = useState({
           styleRules: [],      // [] ou données initiales
           is_create: false,
           columns: [],
           mnModel: {},
           draftRule: {},
         });

    

      
  return (
    <>
      <Card shadow="0" padding="sm" radius="md" withBorder mb={22} bg={'#3799CE'}>
                  <Group justify="flex-end">
                    <div className="flex flex-col">
              <div 
                className="flex flex-row gap-4" 
                role="radiogroup"
                aria-label="Sélection du modèle"
              >
                {/* Boutons radio pour les modèles */}
                {sequenceModels.map((model) => (
                  <label 
                    key={model}
                    className="flex items-center gap-2 cursor-pointer hover:bg-[#3799CE] hover:text-[white]  px-3 py-1 rounded transition-colors"
                  >
                    <input
                      type="radio"
                      name="sequenceModel"
                      value={model}
                      checked={sequenceModel === model}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSequenceChange(e.target.value as SequenceModel)}
                      className="w-4 h-4 text-[#3799CE] bg-gray-100 border-gray-300 focus:ring-[#3799CE] focus:ring-2"
                    />
                    <span className="text-sm font-medium">
                      {model}
                    </span>
                  </label>
                ))}
                
                {/* Bouton radio "Tous" */}
                <label className="flex items-center gap-2 cursor-pointer hover:text-[white] px-3 py-1 rounded transition-colors">
                  <input
                    type="radio"
                    name="sequenceModel"
                    value="all"
                    checked={sequenceModel === 'all'}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSequenceChange(e.target.value as SequenceModel)}
                    className="w-4 h-4 text-[#3799CE] bg-gray-100 border-gray-300 focus:ring-[#3799CE] focus:ring-2"
                  />
                  <span className="text-sm font-medium">
                    Tous
                  </span>
                </label>
              </div>
            </div>
            
            {/* Bouton nouvelle facture */}
            <Button
              size="sm"
              variant="filled"
              color="#3799CE"
              leftSection={<IconFileText size={16} />}
             
              // onClick={() => setIsModalOpen(true)}
             onClick={toggleList}
            >
             Facture &nbsp; &nbsp; 
             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white lucide lucide-plus-icon lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
               {/* <Plus size={16} className="text-white" /> */}
            </Button>
              </Group>
               </Card>
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
      
          {/* Barre de recherche */}
          <Group align="center" gap="sm">
                   {/* Bouton de filtre */}
       
          <ActionIcon variant="subtle" color="gray" size="lg" className="cursor-pointer"
              onClick={(event) => {
                event.preventDefault();
                toggleSidebar(); // Toggle sidebar visibility
              }}
              >
                  <IconFilter size={18} />
                </ActionIcon>
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />
          
          </Group>
<Group>
      {/* Boutons d'action */}
        <button
          onClick={handleReload}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Actualiser"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rotate-ccw-icon lucide-rotate-ccw"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/></svg>
          {/* <RotateCcw size={20} /> */}
        </button>

        <button
          onClick={handleExportExcel}
          className="p-2 text-green-600 hover:bg-green-50 rounded-full transition-colors"
          aria-label="Exporter Excel"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-spreadsheet-icon lucide-file-spreadsheet"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M8 13h2"/><path d="M14 13h2"/><path d="M8 17h2"/><path d="M14 17h2"/></svg>
          {/* <FileSpreadsheet size={20} /> */}
        </button>

        {/* Menu des colonnes */}
        <Menu shadow="md" width={200}>
      <Menu.Target>
        {/* <MoreVertical size={20} /> */}
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-move-vertical-icon lucide-move-vertical"><path d="M12 2v20"/><path d="m8 18 4 4 4-4"/><path d="m8 6 4-4 4 4"/></svg>
      </Menu.Target>
      <Menu.Dropdown>
          {columns.map((column) => (
             <Menu.Item key={column.id} 
             onClick={() => !column.isRequired && toggleColumn(column.id)} 
              disabled={column.isRequired}
              leftSection={<>{column.isShown && (
                        // <Check size={16} className="text-green-600" />
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-600 lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                      )}</>}
             >
                    {column.label}
                   </Menu.Item>
                ))}
       
  
     
      </Menu.Dropdown>
    </Menu>
        </Group>
        </Group>
      </Card>
       <div className='flex'>
      {isSidebarVisible && (
        <>
      <div className='w-[19%] mt-0'>
         <Tabs value={tabIndex} onChange={setTabIndex}>
            <Tabs.List>
              <Tabs.Tab value="0">Filtre avancé</Tabs.Tab>
              <Tabs.Tab value="1">Règles de mise en forme</Tabs.Tab>
              
            </Tabs.List>
      
            <Tabs.Panel value="0" pt="xs">
              <FilterList />
            </Tabs.Panel>
      
            <Tabs.Panel value="1" pt="xs">
          <StyleRulesTab
            styleRules={vm.styleRules}
            isCreate={vm.is_create}
            onStartCreate={() => setVm((prev) => ({ ...prev, is_create: true }))}
            columns={vm.columns}      // ✅ maintenant reconnu
            model={vm.mnModel}
            draftRule={vm.draftRule}
          />
              <Tabs.Panel value="2" pt="xs">

                {/* if click plus icon shwo tabs */}
            {/* <NouvelleRegle
          columns={columns}
          styleRules={styleRules}
          isCreate={isCreate}
          onCancel={handleCancel}
          onSave={handleSave}
        /> */}
NouvelleRegle
        </Tabs.Panel>
      </Tabs.Panel>
            
          </Tabs>
        </div>
        </>)}
      
      <div className={isSidebarVisible ?  "w-[80%]": "w-full "}>
    <Box className="w-full h-full bg-gray-50">
      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        {/* <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        > */}
         <Table striped highlightOnHover withTableBorder withColumnBorders>
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedFactures.length === filteredFactures.length && filteredFactures.length > 0}
                  indeterminate={selectedFactures.length > 0 && selectedFactures.length < filteredFactures.length}
                  onChange={handleSelectAll}
                  size="sm"
                  radius="xs"
                  ml={12}
                />
              </Table.Th>
                <Table.Th style={{ minWidth: 140 }} w={140} >
                 <Text size="lg" fw={700} ta="left" pl="8">N°. Facture</Text>
              </Table.Th>
             <Table.Th style={{ minWidth: 140 }} w={140} >
                 <Text size="lg" fw={700} ta="left" pl="8">Date</Text>
              </Table.Th>
              <Table.Th  >
                 <Text size="lg" fw={700} ta="left" pl="8"> Bénéficiaire</Text>
              </Table.Th>
               <Table.Th  >
                 <Text size="lg" fw={700} ta="left" pl="8"> Montant Total</Text>
              </Table.Th>
            <Table.Th  >
                 <Text size="lg" fw={700} ta="left" pl="8">  Assurance</Text>
              </Table.Th>
              <Table.Th  >
                 <Text size="lg" fw={700} ta="left" pl="8">   Mode de paiement</Text>
              </Table.Th>
              
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-26">
               
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {/* Ligne de recherche */}
            <Table.Tr className="bg-gray-50">
              <Table.Td className="border-r border-gray-300">
                {/* Cellule vide pour la checkbox */}
              </Table.Td>
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  radius="xs"
                  className="w-full"
                  styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td >
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                   styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td>
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                   styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td>
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                   styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td>
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                   styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td>
              <Table.Td p={0}>
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                   styles={{
                input: {
                  border: 'none',
                  boxShadow: 'none',
                  backgroundColor: '#F8F9FA',
                
                },
              }}
                />
              </Table.Td>
              <Table.Td p={0} >
                {/* Cellule vide pour les actions */}
              </Table.Td>
            </Table.Tr>

            {/* Données des factures */}
            {filteredFactures.map((facture) => (
              <Table.Tr key={facture.id} className="hover:bg-gray-50">
                <Table.Td className="border-r border-gray-300">
                  <Checkbox
                    checked={selectedFactures.includes(facture.id)}
                    onChange={() => handleSelectFacture(facture.id)}
                    size="sm"
                     radius="xs"
                    
                  />
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.numeroFacture}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.date}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.beneficiaire}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right">
                  <Text size="sm" className="text-gray-800">
                    {facture.montant.toFixed(2)}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.assurance}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.modePaiement}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Group gap="xs" justify="center">
                    <Tooltip label="Modifier" withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="blue"
                        className="hover:bg-blue-100"
                      >
                        <IconEdit size={18} color='#6E7375'/>
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Imprimer" withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="gray"
                        className="hover:bg-gray-100"
                      >
                        <IconPrinter size={18} color='#6E7375'/>
                      </ActionIcon>
                    </Tooltip>
                    {/* <Tooltip label="Voir">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="green"
                        className="hover:bg-green-100"
                      >
                        <IconEye size={14} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Supprimer">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="red"
                        className="hover:bg-red-100"
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Tooltip> */}
                    <Tooltip label="Paiement" withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="yellow"
                        className="hover:bg-yellow-100"
                      >
                        <IconCurrencyDollar size={18} color='#6E7375' />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={['1', '2', '3', '4', '5']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 5)}
              data={['5', '10', '20', '50']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              1 - 6 de 6
            </Text>
          </Group>

          <Pagination
            total={Math.ceil(filteredFactures.length / itemsPerPage)}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création de facture */}
      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nouvelle Facture"
        size="95%"
        centered
        className="modal-facture"
      >
        <MesFactures />
      </Modal>
    </Box>
    </div>
    </div>
    </>
  );
};

export default MesFacturesList;
