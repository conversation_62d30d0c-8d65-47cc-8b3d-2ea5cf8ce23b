import React from 'react';
import { AddModelModal } from './AddModelModal';
import { SavedModelsModal } from './SavedModelsModal';
import { DictionaryTreeModal } from './DictionaryTreeModal';
import { SavedModel, TreeNode, TreeItemChoixMultipleProps } from './types';

interface DictionaryModalsManagerProps {
  // États des modaux
  isAddModelModalOpen: boolean;
  isSavedModelsModalOpen: boolean;
  isDictionaryTreeModalOpen: boolean;
  
  // Données
  modelTitle: string;
  savedModels: SavedModel[];
  exampleData: TreeNode[];
  selectedNodes: Set<string>;
  collapsedNodes: Record<string, boolean>;
  editingModelId: string | null;
  
  // Fonctions de gestion des états
  setModelTitle: (title: string) => void;
  setIsAddModelModalOpen: (open: boolean) => void;
  setIsSavedModelsModalOpen: (open: boolean) => void;
  setIsDictionaryTreeModalOpen: (open: boolean) => void;
  
  // Fonctions de gestion des modèles
  onSaveModel: () => void;
  onToggleModel: (modelId: string) => void;
  onDeleteModel: (modelId: string) => void;
  onEditModel: (modelId: string) => void;
  
  // Fonctions de gestion de l'arbre
  onToggleNodeCollapse: (nodeId: string) => void;
  onToggleNodeSelection: (nodeId: string) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  
  // Fonctions d'action
  onValidate: () => void;
  onCancel: () => void;
  onCloseSidebar?: () => void;
  getSelectedValues: () => string[];

  // Composants
  TreeItemChoixMultiple: React.ComponentType<TreeItemChoixMultipleProps>;
}

export const DictionaryModalsManager: React.FC<DictionaryModalsManagerProps> = ({
  // États des modaux
  isAddModelModalOpen,
  isSavedModelsModalOpen,
  isDictionaryTreeModalOpen,
  
  // Données
  modelTitle,
  savedModels,
  exampleData,
  selectedNodes,
  collapsedNodes,
  editingModelId,
  
  // Fonctions de gestion des états
  setModelTitle,
  setIsAddModelModalOpen,
  setIsSavedModelsModalOpen,
  setIsDictionaryTreeModalOpen,
  
  // Fonctions de gestion des modèles
  onSaveModel,
  onToggleModel,
  onDeleteModel,
  onEditModel,
  
  // Fonctions de gestion de l'arbre
  onToggleNodeCollapse,
  onToggleNodeSelection,
  onSelectAll,
  onDeselectAll,
  
  // Fonctions d'action
  onValidate,
  onCancel,
  onCloseSidebar,
  getSelectedValues,

  // Composants
  TreeItemChoixMultiple
}) => {
  const handleAddModelCancel = () => {
    setIsAddModelModalOpen(false);
    setModelTitle('');

    // Si on était en mode édition, retourner aux modèles sauvegardés
    // Sinon, retourner au modal de l'arbre du dictionnaire
    if (editingModelId !== null) {
      setIsSavedModelsModalOpen(true);
      console.log('Cancelled edit, returning to SavedModelsModal');
    } else {
      setIsDictionaryTreeModalOpen(true);
      console.log('Cancelled AddModelModal, returning to DictionaryTreeModal');
    }
  };

  const handleSavedModelsNewModel = () => {
    console.log('handleSavedModelsNewModel called, closing sidebar');
    setIsSavedModelsModalOpen(false);
    setIsDictionaryTreeModalOpen(true);
    // Fermer la sidebar
    onCloseSidebar?.();
  };

  const handleDictionaryTreeAddModel = () => {
    // Fermer le modal de l'arbre et ouvrir le modal de saisie de titre
    setIsDictionaryTreeModalOpen(false);
    setIsAddModelModalOpen(true);
    console.log('Opening AddModelModal for title input');
  };

  // Debug: Log des états des modaux
  console.log('DictionaryModalsManager render:', {
    isAddModelModalOpen,
    isSavedModelsModalOpen,
    isDictionaryTreeModalOpen,
    hasOnCloseSidebar: !!onCloseSidebar
  });

  return (
    <>
      {/* Modal d'ajout de modèle */}
      <AddModelModal
        opened={isAddModelModalOpen}
        onClose={() => setIsAddModelModalOpen(false)}
        modelTitle={modelTitle}
        setModelTitle={setModelTitle}
        onSave={onSaveModel}
        onCancel={handleAddModelCancel}
        isEditing={editingModelId !== null}
      />

      {/* Modal des modèles sauvegardés */}
      <SavedModelsModal
        opened={isSavedModelsModalOpen}
        onClose={() => setIsSavedModelsModalOpen(false)}
        savedModels={savedModels}
        onToggleModel={onToggleModel}
        onDeleteModel={onDeleteModel}
        onEditModel={onEditModel}
        onValidate={onValidate}
        onCancel={onCancel}
        onNewModel={handleSavedModelsNewModel}
      />

      {/* Modal de l'arbre du dictionnaire */}
      <DictionaryTreeModal
        opened={isDictionaryTreeModalOpen}
        onClose={() => setIsDictionaryTreeModalOpen(false)}
        exampleData={exampleData}
        selectedNodes={selectedNodes}
        collapsedNodes={collapsedNodes}
        onToggleNodeCollapse={onToggleNodeCollapse}
        onToggleNodeSelection={onToggleNodeSelection}
        onSelectAll={onSelectAll}
        onDeselectAll={onDeselectAll}
        onAddModel={handleDictionaryTreeAddModel}
        onValidate={onValidate}
        onCancel={onCancel}
        getSelectedValues={getSelectedValues}
        TreeItemChoixMultiple={TreeItemChoixMultiple}
      />
    </>
  );
};
