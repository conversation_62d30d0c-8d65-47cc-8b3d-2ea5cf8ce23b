import { Select } from "@mantine/core";
import { useState, useEffect } from "react";
import moment from "moment";

interface SelectMoisProps {
  date: Date;
  setter: (newDate: Date) => void;
  label: string; // Ensure label is part of props
}

const SelectMois: React.FC<SelectMoisProps> = ({ date, setter, label }) => {
  // Create options for month and year
  const options = Array.from({ length: 12 }, (_, i) => ({
    value: moment().month(i).format("YYYY-MM"), // Store month and year
    label: moment().month(i).format("MMMM YYYY"), // Display month and year
  }));

  const currentMonthYear = moment(date).format("YYYY-MM");
  const [selected, setSelected] = useState<string>(currentMonthYear);

  // Sync selected value with label
  useEffect(() => {
    const labelMonthYear = moment(label, "MMMM YYYY").format("YYYY-MM");
    setSelected(labelMonthYear);
  }, [label]);

  const handleMonthChange = (value: string | null) => {
    if (value) {
      setSelected(value);
      const newDate = moment(value, "YYYY-MM").toDate();
      setter(newDate); // Update parent component with new date
    }
  };

  return (
    <Select
      placeholder="Pick a month and year"
      data={options}
      value={selected}
      onChange={handleMonthChange}
      className=" text-sm font-medium"
    />
  );
};

export default SelectMois;
