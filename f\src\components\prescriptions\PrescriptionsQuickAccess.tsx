/**
 * Prescriptions Quick Access Modal
 * Provides quick access to patient prescriptions and medication history from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  SimpleGrid,
  ActionIcon,
  Tooltip,
  TextInput,
  Divider,
} from '@mantine/core';
import {
  IconPrescription,
  IconHistory,
  IconTemplate,
  IconRefresh,
  IconChartPie,
  IconExternalLink,
  IconPlus,
  IconEye,
  IconEdit,
  IconSearch,
  IconPrinter,
  IconSend,
  IconAlertTriangle,
  IconShieldCheck,
} from '@tabler/icons-react';
import { usePrescriptions } from '@/hooks/usePrescriptions';
import PrescriptionsWidgets from './PrescriptionsWidgets';

interface PrescriptionsQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  patientId?: string;
  patientName?: string;
  defaultTab?: 'dashboard' | 'prescriptions' | 'history' | 'templates' | 'refills' | 'analytics';
  dateRange?: { start: string; end: string };
  onNavigateToFullPage?: () => void;
}

const PrescriptionsQuickAccess: React.FC<PrescriptionsQuickAccessProps> = ({
  opened,
  onClose,
  patientId,
  patientName,
  defaultTab = 'dashboard',
  dateRange,
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  const {
    prescriptions,
    medicationHistory,
    prescriptionTemplates,
    prescriptionRefills,
    drugInteractions,
    analytics,
    loading,
    refreshAll,
    getActivePrescriptions,
    getExpiredPrescriptions,
    getPendingRefills,
    getPatientPrescriptionStats,
    getPrescriptionTrends,
    getMostUsedTemplates,
  } = usePrescriptions({ 
    patientId, 
    dateRange, 
    autoFetch: opened,
    dataTypes: ['prescriptions', 'history', 'templates', 'refills', 'analytics']
  });

  const patientStats = patientId ? getPatientPrescriptionStats(patientId) : null;
  const prescriptionTrends = getPrescriptionTrends();
  const activePrescriptions = getActivePrescriptions();
  const expiredPrescriptions = getExpiredPrescriptions();
  const pendingRefills = getPendingRefills();
  const mostUsedTemplates = getMostUsedTemplates(5);

  const handleRefresh = () => {
    refreshAll(patientId);
  };

  const filteredPrescriptions = prescriptions.filter(prescription => {
    const matchesSearch = searchTerm === '' || 
      prescription.medication_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prescription.patient_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || prescription.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <PrescriptionsWidgets 
              patientId={patientId}
              dateRange={dateRange}
              compact={false}
              showDrugInteractions={true}
            />
          </Stack>
        );

      case 'prescriptions':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconPrescription size={20} />
                  <Text fw={600}>Prescriptions</Text>
                  <Badge color="blue">{filteredPrescriptions.length}</Badge>
                </Group>
                <Group gap="xs">
                  <TextInput
                    placeholder="Rechercher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    leftSection={<IconSearch size={14} />}
                    size="xs"
                    style={{ width: 200 }}
                  />
                  <Select
                    value={selectedStatus}
                    onChange={(value) => setSelectedStatus(value || 'all')}
                    data={[
                      { value: 'all', label: 'Tous statuts' },
                      { value: 'active', label: 'Actives' },
                      { value: 'expired', label: 'Expirées' },
                      { value: 'completed', label: 'Terminées' },
                      { value: 'cancelled', label: 'Annulées' },
                    ]}
                    size="xs"
                  />
                  <ActionIcon variant="light" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {filteredPrescriptions.map((prescription) => (
                    <Card key={prescription.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Group gap="xs" mb="xs">
                            <Text size="sm" fw={500}>{prescription.medication_name}</Text>
                            {prescription.is_controlled_substance && (
                              <Badge size="xs" color="orange">Contrôlé</Badge>
                            )}
                            <Badge 
                              size="xs" 
                              color={
                                prescription.priority === 'urgent' ? 'red' :
                                prescription.priority === 'high' ? 'orange' :
                                prescription.priority === 'normal' ? 'blue' : 'gray'
                              }
                            >
                              {prescription.priority}
                            </Badge>
                          </Group>
                          <Text size="xs" c="dimmed">
                            Patient: {prescription.patient_name}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Dosage: {prescription.dosage} | Fréquence: {prescription.frequency}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Durée: {prescription.duration} | Quantité: {prescription.quantity}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Prescrit le: {new Date(prescription.date_prescribed).toLocaleDateString()}
                          </Text>
                          {prescription.instructions && (
                            <Text size="xs" c="dimmed">Instructions: {prescription.instructions}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Badge 
                              size="sm" 
                              color={
                                prescription.status === 'active' ? 'green' : 
                                prescription.status === 'expired' ? 'red' : 
                                prescription.status === 'completed' ? 'blue' : 
                                prescription.status === 'cancelled' ? 'gray' : 'yellow'
                              }
                            >
                              {prescription.status}
                            </Badge>
                            <Text size="xs" c="dimmed" mt="xs">
                              Renouvellements: {prescription.refills_remaining}/{prescription.refills_allowed}
                            </Text>
                          </div>
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Imprimer">
                              <ActionIcon variant="subtle" size="sm">
                                <IconPrinter size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Envoyer">
                              <ActionIcon variant="subtle" size="sm">
                                <IconSend size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Renouveler">
                              <ActionIcon variant="subtle" size="sm">
                                <IconRefresh size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {filteredPrescriptions.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune prescription trouvée
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'history':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconHistory size={20} />
                  <Text fw={600}>Historique Médicamenteux</Text>
                  <Badge color="teal">{medicationHistory.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {medicationHistory.map((history) => (
                    <Card key={history.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{history.medication_name}</Text>
                          <Text size="xs" c="dimmed">
                            Patient: {history.patient_name}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Dosage: {history.dosage}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Période: {new Date(history.start_date).toLocaleDateString()} - {history.end_date ? new Date(history.end_date).toLocaleDateString() : 'En cours'}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Raison: {history.reason}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Prescripteur: {history.prescribing_doctor}
                          </Text>
                          {history.notes && (
                            <Text size="xs" c="dimmed">Notes: {history.notes}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={
                              history.effectiveness === 'excellent' ? 'green' :
                              history.effectiveness === 'good' ? 'blue' :
                              history.effectiveness === 'fair' ? 'yellow' :
                              history.effectiveness === 'poor' ? 'red' : 'gray'
                            }
                          >
                            {history.effectiveness}
                          </Badge>
                          {history.side_effects && (
                            <Tooltip label={`Effets secondaires: ${history.side_effects}`}>
                              <ActionIcon variant="subtle" size="sm" color="orange">
                                <IconAlertTriangle size={14} />
                              </ActionIcon>
                            </Tooltip>
                          )}
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {medicationHistory.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun historique médicamenteux
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'templates':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconTemplate size={20} />
                  <Text fw={600}>Templates de Prescription</Text>
                  <Badge color="purple">{prescriptionTemplates.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <SimpleGrid cols={2} spacing="md">
                  {mostUsedTemplates.map((template) => (
                    <Card key={template.id} padding="sm" radius="sm" withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="sm" fw={500}>{template.name}</Text>
                          <Badge size="xs" color="purple">
                            {template.usage_count} utilisations
                          </Badge>
                        </Group>
                        <Text size="xs" c="dimmed">{template.description}</Text>
                        <Text size="xs" c="dimmed">
                          Médicament: {template.medication_name}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Dosage: {template.dosage} | Fréquence: {template.frequency}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Durée: {template.duration}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Indication: {template.indication}
                        </Text>
                        <Group justify="space-between" mt="xs">
                          <Badge size="xs" color="blue">{template.category}</Badge>
                          <Group gap="xs">
                            <Button size="xs" variant="light">
                              Utiliser
                            </Button>
                            <ActionIcon variant="subtle" size="sm">
                              <IconEdit size={14} />
                            </ActionIcon>
                          </Group>
                        </Group>
                      </Stack>
                    </Card>
                  ))}
                  {prescriptionTemplates.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl" style={{ gridColumn: '1 / -1' }}>
                      Aucun template disponible
                    </Text>
                  )}
                </SimpleGrid>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'refills':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconRefresh size={20} />
                  <Text fw={600}>Renouvellements</Text>
                  <Badge color="orange">{prescriptionRefills.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {prescriptionRefills.map((refill) => (
                    <Card key={refill.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>Renouvellement #{refill.refill_number}</Text>
                          <Text size="xs" c="dimmed">
                            Prescription: {refill.prescription_id}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Date: {new Date(refill.refill_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Quantité: {refill.quantity_dispensed}
                          </Text>
                          {refill.pharmacy_name && (
                            <Text size="xs" c="dimmed">
                              Pharmacie: {refill.pharmacy_name}
                            </Text>
                          )}
                          {refill.patient_pickup_date && (
                            <Text size="xs" c="dimmed">
                              Récupéré le: {new Date(refill.patient_pickup_date).toLocaleDateString()}
                            </Text>
                          )}
                        </div>
                        <Badge 
                          size="sm" 
                          color={
                            refill.status === 'picked_up' ? 'green' :
                            refill.status === 'dispensed' ? 'blue' :
                            refill.status === 'approved' ? 'yellow' :
                            refill.status === 'requested' ? 'orange' : 'red'
                          }
                        >
                          {refill.status}
                        </Badge>
                      </Group>
                    </Card>
                  ))}
                  {prescriptionRefills.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun renouvellement enregistré
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'analytics':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconChartPie size={20} />
                  <Text fw={600}>Analytiques Prescriptions</Text>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                {analytics ? (
                  <SimpleGrid cols={2} spacing="md">
                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Médicaments les Plus Prescrits</Text>
                      <Stack gap="xs">
                        {analytics.most_prescribed_medications.map((medication, index) => (
                          <Group key={medication.medication_name} justify="space-between">
                            <Text size="xs">{medication.medication_name}</Text>
                            <Badge size="xs" color="blue">
                              {medication.prescription_count} prescriptions
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Observance Patients</Text>
                      <Stack gap="xs">
                        {analytics.patient_compliance.map((patient) => (
                          <Group key={patient.patient_id} justify="space-between">
                            <Text size="xs">{patient.patient_name}</Text>
                            <Badge 
                              size="xs" 
                              color={patient.compliance_rate >= 80 ? 'green' : patient.compliance_rate >= 60 ? 'yellow' : 'red'}
                            >
                              {patient.compliance_rate.toFixed(1)}%
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Statistiques Générales</Text>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="xs">Total prescriptions</Text>
                          <Text size="xs" fw={500}>{analytics.total_prescriptions}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Prescriptions actives</Text>
                          <Text size="xs" fw={500}>{analytics.active_prescriptions}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Substances contrôlées</Text>
                          <Text size="xs" fw={500}>{analytics.controlled_substances_prescribed}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Interactions détectées</Text>
                          <Text size="xs" fw={500} c="orange">{analytics.drug_interactions_found}</Text>
                        </Group>
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Tendances</Text>
                      <Stack gap="xs">
                        {analytics.prescription_trends.map((trend) => (
                          <Group key={trend.month} justify="space-between">
                            <Text size="xs">{trend.month}</Text>
                            <Group gap="xs">
                              <Text size="xs">{trend.total_prescriptions} total</Text>
                              <Text size="xs" c="dimmed">{trend.new_prescriptions} nouvelles</Text>
                            </Group>
                          </Group>
                        ))}
                      </Stack>
                    </Card>
                  </SimpleGrid>
                ) : (
                  <Text size="sm" c="dimmed" ta="center" p="xl">
                    Aucune donnée analytique disponible
                  </Text>
                )}
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconPrescription size={20} />
          <Text fw={600}>Gestion des Prescriptions</Text>
          {patientName && (
            <Badge color="blue">{patientName}</Badge>
          )}
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Accès rapide aux prescriptions et historique médicamenteux
          </Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconPrescription size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Prescriptions</Text>
                <Text size="sm" fw={600}>{prescriptions.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconShieldCheck size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Actives</Text>
                <Text size="sm" fw={600}>{activePrescriptions.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconRefresh size={16} color="orange" />
              <div>
                <Text size="xs" c="dimmed">Renouvellements</Text>
                <Text size="sm" fw={600}>{pendingRefills.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconAlertTriangle size={16} color="red" />
              <div>
                <Text size="xs" c="dimmed">Interactions</Text>
                <Text size="sm" fw={600}>{drugInteractions.length}</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconChartPie size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab 
              value="prescriptions" 
              leftSection={<IconPrescription size={16} />}
              rightSection={<Badge size="xs" color="blue">{prescriptions.length}</Badge>}
            >
              Prescriptions
            </Tabs.Tab>
            <Tabs.Tab 
              value="history" 
              leftSection={<IconHistory size={16} />}
              rightSection={<Badge size="xs" color="teal">{medicationHistory.length}</Badge>}
            >
              Historique
            </Tabs.Tab>
            <Tabs.Tab 
              value="templates" 
              leftSection={<IconTemplate size={16} />}
              rightSection={<Badge size="xs" color="purple">{prescriptionTemplates.length}</Badge>}
            >
              Templates
            </Tabs.Tab>
            <Tabs.Tab 
              value="refills" 
              leftSection={<IconRefresh size={16} />}
              rightSection={<Badge size="xs" color="orange">{prescriptionRefills.length}</Badge>}
            >
              Renouvellements
            </Tabs.Tab>
            <Tabs.Tab value="analytics" leftSection={<IconChartPie size={16} />}>
              Analytiques
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default PrescriptionsQuickAccess;
