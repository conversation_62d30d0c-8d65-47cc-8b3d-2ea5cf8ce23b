"""
Appointment models for the patient management system.
"""

from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid


class Appointment(models.Model):
    """
    Main appointment model for scheduling patient visits.
    """
    STATUS_CHOICES = [
        ('scheduled', _('Scheduled')),
        ('confirmed', _('Confirmed')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
        ('no_show', _('No Show')),
        ('rescheduled', _('Rescheduled')),
        ('visite_malade', _('Visite de malade')),
        ('visitor_counter', _('Visitor Counter')),
        ('re_diagnose', _('Re-diagnose')),
        ('waiting_list', _('Liste d\'attente')),
    ]

    APPOINTMENT_TYPE_CHOICES = [
        ('consultation', _('Consultation')),
        ('consultation_specialisee', _('Consultation spécialisée')),
        ('follow_up', _('Follow-up')),
        ('emergency', _('Emergency')),
        ('urgence', _('Urgence')),
        ('routine_checkup', _('Routine Checkup')),
        ('controle', _('Contrôle')),
        ('procedure', _('Procedure')),
        ('surgery', _('Surgery')),
        ('chirurgie', _('Chirurgie')),
        ('cleaning', _('Cleaning')),
        ('visite_malade', _('Visite de malade')),
        ('visitor_counter', _('Visitor Counter')),
        ('re_diagnose', _('Re-diagnose')),
        ('other', _('Other')),
    ]

    TITLE_CHOICES = [
        ('mme', _('Mme')),
        ('m', _('M')),
        ('mlle', _('Mlle')),
        ('dr', _('Dr')),
    ]

    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('normal', _('Normal')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Patient and doctor information
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='patient_appointments',
        limit_choices_to={'user_type': 'patient'}
    )
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='doctor_appointments',
        limit_choices_to={'user_type': 'doctor'},
        null=True,
        blank=True
    )
    
    # Enhanced doctor field that can handle both doctors and assistants
    doctor_or_assistant = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='staff_appointments',
        limit_choices_to={'user_type__in': ['doctor', 'assistant']},
        null=True,
        blank=True,
        help_text=_("Doctor or assistant responsible for this appointment")
    )
    
    # Appointment details
    patient_title = models.CharField(
        max_length=10,
        choices=TITLE_CHOICES,
        blank=True,
        null=True,
        help_text=_("Patient title (Mme, M, Mlle, Dr)")
    )
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    appointment_type = models.CharField(
        max_length=30,
        choices=APPOINTMENT_TYPE_CHOICES,
        default='consultation'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal'
    )
    
    # Date and time
    appointment_date = models.DateField()
    appointment_time = models.TimeField()
    end_time = models.TimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(
        default=30,  # type: ignore
        validators=[MinValueValidator(15), MaxValueValidator(480)]
    )
    consultation_duration = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text=str(_("Consultation duration in minutes (alternative to duration_minutes)"))
    )
    
    # Location and resources
    room = models.CharField(max_length=50, blank=True, null=True)
    resource_id = models.CharField(max_length=50, blank=True, null=True, help_text=_("Resource/Room ID for calendar"))
    equipment_needed = models.TextField(blank=True, null=True)

    # Frontend-specific fields
    color = models.CharField(max_length=7, blank=True, null=True, help_text=_("Hex color code for calendar display"))
    event_type = models.CharField(max_length=50, blank=True, null=True, help_text=_("Frontend event type"))
    is_waiting_list = models.BooleanField(default=False, help_text=str(_("Is this appointment in waiting list")))  # type: ignore
    is_active = models.BooleanField(default=False, help_text=str(_("Is this appointment currently active/in progress")))  # type: ignore
    is_in_presentation_room = models.BooleanField(default=False, help_text=str(_("Is this appointment in presentation room")))  # type: ignore
    is_in_history_journal = models.BooleanField(default=False, help_text=str(_("Is this appointment completed and in history journal")))  # type: ignore

    # Additional patient information for quick access
    patient_phone = models.CharField(max_length=20, blank=True, null=True, help_text=_("Patient phone number"))
    patient_address = models.TextField(blank=True, null=True, help_text=_("Patient address"))

    # Consultation details
    consultation_type = models.CharField(max_length=100, blank=True, null=True, help_text=_("Type of consultation"))
    doctor_assigned = models.CharField(max_length=100, blank=True, null=True, help_text=_("Assigned doctor name"))
    agenda = models.CharField(max_length=100, blank=True, null=True, help_text=_("Agenda type"))
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    reason_for_visit = models.TextField(blank=True, null=True)
    symptoms = models.TextField(blank=True, null=True)

    # Comment fields
    comment = models.TextField(blank=True, null=True, help_text=_("General comment"))
    Commentairelistedattente = models.TextField(blank=True, null=True, help_text=_("Waiting list comment"))
    
    # Billing and insurance
    estimated_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    insurance_covered = models.BooleanField(default=False)  # type: ignore

    # Additional patient fields
    gender = models.CharField(
        max_length=10,
        choices=[
            ('Homme', _('Homme')),
            ('Femme', _('Femme')),
            ('Enfant', _('Enfant')),
            ('Autre', _('Autre'))
        ],
        blank=True,
        null=True,
        help_text=_("Patient gender")
    )
    etat_civil = models.CharField(
        max_length=20,
        choices=[
            ('Célibataire', _('Célibataire')),
            ('Marié(e)', _('Marié(e)')),
            ('Divorcé(e)', _('Divorcé(e)')),
            ('Veuf(ve)', _('Veuf(ve)')),
            ('Autre chose', _('Autre chose'))
        ],
        blank=True,
        null=True,
        help_text=_("Civil status")
    )
    cin = models.CharField(max_length=20, blank=True, null=True, help_text=_("National ID number"))
    social_security = models.CharField(max_length=30, blank=True, null=True, help_text=_("Social security number"))
    profession = models.CharField(max_length=100, blank=True, null=True, help_text=_("Patient profession"))
    birth_place = models.CharField(max_length=100, blank=True, null=True, help_text=_("Place of birth"))
    father_name = models.CharField(max_length=100, blank=True, null=True, help_text=_("Father's name"))
    mother_name = models.CharField(max_length=100, blank=True, null=True, help_text=_("Mother's name"))
    blood_group = models.CharField(
        max_length=5,
        choices=[
            ('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'),
            ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')
        ],
        blank=True,
        null=True,
        help_text=_("Blood group")
    )
    allergies = models.TextField(blank=True, null=True, help_text=_("Known allergies"))

    # Event and notification settings
    event_resource_id = models.CharField(max_length=50, blank=True, null=True, help_text=_("Event resource identifier"))
    event_type = models.CharField(
        max_length=20,
        choices=[
            ('visit', _('Visit')),
            ('visitor-counter', _('Visitor Counter')),
            ('completed', _('Completed')),
            ('diagnosis', _('Diagnosis'))
        ],
        blank=True,
        null=True,
        help_text=_("Event type")
    )
    add_to_waiting_list = models.BooleanField(default=False, help_text=str(_("Add to waiting list")))  # type: ignore
    checked_appel_video = models.BooleanField(default=False, help_text=str(_("Video call reminder enabled")))  # type: ignore
    checked_rappel_sms = models.BooleanField(default=False, help_text=str(_("SMS reminder enabled")))  # type: ignore
    checked_rappel_email = models.BooleanField(default=False, help_text=str(_("Email reminder enabled")))  # type: ignore

    # Reminders and notifications
    reminder_sent = models.BooleanField(default=False)  # type: ignore
    reminder_sent_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_appointments'
    )

    class Meta:
        verbose_name = _('Appointment')
        verbose_name_plural = _('Appointments')
        ordering = ['appointment_date', 'appointment_time']
        indexes = [
            models.Index(fields=['appointment_date', 'appointment_time']),
            models.Index(fields=['patient']),
            models.Index(fields=['doctor']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_full_name method on ForeignKey
        patient_name = self.patient.get_full_name() if self.patient else "Unknown Patient"  # type: ignore
        return f"{patient_name} - {self.title}"

    @property
    def appointment_datetime(self):
        """Combine date and time into a single datetime object."""
        if self.appointment_date and self.appointment_time:
            naive_datetime = timezone.datetime.combine(
                self.appointment_date,  # type: ignore  # This is the actual date value
                self.appointment_time   # type: ignore  # This is the actual time value
            )
            # Make it timezone-aware
            return timezone.make_aware(naive_datetime)
        return None

    @property
    def is_past(self):
        """Check if the appointment is in the past."""
        appointment_dt = self.appointment_datetime
        if appointment_dt:
            return appointment_dt < timezone.now()
        return False

    @property
    def is_today(self):
        """Check if the appointment is today."""
        return self.appointment_date == timezone.now().date()

    def save(self, *args, **kwargs):
        """Override save to calculate end time if not provided."""
        if not self.end_time and self.appointment_time and self.duration_minutes:
            start_datetime = timezone.datetime.combine(
                timezone.now().date(), 
                self.appointment_time  # type: ignore  # This is the actual time value
            )
            # duration_minutes is the actual integer value
            end_datetime = start_datetime + timezone.timedelta(minutes=int(self.duration_minutes))  # type: ignore
            self.end_time = end_datetime.time()
        
        super().save(*args, **kwargs)


class DentistryAppointment(models.Model):
    """
    Specialized appointment model for dentistry services.
    """
    DENTAL_PROCEDURE_CHOICES = [
        ('cleaning', _('Cleaning')),
        ('filling', _('Filling')),
        ('crown', _('Crown')),
        ('root_canal', _('Root Canal')),
        ('extraction', _('Extraction')),
        ('implant', _('Implant')),
        ('orthodontics', _('Orthodontics')),
        ('whitening', _('Whitening')),
        ('checkup', _('Checkup')),
        ('emergency', _('Emergency')),
        ('other', _('Other')),
    ]

    TOOTH_QUADRANT_CHOICES = [
        ('upper_right', _('Upper Right')),
        ('upper_left', _('Upper Left')),
        ('lower_right', _('Lower Right')),
        ('lower_left', _('Lower Left')),
        ('full_mouth', _('Full Mouth')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Link to main appointment
    appointment = models.OneToOneField(
        Appointment,
        on_delete=models.CASCADE,
        related_name='dentistry_details'
    )
    
    # Dental-specific information
    procedure_type = models.CharField(
        max_length=20,
        choices=DENTAL_PROCEDURE_CHOICES,
        default='checkup'
    )
    tooth_numbers = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text=_("Comma-separated tooth numbers (e.g., 1,2,3)")
    )
    quadrant = models.CharField(
        max_length=20,
        choices=TOOTH_QUADRANT_CHOICES,
        blank=True,
        null=True
    )
    
    # Treatment details
    anesthesia_required = models.BooleanField(default=False)  # type: ignore
    anesthesia_type = models.CharField(max_length=50, blank=True, null=True)
    pre_medication = models.TextField(blank=True, null=True)
    post_care_instructions = models.TextField(blank=True, null=True)
    
    # Follow-up
    follow_up_required = models.BooleanField(default=False)  # type: ignore
    follow_up_date = models.DateField(null=True, blank=True)
    follow_up_notes = models.TextField(blank=True, null=True)
    
    # Materials and costs
    materials_used = models.TextField(blank=True, null=True)
    lab_work_required = models.BooleanField(default=False)  # type: ignore
    lab_instructions = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Dentistry Appointment')
        verbose_name_plural = _('Dentistry Appointments')

    def __str__(self):
        patient_name = "Unknown Patient"
        if self.appointment and self.appointment.patient:  # type: ignore
            patient_name = self.appointment.patient.get_full_name()  # type: ignore
        return f"Dental: {self.procedure_type} - {patient_name}"


class AppointmentReminder(models.Model):
    """
    Model for managing appointment reminders.
    """
    REMINDER_TYPE_CHOICES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('phone', _('Phone Call')),
        ('push', _('Push Notification')),
    ]

    REMINDER_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='reminders'
    )
    
    reminder_type = models.CharField(
        max_length=10,
        choices=REMINDER_TYPE_CHOICES,
        default='email'
    )
    status = models.CharField(
        max_length=10,
        choices=REMINDER_STATUS_CHOICES,
        default='pending'
    )
    
    # Timing
    send_at = models.DateTimeField()
    sent_at = models.DateTimeField(null=True, blank=True)
    
    # Content
    subject = models.CharField(max_length=200, blank=True, null=True)
    message = models.TextField()
    
    # Tracking
    attempts = models.PositiveIntegerField(default=0)  # type: ignore
    error_message = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Appointment Reminder')
        verbose_name_plural = _('Appointment Reminders')
        ordering = ['send_at']

    def __str__(self):
        appointment_title = "Unknown Appointment"
        if self.appointment and self.appointment.title:  # type: ignore
            appointment_title = self.appointment.title  # type: ignore
        return f"{self.reminder_type} reminder for {appointment_title}"


class DoctorPause(models.Model):
    """
    Model for managing doctor and assistant breaks and pauses in the schedule.
    """
    ROOM_CHOICES = [
        ('room-a', _('Room A')),
        ('room-b', _('Room B')),
        ('room-c', _('Room C')),
        ('other', _('Other')),
    ]
    
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pauses',
        limit_choices_to={'user_type__in': ['doctor', 'assistant']},
        help_text=_("Doctor or assistant for this pause")
    )
    title = models.CharField(max_length=200, help_text=_("Title of the pause/break"))

    # Date and time
    date_from = models.DateTimeField(help_text=_("Start date and time of the pause"))
    date_to = models.DateTimeField(help_text=_("End date and time of the pause"))

    # Room assignment for lunch breaks
    room = models.CharField(
        max_length=20,
        choices=ROOM_CHOICES,
        blank=True,
        null=True,
        help_text=_("Room where the pause takes place (especially for lunch breaks)")
    )
    
    # Resource ID mapping for calendar integration
    resource_id = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        help_text=_("Resource/Room ID for calendar integration")
    )

    # Additional information
    notes = models.TextField(blank=True, null=True, help_text=_("Additional notes about the pause"))
    is_recurring = models.BooleanField(default=False, help_text=str(_("Is this a recurring pause")))  # type: ignore
    
    # Visual customization
    color = models.CharField(
        max_length=7, 
        blank=True, 
        null=True, 
        default='#15AABF',
        help_text=_("Hex color code for calendar display")
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_pauses'
    )

    class Meta:
        verbose_name = _('Staff Pause')
        verbose_name_plural = _('Staff Pauses')
        ordering = ['date_from']
        indexes = [
            models.Index(fields=['doctor', 'date_from']),
            models.Index(fields=['date_from', 'date_to']),
        ]

    def __str__(self):
        staff_name = self.doctor.get_full_name() if self.doctor else "Unknown Staff Member"  # type: ignore
        # Type ignore for basedpyright issues with strftime
        if self.date_from:
            try:
                date_str = self.date_from.strftime('%Y-%m-%d %H:%M')  # type: ignore
            except AttributeError:
                date_str = str(self.date_from)
        else:
            date_str = "Unknown Date"
        return f"{self.title} - {staff_name} ({date_str})"

    @property
    def duration_minutes(self):
        """Calculate duration in minutes."""
        # Type ignore for basedpyright issues with date operations
        if self.date_from and self.date_to:
            try:
                delta = self.date_to - self.date_from  # type: ignore
                return int(delta.total_seconds() / 60)
            except (AttributeError, TypeError):
                return 0
        return 0

    def clean(self):
        """Validate that date_to is after date_from."""
        from django.core.exceptions import ValidationError
        if self.date_from and self.date_to and self.date_to <= self.date_from:
            raise ValidationError(_('End date must be after start date.'))


class PatientList(models.Model):
    """
    Model for managing patient lists, grouping patients for specific purposes
    such as waiting lists, department assignments, or care teams.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, help_text=_("Name of the patient list"))
    description = models.TextField(blank=True, null=True, help_text=_("Description of the list"))
    
    # Patient grouping
    patients = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='patient_lists',
        limit_choices_to={'user_type': 'patient'},
        blank=True,
        help_text=_("Patients in this list")
    )
    
    # List type and categorization
    list_type = models.CharField(
        max_length=50,
        choices=[
            ('waiting_list', _('Waiting List')),
            ('department', _('Department')),
            ('care_team', _('Care Team')),
            ('custom', _('Custom')),
        ],
        default='custom',
        help_text=_("Type of patient list")
    )
    
    # Status and visibility
    is_active = models.BooleanField(default=True, help_text=str(_("Is this list active")))  # type: ignore
    is_public = models.BooleanField(default=False, help_text=str(_("Is this list publicly visible")))  # type: ignore
    
    # Ownership and permissions
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_patient_lists'
    )
    assigned_staff = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='assigned_patient_lists',
        limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']},
        blank=True,
        help_text=_("Staff members assigned to manage this list")
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Patient List')
        verbose_name_plural = _('Patient Lists')
        ordering = ['name']
        indexes = [
            models.Index(fields=['list_type', 'is_active']),
            models.Index(fields=['created_by']),
        ]

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_FOO_display methods
        list_type_display = self.get_list_type_display() if hasattr(self, 'get_list_type_display') else self.list_type  # type: ignore
        # Type ignore for basedpyright not recognizing ManyToManyField count method
        patient_count = self.patients.count() if hasattr(self.patients, 'count') else 0  # type: ignore
        return f"{self.name} ({list_type_display}) - {patient_count} patients"


class ActiveVisit(models.Model):
    """
    Model for tracking active patient visits in real-time.
    This represents patients who are currently in the facility.
    """
    VISIT_STATUS_CHOICES = [
        ('checked_in', _('Checked In')),
        ('in_waiting', _('In Waiting Area')),
        ('being_seen', _('Being Seen by Staff')),
        ('in_procedure', _('In Procedure/Exam')),
        ('checkout', _('Ready for Checkout')),
        ('completed', _('Visit Completed')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Patient and visit information
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='active_visits',
        limit_choices_to={'user_type': 'patient'}
    )
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='active_visits',
        null=True,
        blank=True,
        help_text=_("Associated appointment if scheduled")
    )
    
    # Visit tracking
    visit_status = models.CharField(
        max_length=20,
        choices=VISIT_STATUS_CHOICES,
        default='checked_in',
        help_text=_("Current status of the visit")
    )
    
    # Location tracking
    current_location = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text=_("Current location of the patient")
    )
    room = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text=_("Assigned room")
    )
    
    # Staff assignment
    assigned_staff = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_active_visits',
        limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']},
        help_text=_("Staff member currently responsible")
    )
    
    # Timing
    check_in_time = models.DateTimeField(auto_now_add=True, help_text=_("When patient checked in"))
    status_change_time = models.DateTimeField(auto_now=True, help_text=_("Last status change time"))
    estimated_duration = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text=str(_("Estimated duration in minutes"))
    )
    
    # Priority and notes
    priority = models.CharField(
        max_length=10,
        choices=[
            ('low', _('Low')),
            ('normal', _('Normal')),
            ('high', _('High')),
            ('urgent', _('Urgent')),
        ],
        default='normal',
        help_text=_("Visit priority")
    )
    notes = models.TextField(blank=True, null=True, help_text=_("Additional notes about the visit"))
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Active Visit')
        verbose_name_plural = _('Active Visits')
        ordering = ['-check_in_time']
        indexes = [
            models.Index(fields=['patient', 'visit_status']),
            models.Index(fields=['visit_status']),
            models.Index(fields=['assigned_staff']),
            models.Index(fields=['check_in_time']),
        ]

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_full_name method on ForeignKey
        patient_name = self.patient.get_full_name() if self.patient else "Unknown Patient"  # type: ignore
        # Type ignore for basedpyright not recognizing get_FOO_display methods
        visit_status_display = self.get_visit_status_display() if hasattr(self, 'get_visit_status_display') else self.visit_status  # type: ignore
        return f"{patient_name} - {visit_status_display}"

    @property
    def is_active(self):
        """Check if the visit is still active (not completed)."""
        return self.visit_status != 'completed'

    @property
    def wait_time_minutes(self):
        """Calculate current wait time in minutes."""
        if self.check_in_time:
            from django.utils import timezone
            # Type ignore for basedpyright operator issue
            delta = timezone.now() - self.check_in_time  # type: ignore
            return int(delta.total_seconds() / 60)
        return 0


class PresenceList(models.Model):
    """
    Model for tracking staff presence and availability.
    This helps in managing staff schedules and patient assignments.
    """
    PRESENCE_STATUS_CHOICES = [
        ('present', _('Present')),
        ('absent', _('Absent')),
        ('break', _('On Break')),
        ('lunch', _('On Lunch')),
        ('meeting', _('In Meeting')),
        ('leave', _('On Leave')),
        ('unavailable', _('Unavailable')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Staff member
    staff_member = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='presence_records',
        limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}
    )
    
    # Presence information
    status = models.CharField(
        max_length=20,
        choices=PRESENCE_STATUS_CHOICES,
        default='present',
        help_text=_("Current presence status")
    )
    
    # Location and assignment
    location = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text=_("Current location")
    )
    assigned_room = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text=_("Assigned room or area")
    )
    
    # Timing
    start_time = models.DateTimeField(auto_now_add=True, help_text=_("When this status started"))
    end_time = models.DateTimeField(blank=True, null=True, help_text=_("When this status ended"))
    
    # Availability for patients
    is_available_for_patients = models.BooleanField(
        default=True,  # type: ignore
        help_text=str(_("Is this staff member available for patient care"))
    )
    
    # Notes
    notes = models.TextField(blank=True, null=True, help_text=_("Additional notes"))
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Presence List Entry')
        verbose_name_plural = _('Presence List Entries')
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['staff_member', 'status']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]

    def __str__(self):
        staff_name = self.staff_member.get_full_name() if self.staff_member else "Unknown Staff"  # type: ignore
        # Type ignore for basedpyright not recognizing get_FOO_display methods
        status_display = self.get_status_display() if hasattr(self, 'get_status_display') else self.status  # type: ignore
        return f"{staff_name} - {status_display}"

    def save(self, *args, **kwargs):
        """Override save to set end_time when status changes to absent or leave."""
        # If this is an update and status is changing to absent/leave, set end_time
        if self.pk and self.status in ['absent', 'leave']:
            if not self.end_time:
                from django.utils import timezone
                self.end_time = timezone.now()
        super().save(*args, **kwargs)

    @property
    def is_current(self):
        """Check if this is the current presence status."""
        return self.end_time is None

    @property
    def duration_minutes(self):
        """Calculate duration in minutes."""
        from django.utils import timezone
        end = self.end_time or timezone.now()
        if self.start_time:
            # Type ignore for basedpyright operator issue
            delta = end - self.start_time  # type: ignore
            return int(delta.total_seconds() / 60)
        return 0


class HistoryJournal(models.Model):
    """
    Model for maintaining a detailed history journal for patients.
    This tracks all significant events in a patient's medical journey.
    """
    JOURNAL_ENTRY_TYPES = [
        ('appointment', _('Appointment')),
        ('visit', _('Visit')),
        ('procedure', _('Procedure')),
        ('diagnosis', _('Diagnosis')),
        ('medication', _('Medication')),
        ('test', _('Test/Lab Result')),
        ('note', _('Clinical Note')),
        ('alert', _('Alert')),
        ('communication', _('Communication')),
        ('other', _('Other')),
    ]

    JOURNAL_ENTRY_CATEGORIES = [
        ('administrative', _('Administrative')),
        ('clinical', _('Clinical')),
        ('financial', _('Financial')),
        ('communication', _('Communication')),
        ('other', _('Other')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Patient and related appointment
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='history_journal_entries',
        limit_choices_to={'user_type': 'patient'}
    )
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='history_journal_entries',
        help_text=_("Related appointment if applicable")
    )
    
    # Entry details
    entry_type = models.CharField(
        max_length=20,
        choices=JOURNAL_ENTRY_TYPES,
        default='note',
        help_text=_("Type of journal entry")
    )
    category = models.CharField(
        max_length=20,
        choices=JOURNAL_ENTRY_CATEGORIES,
        default='clinical',
        help_text=_("Category of the entry")
    )
    
    # Content
    title = models.CharField(max_length=200, help_text=_("Title of the journal entry"))
    description = models.TextField(help_text=_("Detailed description of the entry"))
    summary = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text=_("Brief summary of the entry")
    )
    
    # Staff involved
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_journal_entries',
        limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}
    )
    related_staff = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='related_journal_entries',
        limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']},
        blank=True,
        help_text=_("Other staff members involved")
    )
    
    # Visibility and access
    is_private = models.BooleanField(
        default=False,  # type: ignore
        help_text=str(_("Is this entry private to the creator"))
    )
    is_important = models.BooleanField(
        default=False,  # type: ignore
        help_text=str(_("Mark as important entry"))
    )
    
    # References to other systems
    related_document = models.ForeignKey(
        'patients.PatientDocument',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        help_text=_("Related document if applicable")
    )
    related_attachment = models.ForeignKey(
        'patients.PatientAttachment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        help_text=_("Related attachment if applicable")
    )
    
    # Timestamps
    event_date = models.DateTimeField(help_text=_("Date and time of the event"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('History Journal Entry')
        verbose_name_plural = _('History Journal Entries')
        ordering = ['-event_date']
        indexes = [
            models.Index(fields=['patient', 'entry_type']),
            models.Index(fields=['patient', 'category']),
            models.Index(fields=['event_date']),
            models.Index(fields=['created_by']),
        ]

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_full_name method on ForeignKey
        patient_name = self.patient.get_full_name() if self.patient else "Unknown Patient"  # type: ignore
        return f"{patient_name} - {self.title}"

    @property
    def formatted_event_date(self):
        """Return formatted event date."""
        if self.event_date:
            # Type ignore for basedpyright not recognizing strftime method on DateTimeField
            return self.event_date.strftime('%Y-%m-%d %H:%M')  # type: ignore
        return "Unknown Date"
