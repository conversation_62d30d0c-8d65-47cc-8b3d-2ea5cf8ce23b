# Generated by Django 4.2.7 on 2025-08-31 10:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0002_auto_20250831_1115'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='Commentairelistedattente',
            field=models.TextField(blank=True, help_text='Waiting list comment', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='add_to_waiting_list',
            field=models.BooleanField(default=False, help_text='Add to waiting list'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='agenda',
            field=models.CharField(blank=True, help_text='Agenda type', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='allergies',
            field=models.TextField(blank=True, help_text='Known allergies', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='birth_place',
            field=models.CharField(blank=True, help_text='Place of birth', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='blood_group',
            field=models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], help_text='Blood group', max_length=5, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_appel_video',
            field=models.BooleanField(default=False, help_text='Video call reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_rappel_email',
            field=models.BooleanField(default=False, help_text='Email reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_rappel_sms',
            field=models.BooleanField(default=False, help_text='SMS reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='cin',
            field=models.CharField(blank=True, help_text='National ID number', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='color',
            field=models.CharField(blank=True, help_text='Hex color code for calendar display', max_length=7, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='comment',
            field=models.TextField(blank=True, help_text='General comment', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='consultation_duration',
            field=models.PositiveIntegerField(blank=True, help_text='Consultation duration in minutes (alternative to duration_minutes)', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='consultation_type',
            field=models.CharField(blank=True, help_text='Type of consultation', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='doctor_assigned',
            field=models.CharField(blank=True, help_text='Assigned doctor name', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='doctor_or_assistant',
            field=models.ForeignKey(blank=True, help_text='Doctor or assistant responsible for this appointment', limit_choices_to={'user_type__in': ['doctor', 'assistant']}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='staff_appointments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='appointment',
            name='etat_civil',
            field=models.CharField(blank=True, choices=[('Célibataire', 'Célibataire'), ('Marié(e)', 'Marié(e)'), ('Divorcé(e)', 'Divorcé(e)'), ('Veuf(ve)', 'Veuf(ve)'), ('Autre chose', 'Autre chose')], help_text='Civil status', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='event_resource_id',
            field=models.CharField(blank=True, help_text='Event resource identifier', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='event_type',
            field=models.CharField(blank=True, choices=[('visit', 'Visit'), ('visitor-counter', 'Visitor Counter'), ('completed', 'Completed'), ('diagnosis', 'Diagnosis')], help_text='Event type', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='father_name',
            field=models.CharField(blank=True, help_text="Father's name", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='gender',
            field=models.CharField(blank=True, choices=[('Homme', 'Homme'), ('Femme', 'Femme'), ('Enfant', 'Enfant'), ('Autre', 'Autre')], help_text='Patient gender', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='is_active',
            field=models.BooleanField(default=False, help_text='Is this appointment currently active/in progress'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='is_waiting_list',
            field=models.BooleanField(default=False, help_text='Is this appointment in waiting list'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='mother_name',
            field=models.CharField(blank=True, help_text="Mother's name", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='patient_address',
            field=models.TextField(blank=True, help_text='Patient address', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='patient_phone',
            field=models.CharField(blank=True, help_text='Patient phone number', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='patient_title',
            field=models.CharField(blank=True, choices=[('mme', 'Mme'), ('m', 'M'), ('mlle', 'Mlle'), ('dr', 'Dr')], help_text='Patient title (Mme, M, Mlle, Dr)', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='profession',
            field=models.CharField(blank=True, help_text='Patient profession', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='resource_id',
            field=models.CharField(blank=True, help_text='Resource/Room ID for calendar', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='social_security',
            field=models.CharField(blank=True, help_text='Social security number', max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='appointment_type',
            field=models.CharField(choices=[('consultation', 'Consultation'), ('consultation_specialisee', 'Consultation spécialisée'), ('follow_up', 'Follow-up'), ('emergency', 'Emergency'), ('urgence', 'Urgence'), ('routine_checkup', 'Routine Checkup'), ('controle', 'Contrôle'), ('procedure', 'Procedure'), ('surgery', 'Surgery'), ('chirurgie', 'Chirurgie'), ('cleaning', 'Cleaning'), ('visite_malade', 'Visite de malade'), ('visitor_counter', 'Visitor Counter'), ('re_diagnose', 'Re-diagnose'), ('other', 'Other')], default='consultation', max_length=30),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='status',
            field=models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show'), ('rescheduled', 'Rescheduled'), ('visite_malade', 'Visite de malade'), ('visitor_counter', 'Visitor Counter'), ('re_diagnose', 'Re-diagnose'), ('waiting_list', "Liste d'attente")], default='scheduled', max_length=20),
        ),
        migrations.AlterField(
            model_name='doctorpause',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AddIndex(
            model_name='activevisit',
            index=models.Index(fields=['patient', 'visit_status'], name='appointment_patient_f1b2ca_idx'),
        ),
        migrations.AddIndex(
            model_name='activevisit',
            index=models.Index(fields=['visit_status'], name='appointment_visit_s_b8d0ba_idx'),
        ),
        migrations.AddIndex(
            model_name='activevisit',
            index=models.Index(fields=['assigned_staff'], name='appointment_assigne_3d904b_idx'),
        ),
        migrations.AddIndex(
            model_name='activevisit',
            index=models.Index(fields=['check_in_time'], name='appointment_check_i_53132f_idx'),
        ),
        migrations.AddIndex(
            model_name='doctorpause',
            index=models.Index(fields=['doctor', 'date_from'], name='appointment_doctor__6554be_idx'),
        ),
        migrations.AddIndex(
            model_name='doctorpause',
            index=models.Index(fields=['date_from', 'date_to'], name='appointment_date_fr_3ed9f6_idx'),
        ),
        migrations.AddIndex(
            model_name='historyjournal',
            index=models.Index(fields=['patient', 'entry_type'], name='appointment_patient_c5600f_idx'),
        ),
        migrations.AddIndex(
            model_name='historyjournal',
            index=models.Index(fields=['patient', 'category'], name='appointment_patient_02c3fb_idx'),
        ),
        migrations.AddIndex(
            model_name='historyjournal',
            index=models.Index(fields=['event_date'], name='appointment_event_d_384fc0_idx'),
        ),
        migrations.AddIndex(
            model_name='historyjournal',
            index=models.Index(fields=['created_by'], name='appointment_created_1ca2e5_idx'),
        ),
        migrations.AddIndex(
            model_name='patientlist',
            index=models.Index(fields=['list_type', 'is_active'], name='appointment_list_ty_c93e41_idx'),
        ),
        migrations.AddIndex(
            model_name='patientlist',
            index=models.Index(fields=['created_by'], name='appointment_created_a1ab16_idx'),
        ),
        migrations.AddIndex(
            model_name='presencelist',
            index=models.Index(fields=['staff_member', 'status'], name='appointment_staff_m_3749fb_idx'),
        ),
        migrations.AddIndex(
            model_name='presencelist',
            index=models.Index(fields=['status'], name='appointment_status_84c998_idx'),
        ),
        migrations.AddIndex(
            model_name='presencelist',
            index=models.Index(fields=['start_time'], name='appointment_start_t_723ac8_idx'),
        ),
    ]
