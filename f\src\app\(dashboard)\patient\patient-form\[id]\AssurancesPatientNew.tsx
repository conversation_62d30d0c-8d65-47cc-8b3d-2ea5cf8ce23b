import { useState } from 'react';
import cx from 'clsx';
import Icon from '@mdi/react';
import {  mdiPlusCircle, mdiClose } from '@mdi/js';
import classes from './TableScrollArea.module.css';
import {
  Table,
  ScrollArea,
  ActionIcon,
  Tooltip,
  Text,
  Group,
  Title,
  Badge,
  Stack,
  Paper,
  Button,
  Divider
} from '@mantine/core';
import {
  IconStar,
  IconStarOff,
  IconPencil,
  IconTrash,
  IconShield
} from '@tabler/icons-react';
import { Patient } from '@/types/typesCalendarPatient';
import { PatientInsurance } from '@/services/patientService';

interface InsuranceTableProps {
  patient: Patient;
  insurances: PatientInsurance[];
  onClose: () => void;
}

export function InsuranceTable({
  patient,
  insurances,
  onClose,
}: InsuranceTableProps) {
  const [scrolled, setScrolled] = useState(false);

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Invalid';
    }
  };

  // Check if insurance is active
  const isInsuranceActive = (insurance: PatientInsurance) => {
    if (!insurance.expiry_date) return true;
    return new Date(insurance.expiry_date) > new Date();
  };

  // Get status color
  const getStatusColor = (insurance: PatientInsurance) => {
    if (!isInsuranceActive(insurance)) return 'red';
    if (insurance.expiry_date) {
      const expiryDate = new Date(insurance.expiry_date);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      if (expiryDate <= thirtyDaysFromNow) return 'yellow';
    }
    return 'green';
  };

  const rows = insurances.map((insurance, index) => (
    <Table.Tr key={insurance.id || index}>
      <Table.Td>
        <Group gap="sm">
          <IconShield size={16} color="#228be6" />
          <div>
            <Text fw={500} size="sm">{insurance.provider_name}</Text>
            <Text size="xs" c="dimmed">{insurance.policy_number}</Text>
          </div>
        </Group>
      </Table.Td>
      
      <Table.Td>
        <Text size="sm">{insurance.coverage_type || 'Standard'}</Text>
      </Table.Td>
      
      <Table.Td>
        <Group gap="xs">
          {insurance.medical_coverage && (
            <Badge size="xs" color="blue" variant="light">Medical</Badge>
          )}
          {insurance.dental_coverage && (
            <Badge size="xs" color="green" variant="light">Dental</Badge>
          )}
        </Group>
      </Table.Td>
      
      <Table.Td>
        <Text size="sm">{formatDate(insurance.effective_date)}</Text>
      </Table.Td>
      
      <Table.Td>
        <Text size="sm">{formatDate(insurance.expiry_date)}</Text>
      </Table.Td>
      
      <Table.Td>
        <Badge 
          size="sm" 
          color={getStatusColor(insurance)}
          variant="light"
        >
          {!isInsuranceActive(insurance) ? 'Expired' : 'Active'}
        </Badge>
      </Table.Td>
      
      <Table.Td>
        <Group gap="xs">
          <Tooltip label="Set as primary">
            <ActionIcon
              variant="light"
              color={insurance.is_active ? 'yellow' : 'gray'}
              size="sm"
            >
              {insurance.is_active ? <IconStar size={14} /> : <IconStarOff size={14} />}
            </ActionIcon>
          </Tooltip>
          
          <Tooltip label="Edit insurance">
            <ActionIcon variant="light" color="blue" size="sm">
              <IconPencil size={14} />
            </ActionIcon>
          </Tooltip>
          
          <Tooltip label="Delete insurance">
            <ActionIcon variant="light" color="red" size="sm">
              <IconTrash size={14} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <Paper p="md" h="100%">
      <Stack gap="md" h="100%">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={4}>Insurance Details</Title>
            <Text size="sm" c="dimmed">
              {patient?.first_name} {patient?.last_name}
            </Text>
          </div>
          <ActionIcon variant="light" onClick={onClose}>
            <Icon path={mdiClose} size={0.8} />
          </ActionIcon>
        </Group>

        <Divider />

        {/* Summary */}
        <Paper p="sm" bg="gray.0">
          <Group justify="space-between">
            <div>
              <Text fw={600} size="sm">Insurance Summary</Text>
              <Text size="xs" c="dimmed">
                {insurances.filter(ins => isInsuranceActive(ins)).length} active policies
              </Text>
            </div>
            <Badge 
              color={insurances.some(ins => isInsuranceActive(ins)) ? 'green' : 'red'}
              variant="light"
            >
              {insurances.some(ins => isInsuranceActive(ins)) ? 'Insured' : 'No Coverage'}
            </Badge>
          </Group>
        </Paper>

        {/* Table */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          {insurances.length === 0 ? (
            <Paper p="xl" style={{ textAlign: 'center' }}>
              <Stack align="center" gap="md">
                <IconShield size={48} color="#e0e0e0" />
                <div>
                  <Text fw={500} c="dimmed">No Insurance Policies</Text>
                  <Text size="sm" c="dimmed">
                    No insurance information found for this patient.
                  </Text>
                </div>
              </Stack>
            </Paper>
          ) : (
            <ScrollArea
              h="100%"
              onScrollPositionChange={({ y }) => setScrolled(y !== 0)}
            >
              <Table miw={700}>
                <Table.Thead className={cx(classes.header, { [classes.scrolled]: scrolled })}>
                  <Table.Tr>
                    <Table.Th>Provider</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Coverage</Table.Th>
                    <Table.Th>Start Date</Table.Th>
                    <Table.Th>End Date</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
              </Table>
            </ScrollArea>
          )}
        </div>

        {/* Footer */}
        <Divider />
        <Group justify="space-between">
          <Text size="xs" c="dimmed">
            Last updated: {new Date().toLocaleDateString()}
          </Text>
          <Button
            size="xs"
            variant="light"
            leftSection={<Icon path={mdiPlusCircle} size={0.6} />}
          >
            Add Insurance
          </Button>
        </Group>
      </Stack>
    </Paper>
  );
}
