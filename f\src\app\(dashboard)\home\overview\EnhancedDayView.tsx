'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Calendar, momentLocalizer, View, Event as BigCalendarEvent } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { CalendarEvent } from '../CalendarPatient';

// Configure moment localizer
const localizer = momentLocalizer(moment);

// Configure French locale
moment.locale('fr', {
  months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),
  monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),
  weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),
  weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),
  weekdaysMin: 'Di_Lu_Ma_Me_Je_Ve_Sa'.split('_'),
});

interface EnhancedDayViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick: (date: Date, hour: number, minute?: number, roomId?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEventDrop?: (event: CalendarEvent, start: Date, end: Date) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
}

// Custom event component to show room information
const CustomEvent = ({ event }: { event: BigCalendarEvent }) => {
  const calendarEvent = event as CalendarEvent;
  return (
    <div className="rbc-event-content">
      <div className="font-semibold text-sm">{calendarEvent.title}</div>
      <div className="text-xs opacity-75">
        {calendarEvent.roomId && `Salle: ${calendarEvent.roomId}`}
      </div>
      <div className="text-xs opacity-75">
        {calendarEvent.duration}min
      </div>
    </div>
  );
};

// Custom time slot component (removed - not compatible with current react-big-calendar version)

const EnhancedDayView: React.FC<EnhancedDayViewProps> = ({
  currentDate,
  events,
  onTimeSlotClick,
  onEventClick,
  onEventDrop, // Keep for future use
  onDateChange,
  onNavigate
}) => {
  const [selectedRoom, setSelectedRoom] = useState<string>('all');

  // Room configuration
  const rooms = useMemo(() => [
    { id: 'room-a', name: 'Salle A', color: '#3b82f6' },
    { id: 'room-b', name: 'Salle B', color: '#10b981' },
  ], []);

  // Filter events by selected room
  const filteredEvents = useMemo(() => {
    let filtered = events.filter(event => {
      // Filter by current date
      const eventDate = moment(event.start).format('YYYY-MM-DD');
      const currentDateStr = moment(currentDate).format('YYYY-MM-DD');
      return eventDate === currentDateStr;
    });

    // Filter by room if not 'all'
    if (selectedRoom !== 'all') {
      filtered = filtered.filter(event => event.roomId === selectedRoom);
    }

    return filtered;
  }, [events, currentDate, selectedRoom]);

  // Convert events to react-big-calendar format
  const calendarEvents = useMemo(() => {
    return filteredEvents.map(event => ({
      ...event,
      start: event.start,
      end: event.end || new Date(event.start.getTime() + (event.duration || 30) * 60000),
      resource: event.roomId,
    }));
  }, [filteredEvents]);

  // Event style getter
  const eventStyleGetter = useCallback((event: CalendarEvent) => {
    const room = rooms.find(r => r.id === event.roomId);
    return {
      style: {
        backgroundColor: event.color || room?.color || '#3b82f6',
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '12px',
        padding: '2px 4px',
      }
    };
  }, [rooms]);

  // Handle slot selection
  const handleSelectSlot = useCallback((slotInfo: { start: Date; end: Date; slots: Date[] }) => {
    const { start } = slotInfo;
    const hour = start.getHours();
    const minute = start.getMinutes();
    const roomId = selectedRoom !== 'all' ? selectedRoom : 'room-a';

    onTimeSlotClick(start, hour, minute, roomId);
  }, [onTimeSlotClick, selectedRoom]);

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    onEventClick(event);
  }, [onEventClick]);

  // Note: Drag and drop functionality removed for now due to compatibility issues

  // Custom formats
  const formats = {
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
    },
    dayHeaderFormat: 'dddd DD MMMM YYYY',
  };

  return (
    <div className="enhanced-day-view h-full flex flex-col">
      {/* Room Filter */}
      <div className="flex items-center gap-4 p-4 bg-gray-50 border-b">
        <span className="font-medium text-gray-700">Filtrer par salle:</span>
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedRoom('all')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              selectedRoom === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Toutes les salles
          </button>
          {rooms.map(room => (
            <button
              key={room.id}
              onClick={() => setSelectedRoom(room.id)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                selectedRoom === room.id
                  ? 'text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              style={{
                backgroundColor: selectedRoom === room.id ? room.color : undefined
              }}
            >
              {room.name}
            </button>
          ))}
        </div>
        
        {/* Event count */}
        <div className="ml-auto text-sm text-gray-600">
          {filteredEvents.length} rendez-vous
        </div>
      </div>

      {/* Calendar */}
      <div className="flex-1 p-4">
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          defaultView="day"
          view="day"
          views={['day']}
          date={currentDate}
          onNavigate={(newDate: Date) => {
            if (onDateChange) {
              onDateChange(newDate);
            }
          }}
          step={15} // 15-minute intervals
          timeslots={4} // 4 slots per hour (15 min each)
          min={new Date(2025, 0, 1, 7, 0)} // 7:00 AM
          max={new Date(2025, 0, 1, 22, 0)} // 10:00 PM (to include dinner time)
          formats={formats}
          eventPropGetter={eventStyleGetter}
          onSelectSlot={handleSelectSlot}
          onSelectEvent={handleSelectEvent}
          selectable
          components={{
            event: CustomEvent,
          }}
          messages={{
            allDay: 'Toute la journée',
            previous: 'Précédent',
            next: 'Suivant',
            today: "Aujourd'hui",
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour',
            agenda: 'Agenda',
            date: 'Date',
            time: 'Heure',
            event: 'Événement',
            noEventsInRange: 'Aucun rendez-vous pour cette période.',
          }}
          className="rbc-calendar-enhanced"
        />
      </div>

      {/* Debug Info */}
      <div className="p-2 bg-blue-50 border-t text-xs text-blue-700">
        <strong>🔍 Enhanced Day View Debug:</strong><br/>
        Date: {moment(currentDate).format('YYYY-MM-DD')}<br/>
        Total events: {events.length}<br/>
        Filtered events: {filteredEvents.length}<br/>
        Selected room: {selectedRoom}
      </div>
    </div>
  );
};

export default EnhancedDayView;
