'use client';

import { useState, useEffect } from 'react';
import {
  Paper,
  Title,
  Text,
  Badge,
  Group,

  Alert,
  Divider,
  Button,
  Progress,
  Card,
  ActionIcon,
  Tooltip,
  CopyButton,
  ThemeIcon
} from '@mantine/core';
import { IconCheck, IconAlertCircle, IconCopy, IconCalendar, IconLicense, IconArrowRight, IconDashboard } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import LicenseActivation from './LicenseActivation';

// Define types for better error handling (moved inline to avoid unused interface)

interface License {
  id: number;
  license_number: string;
  formatted_license_number: string;
  status: 'pending' | 'active' | 'expired' | 'revoked';
  issue_date: string;
  expiry_date: string;
  days_until_expiry: number;
  is_active: boolean;
  activation_date: string | null;
  is_renewed: boolean;
}

export default function LicenseInfo() {
  const router = useRouter();
  const [license, setLicense] = useState<License | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showActivation, setShowActivation] = useState(false);

  const fetchLicense = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get('/api/auth/license/');
      setLicense(response.data);
    } catch (err: unknown) {
      console.error('Error fetching license:', err);

      // Type guard to check if error is an API error
      const isApiError = (error: unknown): error is {
        response?: {
          status?: number;
          data?: {
            detail?: string;
            [key: string]: unknown;
          };
        };
        [key: string]: unknown;
      } => {
        return typeof error === 'object' && error !== null && 'response' in error;
      };

      if (isApiError(err) && err.response?.status === 404) {
        // No license found, this is expected for new users
        setLicense(null);
      } else {
        const errorMessage = isApiError(err) && err.response?.data?.detail
          ? err.response.data.detail
          : 'Failed to fetch license information. Please try again later.';
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLicense();
  }, []);

  const handleActivationSuccess = () => {
    setShowActivation(false);
    fetchLicense();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'expired':
        return 'red';
      case 'revoked':
        return 'gray';
      default:
        return 'blue';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Paper p="xl" radius="md" withBorder>
        <Text>Loading license information...</Text>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper p="xl" radius="md" withBorder>
        <Alert
          icon={<IconAlertCircle size="1.1rem" />}
          title="Error"
          color="red"
        >
          {error}
        </Alert>
      </Paper>
    );
  }

  if (!license) {
    return (
      <Paper p="xl" radius="md" withBorder>
        <Alert
          icon={<IconAlertCircle size="1.1rem" />}
          title="No License Found"
          color="yellow"
        >
          You don&apos;t have a license yet. Please contact support to obtain a license.
        </Alert>
      </Paper>
    );
  }

  if (license.status === 'pending' && !showActivation) {
    return (
      <Paper p="xl" radius="md" withBorder>
        <Alert
          icon={<IconAlertCircle size="1.1rem" />}
          title="License Pending Activation"
          color="yellow"
          mb="lg"
        >
          Your license needs to be activated before you can use all features.
        </Alert>

        <Group justify="center">
          <Button
            onClick={() => setShowActivation(true)}
            color="yellow"
          >
            Activate License
          </Button>
        </Group>
      </Paper>
    );
  }

  if (showActivation) {
    return (
      <LicenseActivation
        onActivationSuccess={handleActivationSuccess}
      />
    );
  }

  // Calculate progress percentage for license validity
  const totalDays = 365; // Assuming 1-year license
  const progressPercentage = Math.max(0, Math.min(100, (license.days_until_expiry / totalDays) * 100));

  return (
    <Paper p="xl" radius="md" withBorder>
      <Group justify="space-between" mb="md">
        <Title order={3}>License Information</Title>
        <Badge
          color={getStatusColor(license.status)}
          size="lg"
        >
          {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
        </Badge>
      </Group>

      <Card withBorder p="md" radius="md" mb="xl">
        <Group justify="space-between" mb="xs">
          <Text fw={500}>License Number:</Text>
          <Group gap="xs">
            <Text>{license.formatted_license_number}</Text>
            <CopyButton value={license.license_number} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size="1rem" /> : <IconCopy size="1rem" />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>

        <Divider my="sm" />

        <Group justify="space-between" mb="xs">
          <Text fw={500}>Issue Date:</Text>
          <Text>{formatDate(license.issue_date)}</Text>
        </Group>

        <Group justify="space-between" mb="xs">
          <Text fw={500}>Expiry Date:</Text>
          <Text>{formatDate(license.expiry_date)}</Text>
        </Group>

        {license.activation_date && (
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Activation Date:</Text>
            <Text>{formatDate(license.activation_date)}</Text>
          </Group>
        )}

        {license.is_active && (
          <>
            <Divider my="sm" />
            <Text fw={500} mb="xs">License Validity:</Text>
            <Progress
              value={progressPercentage}
              color={progressPercentage > 50 ? 'green' : progressPercentage > 20 ? 'yellow' : 'red'}
              size="md"
              radius="xl"
              mb="xs"
            />
            <Text ta="center" size="sm">
              {license.days_until_expiry} days remaining
            </Text>
          </>
        )}
      </Card>

      {license.status === 'expired' && (
        <Alert
          icon={<IconAlertCircle size="1.1rem" />}
          title="License Expired"
          color="red"
          mb="lg"
        >
          Your license has expired. Please renew your license to continue using all features.
        </Alert>
      )}

      {license.is_active && license.days_until_expiry < 30 && (
        <Alert
          icon={<IconAlertCircle size="1.1rem" />}
          title="License Expiring Soon"
          color="yellow"
          mb="lg"
        >
          Your license will expire in {license.days_until_expiry} days. Please consider renewing your license.
        </Alert>
      )}

      {license.is_active && (
        <Paper withBorder p="md" radius="md" mt="xl" style={{ backgroundColor: 'rgba(0, 180, 0, 0.05)' }}>
          <Group gap="sm">
            <ThemeIcon color="green" size="lg" radius="xl" variant="light">
              <IconCheck size="1.2rem" />
            </ThemeIcon>
            <div>
              <Text fw={500}>Your license is active</Text>
              <Text size="sm" c="dimmed">
                You have full access to all features of the control panel
              </Text>
            </div>
            <Button
              variant="light"
              color="green"
              style={{ marginLeft: 'auto' }}
              rightSection={<IconArrowRight size="1rem" />}
              onClick={() => router.push('/dashboard')}
            >
              Go To Control Panel
            </Button>
          </Group>
        </Paper>
      )}

      <Group justify="center" mt="xl">
        {license.status === 'expired' && (
          <Button
            leftSection={<IconLicense size="1rem" />}
            color="blue"
          >
            Renew License
          </Button>
        )}

        {license.is_active && license.days_until_expiry < 60 && (
          <Button
            leftSection={<IconCalendar size="1rem" />}
            variant="outline"
            color="blue"
          >
            Extend License
          </Button>
        )}

        {license.is_active && (
          <Button
            leftSection={<IconDashboard size="1rem" />}
            color="brand"
            size="md"
            onClick={() => router.push('/dashboard')}
          >
            Go To Control Panel
          </Button>
        )}
      </Group>


    </Paper>
  );
}
