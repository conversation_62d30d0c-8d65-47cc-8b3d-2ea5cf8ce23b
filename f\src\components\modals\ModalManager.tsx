'use client';
import { ReactNode } from 'react';
import AlertModal from './AlertModal';
import DictionaryModal from './DictionaryModal';
import SpeechRecognitionModal from './SpeechRecognitionModal';
import { useModals, ModalState } from '@/hooks/useModals';

interface TreeNodeChoixMultiple {
  uid: string;
  value: string;
  nodes?: TreeNodeChoixMultiple[];
}

interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface ModalManagerProps {
  children: ReactNode;
  // Props pour AlertModal
  fullName?: string;
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  onAlertSubmit?: (values: AlertFormValues, autoTrigger: boolean) => void;
  // Props pour DictionaryModal
  treeData?: TreeNodeChoixMultiple[];
  onDictionaryValidate?: (selectedValues: string[]) => void;
  // Props pour SpeechRecognitionModal
  onSpeechValidate?: (text: string) => void;
  initialSpeechText?: string;
  // État des modals (optionnel, sinon utilise le hook interne)
  externalModalState?: ModalState;
  externalModalControls?: ReturnType<typeof useModals>;
}

const ModalManager = ({
  children,
  fullName = '',
  staffOptions = [],
  triggerOptions = [],
  onAlertSubmit,
  treeData = [],
  onDictionaryValidate,
  onSpeechValidate,
  initialSpeechText = '',
  externalModalState,
  externalModalControls,
}: ModalManagerProps) => {
  // Utilise soit les contrôles externes, soit crée ses propres contrôles
  const internalModalControls = useModals();
  const modalControls = externalModalControls || internalModalControls;
  const modalState = externalModalState || modalControls.modalState;

  const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
    onAlertSubmit?.(values, autoTrigger);
    modalControls.closeAlertModal();
  };

  const handleDictionaryValidate = (selectedValues: string[]) => {
    onDictionaryValidate?.(selectedValues);
    modalControls.closeDictionaryModal();
  };

  const handleSpeechValidate = (text: string) => {
    onSpeechValidate?.(text);
    modalControls.closeSpeechModal();
  };

  return (
    <>
      {children}
      
      {/* Modal d'alerte */}
      <AlertModal
        opened={modalState.isAlertModalOpen}
        onClose={modalControls.closeAlertModal}
        onSubmit={handleAlertSubmit}
        fullName={fullName}
        staffOptions={staffOptions}
        triggerOptions={triggerOptions}
        onOpenMicrophone={modalControls.openSpeechModal}
        onOpenDictionary={modalControls.openDictionaryModal}
      />

      {/* Modal de dictionnaire */}
      <DictionaryModal
        opened={modalState.isDictionaryModalOpen}
        onClose={modalControls.closeDictionaryModal}
        onValidate={handleDictionaryValidate}
        treeData={treeData}
      />

      {/* Modal de reconnaissance vocale */}
      <SpeechRecognitionModal
        opened={modalState.isSpeechModalOpen}
        onClose={modalControls.closeSpeechModal}
        onValidate={handleSpeechValidate}
        initialText={initialSpeechText}
      />
    </>
  );
};

export default ModalManager;
