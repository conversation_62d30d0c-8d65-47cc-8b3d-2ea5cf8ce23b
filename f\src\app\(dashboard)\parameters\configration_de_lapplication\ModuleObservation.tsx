import React, { useState } from 'react';
import Icon from '@mdi/react';
import { mdiPlus } from '@mdi/js';
import {
  Card,
  Text,
  Tabs,
  Switch,
  Stack,
  Group,
  Table,
  Button,
  ActionIcon,
  Modal,
  TextInput,
  Select,
} from '@mantine/core';
import {
  IconNotebook,
  IconEdit,
  IconTrash,
  IconPlus,
} from '@tabler/icons-react';

// Interface pour la configuration du module
interface ObservationConfig {
  use: boolean;
  useAsDefault: boolean;
  useMinimalEditor: boolean;
  useFilePatientContext: boolean;
  useMedicalPrescription: boolean;
  useReports: boolean;
  reportsRedirect: boolean;
}

// Interface pour les éléments de fiche de synthèse
interface SummaryItem {
  id: string;
  order: number;
  source: string;
  fields: string;
}

// Interface pour les types d'entrée d'observation
interface ObservationEntryType {
  id: string;
  title: string;
  isDefault: boolean;
  isFavorite: boolean;
  isDisabled: boolean;
}

// Interface pour les indications d'observation
interface ObservationIndication {
  id: string;
  title: string;
  isDefault: boolean;
}

const ModuleObservation = () => {
  // État pour l'onglet actif
  const [activeTab, setActiveTab] = useState<string>('general');

  // État pour la configuration
  const [config, setConfig] = useState<ObservationConfig>({
    use: true,
    useAsDefault: false,
    useMinimalEditor: true,
    useFilePatientContext: true,
    useMedicalPrescription: true,
    useReports: true,
    reportsRedirect: true,
  });

  // États pour les données des onglets
  const [summaryItems, setSummaryItems] = useState<SummaryItem[]>([]);
  const [entryTypes, setEntryTypes] = useState<ObservationEntryType[]>([
    {
      id: '108',
      title: 'Anamnèse',
      isDefault: true,
      isFavorite: true,
      isDisabled: false,
    },
    {
      id: '109',
      title: 'Diagnostic',
      isDefault: false,
      isFavorite: false,
      isDisabled: false,
    },
  ]);
  const [indications, setIndications] = useState<ObservationIndication[]>([
    {
      id: '110',
      title: 'Aucune Indication',
      isDefault: true,
    },
  ]);

  // États pour les modals
  const [newTypeModalOpened, setNewTypeModalOpened] = useState(false);
  const [reportModalOpened, setReportModalOpened] = useState(false);
  const [newIndicationModalOpened, setNewIndicationModalOpened] = useState(false);
  const [editTypeModalOpened, setEditTypeModalOpened] = useState(false);
  const [editIndicationModalOpened, setEditIndicationModalOpened] = useState(false);

  // États pour les formulaires des modals
  const [newTypeForm, setNewTypeForm] = useState({ value: '', description: '' });
  const [reportForm, setReportForm] = useState({ value: '', examTemplate: '' });
  const [newIndicationForm, setNewIndicationForm] = useState({ value: '', description: '' });
  const [editTypeForm, setEditTypeForm] = useState({ id: '', value: '', description: '' });
  const [editIndicationForm, setEditIndicationForm] = useState({ id: '', value: '', description: '' });

  // Options pour le select des templates d'examen
  const examTemplates = [
    { value: 'template1', label: 'Template Radiologie' },
    { value: 'template2', label: 'Template Cardiologie' },
    { value: 'template3', label: 'Template Neurologie' },
  ];

  // Fonction pour gérer les changements de configuration
  const handleConfigChange = (field: keyof ObservationConfig, value: boolean) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Fonctions pour gérer les types d'entrée
  const handleEntryTypeChange = (id: string, field: keyof ObservationEntryType, value: boolean) => {
    setEntryTypes(prev => prev.map(type => {
      if (type.id === id) {
        // Si on définit comme défaut, désactiver les autres
        if (field === 'isDefault' && value) {
          return { ...type, [field]: value };
        }
        return { ...type, [field]: value };
      }
      // Désactiver les autres si on active un défaut
      if (field === 'isDefault' && value) {
        return { ...type, isDefault: false };
      }
      return type;
    }));
  };

  // Fonctions pour gérer les indications
  const handleIndicationChange = (id: string, value: boolean) => {
    setIndications(prev => prev.map(indication => {
      if (indication.id === id) {
        return { ...indication, isDefault: value };
      }
      // Désactiver les autres si on active un défaut
      if (value) {
        return { ...indication, isDefault: false };
      }
      return indication;
    }));
  };

  // Fonctions pour ajouter de nouveaux éléments
  const handleAddSummaryBlock = () => {
    // Logique pour ajouter un nouveau bloc de synthèse
    console.log('Ajouter un nouveau bloc de synthèse');
  };

  const handleSaveSummary = () => {
    // Logique pour sauvegarder la configuration de synthèse
    console.log('Sauvegarder la configuration de synthèse');
  };

  const handleEditEntryType = (type: ObservationEntryType) => {
    setEditTypeForm({
      id: type.id,
      value: type.title,
      description: '', // Pas de description dans l'interface actuelle
    });
    setEditTypeModalOpened(true);
  };

  const handleDeleteEntryType = (id: string) => {
    setEntryTypes(prev => prev.filter(type => type.id !== id));
  };

  const handleEditIndication = (indication: ObservationIndication) => {
    setEditIndicationForm({
      id: indication.id,
      value: indication.title,
      description: '', // Pas de description dans l'interface actuelle
    });
    setEditIndicationModalOpened(true);
  };

  const handleDeleteIndication = (id: string) => {
    setIndications(prev => prev.filter(indication => indication.id !== id));
  };

  // Fonctions pour gérer les modals
  const handleNewTypeSubmit = () => {
    if (newTypeForm.value.trim()) {
      const newType: ObservationEntryType = {
        id: Date.now().toString(),
        title: newTypeForm.value,
        isDefault: false,
        isFavorite: false,
        isDisabled: false,
      };
      setEntryTypes(prev => [...prev, newType]);
      setNewTypeForm({ value: '', description: '' });
      setNewTypeModalOpened(false);
    }
  };

  const handleReportSubmit = () => {
    if (reportForm.value.trim() && reportForm.examTemplate) {
      const newReport: ObservationEntryType = {
        id: Date.now().toString(),
        title: `Rapport: ${reportForm.value}`,
        isDefault: false,
        isFavorite: false,
        isDisabled: false,
      };
      setEntryTypes(prev => [...prev, newReport]);
      setReportForm({ value: '', examTemplate: '' });
      setReportModalOpened(false);
    }
  };

  const handleNewIndicationSubmit = () => {
    if (newIndicationForm.value.trim()) {
      const newIndication: ObservationIndication = {
        id: Date.now().toString(),
        title: newIndicationForm.value,
        isDefault: false,
      };
      setIndications(prev => [...prev, newIndication]);
      setNewIndicationForm({ value: '', description: '' });
      setNewIndicationModalOpened(false);
    }
  };

  // Fonctions pour gérer les modals d'édition
  const handleEditTypeSubmit = () => {
    if (editTypeForm.value.trim()) {
      setEntryTypes(prev => prev.map(type =>
        type.id === editTypeForm.id
          ? { ...type, title: editTypeForm.value }
          : type
      ));
      setEditTypeForm({ id: '', value: '', description: '' });
      setEditTypeModalOpened(false);
    }
  };

  const handleEditIndicationSubmit = () => {
    if (editIndicationForm.value.trim()) {
      setIndications(prev => prev.map(indication =>
        indication.id === editIndicationForm.id
          ? { ...indication, title: editIndicationForm.value }
          : indication
      ));
      setEditIndicationForm({ id: '', value: '', description: '' });
      setEditIndicationModalOpened(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <IconNotebook size={24} className="text-blue-600" />
          </div>
          <div>
            <Text size="xl" fw={600} className="text-gray-900">
              Module Observation
            </Text>
          </div>
        </div>
      </div>

      {/* Contenu principal avec onglets */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'general')} className="h-full">
            <Tabs.List>
              <Tabs.Tab value="general">
                Général
              </Tabs.Tab>
              <Tabs.Tab value="summary">
                Fiche de synthèse
              </Tabs.Tab>
              <Tabs.Tab value="entry-types">
                Types d'entrée d'observation
              </Tabs.Tab>
              <Tabs.Tab value="indications">
                Indications d'observation
              </Tabs.Tab>
            </Tabs.List>

            {/* Onglet Général */}
            <Tabs.Panel value="general" pt="md">
              <div className="max-w-4xl">
                <Stack gap="lg">
                  {/* Première ligne de switches */}
                  <Group grow align="flex-start">
                    <Switch
                      checked={config.use}
                      onChange={(event) => handleConfigChange('use', event.currentTarget.checked)}
                      label="Activer le module Observation"
                      description="Active ou désactive le module d'observation"
                      size="md"
                      color="blue"
                    />
                    <Switch
                      checked={config.useAsDefault}
                      onChange={(event) => handleConfigChange('useAsDefault', event.currentTarget.checked)}
                      label="Utiliser l'Observation comme consultation par défaut"
                      description="Définit l'observation comme type de consultation par défaut"
                      size="md"
                      color="blue"
                      disabled={!config.use}
                    />
                  </Group>

                  {/* Deuxième ligne de switches */}
                  <Group grow align="flex-start">
                    <Switch
                      checked={config.useMinimalEditor}
                      onChange={(event) => handleConfigChange('useMinimalEditor', event.currentTarget.checked)}
                      label="Éditeur de texte : Options minimales"
                      description="Utilise une version simplifiée de l'éditeur de texte"
                      size="md"
                      color="blue"
                      disabled={!config.use}
                    />
                    <Switch
                      checked={config.useFilePatientContext}
                      onChange={(event) => handleConfigChange('useFilePatientContext', event.currentTarget.checked)}
                      label="Afficher les fichiers du patient"
                      description="Affiche les fichiers associés au patient dans le contexte"
                      size="md"
                      color="blue"
                      disabled={!config.use}
                    />
                  </Group>

                  {/* Troisième ligne de switches */}
                  <Group grow align="flex-start">
                    <Switch
                      checked={config.useMedicalPrescription}
                      onChange={(event) => handleConfigChange('useMedicalPrescription', event.currentTarget.checked)}
                      label="Lier au module Ordonnance"
                      description="Intègre les fonctionnalités du module ordonnance"
                      size="md"
                      color="blue"
                      disabled={!config.use}
                    />
                    <Switch
                      checked={config.useReports}
                      onChange={(event) => handleConfigChange('useReports', event.currentTarget.checked)}
                      label="Lier au module Compte Rendu"
                      description="Intègre les fonctionnalités du module compte rendu"
                      size="md"
                      color="blue"
                      disabled={!config.use}
                    />
                  </Group>

                  {/* Switch conditionnel pour les rapports */}
                  {config.useReports && (
                    <Group grow align="flex-start">
                      <Switch
                        checked={config.reportsRedirect}
                        onChange={(event) => handleConfigChange('reportsRedirect', event.currentTarget.checked)}
                        label="Utiliser le module Compte Rendu"
                        description="Redirige vers le module compte rendu pour la création"
                        size="md"
                        color="blue"
                        disabled={!config.use || !config.useReports}
                      />
                      <div></div> {/* Espace vide pour maintenir l'alignement */}
                    </Group>
                  )}
                </Stack>
              </div>
            </Tabs.Panel>

            {/* Onglet Fiche de synthèse */}
            <Tabs.Panel value="summary" pt="md">
              <div className="w-full">
                <Stack gap="md">
                  {/* Tableau des éléments de synthèse */}
                  <Table striped highlightOnHover>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th className="w-16 text-center">#</Table.Th>
                        <Table.Th>Source disponible</Table.Th>
                        <Table.Th>Champs</Table.Th>
                        <Table.Th className="w-32"></Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {summaryItems.length === 0 ? (
                        <Table.Tr>
                          <Table.Td colSpan={4} className="text-center text-gray-500 py-8">
                            Aucun élément trouvé.
                          </Table.Td>
                        </Table.Tr>
                      ) : (
                        summaryItems.map((item, index) => (
                          <Table.Tr key={item.id}>
                            <Table.Td className="text-center">{index + 1}</Table.Td>
                            <Table.Td>{item.source}</Table.Td>
                            <Table.Td>{item.fields}</Table.Td>
                            <Table.Td>
                              <Group gap="xs">
                                <ActionIcon
                                  variant="subtle"
                                  color="blue"
                                  onClick={() => console.log('Edit summary item:', item)}
                                >
                                  <IconEdit size={16} />
                                </ActionIcon>
                                <ActionIcon
                                  variant="subtle"
                                  color="red"
                                  onClick={() => setSummaryItems(prev => prev.filter(i => i.id !== item.id))}
                                >
                                  <IconTrash size={16} />
                                </ActionIcon>
                              </Group>
                            </Table.Td>
                          </Table.Tr>
                        ))
                      )}
                    </Table.Tbody>
                  </Table>

                  {/* Boutons d'action */}
                  <Group justify="flex-start" mt="md">
                    <Button
                      leftSection={<IconPlus size={16} />}
                      onClick={handleAddSummaryBlock}
                      className="bg-blue-500 hover:bg-blue-600 text-white"
                    >
                      Ajouter un nouveau bloc
                    </Button>
                    <Button
                      onClick={handleSaveSummary}
                      className="bg-blue-500 hover:bg-blue-600 text-white"
                      disabled={summaryItems.length === 0}
                    >
                      Enregistrer
                    </Button>
                  </Group>
                </Stack>
              </div>
            </Tabs.Panel>

            {/* Onglet Types d'entrée d'observation */}
            <Tabs.Panel value="entry-types" pt="md">
              <div className="w-full">
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Titre</Table.Th>
                      <Table.Th className="text-center">Par défaut</Table.Th>
                      <Table.Th className="text-center">Favorie</Table.Th>
                      <Table.Th className="text-center">Désactivé</Table.Th>
                      <Table.Th className="w-32"></Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {entryTypes.map((type) => (
                      <Table.Tr key={type.id}>
                        <Table.Td>{type.title}</Table.Td>
                        <Table.Td className="text-center">
                          <Switch
                            checked={type.isDefault}
                            onChange={(event) =>
                              handleEntryTypeChange(type.id, 'isDefault', event.currentTarget.checked)
                            }
                            disabled={type.isDisabled}
                            color="blue"
                            size="sm"
                          />
                        </Table.Td>
                        <Table.Td className="text-center">
                          <Switch
                            checked={type.isFavorite}
                            onChange={(event) =>
                              handleEntryTypeChange(type.id, 'isFavorite', event.currentTarget.checked)
                            }
                            disabled={type.isDisabled}
                            color="blue"
                            size="sm"
                          />
                        </Table.Td>
                        <Table.Td className="text-center">
                          <Switch
                            checked={type.isDisabled}
                            onChange={(event) =>
                              handleEntryTypeChange(type.id, 'isDisabled', event.currentTarget.checked)
                            }
                            color="blue"
                            size="sm"
                          />
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => handleEditEntryType(type)}
                              disabled={type.isDisabled}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteEntryType(type.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
                
              </div>
              <Group justify="flex-end" mt="md">
      <Button
        leftSection={<Icon path={mdiPlus} size={1} />}
        onClick={() => setNewTypeModalOpened(true)}
        className="bg-blue-500 hover:bg-blue-600 text-white"
      >
        Nouveau type
      </Button>
    <Button
      leftSection={<Icon path={mdiPlus} size={1} />}
      onClick={() => setReportModalOpened(true)}
      className="bg-blue-500 hover:bg-blue-600 text-white"
    >
      Rapport (Compte-rendue)
    </Button>
    </Group>
            </Tabs.Panel>

            {/* Onglet Indications d'observation */}
            <Tabs.Panel value="indications" pt="md">
              <div className="w-full">
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Indication</Table.Th>
                      <Table.Th className="text-center">Par défaut</Table.Th>
                      <Table.Th className="w-32"></Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {indications.map((indication) => (
                      <Table.Tr key={indication.id}>
                        <Table.Td>{indication.title}</Table.Td>
                        <Table.Td className="text-center">
                          <Switch
                            checked={indication.isDefault}
                            onChange={(event) =>
                              handleIndicationChange(indication.id, event.currentTarget.checked)
                            }
                            color="blue"
                            size="sm"
                          />
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => handleEditIndication(indication)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteIndication(indication.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </div>
                 <Group justify="flex-end" mt="md">
      <Button
        leftSection={<Icon path={mdiPlus} size={1} />}
        onClick={() => setNewIndicationModalOpened(true)}
        className="bg-blue-500 hover:bg-blue-600 text-white"
      >
        Nouvelle indication
      </Button>
    </Group>
            </Tabs.Panel>
          </Tabs>
        </Card>
      </div>

      {/* Modal Nouveau Type */}
      <Modal
        opened={newTypeModalOpened}
        onClose={() => setNewTypeModalOpened(false)}
        title={
          <div className="flex items-center gap-2">
            <Icon path={mdiPlus} size={1} className="text-blue-600" />
            <span>Ajouter Nouveau type</span>
          </div>
        }
        size="sm"
      >
        <Stack gap="md">
          <TextInput
            label="Valeur"
            placeholder="Entrez la valeur"
            value={newTypeForm.value}
            onChange={(event) => setNewTypeForm(prev => ({ ...prev, value: event.currentTarget.value }))}
            required
            data-autofocus
          />
          <TextInput
            label="Description"
            placeholder="Entrez la description"
            value={newTypeForm.description}
            onChange={(event) => setNewTypeForm(prev => ({ ...prev, description: event.currentTarget.value }))}
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setNewTypeModalOpened(false)}
              className="border-red-500 text-red-500 hover:bg-red-50"
            >
              Annuler
            </Button>
            <Button
              onClick={handleNewTypeSubmit}
              disabled={!newTypeForm.value.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Rapport */}
      <Modal
        opened={reportModalOpened}
        onClose={() => setReportModalOpened(false)}
        title={
          <div className="flex items-center gap-2">
            <Icon path={mdiPlus} size={1} className="text-blue-600" />
            <span>Rapport comme type entrée</span>
          </div>
        }
        size="sm"
      >
        <Stack gap="md">
          <TextInput
            label="Valeur"
            placeholder="Entrez la valeur"
            value={reportForm.value}
            onChange={(event) => setReportForm(prev => ({ ...prev, value: event.currentTarget.value }))}
            required
          />
          <Select
            label="Type d'examen"
            placeholder="Sélectionnez un type d'examen"
            data={examTemplates}
            value={reportForm.examTemplate}
            onChange={(value) => setReportForm(prev => ({ ...prev, examTemplate: value || '' }))}
            required
            searchable
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setReportModalOpened(false)}
              className="border-red-500 text-red-500 hover:bg-red-50"
            >
              Annuler
            </Button>
            <Button
              onClick={handleReportSubmit}
              disabled={!reportForm.value.trim() || !reportForm.examTemplate}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Nouvelle Indication */}
      <Modal
        opened={newIndicationModalOpened}
        onClose={() => setNewIndicationModalOpened(false)}
        title={
          <div className="flex items-center gap-2">
            <Icon path={mdiPlus} size={1} className="text-blue-600" />
            <span>Ajouter Nouvelle indication</span>
          </div>
        }
        size="sm"
      >
        <Stack gap="md">
          <TextInput
            label="Valeur"
            placeholder="Entrez la valeur"
            value={newIndicationForm.value}
            onChange={(event) => setNewIndicationForm(prev => ({ ...prev, value: event.currentTarget.value }))}
            required
            data-autofocus
          />
          <TextInput
            label="Description"
            placeholder="Entrez la description"
            value={newIndicationForm.description}
            onChange={(event) => setNewIndicationForm(prev => ({ ...prev, description: event.currentTarget.value }))}
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setNewIndicationModalOpened(false)}
              className="border-red-500 text-red-500 hover:bg-red-50"
            >
              Annuler
            </Button>
            <Button
              onClick={handleNewIndicationSubmit}
              disabled={!newIndicationForm.value.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Éditer Type */}
      <Modal
        opened={editTypeModalOpened}
        onClose={() => setEditTypeModalOpened(false)}
        title={
          <div className="flex items-center gap-2">
            <Icon path={mdiPlus} size={1} className="text-blue-600" />
            <span>Ajouter Nouveau type</span>
          </div>
        }
        size="sm"
      >
        <Stack gap="md">
          <TextInput
            label="Valeur"
            placeholder="Entrez la valeur"
            value={editTypeForm.value}
            onChange={(event) => setEditTypeForm(prev => ({ ...prev, value: event.currentTarget.value }))}
            required
            data-autofocus
          />
          <TextInput
            label="Description"
            placeholder="Entrez la description"
            value={editTypeForm.description}
            onChange={(event) => setEditTypeForm(prev => ({ ...prev, description: event.currentTarget.value }))}
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setEditTypeModalOpened(false)}
              className="border-red-500 text-red-500 hover:bg-red-50"
            >
              Annuler
            </Button>
            <Button
              onClick={handleEditTypeSubmit}
              disabled={!editTypeForm.value.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Éditer Indication */}
      <Modal
        opened={editIndicationModalOpened}
        onClose={() => setEditIndicationModalOpened(false)}
        title={
          <div className="flex items-center gap-2">
            <Icon path={mdiPlus} size={1} className="text-blue-600" />
            <span>Ajouter Nouvelle indication</span>
          </div>
        }
        size="sm"
      >
        <Stack gap="md">
          <TextInput
            label="Valeur"
            placeholder="Entrez la valeur"
            value={editIndicationForm.value}
            onChange={(event) => setEditIndicationForm(prev => ({ ...prev, value: event.currentTarget.value }))}
            required
            data-autofocus
          />
          <TextInput
            label="Description"
            placeholder="Entrez la description"
            value={editIndicationForm.description}
            onChange={(event) => setEditIndicationForm(prev => ({ ...prev, description: event.currentTarget.value }))}
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setEditIndicationModalOpened(false)}
              className="border-red-500 text-red-500 hover:bg-red-50"
            >
              Annuler
            </Button>
            <Button
              onClick={handleEditIndicationSubmit}
              disabled={!editIndicationForm.value.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default ModuleObservation;
