import { createInstance } from "i18next";
import resourcesToBackend from "i18next-resources-to-backend";
import { initReactI18next } from "react-i18next/initReactI18next";
import {
  FALLBACK_LOCALE,
  getOptions,
  Locales,
  LANGUAGE_COOKIE,
} from "./settings";
import { cookies } from "next/headers";

async function initI18next(lang: Locales, namespace: string) {
  const i18nInstance = createInstance();
  await i18nInstance
    .use(initReactI18next)
    .use(
      resourcesToBackend(
        (lang: string, ns: string) => import(`./locales/${lang}/${ns}.json`)
      )
    )
    .init(getOptions(lang, namespace));

  return i18nInstance;
}

// export async function createTranslation(ns: string) {
//   const lang = getLocale();
//   const i18nextInstance = await initI18next(lang, ns);

//   return {
//     t: i18nextInstance.getFixedT(lang, Array.isArray(ns) ? ns[0] : ns),
//   };
// }
export async function createTranslation(ns: string) {
  const lang = await getLocale(); // Ajout de await ici
  const i18nextInstance = await initI18next(lang, ns);

  return {
    t: i18nextInstance.getFixedT(lang, Array.isArray(ns) ? ns[0] : ns),
  };
}


// export function getLocale() {
//   return (cookies().get(LANGUAGE_COOKIE)?.value ?? FALLBACK_LOCALE) as Locales;
// }
export async function getLocale() {
  const cookieStore = await cookies(); // Attendre la résolution
  return (cookieStore.get(LANGUAGE_COOKIE)?.value ?? FALLBACK_LOCALE) as Locales;
}
