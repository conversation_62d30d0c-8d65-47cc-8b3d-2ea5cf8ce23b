/**
 * Utility functions for converting between backend and frontend status formats
 */

/**
 * Convert backend status flags to frontend status string
 *
 * @param is_active - Backend is_active flag
 * @param is_pending - Backend is_pending flag
 * @returns Frontend status string: 'active', 'inactive', or 'pending'
 */
export function backendToFrontendStatus(is_active?: boolean | string, is_pending?: boolean | string): 'active' | 'inactive' | 'pending' {
  // Handle different types of values
  let isActive: boolean;
  let isPending: boolean;

  // Handle is_active
  if (is_active === undefined || is_active === null) {
    isActive = false;
  } else if (typeof is_active === 'boolean') {
    isActive = is_active;
  } else if (typeof is_active === 'string') {
    // Convert string to boolean
    isActive = is_active.toLowerCase() === 'true';
  } else {
    console.warn(`Unexpected is_active type: ${typeof is_active}, value: ${is_active}`);
    isActive = false;
  }

  // Handle is_pending
  if (is_pending === undefined || is_pending === null) {
    isPending = false;
  } else if (typeof is_pending === 'boolean') {
    isPending = is_pending;
  } else if (typeof is_pending === 'string') {
    // Convert string to boolean
    isPending = is_pending.toLowerCase() === 'true';
  } else {
    console.warn(`Unexpected is_pending type: ${typeof is_pending}, value: ${is_pending}`);
    isPending = false;
  }

  // Log input values for debugging
  console.log('backendToFrontendStatus input:', {
    is_active,
    is_pending,
    is_active_type: typeof is_active,
    is_pending_type: typeof is_pending,
    is_active_after_handling: isActive,
    is_pending_after_handling: isPending
  });

  // Convert to frontend status
  let result: 'active' | 'inactive' | 'pending';
  if (isPending) {
    result = 'pending';
  } else if (isActive) {
    result = 'active';
  } else {
    result = 'inactive';
  }

  // Log the result
  console.log('backendToFrontendStatus result:', result);

  return result;
}

/**
 * Convert frontend status string to backend status flags
 *
 * @param status - Frontend status string: 'active', 'inactive', or 'pending'
 * @returns Object with backend status flags: { is_active, is_pending }
 */
export function frontendToBackendStatus(status: 'active' | 'inactive' | 'pending'): { is_active: boolean, is_pending: boolean } {
  // Log input value for debugging
  console.log('frontendToBackendStatus input:', {
    status,
    status_type: typeof status
  });

  let result: { is_active: boolean, is_pending: boolean };

  switch (status) {
    case 'active':
      result = { is_active: true, is_pending: false };
      break;
    case 'pending':
      result = { is_active: false, is_pending: true };
      break;
    case 'inactive':
    default:
      result = { is_active: false, is_pending: false };
      break;
  }

  // Log the result
  console.log('frontendToBackendStatus result:', result);

  return result;
}
