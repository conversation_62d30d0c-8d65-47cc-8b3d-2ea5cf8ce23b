/**
 * Medical Report Dashboard Widgets
 * Displays key medical and billing metrics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconReportMedical,
  IconReceipt,
  IconFileText,
  IconCurrencyEuro,
  IconContract,
  IconClipboardCheck,
  IconAlertCircle,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
} from '@tabler/icons-react';
import { useMedicalReport } from '@/hooks/useMedicalReport';

interface MedicalReportWidgetsProps {
  patientId?: string;
  dateRange?: { start: string; end: string };
  compact?: boolean;
  showBilling?: boolean;
}

const MedicalReportWidgets: React.FC<MedicalReportWidgetsProps> = ({
  patientId,
  dateRange,
  compact = false,
  showBilling = true,
}) => {
  const {
    examReports,
    billingInvoices,
    billingQuotes,
    payments,
    contracts,
    loading,
    error,
    getPatientMedicalStats,
    getBillingStats,
  } = useMedicalReport({ 
    patientId, 
    dateRange, 
    autoFetch: true,
    reportTypes: ['reports', 'billing', 'payments', 'contracts']
  });

  const patientStats = patientId ? getPatientMedicalStats(patientId) : null;
  const billingStats = getBillingStats();

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  const totalReports = examReports.length;
  const totalInvoices = billingInvoices.length;
  const totalQuotes = billingQuotes.length;
  const totalContracts = contracts.length;
  const activeContracts = contracts.filter(c => c.status === 'active').length;
  const paidInvoices = billingInvoices.filter(i => i.status === 'paid').length;
  const invoicePaymentRate = totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 0;

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Medical Reports Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconReportMedical size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Rapports Médicaux</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="blue">
            {totalReports}
          </Text>
          {patientStats && (
            <Text size="xs" c="dimmed">
              {patientStats.lastReportDate ? 
                `Dernier: ${new Date(patientStats.lastReportDate).toLocaleDateString()}` : 
                'Aucun rapport récent'
              }
            </Text>
          )}
        </Card>

        {/* Billing Widget */}
        {showBilling && (
          <Card padding="md" radius="md" withBorder>
            <Group justify="space-between" mb="xs">
              <Group gap="xs">
                <ThemeIcon variant="light" color="green" size="sm">
                  <IconReceipt size={16} />
                </ThemeIcon>
                <Text size="sm" c="dimmed">Facturation</Text>
              </Group>
            </Group>
            <Text size="xl" fw={700} c="green">
              {billingStats.totalRevenue.toLocaleString()}€
            </Text>
            <Progress value={invoicePaymentRate} size="xs" mt="xs" />
            <Text size="xs" c="dimmed">
              {invoicePaymentRate.toFixed(1)}% factures payées
            </Text>
          </Card>
        )}

        {/* Quotes Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconFileText size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Devis</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="orange">
            {totalQuotes}
          </Text>
          <Text size="xs" c="dimmed">
            {billingStats.activeQuotes} actifs
          </Text>
        </Card>

        {/* Contracts Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconContract size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Contrats</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ value: totalContracts > 0 ? (activeContracts / totalContracts) * 100 : 0, color: 'purple' }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {activeContracts}/{totalContracts}
                </Text>
              }
            />
          </Group>
        </Card>
      </SimpleGrid>

      {!compact && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={showBilling ? 3 : 2} spacing="md">
            {/* Recent Reports */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconClipboardCheck size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Rapports Récents</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {examReports.slice(0, 3).map((report) => (
                  <Group key={report.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{report.template_title}</Text>
                      <Text size="xs" c="dimmed">{report.patient_name}</Text>
                    </div>
                    <Group gap="xs">
                      <Badge 
                        size="xs" 
                        color={report.status === 'completed' ? 'green' : report.status === 'draft' ? 'yellow' : 'blue'}
                      >
                        {report.status}
                      </Badge>
                      <Text size="xs" c="dimmed">
                        {new Date(report.exam_date).toLocaleDateString()}
                      </Text>
                    </Group>
                  </Group>
                ))}
                {examReports.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucun rapport disponible
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Billing Summary */}
            {showBilling && (
              <Card padding="md" radius="md" withBorder>
                <Group justify="space-between" mb="md">
                  <Group gap="xs">
                    <ThemeIcon variant="light" color="indigo" size="sm">
                      <IconCurrencyEuro size={16} />
                    </ThemeIcon>
                    <Text size="sm" fw={600}>Résumé Financier</Text>
                  </Group>
                </Group>
                <Stack gap="xs">
                  <Group justify="space-between">
                    <Text size="xs">Revenus totaux</Text>
                    <Text size="xs" fw={500} c="green">
                      {billingStats.totalRevenue.toLocaleString()}€
                    </Text>
                  </Group>
                  <Group justify="space-between">
                    <Text size="xs">En attente</Text>
                    <Text size="xs" fw={500} c="orange">
                      {billingStats.pendingRevenue.toLocaleString()}€
                    </Text>
                  </Group>
                  <Divider size="xs" />
                  <Group justify="space-between">
                    <Text size="xs">Factures payées</Text>
                    <Text size="xs" fw={500}>{billingStats.paidInvoices}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text size="xs">Factures impayées</Text>
                    <Text size="xs" fw={500}>{billingStats.unpaidInvoices}</Text>
                  </Group>
                </Stack>
              </Card>
            )}

            {/* Contract Status */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="violet" size="sm">
                    <IconContract size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>État des Contrats</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {contracts.slice(0, 3).map((contract) => (
                  <Group key={contract.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{contract.contract_type}</Text>
                      <Text size="xs" c="dimmed">{contract.patient_name}</Text>
                    </div>
                    <Group gap="xs">
                      <Badge 
                        size="xs" 
                        color={
                          contract.status === 'active' ? 'green' : 
                          contract.status === 'expired' ? 'red' : 
                          contract.status === 'cancelled' ? 'gray' : 'yellow'
                        }
                      >
                        {contract.status}
                      </Badge>
                      <Text size="xs" fw={500}>
                        {contract.monthly_amount ? `${contract.monthly_amount}€/mois` : `${contract.total_value}€`}
                      </Text>
                    </Group>
                  </Group>
                ))}
                {contracts.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucun contrat disponible
                  </Text>
                )}
              </Stack>
            </Card>
          </SimpleGrid>

          {/* Patient-Specific Stats */}
          {patientId && patientStats && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>Statistiques Patient</Text>
                <Badge size="sm" color="blue">
                  {patientStats.totalReports + patientStats.totalInvoices + patientStats.totalContracts} éléments
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Rapports</Text>
                    <Text size="lg" fw={700} c="blue">{patientStats.totalReports}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Factures</Text>
                    <Text size="lg" fw={700} c="green">{patientStats.totalInvoices}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Revenus</Text>
                    <Text size="lg" fw={700} c="teal">{patientStats.totalRevenue}€</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Contrats Actifs</Text>
                    <Text size="lg" fw={700} c="purple">{patientStats.activeContracts}</Text>
                  </Stack>
                </Grid.Col>
              </Grid>
              {patientStats.pendingAmount > 0 && (
                <>
                  <Divider my="md" />
                  <Group justify="space-between">
                    <Text size="sm" c="dimmed">Montant en attente</Text>
                    <Text size="sm" fw={600} c="orange">
                      {patientStats.pendingAmount.toLocaleString()}€
                    </Text>
                  </Group>
                </>
              )}
            </Card>
          )}
        </>
      )}
    </Stack>
  );
};

export default MedicalReportWidgets;
