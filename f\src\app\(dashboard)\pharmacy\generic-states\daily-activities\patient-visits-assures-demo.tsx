'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { PatientVisitsAssures } from './Patient-visits-assures';

export default function PatientVisitsAssuresDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Période sélectionnée: Du ${query.start.toLocaleDateString()} au ${query.end.toLocaleDateString()}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
    const stateMessages: { [key: string]: string } = {
      'all_objects': 'Tous les objets payables sélectionnés',
      'mutual_only': 'Seulement les déclarations en mutuelles',
      'patient_count': 'Nombre de patients par organisme'
    };
    alert(stateMessages[state.name] || 'État sélectionné');
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    if (filter.onlyInsured !== undefined) {
      alert(`Patients assurés seulement: ${filter.onlyInsured ? 'Activé' : 'Désactivé'}`);
    }
    if (filter.showAdvancedFilter !== undefined) {
      alert(`Filtre avancé: ${filter.showAdvancedFilter ? 'Activé' : 'Désactivé'}`);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des patients/visites assurés en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des patients/visites assurés en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'amount_due': 'Montant dû',
      'amount_paid': 'Montant réglé',
      'discount': 'Remise',
      'remaining': 'Reste à régler'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function PatientVisitsAssuresLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={true}
          onQueryChange={(query) => console.log('Query:', query)}
          onStateChange={(state) => console.log('State:', state)}
          onFilterChange={(filter) => console.log('Filter:', filter)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
          onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function PatientVisitsAssuresWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    alert(`Recherche des patients assurés du ${startDate} au ${endDate}:\n- Consultations: 45\n- Montant total: 12,500 DH\n- Patients uniques: 32`);
  };

  const handleStateChange = (state: any) => {
    console.log('État avec données:', state);
    
    const stateData: { [key: string]: any } = {
      'all_objects': {
        title: 'Tous les objets payables',
        data: 'Consultations, actes, prescriptions, certificats'
      },
      'mutual_only': {
        title: 'Seulement déclarations mutuelles',
        data: 'CNOPS, CNSS, Assurances privées'
      },
      'patient_count': {
        title: 'Nombre de patients par organisme',
        data: 'CNOPS: 25, CNSS: 12, Autres: 8'
      }
    };
    
    const data = stateData[state.name];
    if (data) {
      alert(`${data.title}:\n${data.data}`);
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    if (filter.onlyInsured !== undefined) {
      const message = filter.onlyInsured 
        ? 'Affichage uniquement des patients avec assurance active'
        : 'Affichage de tous les patients (assurés et non-assurés)';
      alert(message);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des patients/visites assurés avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des patients/visites assurés avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'amount_due': 'Tri des montants dus',
      'amount_paid': 'Tri des montants réglés',
      'discount': 'Tri des remises accordées',
      'remaining': 'Tri des restes à régler'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode mutuelles
export function PatientVisitsAssuresMutualDemo() {
  const handleStateChange = (state: any) => {
    console.log('Mode mutuelles:', state);
    if (state.type === 'mutual_only') {
      alert('Mode Mutuelles activé:\n- CNOPS: Caisse Nationale des Organismes de Prévoyance Sociale\n- CNSS: Caisse Nationale de Sécurité Sociale\n- Assurances privées: Wafa, Saham, Atlanta...');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre mutuelles:', filter);
    if (filter.onlyInsured) {
      alert('Filtre mutuelles:\n- Patients avec couverture sociale\n- Remboursements automatiques\n- Tiers payant disponible');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={false}
          onQueryChange={(query) => console.log('Query mutuelles:', query)}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données mutuelles`)}
          onPrint={() => alert('Impression des données mutuelles')}
          onSort={(columnId, direction) => console.log('Sort mutuelles:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function PatientVisitsAssuresErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    const diffDays = Math.abs(query.end - query.start) / (1000 * 60 * 60 * 24);
    if (diffDays > 365) {
      alert('Attention: Période trop longue (> 1 an). Les performances peuvent être affectées.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={(state) => console.log('State avec validation:', state)}
          onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
          onExport={(format) => {
            console.log(`Export ${format} avec validation`);
            if (confirm(`Êtes-vous sûr de vouloir exporter les patients/visites assurés en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onPrint={() => {
            console.log('Impression avec validation');
            if (confirm('Êtes-vous sûr de vouloir imprimer les patients/visites assurés ?')) {
              alert('Impression en cours...');
            }
          }}
          onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données financières simulées
export function PatientVisitsAssuresFinancialDemo() {
  const handleStateChange = (state: any) => {
    console.log('État financier:', state);
    
    // Simuler des données financières selon l'état
    const mockFinancialData: { [key: string]: any } = {
      'all_objects': {
        total: 45780.50,
        paid: 32450.75,
        remaining: 13329.75,
        patients: 127
      },
      'mutual_only': {
        total: 38920.25,
        paid: 29180.50,
        remaining: 9739.75,
        patients: 89
      },
      'patient_count': {
        cnops: 45,
        cnss: 28,
        private: 16,
        total: 89
      }
    };
    
    const data = mockFinancialData[state.name];
    if (data) {
      if (state.name === 'patient_count') {
        alert(`Répartition par organisme:\n- CNOPS: ${data.cnops} patients\n- CNSS: ${data.cnss} patients\n- Assurances privées: ${data.private} patients\n- Total: ${data.total} patients`);
      } else {
        alert(`${state.label}:\n- Montant total: ${data.total.toFixed(2)} DH\n- Montant réglé: ${data.paid.toFixed(2)} DH\n- Reste à régler: ${data.remaining.toFixed(2)} DH\n- Nombre de patients: ${data.patients}`);
      }
    }
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri financier:', columnId, direction);
    
    // Simuler le tri des données financières
    const sortData: { [key: string]: string } = {
      'amount_due': 'Tri des montants dus (factures émises)',
      'amount_paid': 'Tri des montants réglés (encaissements)',
      'discount': 'Tri des remises accordées (réductions)',
      'remaining': 'Tri des restes à régler (impayés)'
    };
    
    const message = sortData[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <PatientVisitsAssures
          loading={false}
          onQueryChange={(query) => console.log('Query financier:', query)}
          onStateChange={handleStateChange}
          onFilterChange={(filter) => console.log('Filter financier:', filter)}
          onExport={(format) => alert(`Export ${format} des données financières des patients assurés`)}
          onPrint={() => alert('Impression des données financières des patients assurés')}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}
