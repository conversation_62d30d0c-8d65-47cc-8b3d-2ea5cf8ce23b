import React from 'react';
import Icon from '@mdi/react';
import { mdiClockOutline } from '@mdi/js';

// Types
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
}

// Utility functions
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

// Agenda View Component
const AgendaView = ({ 
  currentDate, 
  events 
}: {
  currentDate: Date;
  events: CalendarEvent[];
}) => {
  // Get events for the next 30 days
  const endDate = new Date(currentDate);
  endDate.setDate(endDate.getDate() + 30);
  
  const upcomingEvents = events
    .filter(event => event.start >= currentDate && event.start <= endDate)
    .sort((a, b) => a.start.getTime() - b.start.getTime());

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Upcoming Events</h2>
      <div className="space-y-2">
        {upcomingEvents.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No upcoming events</p>
        ) : (
          upcomingEvents.map(event => (
            <div key={event.id} className="border rounded-lg p-4 hover:bg-gray-50">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg" style={{ color: event.color }}>
                    {event.title}
                  </h3>
                  <p className="text-gray-600">{event.desc}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <Icon path={mdiClockOutline} size={1} className="mr-1" />
                    {formatDate(event.start)} • {formatTime(event.start)} - {formatTime(event.end)}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AgendaView;
