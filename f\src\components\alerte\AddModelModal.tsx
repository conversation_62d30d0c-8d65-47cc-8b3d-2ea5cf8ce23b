import React from 'react';
import { Modal,  Text, Input, Group, Button,ActionIcon } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiViewHeadline,  } from '@mdi/js';

interface AddModelModalProps {
  opened: boolean;
  onClose: () => void;
  modelTitle: string;
  setModelTitle: (title: string) => void;
  onSave: () => void;
  onCancel: () => void;
  isEditing?: boolean;
}

export const AddModelModal: React.FC<AddModelModalProps> = ({
  opened,
  onClose,
  modelTitle,
  setModelTitle,
  onSave,
  onCancel,
  isEditing = false
}) => {
  return (
    <>
    
    <Modal.Root
      opened={opened}
      onClose={onClose}
      transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
      centered
      size="sm"
    > 
    <Modal.Overlay />
    <Modal.Content className="overflow-y-hidden">
          <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Group>
                <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                  <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                     {isEditing ? 'Éditer le modèle' : 'Ajouter un modèle'}
                    </Text>
              </Group>
            </Modal.Title>
              <Group justify="flex-end">
                 <ActionIcon
                variant="subtle"
                color="blue"
                onClick={() => {
              console.log('ActionIcon clicked - calling onNewModel');
              onClose();
            }}
             
              >
                
              </ActionIcon>
                <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
              </Group>
          </Modal.Header>
              <Modal.Body style={{ padding: '10px' }}>
                          
     
          <Text size="xl" fw={600}>Titre du modèle</Text>
          <Input
            placeholder="Titre"
            value={modelTitle}
            onChange={(e) => setModelTitle(e.currentTarget.value)}
          />
          <Group justify="flex-end" mt="md">
            <Button
              variant="filled"
              color="gray"
              onClick={onSave}
              disabled={!modelTitle.trim()}
            >
              {isEditing ? 'Modifier' : 'Enregistrer'}
            </Button>
            <Button
              variant="outline"
              color="red"
              onClick={onCancel}
            >
              Annuler
            </Button>
          </Group>
      
              </Modal.Body>
        </Modal.Content>
     </Modal.Root>
    </>
  );
};
