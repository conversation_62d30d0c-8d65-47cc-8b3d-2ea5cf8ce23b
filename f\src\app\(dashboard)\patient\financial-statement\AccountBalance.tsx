// components/AccountBalance.tsx
import { Group, Text, Flex ,Divider} from '@mantine/core';

type BalanceProps = {
  patient: {
    sign: number;
    value: number;
  };
  organizations: {
    value: number;
  };
  balance: {
    sign: number;
    value: number;
  };
};

const formatCurrency = (value: number): string =>
  value.toLocaleString('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2,
  });

export const AccountBalance = ({ patient, organizations, balance }: BalanceProps) => {
  const patientClass = patient.sign > 0 ? 'balance-positive' : patient.sign < 0 ? 'balance-negative' : '';
  const orgClass = organizations.value < 0 ? 'balance-negative' : '';
  const totalClass = balance.sign > 0 ? 'balance-positive' : balance.sign < 0 ? 'balance-negative' : '';

  return (
    <Group justify="flex-end" className="account-balance" gap="sm" pb={14}>
      <Text size='xl' fw={700}>Balance :</Text>

      <Flex direction="row" gap={2}>
        <Text size="lg" fw={500}>Patient :</Text>
        <Text size="lg"c={"#3799ce"} className={patientClass}>
         &nbsp; {formatCurrency(patient.value)}
        </Text>
      </Flex>
 <Divider size="sm" orientation="vertical" />
      <Flex direction="row" gap={2}>
        <Text size="lg"fw={500}>&nbsp;&nbsp;&nbsp;Organismes:</Text>
        <Text size="lg"c={"#3799ce"} className={orgClass}>
          {formatCurrency(organizations.value)}&nbsp;&nbsp;
        </Text>
      </Flex>
 <Divider size="sm" orientation="vertical" />
      <Flex direction="row" gap={2}>
        <Text size="lg"fw={600}>Total:</Text>
        <Text size="lg" c={"green"} className={totalClass}>
          {formatCurrency(balance.value)}
        </Text>
      </Flex>
    </Group>
  );
};
