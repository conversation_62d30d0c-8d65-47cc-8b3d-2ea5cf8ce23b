import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import Demande<PERSON><PERSON> from "./DemandePrix"
import <PERSON>e<PERSON>ommen<PERSON> from "./BondeCommende"
import BonDeposition from "./BonDeposition"
import BonRetour from "./BonRetour"
import FacturePage from "./Facture"
import Avoir from "./Avoir"
import ANouveauPage from "./A-nouveau"
import Reglement from "./Reglement"
import Demandes_dachats from "./Demandes_dachats"
import Bon_de_reception from "./Bon_de_reception"

// Mapping des sous-onglets pour Achat
const subtabMapping: { [key: string]: string } = {
  'demandes-achats': 'Demandes_dachats',
  'demandes-prix': 'DemandePrix',
  'bons-commande': 'BondeCommende',
  'bons-reception': 'Bon_de_reception',
  'bons-deposition': 'BonDeposition',
  'bons-retour': 'BonRetour',
  'facturation': 'FacturePage',
  'liste-nouveaux': 'ANouveauPage',
  'liste-reglements': 'Reglement'
};

const Achat = () => {
  const [activeTab, setActiveTab] = useState('Demandes_dachats');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
     <Tabs
       variant="outline"
       radius="md"
       orientation="vertical"
       value={activeTab}
       onChange={(value) => setActiveTab(value || 'Demandes_dachats')}
       w={"100%"}
       mt={10}
     >
          <Tabs.List>
            <Tabs.Tab value="Demandes_dachats" leftSection={<IconPhoto size={12} />}>
              Demandes d&apos;achats
            </Tabs.Tab>
            <Tabs.Tab value="DemandePrix" leftSection={<IconMessageCircle size={12} />}>
              Demandes de prix
            </Tabs.Tab>
            <Tabs.Tab value="BondeCommende" leftSection={<IconSettings size={12} />}>
              Bons de Commande
            </Tabs.Tab>
             <Tabs.Tab value="Bon_de_reception" leftSection={<IconPhoto size={12} />}>
              Bons de reception
            </Tabs.Tab>
            <Tabs.Tab value="BonDeposition" leftSection={<IconMessageCircle size={12} />}>
              Bons de position
            </Tabs.Tab>
            <Tabs.Tab value="BonRetour" leftSection={<IconSettings size={12} />}>
              Bons de retour
            </Tabs.Tab>
             <Tabs.Tab value="FacturePage" leftSection={<IconSettings size={12} />}>
              Facturation
            </Tabs.Tab>
            <Tabs.Tab value="Avoir" leftSection={<IconSettings size={12} />}>
              Liste des avoirs
            </Tabs.Tab>
            <Tabs.Tab value="ANouveauPage" leftSection={<IconSettings size={12} />}>
              List des a-nouveaux
            </Tabs.Tab>
            <Tabs.Tab value="Reglement" leftSection={<IconSettings size={12} />}>
              List des reglements
            </Tabs.Tab>
          </Tabs.List>
    
          <Tabs.Panel value="Demandes_dachats" ml={20}>
           <Demandes_dachats/>
          </Tabs.Panel>
    
          <Tabs.Panel value="DemandePrix" ml={20}>
            <DemandePrix/>
          </Tabs.Panel>
    
          <Tabs.Panel value="BondeCommende" ml={20}>
            <BondeCommende/>
          </Tabs.Panel>
           <Tabs.Panel value="Bon_de_reception" ml={20}>
            <Bon_de_reception/>
          </Tabs.Panel>
    
          <Tabs.Panel value="BonDeposition" ml={20}>
            <BonDeposition/>
          </Tabs.Panel>
    
          <Tabs.Panel value="BonRetour" ml={20}>
            <BonRetour/>
          </Tabs.Panel>
           <Tabs.Panel value="FacturePage" ml={20}>
           <FacturePage/>
          </Tabs.Panel>
           <Tabs.Panel value="Avoir" ml={20}>
          <Avoir/>
          </Tabs.Panel>
           <Tabs.Panel value="ANouveauPage" ml={20}>
         <ANouveauPage/>
          </Tabs.Panel>
           <Tabs.Panel value="Reglement" ml={20}>
           <Reglement/>
          </Tabs.Panel>
        </Tabs>
  )
}

export default Achat
