'use client';
import React, { useState } from 'react';
import {
  Group,
  Table,
  Text,
  Card,
  Box,
  TextInput,
 
  Checkbox,
  Button,
  Select,
  Pagination,
  Modal,
} from '@mantine/core';
import {
  IconSearch,

  IconPlus,
} from '@tabler/icons-react';

// Import du composant Reglement_form
import Reglement_form from './Reglement_form';

// Interface pour les données de règlement
interface ReglementData {
  id: number;
  numeroReglement: string;
  date: string;
  beneficiaire: string;
  payeur: string;
  mode: string;
  montant: number;
  reliquat: number;
}

const Mes_reglements = () => {
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');

  const [selectedReglements, setSelectedReglements] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Données d'exemple pour les règlements
  const reglementsData: ReglementData[] = [
    {
      id: 1,
      numeroReglement: 'Rechercher',
      date: 'Recher...',
      beneficiaire: 'Rechercher',
      payeur: 'Rechercher',
      mode: 'Reche...',
      montant: 0.00,
      reliquat: 0.00
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredReglements = reglementsData.filter(reglement =>
    reglement.beneficiaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reglement.numeroReglement.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reglement.payeur.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gestion de la sélection
  const handleSelectReglement = (id: number) => {
    setSelectedReglements(prev =>
      prev.includes(id)
        ? prev.filter(reglementId => reglementId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedReglements.length === filteredReglements.length) {
      setSelectedReglements([]);
    } else {
      setSelectedReglements(filteredReglements.map(reglement => reglement.id));
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredReglements.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentReglements = filteredReglements.slice(startIndex, endIndex);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec onglets */}
     

      {/* Barre de recherche et boutons d'action */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          {/* Barre de recherche */}
          <Group align="center" gap="sm">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />
          </Group>

          {/* Boutons d'action à droite */}
          <Group gap="xs" align="center">
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsModalOpen(true)}
            >
              Règlement
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedReglements.length === filteredReglements.length && filteredReglements.length > 0}
                  indeterminate={selectedReglements.length > 0 && selectedReglements.length < filteredReglements.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N°. Règlement
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Bénéficiaire
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Payeur
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mo...
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mo...
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                Reli...
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentReglements.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={8} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentReglements.map((reglement) => (
                <Table.Tr key={reglement.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      checked={selectedReglements.includes(reglement.id)}
                      onChange={() => handleSelectReglement(reglement.id)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {reglement.numeroReglement}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {reglement.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {reglement.beneficiaire}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {reglement.payeur}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {reglement.mode}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {reglement.montant.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-right">
                    <Text size="sm" className="text-gray-800">
                      {reglement.reliquat.toFixed(2)}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 15)}
              data={['15', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {filteredReglements.length > 0
                ? `${startIndex + 1} - ${Math.min(endIndex, filteredReglements.length)} de ${filteredReglements.length}`
                : '0 - 0 de 0'
              }
            </Text>
          </Group>

          <Pagination
            total={totalPages || 1}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création de règlement */}
      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nouveau Règlement"
        size="95%"
        centered
        className="modal-reglement"
      >
        <Reglement_form />
      </Modal>
    </Box>
  );
};

export default Mes_reglements;
