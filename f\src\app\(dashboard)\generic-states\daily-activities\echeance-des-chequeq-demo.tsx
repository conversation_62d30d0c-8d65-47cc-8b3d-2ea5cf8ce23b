'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { EcheanceDesChequeq } from './Echeance_des_chequeq';

export default function EcheanceDesChequeqDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    const searchField = query.date_field === 'due_date' ? 'Date d\'échéance' : 'Date d\'Encaissement';
    console.log(`Recherche par ${searchField}: du ${startDate} au ${endDate}`);
  };

  const handleDisplayTypeChange = (type: 'flat' | 'compact') => {
    console.log('Type d\'affichage changé:', type);
    const typeLabel = type === 'flat' ? 'Affichage aplatie' : 'Tableau croisé-dynamique';
    alert(`Type d'affichage sélectionné: ${typeLabel}`);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des échéances de chèques en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des échéances de chèques en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'payable_title': 'Titre du payable',
      'payment_mode': 'Mode de paiement',
      'beneficiary': 'Bénéficiaire',
      'doctor': 'Médecin',
      'amount': 'Montant',
      'due_date': 'Date d\'échéance'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={false}
            onQueryChange={handleQueryChange}
            onDisplayTypeChange={handleDisplayTypeChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function EcheanceDesChequeqLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onDisplayTypeChange={(type) => console.log('Display type:', type)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
            onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function EcheanceDesChequeqWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données des échéances de chèques chargées pour:', query);
    }, 1000);
  };

  const handleDisplayTypeChange = (type: 'flat' | 'compact') => {
    console.log('Changement de type d\'affichage:', type);
    if (type === 'compact') {
      console.log('Mode tableau croisé-dynamique activé');
    } else {
      console.log('Mode affichage aplatie activé');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des échéances de chèques avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des échéances de chèques avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'payable_title': 'Tri des titres payables',
      'payment_mode': 'Tri par mode de paiement',
      'beneficiary': 'Tri par bénéficiaire',
      'doctor': 'Tri par médecin',
      'amount': 'Tri par montant',
      'due_date': 'Tri par date d\'échéance'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={false}
            onQueryChange={handleQueryChange}
            onDisplayTypeChange={handleDisplayTypeChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec recherche par date d'encaissement
export function EcheanceDesChequeqEncaissementDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query recherche par encaissement:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    
    if (query.date_field === 'encasement_date') {
      alert(`Recherche par Date d'Encaissement du ${startDate} au ${endDate}`);
    } else {
      alert(`Recherche par Date d'échéance du ${startDate} au ${endDate}`);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={false}
            onQueryChange={handleQueryChange}
            onDisplayTypeChange={(type) => console.log('Display type encaissement:', type)}
            onExport={(format) => alert(`Export ${format} par date d'encaissement`)}
            onPrint={() => alert('Impression par date d\'encaissement')}
            onSort={(columnId, direction) => console.log('Sort encaissement:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec tableau croisé-dynamique
export function EcheanceDesChequeqCompactDemo() {
  const handleDisplayTypeChange = (type: 'flat' | 'compact') => {
    console.log('Changement vers mode compact:', type);
    if (type === 'compact') {
      alert('Mode Tableau croisé-dynamique activé - Vue condensée des échéances de chèques');
    } else {
      alert('Mode Affichage aplatie activé - Vue détaillée des échéances de chèques');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={false}
            onQueryChange={(query) => console.log('Query compact:', query)}
            onDisplayTypeChange={handleDisplayTypeChange}
            onExport={(format) => alert(`Export ${format} en mode compact`)}
            onPrint={() => alert('Impression en mode compact')}
            onSort={(columnId, direction) => console.log('Sort compact:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function EcheanceDesChequeqErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec gestion d\'erreurs:', query);
    
    // Simuler une validation de période
    const startDate = new Date(query.start);
    const endDate = new Date(query.end);
    
    if (endDate < startDate) {
      alert('Erreur: La date de fin ne peut pas être antérieure à la date de début');
      return;
    }
    
    // Simuler une période trop longue
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      alert('Attention: La période sélectionnée est très longue (plus d\'un an). Cela peut affecter les performances.');
    }
    
    console.log('Période validée, chargement des échéances...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EcheanceDesChequeq
            loading={false}
            onQueryChange={handleQueryChange}
            onDisplayTypeChange={(type) => console.log('Display type avec validation:', type)}
            onExport={(format) => {
              console.log(`Export ${format} avec validation`);
              if (confirm(`Êtes-vous sûr de vouloir exporter les échéances de chèques en ${format.toUpperCase()} ?`)) {
                alert('Export en cours...');
              }
            }}
            onPrint={() => {
              console.log('Impression avec validation');
              if (confirm('Êtes-vous sûr de vouloir imprimer les échéances de chèques ?')) {
                alert('Impression en cours...');
              }
            }}
            onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
