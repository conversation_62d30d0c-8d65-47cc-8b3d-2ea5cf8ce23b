"""
Serializers for the billing app.
"""

from rest_framework import serializers
from .models import Invoice, InvoiceItem, Payment, Subscription, License


class InvoiceItemSerializer(serializers.ModelSerializer):
    """Serializer for InvoiceItem model."""
    
    class Meta:
        model = InvoiceItem
        fields = [
            'id', 'description', 'quantity', 'unit_price', 'total_price',
            'service_code', 'service_category', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_price', 'created_at', 'updated_at']


class InvoiceSerializer(serializers.ModelSerializer):
    """Serializer for Invoice model."""
    
    items = InvoiceItemSerializer(many=True, read_only=True)
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'patient', 'patient_name', 'appointment',
            'status', 'issue_date', 'due_date',
            'subtotal', 'tax_rate', 'tax_amount', 'discount_amount', 'total_amount',
            'paid_amount', 'balance_due', 'notes', 'terms_and_conditions',
            'items', 'created_at', 'updated_at', 'created_by'
        ]
        read_only_fields = ['id', 'tax_amount', 'total_amount', 'balance_due', 'created_at', 'updated_at']


class PaymentSerializer(serializers.ModelSerializer):
    """Serializer for Payment model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'payment_number', 'invoice', 'invoice_number', 'patient', 'patient_name',
            'amount', 'payment_method', 'status', 'transaction_id', 'reference_number',
            'payment_date', 'processed_date', 'notes',
            'created_at', 'updated_at', 'processed_by'
        ]
        read_only_fields = ['id', 'processed_date', 'created_at', 'updated_at']


class SubscriptionSerializer(serializers.ModelSerializer):
    """Serializer for Subscription model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    
    class Meta:
        model = Subscription
        fields = [
            'id', 'subscription_number', 'patient', 'patient_name',
            'plan', 'status', 'billing_cycle', 'monthly_price', 'setup_fee',
            'start_date', 'end_date', 'next_billing_date',
            'features', 'usage_limits', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LicenseSerializer(serializers.ModelSerializer):
    """Serializer for License model."""
    
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = License
        fields = [
            'id', 'license_key', 'patient', 'patient_name',
            'license_type', 'status', 'issue_date', 'expiry_date',
            'features', 'restrictions', 'activation_count', 'max_activations',
            'is_expired', 'days_until_expiry', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_expired', 'days_until_expiry', 'created_at', 'updated_at']


class PaymentConfigurationSerializer(serializers.Serializer):
    """Serializer for payment configuration."""
    
    stripe_publishable_key = serializers.CharField(max_length=200, required=False)
    paypal_client_id = serializers.CharField(max_length=200, required=False)
    payment_methods = serializers.ListField(
        child=serializers.CharField(max_length=50),
        default=['cash', 'credit_card', 'bank_transfer']
    )
    currency = serializers.CharField(max_length=3, default='USD')
    tax_rate = serializers.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    late_fee_rate = serializers.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    payment_terms_days = serializers.IntegerField(default=30)
    auto_send_invoices = serializers.BooleanField(default=True)  # type: ignore
    auto_send_reminders = serializers.BooleanField(default=True)  # type: ignore
    reminder_days_before = serializers.IntegerField(default=7)
    
    def to_representation(self, instance):
        """Return mock payment configuration for development."""
        # Type ignore for basedpyright OrderedDict vs dict return type mismatch
        result = super().to_representation(instance)  # type: ignore
        result.update({
            'stripe_publishable_key': 'pk_test_mock_key_for_development',
            'paypal_client_id': 'mock_paypal_client_id',
            'payment_methods': ['cash', 'credit_card', 'debit_card', 'bank_transfer', 'insurance'],
            'currency': 'USD',
            'tax_rate': '8.50',
            'late_fee_rate': '1.50',
            'payment_terms_days': 30,
            'auto_send_invoices': True,
            'auto_send_reminders': True,
            'reminder_days_before': 7,
            'is_active': True,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z'
        })
        return result
