'use client';
import { useState } from 'react';
import {
  Modal,
  Stack,
  Text,
  ScrollArea,
  Group,
  Button,
  ActionIcon,
  Input,
  Checkbox,
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiViewHeadline,
  mdiPlus,
  mdiClose,
  mdiDelete,
  mdiAccountAlert,
} from '@mdi/js';

interface TreeNodeChoixMultiple {
  uid: string;
  value: string;
  nodes?: TreeNodeChoixMultiple[];
}

interface DictionaryModalProps {
  opened: boolean;
  onClose: () => void;
  onValidate: (selectedValues: string[]) => void;
  treeData: TreeNodeChoixMultiple[];
}

const DictionaryModal = ({ opened, onClose, onValidate, treeData }: DictionaryModalProps) => {
  // États pour la gestion des modèles
  const [showModels, setShowModels] = useState(false);
  const [showAddModel, setShowAddModel] = useState(false);
  const [modelTitle, setModelTitle] = useState('');
  const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[]}>>([]);
  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());

  const toggleNodeSelection = (nodeId: string) => {
    setSelectedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const toggleNodeCollapse = (nodeId: string) => {
    setCollapsedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
    const allNodes: TreeNodeChoixMultiple[] = [];
    nodes.forEach(node => {
      allNodes.push(node);
      if (node.nodes) {
        allNodes.push(...getAllNodes(node.nodes));
      }
    });
    return allNodes;
  };

  const getSelectedValues = () => {
    const allNodes = getAllNodes(treeData);
    return Array.from(selectedNodes)
      .map(id => allNodes.find(node => node.uid === id))
      .filter(Boolean)
      .map(node => node!.value);
  };

  const handleValidate = () => {
    let textToAdd = '';
    
    if (showModels) {
      // Valider les modèles sélectionnés
      const selectedModelTexts = savedModels
        .filter(model => selectedNodes.has(model.id))
        .flatMap(model => model.selections);
      textToAdd = selectedModelTexts.join(', ');
    } else {
      // Valider les sélections du dictionnaire
      const selectedValues = getSelectedValues();
      textToAdd = selectedValues.join(', ');
    }
    
    if (textToAdd) {
      onValidate(selectedValues);
    }
    
    // Fermer le modal et réinitialiser
    handleCancel();
  };

  const handleCancel = () => {
    // Réinitialiser tous les états
    setSelectedNodes(new Set());
    setShowModels(false);
    setShowAddModel(false);
    setModelTitle('');
    onClose();
  };

  const handleAddModel = () => {
    if (selectedNodes.size > 0) {
      setShowAddModel(true);
    }
  };

  const handleSaveModel = () => {
    if (modelTitle.trim()) {
      const selectedValues = getSelectedValues();
      const newModel = {
        id: `model-${Date.now()}`,
        title: modelTitle.trim(),
        selections: selectedValues
      };
      
      setSavedModels(prev => [...prev, newModel]);
      setModelTitle('');
      setShowAddModel(false);
      setSelectedNodes(new Set());
      
      // Afficher les modèles après sauvegarde
      setShowModels(true);
    }
  };

  const handleDeleteModel = (modelId: string) => {
    setSavedModels(prev => prev.filter(model => model.id !== modelId));
    setSelectedNodes(prev => {
      const newSet = new Set(prev);
      newSet.delete(modelId);
      return newSet;
    });
  };

  // Composant TreeItem simplifié (vous devrez l'adapter selon votre implémentation)
  const TreeItem = ({ node }: { node: TreeNodeChoixMultiple }) => (
    <Group key={node.uid} p="xs">
      <Checkbox
        checked={selectedNodes.has(node.uid)}
        onChange={() => toggleNodeSelection(node.uid)}
      />
      <Text>{node.value}</Text>
    </Group>
  );

  return (
    <Modal
      opened={opened}
      onClose={handleCancel}
      title={
        <Group>
          <Icon path={mdiViewHeadline} size={1} />
          <Text fw={500}>Modèles de dictionnaire</Text>
          {!showModels && !showAddModel && (
            <ActionIcon 
              variant="subtle" 
              color="blue"
              onClick={() => setShowModels(true)}
            >
              <Icon path={mdiPlus} size={0.8} />
            </ActionIcon>
          )}
          <ActionIcon variant="subtle" color="red" onClick={handleCancel}>
            <Icon path={mdiClose} size={0.8} />
          </ActionIcon>
        </Group>
      }
      size="md"
      radius={0}
      transitionProps={{ transition: 'fade', duration: 200 }}
      centered
      withCloseButton={false}
    >
      <div style={{ padding: '20px' }}>
        {/* Modal d'ajout de modèle */}
        {showAddModel ? (
          <Stack>
            <Text size="lg" fw={500}>Titre du modèle</Text>
            <Input
              placeholder="Titre"
              value={modelTitle}
              onChange={(e) => setModelTitle(e.currentTarget.value)}
            />
            <Group justify="flex-end" mt="md">
              <Button 
                variant="filled" 
                color="gray"
                onClick={handleSaveModel}
                disabled={!modelTitle.trim()}
              >
                Enregistrer
              </Button>
              <Button 
                variant="outline" 
                color="red" 
                onClick={() => setShowAddModel(false)}
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        ) : showModels ? (
          /* Vue des modèles sauvegardés */
          <Stack>
            {savedModels.length === 0 ? (
              <Group justify="center" p="xl">
                <Stack align="center">
                  <Icon path={mdiAccountAlert} size={2} color="#ffa726" />
                  <Text c="dimmed">Aucune modèle à afficher</Text>
                </Stack>
              </Group>
            ) : (
              <>
                {savedModels.map((model) => (
                  <Group key={model.id} justify="space-between" p="sm" style={{ border: '1px solid #e0e0e0', borderRadius: '4px' }}>
                    <Group>
                      <Checkbox
                        checked={selectedNodes.has(model.id)}
                        onChange={() => toggleNodeSelection(model.id)}
                      />
                      <Text>{model.title}</Text>
                    </Group>
                    <ActionIcon 
                      variant="subtle" 
                      color="red"
                      onClick={() => handleDeleteModel(model.id)}
                    >
                      <Icon path={mdiDelete} size={0.8} />
                    </ActionIcon>
                  </Group>
                ))}
                
                <Group justify="flex-end" mt="md">
                  <Button onClick={handleValidate}>
                    Valider
                  </Button>
                  <Button variant="outline" color="red" onClick={handleCancel}>
                    Annuler
                  </Button>
                </Group>
              </>
            )}
          </Stack>
        ) : (
          /* Vue du dictionnaire principal */
          <Stack>
            {savedModels.length === 0 ? (
              <div>
                <Group justify="center" p="sm" style={{ backgroundColor: '#fff3cd', borderRadius: '4px', border: '1px solid #ffeaa7' }}>
                  <Icon path={mdiAccountAlert} size={1} color="#f39c12" />
                  <Text c="dimmed">Aucune modèle à afficher</Text>
                </Group>
                <Group justify="flex-end" mt="md">
                  <Button onClick={handleValidate}>
                    Valider
                  </Button>
                  <Button variant="outline" color="red" onClick={handleCancel}>
                    Annuler
                  </Button>
                </Group>
              </div>
            ) : null}
            
            {/* Arbre de sélection */}
            <ScrollArea h={300}>
              {treeData.map((node) => (
                <TreeItem key={node.uid} node={node} />
              ))}
            </ScrollArea>

            {/* Boutons d'action */}
            <Group justify="space-between" mt="md">
              <Group>
                {selectedNodes.size > 0 && (
                  <Button 
                    variant="filled" 
                    color="blue"
                    onClick={handleAddModel}
                  >
                    Ajouter model
                  </Button>
                )}
                <Button onClick={handleValidate}>
                  Valider
                </Button>
              </Group>
              <Button variant="outline" color="red" onClick={handleCancel}>
                Annuler
              </Button>
            </Group>
          </Stack>
        )}
      </div>
    </Modal>
  );
};

export default DictionaryModal;
