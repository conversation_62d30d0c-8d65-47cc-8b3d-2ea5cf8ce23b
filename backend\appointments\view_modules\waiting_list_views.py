"""
Waiting list management views for frontend integration.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction, models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta
import logging

from appointments.models import Appointment
from appointments.serializers import AppointmentSerializer, AppointmentListSerializer
from users.models import Patient

User = get_user_model()
logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_waiting_list(request):
    """
    Get all appointments in waiting list.
    """
    print(f"🔍 get_waiting_list VIEW CALLED with method: {request.method}")
    print(f"🔍 Request path: {request.path}")
    print(f"🔍 Request full path: {request.get_full_path()}")
    try:
        doctor_id = request.GET.get('doctor_id')
        date_filter = request.GET.get('date')  # Optional date filter

        queryset = Appointment.objects.filter(is_waiting_list=True).select_related(
            'patient', 'doctor'
        )

        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)

        # Filter by date if provided, otherwise default to today
        if date_filter:
            try:
                filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                queryset = queryset.filter(appointment_date=filter_date)
            except ValueError:
                # Invalid date format, default to today
                queryset = queryset.filter(appointment_date=timezone.now().date())
        else:
            # Default to today's appointments only
            queryset = queryset.filter(appointment_date=timezone.now().date())

        appointments = queryset.order_by('created_at')
        
        # Format for frontend
        waiting_list = []
        for appointment in appointments:
            waiting_list.append({
                'id': str(appointment.id),
                'first_name': appointment.patient.first_name,  # patient is now a User
                'last_name': appointment.patient.last_name,
                'title': f"{appointment.patient.first_name} {appointment.patient.last_name}",
                'phone_number': appointment.patient_phone or appointment.patient.phone_number,
                'typeConsultation': appointment.consultation_type or appointment.appointment_type,
                'eventType': appointment.event_type or 'visit',
                'duration': appointment.duration_minutes,
                'color': appointment.color or '#3799CE',
                'docteur': appointment.doctor_assigned or (appointment.doctor.get_full_name() if appointment.doctor else ''),
                'desc': appointment.description or '',
                'address': appointment.patient_address or appointment.patient.address,
                'notes': appointment.notes or '',
                'date': appointment.appointment_date.isoformat() if appointment.appointment_date else '',
                'start': appointment.appointment_time.strftime('%H:%M') if appointment.appointment_time else '',
                'end': '',  # Will be calculated on frontend
                'agenda': appointment.agenda or '',
                'isActive': appointment.is_active,
                'status': appointment.status,
                'created_at': appointment.created_at.isoformat(),
            })
        
        return Response({'waiting_list': waiting_list})
        
    except Exception as e:
        logger.error(f"Error getting waiting list: {str(e)}")
        return Response(
            {'error': 'Failed to get waiting list'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def add_to_waiting_list(request):
    """
    Add a patient to the waiting list.
    """
    try:
        data = request.data
        
        # Validate required fields
        required_fields = ['first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return Response(
                    {'error': f'{field} is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        with transaction.atomic():
            # Find or create patient
            patient = None
            patient_id = data.get('patient_id')
            
            if patient_id:
                try:
                    patient = Patient.objects.get(patient_id=patient_id)
                except Patient.DoesNotExist:
                    pass
            
            if not patient:
                # Create new patient
                email = data.get('email') or f"{data['first_name'].lower()}.{data['last_name'].lower()}@temp.com"

                # Check if user with this email already exists
                try:
                    existing_user = User.objects.get(email=email)
                    # If user exists, use it and update missing fields
                    user = existing_user
                    logger.info(f"Using existing user with email: {email}")

                    # Update user with any missing patient fields
                    updated = False
                    if data.get('birth_date') and not user.date_of_birth:
                        user.date_of_birth = data.get('birth_date')
                        updated = True
                    if data.get('gender') and not user.gender:
                        user.gender = data.get('gender')
                        updated = True
                    if updated:
                        user.save()
                        logger.info(f"Updated existing user with missing fields")
                except User.DoesNotExist:
                    # If email doesn't exist, create new user
                    try:
                        user = User.objects.create_user(
                            email=email,
                            first_name=data['first_name'],
                            last_name=data['last_name'],
                            phone_number=data.get('phone_number', ''),
                            address=data.get('address', ''),
                            user_type='patient',
                            password='temp123',
                            # CRITICAL: Add missing patient fields
                            date_of_birth=data.get('birth_date'),
                            gender=data.get('gender'),
                            age=data.get('age')
                        )
                    except Exception as e:
                        # If still fails, generate unique email with timestamp
                        import time
                        unique_email = f"{data['first_name'].lower()}.{data['last_name'].lower()}.{int(time.time())}@temp.com"
                        user = User.objects.create_user(
                            email=unique_email,
                            first_name=data['first_name'],
                            last_name=data['last_name'],
                            phone_number=data.get('phone_number', ''),
                            address=data.get('address', ''),
                            # CRITICAL: Add missing patient fields
                            date_of_birth=data.get('birth_date'),
                            gender=data.get('gender'),
                            age=data.get('age'),
                            user_type='patient',
                            password='temp123'
                        )
                
                # Check if patient record already exists for this user
                try:
                    patient = Patient.objects.get(user=user)
                    logger.info(f"Using existing patient record for user: {user.email}")
                except Patient.DoesNotExist:
                    # Create new patient record
                    patient = Patient.objects.create(
                        user=user,
                        date_of_birth=data.get('birth_date'),
                        gender=data.get('gender', ''),
                        notes=data.get('notes', ''),
                        assigned_doctor_id=data.get('doctor_assigned')
                    )
            
            # Create appointment in waiting list with ALL patient fields
            appointment = Appointment.objects.create(
                patient=user,  # Use user instance, not patient instance
                doctor_id=data.get('doctor_assigned'),
                title=f"{data['first_name']} {data['last_name']}",
                description=data.get('desc', ''),
                appointment_type=data.get('typeConsultation', 'consultation'),
                status='waiting_list',
                appointment_date=data.get('date') or timezone.now().date(),
                appointment_time=data.get('start') and datetime.strptime(data['start'], '%H:%M').time() or timezone.now().time(),
                duration_minutes=data.get('duration', 30),
                is_waiting_list=True,
                color=data.get('color', '#3799CE'),
                event_type=data.get('eventType', 'visit'),
                patient_phone=data.get('phone_number'),
                patient_address=data.get('address'),
                consultation_type=data.get('typeConsultation'),
                doctor_assigned=data.get('docteur'),
                agenda=data.get('agenda'),
                notes=data.get('notes'),
                # CRITICAL: Add all the missing patient fields
                gender=data.get('gender'),
                etat_civil=data.get('etat_civil'),
                cin=data.get('cin'),
                social_security=data.get('social_security'),
                profession=data.get('profession'),
                birth_place=data.get('birth_place'),
                father_name=data.get('father_name'),
                mother_name=data.get('mother_name'),
                blood_group=data.get('blood_group'),
                allergies=data.get('allergies'),
                comment=data.get('comment'),
                Commentairelistedattente=data.get('Commentairelistedattente'),
                patient_title=data.get('patient_title'),
                created_by=request.user if request.user.is_authenticated else None
            )
            
            return Response({
                'id': str(appointment.id),
                'patient_id': patient.patient_id,
                'message': 'Patient added to waiting list successfully'
            }, status=status.HTTP_201_CREATED)
            
    except Exception as e:
        logger.error(f"Error adding to waiting list: {str(e)}")
        return Response(
            {'error': f'Failed to add to waiting list: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def move_to_calendar(request, appointment_id):
    """
    Move an appointment from waiting list to calendar.
    """
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id, is_waiting_list=True)
        data = request.data
        
        # Validate required fields for scheduling
        if not data.get('appointment_date') or not data.get('appointment_time'):
            return Response(
                {'error': 'appointment_date and appointment_time are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            appointment.is_waiting_list = False
            appointment.status = 'scheduled'
            appointment.appointment_date = data['appointment_date']
            appointment.appointment_time = data['appointment_time']
            
            # Update other fields if provided
            if data.get('room'):
                appointment.room = data['room']
            if data.get('resource_id'):
                appointment.resource_id = data['resource_id']
            if data.get('duration'):
                appointment.duration_minutes = data['duration']
            
            appointment.save()
        
        serializer = AppointmentSerializer(appointment)
        return Response({
            'appointment': serializer.data,
            'message': 'Appointment moved to calendar successfully'
        })
        
    except Exception as e:
        logger.error(f"Error moving appointment {appointment_id} to calendar: {str(e)}")
        return Response(
            {'error': f'Failed to move to calendar: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['DELETE'])
@permission_classes([permissions.AllowAny])
def remove_from_waiting_list(request, appointment_id):
    """
    Remove an appointment from waiting list.
    """
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id, is_waiting_list=True)
        
        with transaction.atomic():
            appointment.delete()
        
        return Response({
            'message': 'Appointment removed from waiting list successfully'
        })
        
    except Exception as e:
        logger.error(f"Error removing appointment {appointment_id} from waiting list: {str(e)}")
        return Response(
            {'error': f'Failed to remove from waiting list: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['PUT'])
@permission_classes([permissions.AllowAny])
def update_waiting_list_item(request, appointment_id):
    """
    Update an appointment in the waiting list.
    """
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id, is_waiting_list=True)
        data = request.data
        
        with transaction.atomic():
            # Update appointment fields
            if data.get('title'):
                appointment.title = data['title']
            if data.get('description'):
                appointment.description = data['description']
            if data.get('duration'):
                appointment.duration_minutes = data['duration']
            if data.get('notes'):
                appointment.notes = data['notes']
            if data.get('color'):
                appointment.color = data['color']
            if data.get('consultation_type'):
                appointment.consultation_type = data['consultation_type']
            if data.get('doctor_assigned'):
                appointment.doctor_assigned = data['doctor_assigned']
            if data.get('agenda'):
                appointment.agenda = data['agenda']
            
            # Update patient information if provided
            if any(field in data for field in ['first_name', 'last_name', 'phone_number', 'address', 'email']):
                patient = appointment.patient
                user = patient.user
                
                if data.get('first_name'):
                    user.first_name = data['first_name']
                if data.get('last_name'):
                    user.last_name = data['last_name']
                if data.get('phone_number'):
                    user.phone_number = data['phone_number']
                    appointment.patient_phone = data['phone_number']
                if data.get('address'):
                    user.address = data['address']
                    appointment.patient_address = data['address']
                if data.get('email'):
                    user.email = data['email']
                
                user.save()
                
                # Update appointment title if name changed
                if data.get('first_name') or data.get('last_name'):
                    appointment.title = f"{user.first_name} {user.last_name}"
            
            appointment.save()
        
        serializer = AppointmentSerializer(appointment)
        return Response({
            'appointment': serializer.data,
            'message': 'Waiting list item updated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error updating waiting list item {appointment_id}: {str(e)}")
        return Response(
            {'error': f'Failed to update waiting list item: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def activate_appointment(request, appointment_id):
    """
    Activate an appointment (move to presentation room).
    """
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id)
        
        with transaction.atomic():
            appointment.is_active = True
            appointment.status = 'in_progress'
            appointment.save()
        
        serializer = AppointmentSerializer(appointment)
        return Response({
            'appointment': serializer.data,
            'message': 'Appointment activated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error activating appointment {appointment_id}: {str(e)}")
        return Response(
            {'error': f'Failed to activate appointment: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
