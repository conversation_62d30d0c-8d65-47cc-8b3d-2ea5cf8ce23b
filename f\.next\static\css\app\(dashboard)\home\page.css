/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/styles/DndList.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/************************/
.DndList_root__KgAcf {
  border-top-left-radius: var(--mantine-radius-xl);
  border-bottom-left-radius: var(--mantine-radius-xl);
  padding-left: 4px;

  /* The following styles will be applied only when button is disabled */
  &[data-disabled] {
    /* You can use Mantine PostCSS mixins inside data attributes */
    @mixin light {
      border: 1px solid var(--mantine-color-gray-2);
    }

    @mixin dark {
      border: 1px solid var(--mantine-color-dark-4);
    }

    /* You can target child elements that are inside .root[data-disabled] */
    & .DndList_section__xOORs[data-position="left"] {
      opacity: 0.6;
    }
  }
}

.DndList_section__xOORs {
  /* Apply styles only to left section */
  &[data-position="left"] {
    --section-size: calc(var(--button-height) - 8px);

    background-color: var(--mantine-color-body);
    color: var(--mantine-color-text);
    height: var(--section-size);
    width: var(--section-size);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--mantine-radius-xl);
  }

  &[data-position="right"] {
    @mixin rtl {
      transform: rotate(180deg);
    }
  }
}
.DndList_itemIcon__Dytgt {
  padding: var(--mantine-spacing-xs);
  margin-right: var(--mantine-spacing-md);
}
.DndList_itemTitle___dkR4 {
  margin-bottom: calc(var(--mantine-spacing-xs) / 2);
}

/* .textInput {
  &::placeholder {
    color: var(--mantine-color-red-5);
    
  }
} */
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/(dashboard)/home/<USER>/DayView.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/* DayView.css */
.rbc-toolbar {
  height: 84px;
}
/* topheader */
.rbc-time-view {
  background-color: var(--content-background);
  color: var(--text-daisy);
  border: 0px solid var(--rbc-border) !important;
}
/* DayView.css */
.rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;

  position: relative;
  padding-top: 15px;
  /* overflow-y: auto; */
}
/* *********************************** //////////////////////////////////////////////////////////////*/
.rbc-time-view .rbc-time-gutter {
  width: 83px;
  text-align: right;
  white-space: nowrap;
  position: relative;
}
.rbc-time-column {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.rbc-time-column .rbc-timeslot-group,
.rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  min-height: 40px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
}
.rbc-time-content > * + * > * {
  border-left: 1px solid var(--rbc-border) !important;
}
.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid var(--rbc-border) !important;
}

.rbc-time-slot .rbc-label {
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  font-size: 0.625rem;
  font-family: Rubik, sans-serif;
  height: 20px;
  width: 47px;
  margin: 0px auto;
  border-radius: 8px;
  position: relative;
  top: -10px !important;
  background-color: var(--content-background);
}
.rbc-time-slot {
  color: #74a4c3;
}

/* .rbc-day-slot .rbc-events-container:after {
  background: url(/add.svg) center center / 16px 16px no-repeat !important;
} */

/* تحسين التصميم للخط الحالي */
.current-time {
  position: relative !important;
  height: 100% !important;
}

@media screen and (min-width: 767.98px) {
  .rbc-day-slot .rbc-timeslot-group .current-time .time-indicator {
    left: 10px !important; /*-84*/
    width: calc(100% + 84px) !important;
  }
}

.rbc-timeslot-group .current-time .time-indicator,
.rbc-timeslot-group .current-time .time-indicator .label {
  position: absolute;
}

.rbc-day-slot .rbc-timeslot-group .current-time .time-indicator .label {
  left: 8px;
}

@media screen and (min-width: 767.98px) {
  .rbc-day-slot .rbc-timeslot-group .current-time .time-indicator .label {
    left: 18px;
  }
}

.rbc-timeslot-group .current-time .time-indicator .label {
  background-color: #06b1bd; /* استخدام لون HEX مختصر */
  top: -50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-family: Rubik, sans-serif;
  height: 20px;
  width: 47px;
  margin: 0 auto;
  border-radius: 8px;
  color: #fff; /* استخدام لون HEX مختصر */
  box-shadow: 0 1px 8px rgba(38, 98, 240, 0.4);
}
 .current-time .time-indicator {
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #06b1bd;
  pointer-events: none;
  width: 100%;
  transition: top 0.3s ease;
  top: 50%;
  transform: translateY(-50%);
 
}
.rbc-current-time-indicator {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 0px;
  background-color: #06b1bd;
  pointer-events: none;
}
/* .rbc-time-slot:not(.rbc-today .rbc-time-slot) {
  background-color: #fff;
} */
.rbc-time-slot {
  color: #74a4c3;
  z-index: 1;
}

.rbc-event,
.rbc-background-event {
  z-index: 2;
  /* background-color: red; */
  font-size: 13.2px;
}
.rbc-day-slot .rbc-event-content {
  word-wrap: break-word;
  flex: 1 1;
  height: 100%;
  line-height: 1;
  min-height: 1em;
  width: 100%;
}
.rbc-event-content {
  backface-visibility: visible;
}
   .rbc-time-content > * + * > * {
  background-image: url("/add.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position-x: center;
  background-position-y: center;
}   
 /* DayView.css */
 .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 15px !important; 
  /* overflow-y: auto; */
}
.rbc-toolbar {
  /* height: 84px; */
   display: none; 
}


/* ////////////////////////////////////////////// */

/* TimeSlot Component Styles - for AgendaRoomView */
.current-time {
  position: relative;
  height: 100%;
}

.current-time .time-indicator {
  position: absolute !important;
  z-index: 50 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background-color: #06b1bd !important;
  pointer-events: none !important;
  width: 100% !important;
  transition: top 0.3s ease !important;
  transform: translateY(-50%) !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.current-time .time-indicator .label {
  position: absolute !important;
  background-color: #06b1bd !important;
  top: 0px !important;
  left: 8px !important;
  transform: translateY(-50%) !important;
  display: block !important;
  text-align: center !important;
  line-height: 18px !important;
  font-size: 10px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  height: 18px !important;
  width: auto !important;
  min-width: 36px !important;
  padding: 0 6px !important;
  margin: 0 !important;
  border-radius: 9px !important;
  color: #fff !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15) !important;
  font-weight: 600 !important;
  z-index: 1000 !important;
  visibility: visible !important;
  opacity: 1 !important;
  white-space: nowrap !important;
  letter-spacing: 0.5px !important;
  pointer-events: none !important;
}

/* Fallback for any label that might not be styled */
.time-indicator .label {
  position: absolute !important;
  background-color: #06b1bd !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  z-index: 1000 !important;
  top: -12px !important;
  left: 8px !important;
}

@media screen and (min-width: 767.98px) {
  .current-time .time-indicator {
    left: 10px !important;
    width: calc(100% + 84px) !important;
  }

  .current-time .time-indicator .label {
    left: 18px;
  }
}
/* .event-content {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}*/
/* .event-content.expanded {
  z-index: 9999 !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}  */
.event-content {
  height: 100%;
  transition: all 0.3s ease-in-out;

}  
.description {
  margin-top: 8px;
  transition: all 0.3s ease-in-out;
  transform-origin: top;
} 

.description.hidden {
  opacity: 0;
  transform: scaleY(0);
  height: 0;
  margin: 0;
}

.description.visible {
  opacity: 1;
  transform: scaleY(1);
  height: auto;

 /* position: absolute;
  --popover-shadow: var(--mantine-shadow-md);
  transition-property: opacity;
  transition-duration: 150ms;
  transition-timing-function: ease;
  opacity: 1;
  z-index: 300;
  top: 273.3px;
  left: 660.4px;
  width: calc(12.5rem* var(--mantine-scale));  */
}

/* Override react-big-calendar's default styles */
.rbc-event {
  height: auto !important;
  max-height: none !important;
}

.rbc-event-content {
  height: auto !important;
}

/* .rbc-header {
  background-color: #f3f4f6;
  padding: 8px;
  font-weight: bold;
} */

/* .rbc-header span {
  display: block;
  text-align: center;
} */
/* .rbc-time-view .rbc-time-header {
  display: none;
} */
.rbc-time-view .rbc-time-header {
    display: flex;  
}
.rbc-time-header-content > .rbc-row.rbc-row-resource {
  border-bottom: 0px solid #ddd; 
}
.rbc-header {
  background-color: none !important;
   border-bottom: 0px solid #ddd !important;
}
.rbc-time-view-resources .rbc-time-gutter,
.rbc-time-view-resources .rbc-time-header-gutter {
  background-color: var(--header-nav-base) !important;
  border-right: 1px solid var(--border-color) !important;
 
}
.rbc-time-header-content {
  border-left: 1px solid var(--border-color) !important;
}
.rbc-header {
  background-color: var(--header-nav-base) !important;
  
}
.rbc-addons-dnd-resizable{
  width: 100% !important;
}
/* .rbc-time-column .rbc-timeslot-group, .rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  // min-height: 56px; 
  min-height: 26.6px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
} */
.rbc-time-column .rbc-timeslot-group, .rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  /* min-height: 56px; */
  min-height: 26.6px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
  
}



/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/react-big-calendar/lib/css/react-big-calendar.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.rbc-btn {
  color: inherit;
  font: inherit;
  margin: 0;
}

button.rbc-btn {
  overflow: visible;
  text-transform: none;
  -webkit-appearance: button;
     -moz-appearance: button;
          appearance: button;
  cursor: pointer;
}

button[disabled].rbc-btn {
  cursor: not-allowed;
}

button.rbc-input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.rbc-calendar {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}

.rbc-m-b-negative-3 {
  margin-bottom: -3px;
}

.rbc-h-full {
  height: 100%;
}

.rbc-calendar *,
.rbc-calendar *:before,
.rbc-calendar *:after {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
}

.rbc-abs-full, .rbc-row-bg {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.rbc-ellipsis, .rbc-show-more, .rbc-row-segment .rbc-event-content, .rbc-event-label {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rbc-rtl {
  direction: rtl;
}

.rbc-off-range {
  color: #999999;
}

.rbc-off-range-bg {
  background: #e6e6e6;
}

.rbc-header {
  overflow: hidden;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0%;
          flex: 1 0 0%;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 3px;
  text-align: center;
  vertical-align: middle;
  font-weight: bold;
  font-size: 90%;
  min-height: 0;
  border-bottom: 1px solid #ddd;
}
.rbc-header + .rbc-header {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-header > a, .rbc-header > a:active, .rbc-header > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-button-link {
  color: inherit;
  background: none;
  margin: 0;
  padding: 0;
  border: none;
  cursor: pointer;
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

.rbc-row-content {
  position: relative;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  z-index: 4;
}

.rbc-row-content-scrollable {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: 100%;
}
.rbc-row-content-scrollable .rbc-row-content-scroll-container {
  height: 100%;
  overflow-y: scroll;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  /* Hide scrollbar for Chrome, Safari and Opera */
}
.rbc-row-content-scrollable .rbc-row-content-scroll-container::-webkit-scrollbar {
  display: none;
}

.rbc-today {
  background-color: #eaf6ff;
}

.rbc-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
}
.rbc-toolbar .rbc-toolbar-label {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0 10px;
  text-align: center;
}
.rbc-toolbar button {
  color: #373a3c;
  display: inline-block;
  margin: 0;
  text-align: center;
  vertical-align: middle;
  background: none;
  background-image: none;
  border: 1px solid #ccc;
  padding: 0.375rem 1rem;
  border-radius: 4px;
  line-height: normal;
  white-space: nowrap;
}
.rbc-toolbar button:active, .rbc-toolbar button.rbc-active {
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  background-color: #e6e6e6;
  border-color: #adadad;
}
.rbc-toolbar button:active:hover, .rbc-toolbar button:active:focus, .rbc-toolbar button.rbc-active:hover, .rbc-toolbar button.rbc-active:focus {
  color: #373a3c;
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}
.rbc-toolbar button:focus {
  color: #373a3c;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.rbc-toolbar button:hover {
  color: #373a3c;
  cursor: pointer;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.rbc-btn-group {
  display: inline-block;
  white-space: nowrap;
}
.rbc-btn-group > button:first-child:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:first-child:not(:last-child) {
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:last-child:not(:first-child) {
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.rbc-btn-group button + button {
  margin-left: -1px;
}
.rbc-rtl .rbc-btn-group button + button {
  margin-left: 0;
  margin-right: -1px;
}
.rbc-btn-group + .rbc-btn-group, .rbc-btn-group + button {
  margin-left: 10px;
}

@media (max-width: 767px) {
  .rbc-toolbar {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.rbc-event, .rbc-day-slot .rbc-background-event {
  border: none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin: 0;
  padding: 2px 5px;
  background-color: #3174ad;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  width: 100%;
  text-align: left;
}
.rbc-slot-selecting .rbc-event, .rbc-slot-selecting .rbc-day-slot .rbc-background-event, .rbc-day-slot .rbc-slot-selecting .rbc-background-event {
  cursor: inherit;
  pointer-events: none;
}
.rbc-event.rbc-selected, .rbc-day-slot .rbc-selected.rbc-background-event {
  background-color: #265985;
}
.rbc-event:focus, .rbc-day-slot .rbc-background-event:focus {
  outline: 5px auto #3b99fc;
}

.rbc-event-label {
  font-size: 80%;
}

.rbc-event-overlaps {
  -webkit-box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);
          box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);
}

.rbc-event-continues-prior {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rbc-event-continues-after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-event-continues-earlier {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rbc-event-continues-later {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.rbc-row-segment {
  padding: 0 1px 1px 1px;
}
.rbc-selected-cell {
  background-color: rgba(0, 0, 0, 0.1);
}

.rbc-show-more {
  background-color: rgba(255, 255, 255, 0.3);
  z-index: 4;
  font-weight: bold;
  font-size: 85%;
  height: auto;
  line-height: normal;
  color: #3174ad;
}
.rbc-show-more:hover, .rbc-show-more:focus {
  color: #265985;
}

.rbc-month-view {
  position: relative;
  border: 1px solid #ddd;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
  width: 100%;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  height: 100%;
}

.rbc-month-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.rbc-month-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
  -ms-flex-preferred-size: 0px;
      flex-basis: 0px;
  overflow: hidden;
  height: 100%;
}
.rbc-month-row + .rbc-month-row {
  border-top: 1px solid #ddd;
}

.rbc-date-cell {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 0px;
          flex: 1 1 0;
  min-width: 0;
  padding-right: 5px;
  text-align: right;
}
.rbc-date-cell.rbc-now {
  font-weight: bold;
}
.rbc-date-cell > a, .rbc-date-cell > a:active, .rbc-date-cell > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-row-bg {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
  overflow: hidden;
  right: 1px;
}

.rbc-day-bg {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0%;
          flex: 1 0 0%;
}
.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-day-bg + .rbc-day-bg {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}

.rbc-overlay {
  position: absolute;
  z-index: 5;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
  padding: 10px;
}
.rbc-overlay > * + * {
  margin-top: 1px;
}

.rbc-overlay-header {
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -10px 5px -10px;
  padding: 2px 10px;
}

.rbc-agenda-view {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
  overflow: auto;
}
.rbc-agenda-view table.rbc-agenda-table {
  width: 100%;
  border: 1px solid #ddd;
  border-spacing: 0;
  border-collapse: collapse;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {
  padding: 5px 10px;
  vertical-align: top;
}
.rbc-agenda-view table.rbc-agenda-table .rbc-agenda-time-cell {
  padding-left: 15px;
  padding-right: 15px;
  text-transform: lowercase;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr + tr {
  border-top: 1px solid #ddd;
}
.rbc-agenda-view table.rbc-agenda-table thead > tr > th {
  padding: 3px 5px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}
.rbc-rtl .rbc-agenda-view table.rbc-agenda-table thead > tr > th {
  text-align: right;
}

.rbc-agenda-time-cell {
  text-transform: lowercase;
}
.rbc-agenda-time-cell .rbc-continues-after:after {
  content: " »";
}
.rbc-agenda-time-cell .rbc-continues-prior:before {
  content: "« ";
}

.rbc-agenda-date-cell,
.rbc-agenda-time-cell {
  white-space: nowrap;
}

.rbc-agenda-event-cell {
  width: 100%;
}

.rbc-time-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  min-height: 100%;
}
.rbc-time-column .rbc-timeslot-group {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #ddd;
  min-height: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
}

.rbc-time-gutter,
.rbc-header-gutter {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
}

.rbc-label {
  padding: 0 5px;
}

.rbc-day-slot {
  position: relative;
}
.rbc-day-slot .rbc-events-container {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  margin-right: 10px;
  top: 0;
}
.rbc-day-slot .rbc-events-container.rbc-rtl {
  left: 10px;
  right: 0;
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
  border: 1px solid #265985;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-height: 100%;
  min-height: 20px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column wrap;
          flex-flow: column wrap;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  overflow: hidden;
  position: absolute;
}
.rbc-day-slot .rbc-background-event {
  opacity: 0.75;
}
.rbc-day-slot .rbc-event-label {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  padding-right: 5px;
  width: auto;
}
.rbc-day-slot .rbc-event-content {
  width: 100%;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 0px;
          flex: 1 1 0;
  word-wrap: break-word;
  line-height: 1;
  height: 100%;
  min-height: 1em;
}
.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #f7f7f7;
}

.rbc-time-view-resources .rbc-time-gutter,
.rbc-time-view-resources .rbc-time-header-gutter {
  position: sticky;
  left: 0;
  background-color: white;
  border-right: 1px solid #ddd;
  z-index: 10;
  margin-right: -1px;
}
.rbc-time-view-resources .rbc-time-header {
  overflow: hidden;
}
.rbc-time-view-resources .rbc-time-header-content {
  min-width: auto;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
  -ms-flex-preferred-size: 0px;
      flex-basis: 0px;
}
.rbc-time-view-resources .rbc-time-header-cell-single-day {
  display: none;
}
.rbc-time-view-resources .rbc-day-slot {
  min-width: 140px;
}
.rbc-time-view-resources .rbc-header,
.rbc-time-view-resources .rbc-day-bg {
  width: 140px;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 0px;
          flex: 1 1 0;
  -ms-flex-preferred-size: 0 px;
      flex-basis: 0 px;
}

.rbc-time-header-content + .rbc-time-header-content {
  margin-left: -1px;
}

.rbc-time-slot {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0px;
          flex: 1 0 0;
}
.rbc-time-slot.rbc-now {
  font-weight: bold;
}

.rbc-day-header {
  text-align: center;
}

.rbc-slot-selection {
  z-index: 10;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 75%;
  width: 100%;
  padding: 3px;
}

.rbc-slot-selecting {
  cursor: move;
}

.rbc-time-view {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: 100%;
  border: 1px solid #ddd;
  min-height: 0;
}
.rbc-time-view .rbc-time-gutter {
  white-space: nowrap;
  text-align: right;
}
.rbc-time-view .rbc-allday-cell {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: 100%;
  height: 100%;
  position: relative;
}
.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {
  border-left: 1px solid #ddd;
}
.rbc-time-view .rbc-allday-events {
  position: relative;
  z-index: 4;
}
.rbc-time-view .rbc-row {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  min-height: 20px;
}

.rbc-time-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
.rbc-time-header.rbc-overflowing {
  border-right: 1px solid #ddd;
}
.rbc-rtl .rbc-time-header.rbc-overflowing {
  border-right-width: 0;
  border-left: 1px solid #ddd;
}
.rbc-time-header > .rbc-row:first-child {
  border-bottom: 1px solid #ddd;
}
.rbc-time-header > .rbc-row.rbc-row-resource {
  border-bottom: 1px solid #ddd;
}

.rbc-time-header-cell-single-day {
  display: none;
}

.rbc-time-header-content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-width: 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-time-header-content {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-time-header-content > .rbc-row.rbc-row-resource {
  border-bottom: 1px solid #ddd;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}

.rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 0%;
          flex: 1 0 0%;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  width: 100%;
  border-top: 2px solid #ddd;
  overflow-y: auto;
  position: relative;
}
.rbc-time-content > .rbc-time-gutter {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
}
.rbc-time-content > * + * > * {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-time-content > * + * > * {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-time-content > .rbc-day-slot {
  width: 100%;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

.rbc-current-time-indicator {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #74ad31;
  pointer-events: none;
}

.rbc-resource-grouping.rbc-time-header-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.rbc-resource-grouping .rbc-row .rbc-header {
  width: 141px;
}

/*# sourceMappingURL=react-big-calendar.css.map */
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/enhanced-calendar.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* Enhanced Calendar Styles for react-big-calendar */

.rbc-calendar-enhanced {
  height: 100%;
  font-family: inherit;
}

/* Time gutter styling */
.rbc-calendar-enhanced .rbc-time-gutter {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.rbc-calendar-enhanced .rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-calendar-enhanced .rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

/* Header styling */
.rbc-calendar-enhanced .rbc-header {
  background-color: #e6e9ec;
  border-bottom: 1px solid #d1d5db;
  padding: 8px;
  font-weight: 600;
  color: #565b61;
  text-align: center;
}

/* Event styling */
.rbc-calendar-enhanced .rbc-event {
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
  line-height: 1.2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-calendar-enhanced .rbc-event:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.rbc-calendar-enhanced .rbc-event-content {
  overflow: hidden;
}

/* Time labels */
.rbc-calendar-enhanced .rbc-time-header-gutter {
  background-color: #f8fafc;
}

.rbc-calendar-enhanced .rbc-label {
  color: #6b7280;
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
}

/* Selection styling */
.rbc-calendar-enhanced .rbc-slot-selection {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
  border-radius: 4px;
}

/* Today highlighting */
.rbc-calendar-enhanced .rbc-today {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Time slot hover effects */
.rbc-calendar-enhanced .rbc-time-slot:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Current time indicator */
.rbc-calendar-enhanced .rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
  z-index: 10;
}

/* Drag and drop styling */
.rbc-calendar-enhanced .rbc-addons-dnd-drag-preview {
  opacity: 0.7;
  transform: rotate(5deg);
}

.rbc-calendar-enhanced .rbc-addons-dnd-drop-preview {
  background-color: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  border-radius: 4px;
}

/* Room-specific event colors */
.rbc-event-room-a {
  background-color: #3b82f6 !important;
}

.rbc-event-room-b {
  background-color: #10b981 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-calendar-enhanced .rbc-time-gutter {
    width: 60px;
  }
  
  .rbc-calendar-enhanced .rbc-label {
    font-size: 10px;
    padding: 2px 4px;
  }
  
  .rbc-calendar-enhanced .rbc-event {
    font-size: 11px;
    padding: 1px 2px;
  }
}

/* Custom scrollbar for calendar */
.rbc-calendar-enhanced .rbc-time-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar {
  width: 8px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading state */
.rbc-calendar-enhanced.loading {
  opacity: 0.6;
  pointer-events: none;
}

.rbc-calendar-enhanced.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Event overlap handling */
.rbc-calendar-enhanced .rbc-event-overlaps {
  box-shadow: -2px 0 0 0 #ffffff, -4px 0 0 0 currentColor;
}

/* Accessibility improvements */
.rbc-calendar-enhanced .rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rbc-calendar-enhanced .rbc-time-slot:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background-color: rgba(59, 130, 246, 0.1);
}

/* Print styles */
@media print {
  .rbc-calendar-enhanced {
    background: white !important;
  }
  
  .rbc-calendar-enhanced .rbc-event {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/(dashboard)/home/<USER>/WeekView/WeekView.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.WeekView .rbc-time-view .rbc-time-header {
  display: block;
}
.rbc-time-view .rbc-row {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  min-height: 0px;
}
.WeekView .rbc-time-header {
  margin-left: 82px;
}
.WeekView .rbc-button-link {
  color: var(--text-daisy) !important;
  /* padding: 25px; */
  padding: 5px;
}
.WeekView .rbc-header {
  border-bottom: 1px solid var(---mantine-color-border) !important;
  height: 30px;
  padding: 0px !important;
}

.WeekView .rbc-header + .rbc-header {
  border-left: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-time-view .rbc-time-header-content {
  border-left: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-time-header.rbc-overflowing {
  border-right: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-today {
  /* background-color: #e6e9ec !important; */
}
.WeekView .rbc-today .rbc-button-link {
  color: #ffffff !important;
}
.WeekView .rbc-time-content > * + * > * {
  background-image: url("/add.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center;
} 


/* .saturday{
  pointer-events: none;
  opacity: 0.5;
}  */

.saturday-afternoon-slot>.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #5baad6 !important;
}
/* label {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

select,
input {
  padding: 5px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

input {
  width: 100px;
} */
.dayslotPropGetter
 {
  color: #74a4c3;
  z-index: 0;
}

 /* DayView.css */
 .WeekView > .rbc-time-view >  .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 0px !important; 
  /* overflow-y: auto; */
}
.rbc-event, .rbc-day-slot .rbc-background-event {
  padding: 0px  !important;
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
   border: none !important;
   
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event:focus {

  outline:none !important;
}
.rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 10px !important; 
  /* overflow-y: auto; */
}

/* WeekView */
.WeekView > .rbc-time-view >  .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
  padding-top: 20px !important;
  /* overflow-y: auto; */
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/(dashboard)/home/<USER>/MonthView/MonthView.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/* MonthView.css */
.MonthView {
  background-color: var(--content-background) !important;
}
.rbc-month-view {
  border: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-button-link {
  color: var(--text-daisy) !important;
  padding: 25px;
}
.MonthView .rbc-header {
  border-bottom: 1px solid var(--rbc-border) !important;
  padding: 25px;
}
.MonthView .rbc-header + .rbc-header {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-button-link {
  color: var(--text-daisy) !important;
  padding: 25px;
}
.rbc-month-row + .rbc-month-row {
  border-top: 1px solid var(--rbc-border) !important;
}
.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-time-content > * + * > * {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-today {
  background-color: #3799ce !important;
}
.MonthView .rbc-today .rbc-button-link {
  color: #dcebfa !important;
}
.MonthView .rbc-off-range-bg {
  background-color: #06b1bd !important;
}
.rbc-day-bg.rbc-off-range-bg {
  background-image: none;
}
.rbc-day-bg {
  background-image: url("/add.svg");
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

/* .MonthView .rbc-month-row .rbc-day-bg {
  background-image: url(/add.svg) center center / 16px 16px no-repeat;
} */

/* }
.MonthView .rbc-off-range-bg {
  background: rgb(0, 0, 0, 0.125);
  word-wrap: break-word;
  flex: 1 1;
  height: 100%;
  line-height: 1;
  min-height: 1em;
  width: 100%;
  transition:
    background-color var(--transition),
    opacity var(--transition);
  position: relative;
  transform: scaleX(-1);
  background-size: 10px 10px;
  background-image: repeating-linear-gradient(
    45deg,
    rgb(220, 226, 232) 0px,
    rgb(220, 226, 232) 1px,
    transparent 0px,
    transparent 50%
  );
  pointer-events: none;
  width: 100% !important;
  height: 100% !important;
  border-radius: 0px !important;
} */

/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/tab.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
:root {
  color-scheme: light;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;
}

[data-theme="light"] {
  color-scheme: light;

  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #d9dcde;
  --b1: 100% 0 0;
  --b2: 96.1151% 0 0;
}
[data-theme="dark"] {
  color-scheme: dark;
  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;
  --animation-btn: 0.25s;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #25292d;
  --b1: 25.3267% 0.015896 252.417568;
  --b2: 23.2607% 0.013807 253.100675;
}
@media (hover: hover) {
  .tab[disabled],
  .tab[disabled]:hover {
    cursor: not-allowed;
    color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
    --tw-text-opacity: 0.2;
  }
}
.tabs {
  display: grid;
  align-items: flex-end;
}

.tabs-lifted:has(.tab-content[class^="rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])),
.tabs-lifted:has(.tab-content[class*=" rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])) {
  border-bottom-color: transparent;
}

.tab {
  position: relative;
  grid-row-start: 1;
  display: inline-flex;
  height: 39.6px;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
  --tw-text-opacity: 0.5;
  --tab-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --tab-bg: var(--content-background);
  --tab-border-color: var(--border-color);
  color: var(--text-daisy);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
}

.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  /* border-color: transparent; */
  border-width: var(--tab-border, 0);
}

:checked + .tab-content:nth-child(2),
:is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(2) {
  border-start-start-radius: 0;
}

.tabs-lifted > .tab:focus-visible {
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}

.tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ) {
  border-color: var(--fallback-bc, oklch(var(--bc) / var(--tw-border-opacity)));
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
}

.tab:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.tab:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: -5px;
}

.tab-disabled,
.tab[disabled] {
  cursor: not-allowed;
  color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
  --tw-text-opacity: 0.2;
}

.tabs-lifted > .tab {
  border: var(--tab-border, 1px) solid transparent;
  border-width: 0 0 var(--tab-border, 1px);
  border-start-start-radius: var(--tab-radius, 0.5rem);
  border-start-end-radius: var(--tab-radius, 0.5rem);
  border-bottom-color: var(--tab-border-color);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
  padding-top: var(--tab-border, 1px);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ),
.tabs-lifted > .tab:is(input:checked) {
  background-color: var(--mantine-color-body);
  border-width: var(--tab-border, 1px) var(--tab-border, 1px) 0;
  border-inline-start-color: var(--tab-border-color);
  border-inline-end-color: var(--tab-border-color);
  border-top-color: var(--tab-border-color);
  padding-inline-start: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-inline-end: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-bottom: var(--tab-border, 1px);
  padding-top: 0;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted > .tab:is(input:checked):before {
  z-index: 1;
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + var(--tab-radius, 0.5rem) * 2);
  height: var(--tab-radius, 0.5rem);
  bottom: 0;
  background-size: var(--tab-radius, 0.5rem);
  background-position:
    top left,
    top right;
  background-repeat: no-repeat;
  --tab-grad: calc(69% - var(--tab-border, 1px));
  --radius-start: radial-gradient(
    circle at top left,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  --radius-end: radial-gradient(
    circle at top right,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  background-image: var(--radius-start), var(--radius-end);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
.tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
.tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-lifted
  > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled])
  + .tabs-lifted
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted
  > .tab:is(input:checked)
  + .tabs-lifted
  .tab:is(input:checked):before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-boxed {
  border-radius: var(--rounded-btn, 0.5rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2, oklch(var(--b2) / var(--tw-bg-opacity)));
  padding: 0.25rem;
}

.tabs-boxed .tab {
  border-radius: var(--rounded-btn, 0.5rem);
}

.tabs-boxed
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]),
.tabs-boxed :is(input:checked) {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p, oklch(var(--p) / var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc, oklch(var(--pc) / var(--tw-text-opacity)));
}

.tab-border-none {
  --tab-border: 0px;
}

.tab-border {
  --tab-border: 1px;
}

.tabs-md :where(.tab) {
  height: 2rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
}

.tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}

.tabs-sm :where(.tab) {
  height: 1.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  --tab-padding: 0.75rem;
}

.tabs-xs :where(.tab) {
  height: 1.25rem;
  font-size: 0.75rem;
  line-height: 0.75rem;
  --tab-padding: 0.5rem;
}

.-mb-\[var\(--tab-border\)\] {
  margin-bottom: calc(var(--tab-border) * -1);
}

.\[--tab-bg\: oklch\(var\(--n\)\)\] {
  --tab-bg: oklch(var(--n));
}

.\[--tab-bg\: var\(--fallback-b1\,oklch\(var\(--b1\)\)\)\] {
  --tab-bg: var(--fallback-b1, oklch(var(--b1)));
}

.\[--tab-bg\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-bg: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-bg\: yellow\] {
  --tab-bg: yellow;
}

.\[--tab-border-color\: oklch\(var\(--n\)\)\] {
  --tab-border-color: oklch(var(--n));
}

.\[--tab-border-color\: orange\] {
  --tab-border-color: orange;
}

.\[--tab-border-color\: transparent\] {
  --tab-border-color: transparent;
}

.\[--tab-border-color\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-border-color: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-color\: var\(--fallback-nc\,oklch\(var\(--nc\)\)\)\] {
  --tab-color: var(--fallback-nc, oklch(var(--nc)));
}

.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
}

.xl\:tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}
.tab-active {
  color: #3895c8;
  background-color: var(--content-background);
}
/* .border-base-300 {
  background-color: var(--mantine-color-body);
  border: var(--mantine-color-gray-3);
} */

.rounded-box {
  border-radius: var(--rounded-box, 1rem);
}
.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  border-color: transparent;
  border-width: var(--tab-border, 0);
}
.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
  border-color: var(--border-color);
}
.rounded-se-box {
  border-start-end-radius: var(--rounded-box, 1rem);
}
.rounded-b-box {
  border-bottom-right-radius: var(--rounded-box, 1rem);
  border-bottom-left-radius: var(--rounded-box, 1rem);
}
.overflow-x-auto {
  overflow-x: auto;
}
*:hover {
  scrollbar-color: color-mix(in oklch, currentColor 60%, transparent)
    transparent;
}
.preview {
  background-image: repeating-linear-gradient(
    45deg,
    var(--fallback-b1, oklch(var(--b1))),
    var(--fallback-b1, oklch(var(--b1))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 14px
  );
  background-size: 40px 40px;
}
.border-base-300 {
  --tw-border-opacity: 1;
  background-color: var(--mantine-color-body);
}
.bg-base-100 {
  --tw-bg-opacity: 1;
  background-color: var(oklch(var(--b1) / var(--tw-bg-opacity)));
}

