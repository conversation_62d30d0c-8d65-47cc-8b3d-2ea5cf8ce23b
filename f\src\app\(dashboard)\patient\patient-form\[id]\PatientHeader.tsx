'use client';
import { useState,  } from 'react';

import { Group, Title,  Text, Tooltip, Menu,Modal ,ActionIcon,Card, Table,Button,
  Textarea,
  Select,
  Radio,
  Switch,
  MultiSelect,ScrollArea, Input,Box,Stack,Checkbox, } from '@mantine/core';
import { useEffect } from 'react';
import { DatePickerInput } from '@mantine/dates';
import Icon from '@mdi/react';
import { Patient } from '@/types/typesCalendarPatient';
import Link from 'next/link';
import SimpleBar from "simplebar-react";
import { useForm } from '@mantine/form';

import patientService, { PatientAlert } from '@/services/patientService';

// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};

import {
  mdiArrowLeft,
  mdiCardAccountDetails,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
  mdiHistory,
  mdiPlus,
  mdiMicrophone,
  mdiClipboardText,mdiPlaylistCheck,
  mdiDeleteSweep,mdiChevronRight, mdiChevronDown, mdiViewHeadline, mdiArrowRight, mdiClose,
  mdiDelete, 
  // mdiContentSave, mdiCancel
} from '@mdi/js';

  interface TreeNode {
  value: string;
  children?: TreeNode[];
}



// Convert patient alerts to tree structure for Django integration
const createAlertsTreeFromDjango = (alerts: PatientAlert[]): TreeNode[] => {
  if (!alerts || alerts.length === 0) {
    return [
      {
        value: 'Alertes',
        children: [
          { value: "No alerts found for this patient" }
        ],
      },
    ];
  }

  return [
    {
      value: 'Alertes',
      children: alerts.map(alert => ({
        value: `${alert.trigger || alert.trigger_custom || 'Unknown Alert'} - ${alert.level || 'UNKNOWN'}`
      })),
    },
  ];
};
function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});

  return (
    <ul style={{ listStyle: 'none', paddingLeft: 16 }}>
      {nodes.map((node, idx) => {
        const hasChildren = node.children && node.children.length > 0;
        const isOpen = expanded[node.value];

        return (
          <li key={node.value + idx}>
            <Group gap="xs" align="center" onClick={() => hasChildren && setExpanded(prev => ({ ...prev, [node.value]: !isOpen }))} className=" Alertesslidbar">
              {hasChildren ? (
                <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
              ) : null}
              <Text
                onClick={() => !hasChildren && onSelect(node.value)}
                style={{ cursor: 'pointer' ,paddingLeft:'10px'

                }}
                
              >
                {node.value}
              </Text>
            </Group>
            {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
          </li>
        );
      })}
    </ul>
  );
}
interface TreeNodeChoixMultiple {
  uid: string;
  value: string;
  nodes?: TreeNodeChoixMultiple[];
}
function TreeItemChoixMultiple({
  node,
  collapsedNodes,
  toggleNodeCollapse,
  selectedNodes,
  toggleNodeSelection,
}: {
  node: TreeNodeChoixMultiple;
  collapsedNodes: Record<string, boolean>;
  toggleNodeCollapse: (nodeId: string) => void;
  selectedNodes: Set<string>;
  toggleNodeSelection: (nodeId: string) => void;
}) {
  const isCollapsed = collapsedNodes[node.uid] ?? false;
  const isSelected = selectedNodes.has(node.uid);

  // Calculer l'état indéterminé pour les nœuds parents
  const getIndeterminateState = () => {
    if (!node.nodes || node.nodes.length === 0) return false;

    const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
    return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
  };

  const isIndeterminate = getIndeterminateState();

  return (
    <Stack pl="md" gap="xs">
      <Group gap="xs" align="center">
        {node.nodes && node.nodes.length > 0 && (
          <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
            <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
          </span>
        )}
        <Checkbox
          label={node.value}
          checked={isSelected}
          indeterminate={isIndeterminate}
          onChange={() => toggleNodeSelection(node.uid)}
        />
      </Group>

      {!isCollapsed &&
        node.nodes?.map((child) => (
          <TreeItemChoixMultiple
            key={child.uid}
            node={child}
            collapsedNodes={collapsedNodes}
            toggleNodeCollapse={toggleNodeCollapse}
            selectedNodes={selectedNodes}
            toggleNodeSelection={toggleNodeSelection}
          />
        ))}
    </Stack>
  );
}

interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}


const PatientHeader = ({
  patient,
  onGoBack,
   onSubmit,
  fullName = 'ABDESSALMAD AGADIR',
  staffOptions = [],
  triggerOptions = [],
  onClose, onSelect
}: {
  patient: Patient;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onShowChart: () => void;
  onShowInsurances: () => void;
  onOpenMenu: () => void;
  onGoToContract: () => void;
   onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName?: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];

  onClose: () => void;
  onSelect: (value: string) => void;
}) => {

 const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
 const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
 const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
  const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);

  // Django integration state
  const [patientAlerts, setPatientAlerts] = useState<PatientAlert[]>([]);
  const [alertsLoading, setAlertsLoading] = useState(false);
  const [alertsError, setAlertsError] = useState<string | null>(null);
  // État pour gérer l'effondrement de chaque nœud
  // Initialiser avec certains nœuds effondrés par défaut
  const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>({
    '1': true, // Le nœud "Alertes" sera effondré par défaut
  });

  // État pour gérer les sélections multiples
  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());

  // États pour la gestion des modèles
  const [showModels, setShowModels] = useState(false);
  const [showAddModel, setShowAddModel] = useState(false);
  const [modelTitle, setModelTitle] = useState('');
  const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[]}>>([]);

  // États pour la reconnaissance vocale
  const [isListening, setIsListening] = useState(false);
  const [validSpeech, setValidSpeech] = useState('');
  const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
  const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
  const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut

  // Initialiser la reconnaissance vocale au montage du composant
  useEffect(() => {
    initSpeechRecognition();
  }, []);

  // Load patient alerts from Django when component mounts
  useEffect(() => {
    if (patient?.id) {
      loadPatientAlerts();
    }
  }, [patient?.id]);

  const loadPatientAlerts = async () => {
    try {
      setAlertsLoading(true);
      setAlertsError(null);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        console.warn('Django backend is not connected');
        return;
      }

      if (patient?.id) {
        // Load patient alerts from Django
        const alerts = await patientService.getPatientAlerts(patient.id);
        if (alerts) {
          setPatientAlerts(alerts);
        }
      }
    } catch (error) {
      console.error('Error loading patient alerts:', error);
      setAlertsError('Failed to load patient alerts');
    } finally {
      setAlertsLoading(false);
    }
  };

  // Fonction pour basculer l'effondrement d'un nœud
  const toggleNodeCollapse = (nodeId: string) => {
    setCollapsedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };

  // Fonction pour basculer la sélection d'un nœud
  const toggleNodeSelection = (nodeId: string) => {
    setSelectedNodes(prev => {
      const newSet = new Set(prev);

      // Trouver le nœud correspondant
      const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
        for (const node of nodes) {
          if (node.uid === id) return node;
          if (node.nodes) {
            const found = findNode(node.nodes, id);
            if (found) return found;
          }
        }
        return null;
      };

      const currentNode = findNode(exampleData, nodeId);

      if (newSet.has(nodeId)) {
        // Désélectionner le nœud et tous ses enfants
        newSet.delete(nodeId);
        if (currentNode?.nodes) {
          const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
            nodes.forEach(child => {
              newSet.delete(child.uid);
              if (child.nodes) {
                removeAllChildren(child.nodes);
              }
            });
          };
          removeAllChildren(currentNode.nodes);
        }
      } else {
        // Sélectionner le nœud et tous ses enfants
        newSet.add(nodeId);
        if (currentNode?.nodes) {
          const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
            nodes.forEach(child => {
              newSet.add(child.uid);
              if (child.nodes) {
                addAllChildren(child.nodes);
              }
            });
          };
          addAllChildren(currentNode.nodes);
        }
      }

      return newSet;
    });
  };

  // Fonction pour obtenir les sélections actuelles
  const getSelectedValues = () => {
    const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
      const result: TreeNodeChoixMultiple[] = [];
      nodes.forEach(node => {
        result.push(node);
        if (node.nodes) {
          result.push(...getAllNodes(node.nodes));
        }
      });
      return result;
    };

    const allNodes = getAllNodes(exampleData);
    return Array.from(selectedNodes)
      .map(id => allNodes.find(node => node.uid === id))
      .filter(Boolean)
      .map(node => node!.value);
  };

  // Fonctions pour la reconnaissance vocale
  const initSpeechRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionConstructor = (window as unknown as {
        webkitSpeechRecognition: new () => SpeechRecognitionInstance;
        SpeechRecognition: new () => SpeechRecognitionInstance;
      }).webkitSpeechRecognition || (window as unknown as {
        webkitSpeechRecognition: new () => SpeechRecognitionInstance;
        SpeechRecognition: new () => SpeechRecognitionInstance;
      }).SpeechRecognition;

      const newRecognition = new SpeechRecognitionConstructor();

      newRecognition.continuous = true;
      newRecognition.interimResults = true;
      newRecognition.lang = 'fr-FR';

      newRecognition.onstart = () => {
        setIsListening(true);
        setMicrophoneColor('green'); // Changer la couleur en vert
        setInvalidSpeech('Écoute en cours...');
      };

      newRecognition.onresult = (event: unknown) => {
        const speechEvent = event as {
          resultIndex: number;
          results: {
            length: number;
            [index: number]: {
              isFinal: boolean;
              [index: number]: { transcript: string };
            };
          };
        };

        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
          const transcript = speechEvent.results[i][0].transcript;
          if (speechEvent.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setValidSpeech(finalTranscript);
        setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
      };

      newRecognition.onerror = () => {
        setIsListening(false);
        setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
        setInvalidSpeech('Erreur de reconnaissance vocale');
      };

      newRecognition.onend = () => {
        setIsListening(false);
        setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
        setInvalidSpeech('Parlez maintenant.');
      };

      setRecognition(newRecognition);
    }
  };

  const toggleRecognition = () => {
    if (!recognition) {
      initSpeechRecognition();
      return;
    }

    if (isListening) {
      recognition.stop();
    } else {
      recognition.start();
    }
  };

  const emptyContent = () => {
    setValidSpeech('');
    setInvalidSpeech('Parlez maintenant.');
    setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
    if (recognition && isListening) {
      recognition.stop();
    }
  };

   const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };

    // Alerts table
    const elements = [
  { position: 6, mass: 12.011, symbol: 'C', name: 'Carbon' , symbols: 'C', names: 'Carbon'},
  { position: 7, mass: 14.007, symbol: 'N', name: 'Nitrogen' , symbols: 'C', names: 'Carbon'},
  { position: 39, mass: 88.906, symbol: 'Y', name: 'Yttrium' , symbols: 'C', names: 'Carbon'},
  { position: 56, mass: 137.33, symbol: 'Ba', name: 'Barium' , symbols: 'C', names: 'Carbon'},
  { position: 58, mass: 140.12, symbol: 'Ce', name: 'Cerium' , symbols: 'C', names: 'Carbon'},
];
const rows = elements.map((element) => (
    <Table.Tr key={element.name}>
      <Table.Td>{element.position}</Table.Td>
      <Table.Td>{element.name}</Table.Td>
      <Table.Td>{element.symbol}</Table.Td>
      <Table.Td>{element.mass}</Table.Td>
      <Table.Td>{element.symbols}</Table.Td>
      <Table.Td>{element.names}</Table.Td>
    </Table.Tr>
  ));
   const form = useForm<AlertFormValues>({
    initialValues: {
      trigger_for: [],
      trigger: '',
      level: 'MINIMUM',
      description: '',
      is_permanent: false,
    },
    validate: {
      trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
      trigger: (value) => (!value ? 'Champ requis' : null),
      description: (value) => (!value ? 'Champ requis' : null),
    },
  });
  const [search, setSearch] = useState('');

 

  const handleValidate = () => {
    let textToAdd = '';

    if (showModels) {
      // Valider les modèles sélectionnés
      const selectedModelTexts = savedModels
        .filter(model => selectedNodes.has(model.id))
        .flatMap(model => model.selections);
      textToAdd = selectedModelTexts.join(', ');
    } else {
      // Valider les sélections du dictionnaire
      const selectedValues = getSelectedValues();
      textToAdd = selectedValues.join(', ');
    }

    if (textToAdd) {
      // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
      setValidSpeech(textToAdd);
      setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"

      // 2. Ajouter le texte au champ description du formulaire
      const currentDescription = form.values.description || '';
      const newDescription = currentDescription
        ? `${currentDescription}, ${textToAdd}`
        : textToAdd;
      form.setFieldValue('description', newDescription);
    }

    // Fermer le modal et réinitialiser
    setIsClipboardTextModalOpen(false);
    setShowModels(false);
    setSelectedNodes(new Set());
  };

  const handleCancel = () => {
    // Réinitialiser tous les états
    setSelectedNodes(new Set());
    setShowModels(false);
    setShowAddModel(false);
    setModelTitle('');
    setIsClipboardTextModalOpen(false);
  };

  const handleAddModel = () => {
    if (selectedNodes.size > 0) {
      setShowAddModel(true);
    }
  };

  const handleSaveModel = () => {
    if (modelTitle.trim()) {
      const selectedValues = getSelectedValues();
      const newModel = {
        id: `model-${Date.now()}`,
        title: modelTitle.trim(),
        selections: selectedValues
      };

      setSavedModels(prev => [...prev, newModel]);
      setModelTitle('');
      setShowAddModel(false);
      setSelectedNodes(new Set());

      // Afficher les modèles après sauvegarde
      setShowModels(true);
    }
  };

  const handleDeleteModel = (modelId: string) => {
    setSavedModels(prev => prev.filter(model => model.id !== modelId));
    setSelectedNodes(prev => {
      const newSet = new Set(prev);
      newSet.delete(modelId);
      return newSet;
    });
  };

  const exampleData: TreeNodeChoixMultiple[] = [
    {
      uid: '1',
      value: 'Alertes',
      nodes: [
        { uid: '1-1', value: 'Allaitante depuis:' },
        { uid: '1-2', value: 'Allergique à l\'Aspirine' },

        { uid: '1-3', value: 'Allergique à la Pénicilline' },
        { uid: '1-4', value: 'Arthrose' },
        { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
        { uid: '1-6', value: 'Diabétique NID' },
        { uid: '1-7', value: 'Enceinte depuis:' },
        { uid: '1-8', value: 'Diabétique ID' },
        { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
        { uid: '1-10', value: 'Hypertension' },
         { uid: '1-11', value: 'Hypotension' },
        { uid: '1-12', value: 'Thyroïde' },
  

      ],
    },
    
  ];
  return (
    <Group>
    <div  className={isSidebarVisible ?  "bg-[#3799ce] text-white px-4 py-3 rounded-t-lg w-[70%]": "  w-full bg-[#3799ce] text-white px-4 py-3 rounded-t-lg"}>
    <Group justify="space-between" align="center">
      <Group>
        {patient ? (
          <Icon path={mdiCardAccountDetails} size={1} />
        ) : (
          <Button variant="subtle" onClick={onGoBack}>
            <Icon path={mdiArrowLeft} size={1} />
          </Button>
        )}
        <Title order={2}>Fiche patient</Title>
        <DatePickerInput placeholder="Date de création" />
        23/06/2025
      </Group>

      {patient && (
        <Group>
          <Text>{patient.first_name} {patient.last_name}</Text>
          <Text>{patient.gender}</Text>
          <Text>{patient.age}</Text>
          <Text>{patient.sociale}</Text>
          <Text>{patient.id}</Text>
          <Text>{patient.lastVisit?.date ? new Date(patient.lastVisit.date).toLocaleDateString() : ''}</Text>
        </Group>
      )}

      <Group>
        {/* <Tooltip label="Ajouter des biométries">
          <Button variant="default" onClick={onAddMeasurement}>
            <Icon path={mdiPlus} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Tendances">
          <Button variant="default" onClick={onShowChart}>
            <Icon path={mdiChartLine} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Assurances patient">
          <Button variant="default" onClick={onShowInsurances}>
            <Icon path={mdiPlaylistEdit} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip> */}
<Tooltip label="List" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
        <Menu shadow="md" width={220}>
          <Menu.Target>
            <Button variant="subtle">
              <Icon path={mdiApps} size={1} color={"white"}/>
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />} onClick={() => setIsAlertsModalOpen(true)}>Alerts</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiMicrophone} size={0.8} />} onClick={() => setIsMicrophoneModalOpen(true)}>Reconnaissance vocale</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiClipboardText} size={0.8} />} onClick={() => setIsClipboardTextModalOpen(true)}>Dictionnaire</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
          </Menu.Dropdown>
        </Menu>
</Tooltip>
        <Tooltip label="Dossier d'abonnement" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
          {/* <Button variant="subtle" onClick={onGoToContract}> */}
            <Button variant="light" component={Link} href={`/patients/patient-form/Abonnement/`} >
            <Icon path={mdiCertificate} size={1} color={"white"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Liste patients" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
          <Button component="a" href="/patients?tab=Complets" variant="subtle">
            <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
          </Button>
        </Tooltip>
      </Group>
    </Group>
{/* menu Alerts */}
         <Modal.Root
            opened={isAlertsModalOpen}
            onClose={() => setIsAlertsModalOpen(false)}
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            centered
            size="xl"
          > 
         <Modal.Content className="overflow-y-hidden">
          <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Group>
                <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                  <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
              </Group>
            </Modal.Title>
              <Group justify="flex-end">
                <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                onClick={() => 
                  {setIsAlertsAddModalOpen(true)
                ; toggleSidebar()}}>
         <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                        <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                      </Group>
          </Modal.Header>
            <Modal.Body style={{ padding: '0px' }}>
                    <div className="py-2 pl-4 h-[200px]">
                      <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                        <div className="pr-4">
                         <Table striped highlightOnHover withTableBorder withColumnBorders>
                           <Table.Thead>
        <Table.Tr>
          <Table.Th>Déclencheur</Table.Th>
          <Table.Th>Niveau</Table.Th>
          <Table.Th>Publique</Table.Th>
          <Table.Th>Permanente</Table.Th>
           <Table.Th>Description</Table.Th>
          <Table.Th></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>{rows}</Table.Tbody>
      <Table.Caption>Aucun élément trouvé.</Table.Caption>
                          </Table>
                        </div>
                      </SimpleBar>
                    </div>
                  </Modal.Body>
         </Modal.Content>
          </Modal.Root>
          {/* add Alerts */}
           <Modal.Root
            opened={isAlertsAddModalOpen}
            onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            centered
            size="xl"
          > 
         <Modal.Content className="overflow-y-hidden">
          <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Group>
                <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                  <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                     {`Alerte - ${fullName}`} 
                    </Text>
              </Group>
            </Modal.Title>
              <Group justify="flex-end">
                        <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                      </Group>
          </Modal.Header>
            <Modal.Body style={{ padding: '0px' }}>
                    <div className="py-2 pl-4 h-[300px]">
                      <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                        <div className="pr-4">
                       <form
                              onSubmit={form.onSubmit((values) => onSubmit(values, false))}
                              style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                            >
                              <Group>
                              <MultiSelect
                                label="Déclencher pour"
                                data={staffOptions}
                                {...form.getInputProps('trigger_for')}
                                required
                                w={"30%"}
                              />
                              <Select
                                label="Déclencheur"
                                data={triggerOptions}
                                {...form.getInputProps('trigger')}
                                required
                                 w={"30%"}
                              />
                              <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                <Group>
                                  <Radio value="MINIMUM" label="Minimum" />
                                  <Radio value="MEDIUM" label="Moyen" />
                                  <Radio value="HIGH" label="Haut" />
                                </Group>
                              </Radio.Group>
                              </Group>
                              <Group justify="space-between">
                                <Text>Description *</Text>
                                <Group>
                                 <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                              onClick={
                                ()=>setIsMicrophoneModalOpen(true)
                              }>
                              <Icon path={mdiMicrophone} size={1} />
                                      </ActionIcon>
                               <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                              onClick={
                                ()=>setIsClipboardTextModalOpen(true)
                              }>
                              <Icon path={mdiClipboardText} size={1} />
                                      </ActionIcon>
                               
                                  
                                  <Icon path={mdiDeleteSweep} size={1} />
                                </Group>
                              </Group>
                              <Textarea
                                // label="Description"
                                placeholder="Ajouter"
                                {...form.getInputProps('description')}
                                required
                              />
                            
                              <Switch
                                label="Permanente"
                                {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                              />
                      
                              <Group justify="flex-end" mt="md">
                                <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                  Annuler
                                </Button>
                                <Button
                                  onClick={() => {
                                    if (form.isValid()) {
                                      onSubmit(form.values, true); // submit with autoTrigger = true
                                    }
                                  }}
                                  disabled={!form.isValid()}
                                >
                                  Enregistrer et déclencher
                                </Button>
                                <Button type="submit" disabled={!form.isValid()}>
                                  Enregistrer
                                </Button>
                              </Group>
                            </form>
                        </div>
                      </SimpleBar>
                    </div>
                  </Modal.Body>
         </Modal.Content>
          </Modal.Root>
            {/* Choix multiple */}
           <Modal.Root
            opened={isChoixMultipleModalOpen}
            onClose={() => {setIsChoixMultipleModalOpen(false); setIsSidebarVisible(false)}}
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            centered
            size="xl"
          > 
         <Modal.Content className="overflow-y-hidden">
          <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Group>
                <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                  <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                     Choix multiple
                    </Text>
              </Group>
            </Modal.Title>
              <Group justify="flex-end">
                        <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                      </Group>
          </Modal.Header>
            <Modal.Body style={{ padding: '0px' }}>
                    <div className="py-2 pl-4 h-[300px]">
                      <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                        <div className="pr-4">

       <Stack>
        {/* Boutons de contrôle */}
        <Group justify="space-between" mb="sm">
          <Button
            size="xs"
            variant="light"
            onClick={() => {
              // Sélectionner tous les nœuds
              const getAllNodeIds = (nodes: TreeNodeChoixMultiple[]): string[] => {
                const ids: string[] = [];
                nodes.forEach(node => {
                  ids.push(node.uid);
                  if (node.nodes) {
                    ids.push(...getAllNodeIds(node.nodes));
                  }
                });
                return ids;
              };
              setSelectedNodes(new Set(getAllNodeIds(exampleData)));
            }}
          >
            Tout sélectionner
          </Button>
          <Button
            size="xs"
            variant="light"
            color="red"
            onClick={() => setSelectedNodes(new Set())}
          >
            Tout désélectionner
          </Button>
        </Group>

        {exampleData.map((node) => (
          <TreeItemChoixMultiple
            key={node.uid}
            node={node}
            collapsedNodes={collapsedNodes}
            toggleNodeCollapse={toggleNodeCollapse}
            selectedNodes={selectedNodes}
            toggleNodeSelection={toggleNodeSelection}
          />
        ))}
      </Stack>

      <Group justify="space-between" mt="md">
        <Text size="sm" c="dimmed">
          {selectedNodes.size} élément{selectedNodes.size !== 1 ? 's' : ''} sélectionné{selectedNodes.size !== 1 ? 's' : ''}
        </Text>
        <Group>
          <Button onClick={handleValidate} disabled={selectedNodes.size === 0}>
            Valider ({selectedNodes.size})
          </Button>
          <Button variant="outline" color="red" onClick={handleCancel}>
            Annuler
          </Button>
        </Group>
      </Group>
      
                        </div>
                      </SimpleBar>
                    </div>
                  </Modal.Body>
         </Modal.Content>
          </Modal.Root>
        
        
     
    </div>
       {isSidebarVisible && (
       <Card shadow="sm" padding="lg" radius="md" withBorder   className={isSidebarVisible ?  " w-[28%]": "w-full"}>
  
      <Box mb="sm">
        <Group>
        <Input
          placeholder="Rechercher"
          value={search}
          onChange={(e) => setSearch(e.currentTarget.value)}
          w={"80%"}
        />
       
         <Group justify="flex-end">
                <ActionIcon variant="filled" aria-label="Multiple" color="#3799CE" 
                onClick={()=>{setIsChoixMultipleModalOpen(true);setIsSidebarVisible(false)}}
               >
         <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                      <ActionIcon variant="filled" aria-label="Annuler" color="#3799CE"  onClick={onClose}
               >
         <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon> 
                      </Group>
                      </Group>
      </Box>

      <ScrollArea h={400}>
        {alertsLoading ? (
          <Group justify="center" p="md">
            <Text size="sm" c="dimmed">Loading patient alerts...</Text>
          </Group>
        ) : alertsError ? (
          <Group justify="center" p="md">
            <Text size="sm" c="red">{alertsError}</Text>
          </Group>
        ) : (
          <Tree
            nodes={createAlertsTreeFromDjango(patientAlerts).filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
            onSelect={(value) => {
              onSelect(value);
              onClose();
            }}
          />
        )}
      </ScrollArea>
    </Card>
    )}

      {/* Modal Microphone - Reconnaissance vocale */}
      <Modal
        opened={isMicrophoneModalOpen}
        onClose={() => setIsMicrophoneModalOpen(false)}
        title="Reconnaissance vocale"
        size="lg"
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
        centered
        withCloseButton={false}
        yOffset="30vh" xOffset={0}
        
      >
        <div style={{ padding: '20px' }}>
          {/* Interface de reconnaissance vocale */}
          <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
            <div style={{ flex: 1, marginRight: '16px' }}>
              <div style={{
                border: '1px solid #e0e0e0',
                borderRadius: '4px',
                padding: '12px',
                minHeight: '80px',
                backgroundColor: '#fafafa',
               height:'150px'
              }}>
                {/* Texte valide reconnu */}
                <span
                  style={{
                    color: '#2e7d32',
                    fontWeight: 500,
                    display: validSpeech ? 'inline' : 'none'
                  }}
                  contentEditable
                >
                  {validSpeech}
                </span>
                {/* Texte en cours de reconnaissance */}
                <span
                  style={{
                    color: '#757575',
                    fontStyle: 'italic'
                  }}
                >
                  {invalidSpeech}
                </span>
              </div>
            </div>

            {/* Boutons de contrôle */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <ActionIcon
                variant="subtle"
                color={isListening ? 'orange' : 'blue'}
                size="lg"
                onClick={toggleRecognition}
                style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
              >
                <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
              </ActionIcon>

              <ActionIcon
                variant="subtle"
                color="red"
                size="lg"
                onClick={emptyContent}
              >
                <Icon path={mdiDeleteSweep} size={1} />
              </ActionIcon>
            </div>
          </div>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button
              variant="filled"
              onClick={() => {
                // Ici vous pouvez traiter le texte reconnu
                console.log('Texte reconnu:', validSpeech);
                setIsMicrophoneModalOpen(false);
              }}
            >
              Valider
            </Button>
            <Button
              variant="outline"
              color="red"
              onClick={() => setIsMicrophoneModalOpen(false)}
            >
              Annuler
            </Button>
          </Group>
        </div>
      </Modal>

      {/* Modal ClipboardText - Modèles de dictionnaire */}
      <Modal
        opened={isClipboardTextModalOpen}
        onClose={handleCancel}
        title={
          <Group>
            <Icon path={mdiViewHeadline} size={1} />
            <Text fw={500}>Modèles de dictionnaire</Text>
            {!showModels && !showAddModel && (
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={() => setShowModels(true)}
              >
                <Icon path={mdiPlus} size={0.8} />
              </ActionIcon>
            )}
            <ActionIcon variant="subtle" color="red" onClick={handleCancel}>
              <Icon path={mdiClose} size={0.8} />
            </ActionIcon>
          </Group>
        }
        size="md"
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
        centered
        withCloseButton={false}
      >
        <div style={{ padding: '20px' }}>
          {/* Modal d'ajout de modèle */}
          {showAddModel ? (
            <Stack>
              <Text size="lg" fw={500}>Titre du modèle</Text>
              <Input
                placeholder="Titre"
                value={modelTitle}
                onChange={(e) => setModelTitle(e.currentTarget.value)}
              />
              <Group justify="flex-end" mt="md">
                <Button
                  variant="filled"
                  color="gray"
                  onClick={handleSaveModel}
                  disabled={!modelTitle.trim()}
                >
                  Enregistrer
                </Button>
                <Button
                  variant="outline"
                  color="red"
                  onClick={() => setShowAddModel(false)}
                >
                  Annuler
                </Button>
              </Group>
            </Stack>
          ) : showModels ? (
            /* Vue des modèles sauvegardés */
            <Stack>
              {savedModels.length === 0 ? (
                <Group>
                
                    {/* <Icon path={mdiAccountAlert} size={2} color="#ffa726" />
                    <Text c="dimmed">Aucune modèle à afficher</Text> */}
                      {/* Arbre de sélection */}
              <ScrollArea h={300} w={"100%"}>
                {exampleData.map((node) => (
                  <TreeItemChoixMultiple
                    key={node.uid}
                    node={node}
                    collapsedNodes={collapsedNodes}
                    toggleNodeCollapse={toggleNodeCollapse}
                    selectedNodes={selectedNodes}
                    toggleNodeSelection={toggleNodeSelection}
                  />
                ))}
              </ScrollArea>
               {/* Boutons d'action */}
              <Group justify="space-between" mt="md">
                <Group>
                  {selectedNodes.size > 0 && (
                    <Button
                      variant="filled"
                      color="blue"
                      onClick={handleAddModel}
                    >
                      Ajouter model
                    </Button>
                  )}
                  
                </Group>
                <Button variant="outline" color="red" onClick={handleCancel}>
                  Annuler
                </Button>
              </Group>
              
               
                </Group>
              ) : (
                <>
                  {savedModels.map((model) => (
                    <Group key={model.id} justify="space-between" p="sm" style={{ border: '1px solid #e0e0e0', borderRadius: '4px' }}>
                      <Group>
                        <Checkbox
                          checked={selectedNodes.has(model.id)}
                          onChange={() => toggleNodeSelection(model.id)}
                        
                        />
                        <Text>{model.title}</Text>
                      </Group>
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        onClick={() => handleDeleteModel(model.id)}
                      >
                        <Icon path={mdiDelete} size={0.8} />
                      </ActionIcon>
                    </Group>
                  ))}

                  <Group justify="flex-end" mt="md">
                    <Button onClick={handleValidate}>
                      Valider
                    </Button>
                    <Button variant="outline" color="red" onClick={handleCancel}>
                      Annuler
                    </Button>
                  </Group>
                </>
              )}

           
            </Stack>
          ) : (
            /* Vue du dictionnaire principal */
            <Stack>
              {savedModels.length === 0 ? (
                <div>
                <Group justify="center" p="sm" style={{ backgroundColor: '#fff3cd', borderRadius: '4px', border: '1px solid #ffeaa7' }}>
                  <Icon path={mdiAccountAlert} size={1} color="#f39c12" />
                  <Text c="dimmed">Aucune modèle à afficher</Text>
                  
                </Group>
                 <Group justify="flex-end" mt="md">
                <Button onClick={handleValidate}>
                  Valider
                </Button>
                <Button variant="outline" color="red" onClick={handleCancel}>
                  Annuler
                </Button>
              </Group>
                </div>
              ) : null}

            

             
            </Stack>
          )}
          
        </div>
      </Modal>

    </Group>
  );
};

export default PatientHeader;