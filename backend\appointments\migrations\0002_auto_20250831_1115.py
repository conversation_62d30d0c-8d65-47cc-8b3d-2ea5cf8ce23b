# Generated by Django 4.2.7 on 2025-08-31 10:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('patients', '0005_biometricmeasuredefinition_and_more'),
        ('appointments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientList',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Name of the patient list', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Description of the list', null=True)),
                ('list_type', models.CharField(choices=[('waiting_list', 'Waiting List'), ('department', 'Department'), ('care_team', 'Care Team'), ('custom', 'Custom')], default='custom', help_text='Type of patient list', max_length=50)),
                ('is_active', models.BooleanField(default=True, help_text='Is this list active')),
                ('is_public', models.BooleanField(default=False, help_text='Is this list publicly visible')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_staff', models.ManyToManyField(blank=True, help_text='Staff members assigned to manage this list', limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}, related_name='assigned_patient_lists', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_patient_lists', to=settings.AUTH_USER_MODEL)),
                ('patients', models.ManyToManyField(blank=True, help_text='Patients in this list', limit_choices_to={'user_type': 'patient'}, related_name='patient_lists', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Patient List',
                'verbose_name_plural': 'Patient Lists',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ActiveVisit',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('visit_status', models.CharField(choices=[('checked_in', 'Checked In'), ('in_waiting', 'In Waiting Area'), ('being_seen', 'Being Seen by Staff'), ('in_procedure', 'In Procedure/Exam'), ('checkout', 'Ready for Checkout'), ('completed', 'Visit Completed')], default='checked_in', help_text='Current status of the visit', max_length=20)),
                ('current_location', models.CharField(blank=True, help_text='Current location of the patient', max_length=100, null=True)),
                ('room', models.CharField(blank=True, help_text='Assigned room', max_length=50, null=True)),
                ('check_in_time', models.DateTimeField(auto_now_add=True, help_text='When patient checked in')),
                ('status_change_time', models.DateTimeField(auto_now=True, help_text='Last status change time')),
                ('estimated_duration', models.PositiveIntegerField(blank=True, help_text='Estimated duration in minutes', null=True)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', help_text='Visit priority', max_length=10)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the visit', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(blank=True, help_text='Associated appointment if scheduled', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='active_visits', to='appointments.appointment')),
                ('assigned_staff', models.ForeignKey(blank=True, help_text='Staff member currently responsible', limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_active_visits', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(limit_choices_to={'user_type': 'patient'}, on_delete=django.db.models.deletion.CASCADE, related_name='active_visits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Active Visit',
                'verbose_name_plural': 'Active Visits',
                'ordering': ['-check_in_time'],
            },
        ),
        migrations.CreateModel(
            name='PresenceList',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('break', 'On Break'), ('lunch', 'On Lunch'), ('meeting', 'In Meeting'), ('leave', 'On Leave'), ('unavailable', 'Unavailable')], default='present', help_text='Current presence status', max_length=20)),
                ('location', models.CharField(blank=True, help_text='Current location', max_length=100, null=True)),
                ('assigned_room', models.CharField(blank=True, help_text='Assigned room or area', max_length=50, null=True)),
                ('start_time', models.DateTimeField(auto_now_add=True, help_text='When this status started')),
                ('end_time', models.DateTimeField(blank=True, help_text='When this status ended', null=True)),
                ('is_available_for_patients', models.BooleanField(default=True, help_text='Is this staff member available for patient care')),
                ('notes', models.TextField(blank=True, help_text='Additional notes', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('staff_member', models.ForeignKey(limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}, on_delete=django.db.models.deletion.CASCADE, related_name='presence_records', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Presence List Entry',
                'verbose_name_plural': 'Presence List Entries',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='HistoryJournal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('entry_type', models.CharField(choices=[('appointment', 'Appointment'), ('visit', 'Visit'), ('procedure', 'Procedure'), ('diagnosis', 'Diagnosis'), ('medication', 'Medication'), ('test', 'Test/Lab Result'), ('note', 'Clinical Note'), ('alert', 'Alert'), ('communication', 'Communication'), ('other', 'Other')], default='note', help_text='Type of journal entry', max_length=20)),
                ('category', models.CharField(choices=[('administrative', 'Administrative'), ('clinical', 'Clinical'), ('financial', 'Financial'), ('communication', 'Communication'), ('other', 'Other')], default='clinical', help_text='Category of the entry', max_length=20)),
                ('title', models.CharField(help_text='Title of the journal entry', max_length=200)),
                ('description', models.TextField(help_text='Detailed description of the entry')),
                ('summary', models.CharField(blank=True, help_text='Brief summary of the entry', max_length=500, null=True)),
                ('is_private', models.BooleanField(default=False, help_text='Is this entry private to the creator')),
                ('is_important', models.BooleanField(default=False, help_text='Mark as important entry')),
                ('event_date', models.DateTimeField(help_text='Date and time of the event')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(blank=True, help_text='Related appointment if applicable', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='history_journal_entries', to='appointments.appointment')),
                ('created_by', models.ForeignKey(limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_journal_entries', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(limit_choices_to={'user_type': 'patient'}, on_delete=django.db.models.deletion.CASCADE, related_name='history_journal_entries', to=settings.AUTH_USER_MODEL)),
                ('related_attachment', models.ForeignKey(blank=True, help_text='Related attachment if applicable', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entries', to='patients.patientattachment')),
                ('related_document', models.ForeignKey(blank=True, help_text='Related document if applicable', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entries', to='patients.patientdocument')),
                ('related_staff', models.ManyToManyField(blank=True, help_text='Other staff members involved', limit_choices_to={'user_type__in': ['doctor', 'assistant', 'staff']}, related_name='related_journal_entries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'History Journal Entry',
                'verbose_name_plural': 'History Journal Entries',
                'ordering': ['-event_date'],
            },
        ),
        migrations.CreateModel(
            name='DoctorPause',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='Title of the pause/break', max_length=200)),
                ('date_from', models.DateTimeField(help_text='Start date and time of the pause')),
                ('date_to', models.DateTimeField(help_text='End date and time of the pause')),
                ('room', models.CharField(blank=True, choices=[('room-a', 'Room A'), ('room-b', 'Room B'), ('room-c', 'Room C'), ('other', 'Other')], help_text='Room where the pause takes place (especially for lunch breaks)', max_length=20, null=True)),
                ('resource_id', models.CharField(blank=True, help_text='Resource/Room ID for calendar integration', max_length=50, null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the pause', null=True)),
                ('is_recurring', models.BooleanField(default=False, help_text='Is this a recurring pause')),
                ('color', models.CharField(blank=True, default='#15AABF', help_text='Hex color code for calendar display', max_length=7, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_pauses', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(help_text='Doctor or assistant for this pause', limit_choices_to={'user_type__in': ['doctor', 'assistant']}, on_delete=django.db.models.deletion.CASCADE, related_name='pauses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Staff Pause',
                'verbose_name_plural': 'Staff Pauses',
                'ordering': ['date_from'],
            },
        ),
    ]