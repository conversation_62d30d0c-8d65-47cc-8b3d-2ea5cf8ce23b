'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { notifications } from '@mantine/notifications';
import {
  Paper,
  Title,
  TextInput,
  Table,
  Group,
  ActionIcon,
  Checkbox,
  Select,
  Pagination,
  Text,
  Badge,
  Menu,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Button,
  // ScrollArea ,
} from '@mantine/core';
import {
  IconSearch,
  IconPrinter,
  //IconEdit,
  IconCurrencyDollar,
  //IconFileText,
  //IconTrash,
  IconChevronUp,
  IconChevronDown,
  //IconDots,
  IconFilter,
  IconCheck,
  IconAlertCircle,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiFolderAccount,mdiReload ,mdiDotsVertical,mdiPencil,mdiReceiptTextCheckOutline,mdiTextBoxMultiple,mdiFolderPlus} from '@mdi/js';
import { patientFormService } from '@/services/patientFormService';
import patientService from '@/services/patientService';
// Interface pour les données des visites
interface VisiteData {
  id: string;
  date: string;
  nom: string;
  prenom: string;
  dateNaissance: string;
  age: string;
  cin: string;
  telephone: string;
  ville: string;
  assurance: string;
  selected?: boolean;
  // Additional backend fields
  patient_id?: string;
  appointment_id?: string;
  visit_type?: string;
  status?: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  doctor?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

// Interface for visit statistics
interface VisitStats {
  totalVisits: number;
  todayVisits: number;
  completedVisits: number;
  cancelledVisits: number;
  pendingVisits: number;
}

const Visites = () => {
  // UI State
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedVisites, setSelectedVisites] = useState<string[]>([]);

  // Backend State
  const [visites, setVisites] = useState<VisiteData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [visitStats, setVisitStats] = useState<VisitStats>({
    totalVisits: 0,
    todayVisits: 0,
    completedVisits: 0,
    cancelledVisits: 0,
    pendingVisits: 0
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [visibleColumns, setVisibleColumns] = useState({
    date: true,
    nom: true,
    prenom: true,
    dateNaissance: true,
    age: true,
    cin: true,
    telephone: true,
    ville: true,
    assurance: true,
    montantTotal: false,
    dateEntree: false,
    motif: false,
    medecinTraitant: false,
  });

  // Load visits from backend
  useEffect(() => {
    const loadVisits = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          console.log('🏥 Loading visits from backend...');

          // Load patients and their visit data
          // For now, we'll use patient data to simulate visits
          const patients = await patientService.getPatients();

          // Convert patients to visit format (simulating visits from patient data)
          const visitData: VisiteData[] = patients.map((patient, index) => ({
            id: patient.id || `visit-${index}`,
            date: patient.created_at ? new Date(patient.created_at).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR'),
            nom: patient.last_name || 'N/A',
            prenom: patient.first_name || 'N/A',
            dateNaissance: patient.date_of_birth || '',
            age: calculateAge(patient.date_of_birth || ''),
            cin: patient.national_id_number || '',
            telephone: patient.phone_number || '',
            ville: patient.address?.split(',')[1]?.trim() || 'CASABLANCA',
            assurance: patient.insurance_company || '',
            patient_id: patient.id,
            visit_type: patient.visit_type || 'consultation',
            status: 'completed',
            doctor: patient.doctor_assigned || 'Dr. Smith',
            notes: patient.additional_notes || `Visite pour ${patient.first_name} ${patient.last_name}`,
            created_at: patient.created_at,
            updated_at: patient.updated_at
          }));

          setVisites(visitData);

          // Calculate statistics
          const stats: VisitStats = {
            totalVisits: visitData.length,
            todayVisits: visitData.filter(v => isToday(v.date)).length,
            completedVisits: visitData.filter(v => v.status === 'completed').length,
            cancelledVisits: visitData.filter(v => v.status === 'cancelled').length,
            pendingVisits: visitData.filter(v => v.status === 'in_progress').length
          };

          setVisitStats(stats);
          console.log('✅ Visits loaded:', visitData.length, 'Statistics:', stats);
        } else {
          // Fallback to mock data when Django is not connected
          setVisites(getMockVisitData());
          console.warn('⚠️ Django not connected, using mock data');
        }
      } catch (error) {
        console.error('❌ Error loading visits:', error);
        setError('Failed to load visits from backend');
        setVisites(getMockVisitData());
      } finally {
        setLoading(false);
      }
    };

    loadVisits();
  }, [refreshTrigger]);

  // Helper function to calculate age
  const calculateAge = (birthDate: string): string => {
    if (!birthDate) return 'N/A';
    const birth = new Date(birthDate);
    const today = new Date();
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    const dayDiff = today.getDate() - birth.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      return `${age - 1} ans`;
    }
    return `${age} ans`;
  };

  // Helper function to check if date is today
  const isToday = (dateString: string): boolean => {
    const today = new Date().toLocaleDateString('fr-FR');
    return dateString === today;
  };

  // Mock data fallback
  const getMockVisitData = (): VisiteData[] => [
    {
      id: '1',
      date: '16/09/2022',
      nom: 'ABADI',
      prenom: 'SOUAD',
      dateNaissance: '01/01/1986',
      age: '36 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: '',
      status: 'completed'
    },
    {
      id: '2',
      date: '15/09/2022',
      nom: 'BELKASSA',
      prenom: 'MOHAMED',
      dateNaissance: '01/01/1985',
      age: '37 ans 8 mois 16 jours',
      cin: 'BKH46',
      telephone: '0463568',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
      status: 'completed'
    },
    {
      id: '3',
      date: '08/09/2022',
      nom: 'ABADI',
      prenom: 'SOUAD',
      dateNaissance: '01/01/1986',
      age: '36 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: '',
      status: 'completed'
    },
    {
      id: '4',
      date: '25/08/2022',
      nom: 'EZZAYER',
      prenom: 'AMINE',
      dateNaissance: '01/01/1989',
      age: '33 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
      status: 'completed'
    },
    {
      id: '5',
      date: '25/08/2022',
      nom: 'TEEEST',
      prenom: 'TEEEST',
      dateNaissance: '01/01/2003',
      age: '19 ans 8 mois 16 jours',
      cin: '',
      telephone: '06555889',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: '6',
      date: '24/08/2022',
      nom: 'ZZZ',
      prenom: 'ZDS',
      dateNaissance: '01/01/2007',
      age: '15 ans 8 mois 16 jours',
      cin: '',
      telephone: '066416545646',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: '7',
      date: '24/08/2022',
      nom: 'SDXS',
      prenom: 'XCXC',
      dateNaissance: '01/01/2007',
      age: '15 ans 8 mois 16 jours',
      cin: '',
      telephone: '0649',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: '8',
      date: '20/07/2022',
      nom: 'MBARGA',
      prenom: 'EMILIE',
      dateNaissance: '23/01/1986',
      age: '36 ans 7 mois 25 jours',
      cin: '',
      telephone: '0697256532',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: '9',
      date: '13/07/2022',
      nom: 'EL KANBI',
      prenom: 'HAMZA',
      dateNaissance: '25/11/2002',
      age: '19 ans 9 mois 20 jours',
      cin: 'BE52747',
      telephone: '06665666',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: '10',
      date: '12/07/2022',
      nom: 'AKONGA MBARGA',
      prenom: 'JOSEPH',
      dateNaissance: '28/03/1989',
      age: '33 ans 5 mois 20 jours',
      cin: '',
      telephone: '697894564',
      ville: 'CASABLANCA',
      assurance: 'AXA',
    },
    {
      id: '11',
      date: '12/07/2022',
      nom: 'ALIMA',
      prenom: 'FRANCE',
      dateNaissance: '13/01/2000',
      age: '22 ans 8 mois 4 jours',
      cin: '',
      telephone: '698745214',
      ville: 'CASABLANCA',
      assurance: 'AXA',
    },
    {
      id: '12',
      date: '08/07/2022',
      nom: 'BENHOUDA',
      prenom: 'ICHRAK',
      dateNaissance: '18/07/1991',
      age: '31 ans 1 mois 30 jours',
      cin: '',
      telephone: '065499248',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
  
  ];

  // Filtrer les données selon le terme de recherche
  const filteredVisites = visites.filter(visite => {
    const searchLower = searchTerm.toLowerCase();
    return (
      visite.nom.toLowerCase().includes(searchLower) ||
      visite.prenom.toLowerCase().includes(searchLower) ||
      visite.cin.toLowerCase().includes(searchLower) ||
      visite.telephone.toLowerCase().includes(searchLower) ||
      visite.ville.toLowerCase().includes(searchLower) ||
      visite.assurance.toLowerCase().includes(searchLower)
    );
  });

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Trier les données
  const sortedVisites = [...filteredVisites].sort((a, b) => {
    if (!sortField) return 0;

    const aValue = a[sortField as keyof VisiteData] || '';
    const bValue = b[sortField as keyof VisiteData] || '';

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Pagination
  const totalPages = Math.ceil(sortedVisites.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentVisites = sortedVisites.slice(startIndex, endIndex);

  // Fonction pour rendre l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <IconChevronUp size={14} /> :
      <IconChevronDown size={14} />;
  };

  // Gestion de la sélection
  const handleSelectVisite = (id: string) => {
    setSelectedVisites(prev =>
      prev.includes(id)
        ? prev.filter(visiteId => visiteId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedVisites.length === currentVisites.length) {
      setSelectedVisites([]);
    } else {
      setSelectedVisites(currentVisites.map(visite => visite.id));
    }
  };

  // Fonction pour obtenir le badge d'assurance
  const getAssuranceBadge = (assurance: string) => {
    if (!assurance) return null;

    const colors: { [key: string]: string } = {
      'CNOPS': 'blue',
      'CNSS': 'green',
      'AXA': 'orange',
      'RAMED': 'red',
    };

    return (
      <Badge
        color={colors[assurance] || 'gray'}
        variant="light"
        size="sm"
      >
        {assurance}
      </Badge>
    );
  };

  // Fonction pour basculer la visibilité des colonnes
  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev]
    }));
  };
  //  const [selected, setSelected] = useState<string[]>([]);
  
  //   const toggleSelection = (item: string) => {
  //     setSelected((current) =>
  //       current.includes(item)
  //         ? current.filter((val) => val !== item)
  //         : [...current, item]
  //     );
  //   }
// const options = [
//   'Date de viste',
//   'Nom',
//   'Prénom',
//   'Date de naissance',
//   'Age',
//   'CNIE',
//   'Téléphone',
//   'Assurance',
//   'Montant total (DHS)',
//   'Heure d\'entrée',
//   'Motif',
//   'Médecin traitant',
 
 
// ];
 const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility

const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };

  // Refresh function
  const refreshVisits = () => {
    setRefreshTrigger(prev => prev + 1);
    notifications.show({
      title: 'Refreshing',
      message: 'Visit data refreshed',
      color: 'blue',
    });
  };

    const router = useRouter();
return (
  <>
    <div className="p-6 bg-gray-50 min-h-screen w-full">
     
      <Paper className="shadow-sm">
        {/* En-tête */}
        <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
          <Group justify="space-between" align="center">
            <Group gap="xs">
              <Icon path={mdiFolderAccount} size={1} />
              <Title order={4} className="text-white font-medium">
                Visites
              </Title>
            </Group>
            <Group gap="xs">
                 <Tooltip label="Recharger les colonnes">
              <ActionIcon variant="subtle" color="white">
                <Icon path={mdiReload} size={1} />
              </ActionIcon>
              </Tooltip>
              <Tooltip label="Exporter vers Excel">
              <ActionIcon variant="subtle" color="white">
                <IconPrinter size={18} />
              </ActionIcon>
              </Tooltip>
            {/* Menu de filtres des colonnes */}
            <Menu shadow="md" width={250}>
              <Menu.Target>
                <ActionIcon variant="subtle" color="gray" size="lg">
                    <Icon path={mdiDotsVertical} size={1} color="#ffffff" />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Label>Colonnes visibles</Menu.Label>
                <Menu.Item
                  leftSection={visibleColumns.date ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('date')}
                >
                  Date de visite
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.nom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('nom')}
                >
                  Nom
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.prenom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('prenom')}
                >
                  Prénom
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.dateNaissance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('dateNaissance')}
                >
                  Date de naissance
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.age ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('age')}
                >
                  Age
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.cin ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('cin')}
                >
                  CIN
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.telephone ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('telephone')}
                >
                  Téléphone
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.ville ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('ville')}
                >
                  Ville
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.assurance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('assurance')}
                >
                  Assurance
                </Menu.Item>

                <Menu.Divider />
                <Menu.Label>Colonnes supplémentaires</Menu.Label>
                <Menu.Item
                  leftSection={visibleColumns.montantTotal ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('montantTotal')}
                >
                  Montant total (DHS)
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.dateEntree ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('dateEntree')}
                >
                  Date d&apos;entrée
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.motif ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('motif')}
                >
                  Motif
                </Menu.Item>
                <Menu.Item
                  leftSection={visibleColumns.medecinTraitant ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                  onClick={() => toggleColumnVisibility('medecinTraitant')}
                >
                  Médecin traitant
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
               {/* <Menu shadow="md" width={280} closeOnItemClick={false}>
                    <Menu.Target>
                      
                        <Icon path={mdiDotsVertical} size={1} color="#ffffff" />
                     
                    </Menu.Target>
              
                    <Menu.Dropdown>
                      <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
                        {options.map((item) => (
                          <Menu.Item
                            key={item}
                            onClick={() => toggleSelection(item)}
                            leftSection={
                              <div style={{ width: 20 }}>
                                {selected.includes(item) && (
                                  <Icon path={mdiCheck} size={0.8} />
                                )}
                              </div>
                            }
                            styles={{
                              item: {
                                display: 'flex',
                                alignItems: 'center',
                              },
                            }}
                          >
                            {item}
                          </Menu.Item>
                        ))}
                      </ScrollArea>
                    </Menu.Dropdown>
                  </Menu> */}
            </Group>
          </Group>
        </div>

        {/* Visit Statistics Dashboard */}
        {djangoStatus === 'connected' && (
          <div className="p-4 bg-blue-50 border-b">
            <Group justify="space-between" mb="sm">
              <Text fw={600} size="lg">Visit Overview</Text>
              <Group gap="xs">
                <Button
                  variant="light"
                  size="xs"
                  onClick={refreshVisits}
                  loading={loading}
                  leftSection={<Icon path={mdiReload} size={0.6} />}
                >
                  Refresh
                </Button>
                <Text size="xs" c={djangoStatus === 'connected' ? 'green' : 'red'}>
                  {djangoStatus === 'connected' ? 'Django Connected' : 'Django Disconnected'}
                </Text>
              </Group>
            </Group>

            <Group gap="md">
              {/* Total Visits */}
              <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
                <Text size="xs" c="dimmed">Total Visits</Text>
                <Text fw={600} size="lg">{visitStats.totalVisits}</Text>
              </Card>

              {/* Today's Visits */}
              <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
                <Text size="xs" c="dimmed">Today&apos;s Visits</Text>
                <Text fw={600} size="lg" c={visitStats.todayVisits > 0 ? 'green' : 'gray'}>
                  {visitStats.todayVisits}
                </Text>
              </Card>

              {/* Completed Visits */}
              <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
                <Text size="xs" c="dimmed">Completed</Text>
                <Text fw={600} size="lg" c="green">{visitStats.completedVisits}</Text>
              </Card>

              {/* Pending Visits */}
              <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
                <Text size="xs" c="dimmed">Pending</Text>
                <Text fw={600} size="lg" c="orange">{visitStats.pendingVisits}</Text>
              </Card>

              {/* Cancelled Visits */}
              {visitStats.cancelledVisits > 0 && (
                <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
                  <Text size="xs" c="dimmed">Cancelled</Text>
                  <Text fw={600} size="lg" c="red">{visitStats.cancelledVisits}</Text>
                </Card>
              )}
            </Group>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="p-4 text-center">
            <Loader size="md" />
            <Text size="sm" c="dimmed" mt="xs">Loading visits...</Text>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red" className="m-4">
            {error}
          </Alert>
        )}

        {/* Barre de recherche */}
        <div className="p-4 border-b border-gray-200">
          <Group justify="flex-start" align="center">
            <div>
              <ActionIcon variant="subtle" color="gray" size="lg" className="cursor-pointer"
              onClick={(event) => {
                event.preventDefault();
                toggleSidebar(); // Toggle sidebar visibility
              }}
              >
                  <IconFilter size={18} />
                </ActionIcon>
                </div><div  className="w-[96%] ">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="max-w-[97%] w-full"
            />
</div>
       
          </Group>
        </div>

        
        
         <div className='flex mb-10 ' >
        <div className={isSidebarVisible ?  "w-[19%] mt-0": "hidden "}>
           {isSidebarVisible && (
        <div className='w-[19%] border-2'>1111111111111</div>
        )}
        </div>
        {/* Tableau */}
        <div className={isSidebarVisible ?  "w-[80%]": "w-full "}>
         <div className="overflow-x-auto">
          <Table
            striped
            highlightOnHover
            withTableBorder={false}
            className="min-w-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="w-12">
                  <Checkbox
                    checked={selectedVisites.length === currentVisites.length && currentVisites.length > 0}
                    indeterminate={selectedVisites.length > 0 && selectedVisites.length < currentVisites.length}
                    onChange={handleSelectAll}
                  />
                </Table.Th>
                {visibleColumns.date && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('date')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de visite
                      {renderSortIcon('date')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.nom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('nom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Nom
                      {renderSortIcon('nom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.prenom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('prenom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Prénom
                      {renderSortIcon('prenom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateNaissance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateNaissance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de naissance
                      {renderSortIcon('dateNaissance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.age && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('age')}
                  >
                    <Group gap="xs" justify="space-between">
                      Age
                      {renderSortIcon('age')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.cin && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('cin')}
                  >
                    <Group gap="xs" justify="space-between">
                      CIN
                      {renderSortIcon('cin')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.telephone && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('telephone')}
                  >
                    <Group gap="xs" justify="space-between">
                      Téléphone
                      {renderSortIcon('telephone')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.ville && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ville')}
                  >
                    <Group gap="xs" justify="space-between">
                      Ville
                      {renderSortIcon('ville')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.assurance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('assurance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Assurance
                      {renderSortIcon('assurance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.montantTotal && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('montantTotal')}
                  >
                    <Group gap="xs" justify="space-between">
                      Montant total (DHS)
                      {renderSortIcon('montantTotal')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateEntree && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateEntree')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date d&apos;entrée
                      {renderSortIcon('dateEntree')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.motif && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('motif')}
                  >
                    <Group gap="xs" justify="space-between">
                      Motif
                      {renderSortIcon('motif')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.medecinTraitant && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('medecinTraitant')}
                  >
                    <Group gap="xs" justify="space-between">
                      Médecin traitant
                      {renderSortIcon('medecinTraitant')}
                    </Group>
                  </Table.Th>
                )}
                <Table.Th className="w-42">Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {currentVisites.map((visite) => (
                <Table.Tr key={visite.id} className="hover:bg-gray-50">
                  <Table.Td>
                    <Checkbox
                      checked={selectedVisites.includes(visite.id)}
                      onChange={() => handleSelectVisite(visite.id)}
                    />
                  </Table.Td>
                  {visibleColumns.date && (
                    <Table.Td className="font-medium">{visite.date}</Table.Td>
                  )}
                  {visibleColumns.nom && (
                    <Table.Td className="font-medium">{visite.nom}</Table.Td>
                  )}
                  {visibleColumns.prenom && (
                    <Table.Td>{visite.prenom}</Table.Td>
                  )}
                  {visibleColumns.dateNaissance && (
                    <Table.Td className="text-sm text-gray-600">{visite.dateNaissance}</Table.Td>
                  )}
                  {visibleColumns.age && (
                    <Table.Td className="text-sm text-gray-600">{visite.age}</Table.Td>
                  )}
                  {visibleColumns.cin && (
                    <Table.Td className="text-sm">{visite.cin || '-'}</Table.Td>
                  )}
                  {visibleColumns.telephone && (
                    <Table.Td className="text-sm">{visite.telephone || '-'}</Table.Td>
                  )}
                  {visibleColumns.ville && (
                    <Table.Td className="text-sm">{visite.ville}</Table.Td>
                  )}
                  {visibleColumns.assurance && (
                    <Table.Td>
                      {getAssuranceBadge(visite.assurance)}
                    </Table.Td>
                  )}
                  {visibleColumns.montantTotal && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.dateEntree && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.motif && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.medecinTraitant && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        variant="subtle"
                        color="#3799ce"
                        size="sm"
                        title="Modifier"
                        onClick={() => {router.push('/visites/DentalConsultation')}}
                      >
                        <Icon path={mdiPencil} size={1} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                       color="#3799ce"
                        size="sm"
                        title="Régler"
                      >
                        <IconCurrencyDollar size={16} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="#3799ce"
                        size="sm"
                        title="Facture"
                      >
                        <Icon path={mdiReceiptTextCheckOutline} size={1} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="#3799ce"
                        size="sm"
                        title="Devis"
                      >
                        <Icon path={mdiTextBoxMultiple} size={1}  />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="#3799ce"
                        size="sm"
                        title="Nouvelle mutuelle"
                      >
                        <Icon path={mdiFolderPlus} size={1}  />
                      </ActionIcon>
                      {/* <Menu shadow="md" width={200}>
                        <Menu.Target>
                          <ActionIcon variant="subtle" color="gray" size="sm">
                            <Icon path={mdiFolderPlus} size={1}  />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconFileText size={14} />}>
                            Voir détails
                          </Menu.Item>
                          <Menu.Item leftSection={<IconPrinter size={14} />}>
                            Imprimer fiche
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu> */}
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
        {/* Pagination */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <Group justify="space-between" align="center">
            <Group gap="xs">
              <Text size="sm" className="text-gray-600">
                Page
              </Text>
              <Select
                value={currentPage.toString()}
                onChange={(value) => setCurrentPage(Number(value))}
                data={Array.from({ length: totalPages }, (_, i) => ({
                  value: (i + 1).toString(),
                  label: (i + 1).toString(),
                }))}
                size="sm"
                className="w-20"
              />
              <Text size="sm" className="text-gray-600">
                Lignes par Page
              </Text>
              <Select
                value={itemsPerPage.toString()}
                onChange={(value) => {
                  setItemsPerPage(Number(value));
                  setCurrentPage(1);
                }}
                data={[
                  { value: '10', label: '10' },
                  { value: '15', label: '15' },
                  { value: '25', label: '25' },
                  { value: '50', label: '50' },
                ]}
                size="sm"
                className="w-20"
              />
              <Text size="sm" className="text-gray-600">
                1 - {Math.min(endIndex, sortedVisites.length)} de {sortedVisites.length}
              </Text>
            </Group>

            <Pagination
              value={currentPage}
              onChange={setCurrentPage}
              total={totalPages}
              size="sm"
              withEdges
            />
          </Group>
            </div>
            </div>
        </div>
      </Paper>
     
    </div>
   
    </>
  );
};

export default Visites;
