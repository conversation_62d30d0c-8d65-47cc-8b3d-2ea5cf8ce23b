'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  NumberInput,
  Textarea,
  Tabs,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconFileText,
  IconSearch,
  IconPlus,
  IconMinus,
  IconList,
  IconEye,
  IconMessageCircle,
} from '@tabler/icons-react';

const Reglement_form = () => {
  // États pour les données du règlement
  const [numeroReglement, setNumeroReglement] = useState('1');
  const [date, setDate] = useState<Date | null>(new Date('2022-09-18'));
  const [dateEcheance, setDateEcheance] = useState<Date | null>(new Date('2022-09-18'));
  const [beneficiaire, setBeneficiaire] = useState('Patient');
  const [payeur, setPayeur] = useState('Patient');
  const [nomComplet, setNomComplet] = useState('');
  const [pieceIdentite, setPieceIdentite] = useState('');
  const [mode, setMode] = useState('Espèce');
  const [banque, setBanque] = useState('Aucune');
  const [ref, setRef] = useState('');
  const [montantEncaisse, setMontantEncaisse] = useState(0);
  const [montantConsomme, setMontantConsomme] = useState(0);
  const [reliquat, setReliquat] = useState(0);
  const [commentaire, setCommentaire] = useState('');
  const [activeTab, setActiveTab] = useState('documents');

  // Données d'exemple pour les documents
  const [documentsData, setDocumentsData] = useState([
    { id: 1, numero: 'Aucun élément trouvé', document: '', date: '', montant: 0, resteARegler: 0, montantEncaisse: 0 }
  ]);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconFileText size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Règlement N°: {numeroReglement}
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Liste des règlements">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconList size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Sidebar gauche avec les informations du règlement */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* Bénéficiaire */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Bénéficiaire
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={beneficiaire}
                  onChange={setBeneficiaire}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="Patient" label="Patient" />
                    <Radio value="Tiers payant" label="Tiers payant" />
                  </Stack>
                </Radio.Group>
                <Group gap="xs" mt="xs">
                  <TextInput
                    placeholder="Choisir un patient"
                    size="xs"
                    className="flex-1"
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconSearch size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </div>

            {/* Payeur */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Payeur
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={payeur}
                  onChange={setPayeur}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="Patient" label="Patient" />
                    <Radio value="Tiers payant" label="Tiers payant" />
                    <Radio value="Autre" label="Autre" />
                  </Stack>
                </Radio.Group>
              </div>
            </div>

            {/* Nom complet */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Nom complet *
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={nomComplet}
                  onChange={(e) => setNomComplet(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Pièce d'identité */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Pièce d'identité
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={pieceIdentite}
                  onChange={(e) => setPieceIdentite(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du contenu */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Section des détails du règlement */}
          <div className="p-4 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              {/* Colonne gauche */}
              <div className="space-y-4">
                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    N° Règlement *
                  </Text>
                  <TextInput
                    value={numeroReglement}
                    onChange={(e) => setNumeroReglement(e.target.value)}
                    size="sm"
                  />
                </div>
                
                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Date *
                  </Text>
                  <DatePickerInput
                    value={date}
                    onChange={setDate}
                    size="sm"
                    placeholder="Sélectionner une date"
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Mode
                  </Text>
                  <Select
                    value={mode}
                    onChange={(value) => setMode(value || 'Espèce')}
                    data={['Espèce', 'Chèque', 'Carte bancaire', 'Virement']}
                    size="sm"
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Montant encaissé *
                  </Text>
                  <NumberInput
                    value={montantEncaisse}
                    onChange={(value) => setMontantEncaisse(Number(value) || 0)}
                    size="sm"
                    min={0}
                    step={0.01}
                    decimalScale={2}
                  />
                </div>
              </div>

              {/* Colonne droite */}
              <div className="space-y-4">
                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Date d'échéance
                  </Text>
                  <DatePickerInput
                    value={dateEcheance}
                    onChange={setDateEcheance}
                    size="sm"
                    placeholder="Sélectionner une date"
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Banque
                  </Text>
                  <Select
                    value={banque}
                    onChange={(value) => setBanque(value || 'Aucune')}
                    data={['Aucune', 'BNP Paribas', 'Crédit Agricole', 'Société Générale']}
                    size="sm"
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Réf.
                  </Text>
                  <TextInput
                    value={ref}
                    onChange={(e) => setRef(e.target.value)}
                    size="sm"
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Montant consommé
                  </Text>
                  <NumberInput
                    value={montantConsomme}
                    onChange={(value) => setMontantConsomme(Number(value) || 0)}
                    size="sm"
                    min={0}
                    step={0.01}
                    decimalScale={2}
                  />
                </div>

                <div>
                  <Text size="sm" fw={500} className="text-gray-700 mb-1">
                    Reliquat
                  </Text>
                  <NumberInput
                    value={reliquat}
                    onChange={(value) => setReliquat(Number(value) || 0)}
                    size="sm"
                    min={0}
                    step={0.01}
                    decimalScale={2}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Section commentaire */}
          <div className="p-4 border-b border-gray-200">
            <div>
              <Text size="sm" fw={500} className="text-gray-700 mb-1">
                Commentaire
              </Text>
              <Textarea
                value={commentaire}
                onChange={(e) => setCommentaire(e.target.value)}
                size="sm"
                rows={2}
                className="w-full"
              />
            </div>
          </div>

          {/* Section onglets */}
          <div className="flex-1 flex flex-col">
            <Tabs
              value={activeTab}
              onChange={(value) => setActiveTab(value || 'documents')}
              className="flex-1 flex flex-col"
            >
              <Tabs.List className="bg-gray-100 border-b border-gray-200">
                <Tabs.Tab
                  value="documents"
                  className="text-sm font-medium"
                >
                  Documents à régler
                </Tabs.Tab>
                <Tabs.Tab
                  value="lignes"
                  className="text-sm font-medium"
                >
                  Lignes règlement
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="documents" className="flex-1 flex flex-col">
                <div className="flex-1 overflow-auto">
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                    className="h-full"
                  >
                    <Table.Thead className="bg-gray-50 sticky top-0">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-32">
                          N°. Document
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Document
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Date
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Montant
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-32">
                          Reste à régler
                        </Table.Th>
                        <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-32">
                          Montant encaissé
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {documentsData.map((document, index) => (
                        <Table.Tr key={index} className="hover:bg-gray-50">
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {document.numero}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {document.document}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-center">
                            {document.date}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-right">
                            {document.montant.toFixed(2)}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-right">
                            {document.resteARegler.toFixed(2)}
                          </Table.Td>
                          <Table.Td className="text-sm text-right">
                            {document.montantEncaisse.toFixed(2)}
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Section pagination et total */}
                <div className="border-t border-gray-300 bg-gray-50 p-3">
                  <Group justify="space-between" align="center">
                    <Group gap="sm" align="center">
                      <Text size="sm" className="text-gray-600">Page</Text>
                      <Select
                        value={currentPage.toString()}
                        onChange={(value) => setCurrentPage(Number(value) || 1)}
                        data={['1', '2', '3', '4', '5']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                      <Select
                        value={itemsPerPage.toString()}
                        onChange={(value) => setItemsPerPage(Number(value) || 5)}
                        data={['5', '10', '20', '50']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                      <Text size="sm" className="text-gray-600">K</Text>
                      <Group gap="xs">
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          className="text-gray-500"
                        >
                          <IconMinus size={14} />
                        </ActionIcon>
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          className="text-gray-500"
                        >
                          <IconPlus size={14} />
                        </ActionIcon>
                      </Group>
                    </Group>

                    <Text size="sm" fw={600} className="text-gray-800">
                      Reliquat : {reliquat.toFixed(2)}
                    </Text>
                  </Group>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="lignes" className="flex-1 flex flex-col">
                <div className="flex-1 flex items-center justify-center">
                  <Text size="sm" className="text-gray-500">
                    Contenu des lignes règlement
                  </Text>
                </div>
              </Tabs.Panel>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-gray-50 border-t border-gray-200"
      >
        <Group justify="flex-end" gap="sm">
          <Button
            variant="outline"
            color="red"
            size="sm"
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="blue"
            size="sm"
          >
            Valider l'en-tête
          </Button>
          <Button
            variant="outline"
            color="blue"
            size="sm"
          >
            Enregistrer et quitter
          </Button>
          <Button
            color="blue"
            size="sm"
          >
            Enregistrer
          </Button>
        </Group>
      </Card>
    </Box>
  );
};

export default Reglement_form;
