'use client';
import { useDisclosure } from '@mantine/hooks';
import React, { useState,  } from 'react';
import Icon from '@mdi/react';
import {mdiFileMultiple,mdiReceiptText,mdiWindowClose,mdiFileDocument} from '@mdi/js';
// import { Search, Calendar, X, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import {
  Group,
  Text,
  ActionIcon,
  Table,
  Badge,
  Box,
  Loader,
  Tooltip,Card,Modal,Divider,Button,
  Alert,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconRefresh } from '@tabler/icons-react';

// Import billing hook
import { useBilling } from '@/hooks/useBilling';
interface PatientData {
  docNumber: string;
  color: string;
   id: string;
  createdAt: Date;
  patient: {
    name: string;
    fullName: string;
  };
  insurance?: string;
  source: {
    type: 'visit' |'VisitDentaire' |'appointment' | 'consultation';
    label: string;
    color: string;
  };
  documentNumber?: string;
  status: {
    type: 'not_billed' | 'partially_billed' | 'fully_billed';
    label: string;
    color: string;
    icon: string;
  };
}

type IntervalType = 'today' | 'this_month' | null;
type DateType = 'start' | 'end';
interface BillingFlowItem {
  id: string;
  createdAt: Date;
  patient: {
    name: string;
    fullName: string;
  };
  insurance?: string;
  source: {
    type: 'visit' |'VisitDentaire' |'appointment' | 'consultation';
    label: string;
    color: string;
    
  };
  documentNumber?: string;
  status: {
    type: 'not_billed' | 'partially_billed' | 'fully_billed';
    label: string;
    color: string;
    icon: string;
  };
}

interface BillingFlowQuery {
  searchAll: string;
  startDate: Date | null;
  endDate: Date | null;
  page: number;
  limit: number;
}
interface BillingFlowProps {
  loading?: boolean;
  items?: BillingFlowItem[];
  total?: number;
  onQueryChange?: (query: BillingFlowQuery) => void;
  onInvoice?: (item: BillingFlowItem) => void;
}
 const FluxDeFaturation: React.FC<BillingFlowProps> = ({
  loading = false,
  // items = [],
  // total = 0,
  // onQueryChange,
   onInvoice
}) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [activeInterval, setActiveInterval] = useState<IntervalType>('this_month');

  // Use billing hook for backend integration
  const {
    invoices,
    loading: billingLoading,
    error: billingError,
    refreshAll,
    createInvoice,
  } = useBilling({
    autoFetch: true,
    dataTypes: ['invoices']
  });
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(20);
  const [isLoading, ] = useState<boolean>(false);
  const [opened, { open, close }] = useDisclosure(false);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refreshAll();
      notifications.show({
        title: 'Actualisation',
        message: 'Données actualisées avec succès',
        color: 'blue'
      });
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de l\'actualisation',
        color: 'red'
      });
    }
  };

  // Convert invoices to billing flow items
  const patientDataFromInvoices: BillingFlowItem[] = invoices.map(invoice => ({
    id: invoice.id,
    createdAt: new Date(invoice.date),
    patient: {
      name: invoice.patientName,
      fullName: invoice.patientName
    },
    insurance: 'CNSS', // Default insurance
    source: {
      type: 'consultation' as const,
      label: 'Consultation',
      color: 'blue'
    },
    documentNumber: invoice.number,
    status: {
      type: invoice.status === 'paid' ? 'fully_billed' :
            invoice.status === 'sent' ? 'partially_billed' : 'not_billed',
      label: invoice.status === 'paid' ? 'Facturé' :
             invoice.status === 'sent' ? 'Partiellement facturé' : 'Non facturé',
      color: invoice.status === 'paid' ? 'green' :
             invoice.status === 'sent' ? 'orange' : 'red',
      icon: 'mdiReceiptText'
    }
  }));

   // Use data from backend or fallback to mock data
   const patientData: BillingFlowItem[] = patientDataFromInvoices.length > 0 ? patientDataFromInvoices : [
     {
       id: '1',
       createdAt: new Date('2025-07-02T09:30:00'),
       patient: {
         name: 'KTKTKTKTKT ACHRA',
         fullName: 'KTKTKTKTKT ACHRA'
       },
       insurance: '',
       source: {
         type: 'visit',
         label: 'Visit',
         color: '#d717ec'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
      {
       id: '2',
       createdAt: new Date('11-07-2025 19:21'),
       patient: {
         name: 'HAZAZ ',
         fullName: 'HAZAZ AMINE'
       },
       insurance: 'Cnss',
       source: {
         type: 'VisitDentaire',
         label: 'Visit Dentaire',
         color: '#5fcddc'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
     {
       id: '3',
       createdAt: new Date('11-07-2025 19:21'),
       patient: {
         name: 'HAZAZ ',
         fullName: 'HAZAZ AMINE'
       },
       insurance: 'Cnss',
       source: {
         type: 'VisitDentaire',
         label: 'Visit Dentaire',
         color: '#5fcddc'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
     {
       id: '4',
       createdAt: new Date('11-07-2025 19:21'),
       patient: {
         name: 'HAZAZ ',
         fullName: 'HAZAZ AMINE'
       },
       insurance: 'Cnss',
       source: {
         type: 'VisitDentaire',
         label: 'Visit Dentaire',
         color: '#5fcddc'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
     {
       id: '5',
       createdAt: new Date('11-07-2025 19:21'),
       patient: {
         name: 'HAZAZ ',
         fullName: 'HAZAZ AMINE'
       },
       insurance: 'Cnss',
       source: {
         type: 'VisitDentaire',
         label: 'Visit Dentaire',
         color: '#5fcddc'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
     {
       id: '6',
       createdAt: new Date('11-07-2025 19:21'),
       patient: {
         name: 'HAZAZ ',
         fullName: 'HAZAZ AMINE'
       },
       insurance: 'Cnss',
       source: {
         type: 'VisitDentaire',
         label: 'Visit Dentaire',
         color: '#5fcddc'
       },
       documentNumber: '',
       status: {
         type: 'not_billed',
         label: 'Non Facturé ou Partiellement facturé',
         color: 'red',
         icon: mdiFileDocument
       }
     },
   ];

 const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  const totalItems = patientData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const handleSearch = (value: string): void => {
    setSearchQuery(value);
    // Logique de recherche
  };

  const handleDateChange = (type: DateType, date: string): void => {
    if (type === 'start') {
      setStartDate(date);
    } else {
      setEndDate(date);
    }
    // Logique de filtrage par date
  };

  const setInterval = (interval: IntervalType): void => {
    setActiveInterval(interval);
    // Logique de filtrage par intervalle
  };

  const clearInterval = (): void => {
    setActiveInterval(null);
    setStartDate('');
    setEndDate('');
  };
  const handleInvoice = async (item: BillingFlowItem): Promise<void> => {
    console.log('Facturer:', item);

    try {
      // Create invoice from billing flow item
      const invoiceData = {
        number: `INV-${Date.now()}`,
        date: new Date().toISOString().split('T')[0],
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        patientId: item.id,
        patientName: item.patient.fullName,
        doctorId: '1', // Default doctor ID
        doctorName: 'Dr. Default',
        items: [
          {
            id: '1',
            description: 'Consultation médicale',
            quantity: 1,
            unitPrice: 100,
            total: 100
          }
        ],
        subtotal: 100,
        tax: 20,
        total: 120,
        status: 'draft' as const,
        notes: `Facture générée pour ${item.patient.fullName}`
      };

      await createInvoice(invoiceData);

      notifications.show({
        title: 'Succès',
        message: 'Facture créée avec succès',
        color: 'green'
      });

      // Refresh data
      await refreshAll();
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la création de la facture',
        color: 'red'
      });
    }

    onInvoice?.(item);
    open();
  };

  const handlePageChange = (page: number): void => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <>
     <Card shadow="0" padding="lg" radius="md" withBorder mb={22} bg={'#3799CE'}>
              <Group justify="space-between">
                <Group >
                  <Icon path={mdiFileMultiple} size={1} color={'white'}/>
                  <Text fw={500} c={"white"}>Flux de Facturation</Text>
                  {billingLoading && <Loader size="sm" color="white" />}
                </Group>
                <ActionIcon
                  variant="subtle"
                  color="white"
                  size="lg"
                  onClick={handleRefresh}
                  loading={billingLoading}
                >
                  <IconRefresh size={18} />
                </ActionIcon>
          </Group>
           </Card>

           {/* Error Alert */}
           {billingError && (
             <Alert
               icon={<IconAlertCircle size={16} />}
               title="Erreur"
               color="red"
               variant="light"
               className="mb-4"
             >
               {billingError}
             </Alert>
           )}
    
    <div className="flex min-h-screen bg-gray-50">
     
      {/* Sidebar */}
      {/* <div className="w-64 bg-white shadow-lg hidden md:block">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-800">Filtres</h2>
        </div>
        <div className="p-4">
          {/* Contenu du sidebar pour les filtres personnalisés */}
          {/* <p className="text-sm text-gray-600">Filtres personnalisés à venir</p>
        </div>
      </div>  */}

      {/* Contenu principal */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white shadow-sm border-b p-4">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Recherche */}
            <div className="flex items-center bg-gray-100 rounded-lg px-3 py-2 flex-1 max-w-md">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-gray-500 mr-2 lucide lucide-search-icon lucide-search"><path d="m21 21-4.34-4.34"/><circle cx="11" cy="11" r="8"/></svg>
              {/* <Search className="h-5 w-5 text-gray-500 mr-2" /> */}
              <input
                type="text"
                placeholder="Rechercher"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="bg-transparent flex-1 outline-none text-gray-700"
              />
            </div>

            {/* Filtres de date et boutons */}
            <div className="flex flex-wrap items-center gap-3">
              {/* Date de début */}
              <div className="relative">
                <input
                  type="date"
                  placeholder="Date début"
                  value={startDate}
                  onChange={(e) => handleDateChange('start', e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500lucide lucide-calendar-days-icon lucide-calendar-days"><path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2"/><path d="M3 10h18"/><path d="M8 14h.01"/><path d="M12 14h.01"/><path d="M16 14h.01"/><path d="M8 18h.01"/><path d="M12 18h.01"/><path d="M16 18h.01"/></svg>
                {/* <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" /> */}
              </div>

              {/* Date de fin */}
              <div className="relative">
                <input
                  type="date"
                  placeholder="Date Fin"
                  value={endDate}
                  onChange={(e) => handleDateChange('end', e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500lucide lucide-calendar-days-icon lucide-calendar-days"><path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2"/><path d="M3 10h18"/><path d="M8 14h.01"/><path d="M12 14h.01"/><path d="M16 14h.01"/><path d="M8 18h.01"/><path d="M12 18h.01"/><path d="M16 18h.01"/></svg>
                {/* <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" /> */}
              </div>

              {/* Boutons d'intervalle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setInterval('today')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeInterval === 'today'
                      ? 'bg-[#3799CE] text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Aujourd&apos;hui
                </button>
                <button
                  onClick={() => setInterval('this_month')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeInterval === 'this_month'
                      ? 'bg-[#3799CE] text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Ce mois
                </button>
              </div>

              {/* Bouton clear */}
              {activeInterval && (
                <button
                  onClick={clearInterval}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                  {/* <X className="h-5 w-5" /> */}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="flex-1 bg-white m-4 rounded-lg shadow-sm overflow-hidden">
          {/* Barre de progression */}
          {isLoading && (
            <div className="h-1 bg-blue-600 animate-pulse"></div>
          )}

          <div className="overflow-x-auto">
              {/* Tableau */}
                  <Box style={{ flex: 1, overflow: 'auto' }} >
                    {loading ? (
                      <Box p="xl" style={{ textAlign: 'center' }}>
                        <Loader size="lg" />
                      </Box>
                    ) : (
                      <Table striped highlightOnHover withTableBorder withColumnBorders>
                        <Table.Thead className='border-3 border-b-[#3799CE] border-[#DEE2E6]'>
                          <Table.Tr>
                            <Table.Th style={{ minWidth: 140 }} w={140} >
                              <Text size="lg" fw={700} ta="left" pl="8">Date de création</Text>
                            </Table.Th>
                            <Table.Th style={{ minWidth: 200 }}>
                              <Text size="lg"  fw={700} ta="left" pl="8">Patient</Text>
                            </Table.Th>
                            <Table.Th style={{ minWidth: 140 }} >
                              <Text size="lg"  fw={700} ta="left" pl="8">Assurance</Text>
                            </Table.Th>
                            <Table.Th style={{ minWidth: 100, }}w={100}>
                              <Text size="lg"  fw={700} ta="left" pl="8">Source</Text>
                            </Table.Th>
                            <Table.Th style={{ minWidth: 120,}}>
                              <Text size="lg"  fw={700} ta="left" pl="8">N°. Document</Text>
                            </Table.Th>
                            <Table.Th style={{ minWidth: 100}}>
                              <Text size="lg"  fw={700} ta="left" pl="8">Status</Text>
                            </Table.Th>
                            <Table.Th style={{ width: 80 }}>
                              {/* Actions */}
                            </Table.Th>
                          </Table.Tr>
                        </Table.Thead>
            
                        <Table.Tbody>
                          {patientData.map((item) => (
                            <Table.Tr key={item.id}>
                              <Table.Td>
                                <Text size="sm">{formatDate(item.createdAt)}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Text size="sm" fw={500}> {item.patient.fullName}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Text size="sm">{item.insurance || ''}</Text>
                              </Table.Td>
                              <Table.Td >
                                <Badge
                                  color={item.source.color}
                                  variant="filled"
                                  size="sm"
                                  w={90}
                                  mr={8}
                                >
                                   {item.source.label}
                                </Badge>
                              </Table.Td>
                              <Table.Td style={{ textAlign: 'center' }}>
                                <Text size="sm">{item.documentNumber || ''}</Text>
                              </Table.Td>
                              <Table.Td style={{ textAlign: 'center' }}>
                                <Tooltip label={item.status.label} withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                                  <ActionIcon
                                    variant="subtle"
                                    color={item.status.color}
                                    size="sm"
                                  >
                                    <Icon path={item.status.icon} size={0.8} />
                                  </ActionIcon>
                                </Tooltip>
                              </Table.Td>
                               <Table.Td>
                                <Tooltip label="Document de facturation" withArrow  style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                                  <ActionIcon
                                    variant="subtle"
                                    color="blue"
                                    onClick={() => handleInvoice(item)}
                                  >
                                    <Icon path={mdiReceiptText} size={0.8} />
                                  </ActionIcon>
                                </Tooltip>
                              </Table.Td>
                            </Table.Tr>
                           
                          ))}
                        </Table.Tbody>
                      </Table>
                    )}
                    
                     {/* Pagination */}
                  </Box>
           
          </div>

          {/* Pagination */}
          <div className="bg-white px-6 py-4 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              {/* Sélecteur de page */}
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-700">Page</label>
                <select
                  value={currentPage}
                  onChange={(e) => handlePageChange(parseInt(e.target.value))}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {Array.from({ length: totalPages }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {i + 1}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sélecteur d'éléments par page */}
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-700">Lignes par Page</label>
                <select
                  value={itemsPerPage}
                  onChange={(e) => setItemsPerPage(parseInt(e.target.value))}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>5</option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>

              {/* Info et boutons de navigation */}
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-700">
                  1 - {Math.min(itemsPerPage, totalItems)} de {totalItems}
                </span>
                
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className="p-1 rounded-md text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 lucide lucide-chevrons-left-icon lucide-chevrons-left"><path d="m11 17-5-5 5-5"/><path d="m18 17-5-5 5-5"/></svg>
                    {/* <ChevronsLeft className="h-5 w-5" /> */}
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="p-1 rounded-md text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 lucide lucide-chevron-left-icon lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
                    {/* <ChevronLeft className="h-5 w-5" /> */}
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="p-1 rounded-md text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 lucide lucide-chevron-right-icon lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
                    {/* <ChevronRight className="h-5 w-5" /> */}
                  </button>
                  <button
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="p-1 rounded-md text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 lucide lucide-chevrons-right-icon lucide-chevrons-right"><path d="m6 17 5-5-5-5"/><path d="m13 17 5-5-5-5"/></svg>
                    {/* <ChevronsRight className="h-5 w-5" /> */}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
       <Modal opened={opened} onClose={close} withCloseButton={false}  yOffset="30vh" xOffset={0} size={'lg'}>
             <Divider my="md" />
            Les visites effectuées aux dates suivantes sont déjà facturées
           ou contiennent des actes dentaires (à facturer à partir de la visite dentaire):;
     <Divider my="md" />
      <Group justify="flex-end">
    <Button variant="subtle" component='a' href='/medical-report/billing/contract?tab=Mes_facuers'>Confirmer</Button>
      <ActionIcon variant="subtle" aria-label="Close" onClick={close} >
          <Icon path={mdiWindowClose} size={1} color={'orange'}/>
        </ActionIcon>
    
    </Group>
          </Modal>
    </>
  );
};

export default FluxDeFaturation;