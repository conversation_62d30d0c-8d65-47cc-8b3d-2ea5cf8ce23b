import type { Metada<PERSON> } from "next";
import { getLocale } from '~/i18n/server';
import { ColorSchemeScript, MantineProvider, mantineHtmlProps } from '@mantine/core';
import NextTopLoader from "nextjs-toploader";
import { ModalsProvider } from "@mantine/modals";
import { LocaleProvider } from '~/hooks/LocaleProvider';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Notifications } from '@mantine/notifications';
import { AuthProvider } from '~/hooks/useAuth';
import "@mantine/core/styles.css";
import '@mantine/notifications/styles.css';
import ScrollToTop from "~/components/scrollToTop/ScrollToTop";
import "simplebar-react/dist/simplebar.min.css";
import theme from "~/styles/theme";
import "~/styles/globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function Layout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
return (
    // <html lang="en" data-mantine-color-scheme="light">
       <html lang={locale} {...mantineHtmlProps}>
         <head>
       <ColorSchemeScript defaultColorScheme="auto" />
      </head>
      <body  className={`${geistSans.variable} ${geistMono.variable} antialiased`} cz-shortcut-listen="true">
      <NextTopLoader
          color="#06B1BD"
          initialPosition={0.08}
          crawlSpeed={200}
          height={5}
          crawl={true}
          showSpinner={false}
          easing="ease"
          speed={200}
          shadow="0 0 10px #2299DD,0 0 5px #2299DD"
          zIndex={1600}
          showAtBottom={false}
        />
          <LocaleProvider value={locale}>
        <MantineProvider theme={theme} defaultColorScheme="auto">
        <Notifications position="bottom-right" zIndex={1000} />
        <ModalsProvider>
          <AuthProvider>
            {children}
            <ScrollToTop />
          </AuthProvider>
          </ModalsProvider>
        </MantineProvider>
        </LocaleProvider>
      </body>
    </html>
  );
}
