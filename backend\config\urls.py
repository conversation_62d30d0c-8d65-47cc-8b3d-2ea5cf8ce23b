"""
URL configuration for patient management system.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.routers import DefaultRouter


from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
# API Router
router = DefaultRouter()

# Import viewsets
# Temporarily import directly from models to avoid view import issues
from users.models import User
from appointments.views import AppointmentViewSet, DentistryAppointmentViewSet
from billing.views import InvoiceViewSet, PaymentViewSet, SubscriptionViewSet, LicenseViewSet

# Register viewsets
# Temporarily commented out user viewsets due to import issues
# router.register(r'users', UserViewSet)
# router.register(r'patients', PatientViewSet, basename='patient')
router.register(r'appointments', AppointmentViewSet)
router.register(r'dentistry-appointments', DentistryAppointmentViewSet)
router.register(r'invoices', InvoiceViewSet)
router.register(r'payments', PaymentViewSet)
router.register(r'subscriptions', SubscriptionViewSet)
router.register(r'licenses', LicenseViewSet)
# API documentation setup

schema_view = get_schema_view(
    openapi.Info(
        title="Medical Appointment API",
        default_version='v1',
        description="API for medical appointment system",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)
urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # API Routes
    path('api/', include(router.urls)),
    path('api/auth/', include('users.urls')),
    path('api/users/', include('users.urls')),  # Include users URLs for patient bridge
    path('api/patients/', include('patients.urls')),
    path('api/appointments/', include('appointments.urls')),
    path('api/billing/', include('billing.urls')),

    path('api/subscriptions/', include('subscriptions.urls')),
    path('api/pharmacy/', include('pharmacy.urls')),  # Include pharmacy APIs

      # API documentation
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Admin site customization
admin.site.site_header = 'Patient Management System'
admin.site.site_title = 'Patient Management'
admin.site.index_title = 'Administration'
