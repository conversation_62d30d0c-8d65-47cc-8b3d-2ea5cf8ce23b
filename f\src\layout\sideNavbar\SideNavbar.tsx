import SimpleBar from "simplebar-react";
import { Card, Text, Button, Group , UnstyledButton,Divider} from "@mantine/core";
import { IconX } from '@tabler/icons-react';
import { Box } from "@mantine/core";

import {
  IconCalendarStats,
  IconGauge,
  IconPresentationAnalytics,
  IconAdjustments,
  IconUsersGroup,
  IconStethoscope,
  IconUser,
  IconStar,
  IconNews,
  IconWallet,
  IconFileText,
  IconChartBar,
  IconReportMedical,
  IconCash,
  IconReceiptDollar,
  IconPill,
  IconPackage,
  IconPrescription,
  IconHistory,
  IconServer,
  IconDatabase,
IconUsers ,
IconSettings,
} from "@tabler/icons-react";
//import { FaClipboardCheck } from "react-icons/fa";
import { LinksGroup } from "./NavbarLinksGroup/NavbarLinksGroup";
import classes from "./LeftMenu.module.css";

const mockdata = [
 { key: "dashboard", label: "dashboard", icon: IconGauge, link: "/dashboard" },
  
  {
    key: "appointments",
    label: "Appointments",
    icon: IconCalendarStats,
    links: [
      { label: "Add Appointments", icon: IconNews, link: "/dashboard/appointments/add" },
      { label: "View Appointments", icon: IconNews, link: "/dashboard/appointments" },
      { label: "Calendar", icon: IconNews, link: "/dashboard/appointments/calendar" },
    ],
  },

 {
    key: "patients",
    label: "Patients",
    icon: IconUsersGroup,
    links: [
      { label: "Liste des patients", icon: IconNews, link: "/dashboard/patients" },
      { label: "Etats du patient", icon: IconNews, link: "/dashboard/patients/status" },
      { label: "Ajouter des patients", icon: IconNews, link: "/dashboard/patients/add" },
      { label: "Patients/Visits assurés", icon: IconNews, link: "/dashboard/patients/insured" },
    ],
  },
  {
    key: "care-sheet",
    label: "Dossier de Soins",
    icon: IconFileText,
    links: [
      { label: "Mutuelles", icon: IconNews, link: "/dashboard/care-sheet" },
      { label: "Visites", icon: IconNews, link: "/dashboard/care-sheet/visits" },
      { label: "Procédures", icon: IconNews, link: "/dashboard/care-sheet/procedures" },
      { label: "Devis", icon: IconNews, link: "/dashboard/care-sheet/quotes" },
    ],
  },
  {
    key: "generic-states",
    label: "États & Rapports",
    icon: IconChartBar,
    links: [
      { label: "Activité journalière", icon: IconNews, link: "/dashboard/generic-states" },
      { label: "Rapports financiers", icon: IconNews, link: "/dashboard/generic-states/financial" },
      { label: "Analytique patients", icon: IconNews, link: "/dashboard/generic-states/patients" },
      { label: "Rapports médicaux", icon: IconNews, link: "/dashboard/generic-states/medical" },
    ],
  },
  {
    key: "medical-report",
    label: "Rapports Médicaux",
    icon: IconReportMedical,
    links: [
      { label: "Comptes rendus", icon: IconNews, link: "/dashboard/medical-report/list" },
      { label: "Facturation", icon: IconNews, link: "/dashboard/medical-report/billing/contract" },
      { label: "Devis médicaux", icon: IconNews, link: "/dashboard/medical-report/billing/quotes" },
      { label: "Contrats", icon: IconNews, link: "/dashboard/medical-report/billing/contracts" },
    ],
  },
  {
    key: "payment",
    label: "Gestion des Paiements",
    icon: IconCash,
    links: [
      { label: "Encaissements", icon: IconReceiptDollar, link: "/dashboard/payment" },
      { label: "État des comptes", icon: IconNews, link: "/dashboard/payment/accounts" },
      { label: "Modes de paiement", icon: IconNews, link: "/dashboard/payment/methods" },
      { label: "Analytiques", icon: IconNews, link: "/dashboard/payment/analytics" },
    ],
  },
  {
    key: "pharmacy",
    label: "Gestion Pharmacie",
    icon: IconPill,
    links: [
      { label: "Médicaments", icon: IconPill, link: "/dashboard/pharmacy" },
      { label: "Inventaire", icon: IconPackage, link: "/dashboard/pharmacy/inventory" },
      { label: "Achats", icon: IconNews, link: "/dashboard/pharmacy/Achat" },
      { label: "Ventes", icon: IconNews, link: "/dashboard/pharmacy/Vent" },
      { label: "Fournisseurs", icon: IconNews, link: "/dashboard/pharmacy/suppliers" },
    ],
  },
  {
    key: "prescriptions",
    label: "Gestion des Prescriptions",
    icon: IconPrescription,
    links: [
      { label: "Prescriptions", icon: IconPrescription, link: "/dashboard/prescriptions" },
      { label: "Historique médicamenteux", icon: IconHistory, link: "/dashboard/prescriptions/history" },
      { label: "Templates", icon: IconNews, link: "/dashboard/prescriptions/templates" },
      { label: "Renouvellements", icon: IconNews, link: "/dashboard/prescriptions/refills" },
      { label: "Interactions", icon: IconNews, link: "/dashboard/prescriptions/interactions" },
    ],
  },
  {
    key: "parameters",
    label: "Configuration Système",
    icon: IconSettings,

    links: [
      { label: "Paramètres de base", icon: IconSettings, link: "/dashboard/parameters" },
      { label: "Gestion des utilisateurs", icon: IconUsers, link: "/dashboard/parameters/gestion_des_acteurs" },
      { label: "Configuration application", icon: IconNews, link: "/dashboard/parameters/configuration" },
      { label: "Maintenance données", icon: IconDatabase, link: "/dashboard/parameters/maintenance" },
      { label: "État du système", icon: IconServer, link: "/dashboard/parameters/system" },
    ],
  },
  { key: "doctors", label: "Doctors", icon: IconStethoscope },
  { key: "analytics", label: "Analytics", icon: IconPresentationAnalytics },
  { key: "reviews", label: "Reviews", icon: IconStar },
  { key: "finances", label: "Finances", icon: IconWallet },
  { key: "customers", label: "Customers", icon: IconUser },

  { key: "recette", label: "Votre recette", icon: IconNews },

  {
    key: "mutuelles",
    label: "Mutuelles",
    icon: IconAdjustments,
    link: "/mutuelles",
    links: [
      { label: "Liste des Mutuelles", icon: IconNews, link: "/mutuelles?tab=mutuelles" },
      { label: "Nouvelle Mutuelle", icon: IconNews, link: "/mutuelles?tab=nouvelle-mutuelle" },
      { label: "Validation", icon: IconNews, link: "/mutuelles?tab=validation" },
      { label: "Rapports", icon: IconNews, link: "/mutuelles?tab=rapports" },
    ],
  },

  {
    key: "parameters generaux",
    label: "Parameters generaux",
    icon: IconAdjustments,
    link: "/settings",
    links: [
         // En-tête Parameters de base
      { label: "Parameters de base", isHeader: true },
      { label: "Modeles dimpression", icon: IconNews, link: "/settings" },
      { label: "Impression des mutuelles", icon: IconNews, link: "/settings" },
      { label: "Accueil et aganda", icon: IconNews, link: "/settings" },
      { label: "Gestion des listes", icon: IconNews, link: "/settings" },
      { label: "Gestion des emplacements", icon: IconNews, link: "/settings" },
     
     // En-tête Gestion des acteurs
      { label: "Gestion des acteurs", isHeader: true },
      { label: "List des specialtes", icon: IconNews, link: "/settings" },
      
      { label: "Gestion des profiels", icon: IconNews, link: "/settings" },
      { label: "Listes des utilisateurs", icon: IconNews, link: "/settings" },
      { label: "Listes des contracts", icon: IconNews, link: "/settings" },
      { label: "Liste des techniciens", icon: IconNews, link: "/settings" },
       // En-tête patient
      { label: "Patient", isHeader: true },
      { label: "Module patient", icon: IconNews, link: "/settings" },
      { label: "Patient en cours", icon: IconNews, link: "/settings" },
      { label: "Questionnaires patient", icon: IconNews, link: "/settings" },
    
        // En-tête Configration de l'application
      { label: "Configration de l'application", isHeader: true },
      { label: "Dictionnaire", icon: IconNews, link: "/settings" },
      { label: "Liste des choix", icon: IconNews, link: "/settings" },
      { label: "Catalogues de procedures", icon: IconNews, link: "/settings" },
     { label: "Procedures", icon: IconNews, link: "/settings" },
      
      { label: "List des conventions", icon: IconNews, link: "/profile" },
       { label: "Procedures", icon: IconNews, link: "/settings" },
      { label: "Organismes", icon: IconNews, link: "/profile" },
      { label: "Bloc de saisie", icon: IconNews, link: "/settings#security" },
      { label: "Block du score", icon: IconNews, link: "/settings#notifications" },
      { label: "Modeles des consultation", icon: IconNews, link: "/settings#privacy" },
      { label: "Module observation", icon: IconNews, link: "/settings#accessibility" },
      { label: "Biometrie", icon: IconNews, link: "/logout" },
      { label: "Comptes rendus", icon: IconNews, link: "/logout" },
      { label: "Biologie", icon: IconNews, link: "/logout" },

      { label: "Pathologies et symptomes", icon: IconNews, link: "/logout" },
      { label: "Medicaments & Para", icon: IconNews, link: "/settings" },
      { label: "Familles des certificats", icon: IconNews, link: "/profile" },
      { label: "E-mail,SMS,Consentement", icon: IconNews, link: "/settings#security" },
      { label: "Formulaires", icon: IconNews, link: "/settings#notifications" },
      // En-tête Parametrage du module des specialites
      { label: "Parametrage du module des specialites", isHeader: true },
      { label: "dentaire", icon: IconNews, link: "/settings" },
      { label: "Calendrier de vaccination", icon: IconNews, link: "/settings" },
      { label: "Calendrier de grossesse", icon: IconNews, link: "/settings" },
{ label: "Parmetrage d'hopitalisation", icon: IconNews, link: "/settings" },
{ label: "oxymetrie", icon: IconNews, link: "/settings" },
{ label: "Plan de soins", icon: IconNews, link: "/settings" },
  // En-tête facturation Stock
      { label: "Facturation Stock", isHeader: true },
      { label: "Numeros de sequence", icon: IconNews, link: "/settings" },
  // En-tête Maintenance des donnees
      { label: "Maintenance des donnees", isHeader: true },
      { label: "Backups de donnees", icon: IconNews, link: "/settings" },
{ label: "fusion des patients", icon: IconNews, link: "/settings" },
// En-tête Configration des platformes cloud
      { label: "Configration des platformes cloud", isHeader: true },
      { label: "Configration des platformes cloud", icon: IconNews, link: "/settings" },


      { label: "General Settings", icon: IconNews, link: "/settings?tab=general" },
      { label: "Profile Settings", icon: IconNews, link: "/profile?tab=profile" },
      { label: "Security", icon: IconNews, link: "/settings?tab=security" },
      { label: "Notifications", icon: IconNews, link: "/settings?tab=notifications" },
      { label: "Privacy", icon: IconNews, link: "/settings?tab=privacy" },
      { label: "Accessibility", icon: IconNews, link: "/settings?tab=accessibility" },
      { label: "Logout", icon: IconNews, link: "/settings?tab=logout" },
    ],
  },
  
  
   {
    key: "Pharmacie",
    label: "Pharmacie",
    icon: IconNews,
    link: "/pharmacy",
    links: [
      // En-tête Inventaires
      { label: "Inventaires", isHeader: true },
      { label: "List des Inventaires", icon: IconNews, link: "/pharmacy?tab=inventaire&subtab=liste-inventaires" },
      { label: "Mouvements du stock", icon: IconNews, link: "/pharmacy?tab=inventaire&subtab=mouvements-stock" },
      { label: "Mouvements series/Lots", icon: IconNews, link: "/pharmacy?tab=inventaire&subtab=mouvements-series" },

      // En-tête Pharmacie
      { label: "Pharmacie", isHeader: true },
      { label: "Liste des familles", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=liste-familles" },
      { label: "Liste des articles", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=liste-articles" },
      { label: "Liste des fournisseurs", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=liste-fournisseurs" },
      { label: "Tarification", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=tarification" },
      { label: "Liste des affaires", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=liste-affaires" },
      { label: "Mouvements du stock", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=mouvements-stock" },
      { label: "Transformations", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=transformations" },
      { label: "Echange inter-depots", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=echange-inter-depots" },
      { label: "Liste des depots", icon: IconNews, link: "/pharmacy?tab=pharmacie&subtab=liste-depots" },

      // En-tête Achat
      { label: "Achat", isHeader: true },
      { label: "Demandes d'achats", icon: IconNews, link: "/pharmacy?tab=achat&subtab=demandes-achats" },
      { label: "Demandes de prix", icon: IconNews, link: "/pharmacy?tab=achat&subtab=demandes-prix" },
      { label: "Bons de commande", icon: IconNews, link: "/pharmacy?tab=achat&subtab=bons-commande" },
      { label: "Bons de reception", icon: IconNews, link: "/pharmacy?tab=achat&subtab=bons-reception" },
      { label: "Bons de deposition", icon: IconNews, link: "/pharmacy?tab=achat&subtab=bons-deposition" },
      { label: "Bons de retour", icon: IconNews, link: "/pharmacy?tab=achat&subtab=bons-retour" },
      { label: "Facturation", icon: IconNews, link: "/pharmacy?tab=achat&subtab=facturation" },
      { label: "Liste des a-nouveaux", icon: IconNews, link: "/pharmacy?tab=achat&subtab=liste-nouveaux" },
      { label: "Liste des reglements", icon: IconNews, link: "/pharmacy?tab=achat&subtab=liste-reglements" },

      // En-tête Vente
      { label: "Vente", isHeader: true },
      { label: "Bons de livraison", icon: IconNews, link: "/pharmacy?tab=vente&subtab=bons-livraison" },
      { label: "Bons de consignation", icon: IconNews, link: "/pharmacy?tab=vente&subtab=bons-consignation" },
      { label: "Retours de consignation", icon: IconNews, link: "/pharmacy?tab=vente&subtab=retours-consignation" },

      // En-tête Maintenance
      { label: "Maintenance", isHeader: true },
      { label: "Recalcule de stock", icon: IconNews, link: "/pharmacy?tab=maintenance&subtab=recalcule-stock" },
    ],
  },
];
interface SideNavbarProps {
    toggleSidebar: () => void;
  }
export function SideNavbar({ toggleSidebar }: SideNavbarProps) {
   const handleToggle = () => {
      console.log('Toggle sidebar button clicked in SideNavbar');
      toggleSidebar();
    };
  const links = mockdata.map((item) => (
    <LinksGroup {...item} key={item.key} />
  ));
  return (
      <Box>
           <Group justify="space-between">
      <div className=" h-[50px] w-[190px] pl-0 pt-4 bg-[#f5f5f5]">logo logo 22logo logo 22</div>

      <UnstyledButton
                 onClick={handleToggle}
                 className={classes.link}
                 style={{ cursor: 'pointer',width:"30px" ,justifyContent: "flex-end"}}
               >
                  <IconX size={20} stroke={1.5} className={classes.chevron} />
               </UnstyledButton>
    </Group>
       
                <Divider />
     <nav className={`${classes.navbar}`}>
        <Box   h={550}  >
     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] " >
        <div className={`${classes.linksInner} `}>
          {links}</div>
       </SimpleBar>
</Box>
      <div className={classes.footer}>
        <Box m="sm">
          <Card
            shadow="sm"
            padding="lg"
            radius="md"
            withBorder
            bg="var(--premium-bg)"
          >
            <Group justify="space-between" mt="md" mb="xs">
              <Text fw={500}>Need Premium?</Text>
            </Group>

            <Text size="sm" c="dimmed">
              Access all features with single time purchase 99
            </Text>

            <Button color="blue" fullWidth mt="md" radius="md">
              Purchase
            </Button>
          </Card>
        </Box>
      </div>
    </nav>
    </Box>
  );
}