import React, { useState } from 'react';
import Icon from '@mdi/react';
import { mdiMagnify } from '@mdi/js';
import {
  Button,
  Card,
  Table,
  Text,
  ActionIcon,
  Tooltip,
  Modal,
  TextInput,
  Group,
  Tabs,
  Select,
  ColorPicker,
  Stack,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconEye,
  IconFileText,
  IconArrowLeft,
  IconList,
} from '@tabler/icons-react';

// Types pour les données des blocs de saisie
interface BlocDeSaisie {
  id: number;
  dateModification: string;
  titre: string;
  nombreChamps: number;
}



interface ChampFormulaire {
  id: number;
  nom: string;
  type: string;
  obligatoire: boolean;
  couleur: string;
  listeChamp?: string;
}

const BlocDeSaisie = () => {
  // États pour la gestion des données
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBloc, setEditingBloc] = useState<BlocDeSaisie | null>(null);

  // États pour le modal plein écran
  const [activeTab, setActiveTab] = useState<string | null>('general');
  const [blocTitre, setBlocTitre] = useState('');
  const [champs, setChamps] = useState<ChampFormulaire[]>([]);

  // États pour le modal de couleur
  const [isColorModalOpen, setIsColorModalOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#3b82f6');
  const [editingColorIndex, setEditingColorIndex] = useState<number | null>(null);
  const [colorTab, setColorTab] = useState<string | null>('hex');

  // États pour le modal de liste des champs
  const [isListModalOpen, setIsListModalOpen] = useState(false);
  const [viewingBloc, setViewingBloc] = useState<BlocDeSaisie | null>(null);

  // Données d'exemple des blocs de saisie
  const [blocs, setBlocs] = useState<BlocDeSaisie[]>([
    {
      id: 1,
      dateModification: '27/10/2023 12:45',
      titre: 'Fiche Médicale',
      nombreChamps: 13,
    },
    {
      id: 2,
      dateModification: '27/10/2023 12:45',
      titre: 'Diagnostic',
      nombreChamps: 3,
    },
    {
      id: 3,
      dateModification: '27/10/2023 12:45',
      titre: 'Anamnèse',
      nombreChamps: 3,
    },
  ]);

  // Fonction pour ouvrir le modal
  const openModal = (bloc?: BlocDeSaisie) => {
    if (bloc) {
      setEditingBloc(bloc);
      setBlocTitre(bloc.titre);
      // Charger les champs existants du bloc selon le titre
      if (bloc.titre === 'Fiche Médicale') {
        setChamps([
          { id: 1, nom: 'Facteurs de risque', type: 'Dictionnaire libre', obligatoire: false, couleur: '#3b82f6', listeChamp: 'Facteurs de risque' },
          { id: 2, nom: 'Allergies médicamenteuses', type: 'Liste des choix', obligatoire: false, couleur: '#ef4444', listeChamp: 'Allergies médicamenteuses' },
          { id: 3, nom: 'Antécédents', type: 'Éditeur text', obligatoire: false, couleur: '#10b981', listeChamp: 'Antécédents' },
          { id: 4, nom: 'Motifs de consultation', type: 'Dictionnaire', obligatoire: false, couleur: '#f59e0b', listeChamp: 'Motifs de consultation' },
          { id: 5, nom: 'Examen radiologique', type: 'Éditeur text', obligatoire: false, couleur: '#8b5cf6', listeChamp: 'Examen radiologique' },
          { id: 6, nom: 'Conclusions d\'examen radiologique', type: 'Éditeur text', obligatoire: false, couleur: '#ec4899', listeChamp: 'Conclusions d\'examen radiologique' },
          { id: 7, nom: 'Symptomatologie', type: 'Dictionnaire libre', obligatoire: false, couleur: '#06b6d4', listeChamp: 'Symptomatologie' },
          { id: 8, nom: 'Alertes', type: 'Liste des choix', obligatoire: false, couleur: '#dc2626', listeChamp: 'Alertes' },
          { id: 9, nom: 'Notifications', type: 'Liste des choix', obligatoire: false, couleur: '#84cc16', listeChamp: 'Notifications' },
          { id: 10, nom: 'conseils', type: 'Éditeur text', obligatoire: false, couleur: '#6b7280', listeChamp: 'conseils' },
        ]);
      } else {
        setChamps([]);
      }
    } else {
      setEditingBloc(null);
      setBlocTitre('');
      setChamps([]);
    }
    setIsModalOpen(true);
  };

  // Fonction pour fermer le modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingBloc(null);
    setBlocTitre('');
    setChamps([]);
    setActiveTab('general');
  };

  // Fonctions pour le modal de couleur
  const openColorModal = (index: number) => {
    setEditingColorIndex(index);
    setSelectedColor(champs[index].couleur);
    setIsColorModalOpen(true);
  };

  const closeColorModal = () => {
    setIsColorModalOpen(false);
    setEditingColorIndex(null);
    setSelectedColor('#3b82f6');
  };

  const handleColorSave = () => {
    if (editingColorIndex !== null) {
      const newChamps = [...champs];
      newChamps[editingColorIndex].couleur = selectedColor;
      setChamps(newChamps);
    }
    closeColorModal();
  };

  // Fonctions pour le modal de liste des champs
  const openListModal = (bloc: BlocDeSaisie) => {
    setViewingBloc(bloc);
    setIsListModalOpen(true);
  };

  const closeListModal = () => {
    setIsListModalOpen(false);
    setViewingBloc(null);
  };

  // Fonction pour obtenir les champs d'un bloc
  const getBlocChamps = (bloc: BlocDeSaisie): ChampFormulaire[] => {
    if (bloc.titre === 'Fiche Médicale') {
      return [
        { id: 1, nom: 'Facteurs de risque', type: 'Dictionnaire libre', obligatoire: false, couleur: '#3b82f6', listeChamp: 'Facteurs de risque' },
        { id: 2, nom: 'Allergies médicamenteuses', type: 'Liste des choix', obligatoire: false, couleur: '#ef4444', listeChamp: 'Allergies médicamenteuses' },
        { id: 3, nom: 'Antécédents', type: 'Éditeur text', obligatoire: false, couleur: '#10b981', listeChamp: 'Antécédents' },
        { id: 4, nom: 'Motifs de consultation', type: 'Dictionnaire', obligatoire: false, couleur: '#f59e0b', listeChamp: 'Motifs de consultation' },
        { id: 5, nom: 'Examen radiologique', type: 'Éditeur text', obligatoire: false, couleur: '#8b5cf6', listeChamp: 'Examen radiologique' },
        { id: 6, nom: 'Conclusions d\'examen radiologique', type: 'Éditeur text', obligatoire: false, couleur: '#ec4899', listeChamp: 'Conclusions d\'examen radiologique' },
        { id: 7, nom: 'Symptomatologie', type: 'Dictionnaire libre', obligatoire: false, couleur: '#06b6d4', listeChamp: 'Symptomatologie' },
        { id: 8, nom: 'Alertes', type: 'Liste des choix', obligatoire: false, couleur: '#dc2626', listeChamp: 'Alertes' },
        { id: 9, nom: 'Notifications', type: 'Liste des choix', obligatoire: false, couleur: '#84cc16', listeChamp: 'Notifications' },
        { id: 10, nom: 'conseils', type: 'Éditeur text', obligatoire: false, couleur: '#6b7280', listeChamp: 'conseils' },
      ];
    }
    // Pour les autres blocs, retourner "Traitement en cours"
    return [
      { id: 1, nom: 'Traitement en cours', type: 'Autocomplétion', obligatoire: false, couleur: '#3b82f6', listeChamp: 'Liste des médicaments' },
    ];
  };

  // Fonction de sauvegarde
  const handleSave = () => {
    if (blocTitre.trim()) {
      if (editingBloc) {
        // Modification
        setBlocs(blocs.map(bloc =>
          bloc.id === editingBloc.id
            ? {
                ...bloc,
                titre: blocTitre,
                nombreChamps: champs.length,
                dateModification: new Date().toLocaleString('fr-FR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })
              }
            : bloc
        ));
      } else {
        // Ajout
        const newBloc: BlocDeSaisie = {
          id: Math.max(...blocs.map(b => b.id)) + 1,
          titre: blocTitre,
          nombreChamps: champs.length,
          dateModification: new Date().toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        };
        setBlocs([...blocs, newBloc]);
      }
      closeModal();
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <IconFileText size={24} className="text-blue-600" />
            </div>
            <div>
              <Text size="xl" fw={600} className="text-gray-900">
                Bloc de saisie
              </Text>
            </div>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => openModal()}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="sm"
          >
            Nouveau bloc
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white">
          {/* Tableau */}
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Date de modification</Table.Th>
                <Table.Th>Titre</Table.Th>
                <Table.Th>Nombre de champs</Table.Th>
                <Table.Th style={{ width: 100 }}>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {blocs.map((bloc) => (
                <Table.Tr key={bloc.id}>
                  <Table.Td>{bloc.dateModification}</Table.Td>
                  <Table.Td>{bloc.titre}</Table.Td>
                  <Table.Td>{bloc.nombreChamps}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => openModal(bloc)}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Liste des champs">
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          onClick={() => openListModal(bloc)}
                        >
                          <Icon path={mdiMagnify} size={0.8} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>
      </div>

      {/* Modal plein écran pour créer/modifier un bloc */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title=""
        size="100%"
        padding={0}
        styles={{
          content: {
            height: '100vh',
          },
          body: {
            height: '100%',
            padding: 0,
          },
        }}
      >
        <div className="h-full flex flex-col bg-gray-50">
          {/* En-tête du modal */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  onClick={closeModal}
                  size="lg"
                >
                  <IconArrowLeft size={20} />
                </ActionIcon>
                <div className="bg-blue-100 p-2 rounded-lg">
                  <IconFileText size={24} className="text-blue-600" />
                </div>
                <div>
                  <Text size="xl" fw={600} className="text-gray-900">
                    Bloc de saisie
                  </Text>
                </div>
              </div>
              <Button
                onClick={handleSave}
                disabled={!blocTitre.trim()}
                className="bg-blue-500 hover:bg-blue-600 text-white"
                size="sm"
              >
                Enregistrer
              </Button>
            </div>
          </div>

          {/* Contenu principal */}
          <div className="flex-1 p-6">
            <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
              {/* Onglets */}
              <Tabs value={activeTab} onChange={setActiveTab} className="h-full">
                <Tabs.List className="mb-6">
                  <Tabs.Tab
                    value="general"
                    leftSection={<IconList size={16} />}
                    className="text-blue-600"
                  >
                    Général
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="general" className="h-full">
                  <div className="space-y-6">
                    {/* Champ Titre */}
                    <TextInput
                      label="Titre du bloc"
                      placeholder="Entrez le titre du bloc de saisie"
                      value={blocTitre}
                      onChange={(e) => setBlocTitre(e.target.value)}
                      required
                      size="md"
                      className="max-w-md"
                    />

                    {/* Tableau des champs */}
                    <div>
                      <Table striped highlightOnHover className="border border-gray-200">
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>Titre</Table.Th>
                            <Table.Th>Couleur du titre</Table.Th>
                            <Table.Th>Type</Table.Th>
                            <Table.Th>Liste des champs</Table.Th>
                            <Table.Th style={{ width: 120 }}>Actions</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {champs.map((champ, index) => (
                            <Table.Tr key={champ.id}>
                              <Table.Td>
                                <TextInput
                                  value={champ.nom}
                                  onChange={(e) => {
                                    const newChamps = [...champs];
                                    newChamps[index].nom = e.target.value;
                                    setChamps(newChamps);
                                  }}
                                  size="sm"
                                  variant="unstyled"
                                />
                              </Table.Td>
                              <Table.Td>
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-4 h-4 rounded border"
                                    style={{ backgroundColor: champ.couleur }}
                                  />
                                  <Text size="sm">Couleur</Text>
                                  <ActionIcon
                                    variant="subtle"
                                    size="sm"
                                    onClick={() => openColorModal(index)}
                                  >
                                    <IconEdit size={12} />
                                  </ActionIcon>
                                </div>
                              </Table.Td>
                              <Table.Td>
                                <Select
                                  value={champ.type}
                                  onChange={(value) => {
                                    const newChamps = [...champs];
                                    newChamps[index].type = value || 'Dictionnaire';
                                    setChamps(newChamps);
                                  }}
                                  data={[
                                    { value: 'Dictionnaire', label: 'Dictionnaire' },
                                    { value: 'Dictionnaire libre', label: 'Dictionnaire libre' },
                                    { value: 'Autocomplétion', label: 'Autocomplétion' },
                                    { value: 'Liste des choix', label: 'Liste des choix' },
                                    { value: 'Éditeur text', label: 'Éditeur text' },
                                    { value: 'Date', label: 'Date' },
                                    { value: 'Pièces-jointes', label: 'Pièces-jointes' },
                                  ]}
                                  size="sm"
                                  variant="unstyled"
                                />
                              </Table.Td>
                              <Table.Td>
                                <Select
                                  value={champ.listeChamp || ''}
                                  onChange={(value) => {
                                    const newChamps = [...champs];
                                    newChamps[index].listeChamp = value || '';
                                    setChamps(newChamps);
                                  }}
                                  data={[
                                    { value: 'Facteurs de risque', label: 'Facteurs de risque' },
                                    { value: 'Allergies médicamenteuses', label: 'Allergies médicamenteuses' },
                                    { value: 'Antécédents', label: 'Antécédents' },
                                    { value: 'Motifs de consultation', label: 'Motifs de consultation' },
                                    { value: 'Examen radiologique', label: 'Examen radiologique' },
                                    { value: 'Conclusions d\'examen radiologique', label: 'Conclusions d\'examen radiologique' },
                                    { value: 'Symptomatologie', label: 'Symptomatologie' },
                                    { value: 'Alertes', label: 'Alertes' },
                                    { value: 'Notifications', label: 'Notifications' },
                                    { value: 'conseils', label: 'conseils' },
                                  ]}
                                  size="sm"
                                  variant="unstyled"
                                  placeholder="Sélectionner un champ"
                                />
                              </Table.Td>
                              <Table.Td>
                                <Group gap="xs">
                                  <ActionIcon
                                    variant="subtle"
                                    color="blue"
                                    size="sm"
                                  >
                                    <IconEye size={14} />
                                  </ActionIcon>
                                  <ActionIcon
                                    variant="subtle"
                                    color="red"
                                    size="sm"
                                    onClick={() => {
                                      setChamps(champs.filter((_, i) => i !== index));
                                    }}
                                  >
                                    <IconEdit size={14} />
                                  </ActionIcon>
                                   <Tooltip label="Liste des champs">
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          onClick={() => editingBloc && openListModal(editingBloc)}
                        >
                          <Icon path={mdiMagnify} size={0.8} />
                        </ActionIcon>
                      </Tooltip>
                                </Group>
                              </Table.Td>
                            </Table.Tr>
                          ))}
                        </Table.Tbody>
                      </Table>

                      {/* Boutons en bas */}
                      <div className="flex justify-end gap-3 mt-6">
                        <Button
                          leftSection={<IconPlus size={16} />}
                          variant="outline"
                          className="border-blue-500 text-blue-500 hover:bg-blue-50"
                          onClick={() => {
                            const newChamp: ChampFormulaire = {
                              id: Date.now(),
                              nom: `Nouveau champ`,
                              type: 'Dictionnaire',
                              obligatoire: false,
                              couleur: '#3b82f6',
                              listeChamp: '',
                            };
                            setChamps([...champs, newChamp]);
                          }}
                        >
                          Ajouter un nouveau champ
                        </Button>
                        <Button
                          className="bg-blue-500 hover:bg-blue-600 text-white"
                          onClick={handleSave}
                        >
                          Enregistrer
                        </Button>
                      </div>
                    </div>
                  </div>
                </Tabs.Panel>
              </Tabs>
            </Card>
          </div>
        </div>
      </Modal>

      {/* Modal de sélection de couleur */}
      <Modal
        opened={isColorModalOpen}
        onClose={closeColorModal}
        title="Sélection de couleur"
        size="md"
        centered
      >
        <Stack gap="md">
          {/* Onglets pour les formats de couleur */}
          <Tabs value={colorTab} onChange={setColorTab}>
            <Tabs.List>
              <Tabs.Tab value="hex">Hex</Tabs.Tab>
              <Tabs.Tab value="rgb">RGB</Tabs.Tab>
              <Tabs.Tab value="hsl">HSL</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="hex" pt="md">
              <Stack gap="md">
                <ColorPicker
                  value={selectedColor}
                  onChange={setSelectedColor}
                  format="hex"
                  size="lg"
                  swatches={[
                    '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
                    '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16',
                    '#6b7280', '#1f2937', '#dc2626', '#059669'
                  ]}
                />
                <TextInput
                  label="Valeur Hex"
                  value={selectedColor}
                  onChange={(e) => setSelectedColor(e.target.value)}
                  placeholder="#3b82f6"
                />
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="rgb" pt="md">
              <Stack gap="md">
                <ColorPicker
                  value={selectedColor}
                  onChange={setSelectedColor}
                  format="rgb"
                  size="lg"
                />
                <Text size="sm" className="text-gray-600">
                  Format RGB : {selectedColor}
                </Text>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="hsl" pt="md">
              <Stack gap="md">
                <ColorPicker
                  value={selectedColor}
                  onChange={setSelectedColor}
                  format="hsl"
                  size="lg"
                />
                <Text size="sm" className="text-gray-600">
                  Format HSL : {selectedColor}
                </Text>
              </Stack>
            </Tabs.Panel>
          </Tabs>

          {/* Aperçu de la couleur */}
          <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
            <div
              className="w-8 h-8 rounded border"
              style={{ backgroundColor: selectedColor }}
            />
            <div>
              <Text size="sm" fw={500}>Aperçu</Text>
              <Text size="xs" className="text-gray-500">{selectedColor}</Text>
            </div>
          </div>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeColorModal}>
              Annuler
            </Button>
            <Button
              onClick={handleColorSave}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Appliquer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal de liste des champs */}
      <Modal
        opened={isListModalOpen}
        onClose={closeListModal}
        title="Liste des champs"
        size="lg"
        centered
        styles={{
          title: {
            color: 'white',
            fontWeight: 600,
          },
          header: {
            backgroundColor: '#0ea5e9',
            borderBottom: 'none',
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
        }}
      >
        <div className="space-y-4">
          {viewingBloc && (
            <Table className="border border-gray-200">
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Titre</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Détail du type</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {getBlocChamps(viewingBloc).map((champ) => (
                  <Table.Tr key={champ.id}>
                    <Table.Td>
                      <Text>{champ.nom}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text>{champ.type}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text className="text-gray-600">
                        {champ.type === 'Autocomplétion' ? 'Liste des médicaments' :
                         champ.type === 'Dictionnaire libre' ? 'Facteurs de risque' :
                         champ.type === 'Liste des choix' ? 'Options prédéfinies' :
                         champ.type === 'Éditeur text' ? 'Texte enrichi' :
                         champ.type === 'Date' ? 'Sélecteur de date' :
                         champ.type === 'Pièces-jointes' ? 'Fichiers joints' :
                         'Dictionnaire médical'}
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default BlocDeSaisie;
