
"use client";
import { useState, useEffect, useCallback } from "react";
import React from "react";
import { useSearchParams, useParams } from "next/navigation";
import Link from "next/link";
import Icon from '@mdi/react';
import { mdiCardAccountDetails,mdiShieldAccount,mdiBadgeAccountOutline,mdiBeaker,mdiAccountFileText } from '@mdi/js';
import { Modal, Loader, Alert, Group, Text } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle } from '@tabler/icons-react';
import { Patient as CalendarPatient } from '@/types/typesCalendarPatient';
import { Patient as LocalPatient, Measurement, Attachment } from './types';
import {
  patientFormService,
  PatientFormData,
  patientFormUtils,
  MedicalRecord,
  PatientInsurance,
  PatientAttachment as FormPatientAttachment,
  BiometricMeasurement
} from '../../../../../services/patientFormService';
import FichePatient from "./FichePatient"
import {Assurance} from "./AssuranceNew"
import {FicheMedicale} from "./FicheMedicale"
import {Biometrie} from "./Biometrie"
import {PiecesJointes} from "./PiecesJointes"
import PatientList from "@/components/agenda/Appointments/PatientList";
import { useDisclosure,  } from "@mantine/hooks";
// Type combiné pour le patient qui satisfait les deux interfaces
//type CombinedPatient = CalendarPatient & LocalPatient;

import "~/styles/tab.css";
const tabMapping: { [key: string]: number } = {
  'Fichepatient': 1,
  'Assurance': 2,
  'FicheMedicale': 3,
   'Biometrie': 4,
  'PiecesJointes': 5,

};
function  AppointmentsPage() {
  // State management
  const [toggleState, setToggleState] = useState(1);
  const [isInsured, setIsInsured] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [patient, setPatient] = useState<PatientFormData | null>(null);
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [insurances, setInsurances] = useState<PatientInsurance[]>([]);
  const [attachments, setAttachments] = useState<FormPatientAttachment[]>([]);
  const [biometrics, setBiometrics] = useState<BiometricMeasurement[]>([]);

  // Additional form states (ALL HOOKS MUST BE AT TOP LEVEL)
  const [isFormInvalid, setIsFormInvalid] = useState(false);
  const [isDraft, setIsDraft] = useState(false);
  const [ListDesPatientOpened, { open: openListDesPatient, close: closeListDesPatient }] = useDisclosure(false);

  const searchParams = useSearchParams();
  const params = useParams();
  const patientId = params.id as string;

  // ALL HOOKS MUST BE DECLARED BEFORE ANY CONDITIONAL LOGIC
  // Load patient data from backend
  const loadPatientData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load patient basic info
      const patientData = await patientFormService.getPatient(patientId);
      setPatient(patientData);

      // Check if patient has insurance
      const patientInsurances = await patientFormService.getPatientInsurances(patientId);
      setInsurances(patientInsurances);
      setIsInsured(patientInsurances.length > 0);

      // Load additional data
      const [medicalData, attachmentData, biometricData] = await Promise.allSettled([
        patientFormService.getPatientMedicalRecords(patientId),
        patientFormService.getPatientAttachments(patientId),
        patientFormService.getPatientBiometrics(patientId)
      ]);

      // Process medical records
      if (medicalData.status === 'fulfilled') {
        setMedicalRecords(medicalData.value);
      } else {
        console.warn('Failed to load medical records:', medicalData.reason);
        setMedicalRecords([]);
      }

      // Process attachment data
      if (attachmentData.status === 'fulfilled') {
        setAttachments(attachmentData.value);
      } else {
        console.warn('Failed to load attachments:', attachmentData.reason);
        setAttachments([]);
      }

      // Process biometric data
      if (biometricData.status === 'fulfilled') {
        setBiometrics(biometricData.value);
      } else {
        console.warn('Failed to load biometric data:', biometricData.reason);
        setBiometrics([]);
      }

    } catch (error) {
      console.error('❌ Error loading patient data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load patient data');
    } finally {
      setLoading(false);
    }
  }, [patientId]);

  // Load data on component mount
  useEffect(() => {
    if (patientId && patientId !== 'undefined') {
      console.log('✅ Loading patient data for valid ID:', patientId);
      loadPatientData();
    } else {
      console.error('❌ Invalid patient ID, not loading data:', patientId);
      setLoading(false);
    }
  }, [patientId, loadPatientData]);

  // Effect to read URL parameters and set active tab
  useEffect(() => {
    const tabMapping: { [key: string]: number } = {
      'personal': 1,
      'medical': 2,
      'insurance': 3,
      'attachments': 4,
      'biometric': 5,
      'contract': 6
    };

    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);

  // Debug patient ID extraction
  console.log('🔍 Patient ID Debug:', {
    params,
    patientId,
    paramsId: params.id,
    searchParams: Object.fromEntries(searchParams.entries())
  });

  // Handle undefined patient ID
  if (!patientId || patientId === 'undefined') {
    console.error('❌ Patient ID is undefined or invalid:', patientId);
    console.log('🔍 Available params:', { params, searchParams: Object.fromEntries(searchParams.entries()) });

    // Return an error component instead of continuing with undefined ID
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Patient ID manquant</h2>
          <p className="text-red-600 mb-4">
            L&apos;ID du patient est manquant ou invalide. Impossible de charger les informations du patient.
          </p>
          <p className="text-sm text-red-500">
            ID reçu: &quot;{patientId}&quot;
          </p>
          <div className="mt-4">
            <Link
              href="/patient"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Retour à la liste des patients
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Form states (moved to top of component)

  // (loadPatientData moved to top of component)

  // Create mock patient for fallback
  const createMockPatient = (id: string): PatientFormData => ({
    id,
    title: "Mr",
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    phone_number: "123456789",
    date_of_birth: "1993-01-01",
    gender: "M",
    address: "123 Main St",
    social_security: "123456789",
    default_insurance: "Basic",
    file_number: "F001",
    notes: "Patient de test",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });

  // Convert PatientFormData to CalendarPatient format for component compatibility
  const convertToCalendarPatient = (patientData: PatientFormData): CalendarPatient => {
    const age = patientData.date_of_birth ?
      patientFormUtils.calculateAge(patientData.date_of_birth) : 0;

    return {
      id: patientData.id || '',
      title: patientData.title || '',
      name: patientData.last_name,
      prenom: patientData.first_name,
      first_name: patientData.first_name,
      last_name: patientData.last_name,
      birth_date: patientData.date_of_birth || '',
      appointmentDate: new Date().toISOString(),
      appointmentTime: "11:00",
      appointmentEndTime: "12:00",
      consultationDuration: 60,
      email: patientData.email || '',
      age: age,
      phone_numbers: patientData.phone_number || '',
      socialSecurity: patientData.social_security || '',
      duration: "60",
      agenda: "General",
      comment: patientData.notes || '',
      address: patientData.address || '',
      etatCivil: "Single",
      etatAganda: "Active",
      patientTitle: patientData.title || '',
      notes: patientData.notes || '',
      date: new Date().toISOString(),
      docteur: "Dr. Smith",
      event_Title: "Consultation",
      gender: patientData.gender || 'M',
      sociale: patientData.social_security || '',
      typeConsultation: "Consultation",
      commentairelistedattente: "",
      resourceId: 1,
      type: "visit" as const,
      eventType: "visit" as const,
      start: new Date(),
      end: new Date(),
      lastVisit: {
        date: new Date(patientData.updated_at || new Date()),
        notes: "Dernière consultation de suivi"
      }
    };
  };

  // Convert PatientFormData to LocalPatient format for components that need it
  const convertToLocalPatient = (patientData: PatientFormData): LocalPatient => {
    const age = patientData.date_of_birth ?
      patientFormUtils.calculateAge(patientData.date_of_birth) : 0;

    return {
      id: patientData.id || '',
      full_name: `${patientData.first_name} ${patientData.last_name}`,
      first_name: patientData.first_name,
      last_name: patientData.last_name,
      gender: patientData.gender || 'M',
      age: age,
      default_insurance: patientData.default_insurance || '',
      file_number: patientData.file_number || '',
      sociale: patientData.social_security || '',
      last_visit: patientData.updated_at || new Date().toISOString().split('T')[0],
      lastVisit: {
        date: patientData.updated_at?.split('T')[0] || new Date().toISOString().split('T')[0]
      }
    };
  };

  // Convert BiometricMeasurement to Measurement format
  const convertBiometricsToMeasurements = (biometrics: BiometricMeasurement[] | null | undefined): Measurement[] => {
    if (!biometrics || !Array.isArray(biometrics)) {
      console.warn('No biometric measurements to convert or invalid data:', biometrics);
      return [];
    }

    return biometrics.map(bio => ({
      id: bio.id || '',
      date: bio.measured_date,
      values: {
        [bio.measurement_type]: bio.value
      },
      comment: bio.notes || ''
    }));
  };

  // Convert FormPatientAttachment to Attachment format (Django PatientAttachment)
  const convertAttachmentsToLocal = (attachments: FormPatientAttachment[] | null | undefined): Attachment[] => {
    if (!attachments || !Array.isArray(attachments)) {
      console.warn('No attachments to convert or invalid data:', attachments);
      return [];
    }

    return attachments.map(att => ({
      id: att.id || '',
      patient: att.patient,
      original_filename: att.file_name,
      file_size: att.file_size,
      file_size_mb: att.file_size / (1024 * 1024),
      mime_type: att.file_type,
      attachment_type: 'document' as const,
      category: att.category === 'medical' ? 'medical_record' as const :
                att.category === 'administrative' ? 'insurance' as const :
                att.category === 'image' ? 'imaging' as const : 'other' as const,
      title: att.file_name,
      description: att.description || '',
      tags: '',
      is_private: false,
      is_sensitive: false,
      is_processed: true,
      processing_status: 'completed' as const,
      file_path: att.file_url,
      file_url: att.file_url,
      thumbnail_url: att.file_url,
      created_at: att.created_at || new Date().toISOString(),
      updated_at: att.created_at || new Date().toISOString(),
      uploaded_by: att.uploaded_by ? { id: att.uploaded_by, name: att.uploaded_by } : undefined,
      metadata: {}
    }));
  };

  // (useEffect for loading data moved to top of component)

  // Handler functions with backend integration
  const handlePrint = () => {
    console.log("🖨️ Printing patient form...");
    // TODO: Implement print functionality
    notifications.show({
      title: 'Impression',
      message: 'Fonctionnalité d\'impression en cours de développement',
      color: 'blue'
    });
  };

  const handlePrevious = () => {
    console.log("⬅️ Previous patient...");
    // TODO: Navigate to previous patient
  };

  const handleNext = () => {
    console.log("➡️ Next patient...");
    // TODO: Navigate to next patient
  };

  const handleStartVisit = () => {
    console.log("🏥 Starting visit...");
    // TODO: Create new visit/appointment
    notifications.show({
      title: 'Visite',
      message: 'Démarrage de la visite...',
      color: 'green'
    });
  };

  const handleAppointment = () => {
    console.log("📅 Creating appointment...");
    // TODO: Navigate to appointment creation
  };

  const handleCancel = () => {
    console.log("❌ Cancelling...");
    window.history.back();
  };

  const handleSaveQuitNew = async () => {
    console.log("💾 Save & New...");
    await handleSave();
    // TODO: Navigate to new patient form
  };

  const handleSaveQuit = async () => {
    console.log("💾 Save & Quit...");
    await handleSave();
    window.history.back();
  };

  const handleSubmit = async () => {
    console.log("📤 Submitting form...");
    await handleSave();
  };

  const handleSave = async () => {
    if (!patient) return;

    try {
      setLoading(true);

      // Validate patient data
      const errors = patientFormUtils.validatePatientData(patient);
      if (errors.length > 0) {
        setIsFormInvalid(true);
        notifications.show({
          title: 'Erreurs de validation',
          message: errors.join(', '),
          color: 'red'
        });
        return;
      }

      // Save patient data
      const updatedPatient = await patientFormService.updatePatient(patientId, patient);
      setPatient(updatedPatient);
      setIsFormInvalid(false);
      setIsDraft(false);

      notifications.show({
        title: 'Succès',
        message: 'Données du patient sauvegardées avec succès',
        color: 'green'
      });

      console.log('✅ Patient data saved successfully:', updatedPatient);

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save patient data';
      console.error('❌ Error saving patient data:', err);

      notifications.show({
        title: 'Erreur de sauvegarde',
        message: errorMessage,
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    console.log("🔙 Going back...");
    window.history.back();
  };

  const handleAddMeasurement = async () => {
    console.log("📏 Adding measurement...");
    // TODO: Open measurement dialog
    notifications.show({
      title: 'Mesure',
      message: 'Ajout de mesure en cours de développement',
      color: 'blue'
    });
  };

  const handleGoToContract = () => {
    console.log("📋 Going to contract...");
    // TODO: Navigate to contract page
  };
// (useEffect for tab mapping moved to top of component)
const icons = [
  { icon: <Icon path={mdiCardAccountDetails} size={1} key="FichePatient" />, label: "Fiche patient" },

  {
    icon: <Icon path={mdiShieldAccount} size={1} key="Assurance" />,
    label: "Assurance",
  },
  {
    icon: <Icon path={mdiBadgeAccountOutline} size={1} key="FicheMedicale" />,
    label: "Fiche médicale",
  },
  {
    icon: <Icon path={mdiBeaker} size={1} key="Biometrie" />,
    label: "Biométrie",
  },
   {
    icon: <Icon path={mdiAccountFileText} size={1} key="PiecesJointes" />,
    label: "Pièces jointes",
  },
  
];

const toggleTab = (index: number) => {
  // Prevent navigation to Assurance tab (index 2) if not insured
  if (index === 2 && !isInsured) {
    return; // Do nothing if trying to access Assurance tab when not insured
  }
  setToggleState(index);
};
const staffOptions = [
  { label: 'Dr. Smith', value: 'smith' },
  { label: 'Dr. Jones', value: 'jones' },
];

const triggerOptions = [
  { label: 'Douleur', value: 'pain' },
  { label: 'Contrôle', value: 'checkup' },
];

// Show loading state
if (loading) {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <Group>
        <Loader size="lg" />
        <Text size="lg">Chargement des données du patient...</Text>
      </Group>
    </div>
  );
}

// Show error state
if (error && !patient) {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <Alert
        icon={<IconAlertCircle size={16} />}
        title="Erreur de chargement"
        color="red"
        style={{ maxWidth: 500 }}
      >
        <Text>{error}</Text>
        <Group mt="md">
          <button
            onClick={loadPatientData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Réessayer
          </button>
          <button
            onClick={handleGoBack}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Retour
          </button>
        </Group>
      </Alert>
    </div>
  );
}

const renderTabContent = () => {
  // Convert patient data to the format expected by components
  const currentPatientData = patient || createMockPatient(patientId);
  const calendarPatient = convertToCalendarPatient(currentPatientData);
  const localPatient = convertToLocalPatient(currentPatientData);
  const convertedMeasurements = convertBiometricsToMeasurements(biometrics);
  const convertedAttachments = convertAttachmentsToLocal(attachments);

  // Common props for all components
  const baseProps = {
    patientId,
    patient: currentPatientData,  // Add patient data to baseProps
    medicalRecords,
    insurances,
    attachments: convertedAttachments,
    biometrics,
    measurements: convertedMeasurements,
    isFormInvalid,
    isDraft,
    onPrint: handlePrint,
    onPrevious: handlePrevious,
    onNext: handleNext,
    onStartVisit: handleStartVisit,
    onAppointment: handleAppointment,
    onCancel: handleCancel,
    onSaveQuitNew: handleSaveQuitNew,
    onSaveQuit: handleSaveQuit,
    onSubmit: handleSubmit,
    onGoBack: handleGoBack,
    onAddMeasurement: handleAddMeasurement,
    onGoToContract: handleGoToContract,
    selectedInsurance: "",
    setSelectedInsurance: () => {},
    affiliateNumber: "",
    setAffiliateNumber: () => {},
    affiliateType: "PATIENT" as const,
    setAffiliateType: () => {},
    organizationOptions: [],
    value: "",
    onChang: () => {},
    onAdd: () => {},
    locked: false,
    countryId: null,
    provinceId: null,
    values: null,
    onChange: () => {},
    disabled: false,
  
  };

  switch (toggleState) {
    case 1:
      return (
        <div className="w-full mb-[40px]">
          <FichePatient
          {...baseProps}
          onInsuredChange={setIsInsured}
          staffOptions={staffOptions}
          triggerOptions={triggerOptions}
          openListDesPatient={openListDesPatient}
        />

        </div>
      );

    case 2:
      return (
        <div className="w-full">
          <Assurance
            {...baseProps}
            patient={calendarPatient}
            onInsuredChange={setIsInsured}

            staffOptions={staffOptions}
            triggerOptions={triggerOptions}
            openListDesPatient={openListDesPatient}
          />
        </div>
      );

    case 3:
      return (
        <div className="w-full">
          <FicheMedicale
           {...baseProps}
           onInsuredChange={setIsInsured}
           onValueChange={() => {}}
           openListDesPatient={openListDesPatient}
           // Django integration props
           patientData={calendarPatient}
           enableDjangoSync={true}
           onPatientDataChange={(data) => {
             // Update the patient state when Django data changes
             console.log('Patient data changed:', data);
             // You could update the patient state here if needed
           }}
           staffOptions={staffOptions}
           triggerOptions={triggerOptions}
          />
        </div>
      );

    case 4:
      return (
        <div className="w-full">
          <Biometrie
            {...baseProps}
            patient={localPatient}
            measures={[]}
            onValueChange={() => {}}
            onLocationChange={() => {}}
            staffOptions={staffOptions}
            triggerOptions={triggerOptions}
            openListDesPatient={openListDesPatient}
          />
        </div>
      );

    case 5:
      return (
        <div className="w-full">
          <PiecesJointes
            {...baseProps}
            patient={localPatient}
            staffOptions={staffOptions}
            triggerOptions={triggerOptions}
            openListDesPatient={openListDesPatient}
          />
        </div>
      );

    default:
      return null;
  }
};
  return (
    <>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => {
          const isAssuranceTab = index + 1 === 2; // Assurance tab is index 2
          const isDisabled = isAssuranceTab && !isInsured;

          return (
            <button
              key={index}
              onClick={() => toggleTab(index + 1)}
              disabled={isDisabled}
              className={
                toggleState === index + 1
                  ? "tab tab-active flex items-center gap-2"
                  : isDisabled
                  ? "tab flex items-center gap-2 opacity-50 cursor-not-allowed"
                  : "tab flex items-center gap-2"
              }
              id={`card-type-tab-item-${index + 1}`}
              data-hs-tab={`#card-type-tab-${index + 1}`}
              aria-controls={`card-type-tab-${index + 1}`}
              role="tab"
              title={isDisabled ? "Activez 'Assuré' dans la fiche patient pour accéder à cet onglet" : ""}
            >
              {item.icon}
              <span>{item.label}</span>
            </button>
          );
        })}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    <Modal.Root opened={ListDesPatientOpened} onClose={closeListDesPatient}   size="100%"  >
                <PatientList/>
                 </Modal.Root>
    </>
  );
}

export default AppointmentsPage;

 