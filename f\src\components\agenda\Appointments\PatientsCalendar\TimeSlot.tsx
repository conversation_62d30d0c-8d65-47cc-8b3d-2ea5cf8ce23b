import React, { cloneElement, isValidElement } from "react";
import type { ReactElement } from "react";
import moment from "moment";
export function addZero(num: number): string {
  return num < 10 ? `0${num}` : num.toString();
}
interface CustomDivProps extends React.HTMLAttributes<HTMLDivElement> {
  "data-time": string;
}
type TimeSlotProps = {
  children: React.ReactElement<CustomDivProps> | React.ReactNode;
  value: Date;
  step: number;
  isRender: boolean;
  
};
const TimeSlot: React.FC<TimeSlotProps> = ({
  children,
  value,
  step,
  isRender,
}) => {
  const differenceMs = moment().diff(moment(value));
  const isCurrentTimeSlot =
    differenceMs / (60 * 1000) > 0 && differenceMs / (60 * 1000) < step;

  const currentTime = `${new Date().getHours()}:${addZero(
    new Date().getMinutes(),
  )}`;

  const calculateIndicatorPosition = (): number => {
    const minutesFromProps = value.getMinutes();
    const currentMinutes = new Date().getMinutes();
    switch (minutesFromProps) {
      case 0:
        return minutesFromProps + currentMinutes;
      case 15:
        return Math.abs(minutesFromProps - currentMinutes);
      default:
        return 0;
    }
  };

  // Style properties only valid for CSS
  const timeIndicatorStyle: React.CSSProperties =
    isCurrentTimeSlot && isRender
      ? {
          top: `${(100 / step) * calculateIndicatorPosition()}%`,
          width: "200%", // String with quotes, not percentage with !important
       
        }
      : {};

  // Additional props
  const additionalProps: Partial<CustomDivProps> =
    isCurrentTimeSlot && isRender
      ? {
          className: "current-time",
          children: (
            <span className="time-indicator" style={timeIndicatorStyle}>
              <span className="label">{currentTime}</span>
            </span>
          ),
        }
      : {};

  if (!isValidElement(children)) {
    return null;
  }

  // Cast children to ReactElement<CustomDivProps>
  const childrenElement = children as ReactElement<CustomDivProps>;

  return cloneElement(childrenElement, {
    style: { ...(childrenElement.props.style ?? {}), ...timeIndicatorStyle },
    "data-time": moment(value).format("HH:mm"),
    ...additionalProps,
  });
};

export default TimeSlot;
