"use client";
import { useState, useEffect } from "react";
import React from "react";
import { useSearchParams } from "next/navigation";

//import { rem } from "@mantine/core";
import Icon from '@mdi/react';
import { mdiAccountEdit,mdiAccountPlus,mdiArrowLeftBold } from '@mdi/js';
import EditePatient from "./EditePatient"
import AddPatient from "./AddPatient"
// import AddPatientPage from "./newpatient"
import "~/styles/tab.css";
import { Tooltip,  } from '@mantine/core';
const tabMapping: { [key: string]: number } = {

  'AddPatient': 2,
   'EditePatient': 3,

 
 
};
function  AppointmentsPage() {
   const searchParams = useSearchParams();
   
     // Effet pour lire les paramètres d'URL et définir l'onglet actif
     useEffect(() => {
       const tab = searchParams.get('tab');
       if (tab && tabMapping[tab]) {
         setToggleState(tabMapping[tab]);
       }
     }, [searchParams]);
  // const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(2);
 

const icons = [
   {
    icon: 
    <>
     <Tooltip label="retour" withArrow  style={{color:"var(--mantine-color-text)"}} 
>
    <Icon path={mdiArrowLeftBold} size={1} key="retour" />
    </Tooltip>
    </>,
    label: "",
  },
  {
    icon: <Icon path={mdiAccountPlus} size={1} key="AddPatient" />,
    label: "Ajouter un patient",
  },
  { icon: <Icon path={mdiAccountEdit} size={1} key="EditePatient" />, label: "Edite patient" },
  
 
  // {
  //   icon: <Icon path={mdiAccountEdit} size={1} key="newpatient" />,
  //   label: "newpatient",
  // },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<div>Test-3</div> )
    
     case 2:
      return (<div className="w-full"><AddPatient/></div>  )
      case 3:
         return ( <div className="w-full"><EditePatient/></div> )
        //  case 4:
        //    return ( <div className="w-full"><AddPatientPage/></div> );

    default:
      return null;
  }
};
  return (
    <>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 