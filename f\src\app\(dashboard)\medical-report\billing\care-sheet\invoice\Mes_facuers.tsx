'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  NumberInput,
  Textarea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconFileText,
  IconReceipt,
  IconCalendar,
  IconSearch,
  IconPlus,
  IconMinus,
  IconList,
} from '@tabler/icons-react';

const MesFactures = () => {
  // États pour les filtres et données
  const [numeroFacture, setNumeroFacture] = useState('7');
  const [dateFacture, setDateFacture] = useState<string>('2022-09-18');
  const [dateEcheance, setDateEcheance] = useState<string>('2022-09-18');
  const [mode, setMode] = useState('Espèce');
  const [beneficiaire, setBeneficiaire] = useState('Patient');
  const [patientSearch, setPatientSearch] = useState('');
  const [organismeSearch, setOrganismeSearch] = useState('');

  // États pour les éléments de la facture
  const [elementsFacture, setElementsFacture] = useState([
    { code: 'Aucun élément trouvé', description: '', qte: 0, prix: 0, remise: 0, montant: 0 }
  ]);

  // État pour les commentaires
  const [commentaire, setCommentaire] = useState('');

  // État pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Calcul du total
  const total = elementsFacture.reduce((sum, item) => sum + item.montant, 0);



  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconReceipt size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Facture N°: {numeroFacture}
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Liste des factures">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconList size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* N° Facture */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  N°. Facture *
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={numeroFacture}
                  onChange={(e) => setNumeroFacture(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Date */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Date *
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  value={dateFacture}
                  onChange={(value: string) => setDateFacture(value)}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Date d'échéance */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Date d&apos;échéance...
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  value={dateEcheance}
                  onChange={(value: string) => setDateEcheance(value)}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Mode */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Mode
                </Text>
              </div>
              <div className="p-2">
                <Select
                  value={mode}
                  onChange={(value) => setMode(value || 'Espèce')}
                  data={['Espèce', 'Chèque', 'Carte', 'Virement']}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Bénéficiaire */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Bénéficiaire
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={beneficiaire}
                  onChange={setBeneficiaire}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="Patient" label="Patient" />
                    <Radio value="Organisme" label="Organisme" />
                  </Stack>
                </Radio.Group>
              </div>
            </div>

            {/* Patient */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Patient *
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={patientSearch}
                  onChange={(e) => setPatientSearch(e.target.value)}
                  size="xs"
                  className="w-full"
                  rightSection={<IconSearch size={14} className="text-blue-500" />}
                />
              </div>
            </div>

            {/* Organisme (si sélectionné) */}
            {beneficiaire === 'Organisme' && (
              <div className="border border-gray-300 rounded">
                <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                  <Text size="sm" fw={500} className="text-gray-700">
                    Organisme *
                  </Text>
                </div>
                <div className="p-2">
                  <TextInput
                    value={organismeSearch}
                    onChange={(e) => setOrganismeSearch(e.target.value)}
                    size="xs"
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </Stack>
        </Card>

        {/* Zone principale du contenu */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Barre d'outils avec boutons */}
          <div className="bg-gray-100 border-b border-gray-300 p-2">
            <Group gap="xs" className="flex flex-wrap">
              <Button
                size="xs"
                variant="light"
                color="blue"
                leftSection={<IconSearch size={14} />}
                className="text-xs"
              >
                Visites
              </Button>
              <Button
                size="xs"
                variant="light"
                color="orange"
                leftSection={<IconSearch size={14} />}
                className="text-xs"
              >
                Visites dentaire
              </Button>
              <Button
                size="xs"
                variant="light"
                color="purple"
                leftSection={<IconFileText size={14} />}
                className="text-xs"
              >
                Plan de traitement
              </Button>
              <Button
                size="xs"
                variant="light"
                color="green"
                leftSection={<IconReceipt size={14} />}
                className="text-xs"
              >
                Pharmacie
              </Button>
              <Button
                size="xs"
                variant="light"
                color="yellow"
                leftSection={<IconFileText size={14} />}
                className="text-xs"
              >
                Actes
              </Button>
              <Button
                size="xs"
                variant="light"
                color="red"
                leftSection={<IconFileText size={14} />}
                className="text-xs"
              >
                Autres
              </Button>
              <Button
                size="xs"
                variant="light"
                color="gray"
                leftSection={<IconFileText size={14} />}
                className="text-xs"
              >
                Commentaire
              </Button>
            </Group>
          </div>

          {/* Tableau des éléments */}
          <div className="flex-1 overflow-auto">
            <Table
              striped={false}
              highlightOnHover={true}
              withTableBorder={true}
              withColumnBorders={true}
              className="h-full"
            >
              <Table.Thead className="bg-gray-50 sticky top-0">
                <Table.Tr>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-32">
                    Code
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                    Description
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-20">
                    Qté
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                    Prix
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                    Remise
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                    Montant
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {elementsFacture.map((element, index) => (
                  <Table.Tr key={index} className="hover:bg-gray-50">
                    <Table.Td className="border-r border-gray-300 p-1">
                      <TextInput
                        value={element.code}
                        onChange={(e) => {
                          const newElements = [...elementsFacture];
                          newElements[index].code = e.target.value;
                          setElementsFacture(newElements);
                        }}
                        size="xs"
                        className="w-full"
                      />
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 p-1">
                      <TextInput
                        value={element.description}
                        onChange={(e) => {
                          const newElements = [...elementsFacture];
                          newElements[index].description = e.target.value;
                          setElementsFacture(newElements);
                        }}
                        size="xs"
                        className="w-full"
                      />
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 p-1">
                      <NumberInput
                        value={element.qte}
                        onChange={(value) => {
                          const newElements = [...elementsFacture];
                          newElements[index].qte = Number(value) || 0;
                          newElements[index].montant = newElements[index].qte * newElements[index].prix - newElements[index].remise;
                          setElementsFacture(newElements);
                        }}
                        size="xs"
                        className="w-full"
                        min={0}
                      />
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 p-1">
                      <NumberInput
                        value={element.prix}
                        onChange={(value) => {
                          const newElements = [...elementsFacture];
                          newElements[index].prix = Number(value) || 0;
                          newElements[index].montant = newElements[index].qte * newElements[index].prix - newElements[index].remise;
                          setElementsFacture(newElements);
                        }}
                        size="xs"
                        className="w-full"
                        min={0}
                        decimalScale={2}
                      />
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 p-1">
                      <NumberInput
                        value={element.remise}
                        onChange={(value) => {
                          const newElements = [...elementsFacture];
                          newElements[index].remise = Number(value) || 0;
                          newElements[index].montant = newElements[index].qte * newElements[index].prix - newElements[index].remise;
                          setElementsFacture(newElements);
                        }}
                        size="xs"
                        className="w-full"
                        min={0}
                        decimalScale={2}
                      />
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 p-1">
                      <Text size="sm" className="text-gray-800 text-right font-medium">
                        {element.montant.toFixed(2)}
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </div>

          {/* Section pagination et total */}
          <div className="border-t border-gray-300 bg-gray-50 p-3">
            <Group justify="space-between" align="center">
              <Group gap="sm" align="center">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(Number(value) || 1)}
                  data={['1', '2', '3', '4', '5']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(Number(value) || 5)}
                  data={['5', '10', '20', '50']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                <Text size="sm" className="text-gray-600">K</Text>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconMinus size={12} />
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconPlus size={12} />
                  </ActionIcon>
                </Group>
              </Group>

              <Text size="lg" fw={600} className="text-gray-800">
                Total : {total.toFixed(2)}
              </Text>
            </Group>
          </div>

          {/* Section commentaires */}
          <div className="border-t border-gray-300 bg-white p-3">
            <Text size="sm" fw={500} className="text-gray-700 mb-2">
              Commentaire
            </Text>
            <Textarea
              value={commentaire}
              onChange={(e) => setCommentaire(e.target.value)}
              placeholder="Ajouter un commentaire..."
              size="sm"
              rows={3}
              className="w-full"
            />
          </div>

          {/* Boutons d'action */}
          <div className="border-t border-gray-300 bg-gray-50 p-3">
            <Group justify="flex-end" gap="sm">
              <Button
                variant="filled"
                color="red"
                size="sm"
                className="bg-red-500 hover:bg-red-600"
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="gray"
                size="sm"
                className="bg-gray-500 hover:bg-gray-600"
              >
                Enregistrer et quitter
              </Button>
              <Button
                variant="filled"
                color="blue"
                size="sm"
                className="bg-blue-500 hover:bg-blue-600"
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </div>
      </div>
    </Box>
  );
};

export default MesFactures;
