'use client';
import { useState, useEffect } from 'react';
import React from "react";
import { rem } from "@mantine/core";
import Icon from '@mdi/react';
import { mdiCalendarClockOutline } from '@mdi/js';
import MetaSeo from"./MetaSeo"
import "~/style/tab.css";

import { Title,  Text, Alert, Button, Group, Stack, Paper, Badge } from '@mantine/core';
import { IconAlertCircle, IconPackage, IconHistory, IconCreditCard } from '@tabler/icons-react';
import { useRouter, useSearchParams } from 'next/navigation';
import PackageSelector from '~/components/subscription/PackageSelector';
import SubscriptionDetails from '~/components/subscription/SubscriptionDetails';
import CouponForm from '~/components/subscription/CouponForm';
import subscriptionService, { SubscriptionPackage, Coupon, SubscriptionTransaction } from '~/services/subscriptionService';

export default function SubscriptionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [, setActiveTab] = useState<string | null>('current');
  const [selectedPackage, setSelectedPackage] = useState<SubscriptionPackage | null>(null);
  const [selectedBillingCycle, setSelectedBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [transactions, setTransactions] = useState<SubscriptionTransaction[]>([]);
  const [hasActiveSubscription, setHasActiveSubscription] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(1);
 

const icons = [
  { icon: <IconPackage style={iconStyle} key="CurrentSubscription" />, label: "Current Subscription" },
  {
    icon: <IconCreditCard style={iconStyle} key="SubscriptionPackages" />,
    label: " Subscription Packages",
  },
  {
    icon: <IconHistory style={iconStyle} key="TransactionHistory" />,
    label: "Transaction History",
  },
  {
    icon: <Icon path={mdiCalendarClockOutline} size={1} key="Test-3" />,
    label: "Test-3",
  },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return ( <Paper p="xl" radius="md" withBorder mb="60" w={"100%"}>

          {hasActiveSubscription ? (
            <SubscriptionDetails
              onRenew={() => {
                // Refresh the page to show the updated subscription
                window.location.reload();
              }}
              onCancel={() => {
                // Refresh the page to show the updated subscription
                window.location.reload();
              }}
            />
          ) : (
            <Alert icon={<IconAlertCircle size="1rem" />} title="No Active Subscription" color="yellow">
              <Text mb="md">You don&apos;t have an active subscription. Please subscribe to a plan to access premium features.</Text>
              <Button onClick={() => setActiveTab('packages')}>View Subscription Packages</Button>
            </Alert>
          )}
          </Paper>
       
 )
    
     case 2:
      return (  <>
          <PackageSelector
            onSelectPackage={handleSelectPackage}
            initialBillingCycle={selectedBillingCycle}
            preSelectedPackage={selectedPackage || undefined}
          />

          {selectedPackage && (
            <Paper p="md" withBorder mt="xl">
              <Stack>
                <Title order={3}>Order Summary</Title>

                <Group justify="space-between">
                  <Text>{selectedPackage.name} Plan ({selectedBillingCycle === 'monthly' ? 'Monthly' : 'Annual'})</Text>
                  <Text>
                    ${selectedBillingCycle === 'monthly' ? selectedPackage.price_monthly : selectedPackage.price_yearly}
                  </Text>
                </Group>

                {appliedCoupon && (
                  <Group justify="space-between">
                    <Text>Discount ({appliedCoupon.code})</Text>
                    <Text c="green">
                      {appliedCoupon.discount_type === 'percentage'
                        ? `-${appliedCoupon.discount_value}%`
                        : `-$${appliedCoupon.discount_value}`}
                    </Text>
                  </Group>
                )}

                <Group justify="space-between" style={{ fontWeight: 'bold' }}>
                  <Text>Total</Text>
                  <Text>${calculatePrice()}</Text>
                </Group>

                <CouponForm
                  packageId={selectedPackage.id}
                  onApplyCoupon={handleApplyCoupon}
                />

                {error && (
                  <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
                    {error}
                  </Alert>
                )}

                <Button
                  color="blue"
                  fullWidth
                  mt="md"
                  onClick={handleSubscribe}
                >
                  Subscribe Now
                </Button>
              </Stack>
            </Paper>
          )}
       </>)
      case 3:
        return (  <>
          {transactions.length === 0 ? (
            <Alert icon={<IconAlertCircle size="1rem" />} title="No Transactions" color="blue">
              You don&apos;t have any transaction history yet.
            </Alert>
          ) : (
            <Paper p="md" withBorder>
              <Title order={3} mb="md">Transaction History</Title>

              {transactions.map((transaction) => (
                <Paper key={transaction.id} p="md" withBorder mb="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Transaction #{transaction.id}</Text>
                      <Text size="sm" c="dimmed">
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </Text>
                    </div>
                    <Badge
                      color={
                        transaction.status === 'completed' ? 'green' :
                        transaction.status === 'pending' ? 'yellow' :
                        transaction.status === 'refunded' ? 'blue' : 'red'
                      }
                    >
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </Badge>
                  </Group>

                  <Group justify="space-between" mt="md">
                    <Text>Amount</Text>
                    <Text>${transaction.amount}</Text>
                  </Group>

                  {transaction.discount_amount !== '0.00' && (
                    <Group justify="space-between">
                      <Text>Discount</Text>
                      <Text c="green">-${transaction.discount_amount}</Text>
                    </Group>
                  )}

                  {transaction.payment_method && (
                    <Group justify="space-between">
                      <Text>Payment Method</Text>
                      <Text>{transaction.payment_method}</Text>
                    </Group>
                  )}
                </Paper>
              ))}
            </Paper>
          )}
       </>)
        case 4:
          return <div>Test-4</div>;

    default:
      return null;
  }
};
  useEffect(() => {
    // Check if searchParams is available
    if (!searchParams) {
      console.warn('Search parameters are not available');
      return;
    }

    // Check if there's a tab parameter in the URL
    const tabParam = searchParams.get('tab');
    if (tabParam && ['current', 'packages', 'history'].includes(tabParam)) {
      setActiveTab(tabParam);
    } else {
      // If no tab is specified, but we have package parameters, go to packages tab
      const packageId = searchParams.get('packageId');
      if (packageId) {
        setActiveTab('packages');
      }
    }

    // Check if there are package parameters in the URL
    const packageId = searchParams.get('packageId');
    const packageName = searchParams.get('packageName');
    const packagePrice = searchParams.get('packagePrice');
    const billingCycle = searchParams.get('billingCycle') as 'monthly' | 'annual' | null;

    // If we have package parameters, pre-select the package
    if (packageId && packageName && packagePrice) {
      // Create a temporary package object from URL parameters
      const urlPackage: SubscriptionPackage = {
        id: parseInt(packageId),
        name: packageName,
        description: `Selected from web frontend`,
        max_assistants: 1,
        max_users: 2,
        max_specialties: 1,
        price_monthly: billingCycle === 'annual' ? '0' : packagePrice,
        price_yearly: billingCycle === 'annual' ? packagePrice : '0',
        features: ['Feature information will be loaded'],
        is_active: true
      };

      setSelectedPackage(urlPackage);
      if (billingCycle) {
        setSelectedBillingCycle(billingCycle);
      }
    }

    // Check if the user has an active subscription
    const checkSubscription = async () => {
      try {
        const subscription = await subscriptionService.getCurrentSubscription();
        setHasActiveSubscription(!!subscription);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        // If there's a 404 error, it means the user doesn't have an active subscription
        setHasActiveSubscription(false);
      }
    };

    // Fetch transaction history
    const fetchTransactions = async () => {
      try {
        const data = await subscriptionService.getTransactions();
        setTransactions(data);
      } catch (err) {
        console.error('Error fetching transactions:', err);
      }
    };

    checkSubscription();
    fetchTransactions();
  }, [searchParams]);

  const handleSelectPackage = (pkg: SubscriptionPackage, billingCycle: 'monthly' | 'annual') => {
    setSelectedPackage(pkg);
    setSelectedBillingCycle(billingCycle);
  };

  const handleApplyCoupon = (coupon: Coupon) => {
    setAppliedCoupon(coupon);
  };

  const handleSubscribe = async () => {
    if (!selectedPackage) {
      setError('Please select a package first');
      return;
    }

    try {
      // Check if this is a 6-month package (when billing_cycle is monthly)
      const is_six_month = selectedBillingCycle === 'monthly';

      console.log(`Creating subscription with package ID ${selectedPackage.id}, billing cycle ${selectedBillingCycle}, is_six_month=${is_six_month}`);

      await subscriptionService.createSubscription({
        package: selectedPackage.id,
        billing_cycle: selectedBillingCycle,
        is_six_month: is_six_month,
      });

      // Redirect to the current subscription tab
      router.push('/subscription?tab=current');
      setActiveTab('current');
      setHasActiveSubscription(true);
    } catch (err: unknown) {
      console.error('Error creating subscription:', err);
      // Type guard to check if err is an object with a response property
      if (err && typeof err === 'object' && 'response' in err &&
          err.response && typeof err.response === 'object' && 'data' in err.response &&
          err.response.data && typeof err.response.data === 'object' && 'detail' in err.response.data &&
          typeof err.response.data.detail === 'string') {
        setError(err.response.data.detail);
      } else {
        setError('Failed to create subscription. Please try again.');
      }
    }
  };

  const calculatePrice = () => {
    if (!selectedPackage) return 0;

    let price = selectedBillingCycle === 'monthly'
      ? parseFloat(selectedPackage.price_monthly)
      : parseFloat(selectedPackage.price_yearly);

    if (appliedCoupon) {
      if (appliedCoupon.discount_type === 'percentage') {
        price = price * (1 - parseFloat(appliedCoupon.discount_value) / 100);
      } else {
        price = Math.max(0, price - parseFloat(appliedCoupon.discount_value));
      }
    }

    return price.toFixed(2);
  };

  return (
     <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
    // <Container size="lg" py="xl">
    //   <Title order={2} mb="xl">Subscription Management</Title>

    //   <Tabs value={activeTab} onChange={setActiveTab}>
    //     <Tabs.List>
    //       <Tabs.Tab value="current" leftSection={<IconPackage size="1rem" />}>
    //         Current Subscription
    //       </Tabs.Tab>
    //       <Tabs.Tab value="packages" leftSection={<IconCreditCard size="1rem" />}>
    //         Subscription Packages
    //       </Tabs.Tab>
    //       <Tabs.Tab value="history" leftSection={<IconHistory size="1rem" />}>
    //         Transaction History
    //       </Tabs.Tab>
    //     </Tabs.List>

    //     <Tabs.Panel value="current" pt="xl">
    //       {hasActiveSubscription ? (
    //         <SubscriptionDetails
    //           onRenew={() => {
    //             // Refresh the page to show the updated subscription
    //             window.location.reload();
    //           }}
    //           onCancel={() => {
    //             // Refresh the page to show the updated subscription
    //             window.location.reload();
    //           }}
    //         />
    //       ) : (
    //         <Alert icon={<IconAlertCircle size="1rem" />} title="No Active Subscription" color="yellow">
    //           <Text mb="md">You don&apos;t have an active subscription. Please subscribe to a plan to access premium features.</Text>
    //           <Button onClick={() => setActiveTab('packages')}>View Subscription Packages</Button>
    //         </Alert>
    //       )}
    //     </Tabs.Panel>

    //     <Tabs.Panel value="packages" pt="xl">
    //       <PackageSelector
    //         onSelectPackage={handleSelectPackage}
    //         initialBillingCycle={selectedBillingCycle}
    //         preSelectedPackage={selectedPackage || undefined}
    //       />

    //       {selectedPackage && (
    //         <Paper p="md" withBorder mt="xl">
    //           <Stack>
    //             <Title order={3}>Order Summary</Title>

    //             <Group justify="space-between">
    //               <Text>{selectedPackage.name} Plan ({selectedBillingCycle === 'monthly' ? 'Monthly' : 'Annual'})</Text>
    //               <Text>
    //                 ${selectedBillingCycle === 'monthly' ? selectedPackage.price_monthly : selectedPackage.price_yearly}
    //               </Text>
    //             </Group>

    //             {appliedCoupon && (
    //               <Group justify="space-between">
    //                 <Text>Discount ({appliedCoupon.code})</Text>
    //                 <Text c="green">
    //                   {appliedCoupon.discount_type === 'percentage'
    //                     ? `-${appliedCoupon.discount_value}%`
    //                     : `-$${appliedCoupon.discount_value}`}
    //                 </Text>
    //               </Group>
    //             )}

    //             <Group justify="space-between" style={{ fontWeight: 'bold' }}>
    //               <Text>Total</Text>
    //               <Text>${calculatePrice()}</Text>
    //             </Group>

    //             <CouponForm
    //               packageId={selectedPackage.id}
    //               onApplyCoupon={handleApplyCoupon}
    //             />

    //             {error && (
    //               <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
    //                 {error}
    //               </Alert>
    //             )}

    //             <Button
    //               color="blue"
    //               fullWidth
    //               mt="md"
    //               onClick={handleSubscribe}
    //             >
    //               Subscribe Now
    //             </Button>
    //           </Stack>
    //         </Paper>
    //       )}
    //     </Tabs.Panel>

    //     <Tabs.Panel value="history" pt="xl">
    //       {transactions.length === 0 ? (
    //         <Alert icon={<IconAlertCircle size="1rem" />} title="No Transactions" color="blue">
    //           You don&apos;t have any transaction history yet.
    //         </Alert>
    //       ) : (
    //         <Paper p="md" withBorder>
    //           <Title order={3} mb="md">Transaction History</Title>

    //           {transactions.map((transaction) => (
    //             <Paper key={transaction.id} p="md" withBorder mb="md">
    //               <Group justify="space-between">
    //                 <div>
    //                   <Text fw={500}>Transaction #{transaction.id}</Text>
    //                   <Text size="sm" c="dimmed">
    //                     {new Date(transaction.created_at).toLocaleDateString()}
    //                   </Text>
    //                 </div>
    //                 <Badge
    //                   color={
    //                     transaction.status === 'completed' ? 'green' :
    //                     transaction.status === 'pending' ? 'yellow' :
    //                     transaction.status === 'refunded' ? 'blue' : 'red'
    //                   }
    //                 >
    //                   {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
    //                 </Badge>
    //               </Group>

    //               <Group justify="space-between" mt="md">
    //                 <Text>Amount</Text>
    //                 <Text>${transaction.amount}</Text>
    //               </Group>

    //               {transaction.discount_amount !== '0.00' && (
    //                 <Group justify="space-between">
    //                   <Text>Discount</Text>
    //                   <Text c="green">-${transaction.discount_amount}</Text>
    //                 </Group>
    //               )}

    //               {transaction.payment_method && (
    //                 <Group justify="space-between">
    //                   <Text>Payment Method</Text>
    //                   <Text>{transaction.payment_method}</Text>
    //                 </Group>
    //               )}
    //             </Paper>
    //           ))}
    //         </Paper>
    //       )}
    //     </Tabs.Panel>
    //   </Tabs>
    // </Container>
  );
}
