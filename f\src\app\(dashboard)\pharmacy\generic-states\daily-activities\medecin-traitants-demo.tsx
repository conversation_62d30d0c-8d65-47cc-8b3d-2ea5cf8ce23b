'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { MedecinTraitants } from './Medecin_traitants';

export default function MedecinTraitantsDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    console.log(`Période sélectionnée: du ${startDate} au ${endDate}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
    alert(`Source de données sélectionnée: ${state.label}`);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des médecins traitants en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des médecins traitants en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'doctor': 'Médecin',
      'patient_name': 'Nom du patient'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MedecinTraitantsLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onStateChange={(state) => console.log('State:', state)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
            onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MedecinTraitantsWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données des médecins traitants chargées pour:', query);
    }, 1000);
  };

  const handleStateChange = (state: any) => {
    console.log('Changement d\'état des médecins:', state);
    
    if (state.type === 'treating_doctor') {
      alert('Mode Médecin Traitant activé - Affichage des médecins référents');
    } else if (state.type === 'referred_by') {
      alert('Mode Adressé Par activé - Affichage des médecins prescripteurs');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des médecins traitants avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des médecins traitants avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'doctor': 'Tri des médecins par nom',
      'patient_name': 'Tri des patients par nom'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode "Adressé par"
export function MedecinTraitantsReferredByDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query mode adressé par:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    alert(`Recherche des médecins prescripteurs du ${startDate} au ${endDate}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État mode adressé par:', state);
    if (state.type === 'referred_by') {
      alert('Mode "Adressé par" activé - Recherche des médecins qui ont adressé des patients');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={(format) => alert(`Export ${format} des médecins prescripteurs`)}
            onPrint={() => alert('Impression des médecins prescripteurs')}
            onSort={(columnId, direction) => console.log('Sort prescripteurs:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MedecinTraitantsErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec gestion d\'erreurs:', query);
    
    // Simuler une validation de période
    const startDate = new Date(query.start);
    const endDate = new Date(query.end);
    
    if (endDate < startDate) {
      alert('Erreur: La date de fin ne peut pas être antérieure à la date de début');
      return;
    }
    
    // Simuler une période trop longue
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      alert('Attention: La période sélectionnée est très longue (plus d\'un an). Cela peut affecter les performances.');
    }
    
    console.log('Période validée, chargement des médecins...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={(state) => console.log('State avec validation:', state)}
            onExport={(format) => {
              console.log(`Export ${format} avec validation`);
              if (confirm(`Êtes-vous sûr de vouloir exporter les médecins traitants en ${format.toUpperCase()} ?`)) {
                alert('Export en cours...');
              }
            }}
            onPrint={() => {
              console.log('Impression avec validation');
              if (confirm('Êtes-vous sûr de vouloir imprimer les médecins traitants ?')) {
                alert('Impression en cours...');
              }
            }}
            onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données de médecins simulées
export function MedecinTraitantsSimulatedDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query données simulées:', query);
    
    // Simuler des données de médecins
    const mockDoctors = [
      { name: 'Dr. Ahmed Benali', specialty: 'Cardiologie', patients: 25 },
      { name: 'Dr. Fatima Zahra', specialty: 'Pédiatrie', patients: 18 },
      { name: 'Dr. Mohamed Alami', specialty: 'Orthopédie', patients: 12 },
      { name: 'Dr. Aicha Bennani', specialty: 'Dermatologie', patients: 8 },
      { name: 'Dr. Youssef Tazi', specialty: 'Neurologie', patients: 15 }
    ];
    
    const totalPatients = mockDoctors.reduce((sum, doctor) => sum + doctor.patients, 0);
    alert(`${mockDoctors.length} médecins trouvés avec ${totalPatients} patients au total`);
  };

  const handleStateChange = (state: any) => {
    console.log('État données simulées:', state);
    
    // Simuler des statistiques selon le type
    if (state.type === 'treating_doctor') {
      alert('Médecins traitants: 15 médecins actifs, 78 patients suivis');
    } else if (state.type === 'referred_by') {
      alert('Médecins prescripteurs: 8 médecins référents, 32 orientations');
    }
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri données simulées:', columnId, direction);
    
    // Simuler le tri des données
    const sortLabels: { [key: string]: string } = {
      'doctor': 'noms des médecins',
      'patient_name': 'noms des patients'
    };
    
    const label = sortLabels[columnId] || 'données';
    alert(`Tri des ${label} effectué en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <MedecinTraitants
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={(format) => alert(`Export ${format} des données simulées`)}
            onPrint={() => alert('Impression des données simulées')}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
