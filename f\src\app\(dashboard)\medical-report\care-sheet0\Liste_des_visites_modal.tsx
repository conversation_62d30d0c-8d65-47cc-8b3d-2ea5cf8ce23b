'use client';
import React, { useState } from 'react';
import {
  Modal,
  Table,
  Text,
  Group,
  Button,
  Checkbox,
  Select,
  Pagination,
} from '@mantine/core';

interface ListeDesVisitesModalProps {
  opened: boolean;
  onClose: () => void;
}

const Liste_des_visites_modal = ({ opened, onClose }: ListeDesVisitesModalProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="📋 Liste des visites"
      size="90%"
      centered
    >
      <div className="bg-white">
        {/* Tableau des visites */}
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
        >
          <Table.Thead className="bg-gray-50">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox size="sm" />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Préno...
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Montant dû
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Montant encaissé
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Remise
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                État
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                Reste à régler
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            <Table.Tr>
              <Table.Td colSpan={9} className="text-center py-8">
                <Text size="sm" className="text-gray-500">
                  Aucun élément trouvé
                </Text>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>

        {/* Footer avec pagination */}
        <div className="border-t border-gray-300 bg-gray-50 p-3 mt-4">
          <Group justify="space-between" align="center">
            <Group gap="sm" align="center">
              <Text size="sm" className="text-gray-600">Page</Text>
              <Select
                value={currentPage.toString()}
                onChange={(value) => setCurrentPage(Number(value) || 1)}
                data={['1']}
                size="xs"
                className="w-16"
              />
              <Text size="sm" className="text-gray-600">Lignes par Page</Text>
              <Select
                value={itemsPerPage.toString()}
                onChange={(value) => setItemsPerPage(Number(value) || 10)}
                data={['10', '25', '50']}
                size="xs"
                className="w-16"
              />
              <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
              <Text size="sm" className="text-gray-600">K</Text>
            </Group>

            <Pagination
              total={1}
              value={currentPage}
              onChange={setCurrentPage}
              size="sm"
            />
          </Group>
        </div>

        {/* Boutons d'action */}
        <Group justify="flex-end" gap="sm" className="mt-4">
          <Button
            color="blue"
            size="sm"
          >
            Ok
          </Button>
          <Button
            variant="outline"
            color="red"
            size="sm"
            onClick={onClose}
          >
            Annuler
          </Button>
        </Group>
      </div>
    </Modal>
  );
};

export default Liste_des_visites_modal;
