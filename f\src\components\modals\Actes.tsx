import React, { useState, useMemo } from 'react';
import {
  Modal,
  Table,
  TextInput,
  Button,
  Checkbox,
  Tabs,
  Menu,
  ActionIcon,
  Pagination,
  Select,
  Text,
  Group,
  Stack,
  Paper,
  ScrollArea,
  Switch,
   Divider
} from '@mantine/core';
// import {
//   Search,
//   Filter,
//   FileSpreadsheet,
//   MoreVertical,
//   Check,
//   RotateCcw,
//   FolderSearch,
//   FilterX,
//   Settings
// } from 'lucide-react';
import Icon from '@mdi/react';
import { mdiPlaylistCheck } from '@mdi/js';
interface PatientVisitsDialogProps {
  // opened: boolean;
  onClose: () => void;
  // beneficiaryType?: 'ORGANIZATION' | 'INDIVIDUAL';
}
type Procedure = {
  id: number;
  code: string;
  name: string;
};
type Column = {
  key: keyof Procedure | string;
  label: string;
  visible: boolean;
  searchable: boolean;
};
// const ProcedureListDialog = () => {
const Actes: React.FC<PatientVisitsDialogProps> = ({ 
      // opened, 
      onClose, 
      // beneficiaryType = 'ORGANIZATION' 
    }) => {
//   const [opened, setOpened] = useState(true);
const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchAll, setSearchAll] = useState('');
  const [searchCode, setSearchCode] = useState('');
  const [searchName, setSearchName] = useState('');
  const [activeTab, setActiveTab] = useState('filter');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [showSidebar, setShowSidebar] = useState(false);
  const [sortBy, setSortBy] = useState<keyof Procedure>('code');
  const [sortOrder, setSortOrder] = useState('asc');
  const [columns, setColumns] = useState([
    { key: 'code', label: 'Code', visible: true, searchable: true },
    { key: 'name', label: 'Nom', visible: true, searchable: true }
  ]);
  const [filters, setFilters] = useState<Record<keyof Procedure, boolean>>({
    code: false,
    name: false,
    id: false
  });

  // Sample data
  const [procedures] = useState([
    { id: 1, code: 'CONE001', name: 'Cone Beam' },
    { id: 2, code: 'XRAY002', name: 'Radiographie' },
    { id: 3, code: 'SCAN003', name: 'Scanner' },
    // Add more sample data as needed
     { id: 4, code: 'RTR', name: 'Rétroalvéolaire' },
    { id: 5, code: 'RXP', name: 'Rx Panoramique' },
    { id: 6, code: 'CBM', name: 'Cone Beam' },
     { id: 7, code: 'CIB', name: 'Camera Intrabuccale' },
    { id: 8, code: 'HBD', name: 'Motivation à l’HBD' },
    { id: 9, code: 'EME', name: 'Empreinte D’étude' },
     { id: 10, code: 'ANL', name: 'Analyse Biologique' },
    { id: 11, code: 'SPR', name: 'Sondage Parodontal' },
    { id: 12, code: 'DET', name: 'Détartrage' },
    { id: 13, code: 'AEP', name: 'Aéropolissage' },
     { id: 14, code: 'AFS', name: 'Ablation de fil de suture' },
    { id: 15, code: 'COT', name: 'Contention' },
    { id: 16, code: 'CLT', name: 'Consultation' },
     { id: 17, code: 'ORD', name: 'Ordonnance' },
    { id: 18, code: 'CRI', name: 'Curetage interdentaire' },
   
  ]);

  const totalRecords = 162; // Mock total

  const filteredData = useMemo(() => {
    return procedures.filter(procedure => {
      const matchesSearchAll = !searchAll || 
        procedure.code.toLowerCase().includes(searchAll.toLowerCase()) ||
        procedure.name.toLowerCase().includes(searchAll.toLowerCase());
      
      const matchesSearchCode = !searchCode || 
        procedure.code.toLowerCase().includes(searchCode.toLowerCase());
      
      const matchesSearchName = !searchName || 
        procedure.name.toLowerCase().includes(searchName.toLowerCase());

      return matchesSearchAll && matchesSearchCode && matchesSearchName;
    });
  }, [procedures, searchAll, searchCode, searchName]);

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return sortOrder === 'asc' 
        ? String(aValue).localeCompare(String(bValue))
        : String(bValue).localeCompare(String(aValue));
    });
  }, [filteredData, sortBy, sortOrder]);

  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    return sortedData.slice(start, start + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const handleSort = (column: keyof Procedure) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(paginatedData.map(item => item.id));
    } else {
      setSelectedRows([]);
    }
  };

  const handleSelectRow = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id]);
    } else {
      setSelectedRows(selectedRows.filter(rowId => rowId !== id));
    }
  };

  const toggleColumnVisibility = (columnKey: keyof Procedure) => {
    setColumns(columns.map(col => 
      col.key === columnKey ? { ...col, visible: !col.visible } : col
    ));
  };
  

  const handleFilterToggle = (filterKey: keyof Procedure) => {
    setFilters(prev => ({ ...prev, [filterKey]: !prev[filterKey] }));
  };

  const clearFilters = () => {
    setSearchAll('');
    setSearchCode('');
    setSearchName('');
    setFilters({ code: false, name: false });
  };

  const handleValidateSelection = () => {
    console.log('Selected procedures:', selectedRows);
    onClose(); // Use prop callback instead of local state
  };

  const SidebarContent = () => (
    <Paper className="h-full bg-gray-50">
      <Tabs value={activeTab} onChange={value => value && setActiveTab(value)} 
      orientation="horizontal" 
      className="h-full">
        <Tabs.List className="bg-white border-b">
          <Tabs.Tab value="filter" className="flex-1">
            Filtre avancé
          </Tabs.Tab>
          <Tabs.Tab value="style" className="flex-1">
            Règles de mise en forme
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="filter" className="p-4 h-full">
          <Stack gap="md">
            <Group justify="space-between">
              <Select
                placeholder="Aucun filtre Enregistré"
                data={[]}
                disabled
                className="flex-1"
              />
            </Group>
            
            <Group gap="xs">
              <ActionIcon variant="light" disabled>
                {/* <Settings size={16} /> */}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-settings-icon lucide-settings"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"/><circle cx="12" cy="12" r="3"/></svg>
              </ActionIcon>
              <ActionIcon variant="light" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className=" lucide lucide-search-icon lucide-search"><path d="m21 21-4.34-4.34"/><circle cx="11" cy="11" r="8"/></svg>
                {/* <Search size={16} /> */}
              </ActionIcon>
              <ActionIcon variant="light" color="red" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-funnel-x-icon lucide-funnel-x"><path d="M12.531 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14v6a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341l.427-.473"/><path d="m16.5 3.5 5 5"/><path d="m21.5 3.5-5 5"/></svg>
                {/* <FilterX size={16} /> */}
              </ActionIcon>
              <ActionIcon variant="light" disabled>
                {/* <Filter size={16} /> */}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-list-filter-icon lucide-list-filter"><path d="M3 6h18"/><path d="M7 12h10"/><path d="M10 18h4"/></svg>
              </ActionIcon>
            </Group>

            <Stack gap="sm">
              {/* {Object.entries(filters).map(([key, value]) => (
                <div key={key}>
                  <Switch
                    label={key === 'code' ? 'Code' : 'Nom'}
                    checked={value}
                    onChange={() => handleFilterToggle(key)}
                  />
                  {key !== 'name' && <Divider my="xs" />}
                </div>
              ))} */}
              {Object.entries(filters).map(([key]) => (
    <Switch
      key={key}
      label={key === 'code' ? 'Code' : 'Nom'}
      checked={filters[key as keyof Procedure]}
      onChange={() => handleFilterToggle(key as keyof Procedure)}
    />
  ))}
            </Stack>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="style" className="p-4">
          <Text size="sm" c="dimmed">
            Règles de mise en forme non implémentées
          </Text>
        </Tabs.Panel>
      </Tabs>
    </Paper>
  );

  return (
      <Modal.Content className="overflow-y-hidden">
            <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                       <Modal.Title>
                         <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                           {/* <FolderSearch size={20} /> */}
                           <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-folder-search-icon lucide-folder-search"><path d="M10.7 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v4.1"/><path d="m21 21-1.9-1.9"/><circle cx="17" cy="17" r="3"/></svg>
                           Liste des procédures
                         </Text>
                         
                       </Modal.Title>
                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                     </Modal.Header>
                       <Modal.Body style={{ padding: '0px' }}>
    <div className="flex h-[600px]">
         {/* Sidebar */}
         {showSidebar && (
           <div className="w-80 border-r">
             <SidebarContent />
           </div>
         )}

         {/* Main Content */}
         <div className="flex-1 flex flex-col">
           {/* Toolbar */}
           <Paper className="border-b p-3">
             <Group justify="space-between" gap="md">
               <Group gap="sm">
                 <ActionIcon
                   variant="light"
                   onClick={() => setShowSidebar(!showSidebar)}
                   className="text-blue-600"
                 >
                   {/* <Filter size={16} /> */}
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-list-filter-icon lucide-list-filter"><path d="M3 6h18"/><path d="M7 12h10"/><path d="M10 18h4"/></svg>
                 </ActionIcon>
                
                 {/* <Search size={16} className="text-gray-500" /> */}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className=" text-gray-500 mr-2 lucide lucide-search-icon lucide-search"><path d="m21 21-4.34-4.34"/><circle cx="11" cy="11" r="8"/></svg>
                 <TextInput
                   placeholder="Rechercher"
                   value={searchAll}
                   onChange={(e) => setSearchAll(e.target.value)}
                   className="w-64"
                 />
               </Group>

               <Group gap="xs">
                 <ActionIcon variant="light" title="Actualiser">
                   {/* <RotateCcw size={16} /> */}
                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rotate-ccw-icon lucide-rotate-ccw"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/></svg>
                 </ActionIcon>
                 <ActionIcon variant="light" title="Export Excel">
                   {/* <FileSpreadsheet size={16} /> */}
                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-spreadsheet-icon lucide-file-spreadsheet"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M8 13h2"/><path d="M14 13h2"/><path d="M8 17h2"/><path d="M14 17h2"/></svg>
                 </ActionIcon>
                 <Menu>
                   <Menu.Target>
                     <ActionIcon variant="light">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-move-vertical-icon lucide-move-vertical"><path d="M12 2v20"/><path d="m8 18 4 4 4-4"/><path d="m8 6 4-4 4 4"/></svg>
                       {/* <MoreVertical size={16} /> */}
                     </ActionIcon>
                   </Menu.Target>
                   <Menu.Dropdown>
                   {columns.map((column: Column) => (
  <Menu.Item
    key={column.key}
    leftSection={column.visible ? <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg> : null}
    onClick={() => toggleColumnVisibility(column.key as keyof Procedure)}
  >
    {column.label}
  </Menu.Item>
))}
                   </Menu.Dropdown>
                 </Menu>
               </Group>
             </Group>
           </Paper>

           {/* Table Container */}
           <ScrollArea className="flex-1">
             <Table striped highlightOnHover withTableBorder withColumnBorders>
               <Table.Thead>
                 <Table.Tr>
                   <Table.Th className=' border-b-2 border-b-[#3799CE]'>
                     <Checkbox
                       checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}
                       indeterminate={selectedRows.length > 0 && selectedRows.length < paginatedData.length}
                       onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                       radius="xs"
                       ml={14}
                     />
                   </Table.Th>
                   {/* {columns.filter(col => col.visible).map((column) => (
                     <Table.Th
                       key={column.key}
                       className="cursor-pointer hover:bg-gray-50"
                       onClick={() => handleSort(column.key)}
                     >
                       <Group gap="xs">
                         <Text>{column.label}</Text>
                         {sortBy === column.key && (
                           <Text size="xs" c={sortOrder === 'asc' ? 'blue' : 'red'}>
                             {sortOrder === 'asc' ? '↑' : '↓'}
                           </Text>
                         )}
                       </Group>
                     </Table.Th>
                   ))} */}
                   {columns.filter(col => col.visible).map((column) => (
                     <Table.Th
                       key={column.key}
                       className="cursor-pointer hover:bg-gray-50 border-b-2 border-b-[#3799CE]"
                       onClick={() => handleSort(column.key as keyof Procedure)}
                      
                     >
                       <Group gap="xs" justify="space-between" mx={12} >
                         <Text>{column.label}</Text>
                         {sortBy === column.key && (
                           <Text size="xs" c={sortOrder === 'asc' ? 'blue' : 'red'}>
                             {sortOrder === 'asc' ? '↑' : '↓'}
                           </Text>
                         )}
                       </Group>
                     </Table.Th>
                   ))}
                 </Table.Tr>
                
                 {/* Search Row */}
                 <Table.Tr className="bg-gray-50">
                   <Table.Td></Table.Td>
                   {columns.filter(col => col.visible).map((column) => (
                     <Table.Td key={column.key}  p={0}>
                       {column.searchable && (
                         <TextInput
                           size="xs"
                           placeholder="Rechercher"
                           value={column.key === 'code' ? searchCode : searchName}
                           onChange={(e) => {
                             if (column.key === 'code') {
                               setSearchCode(e.target.value);                             } else {
                               setSearchName(e.target.value);
                             }
                           }}
                           styles={{
                          input: {
                            borderTopWidth: "0",
                            borderRightWidth: "0",
                            borderLeftWidth: "0",
                            fontSize:"13px",
                            fontWeight:'500',
                            lineHeight:'20.15px'
                          },
                      }}
                         />
                       )}
                     </Table.Td>
                   ))}
                 </Table.Tr>
               </Table.Thead>

               <Table.Tbody>
                 {paginatedData.map((procedure) => (
                   <Table.Tr key={procedure.id}>
                     <Table.Td>
                       <Checkbox
                         checked={selectedRows.includes(procedure.id)}
                         onChange={(e) => handleSelectRow(procedure.id, e.currentTarget.checked)}
                         radius="xs"
                       />
                     </Table.Td>
                     {columns.filter(col => col.visible).map((column) => (
                       <Table.Td key={column.key}>
                        {procedure[column.key as keyof Procedure]}
                       </Table.Td>
                     ))}
                  </Table.Tr>
                 ))}
               </Table.Tbody>
             </Table>
           </ScrollArea>
 <Divider my="sm" />
          {/* Pagination */}
           <Paper className=" p-3">
             <Group justify="space-between" align="center">
               <Group gap="md">
                 <Text size="sm">Page</Text>
                 <Pagination
                   value={currentPage}
                   onChange={setCurrentPage}
                   total={Math.ceil(totalRecords / pageSize)}
                   size="sm"
                 />
               </Group>
              
               <Group gap="md">
                 <Text size="sm">Lignes par Page</Text>
                 <Select
                   value={pageSize.toString()}
                   onChange={(value) => setPageSize(Number(value))}
                   data={['5', '10', '15', '20']}
                   size="sm"
                   w={80}
                 />
             </Group>
              
               <Text size="sm" c="dimmed">
                 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalRecords)} de {totalRecords}
               </Text>
             </Group>
           </Paper>
         </div>
       </div>

       {/* Dialog Actions */}
       <Group justify="flex-end" p="md" className="border-t border-t-[#3799CE] bg-gray-50">
         <Button
           leftSection={<Icon path={mdiPlaylistCheck} size={1} />}
           onClick={handleValidateSelection}
           disabled={selectedRows.length === 0}
         >
           Valider la selection
         </Button>
         <Button
           variant="outline"
           color="red"
           onClick={onClose}
         >
           Annuler
         </Button>
       </Group>    
   
    </Modal.Body>
        </Modal.Content>
  );
};

export default Actes;


//  <Modal
//       opened={opened}
//       onClose={() => setOpened(false)}
//       title={
//         <Group gap="sm">
//           <FolderSearch size={20} />
//           <Text fw={500}>Liste des procédures</Text>
//         </Group>
//       }
//       size="xl"
//       padding={0}
//       className="max-h-[90vh]"
//     >
//       <div className="flex h-[600px]">
//         {/* Sidebar */}
//         {showSidebar && (
//           <div className="w-80 border-r">
//             <SidebarContent />
//           </div>
//         )}

//         {/* Main Content */}
//         <div className="flex-1 flex flex-col">
//           {/* Toolbar */}
//           <Paper className="border-b p-3">
//             <Group justify="space-between" gap="md">
//               <Group gap="sm">
//                 <ActionIcon
//                   variant="light"
//                   onClick={() => setShowSidebar(!showSidebar)}
//                   className="text-blue-600"
//                 >
//                   <Filter size={16} />
//                 </ActionIcon>
                
//                 <Search size={16} className="text-gray-500" />
                
//                 <TextInput
//                   placeholder="Rechercher"
//                   value={searchAll}
//                   onChange={(e) => setSearchAll(e.target.value)}
//                   className="w-64"
//                 />
//               </Group>

//               <Group gap="xs">
//                 <ActionIcon variant="light" title="Actualiser">
//                   <RotateCcw size={16} />
//                 </ActionIcon>
//                 <ActionIcon variant="light" title="Export Excel">
//                   <FileSpreadsheet size={16} />
//                 </ActionIcon>
//                 <Menu>
//                   <Menu.Target>
//                     <ActionIcon variant="light">
//                       <MoreVertical size={16} />
//                     </ActionIcon>
//                   </Menu.Target>
//                   <Menu.Dropdown>
//                     {columns.map((column) => (
//                       <Menu.Item
//                         key={column.key}
//                         leftSection={column.visible ? <Check size={14} /> : null}
//                         onClick={() => toggleColumnVisibility(column.key)}
//                       >
//                         {column.label}
//                       </Menu.Item>
//                     ))}
//                   </Menu.Dropdown>
//                 </Menu>
//               </Group>
//             </Group>
//           </Paper>

//           {/* Table Container */}
//           <ScrollArea className="flex-1">
//             <Table striped highlightOnHover>
//               <Table.Thead>
//                 <Table.Tr>
//                   <Table.Th>
//                     <Checkbox
//                       checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}
//                       indeterminate={selectedRows.length > 0 && selectedRows.length < paginatedData.length}
//                       onChange={(e) => handleSelectAll(e.currentTarget.checked)}
//                     />
//                   </Table.Th>
//                   {columns.filter(col => col.visible).map((column) => (
//                     <Table.Th
//                       key={column.key}
//                       className="cursor-pointer hover:bg-gray-50"
//                       onClick={() => handleSort(column.key)}
//                     >
//                       <Group gap="xs">
//                         <Text>{column.label}</Text>
//                         {sortBy === column.key && (
//                           <Text size="xs" c={sortOrder === 'asc' ? 'blue' : 'red'}>
//                             {sortOrder === 'asc' ? '↑' : '↓'}
//                           </Text>
//                         )}
//                       </Group>
//                     </Table.Th>
//                   ))}
//                 </Table.Tr>
                
//                 {/* Search Row */}
//                 <Table.Tr className="bg-gray-50">
//                   <Table.Td></Table.Td>
//                   {columns.filter(col => col.visible).map((column) => (
//                     <Table.Td key={column.key}>
//                       {column.searchable && (
//                         <TextInput
//                           size="xs"
//                           placeholder="Rechercher"
//                           value={column.key === 'code' ? searchCode : searchName}
//                           onChange={(e) => {
//                             if (column.key === 'code') {
//                               setSearchCode(e.target.value);
//                             } else {
//                               setSearchName(e.target.value);
//                             }
//                           }}
//                         />
//                       )}
//                     </Table.Td>
//                   ))}
//                 </Table.Tr>
//               </Table.Thead>

//               <Table.Tbody>
//                 {paginatedData.map((procedure) => (
//                   <Table.Tr key={procedure.id}>
//                     <Table.Td>
//                       <Checkbox
//                         checked={selectedRows.includes(procedure.id)}
//                         onChange={(e) => handleSelectRow(procedure.id, e.currentTarget.checked)}
//                       />
//                     </Table.Td>
//                     {columns.filter(col => col.visible).map((column) => (
//                       <Table.Td key={column.key}>
//                         {procedure[column.key]}
//                       </Table.Td>
//                     ))}
//                   </Table.Tr>
//                 ))}
//               </Table.Tbody>
//             </Table>
//           </ScrollArea>

//           {/* Pagination */}
//           <Paper className="border-t p-3">
//             <Group justify="space-between" align="center">
//               <Group gap="md">
//                 <Text size="sm">Page</Text>
//                 <Pagination
//                   value={currentPage}
//                   onChange={setCurrentPage}
//                   total={Math.ceil(totalRecords / pageSize)}
//                   size="sm"
//                 />
//               </Group>
              
//               <Group gap="md">
//                 <Text size="sm">Lignes par Page</Text>
//                 <Select
//                   value={pageSize.toString()}
//                   onChange={(value) => setPageSize(Number(value))}
//                   data={['5', '10', '15', '20']}
//                   size="sm"
//                   w={80}
//                 />
//               </Group>
              
//               <Text size="sm" c="dimmed">
//                 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalRecords)} de {totalRecords}
//               </Text>
//             </Group>
//           </Paper>
//         </div>
//       </div>

//       {/* Dialog Actions */}
//       <Group justify="flex-end" p="md" className="border-t bg-gray-50">
//         <Button
//           leftSection={<CheckSquare size={16} />}
//           onClick={handleValidateSelection}
//           disabled={selectedRows.length === 0}
//         >
//           Valider la selection
//         </Button>
//         <Button
//           variant="outline"
//           color="red"
//           onClick={() => setOpened(false)}
//         >
//           Annuler
//         </Button>
//       </Group>
//     </Modal>