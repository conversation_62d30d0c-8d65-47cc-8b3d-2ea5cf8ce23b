import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { notifications } from '@mantine/notifications';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Select,
  NumberInput,
  Radio,
  Tabs,
  Table,
  Pagination,
  Avatar,
  ActionIcon,
  Flex,
  Stack,
  Grid,
  Divider,
  Text,
  Badge
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import {
  IconList,
  IconSearch,
  IconPlus,
  IconX,
  IconCalendar,
  IconEdit,
  IconTrash
} from '@tabler/icons-react';
import { patientFormService } from '@/services/patientFormService';

interface ContractDetail {
  id: string;
  code: string;
  description: string;
  quantity: number;
  price: number;
  isInclusive: boolean;
  amount: number;
}

interface Contract {
  number: string;
  folderNumber: string;
  docDate: Date | null;
  adhesionDate: Date | null;
  beneficiary: { id: string; name: string; type: string; fullName?: string } | null;
  externalTreatingPhysician: { id: string; name: string; specialty?: string } | null;
  plan: { id: string; name: string; description?: string } | null;
  tariff: { id: string; name: string; amount: number } | null;
  city: { id: string; name: string; code?: string } | null;
  operator: { id: string; name: string } | null;
  affiliation: { id: string; name: string; type: string } | null;
  tutor: string;
  startAt: Date | null;
  paymentMode: { id: string; name: string; type: string; value?: string } | null;
  paymentCondition: { id: string; name: string; terms: string } | null;
  organization: { id: string; name: string; type: string } | null;
  payerType: 'P' | 'T';
  billingDetails: {
    renewalNbr: number;
    renewalPeriod: 'DAY' | 'WEEK' | 'MONTH' | 'YEAR';
    billNbr: number;
    billPeriod: 'DAY' | 'WEEK' | 'MONTH' | 'YEAR';
    autoRenew: boolean;
    freeTrial: number;
    automaticCloseLimit: number;
    expiredCycles: number;
  };
  details: ContractDetail[];
}

const Abonnement: React.FC = () => {
  const params = useParams();
  const patientId = params?.id as string;

  const [activeTab, setActiveTab] = useState<string>('details');
  const [loading, setLoading] = useState(false);
  const [subscriptions, setSubscriptions] = useState<Contract[]>([]);
  const [contract, setContract] = useState<Contract>({
    number: '1',
    folderNumber: '',
    docDate: null,
    adhesionDate: null,
    beneficiary: null,
    externalTreatingPhysician: null,
    plan: null,
    tariff: null,
    city: null,
    operator: null,
    affiliation: null,
    tutor: '',
    startAt: null,
    paymentMode: null,
    paymentCondition: null,
    organization: null,
    payerType: 'P',
    billingDetails: {
      renewalNbr: 1,
      renewalPeriod: 'YEAR',
      billNbr: 1,
      billPeriod: 'YEAR',
      autoRenew: true,
      freeTrial: 0,
      automaticCloseLimit: 0,
      expiredCycles: 1
    },
    details: []
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  const periodOptions = [
    { value: 'DAY', label: 'Jour(s)' },
    { value: 'WEEK', label: 'Semaine(s)' },
    { value: 'MONTH', label: 'Mois' },
    { value: 'YEAR', label: 'An(s)' }
  ];

  const paymentModeOptions = [
    { value: 'CASH', label: 'Espèce' },
    { value: 'CHECK', label: 'Chèque' },
    { value: 'NONE', label: 'Aucune' },
    { value: 'DRAFT', label: 'Traite' },
    { value: 'TPE', label: 'TPE' }
  ];

  const cityOptions = [
    { value: 'AGADIR', label: 'AGADIR' },
    { value: 'AIT_BAHA', label: 'AIT BAHA' },
    { value: 'AL_HAJEB', label: 'AL HAJEB' }
  ];

  const organizationOptions = [
    { value: 'AMO', label: 'AMO' },
    { value: 'ATLANTA', label: 'ATLANTA' }
  ];

  const updateContract = (field: string, value: unknown) => {
    setContract(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateBillingDetails = (field: string, value: unknown) => {
    setContract(prev => ({
      ...prev,
      billingDetails: {
        ...prev.billingDetails,
        [field]: value
      }
    }));
  };

  const calculateTotal = () => {
    return contract.details.reduce((sum, detail) => sum + detail.amount, 0);
  };

  // Load patient subscriptions on component mount
  useEffect(() => {
    const loadSubscriptions = async () => {
      if (!patientId) return;

      try {
        setLoading(true);
        console.log(`🔄 Loading subscriptions for patient: ${patientId}`);

        // Note: This would need a subscription endpoint in patientFormService
        // For now, we'll use a placeholder and convert the data
        const patientContracts = await patientFormService.getPatientSubscriptions?.(patientId) || [];

        // Convert PatientContract[] to Contract[] format (simplified conversion)
        const convertedSubscriptions: Contract[] = patientContracts.map(contract => ({
          number: contract.id || '',
          folderNumber: contract.patient,
          docDate: contract.start_date ? new Date(contract.start_date) : null,
          adhesionDate: contract.start_date ? new Date(contract.start_date) : null,
          beneficiary: null,
          externalTreatingPhysician: null,
          plan: null,
          tariff: null,
          city: null,
          operator: null,
          affiliation: null,
          tutor: '',
          startAt: contract.start_date ? new Date(contract.start_date) : null,
          paymentMode: null,
          paymentCondition: null,
          organization: null,
          payerType: 'P' as const,
          billingDetails: {
            renewalNbr: 1,
            renewalPeriod: 'MONTH' as const,
            billNbr: 1,
            billPeriod: 'MONTH' as const,
            autoRenew: false,
            freeTrial: 0,
            automaticCloseLimit: 0,
            expiredCycles: 0,
          },
          details: [],
        }));

        setSubscriptions(convertedSubscriptions);
        console.log('✅ Loaded subscriptions:', convertedSubscriptions.length);
      } catch (error) {
        console.error('❌ Error loading subscriptions:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load subscription data',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    loadSubscriptions();
  }, [patientId]);

  const handleSubmit = async (saveAndQuit: boolean = false) => {
    if (!patientId) return;

    try {
      setLoading(true);
      console.log('🔄 Submitting contract:', contract);

      // Convert Contract to PatientContract format for backend
      const contractData = {
        patient: patientId,
        contract_type: 'subscription',
        start_date: contract.startAt?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        end_date: contract.docDate?.toISOString().split('T')[0],
        terms: `Contract ${contract.number} - ${contract.folderNumber}`,
        status: 'active' as const,
      };

      console.log('🔄 Creating subscription with data:', contractData);
      await patientFormService.createPatientSubscription?.(patientId, contractData);

      notifications.show({
        title: 'Success',
        message: 'Contract saved successfully',
        color: 'green',
      });

      if (saveAndQuit) {
        // Navigate back or close modal
        console.log('Save and quit:', saveAndQuit);
      }
    } catch (error) {
      console.error('❌ Error saving contract:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save contract',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    console.log('Cancelling form');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Paper className="mx-auto max-w-7xl" radius="md" withBorder>
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-md">
          <Group justify="space-between">
            <Title order={3} className="text-white">
              <span>Abonnement N°: </span>
              <span>{contract.number}</span>
            </Title>
            <ActionIcon variant="subtle" size="lg" className="text-white hover:bg-blue-700">
              <IconList size={20} />
            </ActionIcon>
          </Group>
        </div>

        {/* Content */}
        <div className="p-6">
          <form className="space-y-6">
            {/* Top Section */}
            <Grid>
              {/* Left Section - Picture and Form Fields */}
              <Grid.Col span={8}>
                <Grid>
                  <Grid.Col span={2}>
                    <div className="flex justify-center">
                      <Avatar size="xl" className="border-2 border-gray-200" />
                    </div>
                  </Grid.Col>
                  <Grid.Col span={10}>
                    <Stack gap="md">
                      {/* First Row */}
                      <Grid>
                        <Grid.Col span={6}>
                          <TextInput
                            label="N°.Contract"
                            value={contract.number}
                            readOnly
                            required
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <TextInput
                            label="N°.dossier"
                            value={contract.folderNumber}
                            onChange={(e) => updateContract('folderNumber', e.target.value)}
                          />
                        </Grid.Col>
                      </Grid>

                      {/* Second Row */}
                      <Grid>
                        <Grid.Col span={4}>
                          <DateInput
                            label="Date"
                            placeholder="Sélectionner une date"
                            value={contract.docDate}
                            onChange={(date) => updateContract('docDate', date)}
                            required
                            leftSection={<IconCalendar size={16} />}
                          />
                        </Grid.Col>
                        <Grid.Col span={8}>
                          <DateInput
                            label="Date d'adhésion"
                            placeholder="Sélectionner une date"
                            value={contract.adhesionDate}
                            onChange={(date) => updateContract('adhesionDate', date)}
                            required
                            leftSection={<IconCalendar size={16} />}
                          />
                        </Grid.Col>
                      </Grid>

                      {/* Third Row */}
                      <Grid>
                        <Grid.Col span={6}>
                          <Group>
                            <TextInput
                              label="Choisir un patient"
                              placeholder="Nom complet du patient"
                              value={contract.beneficiary?.fullName || ''}
                              readOnly
                              required
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconSearch size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Group>
                            <Select
                              label="Adressé par"
                              placeholder="Sélectionner un médecin"
                              data={[]}
                              searchable
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconPlus size={16} />
                            </ActionIcon>
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconX size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                      </Grid>

                      {/* Fourth Row */}
                      <Grid>
                        <Grid.Col span={6}>
                          <Group>
                            <Select
                              label="Plan"
                              placeholder="Sélectionner un plan"
                              data={[]}
                              searchable
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconPlus size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Select
                            label="Tarification"
                            placeholder="Sélectionner une tarification"
                            data={[]}
                            searchable
                          />
                        </Grid.Col>
                      </Grid>

                      {/* Fifth Row */}
                      <Grid>
                        <Grid.Col span={6}>
                          <Group>
                            <Select
                              label="Ville"
                              placeholder="Sélectionner une ville"
                              data={cityOptions}
                              searchable
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconPlus size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Group>
                            <Select
                              label="Technicien"
                              placeholder="Sélectionner un technicien"
                              data={[]}
                              searchable
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconPlus size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                      </Grid>

                      {/* Sixth Row */}
                      <Grid>
                        <Grid.Col span={6}>
                          <Group>
                            <Select
                              label="Affectation"
                              placeholder="Sélectionner une affectation"
                              data={[]}
                              searchable
                              className="flex-1"
                            />
                            <ActionIcon variant="subtle" className="mt-6">
                              <IconPlus size={16} />
                            </ActionIcon>
                          </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <TextInput
                            label="Tuteur"
                            value={contract.tutor}
                            onChange={(e) => updateContract('tutor', e.target.value)}
                          />
                        </Grid.Col>
                      </Grid>
                    </Stack>
                  </Grid.Col>
                </Grid>
              </Grid.Col>

              {/* Right Section - Billing Details */}
              <Grid.Col span={4}>
                <Divider orientation="vertical" className="h-full mx-4" />
                <Stack gap="md">
                  <DateInput
                    label="Démarre le"
                    placeholder="Sélectionner une date"
                    value={contract.startAt}
                    onChange={(date) => updateContract('startAt', date)}
                    required
                    leftSection={<IconCalendar size={16} />}
                  />

                  <Grid>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Renouveler chaque"
                        value={contract.billingDetails.renewalNbr}
                        onChange={(value) => updateBillingDetails('renewalNbr', value || 1)}
                        min={1}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Select
                        data={periodOptions}
                        value={contract.billingDetails.renewalPeriod}
                        onChange={(value) => updateBillingDetails('renewalPeriod', value)}
                      />
                    </Grid.Col>
                  </Grid>

                  <Grid>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Facturer chaque"
                        value={contract.billingDetails.billNbr}
                        onChange={(value) => updateBillingDetails('billNbr', value || 1)}
                        min={1}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Select
                        data={periodOptions}
                        value={contract.billingDetails.billPeriod}
                        onChange={(value) => updateBillingDetails('billPeriod', value)}
                      />
                    </Grid.Col>
                  </Grid>

                  <Grid>
                    <Grid.Col span={6}>
                      <Select
                        label="Mode de paiement"
                        data={paymentModeOptions}
                        value={contract.paymentMode?.value}
                        onChange={(value) => updateContract('paymentMode', { value })}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Select
                        label="Condition de paiement"
                        data={[]}
                        placeholder="Sélectionner"
                      />
                    </Grid.Col>
                  </Grid>

                  <Grid>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Essai gratuit(jours)"
                        value={contract.billingDetails.freeTrial}
                        onChange={(value) => updateBillingDetails('freeTrial', value || 0)}
                        min={0}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Résiliation automatiquement aprés (jours)"
                        value={contract.billingDetails.automaticCloseLimit}
                        onChange={(value) => updateBillingDetails('automaticCloseLimit', value || 0)}
                        min={0}
                      />
                    </Grid.Col>
                  </Grid>

                  {/* Payment Type Radio Group */}
                  <div>
                    <Text size="sm" fw={500} mb="xs">Payeur</Text>
                    <Radio.Group
                      value={contract.payerType}
                      onChange={(value) => updateContract('payerType', value)}
                    >
                      <Group>
                        <Radio value="P" label="Patient" />
                        <Radio value="T" label="Tiers payant" />
                      </Group>
                    </Radio.Group>
                  </div>

                  {/* Billing Cycle Radio Group */}
                  <div>
                    <Text size="sm" fw={500} mb="xs">Cycle d&apos;abonnement</Text>
                    <Radio.Group
                      value={contract.billingDetails.autoRenew.toString()}
                      onChange={(value) => updateBillingDetails('autoRenew', value === 'true')}
                    >
                      <Stack gap="xs">
                        <Radio value="true" label="Renouvellement automatique jusqu'à annulation" />
                        <Radio value="false" label="Expire après des cycles" />
                      </Stack>
                    </Radio.Group>
                  </div>

                  <Grid>
                    <Grid.Col span={6}>
                      <Select
                        label="Organisme"
                        data={organizationOptions}
                        disabled={contract.payerType !== 'T'}
                        value={contract.organization?.name}
                        onChange={(value) => updateContract('organization', { name: value })}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Nombre de cycle"
                        value={contract.billingDetails.expiredCycles}
                        onChange={(value) => updateBillingDetails('expiredCycles', value || 1)}
                        min={1}
                        disabled={contract.billingDetails.autoRenew}
                      />
                    </Grid.Col>
                  </Grid>
                </Stack>
              </Grid.Col>
            </Grid>

            {/* Bottom Section - Tabs */}
            <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'details')}>
              <Tabs.List>
                <Tabs.Tab value="details">Détails</Tabs.Tab>
                <Tabs.Tab value="comments" disabled>Commentaires</Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="details" pt="md">
                <Paper withBorder>
                  <Table>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>Code</Table.Th>
                        <Table.Th>Description</Table.Th>
                        <Table.Th className="text-right">Qté</Table.Th>
                        <Table.Th className="text-right">Prix</Table.Th>
                        <Table.Th className="text-right">Forfaitaire</Table.Th>
                        <Table.Th className="text-right">Montant</Table.Th>
                        <Table.Th></Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {contract.details.length === 0 ? (
                        <Table.Tr>
                          <Table.Td colSpan={7} className="text-center py-8 text-gray-500">
                            Aucun élément trouvé.
                          </Table.Td>
                        </Table.Tr>
                      ) : (
                        contract.details.map((detail) => (
                          <Table.Tr key={detail.id}>
                            <Table.Td>{detail.code}</Table.Td>
                            <Table.Td>{detail.description}</Table.Td>
                            <Table.Td className="text-right">{detail.quantity}</Table.Td>
                            <Table.Td className="text-right">{detail.price.toFixed(2)}</Table.Td>
                            <Table.Td className="text-right">
                              <Badge color={detail.isInclusive ? 'green' : 'gray'}>
                                {detail.isInclusive ? 'Oui' : 'Non'}
                              </Badge>
                            </Table.Td>
                            <Table.Td className="text-right">{detail.amount.toFixed(2)}</Table.Td>
                            <Table.Td>
                              <Group gap="xs">
                                <ActionIcon size="sm" variant="subtle">
                                  <IconEdit size={14} />
                                </ActionIcon>
                                <ActionIcon size="sm" variant="subtle" color="red">
                                  <IconTrash size={14} />
                                </ActionIcon>
                              </Group>
                            </Table.Td>
                          </Table.Tr>
                        ))
                      )}
                    </Table.Tbody>
                  </Table>

                  {/* Pagination */}
                  <div className="p-4 border-t">
                    <Group justify="space-between">
                      <Group>
                        <Text size="sm">Page</Text>
                        <Select
                          data={[{ value: '1', label: '1' }]}
                          value="1"
                          w={60}
                        />
                        <Text size="sm">Lignes par Page</Text>
                        <Select
                          data={[
                            { value: '5', label: '5' },
                            { value: '10', label: '10' },
                            { value: '20', label: '20' },
                            { value: '50', label: '50' }
                          ]}
                          value={itemsPerPage.toString()}
                          onChange={(value) => setItemsPerPage(Number(value) || 5)}
                          w={80}
                        />
                      </Group>
                      <Text size="sm">0 - 0 de 0</Text>
                    </Group>
                  </div>

                  {/* Total */}
                  <div className="p-4 border-t bg-gray-50">
                    <Group justify="end">
                      <Text fw={500}>Total :</Text>
                      <Text fw={700}>{calculateTotal().toFixed(2)}</Text>
                    </Group>
                  </div>
                </Paper>
              </Tabs.Panel>

              <Tabs.Panel value="comments" pt="md">
                <Paper withBorder p="md">
                  <Text c="dimmed">Section commentaires non disponible</Text>
                </Paper>
              </Tabs.Panel>
            </Tabs>
          </form>
        </div>

        {/* Actions */}
        <div className="p-4 border-t bg-gray-50 rounded-b-md">
          <Group justify="end">
            <Button variant="outline" color="red" onClick={handleCancel}>
              Annuler
            </Button>
            <Button onClick={() => handleSubmit(true)}>
              Enregistrer et quitter
            </Button>
            <Button variant="filled" onClick={() => handleSubmit()}>
              Enregistrer
            </Button>
          </Group>
        </div>
      </Paper>
    </div>
  );
};

export default Abonnement;