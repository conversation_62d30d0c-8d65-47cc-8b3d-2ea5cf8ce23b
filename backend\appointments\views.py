"""
Views for the appointments app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Appointment, DentistryAppointment, AppointmentR<PERSON>inder, Patient<PERSON>ist, ActiveVisit, PresenceList, HistoryJournal
from .serializers import (
    AppointmentSerializer,
    DentistryAppointmentSerializer,
    AppointmentReminderSerializer,
    AppointmentCreateSerializer,
    AppointmentListSerializer,
    PatientListSerializer,
    ActiveVisitSerializer,
    PresenceListSerializer,
    HistoryJournalSerializer
)


class AppointmentViewSet(viewsets.ModelViewSet):
    """ViewSet for Appointment model."""
    
    queryset = Appointment.objects.all()  # type: ignore
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_serializer_class(self) -> type[AppointmentCreateSerializer] | type[AppointmentListSerializer] | type[AppointmentSerializer]:  # type: ignore
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return AppointmentCreateSerializer
        elif self.action == 'list':
            return AppointmentListSerializer
        return AppointmentSerializer

    def create(self, request, *args, **kwargs):
        """Create appointment and return response with full serializer."""
        # Use AppointmentCreateSerializer for input validation and creation
        create_serializer = AppointmentCreateSerializer(data=request.data, context={'request': request})
        create_serializer.is_valid(raise_exception=True)
        appointment = create_serializer.save()

        # Use AppointmentSerializer for response (includes SerializerMethodFields)
        response_serializer = AppointmentSerializer(appointment, context={'request': request})

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Include doctor_or_assistant in select_related if it exists
        try:
            # Check if doctor_or_assistant field exists
            Appointment._meta.get_field('doctor_or_assistant')  # type: ignore
            queryset = Appointment.objects.select_related('patient', 'doctor', 'doctor_or_assistant').all()  # type: ignore
        except:
            # Fallback if field doesn't exist
            queryset = Appointment.objects.select_related('patient', 'doctor').all()  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by doctor
        doctor_id = self.request.query_params.get('doctor', None)
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        
        if start_date:
            try:
                start_date = datetime.strptime(str(start_date), '%Y-%m-%d').date()  # type: ignore
                queryset = queryset.filter(appointment_date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date = datetime.strptime(str(end_date), '%Y-%m-%d').date()  # type: ignore
                queryset = queryset.filter(appointment_date__lte=end_date)
            except ValueError:
                pass
        
        # Search
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(  # type: ignore
                Q(title__icontains=search) |  # type: ignore
                Q(patient__first_name__icontains=search) |  # type: ignore
                Q(patient__last_name__icontains=search) |  # type: ignore
                Q(doctor__first_name__icontains=search) |  # type: ignore
                Q(doctor__last_name__icontains=search) |  # type: ignore
                Q(description__icontains=search)  # type: ignore
            )
        
        return queryset.order_by('appointment_date', 'appointment_time')
    
    @action(detail=False, methods=['get'])
    def today(self, request):
        """Get today's appointments."""
        today = timezone.now().date()
        appointments = self.get_queryset().filter(appointment_date=today)
        serializer = AppointmentListSerializer(appointments, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming appointments."""
        today = timezone.now().date()
        upcoming_appointments = self.get_queryset().filter(
            appointment_date__gte=today,
            status__in=['scheduled', 'confirmed']
        )
        serializer = AppointmentListSerializer(upcoming_appointments, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def calendar(self, request):
        """Get appointments for calendar view."""
        # Get date range for calendar (default to current month)
        year = request.query_params.get('year', timezone.now().year)
        month = request.query_params.get('month', timezone.now().month)
        
        try:
            year = int(year)
            month = int(month)
            start_date = datetime(year, month, 1).date()
            if month == 12:
                end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)
        except (ValueError, TypeError):
            # Default to current month
            today = timezone.now().date()
            start_date = today.replace(day=1)
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        appointments = self.get_queryset().filter(
            appointment_date__gte=start_date,
            appointment_date__lte=end_date
        )
        
        serializer = AppointmentListSerializer(appointments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm an appointment."""
        appointment = self.get_object()
        appointment.status = 'confirmed'
        appointment.save()
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel an appointment."""
        appointment = self.get_object()
        appointment.status = 'cancelled'
        appointment.save()
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark appointment as completed."""
        appointment = self.get_object()
        appointment.status = 'completed'
        appointment.save()
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)


class DentistryAppointmentViewSet(viewsets.ModelViewSet):
    """ViewSet for DentistryAppointment model."""
    
    queryset = DentistryAppointment.objects.all()  # type: ignore
    serializer_class = DentistryAppointmentSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Type ignore for basedpyright not recognizing objects attribute
        queryset = DentistryAppointment.objects.select_related('appointment', 'appointment__patient').all()  # type: ignore
        
        # Filter by procedure type
        procedure_type = self.request.query_params.get('procedure_type', None)
        if procedure_type:
            queryset = queryset.filter(procedure_type=procedure_type)
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(appointment__patient_id=patient_id)
        
        return queryset.order_by('-created_at')


class AppointmentReminderViewSet(viewsets.ModelViewSet):
    """ViewSet for AppointmentReminder model."""
    
    queryset = AppointmentReminder.objects.all()  # type: ignore
    serializer_class = AppointmentReminderSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = AppointmentReminder.objects.select_related('appointment', 'appointment__patient').all()
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by reminder type
        reminder_type = self.request.query_params.get('reminder_type', None)
        if reminder_type:
            queryset = queryset.filter(reminder_type=reminder_type)
        
        return queryset.order_by('send_at')
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending reminders."""
        pending_reminders = self.get_queryset().filter(status='pending')
        serializer = self.get_serializer(pending_reminders, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_sent(self, request, pk=None):
        """Mark reminder as sent."""
        reminder = self.get_object()
        reminder.status = 'sent'
        reminder.sent_at = timezone.now()
        reminder.save()
        serializer = self.get_serializer(reminder)
        return Response(serializer.data)


class PatientListViewSet(viewsets.ModelViewSet):
    """ViewSet for PatientList model."""
    
    queryset = PatientList.objects.all()  # type: ignore
    serializer_class = PatientListSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = PatientList.objects.prefetch_related('patients', 'assigned_staff').all()
        
        # Filter by list type
        list_type = self.request.query_params.get('list_type', None)
        if list_type:
            queryset = queryset.filter(list_type=list_type)
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=str(is_active).lower() == 'true')  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patients__id=patient_id)
        
        return queryset.order_by('name')
    
    @action(detail=True, methods=['post'])
    def add_patient(self, request, pk=None):
        """Add a patient to the list."""
        patient_list = self.get_object()
        patient_id = request.data.get('patient_id')
        
        if not patient_id:
            return Response({'error': 'patient_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            patient_list.patients.add(patient_id)
            serializer = self.get_serializer(patient_list)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def remove_patient(self, request, pk=None):
        """Remove a patient from the list."""
        patient_list = self.get_object()
        patient_id = request.data.get('patient_id')
        
        if not patient_id:
            return Response({'error': 'patient_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            patient_list.patients.remove(patient_id)
            serializer = self.get_serializer(patient_list)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ActiveVisitViewSet(viewsets.ModelViewSet):
    """ViewSet for ActiveVisit model."""
    
    queryset = ActiveVisit.objects.select_related('patient', 'appointment', 'assigned_staff').all()
    serializer_class = ActiveVisitSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = ActiveVisit.objects.select_related('patient', 'appointment', 'assigned_staff').all()
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by staff
        staff_id = self.request.query_params.get('staff', None)
        if staff_id:
            queryset = queryset.filter(assigned_staff_id=staff_id)
        
        # Filter by status
        visit_status = self.request.query_params.get('visit_status', None)
        if visit_status:
            queryset = queryset.filter(visit_status=visit_status)
        
        # Filter by active visits only
        active_only = self.request.query_params.get('active_only', None)
        if active_only is not None:
            if active_only.lower() == 'true':
                queryset = queryset.filter(visit_status__in=['checked_in', 'in_waiting', 'being_seen', 'in_procedure'])
        
        return queryset.order_by('-check_in_time')
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update the visit status."""
        active_visit = self.get_object()
        new_status = request.data.get('status')
        
        if not new_status:
            return Response({'error': 'status is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate status
        valid_statuses = [choice[0] for choice in ActiveVisit.VISIT_STATUS_CHOICES]
        if new_status not in valid_statuses:
            return Response({'error': f'Invalid status. Valid options: {valid_statuses}'}, status=status.HTTP_400_BAD_REQUEST)
        
        active_visit.visit_status = new_status
        active_visit.save()
        serializer = self.get_serializer(active_visit)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get all current active visits."""
        current_visits = self.get_queryset().filter(visit_status__in=['checked_in', 'in_waiting', 'being_seen', 'in_procedure'])
        serializer = self.get_serializer(current_visits, many=True)
        return Response(serializer.data)


class PresenceListViewSet(viewsets.ModelViewSet):
    """ViewSet for PresenceList model."""
    
    queryset = PresenceList.objects.select_related('staff_member').all()
    serializer_class = PresenceListSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = PresenceList.objects.select_related('staff_member').all()
        
        # Filter by staff member
        staff_id = self.request.query_params.get('staff', None)
        if staff_id:
            queryset = queryset.filter(staff_member_id=staff_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by current presence only
        current_only = self.request.query_params.get('current_only', None)
        if current_only is not None:
            if str(current_only).lower() == 'true':  # type: ignore
                queryset = queryset.filter(end_time__isnull=True)
        
        return queryset.order_by('-start_time')
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current presence status for all staff."""
        current_presence = self.get_queryset().filter(end_time__isnull=True)
        serializer = self.get_serializer(current_presence, many=True)
        return Response(serializer.data)


class HistoryJournalViewSet(viewsets.ModelViewSet):
    """ViewSet for HistoryJournal model."""
    
    queryset = HistoryJournal.objects.select_related('patient', 'appointment', 'created_by').prefetch_related('related_staff').all()
    serializer_class = HistoryJournalSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = HistoryJournal.objects.select_related('patient', 'appointment', 'created_by').prefetch_related('related_staff').all()
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by entry type
        entry_type = self.request.query_params.get('entry_type', None)
        if entry_type:
            queryset = queryset.filter(entry_type=entry_type)
        
        # Filter by category
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category=category)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        
        if start_date:
            try:
                start_date = datetime.strptime(str(start_date), '%Y-%m-%d')  # type: ignore
                queryset = queryset.filter(event_date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date = datetime.strptime(str(end_date), '%Y-%m-%d')  # type: ignore
                queryset = queryset.filter(event_date__lte=end_date)
            except ValueError:
                pass
        
        return queryset.order_by('-event_date')
    
    @action(detail=False, methods=['get'])
    def patient_history(self, request):
        """Get complete history for a specific patient."""
        patient_id = request.query_params.get('patient_id')
        if not patient_id:
            return Response({'error': 'patient_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        patient_history = self.get_queryset().filter(patient_id=patient_id)
        serializer = self.get_serializer(patient_history, many=True)
        return Response(serializer.data)


# Simple waiting list endpoint as fallback
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def simple_waiting_list(request):
    """
    Simple waiting list endpoint that returns appointments marked as waiting list.
    """
    try:
        # Get appointments that are marked as waiting list
        waiting_appointments = Appointment.objects.filter(is_waiting_list=True)  # type: ignore

        # Apply doctor filter if provided
        doctor_id = request.GET.get('doctor_id')
        if doctor_id:
            waiting_appointments = waiting_appointments.filter(doctor_assigned=doctor_id)  # type: ignore

        serializer = AppointmentListSerializer(waiting_appointments, many=True)

        return Response({
            'waiting_list': serializer.data,
            'count': waiting_appointments.count()
        })
    except Exception as e:
        return Response({
            'waiting_list': [],
            'count': 0,
            'error': str(e)
        }, status=status.HTTP_200_OK)  # Return 200 to avoid breaking frontend