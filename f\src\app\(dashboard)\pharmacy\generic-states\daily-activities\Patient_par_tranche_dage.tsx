'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Radio,
  Menu,
  Alert
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiAccountGroup,
  mdiFilterMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface AgeRangeColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface AgeRangeState {
  name: string;
  label: string;
  type: 'all_ages' | 'pediatric' | 'elderly';
  deactivated?: boolean;
}

interface AgeRangeFilter {
  showAdvancedFilter: boolean;
}

interface PatientParTrancheDAgeProps {
  loading?: boolean;
  onRangeChange?: (range: AgeRangeState) => void;
  onFilterChange?: (filter: AgeRangeFilter) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const Patient_par_tranche_dage: React.FC<PatientParTrancheDAgeProps> = ({
  loading = false,
  onRangeChange,
  onFilterChange,
  onExport,
  onPrint,
  onSort
}) => {
  // États locaux
  const [selectedRange, setSelectedRange] = useState<AgeRangeState>({
    name: 'all_ages',
    label: 'Tous Ages',
    type: 'all_ages'
  });

  const [filter, setFilter] = useState<AgeRangeFilter>({
    showAdvancedFilter: true
  });

  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: AgeRangeColumn[] = [
    { id: 'total', label: 'Total', sortable: true }
  ];

  // États disponibles
  const ageRanges: AgeRangeState[] = [
    { name: 'all_ages', label: 'Tous Ages', type: 'all_ages' },
    { name: 'pediatric', label: 'Pédiatrique (≤18)', type: 'pediatric' },
    { name: 'elderly', label: 'Personnes Agées (>55)', type: 'elderly' }
  ];

  // Données simulées des tranches d'âge
  const ageRangeData = [
    { range: '00-10', total: 1 },
    { range: '10-20', total: 8 },
    { range: '20-30', total: 28 },
    { range: '30-40', total: 31 },
    { range: '330-340', total: 1 },
    { range: '40-50', total: 14 },
    { range: '50-60', total: 10 },
    { range: '60-70', total: 2 }
  ];

  const totalPatients = ageRangeData.reduce((sum, item) => sum + item.total, 0);

  // Gestionnaires d'événements
  const handleRangeChange = (range: AgeRangeState) => {
    setSelectedRange(range);
    onRangeChange?.(range);
  };

  const handleFilterChange = (newFilter: Partial<AgeRangeFilter>) => {
    const updatedFilter = { ...filter, ...newFilter };
    setFilter(updatedFilter);
    onFilterChange?.(updatedFilter);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiAccountGroup} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Patient par tranche d'âge</Text>
          </Group>

          {/* Bouton filtre avancé */}
          {filter.showAdvancedFilter && (
            <ActionIcon
              variant="subtle"
              onClick={() => handleFilterChange({ showAdvancedFilter: !filter.showAdvancedFilter })}
              title="Filtre avancé"
            >
              <Icon path={mdiFilterMultiple} size={0.8} />
            </ActionIcon>
          )}
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Box style={{ marginLeft: 12 }}>
          <Text size="sm" fw={500} mb="xs">Source de données</Text>
          <Radio.Group
            value={selectedRange.name}
            onChange={(value) => {
              const range = ageRanges.find(r => r.name === value);
              if (range) handleRangeChange(range);
            }}
          >
            <Group>
              {ageRanges.map((range) => (
                <Radio
                  key={range.name}
                  value={range.name}
                  label={range.label}
                  disabled={range.deactivated}
                />
              ))}
            </Group>
          </Radio.Group>
        </Box>
      </Paper>

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <Menu shadow="md" width={250}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Format">
                      <Icon path={mdiTableEdit} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatLetterMatches} size={0.8} />}
                    >
                      Format de cellule
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatColorHighlight} size={0.8} />}
                    >
                      La mise en forme conditionnelle
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Champs">
                  <Icon path={mdiTableSettings} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Tableau des données */}
            <ScrollArea style={{ height: 'calc(100vh - 300px)' }}>
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    {/* Colonne vide pour les en-têtes de lignes */}
                    <Table.Th
                      style={{
                        minWidth: 178,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Tranche d'âge (interval)</Text>
                    </Table.Th>
                    {columns.map((column) => (
                      <Table.Th
                        key={column.id}
                        style={{
                          minWidth: 100,
                          backgroundColor: '#f8f9fa',
                          cursor: column.sortable ? 'pointer' : 'default'
                        }}
                        onClick={() => column.sortable && handleSort(column.id)}
                      >
                        <Group gap="xs" justify="space-between">
                          <Text size="sm" fw={500}>{column.label}</Text>
                          {column.sortable && (
                            <Icon
                              path={getSortIcon(column.id) || mdiArrowUp}
                              size={0.6}
                              style={{
                                opacity: getSortIcon(column.id) ? 1 : 0.3
                              }}
                            />
                          )}
                        </Group>
                      </Table.Th>
                    ))}
                    {/* Colonnes vides supplémentaires */}
                    {Array.from({ length: 10 }, (_, index) => (
                      <Table.Th
                        key={`empty-col-${index}`}
                        style={{
                          minWidth: 100,
                          backgroundColor: '#f8f9fa'
                        }}
                      />
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {/* Lignes des tranches d'âge */}
                  {ageRangeData.map((item, index) => (
                    <Table.Tr key={item.range}>
                      <Table.Td style={{ fontWeight: 'bold' }}>
                        <Group gap="xs" justify="space-between">
                          <Text fw={500}>{item.range}</Text>
                          <Icon
                            path={getSortIcon('age_range') || mdiArrowUp}
                            size={0.6}
                            style={{
                              opacity: getSortIcon('age_range') ? 1 : 0.3
                            }}
                          />
                        </Group>
                      </Table.Td>
                      <Table.Td style={{ textAlign: 'center', fontWeight: 'bold' }}>
                        <Text fw={500}>{item.total}</Text>
                      </Table.Td>
                      {/* Cellules vides */}
                      {Array.from({ length: 10 }, (_, cellIndex) => (
                        <Table.Td key={`empty-${index}-${cellIndex}`} />
                      ))}
                    </Table.Tr>
                  ))}

                  {/* Ligne Total */}
                  <Table.Tr style={{ backgroundColor: '#e8f5e8' }}>
                    <Table.Td style={{ fontWeight: 'bold' }}>
                      <Group gap="xs" justify="space-between">
                        <Text fw={500}>Total</Text>
                        <Icon
                          path={getSortIcon('total') || mdiArrowUp}
                          size={0.6}
                          style={{
                            opacity: getSortIcon('total') ? 1 : 0.3
                          }}
                        />
                      </Group>
                    </Table.Td>
                    <Table.Td style={{ textAlign: 'center', fontWeight: 'bold' }}>
                      <Text fw={500}>{totalPatients}</Text>
                    </Table.Td>
                    {/* Cellules vides */}
                    {Array.from({ length: 10 }, (_, index) => (
                      <Table.Td key={`empty-total-${index}`} />
                    ))}
                  </Table.Tr>

                  {/* Lignes vides pour remplir l'espace */}
                  {Array.from({ length: 10 }, (_, index) => (
                    <Table.Tr key={`empty-row-${index}`}>
                      {Array.from({ length: 12 }, (_, cellIndex) => (
                        <Table.Td key={cellIndex} style={{ height: 30 }} />
                      ))}
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Message d'état vide si aucune donnée */}
            {ageRangeData.length === 0 && (
              <Box
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1
                }}
              >
                <Alert
                  icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                  title="Aucune donnée disponible"
                  color="gray"
                  variant="light"
                  style={{ maxWidth: 400 }}
                >
                  <Text size="sm" c="dimmed">
                    Aucun patient trouvé pour la tranche d'âge sélectionnée.
                    Veuillez vérifier les filtres ou sélectionner une autre tranche d'âge.
                  </Text>
                </Alert>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Paper>
  );
};
