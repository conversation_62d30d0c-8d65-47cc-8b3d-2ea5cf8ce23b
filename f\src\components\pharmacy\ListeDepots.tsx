'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,

  Select,
  ActionIcon,
  
  Stack,
  Text,
 
  Textarea,
 
  Modal,

  Pagination,
  Checkbox,
  Container,
  Box,
} from '@mantine/core';

import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,

  IconTrash,
  IconFileText,

  IconBuilding,
  IconEdit,
  IconRefresh,
  IconDots,
  IconMenu2,
} from '@tabler/icons-react';

interface Depot {
  id: string;
  nom: string;
  description?: string;
  adresse?: string;
  responsable?: string;
  telephone?: string;
  email?: string;
  status: 'active' | 'inactive';
  dateCreation: Date;
  mouvementStock?: boolean;
  bonRetour?: boolean;
  inventory?: boolean;
  warehouseExchange?: boolean;
  consignation?: boolean;
  bonDeposition?: boolean;
  bonReception?: boolean;
  transformation?: boolean;
  retourConsignation?: boolean;
  depotParDefaut?: boolean;
}

export default function ListeDepotsPage() {
  const [depots, setDepots] = useState<Depot[]>([
    {
      id: '1',
      nom: 'Dépôt 1',
      description: 'Dépôt principal',
      adresse: '123 Rue de la Pharmacie',
      responsable: 'Jean Dupont',
      telephone: '01 23 45 67 89',
      email: '<EMAIL>',
      status: 'active',
      dateCreation: new Date('2022-01-15'),
    },
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [selectedDepots, setSelectedDepots] = useState<string[]>([]);
  const [opened, { open, close }] = useDisclosure(false);
  const [editingDepot, setEditingDepot] = useState<Depot | null>(null);

  const form = useForm({
    initialValues: {
      nom: '',
      description: '',
      adresse: '',
      responsable: '',
      telephone: '',
      email: '',
      status: 'active' as 'active' | 'inactive',
      mouvementStock: false,
      bonRetour: false,
      inventory: false,
      warehouseExchange: false,
      consignation: false,
      bonDeposition: false,
      bonReception: false,
      transformation: false,
      retourConsignation: false,
      depotParDefaut: false,
    },
  });

  const filteredDepots = depots.filter(depot =>
    depot.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (depot.description && depot.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (depot.responsable && depot.responsable.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const totalPages = Math.ceil(filteredDepots.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentDepots = filteredDepots.slice(startIndex, endIndex);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDepots(currentDepots.map(depot => depot.id));
    } else {
      setSelectedDepots([]);
    }
  };

  const handleSelectDepot = (depotId: string, checked: boolean) => {
    if (checked) {
      setSelectedDepots(prev => [...prev, depotId]);
    } else {
      setSelectedDepots(prev => prev.filter(id => id !== depotId));
    }
  };

  const handleAddDepot = () => {
    setEditingDepot(null);
    form.reset();
    open();
  };

  const handleEditDepot = (depot: Depot) => {
    setEditingDepot(depot);
    form.setValues({
      nom: depot.nom,
      description: depot.description || '',
      adresse: depot.adresse || '',
      responsable: depot.responsable || '',
      telephone: depot.telephone || '',
      email: depot.email || '',
      status: depot.status,
      mouvementStock: depot.mouvementStock || false,
      bonRetour: depot.bonRetour || false,
      inventory: depot.inventory || false,
      warehouseExchange: depot.warehouseExchange || false,
      consignation: depot.consignation || false,
      bonDeposition: depot.bonDeposition || false,
      bonReception: depot.bonReception || false,
      transformation: depot.transformation || false,
      retourConsignation: depot.retourConsignation || false,
      depotParDefaut: depot.depotParDefaut || false,
    });
    open();
  };

  const handleDeleteDepot = (depotId: string) => {
    setDepots(prev => prev.filter(depot => depot.id !== depotId));
    notifications.show({
      title: 'Succès',
      message: 'Dépôt supprimé avec succès',
      color: 'green',
    });
  };

  const handleSubmit = (values: typeof form.values) => {
    if (editingDepot) {
      // Modifier un dépôt existant
      setDepots(prev => prev.map(depot => 
        depot.id === editingDepot.id 
          ? { ...depot, ...values }
          : depot
      ));
      notifications.show({
        title: 'Succès',
        message: 'Dépôt modifié avec succès',
        color: 'green',
      });
    } else {
      // Ajouter un nouveau dépôt
      const newDepot: Depot = {
        id: Date.now().toString(),
        ...values,
        dateCreation: new Date(),
      };
      setDepots(prev => [...prev, newDepot]);
      notifications.show({
        title: 'Succès',
        message: 'Dépôt ajouté avec succès',
        color: 'green',
      });
    }
    close();
    form.reset();
  };

  return (
    <Container fluid p="md">
      <Paper shadow="xs" p="md" withBorder>
        {/* Header */}
        <Group justify="space-between" mb="md">
          <Group>
            <IconBuilding size={24} className="text-blue-600" />
            <Title order={2} className="text-gray-800">
              Liste des dépôts
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleAddDepot}
            color="blue"
          >
            Dépôt
          </Button>
        </Group>

        {/* Toolbar */}
        <Group justify="space-between" mb="md">
          <Group>
            <ActionIcon variant="subtle" size="lg">
              <IconMenu2 size={16} />
            </ActionIcon>
            <TextInput
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
              style={{ width: 300 }}
            />
          </Group>
          <Group>
            <ActionIcon variant="subtle" size="lg">
              <IconRefresh size={16} />
            </ActionIcon>
            <ActionIcon variant="subtle" size="lg">
              <IconFileText size={16} />
            </ActionIcon>
            <ActionIcon variant="subtle" size="lg">
              <IconDots size={16} />
            </ActionIcon>
          </Group>
        </Group>

        {/* Table */}
        <Box style={{ border: '1px solid #e9ecef', borderRadius: '4px' }}>
          {/* Table Header */}
          <Group p="sm" style={{ backgroundColor: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>
            <Checkbox
              checked={selectedDepots.length === currentDepots.length && currentDepots.length > 0}
              indeterminate={selectedDepots.length > 0 && selectedDepots.length < currentDepots.length}
              onChange={(event) => handleSelectAll(event.currentTarget.checked)}
            />
            <Text fw={500} style={{ flex: 1 }}>
              Nom
            </Text>
          </Group>

          {/* Search Row */}
          <Group p="sm" style={{ backgroundColor: '#f1f3f4', borderBottom: '1px solid #e9ecef' }}>
            <Box style={{ width: 40 }}></Box>
            <TextInput
              placeholder="Rechercher"
              style={{ flex: 1 }}
              variant="unstyled"
              size="sm"
            />
          </Group>

          {/* Table Body */}
          {currentDepots.length === 0 ? (
            <Box p="xl" style={{ textAlign: 'center' }}>
              <Text c="dimmed">Aucun dépôt trouvé</Text>
            </Box>
          ) : (
            currentDepots.map((depot) => (
              <Group
                key={depot.id}
                p="sm"
                style={{ 
                  borderBottom: '1px solid #e9ecef',
                  '&:hover': {
                    backgroundColor: '#f8f9fa',
                  }
                }}
              >
                <Checkbox
                  checked={selectedDepots.includes(depot.id)}
                  onChange={(event) => handleSelectDepot(depot.id, event.currentTarget.checked)}
                />
                <Text style={{ flex: 1 }}>
                  {depot.nom}
                </Text>
                <Group gap="xs">
                  <ActionIcon
                    variant="subtle"
                    size="sm"
                    onClick={() => handleEditDepot(depot)}
                  >
                    <IconEdit size={14} />
                  </ActionIcon>
                  <ActionIcon
                    variant="subtle"
                    size="sm"
                    color="red"
                    onClick={() => handleDeleteDepot(depot.id)}
                  >
                    <IconTrash size={14} />
                  </ActionIcon>
                </Group>
              </Group>
            ))
          )}
        </Box>

        {/* Pagination */}
        <Group justify="space-between" mt="md">
          <Group>
            <Text size="sm">Page</Text>
            <Select
              size="sm"
              w={60}
              data={Array.from({ length: totalPages }, (_, i) => (i + 1).toString())}
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(parseInt(value || '1'))}
            />
            <Text size="sm">Lignes par Page</Text>
            <Select
              size="sm"
              w={60}
              data={['15', '25', '50', '100']}
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(parseInt(value || '15'))}
            />
            <Text size="sm">
              {startIndex + 1} - {Math.min(endIndex, filteredDepots.length)} de {filteredDepots.length}
            </Text>
          </Group>
          <Pagination 
            total={totalPages} 
            value={currentPage} 
            onChange={setCurrentPage} 
            size="sm" 
          />
        </Group>
      </Paper>

      {/* Add/Edit Modal */}
      <Modal
        opened={opened}
        onClose={close}
        title={
          <Group>
            <IconBuilding size={20} className="text-white" />
            <Text c="white" fw={500}>Dépôt</Text>
          </Group>
        }
        size="lg"
        styles={{
          header: {
            backgroundColor: '#339af0',
            color: 'white',
          },
          title: {
            color: 'white',
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
        }}
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Nom"
              placeholder=""
              {...form.getInputProps('nom')}
              required
              styles={{
                label: {
                  fontWeight: 500,
                  marginBottom: 4,
                },
                input: {
                  borderBottom: '2px solid #339af0',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderRadius: 0,
                  paddingLeft: 0,
                  backgroundColor: 'transparent',
                  '&:focus': {
                    borderBottom: '2px solid #339af0',
                  },
                },
              }}
            />

            <Textarea
              label="Commentaire"
              placeholder=""
              {...form.getInputProps('description')}
              rows={3}
              styles={{
                label: {
                  fontWeight: 500,
                  marginBottom: 4,
                },
                input: {
                  border: '1px solid #ced4da',
                  borderRadius: 4,
                },
              }}
            />

            <Box>
              <Text fw={500} mb="sm">Interdictions</Text>
              <Box
                p="md"
                style={{
                  border: '2px solid #339af0',
                  borderRadius: 4,
                  backgroundColor: '#f8f9fa',
                }}
              >
                <Grid>
                  <Grid.Col span={6}>
                    <Stack gap="xs">
                      <Checkbox
                        label="Mouvement de Stock"
                        {...form.getInputProps('mouvementStock', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Bon de retour"
                        {...form.getInputProps('bonRetour', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Inventory"
                        {...form.getInputProps('inventory', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="WarehouseExchange"
                        {...form.getInputProps('warehouseExchange', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Consignation"
                        {...form.getInputProps('consignation', { type: 'checkbox' })}
                      />
                    </Stack>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Stack gap="xs">
                      <Checkbox
                        label="Bon de déposition"
                        {...form.getInputProps('bonDeposition', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Bon de réception"
                        {...form.getInputProps('bonReception', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Transformation"
                        {...form.getInputProps('transformation', { type: 'checkbox' })}
                      />
                      <Checkbox
                        label="Retour de consignation"
                        {...form.getInputProps('retourConsignation', { type: 'checkbox' })}
                      />
                    </Stack>
                  </Grid.Col>
                </Grid>
              </Box>
            </Box>

            <Group>
              <Checkbox
                label="Dépôt par défaut"
                {...form.getInputProps('depotParDefaut', { type: 'checkbox' })}
                styles={{
                  label: {
                    color: '#6c757d',
                  },
                }}
              />
            </Group>

            <Group justify="flex-end" mt="xl">
              <Button
                variant="outline"
                onClick={close}
                color="gray"
              >
                Enregistrer
              </Button>
              <Button
                type="submit"
                color="red"
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Container>
  );
}
