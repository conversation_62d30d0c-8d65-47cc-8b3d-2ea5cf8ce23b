import React from 'react';
import { <PERSON><PERSON>, <PERSON>ack, Text, Group, Button,ActionIcon } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiPlaylistCheck,mdiDeleteSweep } from '@mdi/js';
import SimpleBar from 'simplebar-react';
import { TreeNode, TreeItemChoixMultipleProps } from './types';

interface DictionaryTreeModalProps {
  opened: boolean;
  onClose: () => void;
  exampleData: TreeNode[];
  selectedNodes: Set<string>;
  collapsedNodes: Record<string, boolean>;
  onToggleNodeCollapse: (nodeId: string) => void;
  onToggleNodeSelection: (nodeId: string) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onAddModel: () => void;
  onValidate: () => void;
  onCancel: () => void;
  getSelectedValues: () => string[];
  TreeItemChoixMultiple: React.ComponentType<TreeItemChoixMultipleProps>;
}

export const DictionaryTreeModal: React.FC<DictionaryTreeModalProps> = ({
  opened,
  onClose,
  exampleData,
  selectedNodes,
  collapsedNodes,
  onToggleNodeCollapse,
  onToggleNodeSelection,
  onSelectAll,
  onDeselectAll,
  onAddModel,
  onValidate,
  onCancel,
  //getSelectedValues,
  TreeItemChoixMultiple
}) => {
  return (
    <Modal.Root
      opened={opened}
      onClose={onClose}
      transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
      centered
      size="xl"
    > 
      <Modal.Content className="overflow-y-hidden">
        <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
          <Modal.Title>
            <Group>
              <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
              <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                Choix multiple
              </Text>
            </Group>
          </Modal.Title>
          <Group justify="flex-end">
            <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
          </Group>
        </Modal.Header>
        
        <Modal.Body style={{ padding: '0px' }}>
          <div className="py-2 pl-4 h-[300px]">
            <SimpleBar style={{ height: '100%' }}>
              <div style={{ padding: '20px' }}>
                <Stack>
                  <Group justify="space-between" mb="md">
                    <Text size="lg" fw={500}>Sélectionnez les éléments</Text>
                    <Group>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onSelectAll}
                      >
                        Tout sélectionner
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onDeselectAll}
                      >
                        Tout désélectionner
                      </Button>
                    </Group>
                  </Group>

                  {exampleData.map((node) => (
                    <TreeItemChoixMultiple
                      key={node.uid}
                      node={node}
                      collapsedNodes={collapsedNodes}
                      toggleNodeCollapse={onToggleNodeCollapse}
                      selectedNodes={selectedNodes}
                      toggleNodeSelection={onToggleNodeSelection}
                    />
                  ))}
                </Stack>
              </div>
            </SimpleBar>
          </div>

          <Group justify="space-between" mt="md" p="md">
            <Group>
              <Text size="sm" c="dimmed">
                {selectedNodes.size} élément{selectedNodes.size !== 1 ? 's' : ''} sélectionné{selectedNodes.size !== 1 ? 's' : ''}
              </Text>
              <ActionIcon
                variant="filled"
                aria-label="Clear Description"
                color="red"
                onClick={onCancel}
              >
                <Icon path={mdiDeleteSweep} size={1} />
              </ActionIcon>
            </Group>
            <Group>
              {selectedNodes.size > 0 && (
                <Button 
                  variant="filled" 
                  color="blue"
                  onClick={onAddModel}
                >
                  Ajouter model
                </Button>
              )}
              <Button onClick={onValidate} disabled={selectedNodes.size === 0}>
                Valider ({selectedNodes.size})
              </Button>
              <Button variant="outline" color="red" onClick={onClose}>
                Annuler
              </Button>
            </Group>
          </Group>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
  );
};
