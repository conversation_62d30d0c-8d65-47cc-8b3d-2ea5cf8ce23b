import api from '../lib/api';
import { patientAP<PERSON>, Patient, PatientCreateData } from './patientAPI';
import type { AppointmentFormData } from '../types/api';

// Define interfaces for query parameters
export interface AppointmentQueryParams {
  date?: string;
  date_after?: string;
  date_before?: string;
  limit?: number;
  patient_id?: string;
  patient?: string;
  doctor_id?: string;
  doctor?: string;
  status?: string;
  appointment_type?: string;
  appointment_date?: string;
}

export interface DoctorQueryParams {
  specialization?: string;
  location?: string;
  available?: boolean;
  search?: string;
}

export interface TimeSlotQueryParams {
  date?: string;
  date_after?: string;
  date_before?: string;
  doctor_id?: string;
  available?: boolean;
}

export interface Appointment {
  id: string;
  title: string;
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    age?: number;
    birth_date?: string;
    phone_numbers?: string;
    etatCivil?: string;
    etatAganda?: string;
    title?: string;
  };
  // Support both naming conventions for compatibility
  start_time: string;
  end_time: string;
  start?: Date | string; // For compatibility with frontend types
  end?: Date | string; // For compatibility with frontend types
  status: string;
  appointment_type: string;
  type?: string; // Alternative naming
  location?: string;
  room?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Additional properties that might be used in the frontend
  doctor?: string;
  telephone?: string;
  email?: string;
  address?: string;
  socialSecurity?: string;
  cin?: string;
  dateNaissance?: Date | string;
  duration?: string;
  agenda?: string;
  comment?: string;
  event_Title?: string;
}

export interface Doctor {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  specialization: string;
  phone_number?: string;
  address?: string;
  bio?: string;
  avatar_url?: string;
}

export interface TimeSlot {
  id?: string;
  doctor_id?: string;
  date: string;
  start_time: string;
  end_time: string;
  available: boolean;
  is_available?: boolean; // Some components use is_available instead of available
  reason?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AvailabilitySettings {
  id?: string;
  doctor_id?: string;
  day_of_week: number | string; // 0-6 (Sunday-Saturday) or string like 'monday'
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  is_available: boolean;
  created_at?: string;
  updated_at?: string;
}

// Alias for AvailabilitySettings for backward compatibility
export type Availability = AvailabilitySettings;

// Define TimeOffSlot with specific fields for time off
export interface TimeOffSlot extends TimeSlot {
  reason: string;  // Reason for time off (required for TimeOffSlot)
  is_recurring?: boolean;  // Whether this time off repeats
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly' | 'yearly';  // How it repeats
  recurrence_end_date?: string;  // When the recurrence ends
}

const appointmentService = {
  async getAppointments(params?: AppointmentQueryParams): Promise<Appointment[]> {
    try {
      const response = await api.get('/api/appointments/', { params });
      return response.data.results || [];
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        console.warn('⚠️ Backend server not running. Using mock appointment data.');
      } else {
        console.error('Error fetching appointments:', error);
      }

      // Return mock data for development
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const mockAppointments: Appointment[] = [
        {
          id: '1',
          title: 'Regular Checkup',
          patient: {
            id: '101',
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>'
          },
          start_time: new Date(today.setHours(10, 0, 0)).toISOString(),
          end_time: new Date(today.setHours(10, 30, 0)).toISOString(),
          status: 'scheduled',
          appointment_type: 'Checkup',
          location: 'Main Clinic',
          room: 'Room 101',
          notes: 'Regular checkup appointment',
          created_at: new Date(today.setDate(today.getDate() - 7)).toISOString(),
          updated_at: new Date(today.setDate(today.getDate() - 7)).toISOString()
        },
        {
          id: '2',
          title: 'Follow-up Consultation',
          patient: {
            id: '102',
            first_name: 'Jane',
            last_name: 'Smith',
            email: '<EMAIL>'
          },
          start_time: new Date(today.setHours(14, 0, 0)).toISOString(),
          end_time: new Date(today.setHours(14, 30, 0)).toISOString(),
          status: 'scheduled',
          appointment_type: 'Follow-up',
          location: 'Main Clinic',
          room: 'Room 102',
          notes: 'Follow-up after treatment',
          created_at: new Date(today.setDate(today.getDate() - 5)).toISOString(),
          updated_at: new Date(today.setDate(today.getDate() - 5)).toISOString()
        },
        {
          id: '3',
          title: 'Annual Physical',
          patient: {
            id: '103',
            first_name: 'Robert',
            last_name: 'Johnson',
            email: '<EMAIL>'
          },
          start_time: new Date(tomorrow.setHours(9, 0, 0)).toISOString(),
          end_time: new Date(tomorrow.setHours(10, 0, 0)).toISOString(),
          status: 'scheduled',
          appointment_type: 'Physical',
          location: 'Main Clinic',
          room: 'Room 103',
          notes: 'Annual physical examination',
          created_at: new Date(today.setDate(today.getDate() - 10)).toISOString(),
          updated_at: new Date(today.setDate(today.getDate() - 10)).toISOString()
        },
        {
          id: '4',
          title: 'Vaccination',
          patient: {
            id: '104',
            first_name: 'Emily',
            last_name: 'Davis',
            email: '<EMAIL>'
          },
          start_time: new Date(tomorrow.setHours(11, 0, 0)).toISOString(),
          end_time: new Date(tomorrow.setHours(11, 30, 0)).toISOString(),
          status: 'scheduled',
          appointment_type: 'Vaccination',
          location: 'Main Clinic',
          room: 'Room 104',
          notes: 'Flu vaccination',
          created_at: new Date(today.setDate(today.getDate() - 3)).toISOString(),
          updated_at: new Date(today.setDate(today.getDate() - 3)).toISOString()
        },
        {
          id: '5',
          title: 'Consultation',
          patient: {
            id: '105',
            first_name: 'Michael',
            last_name: 'Brown',
            email: '<EMAIL>'
          },
          start_time: new Date(tomorrow.setHours(13, 0, 0)).toISOString(),
          end_time: new Date(tomorrow.setHours(13, 30, 0)).toISOString(),
          status: 'scheduled',
          appointment_type: 'Consultation',
          location: 'Main Clinic',
          room: 'Room 105',
          notes: 'Initial consultation',
          created_at: new Date(today.setDate(today.getDate() - 2)).toISOString(),
          updated_at: new Date(today.setDate(today.getDate() - 2)).toISOString()
        }
      ];

      // Filter mock appointments based on params
      if (params) {
        if (params.date) {
          const dateStr = params.date;
          return mockAppointments.filter(a => a.start_time.startsWith(dateStr));
        }
        if (params.date_after) {
          const dateAfter = new Date(params.date_after);
          return mockAppointments.filter(a => new Date(a.start_time) > dateAfter);
        }
        if (params.limit) {
          return mockAppointments.slice(0, params.limit);
        }
      }

      return mockAppointments;
    }
  },

  async getAppointment(id: string): Promise<Appointment | null> {
    try {
      const response = await api.get(`/api/appointments/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching appointment ${id}:`, error);
      return null;
    }
  },

  async createAppointment(data: Partial<Appointment>): Promise<Appointment | null> {
    try {
      const response = await api.post('/api/appointments/', data);
      return response.data;
    } catch (error) {
      console.error('Error creating appointment:', error);
      return null;
    }
  },

  /**
   * Create or update appointment with proper patient relationship
   */
  async createAppointmentWithPatient(appointmentData: AppointmentFormData, appointmentId?: string): Promise<Appointment | null> {
    try {
      const isEditing = !!appointmentId;
      console.log(`🏥 ${isEditing ? 'Updating' : 'Creating'} appointment with patient relationship:`, appointmentData);
      console.log('🔍 Appointment ID for editing:', appointmentId);

      let patientId = appointmentData.patient;

      // For updates, if no patient ID provided, try to get it from existing appointment
      if (!patientId && isEditing && appointmentId) {
        console.log('🔍 No patient ID provided for update, fetching from existing appointment...');
        try {
          const existingAppointment = await this.getAppointment(appointmentId);
          if (existingAppointment && existingAppointment.patient) {
            // Handle both string ID and patient object
            patientId = typeof existingAppointment.patient === 'string'
              ? existingAppointment.patient
              : existingAppointment.patient.id;
            console.log('✅ Retrieved patient ID from existing appointment:', patientId);
          } else {
            console.warn('⚠️ Could not retrieve patient ID from existing appointment');
          }
        } catch (error) {
          console.error('❌ Error fetching existing appointment for patient ID:', error);
        }
      }

      // If still no patient ID provided, create or find patient
      if (!patientId && appointmentData.patient_first_name && appointmentData.patient_last_name) {
        console.log('👤 No patient ID provided, creating/finding patient...');

        const patientCreateData: PatientCreateData = {
          first_name: appointmentData.patient_first_name as string,
          last_name: appointmentData.patient_last_name as string,
          email: appointmentData.patient_email as string | undefined,
          phone_number: appointmentData.patient_phone as string | undefined,
          address: appointmentData.patient_address as string | undefined,
          birth_date: appointmentData.birth_date as string | undefined,
          age: appointmentData.age as number | undefined,
          gender: appointmentData.gender as '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre' | undefined,
          patient_title: appointmentData.patient_title as string | undefined,
          etat_civil: appointmentData.etat_civil as '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)' | undefined,
          cin: appointmentData.cin as string | undefined,
          social_security: appointmentData.social_security as string | undefined,
          profession: appointmentData.profession as string | undefined,
          birthPlace: appointmentData.birth_place as string | undefined,
          fatherName: appointmentData.father_name as string | undefined,
          motherName: appointmentData.mother_name as string | undefined,
          bloodGroup: appointmentData.blood_group as string | undefined,
          allergies: appointmentData.allergies as string | undefined,
          notes: appointmentData.notes as string | undefined,
        };

        try {
          const { patient, created } = await patientAPI.findOrCreate(patientCreateData);
          patientId = patient.id;

          if (created) {
            console.log('✅ New patient created:', patient.id);
          } else {
            console.log('✅ Existing patient found:', patient.id);
          }
        } catch (patientError) {
          console.error('❌ Failed to create/find patient:', patientError);

          // Try to extract error message
          const errorMessage = (patientError as unknown as { response?: { data?: { message?: string } }; message?: string })?.response?.data?.message ||
                              (patientError as unknown as { message?: string })?.message ||
                              'Failed to create patient';

          throw new Error(`Patient creation failed: ${errorMessage}`);
        }
      }

      // Create appointment payload - different handling for create vs update
      let appointmentPayload;

      if (isEditing && appointmentId) {
        // For UPDATES: Include all required fields for AppointmentSerializer
        // First get existing appointment data to preserve fields not being updated
        let existingData: Record<string, unknown> = {};
        try {
          const existingAppointment = await this.getAppointment(appointmentId);
          if (existingAppointment) {
            // Safely access fields that might exist in the API response
            const existing = existingAppointment as unknown as Record<string, unknown>;
            existingData = {
              resource_id: existing.resource_id,
              event_type: existing.event_type,
              // Preserve other important fields
              status: existing.status,
              priority: existing.priority,
            };
            console.log('📋 Preserved existing appointment data:', existingData);
          }
        } catch (error) {
          console.warn('⚠️ Could not fetch existing appointment data for preservation:', error);
        }

        appointmentPayload = {
          ...existingData, // Start with existing data
          ...appointmentData, // Override with new data
          patient: patientId, // Required field for updates
          // Keep all required fields for update serializer
          appointment_date: appointmentData.appointment_date,
          appointment_time: appointmentData.appointment_time,
          // ALWAYS KEEP EMAIL DATA - the backend needs this
          patient_email: appointmentData.patient_email,
          // Include patient creation fields for updates (they may be needed)
          patient_first_name: appointmentData.patient_first_name,
          patient_last_name: appointmentData.patient_last_name,
          patient_phone: appointmentData.patient_phone,
          patient_address: appointmentData.patient_address,
        };
      } else {
        // For CREATION: Use AppointmentCreateSerializer format
        appointmentPayload = {
          ...appointmentData,
          patient: patientId,
          // ALWAYS KEEP EMAIL DATA - the backend needs this for patient creation/lookup
          patient_email: appointmentData.patient_email, // ALWAYS preserve the email
          // Only remove other patient creation fields if we have a patient ID
          patient_first_name: patientId ? undefined : appointmentData.patient_first_name,
          patient_last_name: patientId ? undefined : appointmentData.patient_last_name,
          patient_phone: patientId ? undefined : appointmentData.patient_phone,
          patient_address: patientId ? undefined : appointmentData.patient_address,
          birth_date: patientId ? undefined : appointmentData.birth_date,
          age: patientId ? undefined : appointmentData.age,
          gender: patientId ? undefined : appointmentData.gender,
          patient_title: patientId ? undefined : appointmentData.patient_title,
          etat_civil: patientId ? undefined : appointmentData.etat_civil,
          cin: patientId ? undefined : appointmentData.cin,
          social_security: patientId ? undefined : appointmentData.social_security,
          profession: patientId ? undefined : appointmentData.profession,
          birth_place: patientId ? undefined : appointmentData.birth_place,
          father_name: patientId ? undefined : appointmentData.father_name,
          mother_name: patientId ? undefined : appointmentData.mother_name,
          blood_group: patientId ? undefined : appointmentData.blood_group,
          allergies: patientId ? undefined : appointmentData.allergies,
        };
      }

      // Enhanced validation for different operations
      if (isEditing && appointmentId) {
        // For updates: patient ID is required
        if (!appointmentPayload.patient) {
          throw new Error('Missing required patient ID for appointment update');
        }
        if (!appointmentPayload.appointment_date || !appointmentPayload.appointment_time) {
          throw new Error('Missing required appointment_date or appointment_time for update');
        }
      } else {
        // For creation: either patient ID or patient names are required
        if (!appointmentPayload.patient && (!appointmentPayload.patient_first_name || !appointmentPayload.patient_last_name)) {
          throw new Error('Missing required patient information: either patient ID or patient first_name and last_name must be provided');
        }
      }

      console.log('📅 Appointment payload prepared:', {
        isEditing,
        appointmentId,
        operation: isEditing ? 'UPDATE' : 'CREATE',
        payloadKeys: Object.keys(appointmentPayload),
        hasPatientId: !!appointmentPayload.patient,
        hasRequiredFields: !!(appointmentPayload.appointment_date && appointmentPayload.appointment_time)
      });

      console.log('🔍 EMAIL DEBUG - appointmentPayload.patient_email:', appointmentPayload.patient_email);
      console.log('🔍 EMAIL DEBUG - original appointmentData.patient_email:', appointmentData.patient_email);
      console.log('🔍 Payload validation:', {
        hasPatientId: !!appointmentPayload.patient,
        hasPatientNames: !!(appointmentPayload.patient_first_name && appointmentPayload.patient_last_name),
        patientId: appointmentPayload.patient,
        patientFirstName: appointmentPayload.patient_first_name,
        patientLastName: appointmentPayload.patient_last_name,
        patientEmail: appointmentPayload.patient_email,
        appointmentDate: appointmentPayload.appointment_date,
        appointmentTime: appointmentPayload.appointment_time
      });

      let response;
      if (isEditing && appointmentId) {
        // Update existing appointment
        console.log('🔄 Updating existing appointment:', appointmentId);
        response = await api.put(`/api/appointments/${appointmentId}/`, appointmentPayload);
        console.log('✅ Appointment updated successfully:', response.data);
      } else {
        // Create new appointment
        console.log('➕ Creating new appointment');
        response = await api.post('/api/appointments/', appointmentPayload);
        console.log('✅ Appointment created successfully:', response.data);
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error creating appointment with patient:', error);

      // Enhanced error logging
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number; data?: unknown } };
        console.error('📊 Error details:', {
          status: axiosError.response?.status,
          data: axiosError.response?.data
        });
      }

      return null;
    }
  },

  /**
   * Get appointment with full patient data
   */
  async getAppointmentWithPatient(appointmentId: string): Promise<{ appointment: Appointment; patient: Patient } | null> {
    try {
      console.log('🔍 Fetching appointment with patient data:', appointmentId);

      // Get appointment data
      const appointment = await this.getAppointment(appointmentId);
      if (!appointment) {
        console.warn('❌ Appointment not found:', appointmentId);
        return null;
      }

      // Get patient data if patient ID exists
      let patient: Patient | null = null;
      if (appointment.patient?.id) {
        try {
          patient = await patientAPI.get(appointment.patient.id);
        } catch (error) {
          console.warn('⚠️ Failed to fetch patient data:', error);
          // Continue without patient data
        }
      }

      console.log('✅ Appointment with patient data fetched:', { appointment, patient });
      return { appointment, patient: patient as Patient };
    } catch (error) {
      console.error('❌ Error fetching appointment with patient:', error);
      return null;
    }
  },

  async updateAppointment(id: string, data: Partial<Appointment>): Promise<Appointment | null> {
    try {
      // Check if backend is available first
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const healthCheck = await fetch(`${API_URL}/api/appointments/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (healthCheck.status === 404) {
        console.warn('⚠️ Backend server not running. Appointment will be updated locally only.');
        // Return a mock response to allow local update to proceed
        return { id, ...data } as Appointment;
      }

      const response = await api.patch(`/api/appointments/${id}/`, data);
      console.log('✅ Appointment updated in backend:', id);
      return response.data;
    } catch (error) {
      console.warn(`⚠️ Failed to update appointment ${id} in backend, continuing with local update:`, error);
      // Return a mock response to allow local update to proceed
      return { id, ...data } as Appointment;
    }
  },

  async cancelAppointment(id: string): Promise<boolean> {
    try {
      // Check if backend is available first
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const healthCheck = await fetch(`${API_URL}/api/appointments/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (healthCheck.status === 404) {
        console.warn('⚠️ Backend server not running. Appointment will be cancelled locally only.');
        // Return true to allow local cancellation to proceed
        return true;
      }

      await api.patch(`/api/appointments/${id}/`, { status: 'cancelled' });
      console.log('✅ Appointment cancelled in backend:', id);
      return true;
    } catch (error) {
      console.warn(`⚠️ Failed to cancel appointment ${id} in backend, continuing with local cancellation:`, error);
      // Return true to allow local cancellation to proceed
      return true;
    }
  },

  async deleteAppointment(id: string): Promise<boolean> {
    try {
      // Check if backend is available first
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const healthCheck = await fetch(`${API_URL}/api/appointments/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (healthCheck.status === 404) {
        console.warn('⚠️ Backend server not running. Appointment will be deleted locally only.');
        // Return true to allow local deletion to proceed
        return true;
      }

      await api.delete(`/api/appointments/${id}/`);
      console.log('✅ Appointment deleted from backend:', id);
      return true;
    } catch (error) {
      console.warn(`⚠️ Failed to delete appointment ${id} from backend, continuing with local deletion:`, error);
      // Return true to allow local deletion to proceed even if backend fails
      return true;
    }
  },

  async completeAppointment(id: string, notes?: string): Promise<boolean> {
    try {
      // Check if backend is available first
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const healthCheck = await fetch(`${API_URL}/api/appointments/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (healthCheck.status === 404) {
        console.warn('⚠️ Backend server not running. Appointment will be completed locally only.');
        // Return true to allow local completion to proceed
        return true;
      }

      await api.patch(`/api/appointments/${id}/`, {
        status: 'completed',
        notes: notes || ''
      });
      console.log('✅ Appointment completed in backend:', id);
      return true;
    } catch (error) {
      console.warn(`⚠️ Failed to complete appointment ${id} in backend, continuing with local completion:`, error);
      // Return true to allow local completion to proceed
      return true;
    }
  },

  async getAvailability(params?: { doctor_id?: string; day_of_week?: string | number }): Promise<AvailabilitySettings[]> {
    try {
      const response = await api.get('/api/availability/', { params });
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching availability settings:', error);
      return [];
    }
  },

  async updateAvailability(settings: AvailabilitySettings[]): Promise<AvailabilitySettings[]> {
    try {
      const response = await api.post('/api/availability/update/', { settings });
      return response.data || [];
    } catch (error) {
      console.error('Error updating availability settings:', error);
      return [];
    }
  },

  async getAvailableTimeSlots(date: string): Promise<TimeSlot[]> {
    try {
      const response = await api.get('/api/availability/slots/', {
        params: { date }
      });
      return response.data || [];
    } catch (error) {
      console.error(`Error fetching time slots for date ${date}:`, error);
      return [];
    }
  },

  async getDoctors(params?: DoctorQueryParams): Promise<Doctor[]> {
    try {
      const response = await api.get('/api/doctors/', { params });
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching doctors:', error);
      // Return mock data for development
      if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
        return [
          {
            id: '1',
            first_name: 'John',
            last_name: 'Smith',
            email: '<EMAIL>',
            specialization: 'Cardiology',
            phone_number: '(*************',
            address: '123 Medical Center Dr, New York, NY',
            bio: 'Dr. Smith is a board-certified cardiologist with over 15 years of experience.',
            avatar_url: ''
          },
          {
            id: '2',
            first_name: 'Sarah',
            last_name: 'Johnson',
            email: '<EMAIL>',
            specialization: 'Dermatology',
            phone_number: '(*************',
            address: '456 Health Blvd, New York, NY',
            bio: 'Dr. Johnson specializes in treating skin conditions and performing cosmetic procedures.',
            avatar_url: ''
          }
        ];
      }
      return [];
    }
  },

  async getDoctor(id: string): Promise<Doctor | null> {
    try {
      const response = await api.get(`/api/doctors/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching doctor ${id}:`, error);
      // Return mock data for development
      if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
        return {
          id,
          first_name: 'John',
          last_name: 'Smith',
          email: '<EMAIL>',
          specialization: 'Cardiology',
          phone_number: '(*************',
          address: '123 Medical Center Dr, New York, NY',
          bio: 'Dr. Smith is a board-certified cardiologist with over 15 years of experience.',
          avatar_url: ''
        };
      }
      return null;
    }
  },

  async getDoctorAvailability(doctorId?: string): Promise<AvailabilitySettings[]> {
    try {
      const params = doctorId ? { doctor_id: doctorId } : {};
      const response = await api.get('/api/doctors/availability/', { params });
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching doctor availability:', error);
      // Return mock data for development
      return [
        {
          id: '1',
          doctor_id: '1',
          day_of_week: 'monday',
          start_time: '09:00',
          end_time: '17:00',
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          doctor_id: '1',
          day_of_week: 'tuesday',
          start_time: '09:00',
          end_time: '17:00',
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          doctor_id: '1',
          day_of_week: 'wednesday',
          start_time: '09:00',
          end_time: '17:00',
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '4',
          doctor_id: '1',
          day_of_week: 'thursday',
          start_time: '09:00',
          end_time: '17:00',
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '5',
          doctor_id: '1',
          day_of_week: 'friday',
          start_time: '09:00',
          end_time: '17:00',
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ];
    }
  },

  async getDoctorTimeSlots(doctorId?: string, params?: TimeSlotQueryParams): Promise<TimeOffSlot[]> {
    try {
      const queryParams = {
        ...(params || {}),
        ...(doctorId ? { doctor_id: doctorId } : {})
      };
      const response = await api.get('/api/doctors/timeoff/', { params: queryParams });
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching doctor time slots:', error);
      // Return mock data for development
      return [
        {
          id: '1',
          doctor_id: '1',
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          start_time: '00:00',
          end_time: '23:59',
          available: false,
          reason: 'Vacation',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ];
    }
  },

  // Get today's appointments
  async getTodayAppointments(): Promise<Appointment[]> {
    try {
      const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD
      return this.getAppointments({ date: today });
    } catch (error) {
      console.error('Error fetching today\'s appointments:', error);
      return [];
    }
  },

  // Get appointments for the current week
  async getCurrentWeekAppointments(): Promise<Appointment[]> {
    try {
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday

      const startDate = startOfWeek.toISOString().split('T')[0];
      const endDate = endOfWeek.toISOString().split('T')[0];

      return this.getAppointments({
        date_after: startDate,
        date_before: endDate
      });
    } catch (error) {
      console.error('Error fetching current week appointments:', error);
      return [];
    }
  },

  // Reschedule an appointment
  async rescheduleAppointment(id: string, newDate: string, newTime: string): Promise<Appointment | null> {
    try {
      const response = await api.patch(`/api/appointments/${id}/`, {
        appointment_date: newDate,
        appointment_time: newTime,
        status: 'rescheduled'
      });
      return response.data;
    } catch (error) {
      console.error(`Error rescheduling appointment ${id}:`, error);
      return null;
    }
  },

  // Confirm an appointment
  async confirmAppointment(id: string): Promise<boolean> {
    try {
      await api.patch(`/api/appointments/${id}/`, { status: 'confirmed' });
      return true;
    } catch (error) {
      console.error(`Error confirming appointment ${id}:`, error);
      return false;
    }
  },

  // Mark appointment as no-show
  async markNoShow(id: string): Promise<boolean> {
    try {
      await api.patch(`/api/appointments/${id}/`, { status: 'no_show' });
      return true;
    } catch (error) {
      console.error(`Error marking appointment ${id} as no-show:`, error);
      return false;
    }
  },

  // Get appointments by patient ID
  async getPatientAppointments(patientId: string): Promise<Appointment[]> {
    try {
      const response = await api.get('/api/appointments/', {
        params: { patient: patientId }
      });
      return response.data.results || [];
    } catch (error) {
      console.error(`Error fetching appointments for patient ${patientId}:`, error);
      return [];
    }
  },

  // Get appointments by doctor ID
  async getDoctorAppointments(doctorId: string, date?: string): Promise<Appointment[]> {
    try {
      const params: AppointmentQueryParams = { doctor_id: doctorId };
      if (date) {
        params.date = date;
      }
      const response = await api.get('/api/appointments/', { params });
      return response.data.results || [];
    } catch (error) {
      console.error(`Error fetching appointments for doctor ${doctorId}:`, error);
      return [];
    }
  }
};

export default appointmentService;
