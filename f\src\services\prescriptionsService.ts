/**
 * Prescriptions Service
 * Handles all prescriptions-related data operations including:
 * - Prescription management (create, view, edit, print, send)
 * - Medication history tracking
 * - Prescription templates
 * - Refill management
 * - Patient-specific prescriptions
 */

// Types for Prescriptions data
export interface Prescription {
  id: string;
  prescription_number: string;
  patient_id: string;
  patient_name: string;
  doctor_id: string;
  doctor_name: string;
  medication_name: string;
  medication_id?: string;
  dosage: string;
  frequency: string;
  duration: string;
  quantity: number;
  refills_allowed: number;
  refills_remaining: number;
  instructions: string;
  indication?: string;
  date_prescribed: string;
  date_expires: string;
  status: 'active' | 'expired' | 'completed' | 'cancelled' | 'pending';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  is_controlled_substance: boolean;
  pharmacy_id?: string;
  pharmacy_name?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface MedicationHistory {
  id: string;
  patient_id: string;
  patient_name: string;
  medication_name: string;
  medication_id?: string;
  dosage: string;
  start_date: string;
  end_date?: string;
  reason: string;
  prescribing_doctor: string;
  effectiveness: 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';
  side_effects?: string;
  notes?: string;
  discontinued_reason?: string;
  created_at: string;
}

export interface PrescriptionTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  medication_name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  indication: string;
  is_controlled_substance: boolean;
  created_by: string;
  is_active: boolean;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface PrescriptionRefill {
  id: string;
  prescription_id: string;
  refill_number: number;
  refill_date: string;
  quantity_dispensed: number;
  pharmacy_id?: string;
  pharmacy_name?: string;
  dispensed_by?: string;
  patient_pickup_date?: string;
  status: 'requested' | 'approved' | 'dispensed' | 'picked_up' | 'cancelled';
  notes?: string;
  created_at: string;
}

export interface DrugInteraction {
  id: string;
  medication_1: string;
  medication_2: string;
  interaction_type: 'major' | 'moderate' | 'minor';
  description: string;
  recommendation: string;
  severity_level: number; // 1-10
}

export interface PrescriptionAnalytics {
  id: number;
  report_date: string;
  total_prescriptions: number;
  active_prescriptions: number;
  expired_prescriptions: number;
  most_prescribed_medications: MostPrescribedMedication[];
  prescription_trends: PrescriptionTrend[];
  refill_statistics: RefillStatistics;
  patient_compliance: PatientCompliance[];
  drug_interactions_found: number;
  controlled_substances_prescribed: number;
}

export interface MostPrescribedMedication {
  medication_name: string;
  prescription_count: number;
  patient_count: number;
  total_quantity: number;
}

export interface PrescriptionTrend {
  month: string;
  total_prescriptions: number;
  new_prescriptions: number;
  refills: number;
  average_duration: number;
}

export interface RefillStatistics {
  total_refills: number;
  pending_refills: number;
  overdue_refills: number;
  average_refill_time: number;
}

export interface PatientCompliance {
  patient_id: string;
  patient_name: string;
  total_prescriptions: number;
  completed_prescriptions: number;
  compliance_rate: number;
  missed_refills: number;
}

export interface PrescriptionsSummary {
  reportDate: string;
  prescriptions: Prescription[];
  medicationHistory: MedicationHistory[];
  prescriptionTemplates: PrescriptionTemplate[];
  prescriptionRefills: PrescriptionRefill[];
  drugInteractions: DrugInteraction[];
  analytics: PrescriptionAnalytics;
  lastUpdate: string;
}

class PrescriptionsService {
  private baseURL = '/api/prescriptions';

  // Prescriptions operations
  async getPrescriptions(patientId?: string, status?: string, dateRange?: { start: string; end: string }): Promise<Prescription[]> {
    try {
      let url = `${this.baseURL}`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (status) params.append('status', status);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch prescriptions: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching prescriptions:', error);
      return this.getMockPrescriptions(patientId);
    }
  }

  async createPrescription(prescriptionData: Omit<Prescription, 'id' | 'created_at' | 'updated_at'>): Promise<Prescription> {
    try {
      const response = await fetch(`${this.baseURL}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(prescriptionData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create prescription: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating prescription:', error);
      // Return mock created data
      return {
        id: Date.now().toString(),
        ...prescriptionData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    }
  }

  // Medication History operations
  async getMedicationHistory(patientId?: string): Promise<MedicationHistory[]> {
    try {
      let url = `${this.baseURL}/medication-history`;
      if (patientId) {
        url += `?patient_id=${patientId}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch medication history: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching medication history:', error);
      return this.getMockMedicationHistory(patientId);
    }
  }

  // Prescription Templates operations
  async getPrescriptionTemplates(category?: string): Promise<PrescriptionTemplate[]> {
    try {
      let url = `${this.baseURL}/templates`;
      if (category) {
        url += `?category=${category}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch prescription templates: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching prescription templates:', error);
      return this.getMockPrescriptionTemplates();
    }
  }

  // Prescription Refills operations
  async getPrescriptionRefills(prescriptionId?: string, patientId?: string): Promise<PrescriptionRefill[]> {
    try {
      let url = `${this.baseURL}/refills`;
      const params = new URLSearchParams();
      
      if (prescriptionId) params.append('prescription_id', prescriptionId);
      if (patientId) params.append('patient_id', patientId);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch prescription refills: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching prescription refills:', error);
      return this.getMockPrescriptionRefills();
    }
  }

  // Drug Interactions operations
  async checkDrugInteractions(medications: string[]): Promise<DrugInteraction[]> {
    try {
      const response = await fetch(`${this.baseURL}/drug-interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ medications }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to check drug interactions: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error checking drug interactions:', error);
      return this.getMockDrugInteractions();
    }
  }

  // Analytics operations
  async getPrescriptionAnalytics(dateRange?: { start: string; end: string }): Promise<PrescriptionAnalytics> {
    try {
      let url = `${this.baseURL}/analytics`;
      if (dateRange) {
        const params = new URLSearchParams({
          start: dateRange.start,
          end: dateRange.end,
        });
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch prescription analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching prescription analytics:', error);
      return this.getMockPrescriptionAnalytics();
    }
  }

  // Get comprehensive prescriptions summary
  async getPrescriptionsSummary(patientId?: string, dateRange?: { start: string; end: string }): Promise<PrescriptionsSummary> {
    try {
      const [prescriptions, medicationHistory, prescriptionTemplates, prescriptionRefills, analytics] = await Promise.all([
        this.getPrescriptions(patientId, undefined, dateRange),
        this.getMedicationHistory(patientId),
        this.getPrescriptionTemplates(),
        this.getPrescriptionRefills(undefined, patientId),
        this.getPrescriptionAnalytics(dateRange),
      ]);

      // Check for drug interactions if we have prescriptions
      const medications = prescriptions.map(p => p.medication_name);
      const drugInteractions = medications.length > 1 ? await this.checkDrugInteractions(medications) : [];

      return {
        reportDate: new Date().toISOString(),
        prescriptions,
        medicationHistory,
        prescriptionTemplates,
        prescriptionRefills,
        drugInteractions,
        analytics,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching prescriptions summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockPrescriptions(patientId?: string): Prescription[] {
    const mockData: Prescription[] = [
      {
        id: 'RX-001',
        prescription_number: 'RX-2024-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        doctor_id: 'doc-1',
        doctor_name: 'Dr. Martin',
        medication_name: 'Amoxicilline 500mg',
        dosage: '500mg',
        frequency: '3 fois par jour',
        duration: '10 jours',
        quantity: 30,
        refills_allowed: 0,
        refills_remaining: 0,
        instructions: 'Prendre avec de la nourriture',
        indication: 'Infection bactérienne',
        date_prescribed: '2024-01-20',
        date_expires: '2024-07-20',
        status: 'active',
        priority: 'normal',
        is_controlled_substance: false,
        created_at: '2024-01-20T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(p => p.patient_id === patientId) : mockData;
  }

  private getMockMedicationHistory(patientId?: string): MedicationHistory[] {
    const mockData: MedicationHistory[] = [
      {
        id: 'MH-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        medication_name: 'Paracétamol 500mg',
        dosage: '500mg',
        start_date: '2024-01-01',
        end_date: '2024-01-10',
        reason: 'Douleur post-opératoire',
        prescribing_doctor: 'Dr. Martin',
        effectiveness: 'good',
        notes: 'Bien toléré par le patient',
        created_at: '2024-01-01T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(h => h.patient_id === patientId) : mockData;
  }

  private getMockPrescriptionTemplates(): PrescriptionTemplate[] {
    return [
      {
        id: 'template-1',
        name: 'Antibiotique Standard',
        description: 'Template pour prescription d\'antibiotique standard',
        category: 'Antibiotiques',
        medication_name: 'Amoxicilline',
        dosage: '500mg',
        frequency: '3 fois par jour',
        duration: '7-10 jours',
        instructions: 'Prendre avec de la nourriture',
        indication: 'Infection bactérienne',
        is_controlled_substance: false,
        created_by: 'Dr. Martin',
        is_active: true,
        usage_count: 25,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockPrescriptionRefills(): PrescriptionRefill[] {
    return [
      {
        id: 'refill-1',
        prescription_id: 'RX-001',
        refill_number: 1,
        refill_date: '2024-01-25',
        quantity_dispensed: 30,
        pharmacy_name: 'Pharmacie Centrale',
        status: 'dispensed',
        created_at: '2024-01-25T10:00:00Z',
      },
    ];
  }

  private getMockDrugInteractions(): DrugInteraction[] {
    return [
      {
        id: 'interaction-1',
        medication_1: 'Warfarine',
        medication_2: 'Aspirine',
        interaction_type: 'major',
        description: 'Risque accru de saignement',
        recommendation: 'Surveiller les signes de saignement',
        severity_level: 8,
      },
    ];
  }

  private getMockPrescriptionAnalytics(): PrescriptionAnalytics {
    return {
      id: 1,
      report_date: '2024-01-20',
      total_prescriptions: 150,
      active_prescriptions: 120,
      expired_prescriptions: 30,
      most_prescribed_medications: [
        {
          medication_name: 'Amoxicilline',
          prescription_count: 25,
          patient_count: 20,
          total_quantity: 750,
        },
      ],
      prescription_trends: [
        {
          month: 'Jan',
          total_prescriptions: 150,
          new_prescriptions: 120,
          refills: 30,
          average_duration: 10,
        },
      ],
      refill_statistics: {
        total_refills: 45,
        pending_refills: 5,
        overdue_refills: 2,
        average_refill_time: 3.5,
      },
      patient_compliance: [
        {
          patient_id: '1',
          patient_name: 'Jean Dupont',
          total_prescriptions: 5,
          completed_prescriptions: 4,
          compliance_rate: 80,
          missed_refills: 1,
        },
      ],
      drug_interactions_found: 3,
      controlled_substances_prescribed: 8,
    };
  }
}

export const prescriptionsService = new PrescriptionsService();
export default prescriptionsService;
