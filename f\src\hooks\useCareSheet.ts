/**
 * Custom hook for managing care-sheet data
 * Provides easy access to mutuelles, visits, procedures, and devis data
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  careSheetService, 
  MutuelleData, 
  VisitData, 
  ProcedureData, 
  DevisData, 
  CareSheetSummary 
} from '@/services/careSheetService';

interface UseCareSheetOptions {
  patientId?: string;
  autoFetch?: boolean;
  refreshInterval?: number;
}

interface UseCareSheetReturn {
  // Data
  mutuelles: MutuelleData[];
  visits: VisitData[];
  procedures: ProcedureData[];
  devis: DevisData[];
  summary: CareSheetSummary | null;
  
  // Loading states
  loading: boolean;
  mutuellesLoading: boolean;
  visitsLoading: boolean;
  proceduresLoading: boolean;
  devisLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchMutuelles: (patientId?: string) => Promise<void>;
  fetchVisits: (patientId?: string) => Promise<void>;
  fetchProcedures: (patientId?: string) => Promise<void>;
  fetchDevis: (patientId?: string) => Promise<void>;
  fetchSummary: (patientId: string) => Promise<void>;
  refreshAll: (patientId?: string) => Promise<void>;
  createMutuelle: (mutuelleData: Omit<MutuelleData, 'id'>) => Promise<void>;
  updateMutuelle: (id: number, mutuelleData: Partial<MutuelleData>) => Promise<void>;
  
  // Utility functions
  getMutuellesByPatient: (patientId: string) => MutuelleData[];
  getVisitsByPatient: (patientId: string) => VisitData[];
  getProceduresByPatient: (patientId: string) => ProcedureData[];
  getDevisByPatient: (patientId: string) => DevisData[];
  getPatientCareSheetStats: (patientId: string) => {
    totalMutuelles: number;
    totalVisits: number;
    totalProcedures: number;
    totalDevis: number;
    lastVisitDate: string | null;
    nextAppointment: string | null;
  };
}

export const useCareSheet = (options: UseCareSheetOptions = {}): UseCareSheetReturn => {
  const { patientId, autoFetch = true, refreshInterval } = options;

  // State
  const [mutuelles, setMutuelles] = useState<MutuelleData[]>([]);
  const [visits, setVisits] = useState<VisitData[]>([]);
  const [procedures, setProcedures] = useState<ProcedureData[]>([]);
  const [devis, setDevis] = useState<DevisData[]>([]);
  const [summary, setSummary] = useState<CareSheetSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [mutuellesLoading, setMutuellesLoading] = useState(false);
  const [visitsLoading, setVisitsLoading] = useState(false);
  const [proceduresLoading, setProceduresLoading] = useState(false);
  const [devisLoading, setDevisLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchMutuelles = useCallback(async (targetPatientId?: string) => {
    setMutuellesLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getMutuelles(targetPatientId || patientId);
      setMutuelles(data);
    } catch (err) {
      setError(`Failed to fetch mutuelles: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setMutuellesLoading(false);
    }
  }, [patientId]);

  const fetchVisits = useCallback(async (targetPatientId?: string) => {
    setVisitsLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getVisits(targetPatientId || patientId);
      setVisits(data);
    } catch (err) {
      setError(`Failed to fetch visits: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setVisitsLoading(false);
    }
  }, [patientId]);

  const fetchProcedures = useCallback(async (targetPatientId?: string) => {
    setProceduresLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getProcedures(targetPatientId || patientId);
      setProcedures(data);
    } catch (err) {
      setError(`Failed to fetch procedures: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setProceduresLoading(false);
    }
  }, [patientId]);

  const fetchDevis = useCallback(async (targetPatientId?: string) => {
    setDevisLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getDevis(targetPatientId || patientId);
      setDevis(data);
    } catch (err) {
      setError(`Failed to fetch devis: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setDevisLoading(false);
    }
  }, [patientId]);

  const fetchSummary = useCallback(async (targetPatientId: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getCareSheetSummary(targetPatientId);
      setSummary(data);
      setMutuelles(data.mutuelles);
      setVisits(data.visites);
      setProcedures(data.procedures);
      setDevis(data.devis);
    } catch (err) {
      setError(`Failed to fetch care sheet summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAll = useCallback(async (targetPatientId?: string) => {
    const id = targetPatientId || patientId;
    if (!id) return;

    setLoading(true);
    try {
      await Promise.all([
        fetchMutuelles(id),
        fetchVisits(id),
        fetchProcedures(id),
        fetchDevis(id),
      ]);
    } finally {
      setLoading(false);
    }
  }, [patientId, fetchMutuelles, fetchVisits, fetchProcedures, fetchDevis]);

  const createMutuelle = useCallback(async (mutuelleData: Omit<MutuelleData, 'id'>) => {
    setError(null);
    try {
      const newMutuelle = await careSheetService.createMutuelle(mutuelleData);
      setMutuelles(prev => [...prev, newMutuelle]);
    } catch (err) {
      setError(`Failed to create mutuelle: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const updateMutuelle = useCallback(async (id: number, mutuelleData: Partial<MutuelleData>) => {
    setError(null);
    try {
      const updatedMutuelle = await careSheetService.updateMutuelle(id, mutuelleData);
      setMutuelles(prev => prev.map(m => m.id === id ? updatedMutuelle : m));
    } catch (err) {
      setError(`Failed to update mutuelle: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const getMutuellesByPatient = useCallback((targetPatientId: string) => {
    return mutuelles.filter(m => m.patientId === targetPatientId);
  }, [mutuelles]);

  const getVisitsByPatient = useCallback((targetPatientId: string) => {
    return visits.filter(v => v.patientId === targetPatientId);
  }, [visits]);

  const getProceduresByPatient = useCallback((targetPatientId: string) => {
    return procedures.filter(p => p.patientId === targetPatientId);
  }, [procedures]);

  const getDevisByPatient = useCallback((targetPatientId: string) => {
    return devis.filter(d => d.patientId === targetPatientId);
  }, [devis]);

  const getPatientCareSheetStats = useCallback((targetPatientId: string) => {
    const patientMutuelles = getMutuellesByPatient(targetPatientId);
    const patientVisits = getVisitsByPatient(targetPatientId);
    const patientProcedures = getProceduresByPatient(targetPatientId);
    const patientDevis = getDevisByPatient(targetPatientId);

    // Find last visit date
    const lastVisit = patientVisits
      .filter(v => v.statut === 'terminee')
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

    // Find next appointment (planned visits)
    const nextVisit = patientVisits
      .filter(v => v.statut === 'planifiee')
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())[0];

    return {
      totalMutuelles: patientMutuelles.length,
      totalVisits: patientVisits.length,
      totalProcedures: patientProcedures.length,
      totalDevis: patientDevis.length,
      lastVisitDate: lastVisit?.date || null,
      nextAppointment: nextVisit?.date || null,
    };
  }, [getMutuellesByPatient, getVisitsByPatient, getProceduresByPatient, getDevisByPatient]);

  // Auto-fetch on mount and when patientId changes
  useEffect(() => {
    if (autoFetch && patientId) {
      refreshAll(patientId);
    }
  }, [autoFetch, patientId, refreshAll]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && patientId) {
      const interval = setInterval(() => {
        refreshAll(patientId);
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, patientId, refreshAll]);

  return {
    // Data
    mutuelles,
    visits,
    procedures,
    devis,
    summary,
    
    // Loading states
    loading,
    mutuellesLoading,
    visitsLoading,
    proceduresLoading,
    devisLoading,
    
    // Error state
    error,
    
    // Actions
    fetchMutuelles,
    fetchVisits,
    fetchProcedures,
    fetchDevis,
    fetchSummary,
    refreshAll,
    createMutuelle,
    updateMutuelle,
    
    // Utility functions
    getMutuellesByPatient,
    getVisitsByPatient,
    getProceduresByPatient,
    getDevisByPatient,
    getPatientCareSheetStats,
  };
};

export default useCareSheet;
