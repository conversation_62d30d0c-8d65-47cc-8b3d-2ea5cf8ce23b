'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { MesContrats } from './Mes_contrats';

export default function MesContratsDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Recherche de contrats:\nTerme: "${query.searchAll}"\nPage: ${query.page}\nLignes: ${query.limit}\nFiltres: ${Object.keys(query.filters).length} actifs`);
  };

  const handleAddContract = (type: 'SUBSCRIPTION' | 'LOCATION') => {
    console.log('Ajouter contrat:', type);
    const typeLabels = {
      'SUBSCRIPTION': 'Abonnement',
      'LOCATION': 'Location'
    };
    alert(`Création d'un nouveau contrat:\nType: ${typeLabels[type]}\nOuverture du formulaire...`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export:', format);
    alert(`Export Excel des contrats en cours...\nFormat: ${format.toUpperCase()}\nTéléchargement démarré`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action:', action, 'Items:', items);
    const actionLabels: { [key: string]: string } = {
      'receipt': 'Générer reçu',
      'view': 'Afficher',
      'reload': 'Actualiser'
    };
    const actionLabel = actionLabels[action] || action;
    alert(`Action: ${actionLabel}\nNombre d'éléments sélectionnés: ${items.length}\nTraitement en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onAddContract={handleAddContract}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MesContratsLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onAddContract={(type) => console.log('Add:', type)}
          onExport={(format) => console.log('Export:', format)}
          onAction={(action, items) => console.log('Action:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MesContratsWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      contractNumber: 'CTR-2021-001',
      fileNumber: 'DOS-001',
      joinDate: new Date('2021-04-13'),
      date: new Date('2021-04-13'),
      startDate: new Date('2021-04-13'),
      referredBy: 'Dr. Martin',
      endDate: new Date('2022-04-13'),
      lastName: 'EL KANBI',
      firstName: 'ANAS',
      insurance: 'CPAM',
      assignment: 'Service A',
      type: 'Abonnement',
      city: 'Paris',
      technician: 'Tech-001',
      totalAmount: 1250.00,
      validation: true
    },
    {
      id: '2',
      contractNumber: 'CTR-2021-002',
      fileNumber: 'DOS-002',
      joinDate: new Date('2021-05-20'),
      date: new Date('2021-05-20'),
      startDate: new Date('2021-05-20'),
      referredBy: 'Dr. Dupont',
      endDate: new Date('2022-05-20'),
      lastName: 'MARTIN',
      firstName: 'SOPHIE',
      insurance: 'Mutuelle XYZ',
      assignment: 'Service B',
      type: 'Location',
      city: 'Lyon',
      technician: 'Tech-002',
      totalAmount: 850.00,
      validation: false
    },
    {
      id: '3',
      contractNumber: 'CTR-2021-003',
      fileNumber: 'DOS-003',
      joinDate: new Date('2021-06-15'),
      date: new Date('2021-06-15'),
      startDate: new Date('2021-06-15'),
      referredBy: 'Dr. Bernard',
      endDate: new Date('2022-06-15'),
      lastName: 'BERNARD',
      firstName: 'PAUL',
      insurance: '',
      assignment: 'Service C',
      type: 'Abonnement',
      city: 'Marseille',
      technician: 'Tech-003',
      totalAmount: 2100.00,
      validation: true
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    alert(`Recherche dans ${sampleItems.length} contrats:\nTerme: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nFiltres actifs: ${Object.keys(query.filters).length}`);
  };

  const handleAddContract = (type: 'SUBSCRIPTION' | 'LOCATION') => {
    console.log('Ajouter contrat avec données:', type);
    const typeLabels = {
      'SUBSCRIPTION': 'Abonnement',
      'LOCATION': 'Location'
    };
    alert(`Nouveau contrat ${typeLabels[type]}:\n\nProchain numéro: CTR-2021-${String(sampleItems.length + 1).padStart(3, '0')}\nFormulaire de création ouvert\nDonnées pré-remplies disponibles`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export avec données:', format);
    alert(`Export Excel de ${sampleItems.length} contrats:\n\nContenu:\n- ${sampleItems.filter(i => i.type === 'Abonnement').length} Abonnements\n- ${sampleItems.filter(i => i.type === 'Location').length} Locations\n- Montant total: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action avec données:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'receipt': `Génération de ${items.length} reçu(s):\n${items.map(item => `- ${item.lastName} ${item.firstName} (${item.contractNumber})`).join('\n')}`,
      'view': `Affichage de ${items.length} contrat(s):\n${items.map(item => `- ${item.contractNumber}: ${item.totalAmount.toFixed(2)}€`).join('\n')}`,
      'reload': `Actualisation des données:\n- ${sampleItems.length} contrats rechargés\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}`
    };
    
    const message = actionMessages[action] || `Action ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddContract={handleAddContract}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres
export function MesContratsFiltersDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtres:', query);
    if (query.searchAll) {
      alert(`Recherche avancée:\nTerme: "${query.searchAll}"\nRecherche dans: Numéros de contrat, Noms, Prénoms, Assurances, etc.`);
    }
    if (Object.keys(query.filters).length > 0) {
      alert(`Filtres appliqués:\n${Object.entries(query.filters).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`);
    }
  };

  const handleAddContract = (type: 'SUBSCRIPTION' | 'LOCATION') => {
    console.log('Ajouter avec filtres:', type);
    alert(`Nouveau contrat ${type}:\nLes filtres actuels seront conservés après création`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddContract={handleAddContract}
          onExport={(format) => alert(`Export ${format} avec filtres appliqués`)}
          onAction={(action, items) => console.log('Action filtres:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function MesContratsPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les contrats`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={false}
          items={[]}
          total={150} // Simule 150 contrats pour tester la pagination
          onQueryChange={handleQueryChange}
          onAddContract={(type) => console.log('Add pagination:', type)}
          onExport={(format) => console.log('Export pagination:', format)}
          onAction={(action, items) => console.log('Action pagination:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MesContratsErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleAddContract = (type: 'SUBSCRIPTION' | 'LOCATION') => {
    console.log('Ajouter avec validation:', type);
    if (confirm(`Êtes-vous sûr de vouloir créer un nouveau contrat de type ${type} ?`)) {
      alert('Contrat créé avec succès !');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesContrats
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddContract={handleAddContract}
          onExport={(format) => {
            if (confirm(`Êtes-vous sûr de vouloir exporter en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onAction={(action, items) => console.log('Action avec validation:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}
