
'use client';

import { useState, useEffect, useMemo } from 'react';
import { TextInput, Card, Modal, Group, Text,  MultiSelect, ActionIcon, Tooltip,CloseButton } from '@mantine/core';

import { DataTable, useDataTableColumns } from 'mantine-datatable';
import {  DatesRangeValue } from '@mantine/dates';
import { IconSearch, IconX, IconChevronUp, IconChevronDown, IconSelector } from '@tabler/icons-react';
import SimpleBar from 'simplebar-react';
import { useDebouncedValue } from '@mantine/hooks';
import dayjs from 'dayjs';
import "./DataTablePagination.css";


// import { companies, type Company } from '~/data';
import { Patient, Patients } from '@/data/patients';
import { Avatar } from '@mantine/core';


const PAGE_SIZES = [10, 15, 20,30];


 export const TableEncaissement = () => {
   const [pageSize, setPageSize] = useState(PAGE_SIZES[1]);
    const [page, setPage] = useState(1);
    
    // Filtering states
    const [query, setQuery] = useState('');
    const [debouncedQuery] = useDebouncedValue(query, 200);
    const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
    const [dateNaissanceSearchRange, ] = useState<DatesRangeValue>();
    const [seniors, ] = useState(false);
    
    // Column-specific filters
    const [dateCreation, setDateCreation] = useState('');
    const [sex, setSex] = useState('');
    const [cin, setCin] = useState('');
    const [dernierVisite, setDernierVisite] = useState('');
    const [telephone, setTelephone] = useState('');
    const [ville, setVille] = useState('');
    const [assurance, setAssurance] = useState('');
    
    // Global search
    const [search, setSearch] = useState('');
    const [debouncedSearch] = useDebouncedValue(search, 200);
    // Get unique departments for the filter dropdown
    const departments = useMemo(() => {
      const uniqueDepartmentIds = [...new Set(Patients.map(emp => emp.departmentId))];
      return uniqueDepartmentIds.map(id => ({
        value: id,
        label: `Department ${id}`
      }));
    }, []);
    // Filter records based on all criteria
    const filteredRecords = useMemo(() => {
      return Patients.filter((patient: Patient) => {
        // Global search across all fields
        if (debouncedSearch) {
          const searchText = debouncedSearch.toLowerCase().trim();
          const matchesSearch = 
            `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchText) ||
            patient.email?.toLowerCase().includes(searchText) ||
            patient.departmentId?.toLowerCase().includes(searchText) ||
            patient.cin?.toLowerCase().includes(searchText) ||
            patient.ville?.toLowerCase().includes(searchText) ||
            patient.assurance?.toLowerCase().includes(searchText) ||
            patient.telephone?.toLowerCase().includes(searchText) ||
            dayjs(patient.dateNaissance).format('MMM DD YYYY').toLowerCase().includes(searchText) ||
            String(dayjs().diff(patient.dateNaissance, 'y')).includes(searchText);
          if (!matchesSearch) return false;
        }
        // Column-specific filters
        if (dateCreation && !patient.dateCreation?.toLowerCase().includes(dateCreation.toLowerCase())) {
          return false;
        }
        
        if (sex && !patient.sex?.toLowerCase().includes(sex.toLowerCase())) {
          return false;
        }
        
        if (cin && !patient.cin?.toLowerCase().includes(cin.toLowerCase())) {
          return false;
        }
        
        if (dernierVisite && !patient.dernierVisite?.toLowerCase().includes(dernierVisite.toLowerCase())) {
          return false;
        }
        if (telephone && !patient.telephone?.toLowerCase().includes(telephone.toLowerCase())) {
          return false;
        }
        
        if (ville && !patient.ville?.toLowerCase().includes(ville.toLowerCase())) {
          return false;
        }
        if (assurance && !patient.assurance?.toLowerCase().includes(assurance.toLowerCase())) {
          return false;
        }
        // Name search (column-specific)
        if (
          debouncedQuery !== '' &&
          !`${patient.firstName} ${patient.lastName}`.toLowerCase().includes(debouncedQuery.trim().toLowerCase())
        ) {
          return false;
        }
        // Birthday range
        if (
          dateNaissanceSearchRange &&
          dateNaissanceSearchRange[0] &&
          dateNaissanceSearchRange[1] &&
          (dayjs(dateNaissanceSearchRange[0]).isAfter(patient.dateNaissance, 'day') ||
            dayjs(dateNaissanceSearchRange[1]).isBefore(patient.dateNaissance, 'day'))
        ) {
          return false;
        }
        // Department filter
        if (
          selectedDepartments.length && 
          !selectedDepartments.includes(patient.departmentId)
        ) {
          return false;
        }
        // Seniors filter
        if (seniors && dayjs().diff(patient.dateNaissance, 'y') < 70) {
          return false;
        }
        return true;
      });
    }, [
      debouncedQuery, 
      dateNaissanceSearchRange, 
      selectedDepartments, 
      seniors, 
      debouncedSearch,
      dateCreation,
      sex,
      cin,
      dernierVisite,
      telephone,
      ville,
      assurance
    ]);
  
    // Calculate paginated records
    const paginatedRecords = useMemo(() => {
      const from = (page - 1) * pageSize;
      const to = from + pageSize;
      return filteredRecords.slice(from, to);
    }, [filteredRecords, page, pageSize]);
  
    // Reset to first page when filters change
    useEffect(() => {
      setPage(1);
    }, [
      debouncedQuery, 
      dateNaissanceSearchRange, 
      selectedDepartments, 
      seniors, 
      pageSize, 
      debouncedSearch,
      dateCreation,
      sex,
      cin,
      dernierVisite,
      telephone,
      ville,
      assurance
    ]);
  const key = 'draggable-example';

  const { effectiveColumns, resetColumnsOrder } = useDataTableColumns<Patient>({
    key,
     columns:[
          {
            accessor: 'departmentId',
            title: '',
            filter: (
              <MultiSelect
                label="Departments"
                description="Show all Patients in the selected departments"
                data={departments}
                value={selectedDepartments}
                placeholder="Search departments…"
                onChange={setSelectedDepartments}
                leftSection={<IconSearch size={16} />}
                clearable
                searchable
              />
            ),
            filtering: selectedDepartments.length > 0,
            draggable: true,
            textAlign: 'left'
          },
          { 
            accessor: 'dateCreation', 
            title: 'Date Creation', 
            draggable: true, 
            textAlign: 'left', 
            filter: (
              <TextInput
                label="dateCreation"
                description="Show dateCreation whose names include the specified text"
                placeholder="Search dateCreation..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setDateCreation('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={dateCreation}
                onChange={(e) => setDateCreation(e.currentTarget.value)}
              />
            ),
            filtering: dateCreation !== ''
          },
          { 
            accessor: 'sex', 
            title: 'Sex', 
            draggable: true, 
            textAlign: 'left',
            filter: (
              <TextInput
                label="sex"
                description="Show sex whose names include the specified text"
                placeholder="Search sex..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setSex('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={sex}
                onChange={(e) => setSex(e.currentTarget.value)}
              />
            ),
            filtering: sex !== ''
          },
          {
            accessor: 'name',
            title: 'Full Name',
            render: ({ firstName, lastName }: Patient) => `${firstName} ${lastName}`,
            filter: (
              <TextInput
                label="Patients"
                description="Show Patients whose names include the specified text"
                placeholder="Search Patients..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setQuery('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={query}
                onChange={(e) => setQuery(e.currentTarget.value)}
              />
            ),
            filtering: query !== '',
            draggable: true,
            textAlign: 'left',
          },  
          {
            accessor: 'dateNaissance',
            title: 'Date Naissance',
            textAlign: 'left',
            render: ({ dateNaissance }: Patient) => dayjs(dateNaissance).format('MMM DD YYYY'),
            // filter: ({ close }) => (
            //   <Stack>
            //     <DatePicker
            //       maxDate={new Date()}
            //       type="range"
            //       value={dateNaissanceSearchRange}
            //       onChange={setdateNaissanceSearchRange}
            //     />
            //     <Button
            //       disabled={!dateNaissanceSearchRange}
            //       variant="light"
            //       onClick={() => {
            //         setdateNaissanceSearchRange(undefined);
            //         close();
            //       }}
            //     >
            //       Clear
            //     </Button>
            //   </Stack>
            // ),
            filtering: Boolean(dateNaissanceSearchRange),
            draggable: true
          },
          {
            accessor: 'age',
            title: 'Age',
            textAlign: 'left',
            render: ({ dateNaissance }: Patient) => dayjs().diff(dateNaissance, 'y'),
            // filter: () => (
            //   <Checkbox
            //     label="Seniors"
            //     description="Show Patients who are older than 70 years"
            //     checked={seniors}
            //     onChange={() => {
            //       setSeniors((current) => !current);
            //     }}
            //   />
            // ),
            filtering: seniors,
            draggable: true
          },
          { 
            accessor: 'cin', 
            title: 'CIN', 
            draggable: true, 
            textAlign: 'left', 
            filter: (
              <TextInput
                label="CIN"
                description="Show CIN whose names include the specified text"
                placeholder="Search CIN..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setCin('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={cin}
                onChange={(e) => setCin(e.currentTarget.value)}
              />
            ),
            filtering: cin !== ''
          },
          { 
            accessor: 'dernierVisite', 
            title: 'Dernier Visite', 
            draggable: true, 
            textAlign: 'left',
            filter: (
              <TextInput
                label="dernierVisite"
                description="Show dernierVisite whose names include the specified text"
                placeholder="Search dernierVisite..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setDernierVisite('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={dernierVisite}
                onChange={(e) => setDernierVisite(e.currentTarget.value)}
              />
            ),
            filtering: dernierVisite !== ''
          },
          { 
            accessor: 'telephone', 
            title: 'Telephone', 
            draggable: true, 
            textAlign: 'left', 
            filter: (
              <TextInput
                label="telephone"
                description="Show telephone whose names include the specified text"
                placeholder="Search telephone..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setTelephone('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={telephone}
                onChange={(e) => setTelephone(e.currentTarget.value)}
              />
            ),
            filtering: telephone !== ''
          },
          { 
            accessor: 'ville', 
            title: 'Ville', 
            draggable: true, 
            textAlign: 'left', 
            filter: (
              <TextInput
                label="ville"
                description="Show ville whose names include the specified text"
                placeholder="Search ville..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setVille('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={ville}
                onChange={(e) => setVille(e.currentTarget.value)}
              />
            ),
            filtering: ville !== ''
          },
          { 
            accessor: 'assurance', 
            title: 'Assurance', 
            draggable: true, 
            textAlign: 'left', 
            filter: (
              <TextInput
                label="assurance"
                description="Show assurance whose names include the specified text"
                placeholder="Search assurance..."
                leftSection={<IconSearch size={16} />}
                rightSection={
                  <ActionIcon size="sm" variant="transparent" c="dimmed" onClick={() => setAssurance('')}>
                    <IconX size={14} />
                  </ActionIcon>
                }
                value={assurance}
                onChange={(e) => setAssurance(e.currentTarget.value)}
              />
            ),
            filtering: assurance !== ''
          },
        ]
  });
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.currentTarget;
    setSearch(value);
  };
  const [sortColumn, setSortColumn] = useState<keyof Patient>('firstName');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const sortedRecords = useMemo(() => {
      return [...paginatedRecords].sort((a, b) => {
        const aValue = a[sortColumn];
        const bValue = b[sortColumn];
        const multiplier = sortDirection === 'asc' ? 1 : -1;
        
        if (aValue < bValue) return -1 * multiplier;
        if (aValue > bValue) return 1 * multiplier;
        return 0;
      });
    }, [paginatedRecords, sortColumn, sortDirection]);
    const [fetching, ] = useState(false);
    // This will hide the "Number of records per page" text while keeping the numbering information visible. Let me know if you need any adjustments!
    // useEffect(() => {
    //   document
    //     .querySelectorAll(".mantine-Text-root")
    //     .forEach((el) => {
    //       if (el.textContent?.trim() === "Records per page") {
    //         (el as HTMLElement).style.display = "none"; // ✅ Cast `el` to `HTMLElement`
    //       }
    //     });
    // }, []);
  
  return (
    <>
    <Modal.Content className="overflow-y-hidden">
      <Modal.Header style={{ height: '40px', background: "#3799CE", padding: "8px 11px", minHeight: "calc(1.75rem * var(--mantine-scale))" }}>
        <Modal.Title>
          <Group justify="space-between">
            {/* <RiFolderUserFill fill='var(--mantine-color-white)'/> */}
            <Text fw={500} c="var(--mantine-color-white)">Liste des patients</Text>
          </Group>
        </Modal.Title>
        <Modal.CloseButton />
      </Modal.Header>
      <Modal.Body>
      <Group>
         <TextInput
          placeholder="Search by any field"
          mb="md"
          leftSection={<IconSearch size={16} stroke={1.5} />}
          value={search}
          onChange={handleSearchChange}
          className="mt-4"
          rightSection={
            <CloseButton
              size={20} 
              aria-label="Clear input"
              onClick={() => setSearch('')}
            />
          }
          w={"96%"}
        /> 
        
        <Group justify="flex-end">
        <Tooltip
                    // label={t('top-title-Langue')} Reset Column Order
                    label="Réinitialiser l'ordre des colonnes"
                    withArrow
                    className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  >
             <Avatar  color="blue" radius="sm" onClick={resetColumnsOrder} h={34} ml="auto">
             {/* <GrPowerReset size={14} /> */}
           </Avatar>
           </Tooltip>
           </Group>
            </Group>
           
        <div className="h-[540px]">
          <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
            <Card shadow="sm" withBorder h={"h-[calc(100%)]"}>
      <DataTable
        withTableBorder
        striped
        highlightOnHover
        verticalSpacing="md"
        borderRadius="md"
        withColumnBorders
        storeColumnsKey={key}
        records={sortedRecords}
        columns={effectiveColumns}
        totalRecords={filteredRecords.length}
         recordsPerPage={pageSize}
        page={page}
         onPageChange={(p) => setPage(p)}
        recordsPerPageOptions={PAGE_SIZES}
        onRecordsPerPageChange={setPageSize}
        paginationSize="md"
         loadingText="Loading..."
          fetching={fetching}
        loaderType="bars"
        noRecordsText="No records found"
        // paginationText={() => ""} // Removes the text
        recordsPerPageLabel=""
      
        sortStatus={{ columnAccessor: sortColumn, direction: sortDirection }}
        onSortStatusChange={({ columnAccessor, direction }) => {
          setSortColumn(columnAccessor as keyof Patient);
          setSortDirection(direction as 'asc' | 'desc');
        }}
        sortIcons={{
          sorted: sortDirection === 'asc' ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />,
          unsorted: <IconSelector size={14} />
        }}
        // paginationText={({ from, to, totalRecords }) => (
        //   <Group >
        //      <span>{` ${from}-${to} of ${totalRecords} `}</span>  
        //      <Group justify="flex-end">
        //      <Avatar  color="blue" radius="sm" onClick={resetColumnsOrder} h={26} style={{width:"26px !important" }}ml="auto">
        //      <GrPowerReset size={14} />
        //   </Avatar>
        //   </Group>
        //   </Group>
        // )}
        // paginationText={({ from, to, totalRecords }) => (
        //   <Group>
        //     {/* <span>{`Showing ${from}-${to} of ${totalRecords} records`}</span> */}
        //     <span>{` ${from}-${to} of ${totalRecords} `}</span>
        //     <Avatar  color="blue" radius="sm" onClick={resetColumnsOrder} h={26} ml="84%">
        //     <GrPowerReset size={14} />
        //   </Avatar>
          
        //   </Group>
        // )}
        // getPaginationControlProps={(control) => {
        //   if (control === 'previous') {
        //     const title = 'Go to previous page';
        //     return { title, 'aria-label': title };
        //   } else if (control === 'next') {
        //     const title = 'Go to next page';
        //     return { title, 'aria-label': title };
        //   }
        //   return {};
        // }}

        // columnsMenuProps={{
        //   menuIconProps: {
        //     icon: <IconSettings />,
        //   },
        //   dragHandleIconProps: {
        //     icon: <IconDragDrop />,
        //   },
        //   hideIconProps: {
        //     icon: <IconEyeOff />,
        //   },
        // }}
      />
     
      </Card>
            </SimpleBar>
          </div>
        </Modal.Body>
      </Modal.Content>
    </>
  );
}
