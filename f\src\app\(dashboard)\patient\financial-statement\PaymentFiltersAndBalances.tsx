import { Switch, Group, Text } from '@mantine/core';
import { useState } from 'react';

type Balances = {
  patient: { value: number; sign: number };
  organizations: { value: number };
  balance: { value: number; sign: number };
};

export function PaymentFiltersAndBalances({ initialBalances }: { initialBalances: Balances }) {
  const [hidePaid, setHidePaid] = useState(false);
  const [hideNoAmount, setHideNoAmount] = useState(true);

  // Simule la fonction vm.filtersChange() appelée quand un switch change
  const onFiltersChange = () => {
    console.log('Filtres changés', { hidePaid, hideNoAmount });
    // Ici tu peux déclencher la mise à jour des données, requêtes, etc.
  };

  // Effets pour appeler onFiltersChange quand l’un des états change
  // (Optionnel, ou tu peux appeler onFiltersChange directement dans onChange)
  const handleHidePaidChange = (checked: boolean) => {
    setHidePaid(checked);
    // Appelle la fonction de filtre immédiatement après changement
    // ou utilise un useEffect pour écouter les changements
    onFiltersChange();
  };

  const handleHideNoAmountChange = (checked: boolean) => {
    setHideNoAmount(checked);
    onFiltersChange();
  };

  const { patient, organizations, balance } = initialBalances;

  return (
    <Group align="center" gap="xl" style={{ width: '100%' }}>
      <Switch
        label="Cacher les visites réglées"
        checked={hidePaid}
        onChange={(event) => handleHidePaidChange(event.currentTarget.checked)}
      />

      <Switch
        label="Cacher les visites sans montant"
        checked={hideNoAmount}
        onChange={(event) => handleHideNoAmountChange(event.currentTarget.checked)}
        disabled={hidePaid} // comme ng-disabled="vm.visitQuery.hide_paid"
      />

      <div style={{ flex: 1 }} />

      <Group gap="xs">
        <Text fw={700}>Balance :</Text>
        <Text>Patient</Text>
        <Text
          color={patient.sign > 0 ? 'green' : patient.sign < 0 ? 'red' : 'gray'}
          fw={600}
        >
          {patient.value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}
        </Text>

        <Text>Organismes</Text>
        <Text color={organizations.value < 0 ? 'red' : 'gray'} fw={600}>
          {organizations.value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}
        </Text>

        <Text>Total</Text>
        <Text
          color={balance.sign > 0 ? 'green' : balance.sign < 0 ? 'red' : 'gray'}
          fw={600}
        >
          {balance.value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}
        </Text>
      </Group>
    </Group>
  );
}




