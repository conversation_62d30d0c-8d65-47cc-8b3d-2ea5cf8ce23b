'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Radio,
  Switch,
  Select,
  Menu,
  Divider,
  Alert
} from '@mantine/core';
import { MonthPickerInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiCalendarMonth,
  mdiFilterMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiCog,
  mdiAlertCircleOutline
} from '@mdi/js';

// Types et interfaces
interface ActivityState {
  name: string;
  label: string;
  type: 'procedure' | 'dental' | 'medical' | 'encasement' | 'payment';
  deactivated?: boolean;
}

interface ProcedureActivityType {
  value: number;
  label: string;
}

interface FinancialSummary {
  total: number;
  encasement_total: number;
  loaded: boolean;
}

interface MonthlyActivityQuery {
  start: Date;
  use_discount: boolean;
}

interface ActiviteMensuelleProps {
  cycle?: 'monthly' | 'annual' | 'periodic';
  summary?: FinancialSummary;
  loading?: boolean;
  onQueryChange?: (query: MonthlyActivityQuery) => void;
  onActivityChange?: (activity: ActivityState) => void;
  onProcedureTypeChange?: (type: number) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
}

export const ActiviteMensuelle: React.FC<ActiviteMensuelleProps> = ({
  cycle = 'monthly',
  summary,
  loading = false,
  onQueryChange,
  onActivityChange,
  onProcedureTypeChange,
  onExport,
  onPrint
}) => {
  // États locaux
  const [query, setQuery] = useState<MonthlyActivityQuery>({
    start: new Date(),
    use_discount: true
  });

  const [selectedActivity, setSelectedActivity] = useState<ActivityState>({
    name: 'procedures',
    label: 'Les actes',
    type: 'procedure'
  });

  const [procedureActivityType, setProcedureActivityType] = useState<number>(0);

  // Données mockées
  const mockSummary: FinancialSummary = {
    total: 0.00,
    encasement_total: 0.00,
    loaded: true
  };

  const activityStates: ActivityState[] = [
    { name: 'procedures', label: 'Les actes', type: 'procedure' },
    { name: 'dental_procedures', label: 'Les actes dentaires', type: 'dental' },
    { name: 'medical_procedures', label: 'les actes de soins', type: 'medical' },
    { name: 'encasements', label: 'Encaissements', type: 'encasement' },
    { name: 'payments', label: 'Paiements', type: 'payment' }
  ];

  const procedureActivityTypes: ProcedureActivityType[] = [
    { value: 0, label: 'État en chiffre d\'affaire' },
    { value: 1, label: 'État en nombre d\'execution' },
    { value: 2, label: 'Les deux' }
  ];

  const currentSummary = summary || mockSummary;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<MonthlyActivityQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleActivityChange = (activity: ActivityState) => {
    setSelectedActivity(activity);
    onActivityChange?.(activity);
  };

  const handleProcedureTypeChange = (value: string | null) => {
    if (value !== null) {
      const type = parseInt(value);
      setProcedureActivityType(type);
      onProcedureTypeChange?.(type);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  // Fonctions utilitaires
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 2
    }).format(amount).replace('MAD', 'DH');
  };

  const getDateLabel = () => {
    switch (cycle) {
      case 'monthly':
        return 'Mois';
      case 'periodic':
        return 'Période';
      default:
        return 'Date';
    }
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiCalendarMonth} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Activité mensuelle</Text>
          </Group>

          {/* Résumé financier */}
          {currentSummary.loaded && (
            <Group gap="md" style={{ flex: 1, justifyContent: 'center' }}>
              <Text size="sm">
                <Text component="span" fw={500}>Total actes: </Text>
                <Text component="span" fw={700}>{formatCurrency(currentSummary.total)}</Text>
              </Text>
              <Text size="sm">
                <Text component="span" fw={500}>Total encaissements: </Text>
                <Text component="span" fw={700}>{formatCurrency(currentSummary.encasement_total)}</Text>
              </Text>
              <Text size="sm">
                <Text component="span" fw={500}>Différence: </Text>
                <Text
                  component="span"
                  fw={700}
                  c={currentSummary.encasement_total - currentSummary.total < 0 ? 'red' : 'green'}
                >
                  {formatCurrency(currentSummary.encasement_total - currentSummary.total)}
                </Text>
              </Text>
            </Group>
          )}

          {/* Bouton filtre */}
          <ActionIcon variant="subtle" size="lg">
            <Icon path={mdiFilterMultiple} size={1} />
          </ActionIcon>
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md">
          {/* Date picker pour le mois */}
          {cycle !== 'annual' && (
            <MonthPickerInput
              label={getDateLabel()}
              value={query.start}
              onChange={(value) => value && handleQueryChange({ start: value })}
              required
              style={{ width: 200 }}
            />
          )}

          {/* Source de données */}
          <Box style={{ marginLeft: 12 }}>
            <Text size="sm" fw={500} mb="xs">Source de données</Text>
            <Radio.Group
              value={selectedActivity.name}
              onChange={(value) => {
                const activity = activityStates.find(a => a.name === value);
                if (activity) handleActivityChange(activity);
              }}
            >
              <Group>
                {activityStates.map((state) => (
                  <Radio
                    key={state.name}
                    value={state.name}
                    label={state.label}
                    disabled={state.deactivated}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>

          <Box style={{ flex: 1 }} />

          {/* Switch remise globale */}
          {selectedActivity.type === 'procedure' && (
            <Switch
              label="Remise globale"
              checked={query.use_discount}
              onChange={(event) => handleQueryChange({ use_discount: event.currentTarget.checked })}
            />
          )}

          {/* Type d'état de procédure */}
          {selectedActivity.type === 'procedure' && (
            <Select
              label="Type d'état de procedure"
              value={procedureActivityType.toString()}
              onChange={handleProcedureTypeChange}
              data={procedureActivityTypes.map(type => ({
                value: type.value.toString(),
                label: type.label
              }))}
              style={{ width: 250 }}
            />
          )}
        </Group>
      </Paper>

      <Divider />

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Format">
                  <Icon path={mdiTableEdit} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Zone de contenu vide */}
            <Box style={{ height: 'calc(100vh - 300px)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Alert
                icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                title="Aucune donnée disponible"
                color="gray"
                variant="light"
                style={{ maxWidth: 400 }}
              >
                <Text size="sm" c="dimmed">
                  Aucune activité trouvée pour la période sélectionnée.
                  Veuillez vérifier les filtres ou sélectionner une autre période.
                </Text>
              </Alert>
            </Box>

            {/* Tableau vide (structure de base) */}
            <ScrollArea style={{ height: 0, overflow: 'hidden' }}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ minWidth: 120 }}>Médecin</Table.Th>
                    <Table.Th style={{ minWidth: 150 }}>Nom du patient</Table.Th>
                    <Table.Th style={{ minWidth: 130, textAlign: 'right' }}>Somme de Prix total</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  <Table.Tr>
                    <Table.Td colSpan={3} style={{ textAlign: 'center', padding: '2rem' }}>
                      <Text c="dimmed">Aucune donnée à afficher</Text>
                    </Table.Td>
                  </Table.Tr>
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Box>
        )}
      </Box>
    </Paper>
  );
};
