'use client';

import React, { useState,  } from 'react';
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  TextInput,
  Select,
  Divider,
  Alert,
  Stepper,
  
  Paper,
  Stack,
  Checkbox,

} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconAlertCircle, IconCreditCard } from '@tabler/icons-react';
import { useRouter, useSearchParams } from 'next/navigation';
// import { useAuth } from '~/hooks/useAuth';

const plans = {
  basic: {
    name: 'Basic',
    price: 29.99,
    color: 'blue',
  },
  professional: {
    name: 'Professional',
    price: 59.99,
    color: 'violet',
  },
  premium: {
    name: 'Premium',
    price: 99.99,
    color: 'green',
  },
};

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  // const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activeStep, setActiveStep] = useState(0);

  // Safely get URL parameters with null checking
  const planId = searchParams ? searchParams.get('plan') || 'basic' : 'basic';
  const billingCycle = searchParams ? searchParams.get('billing') || 'monthly' : 'monthly';

  // Ensure planId is a valid key in the plans object
  const validPlanId = Object.keys(plans).includes(planId) ? planId : 'basic';
  const plan = plans[validPlanId as keyof typeof plans];
  const price = billingCycle === 'annual' ? plan.price * 12 * 0.8 : plan.price;

  const form = useForm({
    initialValues: {
      cardName: '',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
      saveCard: false,
    },
    validate: {
      cardName: (value) => (value.length < 3 ? 'Name must be at least 3 characters' : null),
      cardNumber: (value) => (/^\d{16}$/.test(value) ? null : 'Card number must be 16 digits'),
      expiryMonth: (value) => (/^(0[1-9]|1[0-2])$/.test(value) ? null : 'Invalid month'),
      expiryYear: (value) => (/^20\d{2}$/.test(value) ? null : 'Invalid year'),
      cvv: (value) => (/^\d{3,4}$/.test(value) ? null : 'CVV must be 3 or 4 digits'),
      address: (value) => (value.length < 5 ? 'Address is too short' : null),
      city: (value) => (value.length < 2 ? 'City is too short' : null),
      zipCode: (value) => (/^\d{5}(-\d{4})?$/.test(value) ? null : 'Invalid ZIP code'),
    },
  });

  const handleSubmit = async () => {
    setLoading(true);

    try {
      // In a real app, you would call an API to process the payment with form.values
      // For now, we'll just simulate a successful payment
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Show success notification
      notifications.show({
        title: 'Payment Successful',
        message: `You have successfully subscribed to the ${plan.name} plan.`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // Redirect to dashboard
      router.push('/dashboard-simple');
    } catch (error) {
      console.error('Payment error:', error);

      // Show error notification
      notifications.show({
        title: 'Payment Failed',
        message: 'There was an error processing your payment. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    const fieldsToValidate = activeStep === 0
      ? ['cardName', 'cardNumber', 'expiryMonth', 'expiryYear', 'cvv']
      : ['address', 'city', 'state', 'zipCode', 'country'];

    const validation = form.validate();
    const hasErrors = fieldsToValidate.some(field => validation.errors[field]);

    if (!hasErrors) {
      setActiveStep((current) => current + 1);
    }
  };

  const prevStep = () => setActiveStep((current) => current - 1);

  return (
    <Container size="md" py="xl">
      <Title order={1} ta="center" mb="sm">
        Complete Your Subscription
      </Title>
      <Text c="dimmed" size="lg" ta="center" mb="xl">
        You&apos;re subscribing to the {plan.name} plan
      </Text>

      <Stepper active={activeStep} onStepClick={setActiveStep} mb="xl">
        <Stepper.Step label="Payment Information" description="Enter your card details">
          <Card withBorder p="xl" radius="md">
            <Title order={3} mb="md">Payment Information</Title>

            <form>
              <TextInput
                label="Name on Card"
                placeholder="John Smith"
                required
                mb="md"
                {...form.getInputProps('cardName')}
              />

              <TextInput
                label="Card Number"
                placeholder="1234 5678 9012 3456"
                required
                mb="md"
                leftSection={<IconCreditCard size={16} />}
                {...form.getInputProps('cardNumber')}
              />

              <Group grow mb="md">
                <TextInput
                  label="Expiry Month"
                  placeholder="MM"
                  required
                  {...form.getInputProps('expiryMonth')}
                />

                <TextInput
                  label="Expiry Year"
                  placeholder="YYYY"
                  required
                  {...form.getInputProps('expiryYear')}
                />

                <TextInput
                  label="CVV"
                  placeholder="123"
                  required
                  {...form.getInputProps('cvv')}
                />
              </Group>

              <Checkbox
                label="Save card for future payments"
                mt="md"
                {...form.getInputProps('saveCard', { type: 'checkbox' })}
              />
            </form>
          </Card>
        </Stepper.Step>

        <Stepper.Step label="Billing Address" description="Enter your billing address">
          <Card withBorder p="xl" radius="md">
            <Title order={3} mb="md">Billing Address</Title>

            <form>
              <TextInput
                label="Address"
                placeholder="123 Main St"
                required
                mb="md"
                {...form.getInputProps('address')}
              />

              <Group grow mb="md">
                <TextInput
                  label="City"
                  placeholder="New York"
                  required
                  {...form.getInputProps('city')}
                />

                <TextInput
                  label="State/Province"
                  placeholder="NY"
                  required
                  {...form.getInputProps('state')}
                />
              </Group>

              <Group grow mb="md">
                <TextInput
                  label="ZIP/Postal Code"
                  placeholder="10001"
                  required
                  {...form.getInputProps('zipCode')}
                />

                <Select
                  label="Country"
                  placeholder="Select country"
                  required
                  data={[
                    { value: 'US', label: 'United States' },
                    { value: 'CA', label: 'Canada' },
                    { value: 'UK', label: 'United Kingdom' },
                    { value: 'FR', label: 'France' },
                    { value: 'DE', label: 'Germany' },
                  ]}
                  {...form.getInputProps('country')}
                />
              </Group>
            </form>
          </Card>
        </Stepper.Step>

        <Stepper.Step label="Review & Confirm" description="Review your subscription">
          <Card withBorder p="xl" radius="md">
            <Title order={3} mb="md">Review Your Subscription</Title>

            <Paper withBorder p="md" radius="md" mb="xl">
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text fw={500}>Plan:</Text>
                  <Text>{plan.name}</Text>
                </Group>

                <Group justify="space-between">
                  <Text fw={500}>Billing Cycle:</Text>
                  <Text>{billingCycle === 'annual' ? 'Annual' : 'Monthly'}</Text>
                </Group>

                <Group justify="space-between">
                  <Text fw={500}>Price:</Text>
                  <Text>${price.toFixed(2)} {billingCycle === 'annual' ? '/year' : '/month'}</Text>
                </Group>

                {billingCycle === 'annual' && (
                  <Group justify="space-between">
                    <Text fw={500}>Savings:</Text>
                    <Text c="green">20% off</Text>
                  </Group>
                )}

                <Divider my="sm" />

                <Group justify="space-between">
                  <Text fw={700}>Total:</Text>
                  <Text fw={700} size="lg">${price.toFixed(2)}</Text>
                </Group>
              </Stack>
            </Paper>

            <Alert color="blue" mb="xl">
              By clicking &quot;Complete Subscription&quot;, you agree to our Terms of Service and authorize us to charge your card for the amount above.
            </Alert>
          </Card>
        </Stepper.Step>
      </Stepper>

      <Group justify="space-between" mt="xl">
        {activeStep > 0 && (
          <Button variant="default" onClick={prevStep}>
            Back
          </Button>
        )}

        {activeStep < 2 ? (
          <Button onClick={nextStep}>
            Next Step
          </Button>
        ) : (
          <Button
            color="green"
            onClick={handleSubmit}
            loading={loading}
          >
            Complete Subscription
          </Button>
        )}
      </Group>
    </Container>
  );
}
