/**
 * Generic States Quick Access Modal
 * Provides quick access to reports and analytics from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  DateInput,
  SimpleGrid,
} from '@mantine/core';
import {
  IconChartBar,
  IconCurrencyEuro,
  IconUsers,
  IconStethoscope,
  IconCalendarStats,
  IconX,
  IconExternalLink,
  IconRefresh,
  IconDownload,
} from '@tabler/icons-react';
import { useGenericStates } from '@/hooks/useGenericStates';
import DashboardWidgets from './DashboardWidgets';

// Import existing generic-states components
import { ActivitEjournaliere } from '@/app/(dashboard)/generic-states/daily-activities/ActivitEjournaliere';
import { Activiteperiodique } from '@/app/(dashboard)/generic-states/daily-activities/Activiteperiodique';
import { ActiviteMensuelle } from '@/app/(dashboard)/generic-states/daily-activities/ActiviteMensuelle';
import RapportDesRendezVous from '@/app/(dashboard)/generic-states/daily-activities/Rapport_des_rendez_vous';
import EcheanceDesCheques from '@/app/(dashboard)/generic-states/daily-activities/Echeance_des_chequeq';

interface GenericStatesQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  defaultTab?: 'dashboard' | 'activity' | 'financial' | 'patients' | 'appointments';
  dateRange?: { start: string; end: string };
  onNavigateToFullPage?: () => void;
}

const GenericStatesQuickAccess: React.FC<GenericStatesQuickAccessProps> = ({
  opened,
  onClose,
  defaultTab = 'dashboard',
  dateRange,
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [selectedDateRange, setSelectedDateRange] = useState(dateRange);
  const [reportType, setReportType] = useState<string>('daily');

  const {
    activityReports,
    financialReports,
    patientAnalytics,
    appointmentAnalytics,
    loading,
    refreshAll,
    getTotalRevenue,
    getTotalPatients,
    getTopProcedures,
  } = useGenericStates({ 
    dateRange: selectedDateRange, 
    autoFetch: opened,
    reportTypes: ['activity', 'financial', 'patient', 'appointment']
  });

  const handleRefresh = () => {
    refreshAll();
  };

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    if (start && end) {
      setSelectedDateRange({
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0],
      });
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <DashboardWidgets 
              dateRange={selectedDateRange}
              compact={false}
              showTrends={true}
            />
          </Stack>
        );

      case 'activity':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Text fw={600}>Rapports d'Activité</Text>
                <Select
                  value={reportType}
                  onChange={(value) => setReportType(value || 'daily')}
                  data={[
                    { value: 'daily', label: 'Journalier' },
                    { value: 'periodic', label: 'Périodique' },
                    { value: 'monthly', label: 'Mensuel' },
                    { value: 'annual', label: 'Annuel' },
                  ]}
                  size="xs"
                />
              </Group>
              
              <ScrollArea h={400}>
                {reportType === 'daily' && <ActivitEjournaliere />}
                {reportType === 'periodic' && <Activiteperiodique />}
                {reportType === 'monthly' && <ActiviteMensuelle />}
                {reportType === 'annual' && (
                  <Text size="sm" c="dimmed" ta="center" p="xl">
                    Rapport annuel - Fonctionnalité à venir
                  </Text>
                )}
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'financial':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconCurrencyEuro size={20} />
                  <Text fw={600}>Rapports Financiers</Text>
                  <Badge color="blue">{financialReports.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <EcheanceDesCheques />
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'patients':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconUsers size={20} />
                  <Text fw={600}>Analytique Patients</Text>
                  <Badge color="green">{getTotalPatients()}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                {patientAnalytics ? (
                  <SimpleGrid cols={2} spacing="md">
                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Répartition par âge</Text>
                      <Stack gap="xs">
                        {patientAnalytics.ageGroups.map((group) => (
                          <Group key={group.ageRange} justify="space-between">
                            <Text size="xs">{group.ageRange} ans</Text>
                            <Badge size="xs" color="blue">
                              {group.count} ({group.percentage}%)
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Pathologies courantes</Text>
                      <Stack gap="xs">
                        {patientAnalytics.pathologies.slice(0, 5).map((pathology) => (
                          <Group key={pathology.id} justify="space-between">
                            <Text size="xs">{pathology.name}</Text>
                            <Badge 
                              size="xs" 
                              color={pathology.severity === 'high' ? 'red' : pathology.severity === 'medium' ? 'orange' : 'green'}
                            >
                              {pathology.count} ({pathology.percentage}%)
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Assurances</Text>
                      <Stack gap="xs">
                        {patientAnalytics.insuranceDistribution.map((insurance) => (
                          <Group key={insurance.provider} justify="space-between">
                            <Text size="xs">{insurance.provider}</Text>
                            <Badge size="xs" color="purple">
                              {insurance.count} ({insurance.percentage}%)
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Statistiques générales</Text>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="xs">Total patients</Text>
                          <Text size="xs" fw={500}>{patientAnalytics.totalPatients}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Nouveaux patients</Text>
                          <Text size="xs" fw={500} c="green">+{patientAnalytics.newPatients}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Hommes</Text>
                          <Text size="xs" fw={500}>{patientAnalytics.genderDistribution.male}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Femmes</Text>
                          <Text size="xs" fw={500}>{patientAnalytics.genderDistribution.female}</Text>
                        </Group>
                      </Stack>
                    </Card>
                  </SimpleGrid>
                ) : (
                  <Text size="sm" c="dimmed" ta="center" p="xl">
                    Aucune donnée patient disponible
                  </Text>
                )}
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'appointments':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconCalendarStats size={20} />
                  <Text fw={600}>Analytique Rendez-vous</Text>
                  <Badge color="orange">{appointmentAnalytics?.totalAppointments || 0}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <RapportDesRendezVous />
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  const totalRevenue = getTotalRevenue();
  const totalPatients = getTotalPatients();
  const topProcedures = getTopProcedures(3);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconChartBar size={20} />
          <Text fw={600}>États et Rapports</Text>
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Group gap="xs">
            <Text size="sm" c="dimmed">
              Accès rapide aux rapports et analyses
            </Text>
            {selectedDateRange && (
              <Badge size="sm" variant="light">
                {selectedDateRange.start} - {selectedDateRange.end}
              </Badge>
            )}
          </Group>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconCurrencyEuro size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Revenus</Text>
                <Text size="sm" fw={600}>{totalRevenue.toLocaleString()}€</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconUsers size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Patients</Text>
                <Text size="sm" fw={600}>{totalPatients}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconStethoscope size={16} color="orange" />
              <div>
                <Text size="xs" c="dimmed">Procédures</Text>
                <Text size="sm" fw={600}>{topProcedures.reduce((sum, p) => sum + p.count, 0)}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconCalendarStats size={16} color="purple" />
              <div>
                <Text size="xs" c="dimmed">RDV</Text>
                <Text size="sm" fw={600}>{appointmentAnalytics?.totalAppointments || 0}</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconChartBar size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab value="activity" leftSection={<IconCalendarStats size={16} />}>
              Activité
            </Tabs.Tab>
            <Tabs.Tab value="financial" leftSection={<IconCurrencyEuro size={16} />}>
              Financier
            </Tabs.Tab>
            <Tabs.Tab value="patients" leftSection={<IconUsers size={16} />}>
              Patients
            </Tabs.Tab>
            <Tabs.Tab value="appointments" leftSection={<IconCalendarStats size={16} />}>
              Rendez-vous
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default GenericStatesQuickAccess;
