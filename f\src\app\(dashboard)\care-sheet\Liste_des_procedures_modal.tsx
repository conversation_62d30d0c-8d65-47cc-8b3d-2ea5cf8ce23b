'use client';
import React, { useState } from 'react';
import {
  Modal,
  Table,
  Text,
  Group,
  Button,
  Checkbox,
  Select,
  Pagination,
  TextInput,
  ActionIcon,
} from '@mantine/core';
import { IconSearch, IconFilter, IconRefresh, IconMenu2 } from '@tabler/icons-react';

interface ListeDesProceduresModalProps {
  opened: boolean;
  onClose: () => void;
}

// Données d'exemple pour les procédures
const proceduresData = [
  { code: 'RTR', nom: 'Rétroalvéolaire' },
  { code: 'RXP', nom: 'Rx Panoramique' },
  { code: 'CBM', nom: 'Cone Beam' },
  { code: 'CIB', nom: 'Camera Intrabuccale' },
  { code: 'HBD', nom: 'Motivation à l\'HBD' },
  { code: 'EME', nom: 'Empreinte D\'étude' },
  { code: 'ANL', nom: 'Analyse Biologique' },
  { code: 'SPR', nom: 'Sondage Parodontal' },
  { code: 'DET', nom: 'Détartrage' },
  { code: 'AEP', nom: 'Aéropolissage' },
  { code: 'AFS', nom: 'Ablation de fil de suture' },
  { code: 'COT', nom: 'Contention' },
  { code: 'CLT', nom: 'Consultation' },
  { code: 'ORD', nom: 'Ordonnance' },
  { code: 'CRI', nom: 'Curetage interdentaire' },
];

const Liste_des_procedures_modal = ({ opened, onClose }: ListeDesProceduresModalProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProcedures, setSelectedProcedures] = useState<string[]>([]);

  const filteredProcedures = proceduresData.filter(procedure =>
    procedure.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    procedure.nom.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredProcedures.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentProcedures = filteredProcedures.slice(startIndex, startIndex + itemsPerPage);

  const handleSelectProcedure = (code: string) => {
    setSelectedProcedures(prev =>
      prev.includes(code)
        ? prev.filter(c => c !== code)
        : [...prev, code]
    );
  };

  const handleSelectAll = () => {
    if (selectedProcedures.length === currentProcedures.length) {
      setSelectedProcedures([]);
    } else {
      setSelectedProcedures(currentProcedures.map(p => p.code));
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="📋 Liste des procédures"
      size="90%"
      centered
    >
      <div className="bg-white">
        {/* Barre de recherche */}
        <div className="border-b border-gray-200 p-3 bg-gray-50">
          <Group gap="sm" align="center">
            <ActionIcon variant="subtle" color="blue" size="sm">
              <IconFilter size={16} />
            </ActionIcon>
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="flex-1"
              size="sm"
            />
            <ActionIcon variant="subtle" color="gray" size="sm">
              <IconRefresh size={16} />
            </ActionIcon>
            <ActionIcon variant="subtle" color="gray" size="sm">
              <IconMenu2 size={16} />
            </ActionIcon>
            <ActionIcon variant="subtle" color="gray" size="sm">
              <IconMenu2 size={16} />
            </ActionIcon>
          </Group>
        </div>

        {/* Tableau des procédures */}
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
        >
          <Table.Thead className="bg-gray-50">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox 
                  size="sm"
                  checked={selectedProcedures.length === currentProcedures.length && currentProcedures.length > 0}
                  indeterminate={selectedProcedures.length > 0 && selectedProcedures.length < currentProcedures.length}
                  onChange={handleSelectAll}
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Code
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                Nom
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentProcedures.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={3} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Rechercher
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentProcedures.map((procedure) => (
                <Table.Tr key={procedure.code} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      size="sm"
                      checked={selectedProcedures.includes(procedure.code)}
                      onChange={() => handleSelectProcedure(procedure.code)}
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800 font-medium">
                      {procedure.code}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm" className="text-gray-800">
                      {procedure.nom}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>

        {/* Footer avec pagination */}
        <div className="border-t border-gray-300 bg-gray-50 p-3 mt-4">
          <Group justify="space-between" align="center">
            <Group gap="sm" align="center">
              <Text size="sm" className="text-gray-600">Page</Text>
              <Select
                value={currentPage.toString()}
                onChange={(value) => setCurrentPage(Number(value) || 1)}
                data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
                size="xs"
                className="w-16"
              />
              <Text size="sm" className="text-gray-600">Lignes par Page</Text>
              <Select
                value={itemsPerPage.toString()}
                onChange={(value) => setItemsPerPage(Number(value) || 15)}
                data={['15', '25', '50']}
                size="xs"
                className="w-16"
              />
              <Text size="sm" className="text-gray-600">
                1 - {Math.min(itemsPerPage, filteredProcedures.length)} de {filteredProcedures.length}
              </Text>
              <Text size="sm" className="text-gray-600">K</Text>
            </Group>

            <Pagination
              total={totalPages || 1}
              value={currentPage}
              onChange={setCurrentPage}
              size="sm"
            />
          </Group>
        </div>

        {/* Boutons d'action */}
        <Group justify="flex-end" gap="sm" className="mt-4">
          <Button
            color="blue"
            size="sm"
            leftSection={<IconSearch size={16} />}
          >
            Ajouter
          </Button>
          <Button
            variant="outline"
            color="red"
            size="sm"
            onClick={onClose}
          >
            Annuler
          </Button>
        </Group>
      </div>
    </Modal>
  );
};

export default Liste_des_procedures_modal;
