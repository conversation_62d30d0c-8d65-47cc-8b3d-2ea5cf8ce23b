/**
 * Care Sheet Service
 * Handles all care-sheet related data operations including:
 * - Mutuelles (Insurance) management
 * - Patient visits tracking
 * - Dental procedures
 * - Quotes/estimates (devis)
 */

// Types for Care Sheet data
export interface MutuelleData {
  id: number;
  date: string;
  organisme: string;
  patient: string;
  patientId?: string;
  nomAssure: string;
  numeroAffiliate: string;
  beneficiaire: 'PATIENT' | 'CONJOINT' | 'ENFANT';
  montant: number;
  etat: 'validee' | 'non-validee' | 'en-attente';
  numeroDossier?: string;
  dateCreation?: string;
  dateModification?: string;
}

export interface VisitData {
  id: number;
  date: string;
  patientId: string;
  patientName: string;
  typeVisite: string;
  description: string;
  docteur: string;
  statut: 'planifiee' | 'en-cours' | 'terminee' | 'annulee';
  duree: number;
  notes?: string;
  cout?: number;
}

export interface ProcedureData {
  id: number;
  date: string;
  patientId: string;
  patientName: string;
  typeProcedure: string;
  description: string;
  dentiste: string;
  dent?: string;
  statut: 'planifiee' | 'en-cours' | 'terminee';
  cout: number;
  mutuelleCouverte?: boolean;
}

export interface DevisData {
  id: number;
  date: string;
  patientId: string;
  patientName: string;
  numeroDevis: string;
  description: string;
  montantTotal: number;
  statut: 'brouillon' | 'envoye' | 'accepte' | 'refuse';
  validiteJusqu?: string;
  procedures: ProcedureData[];
}

export interface CareSheetSummary {
  patientId: string;
  mutuelles: MutuelleData[];
  visites: VisitData[];
  procedures: ProcedureData[];
  devis: DevisData[];
  lastUpdate: string;
}

class CareSheetService {
  private baseURL = '/api/care-sheet';

  // Mutuelles (Insurance) operations
  async getMutuelles(patientId?: string): Promise<MutuelleData[]> {
    try {
      const url = patientId 
        ? `${this.baseURL}/mutuelles?patientId=${patientId}`
        : `${this.baseURL}/mutuelles`;
      
      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Mutuelles API not available (${response.status}), using mock data`);
        return this.getMockMutuelles(patientId);
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Mutuelles API error, using mock data:', error);
      // Return mock data for development
      return this.getMockMutuelles(patientId);
    }
  }

  async createMutuelle(mutuelleData: Omit<MutuelleData, 'id'>): Promise<MutuelleData> {
    try {
      const response = await fetch(`${this.baseURL}/mutuelles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mutuelleData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create mutuelle: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating mutuelle:', error);
      // Return mock created data
      return {
        id: Date.now(),
        ...mutuelleData,
        dateCreation: new Date().toISOString(),
      };
    }
  }

  async updateMutuelle(id: number, mutuelleData: Partial<MutuelleData>): Promise<MutuelleData> {
    try {
      const response = await fetch(`${this.baseURL}/mutuelles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mutuelleData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update mutuelle: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error updating mutuelle:', error);
      throw error;
    }
  }

  async deleteMutuelle(id: number): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/mutuelles/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete mutuelle: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting mutuelle:', error);
      // For mock purposes, we'll just log the deletion
      console.log(`Mock deletion of mutuelle ${id}`);
    }
  }

  // Visits operations
  async getVisits(patientId?: string): Promise<VisitData[]> {
    try {
      const url = patientId 
        ? `${this.baseURL}/visits?patientId=${patientId}`
        : `${this.baseURL}/visits`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch visits: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching visits:', error);
      return this.getMockVisits(patientId);
    }
  }

  // Procedures operations
  async getProcedures(patientId?: string): Promise<ProcedureData[]> {
    try {
      const url = patientId 
        ? `${this.baseURL}/procedures?patientId=${patientId}`
        : `${this.baseURL}/procedures`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch procedures: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching procedures:', error);
      return this.getMockProcedures(patientId);
    }
  }

  // Devis operations
  async getDevis(patientId?: string): Promise<DevisData[]> {
    try {
      const url = patientId 
        ? `${this.baseURL}/devis?patientId=${patientId}`
        : `${this.baseURL}/devis`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch devis: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching devis:', error);
      return this.getMockDevis(patientId);
    }
  }

  // Get comprehensive care sheet summary for a patient
  async getCareSheetSummary(patientId: string): Promise<CareSheetSummary> {
    try {
      const [mutuelles, visites, procedures, devis] = await Promise.all([
        this.getMutuelles(patientId),
        this.getVisits(patientId),
        this.getProcedures(patientId),
        this.getDevis(patientId),
      ]);

      return {
        patientId,
        mutuelles,
        visites,
        procedures,
        devis,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching care sheet summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockMutuelles(patientId?: string): MutuelleData[] {
    const mockData: MutuelleData[] = [
      {
        id: 1,
        date: '2024-01-15',
        organisme: 'CNOPS',
        patient: 'Jean Dupont',
        patientId: patientId || '1',
        nomAssure: 'Jean Dupont',
        numeroAffiliate: 'CN123456',
        beneficiaire: 'PATIENT',
        montant: 1500,
        etat: 'validee',
        numeroDossier: 'DOS001',
      },
      {
        id: 2,
        date: '2024-02-10',
        organisme: 'AMO',
        patient: 'Marie Martin',
        patientId: patientId || '2',
        nomAssure: 'Marie Martin',
        numeroAffiliate: 'AM789012',
        beneficiaire: 'PATIENT',
        montant: 800,
        etat: 'en-attente',
        numeroDossier: 'DOS002',
      },
    ];

    return patientId ? mockData.filter(m => m.patientId === patientId) : mockData;
  }

  private getMockVisits(patientId?: string): VisitData[] {
    const mockData: VisitData[] = [
      {
        id: 1,
        date: '2024-01-20',
        patientId: patientId || '1',
        patientName: 'Jean Dupont',
        typeVisite: 'Consultation',
        description: 'Contrôle de routine',
        docteur: 'Dr. Martin',
        statut: 'terminee',
        duree: 30,
        cout: 150,
      },
      {
        id: 2,
        date: '2024-02-15',
        patientId: patientId || '1',
        patientName: 'Jean Dupont',
        typeVisite: 'Urgence',
        description: 'Douleur dentaire',
        docteur: 'Dr. Dubois',
        statut: 'terminee',
        duree: 45,
        cout: 200,
      },
    ];

    return patientId ? mockData.filter(v => v.patientId === patientId) : mockData;
  }

  private getMockProcedures(patientId?: string): ProcedureData[] {
    const mockData: ProcedureData[] = [
      {
        id: 1,
        date: '2024-01-25',
        patientId: patientId || '1',
        patientName: 'Jean Dupont',
        typeProcedure: 'Plombage',
        description: 'Plombage dent 16',
        dentiste: 'Dr. Martin',
        dent: '16',
        statut: 'terminee',
        cout: 300,
        mutuelleCouverte: true,
      },
    ];

    return patientId ? mockData.filter(p => p.patientId === patientId) : mockData;
  }

  private getMockDevis(patientId?: string): DevisData[] {
    const mockData: DevisData[] = [
      {
        id: 1,
        date: '2024-02-01',
        patientId: patientId || '1',
        patientName: 'Jean Dupont',
        numeroDevis: 'DEV001',
        description: 'Traitement orthodontique',
        montantTotal: 2500,
        statut: 'envoye',
        validiteJusqu: '2024-03-01',
        procedures: [],
      },
    ];

    return patientId ? mockData.filter(d => d.patientId === patientId) : mockData;
  }
}

export const careSheetService = new CareSheetService();
export default careSheetService;
