import api from '../lib/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Define a custom error interface for API errors
export interface ApiError extends Error {
  userTypeError?: boolean;
  response?: {
    data?: {
      detail?: string;
      message?: string;
      [key: string]: unknown;
    };
    status?: number;
    statusText?: string;
  };
  isAxiosError?: boolean;
  code?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  userType?: string; // Optional user type for role-based login
}

export interface RegisterData {
  email: string;
  password: string;
  password2: string;
  first_name: string;
  last_name: string;
  user_type: string;
  // Doctor-specific fields
  specialization?: string;
  license_number?: string;
  // Common optional fields
  phone_number?: string;
  address?: string;
  country?: string;
  region?: string;
  city?: string;
  // Subscription information
  subscription_period?: string;
  // Package information
  package_id?: string;
  package_name?: string;
  package_price?: string;
  billing_cycle?: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface Specialty {
  id: number;
  name: string;
  description: string | null;
  slug: string;
  icon: string | null;
  is_active: boolean;
  doctor_count: number;
}

export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type: string;
  // Doctor-specific fields
  specialization?: string;
  specialty?: string;  // Added specialty field
  license_number?: string;
  bio?: string;
  profile_image?: string;
  profile_image_medium?: string;
  profile_image_large?: string;
  years_of_experience?: number;
  education?: string;
  certifications?: string;
  hospital_affiliations?: string;
  languages?: string;
  consultation_fee?: number;
  accepting_new_patients?: boolean;
  // Common optional fields
  phone_number?: string;
  landline_number?: string;
  address?: string;
  country?: string;
  region?: string;
  city?: string;
  country_name?: string;
  region_name?: string;
  city_name?: string;
  // Social media fields
  facebook_url?: string;
  twitter_url?: string;
  linkedin_url?: string;
  youtube_url?: string;
  telegram_url?: string;
  whatsapp_number?: string;
  // Clinic photos
  clinic_photo_1?: string;
  clinic_photo_2?: string;
  clinic_photo_3?: string;
  // Trial information
  is_trial?: boolean;
  trial_start_date?: string;
  trial_end_date?: string;
  trial_duration_months?: number;
  is_trial_active?: boolean;
  trial_days_remaining?: number;
}

const authService = {
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    try {
      const { email, password, userType } = credentials;
      console.log('AuthService: Attempting login with:', email, 'as', userType);

      // Use different endpoints based on user type
      let endpoint = '/api/auth/token/';
      let loginData = {
        email: email,
        password: password,
        user_type: userType
      };

      // Use specialized endpoints for assistant and staff
      if (userType === 'assistant') {
        console.log('AuthService: Using assistant login endpoint');
        endpoint = '/api/auth/login/assistant/';
      } else if (userType === 'staff') {
        console.log('AuthService: Using staff login endpoint');
        endpoint = '/api/auth/login/staff/';
      } else {
        // For doctor and other types, use the standard endpoint with user_type
        loginData = {
          ...loginData,
          user_type: userType
        };
      }

      console.log('AuthService: Login data being sent:', {
        email: loginData.email,
        endpoint: endpoint
      });

      // Call the auth service to log in the user
      console.log(`AuthService: Sending request to ${endpoint}`);
      let response;
      try {
        response = await api.post(endpoint, loginData);
        console.log('AuthService: Raw response:', response);

        // Check if the response contains the expected tokens
        if (!response.data.access || !response.data.refresh) {
          console.error('AuthService: Missing tokens in response:', response.data);
          throw new Error('Authentication failed: Missing tokens in response');
        }
      } catch (error) {
        console.error(`AuthService: Error calling ${endpoint}:`, error);

        // Try fallback to standard token endpoint if specialized endpoint fails
        if (endpoint !== '/api/auth/token/' && (userType === 'assistant' || userType === 'staff')) {
          console.log('AuthService: Trying fallback to standard token endpoint');
          const fallbackData = {
            email: email,
            password: password,
            user_type: userType
          };

          try {
            response = await api.post('/api/auth/token/', fallbackData);
            console.log('AuthService: Fallback response:', response);
          } catch (fallbackError) {
            console.error('AuthService: Fallback also failed:', fallbackError);
            throw fallbackError;
          }
        } else {
          throw error;
        }
      }

      const { access, refresh, user_type } = response.data;

      console.log('AuthService: Login successful, tokens received:', {
        access: access ? `${access.substring(0, 10)}...` : 'missing',
        refresh: refresh ? `${refresh.substring(0, 10)}...` : 'missing',
        user_type: user_type || 'not provided'
      });

      // Verify that the returned user_type matches the requested user_type
      if (user_type && userType && user_type !== userType) {
        console.warn(`AuthService: User type mismatch. Requested ${userType} but got ${user_type}`);

        // Special handling for assistant/staff user types
        // Allow admin users to log in as staff
        if (userType === 'staff' && user_type === 'admin') {
          console.log('AuthService: Admin user logging in as staff, allowing this');
        }
        // Allow assistants to log in as staff
        else if (userType === 'staff' && user_type === 'assistant') {
          console.log('AuthService: Assistant user logging in as staff, allowing this');
        }
        // Allow staff to log in as assistant
        else if (userType === 'assistant' && user_type === 'staff') {
          console.log('AuthService: Staff user logging in as assistant, allowing this');
        }
      }

      // Store tokens in localStorage
      console.log('AuthService: Storing tokens in localStorage');
      localStorage.setItem('token', access);
      localStorage.setItem('refreshToken', refresh);

      // Store user type for future reference
      if (user_type) {
        localStorage.setItem('userType', user_type);

        // Also store user type in a cookie for the middleware
        console.log('AuthService: Setting userType cookie:', user_type);
        document.cookie = `userType=${user_type}; path=/; max-age=86400; SameSite=Lax`;
      }

      // Store user ID if available
      if (response.data.user_id || response.data.id || response.data.uuid) {
        const userId = response.data.user_id || response.data.id || response.data.uuid;
        console.log('AuthService: Storing user ID in localStorage:', userId);
        localStorage.setItem('userId', userId);
      }

      // Also set a cookie for the middleware
      console.log('AuthService: Setting token cookie');
      // Set the cookie with SameSite=Lax to ensure it's sent with navigation requests
      document.cookie = `token=${access}; path=/; max-age=86400; SameSite=Lax`;

      // Log the token for debugging
      console.log('Token stored in localStorage:', access.substring(0, 10) + '...');
      console.log('Cookie set with token:', access.substring(0, 10) + '...');

      // Verify the token was set correctly
      setTimeout(() => {
        const storedToken = localStorage.getItem('token');
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);

        console.log('Verification - localStorage token:', storedToken ? storedToken.substring(0, 10) + '...' : 'none');
        console.log('Verification - cookie token:', cookies.token ? cookies.token.substring(0, 10) + '...' : 'none');
      }, 100);

      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      console.error('AuthService: Login error:', apiError);
      console.error('AuthService: Error response:', apiError.response?.data);
      console.error('AuthService: Error status:', apiError.response?.status);

      // Add more specific error handling for user type issues
      if (apiError.response?.data?.detail &&
          typeof apiError.response.data.detail === 'string' &&
          apiError.response.data.detail.toLowerCase().includes('user type')) {
        console.error('AuthService: User type error detected');
        apiError.userTypeError = true;
      }

      throw apiError;
    }
  },

  async register(data: RegisterData): Promise<{ token?: string; user?: UserProfile; message?: string }> {
    try {
      console.log('Registration data received:', data);

      // Prepare the data for the backend
      // Make sure we only include fields that the backend expects
      const registerData = {
        email: data.email,
        password: data.password,
        password2: data.password2 || data.password,
        first_name: data.first_name,
        last_name: data.last_name,
        user_type: data.user_type || 'doctor',
        // Include optional fields only if they exist
        ...(data.specialization && { specialization: data.specialization }),
        ...(data.license_number && { license_number: data.license_number }),
        ...(data.phone_number && { phone_number: data.phone_number }),
        ...(data.address && { address: data.address }),
        // Include subscription information
        ...(data.subscription_period && { subscription_period: data.subscription_period }),
        // Include package information
        ...(data.package_id && { package_id: data.package_id }),
        ...(data.package_name && { package_name: data.package_name }),
        ...(data.package_price && { package_price: data.package_price }),
        ...(data.billing_cycle && { billing_cycle: data.billing_cycle })
      };

      console.log('Sending registration data to backend:', registerData);

      const response = await api.post('/api/auth/register/', registerData);
      console.log('Registration response:', response.data);
      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      console.error('Registration error:', apiError);
      console.error('Error response:', apiError.response?.data);

      // Log specific error details if available
      if (apiError.response?.data) {
        if (typeof apiError.response.data === 'object') {
          Object.entries(apiError.response.data).forEach(([field, errors]) => {
            console.error(`Field '${field}' errors:`, errors);
          });
        }
      }

      throw apiError;
    }
  },

  async logout(): Promise<void> {
    // Clear all authentication data from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userType');
    localStorage.removeItem('assignedDoctor');
    localStorage.removeItem('assignedDoctorName');
    localStorage.removeItem('specialty');
    localStorage.removeItem('userId'); // Clear user ID

    // Also clear any cookies that might be used for authentication
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'userType=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

    console.log('AuthService: Logged out, all tokens and cookies cleared');
  },

  async getProfile(): Promise<UserProfile> {
    try {
      const response = await api.get('/api/auth/me/');

      // Process image URLs
      const profileData = response.data;

      // Store user ID in localStorage
      if (profileData.id || profileData.uuid) {
        const userId = profileData.id || profileData.uuid;
        console.log('AuthService: Storing user ID from profile in localStorage:', userId);
        localStorage.setItem('userId', userId);
      }

      // Convert relative image URLs to absolute URLs if they exist
      if (profileData.profile_image && typeof profileData.profile_image === 'string' && !profileData.profile_image.startsWith('http')) {
        profileData.profile_image = `${API_URL}${profileData.profile_image}`;
      }

      if (profileData.profile_image_medium && typeof profileData.profile_image_medium === 'string' && !profileData.profile_image_medium.startsWith('http')) {
        profileData.profile_image_medium = `${API_URL}${profileData.profile_image_medium}`;
      }

      if (profileData.profile_image_large && typeof profileData.profile_image_large === 'string' && !profileData.profile_image_large.startsWith('http')) {
        profileData.profile_image_large = `${API_URL}${profileData.profile_image_large}`;
      }

      console.log('Auth service - processed profile image URLs:', {
        profile_image: profileData.profile_image || 'none',
        profile_image_medium: profileData.profile_image_medium || 'none'
      });

      return profileData;
    } catch (error) {
      console.error('Get profile error:', error);

      // Don't use mock data - always require proper authentication
      console.log('Authentication required - no mock data fallback');

      throw error;
    }
  },

  // Get the current user with role information
  async getCurrentUser(): Promise<{
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    role: string;
    user_type: string;
    specialization?: string;
    specialty?: string;
    license_number?: string;
    profile_image?: string;
    profile_image_medium?: string;
    profile_image_large?: string;
  }> {
    try {
      // First try to get the user profile
      const profile = await this.getProfile();

      // Map user_type to role for consistency across applications
      const role = profile.user_type === 'admin' ? 'admin' :
                  profile.user_type === 'supervisor' ? 'supervisor' :
                  profile.user_type === 'doctor' ? 'doctor' : 'patient';

      return {
        id: profile.id,
        email: profile.email,
        first_name: profile.first_name,
        last_name: profile.last_name,
        role: role,
        user_type: profile.user_type,
        specialization: profile.specialization,
        specialty: profile.specialty,
        license_number: profile.license_number,
        profile_image: profile.profile_image,
        profile_image_medium: profile.profile_image_medium,
        profile_image_large: profile.profile_image_large
      };
    } catch (error) {
      console.error('Get current user error:', error);

      // Always require authentication - no fallback
      throw error;
    }
  },

  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token;
  },

  // Validate authentication by checking token validity with the server
  async validateAuthentication(): Promise<boolean> {
    try {
      // If there's no token, user is not authenticated
      if (!this.isAuthenticated()) {
        return false;
      }

      // Try to get the user profile to validate the token
      await this.getProfile();
      return true;
    } catch (error) {
      console.error('Authentication validation error:', error);

      // If we get a 401 error, the token is invalid
      const apiError = error as ApiError;
      if (apiError && apiError.response && apiError.response.status === 401) {
        // Try to refresh the token
        try {
          await this.refreshToken();
          return true;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (_refreshError) {
          // If refresh fails, user is not authenticated
          this.logout();
          return false;
        }
      }

      // For other errors, assume the user is not authenticated
      return false;
    }
  },

  // Refresh the access token
  async refreshToken(): Promise<string> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post('/api/auth/token/refresh/', {
        refresh: refreshToken,
      });

      const { access } = response.data;
      localStorage.setItem('token', access);

      return access;
    } catch (error) {
      console.error('Refresh token error:', error);
      this.logout();
      throw error;
    }
  },

  // Request a password reset
  async forgotPassword(email: string): Promise<void> {
    try {
      await api.post('/api/auth/password/reset/', { email });
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    }
  },

  // Reset the password
  async resetPassword(data: { token: string; email: string; password: string }): Promise<void> {
    try {
      await api.post('/api/auth/password/reset/confirm/', data);
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  },

  // Verify the reset token
  async verifyResetToken(token: string, email: string): Promise<boolean> {
    try {
      // In a real implementation, we would validate the token with the backend
      // For now, we'll just return true for any token
      console.log(`Verifying reset token for email: ${email} with token: ${token.substring(0, 5)}...`);
      return true;
    } catch (error) {
      console.error('Verify reset token error:', error);
      return false;
    }
  },

  // Verify the email
  async verifyEmail(token: string, email: string): Promise<void> {
    try {
      await api.post('/api/auth/verify-email/', { token, email });
    } catch (error) {
      console.error('Verify email error:', error);
      throw error;
    }
  },

  // Get all active specialties
  async getSpecialties(): Promise<Specialty[]> {
    try {
      const response = await api.get('/api/auth/specialties/');
      console.log('Fetched specialties:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching specialties:', error);

      // In development mode, return mock data
      if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
        console.log('Using mock specialties data');
        return [
          { id: 1, name: 'Cardiology', description: 'Heart and cardiovascular system', slug: 'cardiology', icon: 'fa-heart', is_active: true, doctor_count: 5 },
          { id: 2, name: 'Dermatology', description: 'Skin conditions', slug: 'dermatology', icon: 'fa-user', is_active: true, doctor_count: 3 },
          { id: 3, name: 'Endocrinology', description: 'Hormonal disorders', slug: 'endocrinology', icon: 'fa-flask', is_active: true, doctor_count: 2 },
          { id: 4, name: 'Gastroenterology', description: 'Digestive system', slug: 'gastroenterology', icon: 'fa-utensils', is_active: true, doctor_count: 4 },
          { id: 5, name: 'Neurology', description: 'Brain and nervous system', slug: 'neurology', icon: 'fa-brain', is_active: true, doctor_count: 3 },
          { id: 6, name: 'Obstetrics & Gynecology', description: 'Women\'s health', slug: 'obstetrics', icon: 'fa-female', is_active: true, doctor_count: 6 },
          { id: 7, name: 'Oncology', description: 'Cancer treatment', slug: 'oncology', icon: 'fa-ribbon', is_active: true, doctor_count: 2 },
          { id: 8, name: 'Ophthalmology', description: 'Eye care', slug: 'ophthalmology', icon: 'fa-eye', is_active: true, doctor_count: 3 },
          { id: 9, name: 'Orthopedics', description: 'Musculoskeletal system', slug: 'orthopedics', icon: 'fa-bone', is_active: true, doctor_count: 4 },
          { id: 10, name: 'Pediatrics', description: 'Children\'s health', slug: 'pediatrics', icon: 'fa-child', is_active: true, doctor_count: 5 },
          { id: 11, name: 'Psychiatry', description: 'Mental health', slug: 'psychiatry', icon: 'fa-brain', is_active: true, doctor_count: 3 },
          { id: 12, name: 'Radiology', description: 'Medical imaging', slug: 'radiology', icon: 'fa-x-ray', is_active: true, doctor_count: 2 },
          { id: 13, name: 'Urology', description: 'Urinary system', slug: 'urology', icon: 'fa-toilet', is_active: true, doctor_count: 3 },
          { id: 14, name: 'Other', description: 'Other specialties', slug: 'other', icon: 'fa-plus', is_active: true, doctor_count: 1 },
        ];
      }

      throw error;
    }
  },

  // Get specialty subdomains mapping
  async getSpecialtySubdomains(): Promise<Record<string, string>> {
    try {
      const specialties = await this.getSpecialties();

      // Create a mapping of subdomain to slug
      const subdomainMap: Record<string, string> = {};

      // Map common specialty slugs to subdomains
      specialties.forEach(specialty => {
        let subdomain = '';

        // Create subdomain based on specialty slug
        switch (specialty.slug) {
          case 'cardiology':
            subdomain = 'cardio';
            break;
          case 'dermatology':
            subdomain = 'derma';
            break;
          case 'neurology':
            subdomain = 'neuro';
            break;
          case 'orthopedics':
            subdomain = 'ortho';
            break;
          case 'pediatrics':
            subdomain = 'pedia';
            break;
          case 'gastroenterology':
            subdomain = 'gastro';
            break;
          case 'ophthalmology':
            subdomain = 'ophtha';
            break;
          case 'obstetrics-gynecology':
          case 'obstetrics':
            subdomain = 'obgyn';
            break;
          case 'psychiatry':
            subdomain = 'psych';
            break;
          case 'oncology':
            subdomain = 'onco';
            break;
          default:
            // For other specialties, use the first 5 characters of the slug
            subdomain = specialty.slug.substring(0, 5);
        }

        if (subdomain) {
          subdomainMap[subdomain] = specialty.slug;
        }
      });

      console.log('Generated specialty subdomain mapping:', subdomainMap);
      return subdomainMap;
    } catch (error) {
      console.error('Error generating specialty subdomains:', error);

      // Return default mapping if there's an error
      return {
        'cardio': 'cardiology',
        'derma': 'dermatology',
        'neuro': 'neurology',
        'ortho': 'orthopedics',
        'pedia': 'pediatrics',
        'gastro': 'gastroenterology',
        'ophtha': 'ophthalmology',
        'obgyn': 'obstetrics',
        'psych': 'psychiatry',
        'onco': 'oncology'
      };
    }
  },
};

export default authService;
