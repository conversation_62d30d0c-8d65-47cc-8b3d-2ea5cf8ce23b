'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  ActionIcon,
  Card,
  Stack,
  Text,
  Badge,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Pagination,
  Box,
  Center,
  Textarea,
  Divider,
  NumberInput,
  Table,
  ScrollArea,
  
  Tooltip,
  ThemeIcon,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconPhoto,
  IconUpload,
  IconEye,
  IconEdit,
  IconTrash,
  IconFilter,
  IconCurrencyEuro,
  IconCalendar,
  IconUser,
  IconTag,
 
  IconTrendingDown,
  IconCheck,
  IconX,
  IconClock,
  IconAlertTriangle,
  IconDownload,
  IconShare,
  IconPrinter,
  IconMail,
  IconFileText,
  IconReceipt,
  IconDiscount,
  IconChartBar,
} from '@tabler/icons-react';

interface PricingImageItem {
  id: string;
  title: string;
  documentType: string;
  imageUrl: string;
  thumbnail: string;
  category: string;
  basePrice: number;
  discountedPrice?: number;
  discountPercentage?: number;
  currency: string;
  validFrom: Date;
  validUntil: Date;
  status: 'active' | 'expired' | 'pending' | 'draft';
  targetAudience: string;
  uploadedBy: string;
  uploadDate: Date;
  description: string;
  tags: string[];
  productCode?: string;
  supplier?: string;
  minimumQuantity?: number;
  maximumQuantity?: number;
}

export default function PricingImagesGallery() {
  const [pricingImages, setPricingImages] = useState<PricingImageItem[]>([
    {
      id: '1',
      title: 'Tarifs Médicaments Génériques 2024',
      documentType: 'Liste de prix',
      imageUrl: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop',
      category: 'Médicaments',
      basePrice: 25.50,
      discountedPrice: 22.95,
      discountPercentage: 10,
      currency: 'EUR',
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2024-12-31'),
      status: 'active',
      targetAudience: 'Pharmacies partenaires',
      uploadedBy: 'Dr. Martin',
      uploadDate: new Date('2024-01-15'),
      description: 'Liste complète des tarifs pour médicaments génériques avec remises volume',
      tags: ['médicaments', 'génériques', 'tarifs', '2024'],
      productCode: 'MED-GEN-2024',
      supplier: 'Laboratoire Pharma Plus',
      minimumQuantity: 10,
      maximumQuantity: 1000,
    },
    {
      id: '2',
      title: 'Promotion Équipements Médicaux',
      documentType: 'Offre promotionnelle',
      imageUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
      category: 'Équipements',
      basePrice: 450.00,
      discountedPrice: 360.00,
      discountPercentage: 20,
      currency: 'EUR',
      validFrom: new Date('2024-01-10'),
      validUntil: new Date('2024-02-29'),
      status: 'active',
      targetAudience: 'Hôpitaux et cliniques',
      uploadedBy: 'Commercial Sophie',
      uploadDate: new Date('2024-01-12'),
      description: 'Promotion spéciale sur équipements médicaux - 20% de réduction',
      tags: ['équipements', 'promotion', 'réduction', 'hôpitaux'],
      productCode: 'EQUIP-PROMO-2024',
      supplier: 'MedEquip Solutions',
      minimumQuantity: 5,
      maximumQuantity: 50,
    },
    {
      id: '3',
      title: 'Grille Tarifaire Services Consultation',
      documentType: 'Grille tarifaire',
      imageUrl: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=200&fit=crop',
      category: 'Services',
      basePrice: 75.00,
      currency: 'EUR',
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2024-06-30'),
      status: 'expired',
      targetAudience: 'Patients particuliers',
      uploadedBy: 'Dr. Dubois',
      uploadDate: new Date('2023-12-20'),
      description: 'Tarifs des consultations médicales et services associés',
      tags: ['consultation', 'services', 'tarifs', 'patients'],
      productCode: 'SERV-CONS-2024',
    },
    {
      id: '4',
      title: 'Catalogue Produits Cosmétiques',
      documentType: 'Catalogue prix',
      imageUrl: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=300&h=200&fit=crop',
      category: 'Cosmétiques',
      basePrice: 35.90,
      discountedPrice: 29.90,
      discountPercentage: 17,
      currency: 'EUR',
      validFrom: new Date('2024-02-01'),
      validUntil: new Date('2024-04-30'),
      status: 'pending',
      targetAudience: 'Pharmacies de détail',
      uploadedBy: 'Responsable Achat',
      uploadDate: new Date('2024-01-25'),
      description: 'Nouveau catalogue de produits cosmétiques avec tarifs préférentiels',
      tags: ['cosmétiques', 'catalogue', 'beauté', 'soins'],
      productCode: 'COSM-CAT-2024',
      supplier: 'Beauty Care International',
      minimumQuantity: 20,
      maximumQuantity: 500,
    },
    {
      id: '5',
      title: 'Tarifs Spéciaux Vaccins',
      documentType: 'Tarif spécial',
      imageUrl: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
      category: 'Vaccins',
      basePrice: 125.00,
      currency: 'EUR',
      validFrom: new Date('2024-01-20'),
      validUntil: new Date('2024-12-31'),
      status: 'draft',
      targetAudience: 'Centres de vaccination',
      uploadedBy: 'Dr. Claire',
      uploadDate: new Date('2024-01-22'),
      description: 'Tarification spéciale pour campagnes de vaccination',
      tags: ['vaccins', 'campagne', 'prévention', 'santé'],
      productCode: 'VACC-SPEC-2024',
      supplier: 'VacciPharma',
      minimumQuantity: 100,
      maximumQuantity: 10000,
    },
  ]);

  const [selectedImage, setSelectedImage] = useState<PricingImageItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  const [opened, { open, close }] = useDisclosure(false);
  const [viewerOpened, { open: openViewer, close: closeViewer }] = useDisclosure(false);
  const [uploadOpened, { open: openUpload, close: closeUpload }] = useDisclosure(false);

  const form = useForm({
    initialValues: {
      title: '',
      documentType: '',
      category: '',
      basePrice: 0,
      discountedPrice: 0,
      targetAudience: '',
      description: '',
      tags: '',
      productCode: '',
      supplier: '',
      minimumQuantity: 0,
      maximumQuantity: 0,
      file: null as File | null,
    },
  });

  const categories = [
    'Médicaments',
    'Équipements',
    'Services',
    'Cosmétiques',
    'Vaccins',
    'Matériel médical',
    'Compléments',
  ];

  const documentTypes = [
    'Liste de prix',
    'Offre promotionnelle',
    'Grille tarifaire',
    'Catalogue prix',
    'Tarif spécial',
    'Devis',
    'Barème',
  ];

  const statusOptions = [
    { value: 'active', label: 'Actif', color: 'green' },
    { value: 'expired', label: 'Expiré', color: 'red' },
    { value: 'pending', label: 'En attente', color: 'orange' },
    { value: 'draft', label: 'Brouillon', color: 'gray' },
  ];

  const targetAudiences = [
    'Pharmacies partenaires',
    'Hôpitaux et cliniques',
    'Patients particuliers',
    'Pharmacies de détail',
    'Centres de vaccination',
    'Professionnels de santé',
  ];

  const itemsPerPage = 12;

  const filteredImages = pricingImages.filter((item) => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.productCode && item.productCode.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = !selectedCategory || item.category === selectedCategory;
    const matchesStatus = !selectedStatus || item.status === selectedStatus;
    const matchesDocumentType = !selectedDocumentType || item.documentType === selectedDocumentType;
    return matchesSearch && matchesCategory && matchesStatus && matchesDocumentType;
  });

  const totalPages = Math.ceil(filteredImages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedImages = filteredImages.slice(startIndex, startIndex + itemsPerPage);

  const getStatusColor = (status: string) => {
    const statusMap = {
      'active': 'green',
      'expired': 'red',
      'pending': 'orange',
      'draft': 'gray',
    };
    return statusMap[status as keyof typeof statusMap] || 'gray';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <IconCheck size={16} />;
      case 'expired': return <IconX size={16} />;
      case 'pending': return <IconClock size={16} />;
      case 'draft': return <IconFileText size={16} />;
      default: return <IconTag size={16} />;
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const calculateSavings = (basePrice: number, discountedPrice?: number) => {
    if (!discountedPrice) return 0;
    return basePrice - discountedPrice;
  };

  const isExpiringSoon = (validUntil: Date) => {
    const today = new Date();
    const daysUntilExpiry = Math.ceil((validUntil.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  const handleImageUpload = async (values: typeof form.values) => {
    if (!values.file) return;

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      const discountPercentage = values.discountedPrice > 0 ? 
        Math.round(((values.basePrice - values.discountedPrice) / values.basePrice) * 100) : undefined;

      const newItem: PricingImageItem = {
        id: Date.now().toString(),
        title: values.title,
        documentType: values.documentType,
        imageUrl: URL.createObjectURL(values.file),
        thumbnail: URL.createObjectURL(values.file),
        category: values.category,
        basePrice: values.basePrice,
        discountedPrice: values.discountedPrice > 0 ? values.discountedPrice : undefined,
        discountPercentage,
        currency: 'EUR',
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        status: 'draft',
        targetAudience: values.targetAudience,
        uploadedBy: 'Utilisateur actuel',
        uploadDate: new Date(),
        description: values.description,
        tags: values.tags.split(',').map(tag => tag.trim()),
        productCode: values.productCode,
        supplier: values.supplier,
        minimumQuantity: values.minimumQuantity > 0 ? values.minimumQuantity : undefined,
        maximumQuantity: values.maximumQuantity > 0 ? values.maximumQuantity : undefined,
      };

      setPricingImages(prev => [newItem, ...prev]);
      form.reset();
      closeUpload();

      notifications.show({
        title: 'Succès',
        message: 'Document tarifaire ajouté avec succès',
        color: 'green',
      });
    } catch {
  notifications.show({
    title: 'Erreur',
    message: 'Erreur lors de l\'ajout du document',
    color: 'red',
  });
    }
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconCurrencyEuro size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Galerie Tarification avec Images
          </Title>
        </Group>
        <Group>
          <Button
            leftSection={<IconUpload size={16} />}
            onClick={openUpload}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Ajouter Tarif
          </Button>
          <Button
            variant="outline"
            leftSection={<IconFilter size={16} />}
            onClick={open}
          >
            Filtres
          </Button>
        </Group>
      </Group>

      {/* Search and Filters */}
      <Grid mb="md">
        <Grid.Col span={3}>
          <TextInput
            placeholder="Rechercher tarifs..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Catégorie"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Statut"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Type document"
            data={documentTypes}
            value={selectedDocumentType}
            onChange={setSelectedDocumentType}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Group>
            <ActionIcon
              variant={viewMode === 'grid' ? 'filled' : 'outline'}
              onClick={() => setViewMode('grid')}
            >
              <IconPhoto size={16} />
            </ActionIcon>
            <ActionIcon
              variant={viewMode === 'table' ? 'filled' : 'outline'}
              onClick={() => setViewMode('table')}
            >
              <IconChartBar size={16} />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>

      {/* Content Display */}
      {paginatedImages.length === 0 ? (
        <Center h={300}>
          <Stack align="center">
            <IconCurrencyEuro size={48} className="text-gray-400" />
            <Text c="dimmed">Aucun document tarifaire trouvé</Text>
          </Stack>
        </Center>
      ) : viewMode === 'grid' ? (
        <SimpleGrid
          cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
          spacing="md"
          mb="xl"
        >
          {paginatedImages.map((item) => (
            <Card key={item.id} withBorder shadow="sm" className="hover:shadow-md transition-shadow">
              <Card.Section>
                <Box pos="relative">
                  <Image
                    src={item.thumbnail}
                    height={200}
                    alt={item.title}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}
                  />
                  <Badge
                    pos="absolute"
                    top={8}
                    right={8}
                    color={getStatusColor(item.status)}
                    leftSection={getStatusIcon(item.status)}
                  >
                    {statusOptions.find(s => s.value === item.status)?.label}
                  </Badge>
                  <Badge
                    pos="absolute"
                    top={8}
                    left={8}
                    color="blue"
                    variant="light"
                  >
                    {item.documentType}
                  </Badge>
                  {item.discountPercentage && (
                    <Badge
                      pos="absolute"
                      bottom={8}
                      right={8}
                      color="red"
                      leftSection={<IconDiscount size={14} />}
                    >
                      -{item.discountPercentage}%
                    </Badge>
                  )}
                  {isExpiringSoon(item.validUntil) && (
                    <Badge
                      pos="absolute"
                      bottom={8}
                      left={8}
                      color="orange"
                      leftSection={<IconAlertTriangle size={14} />}
                    >
                      Expire bientôt
                    </Badge>
                  )}
                </Box>
              </Card.Section>

              <Stack gap="xs" mt="md">
                <Group justify="space-between">
                  <Text fw={500} size="sm" truncate>
                    {item.title}
                  </Text>
                  <ThemeIcon size="sm" color="blue" variant="light">
                    <IconTag size={14} />
                  </ThemeIcon>
                </Group>

                <Text size="xs" c="dimmed" lineClamp={2}>
                  {item.description}
                </Text>

                <Group justify="space-between">
                  <Stack gap={2}>
                    {item.discountedPrice ? (
                      <>
                        <Text size="xs" td="line-through" c="dimmed">
                          {formatCurrency(item.basePrice, item.currency)}
                        </Text>
                        <Text size="sm" fw={600} c="red">
                          {formatCurrency(item.discountedPrice, item.currency)}
                        </Text>
                      </>
                    ) : (
                      <Text size="sm" fw={600} c="blue">
                        {formatCurrency(item.basePrice, item.currency)}
                      </Text>
                    )}
                  </Stack>
                  <Badge size="xs" color="gray" variant="light">
                    {item.category}
                  </Badge>
                </Group>

                <Group justify="space-between">
                  <Text size="xs" c="dimmed">
                    {item.targetAudience}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Expire: {item.validUntil.toLocaleDateString()}
                  </Text>
                </Group>

                <Group justify="space-between" mt="xs">
                  <Group gap="xs">
                    {item.discountedPrice && (
                      <Tooltip label={`Économie: ${formatCurrency(calculateSavings(item.basePrice, item.discountedPrice), item.currency)}`}>
                        <ThemeIcon size="sm" color="green" variant="light">
                          <IconTrendingDown size={12} />
                        </ThemeIcon>
                      </Tooltip>
                    )}
                    {item.productCode && (
                      <Tooltip label={`Code: ${item.productCode}`}>
                        <ThemeIcon size="sm" color="gray" variant="light">
                          <IconReceipt size={12} />
                        </ThemeIcon>
                      </Tooltip>
                    )}
                  </Group>
                  <Group gap="xs">
                    <ActionIcon size="sm" variant="subtle" onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}>
                      <IconEye size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle">
                      <IconEdit size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle">
                      <IconPrinter size={14} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>
      ) : (
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Document</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Catégorie</Table.Th>
                <Table.Th>Prix de base</Table.Th>
                <Table.Th>Prix réduit</Table.Th>
                <Table.Th>Réduction</Table.Th>
                <Table.Th>Validité</Table.Th>
                <Table.Th>Statut</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedImages.map((item) => (
                <Table.Tr key={item.id}>
                  <Table.Td>
                    <Group gap="sm">
                      <Image
                        src={item.thumbnail}
                        width={50}
                        height={35}
                        alt={item.title}
                        className="cursor-pointer rounded"
                        onClick={() => {
                          setSelectedImage(item);
                          openViewer();
                        }}
                      />
                      <Stack gap={2}>
                        <Text fw={500} size="sm">{item.title}</Text>
                        <Text size="xs" c="dimmed">{item.productCode}</Text>
                      </Stack>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Badge size="sm" color="blue" variant="light">
                      {item.documentType}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{item.category}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500} c="blue">
                      {formatCurrency(item.basePrice, item.currency)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    {item.discountedPrice ? (
                      <Text fw={600} c="red">
                        {formatCurrency(item.discountedPrice, item.currency)}
                      </Text>
                    ) : (
                      <Text c="dimmed">-</Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    {item.discountPercentage ? (
                      <Badge color="red" leftSection={<IconDiscount size={14} />}>
                        -{item.discountPercentage}%
                      </Badge>
                    ) : (
                      <Text c="dimmed">-</Text>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Stack gap={2}>
                      <Text size="xs">Jusqu&apos;au</Text>
                      <Text size="sm" fw={500}>
                        {item.validUntil.toLocaleDateString()}
                      </Text>
                      {isExpiringSoon(item.validUntil) && (
                        <Badge size="xs" color="orange">
                          Expire bientôt
                        </Badge>
                      )}
                    </Stack>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={getStatusColor(item.status)}
                      leftSection={getStatusIcon(item.status)}
                    >
                      {statusOptions.find(s => s.value === item.status)?.label}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon size="sm" variant="subtle" onClick={() => {
                        setSelectedImage(item);
                        openViewer();
                      }}>
                        <IconEye size={14} />
                      </ActionIcon>
                      <ActionIcon size="sm" variant="subtle">
                        <IconEdit size={14} />
                      </ActionIcon>
                      <ActionIcon size="sm" variant="subtle">
                        <IconPrinter size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Group justify="center" mt="xl">
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
          />
        </Group>
      )}

      {/* Upload Modal */}
      <Modal opened={uploadOpened} onClose={closeUpload} title="Ajouter un document tarifaire" size="lg">
        <form onSubmit={form.onSubmit(handleImageUpload)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Titre du document"
                  placeholder="Ex: Tarifs Médicaments 2024"
                  {...form.getInputProps('title')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Type de document"
                  placeholder="Sélectionner le type"
                  data={documentTypes}
                  {...form.getInputProps('documentType')}
                  required
                />
              </Grid.Col>
            </Grid>

            <FileInput
              label="Image du document"
              placeholder="Sélectionner une image"
              accept="image/*"
              leftSection={<IconUpload size={16} />}
              {...form.getInputProps('file')}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Catégorie"
                  placeholder="Sélectionner une catégorie"
                  data={categories}
                  {...form.getInputProps('category')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Public cible"
                  placeholder="Sélectionner le public"
                  data={targetAudiences}
                  {...form.getInputProps('targetAudience')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Prix de base (EUR)"
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  {...form.getInputProps('basePrice')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Prix réduit (EUR)"
                  placeholder="0.00 (optionnel)"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  {...form.getInputProps('discountedPrice')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code produit"
                  placeholder="Ex: MED-GEN-2024"
                  {...form.getInputProps('productCode')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Fournisseur"
                  placeholder="Ex: Laboratoire Pharma Plus"
                  {...form.getInputProps('supplier')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Quantité minimum"
                  placeholder="0"
                  min={0}
                  {...form.getInputProps('minimumQuantity')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Quantité maximum"
                  placeholder="0"
                  min={0}
                  {...form.getInputProps('maximumQuantity')}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Description"
              placeholder="Description du document tarifaire"
              rows={3}
              {...form.getInputProps('description')}
            />

            <TextInput
              label="Tags"
              placeholder="Tags séparés par des virgules"
              {...form.getInputProps('tags')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={closeUpload}>
                Annuler
              </Button>
              <Button type="submit" leftSection={<IconUpload size={16} />}>
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Pricing Document Viewer Modal */}
      <Modal
        opened={viewerOpened}
        onClose={closeViewer}
        title={selectedImage?.title}
        size="xl"
        centered
      >
        {selectedImage && (
          <Stack>
            <Image
              src={selectedImage.imageUrl}
              alt={selectedImage.title}
              fit="contain"
              h={400}
            />

            <Grid>
              <Grid.Col span={8}>
                <Stack gap="xs">
                  <Group>
                    <Text fw={500} size="lg">{selectedImage.title}</Text>
                    <Badge color="blue">{selectedImage.documentType}</Badge>
                    <Badge
                      color={getStatusColor(selectedImage.status)}
                      leftSection={getStatusIcon(selectedImage.status)}
                    >
                      {statusOptions.find(s => s.value === selectedImage.status)?.label}
                    </Badge>
                  </Group>

                  <Text size="sm" c="dimmed">{selectedImage.description}</Text>

                  <Group gap="xs">
                    {selectedImage.tags.map((tag, index) => (
                      <Badge key={index} size="sm" variant="light">
                        {tag}
                      </Badge>
                    ))}
                  </Group>

                  <Divider />

                  <Group>
                    <Text size="sm" fw={500}>Prix:</Text>
                    {selectedImage.discountedPrice ? (
                      <Group gap="xs">
                        <Text size="sm" td="line-through" c="dimmed">
                          {formatCurrency(selectedImage.basePrice, selectedImage.currency)}
                        </Text>
                        <Text size="lg" fw={600} c="red">
                          {formatCurrency(selectedImage.discountedPrice, selectedImage.currency)}
                        </Text>
                        <Badge color="red" leftSection={<IconDiscount size={14} />}>
                          -{selectedImage.discountPercentage}%
                        </Badge>
                      </Group>
                    ) : (
                      <Text size="lg" fw={600} c="blue">
                        {formatCurrency(selectedImage.basePrice, selectedImage.currency)}
                      </Text>
                    )}
                  </Group>

                  {selectedImage.discountedPrice && (
                    <Group>
                      <Text size="sm" fw={500}>Économie:</Text>
                      <Text size="sm" c="green" fw={600}>
                        {formatCurrency(calculateSavings(selectedImage.basePrice, selectedImage.discountedPrice), selectedImage.currency)}
                      </Text>
                    </Group>
                  )}

                  <Group>
                    <Text size="sm">Public cible: {selectedImage.targetAudience}</Text>
                  </Group>

                  {selectedImage.minimumQuantity && selectedImage.maximumQuantity && (
                    <Group>
                      <Text size="sm">
                        Quantités: {selectedImage.minimumQuantity} - {selectedImage.maximumQuantity} unités
                      </Text>
                    </Group>
                  )}
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="xs">
                  <Group>
                    <IconTag size={16} />
                    <Text size="sm">{selectedImage.category}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">Valide du: {selectedImage.validFrom.toLocaleDateString()}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">Jusqu&apos;au: {selectedImage.validUntil.toLocaleDateString()}</Text>
                  </Group>

                  <Group>
                    <IconUser size={16} />
                    <Text size="sm">{selectedImage.uploadedBy}</Text>
                  </Group>

                  {selectedImage.productCode && (
                    <Group>
                      <IconReceipt size={16} />
                      <Text size="sm">{selectedImage.productCode}</Text>
                    </Group>
                  )}

                  {selectedImage.supplier && (
                    <Group>
                      <IconUser size={16} />
                      <Text size="sm">{selectedImage.supplier}</Text>
                    </Group>
                  )}

                  {isExpiringSoon(selectedImage.validUntil) && (
                    <Badge color="orange" leftSection={<IconAlertTriangle size={14} />}>
                      Expire dans {Math.ceil((selectedImage.validUntil.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} jours
                    </Badge>
                  )}
                </Stack>
              </Grid.Col>
            </Grid>

            <Group justify="space-between" mt="md">
              <Group>
                <ActionIcon variant="outline">
                  <IconShare size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconDownload size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconPrinter size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconMail size={16} />
                </ActionIcon>
              </Group>

              <Group>
                <Button variant="outline" leftSection={<IconEdit size={16} />}>
                  Modifier
                </Button>
                <Button variant="outline" color="red" leftSection={<IconTrash size={16} />}>
                  Supprimer
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Filters Modal */}
      <Modal opened={opened} onClose={close} title="Filtres avancés">
        <Stack>
          <Select
            label="Catégorie"
            placeholder="Toutes les catégories"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />

          <Select
            label="Statut"
            placeholder="Tous les statuts"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />

          <Select
            label="Type de document"
            placeholder="Tous les types"
            data={documentTypes}
            value={selectedDocumentType}
            onChange={setSelectedDocumentType}
            clearable
          />

          <DatePickerInput
            label="Date de validité"
            placeholder="Sélectionner une date"
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={close}>
              Fermer
            </Button>
            <Button onClick={close}>
              Appliquer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Paper>
  );
}
