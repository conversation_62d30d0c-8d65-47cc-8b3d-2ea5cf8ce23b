# Generated by Django 5.1.3 on 2025-08-06 12:54

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("patients", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PatientAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "trigger",
                    models.CharField(
                        choices=[
                            ("appointment", "Appointment"),
                            ("medication", "Medication"),
                            ("follow_up", "Follow-up"),
                            ("emergency", "Emergency"),
                            ("reminder", "Reminder"),
                            ("custom", "Custom"),
                        ],
                        default="custom",
                        max_length=50,
                    ),
                ),
                (
                    "trigger_custom",
                    models.CharField(
                        blank=True,
                        help_text="Custom trigger description",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("MINIMUM", "Minimum"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                        ],
                        default="MINIMUM",
                        max_length=10,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "is_permanent",
                    models.BooleanField(
                        default=False, help_text="Is this alert permanent"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Is this alert currently active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "triggered_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this alert has been triggered",
                    ),
                ),
                (
                    "last_triggered",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time this alert was triggered",
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_patient_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        limit_choices_to={"user_type": "patient"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="patient_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "trigger_for",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Staff members this alert should trigger for",
                        limit_choices_to={
                            "user_type__in": ["doctor", "nurse", "staff"]
                        },
                        related_name="targeted_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Alert",
                "verbose_name_plural": "Patient Alerts",
                "ordering": ["-created_at"],
            },
        ),
    ]
