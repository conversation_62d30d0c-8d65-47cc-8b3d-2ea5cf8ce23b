"use client";
import { useState, useCallback, useMemo, useEffect } from "react";
import { SegmentedControl } from "@mantine/core";
import React from "react";
import { Menu, rem, Button,Modal,Group,Text,Select,TextInput,ActionIcon,Radio,Textarea,NumberInput, InputBase,Avatar,
  Autocomplete,Switch,ColorPicker,
} from "@mantine/core";
import { SearchForm } from "@/layout/SearchForm";
import Icon from '@mdi/react';
import {  mdiPlusCircle,mdiFilterVariant, mdiChevronLeft, mdiChevronRight,mdiAccountCheckOutline,mdiMicrophone,mdiPowerSocketUs,
} from '@mdi/js';
import PauseModal from './PauseModal';
import LunchtimeBackgroundModal from './overview/LunchtimeBackgroundModal';
import SimpleBar from "simplebar-react";
import { IMaskInput } from 'react-imask';
import DayViewToggle from "./overview/DayViewToggle";
import WeekViewToggle from "./overview/WeekViewToggle";
import MonthViewToggle from "./overview/MonthViewToggle";
import AgendaViewToggle from "./overview/AgendaViewToggle";
import "@/styles/tab.css";
import { Center } from "@mantine/core";
import {
  IconCalendar,
  IconCalendarWeek,
  IconCalendarMonth,
  IconHexagonPlusFilled,IconColorPicker,IconTextPlus,IconCheck,IconFileText,IconChartBar,IconReportMedical,IconCash,IconPill,IconPrescription,IconSettings,
} from "@tabler/icons-react";
//import PatientList from "./PatientList";
import { appointmentAPI, pauseAPI, patientAPI } from '@/services/api';
import { patientAPI as enhancedPatientAPI, Patient } from '@/services/patientAPI';
import appointmentService from '@/services/appointmentService';
import authService from '@/services/authService';
import type { AppointmentFormData, DoctorPause } from '@/types/api';
import { notifications } from '@mantine/notifications';
import { useDisclosure,  } from "@mantine/hooks";
import CareSheetQuickAccess from '@/components/care-sheet/CareSheetQuickAccess';
//import CareSheetSummary from '@/components/care-sheet/CareSheetSummary';
import { careSheetBackgroundFetcher } from '@/utils/careSheetBackgroundFetcher';
import GenericStatesQuickAccess from '@/components/generic-states/GenericStatesQuickAccess';
//import DashboardWidgets from '@/components/generic-states/DashboardWidgets';
import { genericStatesBackgroundFetcher } from '@/utils/genericStatesBackgroundFetcher';
import MedicalReportQuickAccess from '@/components/medical-report/MedicalReportQuickAccess';
//import MedicalReportWidgets from '@/components/medical-report/MedicalReportWidgets';
import { medicalReportBackgroundFetcher } from '@/utils/medicalReportBackgroundFetcher';
import PaymentQuickAccess from '@/components/payment/PaymentQuickAccess';
//import PaymentWidgets from '@/components/payment/PaymentWidgets';
import { paymentBackgroundFetcher } from '@/utils/paymentBackgroundFetcher';
import PharmacyQuickAccess from '@/components/pharmacy/PharmacyQuickAccess';
//import PharmacyWidgets from '@/components/pharmacy/PharmacyWidgets';
import { pharmacyBackgroundFetcher } from '@/utils/pharmacyBackgroundFetcher';
import PrescriptionsQuickAccess from '@/components/prescriptions/PrescriptionsQuickAccess';
//import PrescriptionsWidgets from '@/components/prescriptions/PrescriptionsWidgets';
import { prescriptionsBackgroundFetcher } from '@/utils/prescriptionsBackgroundFetcher';
import ParametersQuickAccess from '@/components/parameters/ParametersQuickAccess';
//import ParametersWidgets from '@/components/parameters/ParametersWidgets';
import { parametersBackgroundFetcher } from '@/utils/parametersBackgroundFetcher';
// Types for calendar events
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
  roomId?: string;
  duration?: number;
  consultationDuration?: number;
  type?: string;
  status?: string;
  patient_phone?: string;
  doctor?: string;
  // Additional patient data for editing
  patient_name?: string;
  patient_email?: string;
  patient_address?: string;
  notes?: string;
  reason_for_visit?: string;
  appointment_type?: string;
  doctor_assigned?: string;
  resource_id?: string;
  resourceId?: string | number;
  lunchTime?: boolean;
  first_name?: string;
  last_name?: string;
  docteur?: string;
  typeConsultation?: string;
  patient_id?: string;
  patient?: string; // Add patient field for backend compatibility
}

// Type for appointment data from API
interface AppointmentData {
  id: string;
  title?: string;
  appointment_date: string;
  appointment_time: string;
  color?: string;
  resource_id?: string;
  appointment_type?: string;
  status?: string;
  duration_minutes?: number;
  consultation_duration?: number;
  patient_phone?: string;
  doctor_assigned?: string;
  patient_name?: string;
  patient_id?: string; // Patient ID field
  patient?: string; // Alternative patient field
}

// User profile interface
interface UserProfile {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  firstName?: string;
  lastName?: string;
  user_type: string; // Changed to string to match authService UserProfile
  assigned_doctor?: string;
}

// Interface for backend patient data

interface EventFormData {

   id?: string;
  title: string;
  start: string;
  end: string;
  desc: string;
  // Patient Information
  patient_title: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  landline_number: string;
  address: string;
  birth_date: string;
  age: number | null;
  gender: 'Homme' | 'Femme' | 'Enfant' | 'Autre' | '';
  cin: string;
  social_security: string;
  etat_civil: 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)' | '';
  countryId: string;
  // Appointment Details
  etat_aganda: 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose' | '';
  doctor_assigned: string;
  duration: number;
  resourceId: string;
  typeConsultation: 'consultation' | 'urgence' | 'controle' | 'chirurgie' | '';
  date: string;
  // Administrative
  notes: string;
  comment: string;
  agenda: string;
  is_active: boolean;
  user_type: 'patient' | 'doctor' | 'admin' | '';
  isFormInvalid: boolean;
  isDraft: boolean;
  is_permanent: boolean;
  addToWaitingList: boolean;
  // Additional fields for comprehensive form
  profession: string;
  birthPlace: string;
  fatherName: string;
  motherName: string;
  bloodGroup: string;
  maritalStatus: string;
  emergencyContact: string;
  emergencyPhone: string;
  medicalHistory: string;
  currentMedications: string;
  insuranceProvider: string;
  insuranceNumber: string;
  preferredLanguage: string;
  referredBy: string;
  specialNeeds: string;
  consentGiven: boolean;
  photoConsent: boolean;
  dataProcessingConsent: boolean;
  marketingConsent: boolean;
  allergies: string;
  Commentairelistedattente: string;
  checkedAppelvideo: boolean;
  checkedRappelSms: boolean;
  checkedRappelEmail: boolean;
  type: string;
  removeFromCalendar: boolean;
  rescheduleDateTime: string;
  mobility_assistance: boolean;
  interpreter_required: boolean;
  special_accommodations: string;
  color: string;
  
  docteur: string;
}
// Sample events data with different time periods and rooms for testing
// Initialize with empty events - will be loaded from backend only
const initialEvents: CalendarEvent[] = [];

type SectionType = "jour" | "semaine" | "mois" | "m";
type EventType = 'visit' | 'visitor-counter' | 'completed' | 'diagnosis';

// Import the interface from This_Day.tsx or define it here to match
interface WaitingListPatient {
  id: string;
  first_name: string;
  last_name: string;
  typeConsultation?: string;
  eventType?: 'visit' | 'visitor-counter' | 'completed' | 'diagnosis';
  phone_number?: string;
  start?: string;
  duration?: number;
  color?: string;
  docteur?: string;
  date?: string;
  title?: string;
  end?: string;
  desc?: string;
  address?: string;
  notes?: string;
  isActive?: boolean;
  status?: string;
  resourceId?: string;
  agenda?: string;
  email?: string;
    consultationDuration?: number;
}

interface CalendarPatientProps {
  onWaitingListUpdate?: (waitingList: WaitingListPatient[]) => void;
}

// Define appointment type for API response
interface ApiAppointmentData {
  id: string;
  title: string;
  patient_name: string;
  patient_id?: string;
  appointment_type: string;
  status: string;
  appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  has_dentistry_details: boolean;
  created_at: string;
  doctor_name?: string;
}

const CalendarPatient: React.FC<CalendarPatientProps> = ({ onWaitingListUpdate }) => {
  const [section, setSection] = useState<SectionType>("jour");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>(initialEvents);
  const [showEventModal, setShowEventModal] = useState(false);
  const [eventFilter, setEventFilter] = useState<'all' | 'last3days' | 'lastweek' | 'archived'>('all');
  const [isTimeSlotClick, setIsTimeSlotClick] = useState(false);

  // Care-sheet modal states
  const [showCareSheetModal, setShowCareSheetModal] = useState(false);
  const [careSheetPatientId, setCareSheetPatientId] = useState<string>('');
  const [careSheetPatientName, setCareSheetPatientName] = useState<string>('');

  // Generic-states modal states
  const [showGenericStatesModal, setShowGenericStatesModal] = useState(false);
  const [genericStatesDateRange, setGenericStatesDateRange] = useState<{ start: string; end: string } | undefined>();

  // Medical-report modal states
  const [showMedicalReportModal, setShowMedicalReportModal] = useState(false);
  const [medicalReportPatientId, setMedicalReportPatientId] = useState<string>('');
  const [medicalReportPatientName, setMedicalReportPatientName] = useState<string>('');
  const [medicalReportDateRange, setMedicalReportDateRange] = useState<{ start: string; end: string } | undefined>();

  // Payment modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentPatientId, setPaymentPatientId] = useState<string>('');
  const [paymentPatientName, setPaymentPatientName] = useState<string>('');
  const [paymentDateRange, setPaymentDateRange] = useState<{ start: string; end: string } | undefined>();

  // Pharmacy modal states
  const [showPharmacyModal, setShowPharmacyModal] = useState(false);
  const [pharmacyDateRange, setPharmacyDateRange] = useState<{ start: string; end: string } | undefined>();

  // Prescriptions modal states
  const [showPrescriptionsModal, setShowPrescriptionsModal] = useState(false);
  const [prescriptionsPatientId, setPrescriptionsPatientId] = useState<string>('');
  const [prescriptionsPatientName, setPrescriptionsPatientName] = useState<string>('');
  const [prescriptionsDateRange, setPrescriptionsDateRange] = useState<{ start: string; end: string } | undefined>();

  // Parameters modal states
  const [showParametersModal, setShowParametersModal] = useState(false);

  // ✅ LUNCH EDIT MODAL STATES - NEW
  const [showLunchtimeEditModal, setShowLunchtimeEditModal] = useState(false);
  const [lunchtimeEditData, setLunchtimeEditData] = useState<{
    id: string;
    title: string;
    startTime: Date;
    endTime: Date;
    duration: number;
    doctorId: string;
    roomId: string;
    color: string;
    notes: string;
    isRecurring: boolean;
  } | null>(null);
const [ListDesPatientOpened, { open: openListDesPatient, close: closeListDesPatient }] = useDisclosure(false);
const [ColorPickeropened, { open:openedColorPicker, close:closeColorPicker }] = useDisclosure(false);
  const [ListRendezVousOpened, { open: openListRendezVous, close: closeListRendezVous }] = useDisclosure(false);

  // Additional state variables for the form
  const [newTitle, setNewTitle] = useState('');
  const [titleOptions, setTitleOptions] = useState([
    { value: "M.", label: "M." },
    { value: "Mme", label: "Mme" },
    { value: "Mlle", label: "Mlle" },
    { value: "Dr.", label: "Dr." },
    { value: "Pr.", label: "Pr." }
  ]);
  const [newConsultationType, setNewConsultationType] = useState('');
  const [consultationTypes, setConsultationTypes] = useState([
    { value: "15 min", label: "15 min" },
    { value: "30 min", label: "30 min" },
    { value: "45 min", label: "45 min" },
    { value: "60 min", label: "60 min" }
  ]);
  const [newAgendaType, setNewAgendaType] = useState('');
  const [agendaTypes, setAgendaTypes] = useState([
    { value: "Cabinet", label: "Cabinet" },
    { value: "Center", label: "Center" },
    { value: "Kaders", label: "Kaders" }
  ]);
  const [newConsultationColor, setNewConsultationColor] = useState('#FF5178');
  const [changeEndValue, setChangeEndValue] = useState('#FF5178');
  const [, setEventTitle] = useState('');
  const [, setEventAganda] = useState('');
  const [, setValue] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [, setPatientTypeConsultation] = useState('');
  const [eventType, setEventType] = useState<'visit' | 'visitor-counter' | 'completed' | 'diagnosis'>('visit');
  const [dureeDeLexamen, setDureeDeLexamen] = useState('15 min');
  
  const [isWaitingList, ] = useState(false);
  const [waitingList, setWaitingList] = useState<WaitingListPatient[]>([]);
  const [allPatients, ] = useState<EventFormData[]>([]);

  // Callback for waiting list updates to prevent render-time setState
  const handleWaitingListUpdate = useCallback((newWaitingList: WaitingListPatient[]) => {
    console.log('📋 CalendarPatient: Waiting list updated from DayViewToggle:', newWaitingList);
    // Use setTimeout to defer setState and avoid render-time updates
    setTimeout(() => {
      setWaitingList(newWaitingList);
      if (onWaitingListUpdate) {
        onWaitingListUpdate(newWaitingList);
      }
    }, 0);
  }, [onWaitingListUpdate]);

  // Email validation state
  const [emailError, setEmailError] = useState<string>('');
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [doctors, setDoctors] = useState<Array<{
    id: string;
    name: string;
    email: string;
    user_type: string;
    assigned_doctor?: string;
    assistants?: Array<{ id: string; name: string; email: string; }>;
  }>>([]);

  // Get current user from context
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoadingPatients, ] = useState(false);
  // Helper function to calculate end time based on start time and duration
  const calculateEndTime = (startTime: string, durationMinutes: number): string => {
    if (!startTime) return '';

    const [hours, minutes] = startTime.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);

    const endDate = new Date(startDate.getTime() + durationMinutes * 60000);

    return endDate.toTimeString().slice(0, 5);
  };

  // Update end time when start time or duration changes
  

  // Mock functions
  const onPatientSelect = (patient: EventFormData) => {
    setFormData(patient);
  };

  const handleAppelvideoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, checkedAppelvideo: event.currentTarget.checked }));
  };

  const handleRappelSmsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, checkedRappelSms: event.currentTarget.checked }));
  };

  const handleRappelEmailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, checkedRappelEmail: event.currentTarget.checked }));
  };

  // STRICT Email validation function - NO DUPLICATES ALLOWED
  const validateEmail = useCallback(async (email: string) => {
    if (!email || !email.trim()) {
      setEmailError('');
      return true; // Email is optional
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError('Format d\'email invalide');
      return false;
    }

    // STRICT duplicate email check - NO OVERRIDES
    setIsCheckingEmail(true);
    try {
      const searchResults = await enhancedPatientAPI.search({
        search: email.trim(),
        limit: 10
      });

      const emailExists = searchResults.results.some(patient =>
        patient.email?.toLowerCase() === email.toLowerCase()
      );

      if (emailExists) {
        setEmailError('Cette adresse email est déjà utilisée. Si vous êtes certain qu\'il s\'agit du même patient, veuillez vérifier que les informations de nom, prénom, téléphone et autres détails correspondent.');
        setIsCheckingEmail(false);
        return false;
      }

      setEmailError('');
      setIsCheckingEmail(false);
      return true;
    } catch (error) {
      console.error('Error checking email:', error);
      setEmailError('Erreur lors de la vérification de l\'adresse email');
      setIsCheckingEmail(false);
      return false;
    }
  }, []);

  /**
   * Verify patient identity to prevent data corruption
   * Uses a more flexible approach - if email matches and basic info matches, consider it the same patient
   * NOTE: This function is currently unused as we now use strict email duplicate prevention
   */
  /*
  const verifyPatientIdentity = (existingPatient: Patient, newPatientData: Partial<EventFormData>): boolean => {
    console.log('🔍 Verifying patient identity with enhanced validation:');
    console.log('Existing patient:', existingPatient);
    console.log('New patient data:', newPatientData);

    // Helper function to safely get field value
    const getField = (obj: Patient | Partial<EventFormData>, field: string): string => {
      return (obj as Record<string, unknown>)?.[field] as string || '';
    };

    // Since we already know the email matches (that's how we found this patient),
    // we need to verify at least one other key identifying field

    let hasBasicMatch = false;
    let matchDetails: string[] = [];

    // Check first name
    const newFirstName = getField(newPatientData, 'first_name');
    const existingFirstName = getField(existingPatient, 'first_name');
    if (newFirstName && existingFirstName) {
      const firstNameMatch = existingFirstName.toLowerCase().trim() === newFirstName.toLowerCase().trim();
      if (firstNameMatch) {
        hasBasicMatch = true;
        matchDetails.push(`First name: "${existingFirstName}" = "${newFirstName}"`);
      } else {
        matchDetails.push(`First name: "${existingFirstName}" ≠ "${newFirstName}"`);
      }
    }

    // Check last name
    const newLastName = getField(newPatientData, 'last_name');
    const existingLastName = getField(existingPatient, 'last_name');
    if (newLastName && existingLastName) {
      const lastNameMatch = existingLastName.toLowerCase().trim() === newLastName.toLowerCase().trim();
      if (lastNameMatch) {
        hasBasicMatch = true;
        matchDetails.push(`Last name: "${existingLastName}" = "${newLastName}"`);
      } else {
        matchDetails.push(`Last name: "${existingLastName}" ≠ "${newLastName}"`);
      }
    }

    // Check phone number
    const newPhone = getField(newPatientData, 'phone_number');
    const existingPhone = getField(existingPatient, 'phone_number');
    if (newPhone && existingPhone) {
      const phoneMatch = existingPhone.trim() === newPhone.trim();
      if (phoneMatch) {
        hasBasicMatch = true;
        matchDetails.push(`Phone: "${existingPhone}" = "${newPhone}"`);
      } else {
        matchDetails.push(`Phone: "${existingPhone}" ≠ "${newPhone}"`);
      }
    }

    console.log('🔍 Match details:', matchDetails);

    // If we have basic identifying information that matches, consider it the same patient
    // This is more practical than requiring multiple fields that might not be filled in
    if (hasBasicMatch) {
      console.log(`✅ Patient identity verified: Basic identifying information matches`);
      console.log(`✅ Email already matches, and at least one of: name or phone matches`);
      return true;
    }

    // If no basic match but we have some other identifying information, check those
    // This handles cases where names might be slightly different but other info matches
    let additionalMatches = 0;

    // Check CIN number (strong identifier)
    const newCin = getField(newPatientData, 'cin');
    const existingCin = getField(existingPatient, 'cin');
    if (newCin && existingCin && newCin.toLowerCase().trim() === existingCin.toLowerCase().trim()) {
      additionalMatches++;
      matchDetails.push(`CIN: "${existingCin}" = "${newCin}"`);
    }

    // Check birth date (strong identifier)
    const newBirthDate = getField(newPatientData, 'birth_date');
    const existingBirthDate = getField(existingPatient, 'birth_date');
    if (newBirthDate && existingBirthDate && newBirthDate === existingBirthDate) {
      additionalMatches++;
      matchDetails.push(`Birth date: "${existingBirthDate}" = "${newBirthDate}"`);
    }

    // If we have strong additional identifiers, consider it the same patient
    if (additionalMatches > 0) {
      console.log(`✅ Patient identity verified: Strong additional identifiers match`);
      console.log(`✅ Email matches and ${additionalMatches} strong identifier(s) match`);
      return true;
    }

    // If we reach here, we have conflicting information
    console.warn(`⚠️ Patient identity verification failed:`);
    console.warn(`⚠️ Email matches but basic identifying information conflicts`);
    console.warn(`⚠️ This likely indicates a different patient with the same email`);
    console.warn('Match details:', matchDetails);

    return false;
  };
  */

  // Handle email change - WITH DUPLICATE VALIDATION
  const handleEmailChange = useCallback(async (email: string) => {
    // DIRECT EMAIL STORAGE - NO MODIFICATIONS
    setFormData(prev => ({ ...prev, email }));
    console.log('📧 EMAIL STORED AS-IS:', email);

    // Clear any previous errors
    setEmailError('');

    // If email is empty, no validation needed
    if (!email || !email.trim()) {
      return;
    }

    // Basic format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      setEmailError('Format d\'email invalide');
      return;
    }

    // Call the duplicate validation function
    await validateEmail(email.trim());
  }, [validateEmail]);


  // Define rooms for agenda view
  
 const [eventResourceId, setEventResourceId] = useState<number>(1); // Default to Room A
  // Form data state
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    start: '',
    end: '',
    desc: '',
    // Patient Information
    patient_title: '',
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    landline_number: '',
    address: '',
    birth_date: '',
    age: null,
    gender: 'Homme', // Set default gender value
    cin: '',
    social_security: '',
    etat_civil: '',
    countryId: '',
    // Appointment Details
    etat_aganda: '',
    doctor_assigned: '',
    typeConsultation: '',
    // Administrative
    notes: '',
    comment: '',
    agenda: '',
    is_active: true,
    user_type: 'patient',
    isFormInvalid: false,
    isDraft: false,
    is_permanent: false,
    addToWaitingList: false,
    // Additional fields for comprehensive form
    profession: '',
    birthPlace: '',
    fatherName: '',
    motherName: '',
    bloodGroup: '',
    maritalStatus: '',
    emergencyContact: '',
    emergencyPhone: '',
    medicalHistory: '',
    currentMedications: '',
    insuranceProvider: '',
    insuranceNumber: '',
    preferredLanguage: '',
    referredBy: '',
    specialNeeds: '',
    consentGiven: false,
    photoConsent: false,
    dataProcessingConsent: false,
    marketingConsent: false,
    allergies: '',
    Commentairelistedattente: '',
    checkedAppelvideo: false,
    checkedRappelSms: false,
    checkedRappelEmail: false,
    date: '',
    duration: 15, // 30minutes
    type: "visit",
    resourceId: `room-${eventResourceId === 1 ? 'a' : 'b'}`,
    removeFromCalendar: false,
    rescheduleDateTime: '',
    mobility_assistance: false,
    interpreter_required: false,
    special_accommodations: '',
    color: '#3799CE',
    id: '',
    docteur: ''
  });

  // Initialize date on client side to avoid hydration issues
  useEffect(() => {
    if (typeof window !== 'undefined' && !formData.date) {
      const today = new Date();
      setFormData(prev => ({
        ...prev,
        date: today.toISOString().split('T')[0]
      }));
    }
  }, []); // Remove formData.date from dependencies to prevent infinite loop

  // Sync eventResourceId with formData.resourceId
  useEffect(() => {
    if (formData.resourceId) {
      const roomNumber = formData.resourceId === 'room-a' ? 1 : 2;
      if (roomNumber !== eventResourceId) {
        setEventResourceId(roomNumber);
        console.log('🔄 Synced room selection from form data:', { resourceId: formData.resourceId, roomNumber });
      }
    }
  }, [formData.resourceId, eventResourceId]);

  // Fetch current user profile
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const userData = await authService.getProfile();
        setUserProfile(userData);
        console.log('User profile fetched:', userData);
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, []);

  // Fetch doctors based on current user - only show current user and their assistant
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        console.log('Current user profile:', userProfile);

        if (!userProfile) {
          console.log('No user profile available, cannot fetch doctors');
          setDoctors([]);
          return;
        }

        const doctorsToShow: Array<{
          id: string;
          name: string;
          email: string;
          user_type: string;
          assistants: Array<{ id: string; name: string; email: string; }>;
        }> = [];

        if (userProfile.user_type === 'doctor') {
          // If current user is a doctor, show only them
          const doctorName = `${userProfile.first_name || userProfile.firstName || ''} ${userProfile.last_name || userProfile.lastName || ''}`.trim();

          doctorsToShow.push({
            id: userProfile.id || 'current-doctor',
            name: doctorName || userProfile.email || 'Current Doctor',
            email: userProfile.email || '',
            user_type: 'doctor',
            assistants: []
          });

          // Fetch assistants assigned to this doctor using authenticated API
          try {
            const assistantsData = await patientAPI.getAssistants();
            console.log('Fetched assistants data:', assistantsData);

            const assistants = assistantsData.results?.filter((assistant: {
              id: string;
              first_name: string;
              last_name: string;
              email: string;
              assigned_doctor?: string;
            }) => assistant.assigned_doctor === userProfile.id).map((assistant: {
              id: string;
              first_name: string;
              last_name: string;
              email: string;
            }) => ({
              id: assistant.id,
              name: `${assistant.first_name} ${assistant.last_name}`.trim(),
              email: assistant.email
            })) || [];

            console.log('Filtered assistants for doctor:', assistants);

            // Add assistants to the doctor's record
            if (doctorsToShow.length > 0) {
              doctorsToShow[0].assistants = assistants;
            }
          } catch (assistantError) {
            console.error('Failed to fetch assistants:', assistantError);
          }
        } else if (userProfile.user_type === 'assistant') {
          // If current user is an assistant, show their assigned doctor
          if (userProfile.assigned_doctor) {
            try {
              // Use the authenticated API to get doctors
              const doctorsData = await patientAPI.getDoctors();
              console.log('Fetched doctors data for assistant:', doctorsData);

              const assignedDoctor = doctorsData.results?.find((doctor: {
                id: string;
                first_name: string;
                last_name: string;
                email: string;
                user_type?: string;
              }) => doctor.id === userProfile.assigned_doctor);

              console.log('Found assigned doctor:', assignedDoctor);

              if (assignedDoctor) {
                // Get all assistants for this doctor
                const assistantsData = await patientAPI.getAssistants();
                const doctorAssistants = assistantsData.results?.filter(assistant =>
                  assistant.assigned_doctor === assignedDoctor.id
                ).map(assistant => ({
                  id: assistant.id,
                  name: `${assistant.first_name} ${assistant.last_name}`.trim(),
                  email: assistant.email
                })) || [];

                doctorsToShow.push({
                  id: assignedDoctor.id,
                  name: `${assignedDoctor.first_name} ${assignedDoctor.last_name}`.trim(),
                  email: assignedDoctor.email,
                  user_type: assignedDoctor.user_type || 'doctor',
                  assistants: doctorAssistants
                });
              }
            } catch (doctorError) {
              console.error('Failed to fetch assigned doctor:', doctorError);
            }
          }
        }

        // If no doctors found, add current user as default
        if (doctorsToShow.length === 0) {
          const userName = `${userProfile.first_name || userProfile.firstName || ''} ${userProfile.last_name || userProfile.lastName || ''}`.trim();
          doctorsToShow.push({
            id: userProfile.id || 'current-user',
            name: userName || 'Current User',
            email: userProfile.email || '',
            user_type: userProfile.user_type || 'doctor',
            assistants: []
          });
        }

        // If no doctors found, add a default doctor
        if (doctorsToShow.length === 0) {
          console.warn('No doctors found, adding default doctor');
          doctorsToShow.push({
            id: 'default-doctor',
            name: 'Dr. Défaut',
            email: '<EMAIL>',
            user_type: 'doctor',
            assistants: []
          });
        }

        setDoctors(doctorsToShow);
        console.log('Doctors set based on current user:', doctorsToShow);

        // Validate that we have valid doctor IDs
        doctorsToShow.forEach(doctor => {
          console.log(`Doctor: ${doctor.name}, ID: ${doctor.id}, Type: ${doctor.user_type}`);
        });
      } catch (error) {
        console.error('Failed to fetch doctors:', error);
        // Set a default doctor as fallback
        const fallbackDoctors = [{
          id: 'fallback-doctor',
          name: 'Dr. Système',
          email: '<EMAIL>',
          user_type: 'doctor' as const,
          assistants: []
        }];
        setDoctors(fallbackDoctors);
        console.log('Set fallback doctors:', fallbackDoctors);
      }
    };

    fetchDoctors();
  }, [userProfile]); // Depend on userProfile to refetch when user changes

  // Set default doctor when doctors are loaded
  useEffect(() => {
    if (doctors.length > 0 && !formData.doctor_assigned) {
      // Set the first doctor as default
      console.log('Setting default doctor:', doctors[0]);
      setFormData(prev => ({
        ...prev,
        doctor_assigned: doctors[0].id
      }));
    } else if (doctors.length === 0) {
      // Clear doctor assignment if no doctors available
      setFormData(prev => ({
        ...prev,
        doctor_assigned: ''
      }));
    }
  }, [doctors, formData.doctor_assigned]);

  // Helper function to populate form with event data
  const populateFormWithEventData = useCallback((event: CalendarEvent) => {
    console.log('📝 POPULATE FORM CALLED with event:', event);
    console.log('📝 Event properties:', Object.keys(event));
    console.log('📝 Current formData before populate:', formData);

    // Safe property access for event data
    const getEventProperty = (key: string, fallback: string = ''): string => {
      return ((event as unknown as Record<string, unknown>)[key] as string) || fallback;
    };

    // Safe numeric property access for event data
    const getEventNumber = (key: string, fallback: number | null = null): number | null => {
      const value = (event as unknown as Record<string, unknown>)[key];
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? fallback : parsed;
      }
      return fallback;
    };

    // Extract patient name - try from patient_name first, then from title
    const patientName = getEventProperty('patient_name') || event.title || '';
    const nameParts = patientName.split(' ') || [];
    const first_name = getEventProperty('patient_first_name') || nameParts[0] || '';
    const last_name = getEventProperty('patient_last_name') || nameParts.slice(1).join(' ') || '';

    // Format date for input (YYYY-MM-DD)
    const eventDate = event.start instanceof Date ? event.start : new Date(event.start);
    const formattedDate = eventDate.toISOString().split('T')[0];

    // Format time for input (HH:MM)
    const formattedTime = eventDate.toTimeString().slice(0, 5);

    // Format end time for input (HH:MM)
    const eventEndDate = event.end instanceof Date ? event.end : new Date(event.end || event.start);
    const formattedEndTime = eventEndDate.toTimeString().slice(0, 5);



    console.log('📝 Setting form data with event:', {
      id: event.id,
      title: event.title,
      date: formattedDate,
      start: formattedTime,
      end: formattedEndTime,
      duration: Math.max(event.duration || 30, 15)
    });

    setFormData(prev => ({
      ...prev,
      // Store event ID for editing detection
      id: event.id,
      title: event.title || `${first_name} ${last_name}`.trim(),

      // Basic info
      first_name,
      last_name,
      date: formattedDate,
      start: formattedTime,
      end: formattedEndTime,
      desc: event.desc || '',
      duration: Math.max(event.duration || 30, 15), // Ensure minimum 15 minutes

      // Event details
      doctor_assigned: getEventProperty('doctor_assigned') || getEventProperty('docteur') || '',
      typeConsultation: (getEventProperty('typeConsultation') as '' | 'consultation' | 'urgence' | 'controle' | 'chirurgie') || 'consultation',
      etat_aganda: (getEventProperty('etat_aganda') as '' | 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose') || 'scheduled',

      // Patient details (if available in event) - improved extraction
      phone_number: getEventProperty('phone_number') || getEventProperty('phone_numbers') || getEventProperty('patient_phone') || '',
      email: getEventProperty('email') || getEventProperty('patient_email') || '',
      address: getEventProperty('address') || getEventProperty('patient_address') || '',
      notes: getEventProperty('notes') || getEventProperty('reason_for_visit') || '',
      agenda: getEventProperty('agenda') || getEventProperty('agenda_type') || '',
      comment: getEventProperty('comment') || getEventProperty('commentaire') || '',
      Commentairelistedattente: getEventProperty('Commentairelistedattente') || getEventProperty('commentaire_liste_attente') || '',

      // Keep other existing form data - improved extraction
      patient_title: getEventProperty('patient_title') || '',
      birth_date: getEventProperty('birth_date') || getEventProperty('date_of_birth') || '',
      age: getEventNumber('age') || null,
      gender: (getEventProperty('gender') as '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre') || 'Homme',
      etat_civil: (getEventProperty('etat_civil') as '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)') || '',
      cin: getEventProperty('cin') || getEventProperty('national_id_number') || '',
      social_security: getEventProperty('social_security') || '',
      profession: getEventProperty('profession') || '',
      birthPlace: getEventProperty('birthPlace') || getEventProperty('birth_place') || '',
      fatherName: getEventProperty('fatherName') || getEventProperty('father_name') || '',
      motherName: getEventProperty('motherName') || getEventProperty('mother_name') || '',
      bloodGroup: getEventProperty('bloodGroup') || getEventProperty('blood_group') || '',
      allergies: getEventProperty('allergies') || '',

      // Resource/Room information - improved mapping with better fallback
      resourceId: event.roomId || getEventProperty('resource_id') || event.resource_id ||
                  getEventProperty('event_resource_id') || (event as unknown as Record<string, unknown>).event_resource_id as string || 'room-a',
    }));

    // Update room selection to match the event's room
    const eventRoomId = event.roomId || getEventProperty('resource_id') ||
                       getEventProperty('event_resource_id') || (event as unknown as Record<string, unknown>).event_resource_id as string;
    if (eventRoomId) {
      const roomNumber = eventRoomId === 'room-a' ? 1 : 2;
      setEventResourceId(roomNumber);
      console.log('🏠 Updated room selection for editing:', { eventRoomId, roomNumber });
    }

    console.log('✅ Form populated for editing event:', event.id);
  }, []); // Remove formData dependency to prevent infinite re-renders



  // Fetch events from backend when component loads
  useEffect(() => {
    console.log('🚀 CALENDAR COMPONENT: useEffect TRIGGERED!');

    const fetchEvents = async () => {
      console.log('🔄 CALENDAR COMPONENT: Starting to fetch events from backend...');
      console.log('🌐 API Base URL:', process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api');



      try {
        console.log('📞 Making API call to appointmentAPI.list()...');

        // Get appointments for a wider date range to ensure we get all relevant events
        const today = new Date();

        // DYNAMIC: Use the currentDate state for fetching appointments
        const startDate = currentDate.toISOString().split('T')[0]; // Selected date
        const endDate = currentDate.toISOString().split('T')[0];   // Selected date only

        console.log('📅 FIXED: Fetching appointments from', startDate, 'to', endDate);
        console.log('🔍 Today is:', today.toISOString().split('T')[0]);

        // TEMPORARY: Direct API call to bypass any caching issues
        const directUrl = `http://127.0.0.1:8000/api/appointments/?start_date=${startDate}&end_date=${endDate}&limit=1000`;
        console.log('🌐 DIRECT API CALL:', directUrl);

        const directResponse = await fetch(directUrl);
        const response = await directResponse.json();

        console.log('📥 DIRECT API RESPONSE:', response);
        console.log('📥 Raw API response received:', response);
        console.log('📊 Response type:', typeof response);
        console.log('📋 Response keys:', Object.keys(response || {}));
        console.log('🔍 Response structure:', {
          count: response?.count,
          resultsLength: response?.results?.length,
          hasNext: !!response?.next,
          hasPrevious: !!response?.previous
        });

        const appointments = response.results || response.data || response || [];
        console.log('📋 Appointments found:', appointments.length);
        console.log('📋 Sample appointment data:', appointments[0]);

        // Always set events, even if empty
        if (appointments.length === 0) {
          console.log('⚠️ No appointments found, setting empty array');
          setEvents([]);
          return;
        }



        // Helper function to get color by appointment type
        const getColorByAppointmentType = (type: string): string => {
          const colorMap: Record<string, string> = {
            'consultation': '#3b82f6',
            'urgence': '#ef4444',
            'controle': '#10b981',
            'follow_up': '#f59e0b',
            'emergency': '#dc2626',
            'routine_checkup': '#06b6d4',
            'procedure': '#8b5cf6',
            'surgery': '#ec4899',
            'cleaning': '#84cc16',
          };
          return colorMap[type] || '#6b7280';
        };

        // Convert appointments to calendar events
        const calendarEvents: CalendarEvent[] = appointments.map((appointment: AppointmentData) => {
          const appt = appointment as unknown as ApiAppointmentData;
          console.log('🔄 Processing appointment:', appt);

          // Handle different date/time formats - improved parsing
          let startDate: Date;
          if (appt.appointment_date && appt.appointment_time) {
            // Parse the date and time from the API response
            const dateStr = appt.appointment_date; // Format: "2025-08-03"
            const timeStr = appt.appointment_time; // Format: "13:45:00"
            startDate = new Date(`${dateStr}T${timeStr}`);
            console.log('📅 Parsed date from API:', startDate);
          } else {
            console.warn('⚠️ No valid date found for appointment:', appt);
            startDate = new Date();
          }

          // Validate the parsed date
          if (isNaN(startDate.getTime())) {
            console.error('❌ Invalid date parsed for appointment:', appt);
            startDate = new Date();
          }

          // Calculate end time
          const duration = appt.duration_minutes || 30;
          const endDate = new Date(startDate.getTime() + duration * 60000);

          // Parse patient name into first_name and last_name
          const nameParts = appt.patient_name ? appt.patient_name.split(' ') : ['', ''];
          const firstName = nameParts[0] || '';
          const lastName = nameParts.slice(1).join(' ') || '';

          // Map resource_id from backend to roomId for frontend
          const mapResourceIdToRoomId = (resourceId: string | number | undefined, fallbackRoom?: string): string => {
            console.log('🏠 ROOM MAPPING DEBUG:', {
              resourceId,
              event_resource_id: (appt as unknown as Record<string, unknown>).event_resource_id,
              room: (appt as unknown as Record<string, unknown>).room,
              fallbackRoom,
              appointmentId: appt.id
            });

            // CRITICAL: Try multiple sources for room information
            const roomSources = [
              resourceId,
              (appt as unknown as Record<string, unknown>).event_resource_id,
              (appt as unknown as Record<string, unknown>).room,
              fallbackRoom
            ];

            for (const source of roomSources) {
              if (source) {
                const sourceStr = source.toString().toLowerCase();

                // Handle different resource ID formats
                if (sourceStr === 'room-a' || sourceStr === '1') {
                  console.log('🏠 FINAL ROOM MAPPING RESULT: room-a (from source:', source, ')');
                  return 'room-a';
                } else if (sourceStr === 'room-b' || sourceStr === '2') {
                  console.log('🏠 FINAL ROOM MAPPING RESULT: room-b (from source:', source, ')');
                  return 'room-b';
                } else if (sourceStr.includes('room b') || sourceStr.includes('room-b')) {
                  console.log('🏠 FINAL ROOM MAPPING RESULT: room-b (from source contains room-b:', source, ')');
                  return 'room-b';
                } else if (sourceStr.includes('room a') || sourceStr.includes('room-a')) {
                  console.log('🏠 FINAL ROOM MAPPING RESULT: room-a (from source contains room-a:', source, ')');
                  return 'room-a';
                }

                // If it's a numeric string, convert to room
                const numericId = parseInt(sourceStr, 10);
                if (!isNaN(numericId)) {
                  return numericId === 1 ? 'room-a' : 'room-b';
                }
              }
            }

            // CRITICAL: Only default to room-a if absolutely no room information is found
            console.warn('⚠️ No room information found for appointment, defaulting to room-a');
            console.log('🏠 FINAL ROOM MAPPING RESULT: room-a (default)');
            return 'room-a';
          };

          // Create calendar event with comprehensive patient data for editing
          const calendarEvent: CalendarEvent = {
            id: appt.id,
            title: appt.title || appt.patient_name || 'Rendez-vous',
            start: startDate,
            end: endDate,
            desc: `${appt.appointment_type} - ${appt.patient_name}`,
            color: getColorByAppointmentType(appt.appointment_type),
            roomId: mapResourceIdToRoomId(
              (appt as unknown as Record<string, unknown>).resource_id as string,
              'room-a' // fallback only if no room info found
            ), // Properly map resource_id to roomId with improved logic
            duration: duration,
            consultationDuration: duration,
            // Map backend properties to frontend properties
            type: appt.appointment_type,
            status: appt.status,
            patient_name: appt.patient_name,
            appointment_type: appt.appointment_type,
            // Map patient data
            first_name: firstName,
            last_name: lastName,
            docteur: appt.doctor_name || 'Dr. Non spécifié',
            typeConsultation: appt.appointment_type || 'Consultation',
            patient_id: appt.patient_id || (appt as unknown as Record<string, unknown>).patient as string,
            patient: (appt as unknown as Record<string, unknown>).patient as string || appt.patient_id,
            // Store the full appointment data for editing
            resource_id: (appt as unknown as Record<string, unknown>).resource_id as string, // Keep original resource_id for backend compatibility
            ...(appt as unknown as Record<string, unknown>)
          };

          console.log('✅ Created calendar event:', calendarEvent);
          return calendarEvent;
        }).filter((event: CalendarEvent) => event && !isNaN(event.start.getTime())); // Filter out invalid events

        // Load lunch breaks/pauses from backend and combine with appointments
        console.log('🍽️ Loading lunch breaks from backend...');
        try {
          const pauseResponse = await pauseAPI.list({
            start_date: startDate,
            end_date: endDate
          });
          
          console.log('🍽️ Pause API response:', pauseResponse);
          const pauses = pauseResponse.results || [];
          console.log('🍽️ Found', pauses.length, 'lunch breaks from backend');
          
          // Convert pauses to calendar events
          const pauseEvents: CalendarEvent[] = pauses.map((pause: {
            id: string;
            title: string;
            date_from: string;
            date_to: string;
            doctor: string;
            doctor_name?: string;
            room?: string;
            resource_id?: string;
            color?: string;
            notes?: string;
          }) => {
            const pauseStart = new Date(pause.date_from);
            const pauseEnd = new Date(pause.date_to);
            
            // FIXED: Improved room mapping logic - prioritize explicit room field
            const roomId = (() => {
              // First check room field
              if (pause.room === 'room-a') return 'room-a';
              if (pause.room === 'room-b') return 'room-b';
              if (pause.room === 'room-c') return 'room-c';
              
              // Fallback to resource_id mapping
              if (pause.resource_id === '1') return 'room-a';
              if (pause.resource_id === '2') return 'room-b';
              if (pause.resource_id === '3') return 'room-c';
              
              // Only default to room-a if absolutely no room information
              console.warn('⚠️ No room information found for pause, defaulting to room-a:', pause);
              return 'room-a';
            })();
            
            console.log('🏠 Room mapping for pause:', {
              pauseId: pause.id,
              room: pause.room,
              resource_id: pause.resource_id,
              mapped_roomId: roomId,
              doctor: pause.doctor_name
            });
            
            return {
              id: pause.id,
              title: pause.title || '🍽️ Pause Déjeuner',
              start: pauseStart,
              end: pauseEnd,
              desc: pause.notes || 'Pause déjeuner',
              color: pause.color || '#15AABF',
              roomId: roomId,
              duration: Math.round((pauseEnd.getTime() - pauseStart.getTime()) / (1000 * 60)),
              doctorId: pause.doctor,
              doctorName: pause.doctor_name || 'Dr. Non spécifié',
              lunchTime: true,
              type: 'pause'
            };
          }).filter((event: CalendarEvent) => event && !isNaN(event.start.getTime()));
          
          console.log('🍽️ Converted', pauseEvents.length, 'pause events for calendar');
          
          // Combine appointments and pauses
          const allEvents = [...calendarEvents, ...pauseEvents];
          setEvents(allEvents);
          
          console.log('✅ All events set in CalendarPatient:', allEvents.length, 'total events (', calendarEvents.length, 'appointments +', pauseEvents.length, 'lunch breaks)');
          
        } catch (pauseError) {
          console.warn('⚠️ Failed to load lunch breaks, showing appointments only:', pauseError);
          // Set only appointments if pause loading fails
          setEvents(calendarEvents);
        }

        // Debug: Log events by date to see what dates we have
        const eventsByDate = events.reduce((acc, event) => {
          const dateKey = event.start.toISOString().split('T')[0];
          if (!acc[dateKey]) acc[dateKey] = [];
          acc[dateKey].push(event.title);
          return acc;
        }, {} as Record<string, string[]>);
        console.log('📊 Events by date:', eventsByDate);
      } catch (error) {
        console.error('❌ CRITICAL: Failed to fetch events:', error);
        console.error('❌ Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          type: typeof error,
          error: error
        });

        // Check if it's a network error
        if (error instanceof TypeError && error.message.includes('fetch')) {
          console.error('🌐 NETWORK ERROR - Backend not reachable at http://127.0.0.1:8000');
        }

        // Set empty events array to show calendar is working but no data
        setEvents([]);
        console.log('🔧 Set empty events array due to API error');
      }
    };

    console.log('🚀 CALENDAR COMPONENT: useEffect triggered - calling fetchEvents()');
    fetchEvents();
  }, [currentDate]); // Refetch events when currentDate changes
  // Function to refresh events (can be called manually)
  const refreshEvents = useCallback(async (silent = false) => {
    console.log('🔄 REFRESH EVENTS CALLED!', silent ? '(silent)' : '');
    try {
      // FIXED: Use the current date range for appointments
      const today = new Date();
      const startDate = today.toISOString().split('T')[0];
      const endDate = today.toISOString().split('T')[0];

      console.log('🌐 REFRESH: Making API calls for appointments and pauses...');

      // Load appointments
      const appointmentUrl = `http://127.0.0.1:8000/api/appointments/?start_date=${startDate}&end_date=${endDate}&limit=1000`;
      console.log('🌐 APPOINTMENT URL:', appointmentUrl);

      const appointmentResponse = await fetch(appointmentUrl);
      const appointmentData = await appointmentResponse.json();

      console.log('📥 REFRESH APPOINTMENT RESPONSE:', appointmentData);
      const appointments = appointmentData.results || [];

      // Helper function to get color by appointment type
      const getColorByAppointmentType = (type: string): string => {
        const colorMap: Record<string, string> = {
          'consultation': '#3b82f6',
          'urgence': '#ef4444',
          'controle': '#10b981',
          'follow_up': '#f59e0b',
          'emergency': '#dc2626',
          'routine_checkup': '#06b6d4',
          'procedure': '#8b5cf6',
          'surgery': '#ec4899',
          'cleaning': '#84cc16',
        };
        return colorMap[type] || '#6b7280';
      };

      // Convert appointments to calendar events
      const calendarEvents: CalendarEvent[] = appointments.map((appointment: AppointmentData) => {
        const appt = appointment as unknown as ApiAppointmentData;
        const startDate = new Date(`${appt.appointment_date}T${appt.appointment_time}`);
        const duration = appt.duration_minutes || 30;
        const endDate = new Date(startDate.getTime() + duration * 60000);

        return {
          id: appt.id,
          title: appt.title || appt.patient_name || 'Rendez-vous',
          start: startDate,
          end: endDate,
          desc: `${appt.appointment_type} - ${appt.patient_name}`,
          color: getColorByAppointmentType(appt.appointment_type),
          roomId: 'room-a',
          duration: duration,
          consultationDuration: duration,
        };
      }).filter((event: CalendarEvent) => event && !isNaN(event.start.getTime()));

      // Load lunch breaks/pauses from backend
      console.log('🍽️ REFRESH: Loading lunch breaks from backend...');
      let pauseEvents: CalendarEvent[] = [];
      
      try {
        const pauseResponse = await pauseAPI.list({
          start_date: startDate,
          end_date: endDate
        });
        
        console.log('🍽️ REFRESH Pause API response:', pauseResponse);
        const pauses = pauseResponse.results || [];
        console.log('🍽️ REFRESH Found', pauses.length, 'lunch breaks from backend');
        
        // Convert pauses to calendar events
        pauseEvents = pauses.map((pause: {
          id: string;
          title: string;
          date_from: string;
          date_to: string;
          doctor: string;
          doctor_name?: string;
          room?: string;
          resource_id?: string;
          color?: string;
          notes?: string;
        }) => {
          const pauseStart = new Date(pause.date_from);
          const pauseEnd = new Date(pause.date_to);
          
          // FIXED: Improved room mapping logic - prioritize explicit room field
          const roomId = (() => {
            // First check room field
            if (pause.room === 'room-a') return 'room-a';
            if (pause.room === 'room-b') return 'room-b';
            if (pause.room === 'room-c') return 'room-c';
            
            // Fallback to resource_id mapping
            if (pause.resource_id === '1') return 'room-a';
            if (pause.resource_id === '2') return 'room-b';
            if (pause.resource_id === '3') return 'room-c';
            
            // Only default to room-a if absolutely no room information
            console.warn('⚠️ REFRESH: No room information found for pause, defaulting to room-a:', pause);
            return 'room-a';
          })();
          
          console.log('🏠 REFRESH: Room mapping for pause:', {
            pauseId: pause.id,
            room: pause.room,
            resource_id: pause.resource_id,
            mapped_roomId: roomId,
            doctor: pause.doctor_name
          });
          
          return {
            id: pause.id,
            title: pause.title || '🍽️ Pause Déjeuner',
            start: pauseStart,
            end: pauseEnd,
            desc: pause.notes || 'Pause déjeuner',
            color: pause.color || '#15AABF',
            roomId: roomId,
            duration: Math.round((pauseEnd.getTime() - pauseStart.getTime()) / (1000 * 60)),
            doctorId: pause.doctor,
            doctorName: pause.doctor_name || 'Dr. Non spécifié',
            lunchTime: true,
            type: 'pause'
          };
        }).filter((event: CalendarEvent) => event && !isNaN(event.start.getTime()));
        
        console.log('🍽️ REFRESH Converted', pauseEvents.length, 'pause events for calendar');
        
      } catch (pauseError) {
        console.warn('⚠️ REFRESH Failed to load lunch breaks, showing appointments only:', pauseError);
      }

      // Combine appointments and pauses
      const allEvents = [...calendarEvents, ...pauseEvents];
      setEvents(allEvents);
      
      console.log('✅ REFRESH Events set:', allEvents.length, 'total events (', calendarEvents.length, 'appointments +', pauseEvents.length, 'lunch breaks)');

      // Only show notification if not silent
      if (!silent) {
        notifications.show({
          title: 'Événements actualisés',
          message: `${allEvents.length} événements chargés (${calendarEvents.length} RDV + ${pauseEvents.length} pauses)`,
          color: 'blue',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('❌ REFRESH Failed to refresh events:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger les événements',
        color: 'red',
        autoClose: 3000
      });
    }
  }, []);

  const handleSectionChange = (value: string) => {
    if (value === "jour" || value === "semaine" || value === "mois" || value === "m") {
      setSection(value);
    }
  };

 

  const handleTimeSlotClick = useCallback((date: Date, hour: number, minute: number = 0, roomId: string = 'room-a') => {
    console.log('Time slot clicked:', { date, hour, minute, roomId });

    // Format the date for the date input (YYYY-MM-DD)
    const formattedDate = date.toISOString().split('T')[0];

    // Format the time for the time input (HH:MM)
    const formattedTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

    // Reset form to default values and set time slot information
    const defaultDuration = 15;
    const endTime = calculateEndTime(formattedTime, defaultDuration);

    setFormData({
      title: '',
      start: formattedTime,
      end: endTime,
      desc: '',
      // Patient Information
      patient_title: '',
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      landline_number: '',
      address: '',
      birth_date: '',
      age: null,
      gender: 'Homme', // Set default gender value
      cin: '',
      social_security: '',
      etat_civil: '',
      countryId: '',
      // Appointment Details
      etat_aganda: '',
      doctor_assigned: '',
      duration: defaultDuration,
      resourceId: roomId,
      typeConsultation: '',
      date: formattedDate,
      // Administrative
      notes: '',
      comment: '',
      agenda: '',
      is_active: false,
      user_type: '',
      isFormInvalid: false,
      isDraft: false,
      is_permanent: false,
      addToWaitingList: false,
      profession: '',
      birthPlace: '',
      fatherName: '',
      motherName: '',
      bloodGroup: '',
      maritalStatus: '',
      emergencyContact: '',
      emergencyPhone: '',
      medicalHistory: '',
      currentMedications: '',
      insuranceProvider: '',
      insuranceNumber: '',
      preferredLanguage: '',
      referredBy: '',
      specialNeeds: '',
      consentGiven: false,
      photoConsent: false,
      dataProcessingConsent: false,
      marketingConsent: false,
      allergies: '',
      Commentairelistedattente: '',
      checkedAppelvideo: false,
      checkedRappelSms: false,
      checkedRappelEmail: false,
      type: "visit",
      removeFromCalendar: false,
      rescheduleDateTime: '',
      mobility_assistance: false,
      interpreter_required: false,
      special_accommodations: '',
      color: '#3799CE',
      id: '',
      docteur: ''
    });

    // Mark that this was opened from a time slot click
    setIsTimeSlotClick(true);

    // Open the modal
    setShowEventModal(true);
  }, []);

  // Handle adding new events (like lunch breaks)
  const handleEventAdd = useCallback((event: CalendarEvent) => {
    console.log('🍽️ Adding new event:', event);

    // Ensure the event has a valid ID
    if (!event.id || event.id === 'undefined' || event.id === '') {
      // Generate a temporary ID for local events
      event.id = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      console.log('Generated temporary ID for event:', event.id);
    }

    setEvents(prev => {
      const newEvents = [...prev, event];
      console.log('✅ Event added to calendar:', event.title);
      return newEvents;
    });
  }, []);

  // Handle editing existing events
  const handleEventEdit = useCallback(async (event: CalendarEvent) => {
    console.log('🔧 EDIT EVENT CLICKED:', event);
    console.log('🔧 Event ID:', event.id);
    console.log('🔧 Event Title:', event.title);
    console.log('🔧 Event Start:', event.start);
    console.log('🔧 Event Data:', JSON.stringify(event, null, 2));

    // Validate that event.start is a valid Date object
    if (!event.start || !(event.start instanceof Date) || isNaN(event.start.getTime())) {
      console.error('Invalid event start date:', event.start);
      notifications.show({
        title: 'Erreur',
        message: 'Date de l\'événement invalide',
        color: 'red',
        autoClose: 3000
      });
      return;
    }

    // ✅ CHECK IF THIS IS A LUNCH BREAK EVENT
    if (event.lunchTime || event.type === 'pause') {
      console.log('🍽️ Detected lunch break event, opening lunch edit modal:', event);
      
      // Set the lunch modal state for editing
      setLunchtimeEditData({
        id: event.id,
        title: event.title,
        startTime: event.start,
        endTime: event.end,
        duration: event.duration || Math.round((event.end.getTime() - event.start.getTime()) / (1000 * 60)),
        doctorId: event.doctor || '',
        roomId: event.roomId || 'room-a',
        color: event.color || '#15AABF',
        notes: event.notes || '',
        isRecurring: false
      });
      
      // Open the lunch edit modal
      setShowLunchtimeEditModal(true);
      return;
    }

    try {
      console.log('Attempting to fetch appointment with ID:', event.id);

      // Check if event ID is valid
      if (!event.id || event.id === 'undefined' || event.id === '' || typeof event.id !== 'string') {
        console.warn('Invalid event ID, trying to edit with local data:', event.id);
        console.log('Event data:', event);

        // Try to edit with local data instead of showing error
        populateFormWithEventData(event);
        setShowEventModal(true);
        return;
      }

      // Check if this is a temporary event (created locally but not saved to backend)
      if (event.id.startsWith('temp-') || event.id.startsWith('local-') || event.id.startsWith('sample-')) {
        console.log('🔧 Editing temporary/local event, using local data:', event);
        // For temporary events, populate form with event data directly
        populateFormWithEventData(event);
        // Small delay to ensure form data is set before modal opens
        setTimeout(() => setShowEventModal(true), 50);
        return;
      }

      // For real events, try to fetch full appointment data from backend
      console.log('🔧 Fetching full appointment data for event ID:', event.id);

      // Fetch full appointment data with patient information from backend
      let appointment;
      let patient: Patient | null = null;

      try {
        // Use enhanced appointment service to get appointment with patient data
        const appointmentWithPatient = await appointmentService.getAppointmentWithPatient(event.id);

        if (appointmentWithPatient) {
          appointment = appointmentWithPatient.appointment;
          patient = appointmentWithPatient.patient;
          console.log('✅ Full appointment and patient data retrieved:', { appointment, patient });
        } else {
          throw new Error('Appointment not found');
        }
      } catch (apiError) {
        console.warn('⚠️ Failed to fetch full appointment data, trying fallback:', apiError);

        // Fallback: try the original API
        try {
          appointment = await appointmentAPI.get(event.id);
          console.log('✅ Fallback appointment data retrieved:', appointment);
        } catch (fallbackError) {
          console.warn('⚠️ Fallback also failed, using enhanced event data:', fallbackError);
        // Fallback to using event data if API call fails - use event data when available
        const eventData = event as CalendarEvent & Record<string, unknown>; // Type assertion to access additional properties
        const nameParts = eventData.patient_name ? eventData.patient_name.split(' ') :
                         event.title ? event.title.split(' ') : ['', ''];

        appointment = {
          id: event.id,
          title: event.title,
          description: event.desc,
          appointment_date: event.start.toISOString().split('T')[0],
          appointment_time: `${event.start.getHours().toString().padStart(2, '0')}:${event.start.getMinutes().toString().padStart(2, '0')}`,
          duration_minutes: event.duration || 15,
          consultation_duration: event.consultationDuration || event.duration || 15,
          resource_id: event.roomId,
          // Use event data when available, fallback to empty values
          patient_first_name: event.first_name || nameParts[0] || '',
          patient_last_name: event.last_name || nameParts.slice(1).join(' ') || '',
          patient_title: eventData.patient_title || '',
          gender: eventData.gender || 'Homme',
          etat_civil: eventData.etat_civil || '',
          cin: eventData.cin || '',
          social_security: eventData.social_security || '',
          profession: eventData.profession || '',
          birth_place: eventData.birthPlace || '',
          father_name: eventData.fatherName || '',
          mother_name: eventData.motherName || '',
          blood_group: eventData.bloodGroup || '',
          allergies: eventData.allergies || '',
          patient_phone: eventData.phone_number || eventData.patient_phone || '',
          patient_email: eventData.email || eventData.patient_email || '',
          patient_address: eventData.address || eventData.patient_address || '',
          doctor_assigned: eventData.doctor_assigned || eventData.docteur || '',
          appointment_type: eventData.appointment_type || eventData.typeConsultation || 'consultation',
          notes: eventData.notes || eventData.comment || ''
        };
        }
      }

      // Pre-fill the form with complete appointment data
      const eventDate = event.start.toISOString().split('T')[0];
      const eventTime = `${event.start.getHours().toString().padStart(2, '0')}:${event.start.getMinutes().toString().padStart(2, '0')}`;

      console.log('🔄 Populating form with appointment data:', appointment);
      console.log('📅 Event date:', eventDate, 'Event time:', eventTime);

      // Helper function to safely get property from appointment object
      const getAppointmentProperty = (key: string, fallback: string = ''): string => {
        const appt = appointment as Record<string, unknown>;
        return (appt[key] as string) || fallback;
      };

      // Helper function to safely get numeric property
      const getAppointmentNumber = (key: string, fallback: number = 0): number => {
        const appt = appointment as Record<string, unknown>;
        const value = appt[key];
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
          const parsed = parseInt(value, 10);
          return isNaN(parsed) ? fallback : parsed;
        }
        return fallback;
      };

      // Helper function to safely get gender with validation
      const getValidGender = (key: string): '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre' => {
        const value = getAppointmentProperty(key);
        const validGenders: Array<'' | 'Homme' | 'Femme' | 'Enfant' | 'Autre'> = ['', 'Homme', 'Femme', 'Enfant', 'Autre'];
        return validGenders.includes(value as '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre') ? value as '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre' : 'Homme';
      };

      // Helper function to safely get marital status with validation
      const getValidMaritalStatus = (key: string): '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)' => {
        const value = getAppointmentProperty(key);
        const validStatuses: Array<'' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)'> = ['', 'Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf(ve)'];
        return validStatuses.includes(value as '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)') ? value as '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)' : '';
      };

      // Helper function to safely get consultation type with validation
      const getValidConsultationType = (key: string): '' | 'consultation' | 'urgence' | 'controle' | 'chirurgie' => {
        const value = getAppointmentProperty(key) || getAppointmentProperty('typeConsultation');
        const validTypes: Array<'' | 'consultation' | 'urgence' | 'controle' | 'chirurgie'> = ['', 'consultation', 'urgence', 'controle', 'chirurgie'];
        return validTypes.includes(value as '' | 'consultation' | 'urgence' | 'controle' | 'chirurgie') ? value as '' | 'consultation' | 'urgence' | 'controle' | 'chirurgie' : 'consultation';
      };

      // Helper function to safely get agenda status with validation
      const getValidAgendaStatus = (key: string): '' | 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose' => {
        const value = getAppointmentProperty(key);
        const validStatuses: Array<'' | 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose'> =
          ['', 'scheduled', 'confirmed', 'cancelled', 'completed', 'visite_malade', 'visitor_counter', 're_diagnose'];
        return validStatuses.includes(value as '' | 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose')
          ? value as '' | 'scheduled' | 'confirmed' | 'cancelled' | 'completed' | 'visite_malade' | 'visitor_counter' | 're_diagnose'
          : 'scheduled';
      };

      setFormData(prev => {
        const newFormData = {
          ...prev,
          // Store the event ID for editing detection
          id: event.id,

          // Basic info
          first_name: getAppointmentProperty('patient_first_name') || event.title.split(' ')[0] || '',
          last_name: getAppointmentProperty('patient_last_name') || event.title.split(' ').slice(1).join(' ') || '',
          patient_title: getAppointmentProperty('patient_title'),
          title: event.title || `${getAppointmentProperty('patient_first_name')} ${getAppointmentProperty('patient_last_name')}`.trim(),
          date: eventDate,
          start: eventTime,
          end: `${event.end.getHours().toString().padStart(2, '0')}:${event.end.getMinutes().toString().padStart(2, '0')}`,
          desc: getAppointmentProperty('description') || event.desc || '',
          duration: Math.max(getAppointmentNumber('consultation_duration') || getAppointmentNumber('duration_minutes') || event.duration || 30, 15), // Ensure minimum 15 minutes
          resourceId: getAppointmentProperty('resource_id') || event.roomId || 'room-a',

          // Patient details
          birth_date: getAppointmentProperty('birth_date') || getAppointmentProperty('date_of_birth'),
          age: getAppointmentNumber('age'),
          gender: getValidGender('gender'),
          etat_civil: getValidMaritalStatus('etat_civil'),
          cin: getAppointmentProperty('cin') || getAppointmentProperty('national_id_number'),
          social_security: getAppointmentProperty('social_security'),
          profession: getAppointmentProperty('profession'),
          birthPlace: getAppointmentProperty('birth_place'),
          fatherName: getAppointmentProperty('father_name'),
          motherName: getAppointmentProperty('mother_name'),
          bloodGroup: getAppointmentProperty('blood_group'),
          allergies: getAppointmentProperty('allergies'),

          // Contact info
          phone_number: getAppointmentProperty('patient_phone') || getAppointmentProperty('phone_number') || getAppointmentProperty('phone_numbers'),
          landline_number: getAppointmentProperty('patient_landline') || getAppointmentProperty('landline_number') || getAppointmentProperty('landline'),
          email: getAppointmentProperty('patient_email') || getAppointmentProperty('email'),
          address: getAppointmentProperty('patient_address') || getAppointmentProperty('address'),

          // Appointment details
          doctor_assigned: getAppointmentProperty('doctor_assigned') || getAppointmentProperty('docteur'),
          typeConsultation: getValidConsultationType('appointment_type'),
          notes: getAppointmentProperty('notes'),
          etat_aganda: getValidAgendaStatus('etat_aganda'),
          agenda: getAppointmentProperty('agenda') || getAppointmentProperty('agenda_type') || '',
          comment: getAppointmentProperty('comment') || getAppointmentProperty('commentaire') || '',
          Commentairelistedattente: getAppointmentProperty('Commentairelistedattente') || getAppointmentProperty('commentaire_liste_attente') || '',
        };

        console.log('✅ New form data set for editing:', newFormData);
        console.log('🔍 Checking specific fields:', {
          id: newFormData.id,
          comment: newFormData.comment,
          agenda: newFormData.agenda,
          Commentairelistedattente: newFormData.Commentairelistedattente,
          resourceId: newFormData.resourceId,
          email: newFormData.email,
          first_name: newFormData.first_name,
          last_name: newFormData.last_name
        });
        return newFormData;
      });

      console.log('Form data set for editing with full appointment data');
      // Mark that this is an edit operation
      setIsTimeSlotClick(false);

      // Open the modal with a small delay to ensure form data is set
      setTimeout(() => setShowEventModal(true), 50);
    } catch (error) {
      console.error('Error in handleEventEdit:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la préparation de l\'édition',
        color: 'red',
        autoClose: 3000
      });
    }
  }, [populateFormWithEventData]);

  const handleEventDrag = useCallback(async (event: CalendarEvent) => {
    console.log('🔄 Event dragged:', event);
    console.log('🔄 New resource ID:', event.resourceId);
    console.log('🔄 New start time:', event.start);
    console.log('🔄 New end time:', event.end);

    // Update the event in the local events array immediately for UI responsiveness
    setEvents(prev => prev.map(e => e.id === event.id ? event : e));

    // Persist the change to the backend if it's a real appointment (not a temp/local event)
    if (event.id && !event.id.toString().startsWith('temp-') && !event.id.toString().startsWith('local-')) {
      try {
        console.log('💾 Persisting drag changes to backend for event:', event.id);

        // Prepare update data with new time and resource
        const updateData = {
          appointment_date: event.start.toISOString().split('T')[0],
          appointment_time: event.start.toTimeString().split(' ')[0].substring(0, 5), // HH:MM format
          resource_id: event.resourceId?.toString() || 'room-a', // Ensure string type
          duration_minutes: event.duration || 30,
        };

        console.log('💾 Update data for drag:', updateData);

        // Update the appointment in the backend
        await appointmentAPI.update(event.id.toString(), updateData);

        console.log('✅ Event drag changes saved to backend successfully');

        // Show success notification
        notifications.show({
          title: 'Rendez-vous déplacé',
          message: `Le rendez-vous a été déplacé avec succès`,
          color: 'green',
          autoClose: 2000
        });

      } catch (error) {
        console.error('❌ Failed to save drag changes to backend:', error);

        // Revert the local change if backend update failed
        setEvents(prev => prev.map(e => {
          if (e.id === event.id) {
            // Revert to previous state - we need to find the original event
            console.log('⏪ Reverting drag changes due to backend error');
            return e; // Keep the current state for now, ideally we'd revert to original
          }
          return e;
        }));

        // Show error notification
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de sauvegarder le déplacement. Veuillez réessayer.',
          color: 'red',
          autoClose: 5000
        });
      }
    } else {
      console.log('ℹ️ Skipping backend update for local/temporary event:', event.id);
    }
  }, []);

  // Handle event deletion (for lunch events and other local events)
  const handleEventDelete = useCallback(async (event: CalendarEvent) => {
    console.log('🗑️ Deleting event:', event);
    console.log('🗑️ Event ID:', event.id);
    console.log('🗑️ Event Type:', event.lunchTime ? 'lunch' : 'appointment');
    console.log('🗑️ Event ID Type:', typeof event.id);
    console.log('🗑️ Event ID Length:', event.id ? event.id.toString().length : 'null');

    let backendSuccess = true;
    let backendErrorMessage = '';

    try {
      // Check if this is a lunch break event
      if (event.lunchTime || event.type === 'pause') {
        console.log('🍽️ Deleting lunch break event from backend...');
        
        // Delete lunch break from backend using pauseAPI
        if (event.id && !event.id.startsWith('temp-') && !event.id.startsWith('local-')) {
          try {
            await pauseAPI.delete(event.id);
            console.log('✅ Lunch break deleted from backend successfully');
          } catch (pauseError) {
            console.error('❌ Failed to delete lunch break from backend:', pauseError);
            backendSuccess = false;
            backendErrorMessage = 'Impossible de supprimer la pause déjeuner du serveur';
            // Continue with frontend deletion even if backend fails
          }
        }
      } else {
        console.log('📅 Deleting appointment from backend...');
        
        // Delete regular appointment from backend using appointmentAPI
        if (event.id && !event.id.startsWith('temp-') && !event.id.startsWith('local-')) {
          try {
            console.log('📡 Sending DELETE request to /api/appointments/' + event.id + '/');
            console.log('📡 Event ID for deletion:', {
              id: event.id,
              type: typeof event.id,
              stringRepresentation: event.id.toString(),
              startsWithTemp: event.id.startsWith('temp-'),
              startsWithLocal: event.id.startsWith('local-')
            });
            
            const result = await appointmentAPI.delete(event.id);
            console.log('✅ Appointment deleted from backend successfully', result);
          } catch (error: unknown) {
            console.error('❌ Failed to delete appointment from backend:', error);
            
            // Better error handling with type checking
            if (error instanceof Error) {
              console.error('❌ Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
              });
              
              backendSuccess = false;
              
              if (error.message.includes('404')) {
                backendErrorMessage = 'Rendez-vous non trouvé sur le serveur (404)';
              } else if (error.message.includes('403') || error.message.includes('401')) {
                backendErrorMessage = 'Accès refusé - vérifiez vos permissions (403/401)';
              } else if (error.message.includes('500')) {
                backendErrorMessage = 'Erreur interne du serveur (500)';
              } else {
                backendErrorMessage = 'Impossible de supprimer le rendez-vous du serveur: ' + error.message;
              }
            } else {
              console.error('❌ Unknown error type:', error);
              backendSuccess = false;
              backendErrorMessage = 'Impossible de supprimer le rendez-vous du serveur: Erreur inconnue';
            }
            
            // Continue with frontend deletion even if backend fails
          }
        } else {
          console.log('ℹ️ Skipping backend deletion for temporary/local event');
          // For temporary events, we can consider it a success since there's no backend to delete from
          backendSuccess = true;
        }
      }
    } catch (error: unknown) {
      console.error('❌ Error during backend deletion:', error);
      backendSuccess = false;
      
      if (error instanceof Error) {
        backendErrorMessage = 'Erreur lors de la suppression du serveur: ' + error.message;
      } else {
        backendErrorMessage = 'Erreur lors de la suppression du serveur';
      }
    }

    // Always update frontend state (even if backend deletion failed)
    setEvents(prev => {
      const updatedEvents = prev.filter(e => e.id !== event.id);
      console.log('✅ Event removed from frontend, remaining events:', updatedEvents.length);
      return updatedEvents;
    });

    // Show appropriate notification based on backend success
    if (backendSuccess) {
      notifications.show({
        title: 'Événement supprimé',
        message: `${event.title} a été supprimé avec succès`,
        color: 'green',
        autoClose: 3000
      });
    } else {
      notifications.show({
        title: 'Erreur',
        message: backendErrorMessage,
        color: 'red',
        autoClose: 5000
      });
    }
  }, []);

  // Navigation handlers
  const handleNavigate = useCallback((direction: 'prev' | 'next' | 'today' | Date) => {
    // If direction is a Date object (from react-big-calendar), use it directly
    if (direction instanceof Date) {
      setCurrentDate(direction);
      return;
    }

    if (direction === 'today') {
      setCurrentDate(new Date());
      return;
    }

    const newDate = new Date(currentDate);
    switch (section) {
      case 'mois':
      case 'm':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'semaine':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'jour':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
    }
    setCurrentDate(newDate);
  }, [currentDate, section]);

  // Get current date title
  const getCurrentDateTitle = () => {
    switch (section) {
      case 'mois':
      case 'm':
        return currentDate.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });
      case 'semaine':
        const startOfWeek = new Date(currentDate);
        const day = startOfWeek.getDay();
        const diff = startOfWeek.getDate() - day;
        startOfWeek.setDate(diff);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${endOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}`;
      case 'jour':
        return currentDate.toLocaleDateString('fr-FR', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' });
      default:
        return '';
    }
  };

  // Handle new event creation
  const onNewEvent = useCallback(() => {
    console.log('Creating new event...');
    // Initialize form with current date and time
    const now = new Date();
    const formattedDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format
    const formattedTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`; // HH:MM format

    setFormData(prev => {
      const newData = {
        ...prev,
        date: formattedDate,
        start: formattedTime,
        resourceId: '', // Clear any pre-selected room
      };

      // Auto-calculate end time with default 15 minutes duration
      if (formattedTime) {
        const duration = prev.duration || 15;
        newData.end = calculateEndTime(formattedTime, duration);
      }

      return newData;
    });

    // Mark that this was opened manually
    setIsTimeSlotClick(false);

    setShowEventModal(true);
  }, []);

  // Handle event creation/saving
  const handleSaveEvent = useCallback(async () => {
    console.log('handleSaveEvent called with formData:', formData);
    console.log('addToWaitingList value:', formData.addToWaitingList);



    // Validation: Check if required fields are filled
    if (!formData.first_name || !formData.last_name) {
      notifications.show({
        title: 'Erreur de validation',
        message: 'Veuillez remplir au moins le prénom et le nom du patient.',
        color: 'red',
        autoClose: 5000
      });
      return;
    }

    // Validation: Check if duration is at least 15 minutes
    if (formData.duration && formData.duration < 15) {
      notifications.show({
        title: 'Erreur de validation',
        message: 'La durée doit être d\'au moins 15 minutes.',
        color: 'red',
        autoClose: 5000
      });
      return;
    }

    // Validation: Check for any remaining email errors
    if (emailError) {
      notifications.show({
        title: 'Erreur de validation',
        message: `Erreur avec l'email: ${emailError}. Veuillez corriger l'email ou le laisser vide.`,
        color: 'red',
        autoClose: 8000
      });
      return;
    }

    // Check if we're editing an existing event (ID stored in formData.id) - MOVED UP FOR SCOPE
    const isEditing = formData.id && formData.id !== '' && !formData.id.startsWith('temp-') && !formData.id.startsWith('local-');

    // ABSOLUTE EMAIL VALIDATION - COMPLETELY BLOCK DUPLICATES
    if (formData.email && formData.email.trim()) {
      console.log('🔍 STRICT email validation for:', formData.email);

      try {
        // Direct API check for email duplicates - FOR ALL APPOINTMENTS (NEW AND EDITING)
        console.log('🔍 Performing email duplicate check for:', formData.email.trim());

          const searchResults = await enhancedPatientAPI.search({
            search: formData.email.trim(),
            limit: 10
          });

          console.log('🔍 Search results for email validation:', searchResults);
          console.log('🔍 Number of results found:', searchResults.results?.length || 0);

          if (searchResults.results && searchResults.results.length > 0) {
            console.log('🔍 Found patients:', searchResults.results.map(p => ({
              id: p.id,
              email: p.email,
              name: `${p.first_name} ${p.last_name}`
            })));
          }

          const emailExists = searchResults.results.some(patient => {
            const patientEmail = patient.email?.toLowerCase();
            const formEmail = formData.email?.toLowerCase();

            // If editing, exclude the current patient by ID
            if (isEditing && formData.id && patient.id === formData.id) {
              console.log(`🔍 Skipping self-check for editing patient: ${patient.id}`);
              return false;
            }

            console.log(`🔍 Comparing: "${patientEmail}" === "${formEmail}"`, patientEmail === formEmail);
            return patientEmail === formEmail;
          });

          console.log('🔍 Email exists check result:', emailExists);

          if (emailExists) {
            console.error('❌ DUPLICATE EMAIL DETECTED - BLOCKING SUBMISSION');
            notifications.show({
              title: '⚠️ EMAIL EXISTE DÉJÀ',
              message: `L'email "${formData.email}" existe déjà dans le système. Veuillez modifier l'email pour continuer.`,
              color: 'red',
              autoClose: 10000
            });

            // Set error state to show red border
            setEmailError('Cet email existe déjà - Veuillez le modifier pour continuer');
            return; // COMPLETELY BLOCK FORM SUBMISSION
          } else {
            console.log('✅ Email is unique, proceeding with submission');
          }
      } catch (emailCheckError) {
        console.error('❌ Email validation failed:', emailCheckError);
        notifications.show({
          title: '⚠️ Erreur de validation',
          message: 'Impossible de vérifier l\'email. Veuillez réessayer.',
          color: 'red',
          autoClose: 8000
        });
        return; // BLOCK SUBMISSION IF VALIDATION FAILS
      }
    }

    // Set default date and time if not provided
    if (!formData.date && !formData.start) {
      const today = new Date();
      formData.date = today.toISOString().split('T')[0]; // YYYY-MM-DD format
      formData.start = '09:00'; // Default time
    }

    try {
      // CRITICAL: Ensure we're working with a clean copy of formData to prevent data sharing
      const safeFormData = { ...formData };
      
      // isEditing is already defined above - use it here
      const eventId = isEditing ? (safeFormData.id as string) : Date.now().toString();

      console.log('🔄 SAVE EVENT CALLED');
      console.log('🔍 EDIT DEBUG - formData.id:', formData.id, 'type:', typeof formData.id);
      console.log('🔍 EDIT DEBUG - isEditing:', isEditing, 'eventId:', eventId);
      console.log('🔍 EDIT DEBUG - formData.title:', formData.title);
      console.log('🔍 EDIT DEBUG - resourceId:', formData.resourceId, 'eventResourceId:', eventResourceId);

      if (isEditing) {
        console.log('✅ EDITING MODE: Will update existing appointment with ID:', eventId);
      } else {
        console.log('✅ CREATE MODE: Will create new appointment');
      }

      // SIMPLIFIED EMAIL HANDLING - Allow real emails to be sent
      if (safeFormData.email && safeFormData.email.trim()) {
        console.log('📧 PROCESSING EMAIL:', safeFormData.email);
        // Enhanced patient verification
        try {
          const searchResults = await enhancedPatientAPI.search({
            search: safeFormData.email.trim(),
            limit: 10
          });

          const emailToCheck = safeFormData.email.trim().toLowerCase();
          const duplicatePatient = searchResults.results.find(patient =>
            patient.email?.toLowerCase() === emailToCheck
          );

          if (duplicatePatient && !isEditing) {
            console.error('❌ DUPLICATE EMAIL FOUND - BLOCKING ALL DUPLICATES');

            // STRICT: Block ALL duplicate emails regardless of patient identity
            notifications.show({
              title: '⚠️ EMAIL EXISTE DÉJÀ',
              message: `L'email "${safeFormData.email}" existe déjà dans le système. Les emails en double ne sont pas autorisés. Veuillez utiliser une adresse email différente.`,
              color: 'red',
              autoClose: 10000
            });

            // Set error state to show red border
            setEmailError('Cet email existe déjà - Veuillez utiliser une adresse email différente');
            return; // COMPLETELY BLOCK FORM SUBMISSION
          }
        } catch (emailCheckError) {
          console.error('❌ Email validation failed:', emailCheckError);
          notifications.show({
            title: '⚠️ Erreur de validation',
            message: 'Impossible de vérifier l\'email. Veuillez réessayer.',
            color: 'red',
            autoClose: 8000
          });
          return; // BLOCK SUBMISSION IF VALIDATION FAILS
        }
      }

      // CRITICAL: Don't remove event here - wait until after successful API call
      // This prevents event loss if API call fails

      if (formData.addToWaitingList) {
        // Add to waiting list using API
        console.log('Adding to waiting list:', safeFormData);

        // Prepare waiting list data for local storage
       

        try {
          // Use the waiting list API to add to backend
          console.log('🔄 Adding to waiting list backend...');
          const { waitingListAPI } = await import('@/services/api');
          const response = await waitingListAPI.add(safeFormData);

          console.log('✅ Successfully added to waiting list backend:', response);

          // Create waiting list item with backend response ID
          const waitingListItem: WaitingListPatient = {
            id: response.id || `temp-waiting-${Date.now()}`,
            first_name: safeFormData.first_name,
            last_name: safeFormData.last_name,
            typeConsultation: safeFormData.typeConsultation,
            eventType: safeFormData.etat_aganda as 'visit' | 'visitor-counter' | 'completed' | 'diagnosis' || 'visit',
            phone_number: safeFormData.phone_number,
            start: safeFormData.start,
            duration: safeFormData.duration,
            color: '#3799CE',
            docteur: safeFormData.doctor_assigned,
            date: safeFormData.date,
            title: `${safeFormData.first_name} ${safeFormData.last_name}`.trim(),
            end: safeFormData.end,
            desc: safeFormData.desc,
            address: safeFormData.address,
            notes: safeFormData.notes,
            isActive: true,
            status: 'waiting_list',
            // Add resource/room information
            resourceId: safeFormData.resourceId || eventResourceId?.toString() || 'room-1',
            agenda: safeFormData.agenda || '',
            email: safeFormData.email || '',
            consultationDuration: safeFormData.duration || 30
          };

        setWaitingList(prev => {
          const newWaitingList = [...prev, waitingListItem];
          console.log('New waiting list:', newWaitingList);
          // Notify parent component about waiting list update
          if (onWaitingListUpdate) {
            onWaitingListUpdate(newWaitingList);
          }
          return newWaitingList;
        });

          // Only remove from calendar if explicitly requested
          if (formData.removeFromCalendar && formData.id) {
            setEvents(prev => {
              const updatedEvents = prev.filter(event => event.id !== formData.id);
              console.log('Removed event from calendar:', formData.id);
              console.log('Updated events:', updatedEvents);
              return updatedEvents;
            });
          }

          // Show waiting list notification
          notifications.show({
            title: 'Ajouté à la liste d\'attente',
            message: formData.removeFromCalendar
              ? `${formData.first_name} ${formData.last_name} a été ajouté à la liste d'attente et retiré du calendrier`
              : `${formData.first_name} ${formData.last_name} a été ajouté à la liste d'attente (reste au calendrier)`,
            color: 'orange',
            autoClose: 3000
          });

          // Close modal and reset form after successful waiting list addition
          setShowEventModal(false);
          setIsTimeSlotClick(false);
          setFormData({
            title: '',
            start: '',
            end: '',
            desc: '',
            patient_title: '',
            first_name: '',
            last_name: '',
            email: '',
            phone_number: '',
            landline_number: '',
            address: '',
            birth_date: '',
            age: null,
            gender: '',
            cin: '',
            social_security: '',
            etat_civil: '',
            countryId: '',
            date: '',
            duration: 30,
            doctor_assigned: '',
            typeConsultation: 'consultation',
            etat_aganda: 'scheduled',
            notes: '',
            comment: '',
            agenda: '',
            is_active: true,
            user_type: 'patient',
            isFormInvalid: false,
            isDraft: false,
            is_permanent: false,
            addToWaitingList: false,
            profession: '',
            birthPlace: '',
            fatherName: '',
            motherName: '',
            bloodGroup: '',
            resourceId: `room-1`,
            Commentairelistedattente: '',
            checkedAppelvideo: false,
            checkedRappelSms: false,
            checkedRappelEmail: false,
            type: 'visit',
            removeFromCalendar: false,
            rescheduleDateTime: '',
            maritalStatus: '',
            emergencyContact: '',
            emergencyPhone: '',
            medicalHistory: '',
            allergies: '',
            currentMedications: '',
            insuranceProvider: '',
            insuranceNumber: '',
            preferredLanguage: '',
            referredBy: '',
            specialNeeds: '',
            consentGiven: false,
            photoConsent: false,
            dataProcessingConsent: false,
            marketingConsent: false,
            id: '',
            color: '#3799CE',
            docteur: '',
            mobility_assistance: false,
            interpreter_required: false,
            special_accommodations: ''
          });

          return; // Exit early after adding to waiting list
        } catch (waitingListError) {
          console.error('Failed to add to waiting list:', waitingListError);

          // Create a local waiting list item as fallback
          const fallbackWaitingListItem: WaitingListPatient = {
            id: `temp-waiting-${Date.now()}`,
            first_name: formData.first_name,
            last_name: formData.last_name,
            typeConsultation: formData.typeConsultation,
            eventType: formData.etat_aganda as 'visit' | 'visitor-counter' | 'completed' | 'diagnosis' || 'visit',
            phone_number: formData.phone_number,
            start: formData.start,
            duration: formData.duration,
            color: '#3799CE',
            docteur: formData.doctor_assigned || 'Unknown Doctor',
            desc: formData.desc,
            address: formData.address,
            notes: formData.notes,
            date: formData.date,
            isActive: true,
            status: 'waiting_list'
          };

          // Add to local waiting list
          setWaitingList(prev => [...prev, fallbackWaitingListItem]);
          if (onWaitingListUpdate) {
            onWaitingListUpdate([...waitingList, fallbackWaitingListItem]);
          }

          // Only remove from calendar if explicitly requested
          if (formData.removeFromCalendar && formData.id) {
            setEvents(prev => {
              const updatedEvents = prev.filter(event => event.id !== formData.id);
              console.log('Removed event from calendar (fallback):', formData.id);
              return updatedEvents;
            });
          }

          notifications.show({
            title: 'Ajouté à la liste d\'attente (local)',
            message: formData.removeFromCalendar
              ? 'Patient ajouté à la liste d\'attente locale et retiré du calendrier. Synchronisation avec le serveur en cours...'
              : 'Patient ajouté à la liste d\'attente locale (reste au calendrier). Synchronisation avec le serveur en cours...',
            color: 'yellow',
            autoClose: 5000
          });

          // Close modal and reset form after successful waiting list addition
          setShowEventModal(false);
          setIsTimeSlotClick(false);
          setFormData({
            title: '',
            start: '',
            end: '',
            desc: '',
            patient_title: '',
            first_name: '',
            last_name: '',
            email: '',
            phone_number: '',
            landline_number: '',
            address: '',
            birth_date: '',
            age: null,
            gender: '',
            cin: '',
            social_security: '',
            etat_civil: '',
            countryId: '',
            date: '',
            duration: 30,
            doctor_assigned: '',
            typeConsultation: 'consultation',
            etat_aganda: 'scheduled',
            notes: '',
            comment: '',
            agenda: '',
            is_active: true,
            user_type: 'patient',
            isFormInvalid: false,
            isDraft: false,
            is_permanent: false,
            addToWaitingList: false,
            profession: '',
            birthPlace: '',
            fatherName: '',
            motherName: '',
            bloodGroup: '',
            resourceId: `room-1`,
            Commentairelistedattente: '',
            checkedAppelvideo: false,
            checkedRappelSms: false,
            checkedRappelEmail: false,
            type: 'visit',
            removeFromCalendar: false,
            rescheduleDateTime: '',
            maritalStatus: '',
            emergencyContact: '',
            emergencyPhone: '',
            medicalHistory: '',
            allergies: '',
            currentMedications: '',
            insuranceProvider: '',
            insuranceNumber: '',
            preferredLanguage: '',
            referredBy: '',
            specialNeeds: '',
            consentGiven: false,
            photoConsent: false,
            dataProcessingConsent: false,
            marketingConsent: false,
            id: '',
            color: '#3799CE',
            docteur: '',
            mobility_assistance: false,
            interpreter_required: false,
            special_accommodations: ''
          });

          return; // Exit early after adding to waiting list
        }
      } else {
        // Ensure date is in correct format (YYYY-MM-DD)
        const formAppointmentDate = safeFormData.date || new Date().toISOString().split('T')[0];
        const formAppointmentTime = safeFormData.start || '11:00';

        // Map frontend consultation types to backend values
        const consultationTypeMapping: Record<string, string> = {
          'Consultation générale': 'consultation',
          'Consultation spécialisée': 'consultation_specialisee',
          'Contrôle': 'controle',
          'Urgence': 'urgence',
          'Téléconsultation': 'consultation',
          'Vaccination': 'procedure',
          'Bilan de santé': 'routine_checkup',
          'consultation': 'consultation',
          'urgence': 'urgence',
          'controle': 'controle',
          'chirurgie': 'chirurgie',
        };

        const mappedAppointmentType = consultationTypeMapping[safeFormData.typeConsultation] || 'consultation';

        // Validate doctor ID before creating appointment
        let selectedDoctor = doctors.find(d => d.id === safeFormData.doctor_assigned);

        // If no valid doctor found, try to use the first available doctor
        if (!selectedDoctor && doctors.length > 0) {
          console.warn('Selected doctor not found, using first available doctor:', formData.doctor_assigned);
          selectedDoctor = doctors[0];

          // Update form data with the valid doctor
          setFormData(prev => ({
            ...prev,
            doctor_assigned: selectedDoctor!.id
          }));

          console.log('Using fallback doctor:', selectedDoctor);
        }

        // If still no doctor found, show error
        if (!selectedDoctor) {
          console.error('No doctors available in the system');
          console.log('Available doctors:', doctors);
          notifications.show({
            title: 'Erreur',
            message: 'Aucun docteur disponible. Veuillez ajouter un docteur au système.',
            color: 'red',
          });
          return;
        }

        console.log('Creating appointment with doctor:', selectedDoctor);

        // CRITICAL: Properly handle doctor vs assistant assignment
        // - doctor_assigned field: can be either doctor or assistant ID  
        // - doctor field: must always be a doctor ID (backend validation)
        let doctorIdForAppointment = '';
        let selectedUser: { id: string; name: string; email: string; user_type: string; } | undefined = undefined;
        let assignedDoctorId: string | null = null;
        
        if (safeFormData.doctor_assigned) {
          // Find the selected user (could be doctor or assistant)
          // First, look in the main doctors array
          selectedUser = doctors.find(d => d.id === safeFormData.doctor_assigned);
          
          // If not found in doctors array, search in assistants within each doctor
          if (!selectedUser) {
            for (const doctor of doctors) {
              if (doctor.assistants) {
                const assistant = doctor.assistants.find(a => a.id === safeFormData.doctor_assigned);
                if (assistant) {
                  // Found assistant - use a simplified approach
                  selectedUser = {
                    id: assistant.id,
                    name: assistant.name,
                    email: assistant.email,
                    user_type: 'assistant'
                  };
                  assignedDoctorId = doctor.id; // Store the assigned doctor ID separately
                  console.log('✅ Assistant found:', assistant.name, 'assigned to doctor:', doctor.name, 'Doctor ID:', doctor.id);
                  break;
                }
              }
            }
          }
          
          if (selectedUser) {
            if (selectedUser.user_type === 'doctor') {
              // If a doctor is selected, use their ID for both fields
              doctorIdForAppointment = selectedUser.id;
              console.log('✅ Doctor selected:', selectedUser.name, 'ID:', selectedUser.id);
            } else if (selectedUser.user_type === 'assistant') {
              // If an assistant is selected, use the assigned doctor's ID
              if (assignedDoctorId) {
                doctorIdForAppointment = assignedDoctorId;
                console.log('✅ Assistant selected:', selectedUser.name, 'assigned to doctor ID:', assignedDoctorId);
              } else {
                console.error('❌ Assistant has no assigned doctor:', selectedUser.name);
                notifications.show({
                  title: 'Erreur',
                  message: 'L\'assistant sélectionné n\'a pas de docteur assigné.',
                  color: 'red',
                });
                return;
              }
            } else {
              console.error('❌ Invalid user type selected:', selectedUser.user_type);
              notifications.show({
                title: 'Erreur',
                message: 'Type d\'utilisateur invalide sélectionné.',
                color: 'red',
              });
              return;
            }
          } else {
            console.error('❌ Selected user not found:', safeFormData.doctor_assigned);
            notifications.show({
              title: 'Erreur',
              message: 'Utilisateur sélectionné introuvable.',
              color: 'red',
            });
            return;
          }
        } else {
          console.error('❌ No doctor/assistant selected');
          notifications.show({
            title: 'Erreur',
            message: 'Veuillez sélectionner un docteur ou assistant.',
            color: 'red',
          });
          return;
        }

        // Create appointment using API
        const appointmentData: AppointmentFormData = {
          title: `${safeFormData.first_name} ${safeFormData.last_name}`.trim(),
          description: `${safeFormData.typeConsultation || 'Consultation'} - ${safeFormData.desc || ''}`.trim(),
          appointment_type: mappedAppointmentType,
          appointment_date: formAppointmentDate,
          appointment_time: formAppointmentTime,
          duration_minutes: Math.max(safeFormData.duration || 30, 15), // Ensure minimum 15 minutes
          consultation_duration: Math.max(safeFormData.duration || 30, 15), // Ensure minimum 15 minutes
          doctor: doctorIdForAppointment, // Always a doctor ID (validated above)
          doctor_assigned: safeFormData.doctor_assigned, // Can be doctor or assistant ID
          notes: safeFormData.notes || '',

          // Comment and agenda fields
          comment: formData.comment || '',
          commentaire: formData.comment || '', // Alternative field name
          Commentairelistedattente: formData.Commentairelistedattente || '',
          commentaire_liste_attente: formData.Commentairelistedattente || '', // Alternative field name
          agenda: formData.agenda || '',
          agenda_type: formData.agenda || '', // Alternative field name
          etat_aganda: formData.etat_aganda || 'scheduled',

          // CRITICAL: Room/Resource assignment - ENSURE PROPER MAPPING
          resource_id: (() => {
            let finalResourceId;

            console.log('🏠 RESOURCE DEBUG - BEFORE MAPPING:', {
              formDataResourceId: safeFormData.resourceId,
              eventResourceId: eventResourceId,
              isEditing: isEditing
            });

            // EDITING MODE: Use the updated resource_id from form data, not the existing one
            // The previous logic was incorrectly preserving the old resource_id
            if (isEditing && safeFormData.id) {
              // For editing, always use the form data resource_id if available
              if (safeFormData.resourceId && safeFormData.resourceId.trim() !== '') {
                finalResourceId = safeFormData.resourceId; // Use as-is (room-a or room-b)
                console.log('🏠 EDITING - Using updated resource_id from form:', finalResourceId);
              } else {
                // Fallback to eventResourceId state if formData.resourceId is not set
                finalResourceId = eventResourceId === 1 ? 'room-a' : 'room-b';
                console.log('🏠 EDITING - Using eventResourceId state:', finalResourceId);
              }
            }
            // Priority 1: Use formData.resourceId if it's valid (not empty)
            else if (safeFormData.resourceId && safeFormData.resourceId.trim() !== '') {
              finalResourceId = safeFormData.resourceId; // Use as-is (room-a or room-b)
            }
            // Priority 2: Use eventResourceId state
            else if (eventResourceId) {
              finalResourceId = eventResourceId === 1 ? 'room-a' : 'room-b';
            }
            // Priority 3: Default fallback
            else {
              finalResourceId = 'room-a';
            }

            console.log('🏠 RESOURCE DEBUG - FINAL MAPPING:', {
              finalResourceId: finalResourceId,
              willSendToBackend: finalResourceId
            });

            return finalResourceId;
          })(),

          // CRITICAL: Event type mapping for backend - USE VALID CHOICES ONLY
          event_type: (() => {
            // Valid choices from backend: 'visit', 'visitor-counter', 'completed', 'diagnosis'
            const eventTypeMapping = {
              'visit': 'visit',
              'visitor-counter': 'visitor-counter',
              'completed': 'completed',
              'diagnosis': 'diagnosis',
              'consultation': 'visit',  // Map consultation to visit
              'urgence': 'visit',       // Map urgence to visit
              'controle': 'visit',      // Map controle to visit
              'chirurgie': 'diagnosis'  // Map chirurgie to diagnosis
            };

            const mappedEventType = eventTypeMapping[safeFormData.typeConsultation as keyof typeof eventTypeMapping] ||
                                   eventTypeMapping[safeFormData.etat_aganda as keyof typeof eventTypeMapping] ||
                                   'visit';

            console.log('🎯 EVENT TYPE DEBUG:', {
              typeConsultation: safeFormData.typeConsultation,
              etat_aganda: safeFormData.etat_aganda,
              mappedEventType: mappedEventType
            });

            return mappedEventType;
          })(),
          // Patient data for creation if needed
          patient_title: safeFormData.patient_title || '',
          patient_first_name: safeFormData.first_name || 'Unknown',
          patient_last_name: safeFormData.last_name || 'Patient',
          patient_phone: safeFormData.phone_number || '',
          patient_landline: safeFormData.landline_number || '',
          landline_number: safeFormData.landline_number || '', // Backend expects this field name
          patient_address: safeFormData.address || '',
          // CRITICAL: Add missing date_of_birth and age fields
          birth_date: safeFormData.birth_date || '', // Frontend field name
          date_of_birth: safeFormData.birth_date || '', // Backend field name
          age: safeFormData.age || undefined,
          // Additional patient details
          gender: safeFormData.gender || 'Homme',
          etat_civil: safeFormData.etat_civil || '',
          cin: safeFormData.cin || '',
          social_security: safeFormData.social_security || '',
          profession: safeFormData.profession || '',
          birth_place: safeFormData.birthPlace || '',
          father_name: safeFormData.fatherName || '',
          mother_name: safeFormData.motherName || '',
          blood_group: safeFormData.bloodGroup || '',
          allergies: safeFormData.allergies || '',
        };

        // Debug logging for field assignments
        console.log('🔍 FIELD ASSIGNMENT DEBUG:', {
          'formData.doctor_assigned': safeFormData.doctor_assigned,
          'appointmentData.doctor': appointmentData.doctor,
          'appointmentData.doctor_assigned': appointmentData.doctor_assigned,
          'selectedUserType': selectedUser?.user_type,
          'selectedUserName': selectedUser?.name,
          'assignedDoctorId': assignedDoctorId
        });

        // CRITICAL: Email handling - PREVENT DUPLICATES
        console.log('🔍 EMAIL DEBUG: formData.email value:', safeFormData.email);
        console.log('🔍 EMAIL DEBUG: formData.email type:', typeof safeFormData.email);
        console.log('🔍 EMAIL DEBUG: formData.email truthy?', !!safeFormData.email);
        console.log('🔍 EMAIL DEBUG: isEditing?', isEditing);

        // For editing, always send the email (even if duplicate)
        // For new appointments, only send if no duplicates detected
        if (safeFormData.email && safeFormData.email.trim()) {
          if (isEditing) {
            appointmentData.patient_email = safeFormData.email.trim();
            console.log('✅ EDITING: SENDING EMAIL AS-IS:', appointmentData.patient_email);
          } else {
            // For new appointments, we already validated no duplicates above
            appointmentData.patient_email = safeFormData.email.trim();
            console.log('✅ NEW APPOINTMENT: SENDING VALIDATED EMAIL:', appointmentData.patient_email);
          }
        } else {
          console.log('⚠️ NO EMAIL TO SEND - formData.email is empty or falsy');
          appointmentData.patient_email = '';
        }

        console.log('🔍 FINAL appointmentData.patient_email:', appointmentData.patient_email);
        console.log('Sending appointment data:', appointmentData);
        console.log('🔍 PATIENT FIELDS DEBUG:', {
          patient_title: appointmentData.patient_title,
          gender: appointmentData.gender,
          etat_civil: appointmentData.etat_civil,
          cin: appointmentData.cin,
          social_security: appointmentData.social_security,
          profession: appointmentData.profession,
          birth_place: appointmentData.birth_place,
          father_name: appointmentData.father_name,
          mother_name: appointmentData.mother_name,
          blood_group: appointmentData.blood_group,
          allergies: appointmentData.allergies
        });

        // First test if the API is reachable by trying to list appointments
        try {
          console.log('Testing API connectivity...');
          const testResponse = await appointmentAPI.list();
          console.log('API connectivity test successful:', testResponse);
        } catch (testError) {
          console.error('API connectivity test failed:', testError);
        }

        let response;
        if (isEditing) {
          // Update existing appointment using enhanced service
          console.log('🔄 UPDATING APPOINTMENT:', eventId);
          console.log('🔄 UPDATE DATA:', JSON.stringify(appointmentData, null, 2));

          try {
            // Use enhanced service for updating with patient relationship
            response = await appointmentService.createAppointmentWithPatient(appointmentData, eventId);

            if (!response) {
              // Fallback to basic update API
              console.log('⚠️ Enhanced update failed, using basic update...');
              response = await appointmentAPI.update(eventId, appointmentData);
            }

            console.log('✅ APPOINTMENT UPDATED SUCCESSFULLY:', response);
            console.log('🔍 EDIT RESPONSE DEBUG:', {
              id: (response as unknown as Record<string, unknown>).id,
              resource_id: (response as unknown as Record<string, unknown>).resource_id,
              patient_title: (response as unknown as Record<string, unknown>).patient_title,
              gender: (response as unknown as Record<string, unknown>).gender,
              etat_civil: (response as unknown as Record<string, unknown>).etat_civil
            });
          } catch (updateError) {
            console.error('❌ APPOINTMENT UPDATE FAILED:', updateError);
            throw updateError;
          }
        } else {
          // Create new appointment with proper patient relationship
          console.log('🏥 Creating appointment with enhanced service...');
          try {
            response = await appointmentService.createAppointmentWithPatient(appointmentData);

            if (!response) {
              // Fallback to original API if enhanced service fails
              console.log('⚠️ Enhanced service failed, using fallback...');
              response = await appointmentAPI.create(appointmentData);
            }

            console.log('Created appointment:', response);
            console.log('🔍 CREATE RESPONSE DEBUG:', {
              id: (response as unknown as Record<string, unknown>).id,
              resource_id: (response as unknown as Record<string, unknown>).resource_id,
              patient_title: (response as unknown as Record<string, unknown>).patient_title,
              gender: (response as unknown as Record<string, unknown>).gender,
              etat_civil: (response as unknown as Record<string, unknown>).etat_civil
            });
          } catch (createError) {
            console.error('❌ APPOINTMENT CREATION FAILED:', createError);
            
            // Check if it's a duplicate email error
            const errorMessage = (createError as unknown as { message?: string })?.message || '';
            if (errorMessage.includes('EMAIL DUPLIQUÉ DÉTECTÉ') || errorMessage.includes('email existe déjà')) {
              notifications.show({
                title: '⚠️ EMAIL EXISTE DÉJÀ',
                message: 'Un patient avec cet email existe déjà dans le système. Veuillez modifier l\'email pour continuer.',
                color: 'red',
                autoClose: 10000
              });
              // Set error state to show red border
              setEmailError('Cet email existe déjà - Veuillez le modifier pour continuer');
              return; // COMPLETELY BLOCK FORM SUBMISSION
            }
            
            // For other errors, show a generic message
            notifications.show({
              title: 'Erreur',
              message: 'Impossible de créer le rendez-vous. Veuillez réessayer.',
              color: 'red',
              autoClose: 5000
            });
            return; // BLOCK FORM SUBMISSION
          }
        }

        // Convert API response to calendar event format
        // Safe property access for response data
        const getResponseProperty = (key: string, fallback: unknown = ''): unknown => {
          return (response as unknown as Record<string, unknown>)[key] || fallback;
        };

        const responseAppointmentDate = getResponseProperty('appointment_date') as string;
        const responseAppointmentTime = getResponseProperty('appointment_time') as string;
        const responseDurationMinutes = (getResponseProperty('duration_minutes') as number) || (getResponseProperty('consultation_duration') as number) || 30;

        const eventStart = new Date(`${responseAppointmentDate}T${responseAppointmentTime}`);
        const eventEnd = new Date(eventStart.getTime() + responseDurationMinutes * 60 * 1000);

        // Enhanced event data with all available fields for better editing support
        const eventData: CalendarEvent & Record<string, unknown> = {
          id: (response as unknown as Record<string, unknown>).id as string,
          title: getResponseProperty('title') as string,
          start: eventStart,
          end: eventEnd,
          desc: getResponseProperty('description') as string,
          color: getResponseProperty('color') as string,
          roomId: (() => {
            // CRITICAL: Preserve room from multiple sources
            const resourceId = getResponseProperty('resource_id') as string;
            const eventResourceId = getResponseProperty('event_resource_id') as string;
            const currentRoom = eventResourceId === '1' || eventResourceId === 'room-a' ? 'room-a' :
                               eventResourceId === '2' || eventResourceId === 'room-b' ? 'room-b' : null;

            // Check multiple sources for room information
            if (resourceId === 'room-b' || resourceId === '2' || eventResourceId === 'room-b') return 'room-b';
            if (resourceId === 'room-a' || resourceId === '1' || eventResourceId === 'room-a') return 'room-a';
            if (currentRoom) return currentRoom;

            // CRITICAL: If editing, preserve the original room from formData
            if (isEditing && safeFormData.resourceId) {
              return ( safeFormData.resourceId === '2' || safeFormData.resourceId === 'room-b') ? 'room-b' : 'room-a';
            }

            return 'room-a'; // Only default if absolutely no room info found
          })(),
          duration: responseDurationMinutes,
          consultationDuration: responseDurationMinutes,

          // Store all form data for editing - USE SAFE FORM DATA COPY
          first_name: safeFormData.first_name,
          last_name: safeFormData.last_name,
          patient_title: safeFormData.patient_title,
          birth_date: safeFormData.birth_date,
          age: safeFormData.age,
          gender: safeFormData.gender,
          etat_civil: safeFormData.etat_civil,
          cin: safeFormData.cin,
          social_security: safeFormData.social_security,
          profession: safeFormData.profession,
          birthPlace: safeFormData.birthPlace,
          fatherName: safeFormData.fatherName,
          motherName: safeFormData.motherName,
          bloodGroup: safeFormData.bloodGroup,
          allergies: safeFormData.allergies,
          phone_number: safeFormData.phone_number,
          phone_numbers: safeFormData.phone_number, // Alternative field name
          landline_number: safeFormData.landline_number,
          email: safeFormData.email,
          address: safeFormData.address,
          doctor_assigned: safeFormData.doctor_assigned,
          docteur: safeFormData.doctor_assigned, // Alternative field name
          typeConsultation: safeFormData.typeConsultation,
          appointment_type: safeFormData.typeConsultation,
          etat_aganda: safeFormData.etat_aganda,
          notes: safeFormData.notes,

          // Comment and agenda fields
          comment: formData.comment,
          commentaire: formData.comment, // Alternative field name
          Commentairelistedattente: formData.Commentairelistedattente,
          commentaire_liste_attente: formData.Commentairelistedattente, // Alternative field name
          agenda: formData.agenda,
          agenda_type: formData.agenda, // Alternative field name

          // API response fields
          patient_first_name: getResponseProperty('patient_first_name') as string || formData.first_name,
          patient_last_name: getResponseProperty('patient_last_name') as string || formData.last_name,
          patient_phone: getResponseProperty('patient_phone') as string || formData.phone_number,
          patient_email: getResponseProperty('patient_email') as string || formData.email,
          patient_address: getResponseProperty('patient_address') as string || formData.address,

          // Patient ID fields for navigation
          patient_id: getResponseProperty('patient_id') as string || getResponseProperty('patient') as string,
          patient: getResponseProperty('patient') as string || getResponseProperty('patient_id') as string,
          
          // Doctor ID field
          doctor: getResponseProperty('doctor') as string || formData.doctor_assigned,
        };

        if (isEditing) {
          // Update existing event in local state
          setEvents(prev => {
            const updated = prev.map(event => {
              if (event.id === eventId) {
                console.log('Found matching event to update:', event.id);
                return eventData;
              }
              return event;
            });
            console.log('Events after update:', updated.map(e => ({ id: e.id, title: e.title })));
            return updated;
          });
        } else {
          // Add new event to calendar
          setEvents(prev => [...prev, eventData]);
        }

        // Show success notification
        notifications.show({
          title: isEditing ? 'Rendez-vous modifié' : 'Rendez-vous créé',
          message: `Rendez-vous pour ${safeFormData.first_name} ${safeFormData.last_name} ${isEditing ? 'modifié' : 'créé'} avec succès`,
          color: 'green',
          autoClose: 3000
        });

        // CRITICAL: Refresh events from backend after update to prevent duplication
        if (isEditing) {
          console.log('🔄 Refreshing events after update to prevent room duplication');
          setTimeout(() => {
            refreshEvents(true); // Silent refresh to avoid duplicate notifications
          }, 500); // Small delay to ensure backend is updated
        }

        // Close modal after successful save - DON'T reset form data here to preserve state
        setShowEventModal(false);
        setIsTimeSlotClick(false);

        // Reset form data only after successful save and modal close
        setTimeout(() => {
          setFormData({
            id: '',
            title: '',
            start: '',
            end: '',
            desc: '',
            patient_title: '',
            first_name: '',
            last_name: '',
            email: '',
            phone_number: '',
            landline_number: '',
            address: '',
            birth_date: '',
            age: null,
            gender: 'Homme', // Set default gender value
            cin: '',
            social_security: '',
            etat_civil: '',
            countryId: '',
            etat_aganda: '',
            doctor_assigned: doctors.length > 0 ? doctors[0].id : '',
            duration: 30,
            resourceId: 'room-a', // Default to room-a instead of empty string
            typeConsultation: '',
            date: '',
            notes: '',
            comment: '',
            agenda: '',
            is_active: false,
            user_type: '',
            isFormInvalid: false,
            isDraft: false,
            is_permanent: false,
            addToWaitingList: false,
            profession: '',
            birthPlace: '',
            fatherName: '',
            motherName: '',
            bloodGroup: '',
            maritalStatus: '',
            emergencyContact: '',
            emergencyPhone: '',
            medicalHistory: '',
            currentMedications: '',
            insuranceProvider: '',
            insuranceNumber: '',
            preferredLanguage: '',
            referredBy: '',
            specialNeeds: '',
            consentGiven: false,
            photoConsent: false,
            dataProcessingConsent: false,
            marketingConsent: false,
            allergies: '',
            Commentairelistedattente: '',
            checkedAppelvideo: false,
            checkedRappelSms: false,
            checkedRappelEmail: false,
            type: '',
            removeFromCalendar: false,
            rescheduleDateTime: '',
            mobility_assistance: false,
            interpreter_required: false,
            special_accommodations: '',
            color: '#3799CE',
            docteur: ''
          });
          // Reset email error state
          setEmailError('');
        }, 100);
      }
    } catch (error) {
      console.error('Error saving appointment:', error);

      // Extract specific error message
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';

      let userMessage = errorMessage;

      // Provide user-friendly messages for common errors
      if (errorMessage.includes('UNIQUE constraint') || errorMessage.includes('already exists') || errorMessage.includes('EMAIL DUPLIQUÉ DÉTECTÉ') || errorMessage.includes('email existe déjà')) {
        userMessage = 'Un patient avec cet email existe déjà dans le système. Veuillez utiliser un email différent ou rechercher le patient existant. Si vous êtes certain qu\'il s\'agit du même patient, veuillez vérifier que les informations de nom, prénom, téléphone et autres détails correspondent.';
        // Set error state to show red border
        setEmailError('Cet email existe déjà - Veuillez le modifier pour continuer');
      } else if (errorMessage.includes('Validation failed')) {
        userMessage = 'Les données du patient ne sont pas valides. Veuillez vérifier les champs obligatoires (nom, prénom).';
      } else if (errorMessage.includes('Patient creation failed')) {
        userMessage = 'Erreur lors de la création du patient. Veuillez vérifier les informations saisies.';
      } else if (errorMessage.includes('doctor')) {
        userMessage = 'Erreur avec la sélection du docteur. Veuillez sélectionner un docteur valide.';
      } else if (errorMessage.includes('duration')) {
        userMessage = 'La durée doit être d\'au moins 15 minutes.';
      } else if (errorMessage.includes('500')) {
        userMessage = 'Erreur serveur. Veuillez réessayer dans quelques instants.';
      } else if (errorMessage.includes('400')) {
        userMessage = 'Données invalides. Veuillez vérifier tous les champs requis.';
      }

      notifications.show({
        title: 'Erreur',
        message: `Erreur lors de la sauvegarde: ${userMessage}`,
        color: 'red',
        autoClose: 5000
      });
      return; // Don't close modal on error
    }

    // Form reset is now handled in the success block above to preserve state during editing
  }, [formData, setEvents, setWaitingList, onWaitingListUpdate, doctors, emailError, eventResourceId, waitingList, refreshEvents]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setShowEventModal(false);
    setIsTimeSlotClick(false); // Reset time slot click state
    // Reset form data
    setFormData({
      title: '',
      start: '',
      end: '',
      desc: '',
      // Patient Information
      patient_title: '',
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      landline_number: '',
      address: '',
      birth_date: '',
      age: null,
      gender: 'Homme', // Set default gender value
      cin: '',
      social_security: '',
      etat_civil: '',
      countryId: '',
      // Appointment Details
      etat_aganda: '',
      doctor_assigned: doctors.length > 0 ? doctors[0].id : '',
      duration: 15,
      resourceId: 'room-a', // Default to room-a instead of empty string
      typeConsultation: '',
      date: '',
      // Administrative
      notes: '',
      comment: '',
      agenda: '',
      is_active: true,
      user_type: 'patient',
      isFormInvalid: false,
      isDraft: false,
      is_permanent: false,
      addToWaitingList: false,
      // Additional fields for comprehensive form
      profession: '',
      birthPlace: '',
      fatherName: '',
      motherName: '',
      bloodGroup: '',
      maritalStatus: '',
      emergencyContact: '',
      emergencyPhone: '',
      medicalHistory: '',
      currentMedications: '',
      insuranceProvider: '',
      insuranceNumber: '',
      preferredLanguage: '',
      referredBy: '',
      specialNeeds: '',
      consentGiven: false,
      photoConsent: false,
      dataProcessingConsent: false,
      marketingConsent: false,
      allergies: '',
      Commentairelistedattente: '',
      checkedAppelvideo: false,
      checkedRappelSms: false,
      checkedRappelEmail: false,
      type: "visit",
      removeFromCalendar: false,
      rescheduleDateTime: '',
      mobility_assistance: false,
      interpreter_required: false,
      special_accommodations: '',
      color: '#3799CE',
      id: '',
      docteur: ''
    });
    setEmailError(''); // Clear email errors
  }, [doctors]);

  // Handle form field changes
  const handleFormChange = useCallback((field: keyof EventFormData, value: string | number | boolean | null) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };
      // Auto-calculate end time when start time or duration changes
      if (field === 'start' || field === 'duration') {
        if (newData.start) {
          // Use duration if available, otherwise default to 15 minutes
          const duration = newData.duration || 15;
          newData.end = calculateEndTime(newData.start, duration);
        }
      }

      // Auto-calculate age when birth_date changes
      if (field === 'birth_date' && value && typeof value === 'string') {
        const birthDate = new Date(value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          newData.age = age - 1;
        } else {
          newData.age = age;
        }
      }

      return newData;
    });
  }, []);

  // Filter events based on selected filter
  const filteredEvents = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    console.log('🔍 FILTERING EVENTS:', {
      totalEvents: events.length,
      currentFilter: eventFilter,
      todayDate: today.toISOString().split('T')[0],
      currentCalendarDate: currentDate.toISOString().split('T')[0]
    });

    switch (eventFilter) {
      case 'last3days':
        const threeDaysAgo = new Date(today);
        threeDaysAgo.setDate(today.getDate() - 3);
        return events.filter(event =>
          event.start >= threeDaysAgo && event.start <= now
        );

      case 'lastweek':
        const oneWeekAgo = new Date(today);
        oneWeekAgo.setDate(today.getDate() - 7);
        return events.filter(event =>
          event.start >= oneWeekAgo && event.start <= now
        );

      case 'archived':
        // Events older than 30 days
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        return events.filter(event => event.start < thirtyDaysAgo);

      case 'all':
      default:
        console.log('📋 Returning all events:', events.length);
        return events;
    }
  }, [events, eventFilter, currentDate]);

  // Handle filter change
  const handleFilterChange = useCallback((filter: 'all' | 'last3days' | 'lastweek' | 'archived') => {
    setEventFilter(filter);
  }, []);

  // Care-sheet handlers
  const handleOpenCareSheet = useCallback((patientId: string, patientName?: string) => {
    setCareSheetPatientId(patientId);
    setCareSheetPatientName(patientName || '');
    setShowCareSheetModal(true);

    // Preload care-sheet data in background
    careSheetBackgroundFetcher.getCareSheetData(patientId, { priority: 'high' });
  }, []);

  const handleCloseCareSheet = useCallback(() => {
    setShowCareSheetModal(false);
    setCareSheetPatientId('');
    setCareSheetPatientName('');
  }, []);

  const handleNavigateToFullCareSheet = useCallback(() => {
    // Navigate to the full care-sheet page
    window.open(`/dashboard/care-sheet?patientId=${careSheetPatientId}`, '_blank');
  }, [careSheetPatientId]);

  // Generic-states handlers
  const handleOpenGenericStates = useCallback((dateRange?: { start: string; end: string }) => {
    setGenericStatesDateRange(dateRange);
    setShowGenericStatesModal(true);

    // Preload generic-states data in background
    genericStatesBackgroundFetcher.getGenericStatesData({
      priority: 'high',
      dateRange
    });
  }, []);

  const handleCloseGenericStates = useCallback(() => {
    setShowGenericStatesModal(false);
    setGenericStatesDateRange(undefined);
  }, []);

  const handleNavigateToFullGenericStates = useCallback(() => {
    // Navigate to the full generic-states page
    window.open('/dashboard/generic-states', '_blank');
  }, []);

  // Medical-report handlers
  const handleOpenMedicalReport = useCallback((patientId?: string, patientName?: string, dateRange?: { start: string; end: string }) => {
    setMedicalReportPatientId(patientId || '');
    setMedicalReportPatientName(patientName || '');
    setMedicalReportDateRange(dateRange);
    setShowMedicalReportModal(true);

    // Preload medical-report data in background
    medicalReportBackgroundFetcher.getMedicalReportData({
      priority: 'high',
      patientId,
      dateRange
    });
  }, []);

  const handleCloseMedicalReport = useCallback(() => {
    setShowMedicalReportModal(false);
    setMedicalReportPatientId('');
    setMedicalReportPatientName('');
    setMedicalReportDateRange(undefined);
  }, []);

  const handleNavigateToFullMedicalReport = useCallback(() => {
    // Navigate to the full medical-report page
    window.open('/dashboard/medical-report', '_blank');
  }, []);

  // Payment handlers
  const handleOpenPayment = useCallback((patientId?: string, patientName?: string, dateRange?: { start: string; end: string }) => {
    setPaymentPatientId(patientId || '');
    setPaymentPatientName(patientName || '');
    setPaymentDateRange(dateRange);
    setShowPaymentModal(true);

    // Preload payment data in background
    paymentBackgroundFetcher.getPaymentData({
      priority: 'high',
      patientId,
      dateRange
    });
  }, []);

  const handleClosePayment = useCallback(() => {
    setShowPaymentModal(false);
    setPaymentPatientId('');
    setPaymentPatientName('');
    setPaymentDateRange(undefined);
  }, []);

  const handleNavigateToFullPayment = useCallback(() => {
    // Navigate to the full payment page
    window.open('/dashboard/payment', '_blank');
  }, []);

  // Pharmacy handlers
  const handleOpenPharmacy = useCallback((dateRange?: { start: string; end: string }) => {
    setPharmacyDateRange(dateRange);
    setShowPharmacyModal(true);

    // Preload pharmacy data in background
    pharmacyBackgroundFetcher.getPharmacyData({
      priority: 'high',
      dateRange
    });
  }, []);

  const handleClosePharmacy = useCallback(() => {
    setShowPharmacyModal(false);
    setPharmacyDateRange(undefined);
  }, []);

  const handleNavigateToFullPharmacy = useCallback(() => {
    // Navigate to the full pharmacy page
    window.open('/dashboard/pharmacy', '_blank');
  }, []);

  // Prescriptions handlers
  const handleOpenPrescriptions = useCallback((patientId?: string, patientName?: string, dateRange?: { start: string; end: string }) => {
    setPrescriptionsPatientId(patientId || '');
    setPrescriptionsPatientName(patientName || '');
    setPrescriptionsDateRange(dateRange);
    setShowPrescriptionsModal(true);

    // Preload prescriptions data in background
    prescriptionsBackgroundFetcher.getPrescriptionsData({
      priority: 'high',
      patientId,
      dateRange
    });
  }, []);

  const handleClosePrescriptions = useCallback(() => {
    setShowPrescriptionsModal(false);
    setPrescriptionsPatientId('');
    setPrescriptionsPatientName('');
    setPrescriptionsDateRange(undefined);
  }, []);

  const handleNavigateToFullPrescriptions = useCallback(() => {
    // Navigate to the full prescriptions page
    window.open('/dashboard/prescriptions', '_blank');
  }, []);

  // Parameters handlers
  const handleOpenParameters = useCallback(() => {
    setShowParametersModal(true);

    // Preload parameters data in background
    parametersBackgroundFetcher.getParametersData({
      priority: 'high'
    });
  }, []);

  const handleCloseParameters = useCallback(() => {
    setShowParametersModal(false);
  }, []);

  const handleNavigateToFullParameters = useCallback(() => {
    // Navigate to the full parameters page
    window.open('/dashboard/parameters', '_blank');
  }, []);

  // ✅ LUNCH EDIT MODAL HANDLERS - NEW
  const handleLunchtimeEditSave = useCallback(async (lunchData: {
    id: string;
    title: string;
    start: Date;
    end: Date;
    desc?: string;
    color: string;
    roomId: string;
    resourceId: string | number;
    lunchTime: boolean;
    doctorId: string;
    doctorName: string;
    notes?: string;
    isRecurring?: boolean;
  }) => {
    console.log('🍽️ UPDATING lunch break:', lunchData);
    
    try {
      // Calculate duration from start and end times
      const duration = Math.round((lunchData.end.getTime() - lunchData.start.getTime()) / (1000 * 60));
      
      // Update the lunch break in the backend using pauseAPI.update
      const updateData = {
        title: lunchData.title,
        dateFrom: lunchData.start.toISOString(),
        dateTo: lunchData.end.toISOString(),
        doctor: lunchData.doctorId,
        room: lunchData.roomId,
        resource_id: lunchData.roomId === 'room-a' ? '1' : '2',
        color: lunchData.color,
        notes: lunchData.notes,
        is_recurring: lunchData.isRecurring || false
      };
      
      console.log('🚀 Calling pauseAPI.update with:', { id: lunchData.id, data: updateData });
      const response = await pauseAPI.update(lunchData.id, updateData);
      
      console.log('✅ Backend update successful:', response);
      
      // Update the local events state
      setEvents(prev => prev.map(event => {
        if (event.id === lunchData.id) {
          return {
            ...event,
            title: lunchData.title,
            start: lunchData.start,
            end: lunchData.end,
            duration: duration,
            color: lunchData.color,
            roomId: lunchData.roomId,
            doctorId: lunchData.doctorId,
            notes: lunchData.notes,
          };
        }
        return event;
      }));
      
      // Close the edit modal
      setShowLunchtimeEditModal(false);
      setLunchtimeEditData(null);
      
      notifications.show({
        title: 'Pause déjeuner modifiée',
        message: 'La pause déjeuner a été mise à jour avec succès',
        color: 'green',
        autoClose: 3000
      });
    } catch (error) {
      console.error('❌ Failed to update lunch break:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de mettre à jour la pause déjeuner',
        color: 'red',
        autoClose: 5000
      });
    }
  }, []);

  const handleCloseLunchtimeEdit = useCallback(() => {
    setShowLunchtimeEditModal(false);
    setLunchtimeEditData(null);
  }, []);

  // Get filter label for display
  const getFilterLabel = () => {
    switch (eventFilter) {
      case 'last3days':
        return 'Filtre: 3 derniers jours';
      case 'lastweek':
        return 'Filtre: Semaine dernière';
      case 'archived':
        return 'Filtre: Archivés';
      case 'all':
      default:
        return 'Filtre';
    }
  };

  // Function to render the appropriate calendar view
  const renderCalendarView = () => {
    console.log('🔄 Rendering calendar view:', section);
    console.log('📅 Filtered events for calendar:', filteredEvents.length);
    console.log('📋 All events:', events.length);

    switch (section) {
      case "jour":
        return (
          <DayViewToggle
            currentDate={currentDate}
            events={filteredEvents}
            onTimeSlotClick={handleTimeSlotClick}
            onEventClick={handleEventEdit}
            onEventAdd={handleEventAdd}
            onEventDrop={handleEventDrag}
            onEventDelete={handleEventDelete}
            onDateChange={setCurrentDate}
            onNavigate={handleNavigate}
            waitingList={waitingList}
            onWaitingListUpdate={handleWaitingListUpdate}
          />
        );
      case "semaine":
        return (
          <WeekViewToggle
            currentDate={currentDate}
            events={filteredEvents}
            onTimeSlotClick={handleTimeSlotClick}
            onEventClick={handleEventEdit}
            onDateChange={setCurrentDate}
            onNavigate={handleNavigate}
            onWaitingListUpdate={() => {
              if (onWaitingListUpdate) {
                onWaitingListUpdate(waitingList);
              }
            }}
          />
        );
      case "mois":
        return (
          <MonthViewToggle
            currentDate={currentDate}
            events={filteredEvents}
            onTimeSlotClick={handleTimeSlotClick}
            onEventClick={handleEventEdit}
            onDateChange={setCurrentDate}
            onNavigate={handleNavigate}
            onWaitingListUpdate={() => {
              if (onWaitingListUpdate) {
                onWaitingListUpdate(waitingList);
              }
            }}
          />
        );
      case "m":
        return (
          <AgendaViewToggle
            currentDate={currentDate}
            events={filteredEvents}
            onTimeSlotClick={handleTimeSlotClick}
            onEventClick={handleEventEdit}
            onDateChange={setCurrentDate}
            onNavigate={handleNavigate}
            onWaitingListUpdate={() => {
              if (onWaitingListUpdate) {
                onWaitingListUpdate(waitingList);
              }
            }}
          />
        );
      default:
        return <div>No component found</div>;
    }
  };
  // Composant RendezVousSelector
interface RendezVousSelectorProps {
  onClose: () => void;
}
  const RendezVousSelector: React.FC<RendezVousSelectorProps> = ({ onClose }) => {
    const [selectedPeriod, setSelectedPeriod] = React.useState<'15days' | '1month' | '3months'>('15days');
    const [, setStartDate] = React.useState('12/06/2025');
    const [duration, setDuration] = React.useState(30);
    const [numberOfDays, setNumberOfDays] = React.useState(3);
    const [selectedSlots, setSelectedSlots] = React.useState<Set<string>>(new Set());
  
    // Générer les créneaux horaires
    const generateTimeSlots = () => {
      const slots = [];
      const startHour = 8;
      const endHour = 14;
      
      for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          const endMinute = minute + 30;
          const endHour = endMinute >= 60 ? hour + 1 : hour;
          const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;
          const endTime = `${endHour.toString().padStart(2, '0')}:${adjustedEndMinute.toString().padStart(2, '0')}`;
          
          slots.push({
            id: `${hour}-${minute}`,
            startTime,
            endTime,
          });
        }
      }
      return slots;
    };
  
    // Calculer la date selon la période sélectionnée
    const getDateForPeriod = () => {
      switch (selectedPeriod) {
        case '15days':
          return '12 juin 2025';
        case '1month':
          return '26 juin 2025';
        case '3months':
          return '10 juillet 2025';
        default:
          return '12 juin 2025';
      }
    };
  
    // Calculer la date de début selon la période
    const getStartDateForPeriod = () => {
      switch (selectedPeriod) {
        case '15days':
          return '12/06/2025';
        case '1month':
          return '25/06/2025';
        case '3months':
          return '10/07/2025';
        default:
          return '12/06/2025';
      }
    };
  
    // Calculer la date formatée selon la période
    const getFormattedDateForPeriod = () => {
      switch (selectedPeriod) {
        case '15days':
          return '12/06/2025';
        case '1month':
          return '26/06/2025';
        case '3months':
          return '10/07/2025';
        default:
          return '12/06/2025';
      }
    };
  
    const timeSlots = generateTimeSlots();
  
    const handleSlotToggle = (slotId: string) => {
      const newSelectedSlots = new Set(selectedSlots);
      if (newSelectedSlots.has(slotId)) {
        newSelectedSlots.delete(slotId);
      } else {
        newSelectedSlots.add(slotId);
      }
      setSelectedSlots(newSelectedSlots);
    };
  
    const isValidateEnabled = selectedSlots.size > 0;
  
    return (
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-4">
          <div className="p-4 bg-gray-50">
            <div className="space-y-4">
              <div>
                <Text size="sm" fw={500} mb="xs">À partir de</Text>
                <Select
                  value={getStartDateForPeriod()}
                  onChange={(value) => setStartDate(value || '')}
                  data={[
                    { value: '12/06/2025', label: '12/06/2025' },
                    { value: '25/06/2025', label: '25/06/2025' },
                    { value: '10/07/2025', label: '10/07/2025' },
                    { value: '10/09/2025', label: '10/09/2025' },
                  ]}
                />
              </div>
  
              <div>
                <Text size="sm" fw={500} mb="xs">Durée (min)</Text>
                <NumberInput
                  value={duration}
                  onChange={(value) => setDuration(Number(value))}
                  min={15}
                  max={120}
                  step={15}
                />
              </div>
  
              <div>
                <Text size="sm" fw={500} mb="xs">Nbre des jours</Text>
                <NumberInput
                  value={numberOfDays}
                  onChange={(value) => setNumberOfDays(Number(value))}
                  min={1}
                  max={30}
                />
              </div>
            </div>
          </div>
        </div>
  
        <div className="col-span-8">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Text size="lg" fw={600}>{getDateForPeriod()}</Text>
              <Text size="sm" color="dimmed">24</Text>
            </div>
  
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {timeSlots.map((slot) => (
                <div key={slot.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    <Text size="sm">le</Text>
                    <Text size="sm" color="blue" fw={500}>
                      {getFormattedDateForPeriod()}
                    </Text>
                    <Text size="sm">de</Text>
                    <Text size="sm" c="red" fw={500}>
                      {slot.startTime}
                    </Text>
                    <Text size="sm">à</Text>
                    <Text size="sm" c="green" fw={500}>
                      {slot.endTime}
                    </Text>
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedSlots.has(slot.id)}
                    onChange={() => handleSlotToggle(slot.id)}
                    className="form-checkbox h-4 w-4 text-blue-600"
                  />
                </div>
              ))}
            </div>
  
            <div className="flex justify-between items-center mt-6 pt-4 border-t">
              <div className="flex space-x-4">
                <Button
                  variant={selectedPeriod === '15days' ? 'filled' : 'outline'}
                  onClick={() => setSelectedPeriod('15days')}
                  size="sm"
                >
                  15 jours
                </Button>
                <Button
                  variant={selectedPeriod === '1month' ? 'filled' : 'outline'}
                  onClick={() => setSelectedPeriod('1month')}
                  size="sm"
                >
                  1 Mois
                </Button>
                <Button
                  variant={selectedPeriod === '3months' ? 'filled' : 'outline'}
                  onClick={() => setSelectedPeriod('3months')}
                  size="sm"
                >
                  3 Mois
                </Button>
              </div>
  
              <div className="flex space-x-2">
                <Button
                  color={isValidateEnabled ? 'blue' : 'gray'}
                  disabled={!isValidateEnabled}
                  onClick={() => {
                    // Logique de validation ici
                    onClose();
                  }}
                >
                  Valider
                </Button>
                <Button
                  variant="outline"
                  color="red"
                  onClick={onClose}
                >
                  Annuler
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
    // État pour gérer le mode Pause
    const [isPauseMode, setIsPauseMode] = React.useState(false);
    const [pauseModalOpened, setPauseModalOpened] = React.useState(false);
  return (
    <>
      <div className="w-full">
           <div className="flex items-center rounded-sm bg-[var(--content-background)] p-1 pt-0">
          <div style={{width: "460px"}}>
          <SegmentedControl
            color="#3799CE"
            // className="w-[50%]"
            value={section}
            onChange={handleSectionChange}
            transitionTimingFunction="ease"
            fullWidth
            data={[
              {
                value: "jour",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendar style={{ width: rem(16), height: rem(16) }} />
                    <span>Jour</span>
                  </Center>
                ),
              },
              {
                value: "semaine",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendarWeek
                      style={{ width: rem(16), height: rem(16) }}
                    />
                    <span>Semaine</span>
                  </Center>
                ),
              },
              {
                value: "mois",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendarMonth
                      style={{ width: rem(16), height: rem(16) }}
                    />
                    <span>Mois</span>
                  </Center>
                ),
              },
             
              {
                value: "m",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendarMonth
                      style={{ width: rem(16), height: rem(16) }}
                    />
                    <span>Ordre du jour</span>
                  </Center>
                ),
              },
            ]}
          />
          </div>
       
          <div className="ml-auto flex items-center gap-2">
            {/* Refresh button */}
            <button
              onClick={() => refreshEvents(false)}
              className="ring-offset-background focus-visible:ring-ring bg-background hover:text-accent-foreground border-base-300 hover:bg-accent inline-flex h-8 items-center justify-center gap-1 whitespace-nowrap rounded-md border px-3 text-sm font-medium uppercase tracking-wider transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
              title="Actualiser les événements"
            >
              <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                Actualiser
              </span>
            </button>

            {/* Event count indicator */}
            {eventFilter !== 'all' && (
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {filteredEvents.length} événement{filteredEvents.length !== 1 ? 's' : ''}
              </span>
            )}

            {/* Debug info */}
            <span className="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded">
              {events.length} total | {filteredEvents.length} affichés
            </span>

            <Menu
              width={200}
              shadow="md"
              transitionProps={{ transition: "rotate-right", duration: 150 }}
            >
              <Menu.Target>
                <button
                  className={`ring-offset-background focus-visible:ring-ring bg-background hover:text-accent-foreground border-base-300 hover:bg-accent inline-flex h-8 items-center justify-center gap-1 whitespace-nowrap rounded-md border px-3 text-sm font-medium uppercase tracking-wider transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    eventFilter !== 'all' ? 'bg-blue-50 border-blue-300 text-blue-700' : ''
                  }`}
                  tabIndex={0}
                  role="button"
                >
                  <Icon path={mdiFilterVariant} size={1} className="h-3.5 w-3.5" />
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                    {getFilterLabel()}
                  </span>
                </button>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Item
                  leftSection={
                    <Icon path={mdiFilterVariant} size={0.6} />
                  }
                  onClick={() => handleFilterChange('all')}
                  style={{
                    backgroundColor: eventFilter === 'all' ? '#e3f2fd' : 'transparent',
                    fontWeight: eventFilter === 'all' ? 'bold' : 'normal'
                  }}
                >
                  Tous les rendez-vous
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    <Icon path={mdiFilterVariant} size={0.6} />
                  }
                  onClick={() => handleFilterChange('last3days')}
                  style={{
                    backgroundColor: eventFilter === 'last3days' ? '#e3f2fd' : 'transparent',
                    fontWeight: eventFilter === 'last3days' ? 'bold' : 'normal'
                  }}
                >
                  Les trois derniers jours
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    <Icon path={mdiFilterVariant} size={0.6} />
                  }
                  onClick={() => handleFilterChange('lastweek')}
                  style={{
                    backgroundColor: eventFilter === 'lastweek' ? '#e3f2fd' : 'transparent',
                    fontWeight: eventFilter === 'lastweek' ? 'bold' : 'normal'
                  }}
                >
                  La semaine dernière
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    <Icon path={mdiFilterVariant} size={0.6} />
                  }
                  onClick={() => handleFilterChange('archived')}
                  style={{
                    backgroundColor: eventFilter === 'archived' ? '#e3f2fd' : 'transparent',
                    fontWeight: eventFilter === 'archived' ? 'bold' : 'normal'
                  }}
                >
                  Archivés (+ 30 jours)
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>

        <div className="ml-auto flex items-center gap-2 pb-1">
            <SearchForm />
          </div>
            <Button
             leftSection={<Icon path={mdiPlusCircle} size={1}  className="h-3.5 w-3.5" />}
              className="ButtonHover"
              onClick={onNewEvent}
            >
              Ajouter des patients
            </Button>

            <Button
              variant="light"
              leftSection={<IconFileText size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Open care-sheet for the first available patient or show patient selector
                const firstEvent = events.find(e => e.patient_name);
                if (firstEvent && firstEvent.id) {
                  handleOpenCareSheet(firstEvent.id, firstEvent.patient_name);
                } else {
                  // Could open a patient selector modal here
                  notifications.show({
                    title: 'Sélectionner un patient',
                    message: 'Veuillez d\'abord sélectionner un patient pour voir son dossier de soins',
                    color: 'blue',
                  });
                }
              }}
            >
              Dossier de Soins
            </Button>

            <Button
              variant="light"
              leftSection={<IconChartBar size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Generate date range for current week
                const today = new Date();
                const startOfWeek = new Date(today);
                startOfWeek.setDate(today.getDate() - today.getDay());
                const endOfWeek = new Date(today);
                endOfWeek.setDate(today.getDate() + (6 - today.getDay()));

                const dateRange = {
                  start: startOfWeek.toISOString().split('T')[0],
                  end: endOfWeek.toISOString().split('T')[0],
                };

                handleOpenGenericStates(dateRange);
              }}
            >
              États & Rapports
            </Button>

            <Button
              variant="light"
              leftSection={<IconReportMedical size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Open medical-report for the first available patient or show patient selector
                const firstEvent = events.find(e => e.patient_name);
                if (firstEvent && firstEvent.id) {
                  const patientId = firstEvent.id;
                  const patientName = firstEvent.patient_name;

                  // Generate date range around current date
                  const today = new Date();
                  const startDate = new Date(today);
                  startDate.setMonth(today.getMonth() - 1); // 1 month back
                  const endDate = new Date(today);
                  endDate.setMonth(today.getMonth() + 1); // 1 month forward

                  const dateRange = {
                    start: startDate.toISOString().split('T')[0],
                    end: endDate.toISOString().split('T')[0],
                  };

                  handleOpenMedicalReport(patientId, patientName, dateRange);
                } else {
                  // Open without specific patient
                  handleOpenMedicalReport();
                }
              }}
            >
              Rapports Médicaux
            </Button>

            <Button
              variant="light"
              leftSection={<IconCash size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Open payment for the first available patient or show patient selector
                const firstEvent = events.find(e => e.patient_name);
                if (firstEvent && firstEvent.id) {
                  const patientId = firstEvent.id;
                  const patientName = firstEvent.patient_name;

                  // Generate date range around current date
                  const today = new Date();
                  const startDate = new Date(today);
                  startDate.setMonth(today.getMonth() - 1); // 1 month back
                  const endDate = new Date(today);
                  endDate.setMonth(today.getMonth() + 1); // 1 month forward

                  const dateRange = {
                    start: startDate.toISOString().split('T')[0],
                    end: endDate.toISOString().split('T')[0],
                  };

                  handleOpenPayment(patientId, patientName, dateRange);
                } else {
                  // Open without specific patient
                  handleOpenPayment();
                }
              }}
            >
              Paiements
            </Button>

            <Button
              variant="light"
              leftSection={<IconPill size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Generate date range for current month
                const today = new Date();
                const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

                const dateRange = {
                  start: startOfMonth.toISOString().split('T')[0],
                  end: endOfMonth.toISOString().split('T')[0],
                };

                handleOpenPharmacy(dateRange);
              }}
            >
              Pharmacie
            </Button>

            <Button
              variant="light"
              leftSection={<IconPrescription size={16} />}
              className="ButtonHover"
              onClick={() => {
                // Open prescriptions for the first available patient or show patient selector
                const firstEvent = events.find(e => e.patient_name);
                if (firstEvent && firstEvent.id) {
                  const patientId = firstEvent.id;
                  const patientName = firstEvent.patient_name;

                  // Generate date range around current date
                  const today = new Date();
                  const startDate = new Date(today);
                  startDate.setMonth(today.getMonth() - 3); // 3 months back
                  const endDate = new Date(today);
                  endDate.setMonth(today.getMonth() + 3); // 3 months forward

                  const dateRange = {
                    start: startDate.toISOString().split('T')[0],
                    end: endDate.toISOString().split('T')[0],
                  };

                  handleOpenPrescriptions(patientId, patientName, dateRange);
                } else {
                  // Open without specific patient
                  handleOpenPrescriptions();
                }
              }}
            >
              Prescriptions
            </Button>

            <Button
              variant="light"
              leftSection={<IconSettings size={16} />}
              className="ButtonHover"
              onClick={handleOpenParameters}
            >
              Paramètres
            </Button>
          </div>
        </div>
   
        {/* Navigation Header - Hidden for day view since it has its own navigation */}
        {section !== 'jour' && section !=='semaine' && section !=='mois' && section !=='m' && (
          <div className="flex items-center justify-between mb-4 p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleNavigate('prev')}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Icon path={mdiChevronLeft} size={1} />
              </button>
              <h2 className="text-lg font-semibold min-w-fit">{getCurrentDateTitle()}</h2>
              <button
                onClick={() => handleNavigate('next')}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Icon path={mdiChevronRight} size={1} />
              </button>
              <button
                 onClick={() => handleNavigate('today')}
                className="px-3 py-2 bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-lg transition-colors"
              >
                Aujourd&apos;hui 
              </button>
            </div>
          </div>
        )}


        {/* Waiting List Debug Display - Remove this in production */}
        <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <Text size="sm" fw={600} mb="xs">
            🔍 DEBUG - Liste d&apos;attente ({waitingList.length} patients)
          </Text>
          {waitingList.length === 0 ? (
            <div className="text-xs text-gray-500">Aucun patient en liste d&apos;attente</div>
          ) : (
            waitingList.map((patient, index) => (
              <div key={patient.id || index} className="text-xs text-gray-600 mb-1">
                {patient.first_name} {patient.last_name} - {patient.typeConsultation || 'Consultation'}
                <span className="text-gray-400 ml-2">(ID: {patient.id})</span>
              </div>
            ))
          )}
        </div>

        <div className="overflow-hidden ">
          {renderCalendarView()}
        </div>
  

        {/* Comprehensive Event Creation Modal */}
        {showEventModal && (
          <Modal.Root 
           opened={showEventModal}
      onClose={handleModalClose}
          size="70%"
    >
        <Modal.Overlay />
             <Modal.Content className="overflow-y-hidden">
                <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                         <Modal.Title>
                           <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                             <svg
                               xmlns="http://www.w3.org/2000/svg"
                               width="1em"
                               height="1em"
                               viewBox="0 0 24 24"
                             >
                               <g
                                 fill="none"
                                 stroke="currentColor"
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                               >
                                 <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                                 <circle cx={16} cy={16} r={6}></circle>
                               </g>
                             </svg>
                             {isTimeSlotClick ? "Ajouter un rendez-vous" : "Modifier le rendez-vous"}
                           </Text>
                           <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
                             {isTimeSlotClick
                               ? "Remplissez les détails ci-dessous pour ajouter un nouvel événement"
                               : "Modifiez les détails ci-dessous pour mettre à jour le rendez-vous"
                             }
                           </p>
                         </Modal.Title>
                         <Group justify="flex-end">
                          {/* Care Sheet Access Button */}
                          {formData.email && (
                            <ActionIcon
                              variant="light"
                              color="white"
                              size="sm"
                              onClick={() => {
                                // Use email as patient ID or find the actual patient ID
                                const patientId = formData.id || formData.email;
                                const patientName = `${formData.first_name || ''} ${formData.last_name || ''}`.trim();
                                handleOpenCareSheet(patientId, patientName || formData.email);
                              }}
                              title="Voir le dossier de soins"
                            >
                              <IconFileText size={16} />
                            </ActionIcon>
                          )}

                          {/* Generic States Access Button */}
                          <ActionIcon
                            variant="light"
                            color="white"
                            size="sm"
                            onClick={() => {
                              // Generate date range around the appointment date
                              const appointmentDate = new Date(formData.date || new Date());
                              const startDate = new Date(appointmentDate);
                              startDate.setDate(appointmentDate.getDate() - 7); // 1 week before
                              const endDate = new Date(appointmentDate);
                              endDate.setDate(appointmentDate.getDate() + 7); // 1 week after

                              const dateRange = {
                                start: startDate.toISOString().split('T')[0],
                                end: endDate.toISOString().split('T')[0],
                              };

                              handleOpenGenericStates(dateRange);
                            }}
                            title="Voir les états et rapports"
                          >
                            <IconChartBar size={16} />
                          </ActionIcon>

                          {/* Medical Report Access Button */}
                          {formData.email && (
                            <ActionIcon
                              variant="light"
                              color="white"
                              size="sm"
                              onClick={() => {
                                // Use email as patient ID or find the actual patient ID
                                const patientId = formData.id || formData.email;
                                const patientName = `${formData.first_name || ''} ${formData.last_name || ''}`.trim();

                                // Generate date range around the appointment date
                                const appointmentDate = new Date(formData.date || new Date());
                                const startDate = new Date(appointmentDate);
                                startDate.setMonth(appointmentDate.getMonth() - 1); // 1 month before
                                const endDate = new Date(appointmentDate);
                                endDate.setMonth(appointmentDate.getMonth() + 1); // 1 month after

                                const dateRange = {
                                  start: startDate.toISOString().split('T')[0],
                                  end: endDate.toISOString().split('T')[0],
                                };

                                handleOpenMedicalReport(patientId, patientName || formData.email, dateRange);
                              }}
                              title="Voir les rapports médicaux"
                            >
                              <IconReportMedical size={16} />
                            </ActionIcon>
                          )}

                          {/* Payment Access Button */}
                          {formData.email && (
                            <ActionIcon
                              variant="light"
                              color="white"
                              size="sm"
                              onClick={() => {
                                // Use email as patient ID or find the actual patient ID
                                const patientId = formData.id || formData.email;
                                const patientName = `${formData.first_name || ''} ${formData.last_name || ''}`.trim();

                                // Generate date range around the appointment date
                                const appointmentDate = new Date(formData.date || new Date());
                                const startDate = new Date(appointmentDate);
                                startDate.setMonth(appointmentDate.getMonth() - 1); // 1 month before
                                const endDate = new Date(appointmentDate);
                                endDate.setMonth(appointmentDate.getMonth() + 1); // 1 month after

                                const dateRange = {
                                  start: startDate.toISOString().split('T')[0],
                                  end: endDate.toISOString().split('T')[0],
                                };

                                handleOpenPayment(patientId, patientName || formData.email, dateRange);
                              }}
                              title="Voir les paiements"
                            >
                              <IconCash size={16} />
                            </ActionIcon>
                          )}

                          {/* Pharmacy Access Button */}
                          <ActionIcon
                            variant="light"
                            color="white"
                            size="sm"
                            onClick={() => {
                              // Generate date range around the appointment date
                              const appointmentDate = new Date(formData.date || new Date());
                              const startOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth(), 1);
                              const endOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth() + 1, 0);

                              const dateRange = {
                                start: startOfMonth.toISOString().split('T')[0],
                                end: endOfMonth.toISOString().split('T')[0],
                              };

                              handleOpenPharmacy(dateRange);
                            }}
                            title="Voir la pharmacie"
                          >
                            <IconPill size={16} />
                          </ActionIcon>

                          {/* Prescriptions Access Button */}
                          <ActionIcon
                            variant="light"
                            color="white"
                            size="sm"
                            onClick={() => {
                              // Use email as patient ID or find the actual patient ID
                              const patientId = formData.id || formData.email;
                              const patientName = `${formData.first_name || ''} ${formData.last_name || ''}`.trim();

                              // Generate date range around the appointment date
                              const appointmentDate = new Date(formData.date || new Date());
                              const startDate = new Date(appointmentDate);
                              startDate.setMonth(appointmentDate.getMonth() - 3); // 3 months before
                              const endDate = new Date(appointmentDate);
                              endDate.setMonth(appointmentDate.getMonth() + 3); // 3 months after

                              const dateRange = {
                                start: startDate.toISOString().split('T')[0],
                                end: endDate.toISOString().split('T')[0],
                              };

                              handleOpenPrescriptions(patientId, patientName || formData.email, dateRange);
                            }}
                            title="Voir les prescriptions"
                          >
                            <IconPrescription size={16} />
                          </ActionIcon>

                          {/* Parameters Access Button */}
                          <ActionIcon
                            variant="light"
                            color="white"
                            size="sm"
                            onClick={handleOpenParameters}
                            title="Configuration système"
                          >
                            <IconSettings size={16} />
                          </ActionIcon>

                          <Switch
                             checked={isPauseMode}
                             onChange={(event) => {
                               setIsPauseMode(event.currentTarget.checked);
                               if (event.currentTarget.checked) {
                                 setPauseModalOpened(true);
                               }
                             }}
                             color="teal"
                             size="xs"
                           />
                           <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
                             Pause
                           </p>
                           <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                         </Group>
                       </Modal.Header>
                        <Modal.Body style={{ padding: '0px' }}>
                        <div className="py-2 pl-4 h-[600px] ">
                   <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                 <div className="pr-4">
                  <form onSubmit={(e) => { e.preventDefault(); handleSaveEvent(); }}>
                      <div className="grid gap-3 py-2 pr-4">
                          {/* Titre, Nom, Prénom */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        value={formData.patient_title || ''}
                        onChange={(value) => handleFormChange('patient_title', value || '')}
                        placeholder="Titre"
                         data={[
                               { value: "m", label: "M." },
                               { value: "mme", label: "Mme" },
                               { value: "mlle", label: "Mlle" },
                               { value: "dr", label: "Dr." },
                               { value: "pr", label: "Pr." }
                             ]}
                        className="w-full"
                          leftSection={<span className="">
                        <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg></span>}
                        />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                            placeholder="Ajouter des titres"
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newTitle.trim()) {
                                    const newTitleOption = { value: newTitle, label: newTitle };
                                    setTitleOptions([...titleOptions, newTitleOption]);
                                    setEventTitle(newTitle);
                                    setNewTitle("");
                                    notifications.show({
                                      title: 'Titre ajouté',
                                      message: `"${newTitle}" a été ajouté à la liste des titres`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newTitle.trim()}
                              >
                                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newTitle.trim()) {
                                const newTitleOption = { value: newTitle, label: newTitle };
                                setTitleOptions([...titleOptions, newTitleOption]);
                                setEventTitle(newTitle);
                                setNewTitle("");
                                notifications.show({
                                  title: 'Titre ajouté',
                                  message: `"${newTitle}" a été ajouté à la liste des titres`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>

                      {/* Patient Selection Autocomplete */}
                      {allPatients && allPatients.length > 0 && (
                        <Autocomplete
                          label="Sélectionner un patient existant"
                          placeholder={isLoadingPatients ? "Chargement des patients..." : "Rechercher un patient..."}
                          data={allPatients.map((patient: EventFormData) =>
                            `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}`
                          )}
                          onOptionSubmit={(value) => {
                            // Find the patient based on the selected value
                            const selectedPatient = allPatients.find((patient: EventFormData) =>
                              `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}` === value
                            );
                            if (selectedPatient && onPatientSelect) {
                              onPatientSelect(selectedPatient);
                            }
                          }}
                          disabled={isLoadingPatients}
                          leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                          className="mb-2"
                          limit={10}
                        />
                      )}

                      <TextInput
                        id="event-nom"
                        placeholder="Nom *"
                        type="text"
                         value={formData.last_name}
                          onChange={(e) => handleFormChange('last_name', e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                      />
                      <TextInput
                        id="event-prenom"
                        placeholder="Prénom *"
                        type="text"
                        value={formData.first_name}
                             onChange={(e) => handleFormChange('first_name', e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                      />
                      <div
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          openListDesPatient();
                        }}
                        style={{ cursor: 'pointer', display: 'inline-block' }}
                      >
                        <Avatar
                          color="#4BA3D3"
                          radius="sm"
                          h={36}
                          style={{ pointerEvents: 'none' }}
                        >
                        <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="20" width="20" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 5h2v3h10V5h2v5h2V5c0-1.1-.9-2-2-2h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h5v-2H5V5zm7-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"></path><path d="M20.3 18.9c.4-.7.7-1.5.7-2.4 0-2.5-2-4.5-4.5-4.5S12 14 12 16.5s2 4.5 4.5 4.5c.9 0 1.7-.3 2.4-.7l2.7 2.7 1.4-1.4-2.7-2.7zm-3.8.1c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5z"></path></svg>
                      </Avatar>
                            {/*   Show Patient List */}
                                   <Modal.Root
                                     opened={ListDesPatientOpened}
                                     onClose={() => {
                                       closeListDesPatient();
                                     }}
                                     size="lg"
                                   >
                                     <Modal.Overlay />
                                     <Modal.Content>
                                       <Modal.Header>
                                         <Modal.Title>Détails du patient</Modal.Title>
                                         <Modal.CloseButton />
                                       </Modal.Header>
                                       <Modal.Body>
                                         <div className="p-4">
                                           <Text size="lg" fw={600} mb="md">Liste des patients</Text>
                                           <Text c="dimmed">Cette fonctionnalité sera bientôt disponible.</Text>
                                           <Group justify="flex-end" mt="xl">
                                             <Button
                                               onClick={(e) => {
                                                 e.preventDefault();
                                                 e.stopPropagation();
                                                 closeListDesPatient();
                                               }}
                                               variant="filled"
                                             >
                                               Fermer
                                             </Button>
                                           </Group>
                                         </div>
                                       </Modal.Body>
                                     </Modal.Content>
                                   </Modal.Root>
                      </div>
                    </div>

                  
  <div className="flex gap-4 mb-2">
                       <TextInput
                        type="date"
                        placeholder="Date de Naissance..."
                        id="event-dateDeNaissance"
                         value={formData.birth_date}
                          onChange={(e) => handleFormChange('birth_date', e.target.value)}
                        required
                        className="input input-bordered  w-full"
                      />
                      <Avatar
                    color="#4BA3D3"
                    radius="sm"
                    h={36}
                    style={{
                      opacity: 0.5,
                      cursor: 'not-allowed',
                      pointerEvents: 'none',
                    }}
                  >
                    <Icon path={mdiPowerSocketUs} size={1} />
                  </Avatar>

                      <TextInput
                        type="text"
                        id="event-age"
                        value={formData.age ? formData.age.toString() : ''}
                       
                        placeholder={
                          formData.age !== null
                            ? formData.age.toString()
                            : "Veuillez entrer votre date de naissance"
                        }
                        readOnly
                        className="input input-bordered  w-full "
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 16 1.25 L 15.1875 2.4375 C 15.1875 2.4375 14.648438 3.191406 14.125 4.09375 C 13.863281 4.546875 13.617188 5.019531 13.40625 5.5 C 13.195313 5.980469 13 6.421875 13 7 C 13 8.644531 14.355469 10 16 10 C 17.644531 10 19 8.644531 19 7 C 19 6.421875 18.804688 5.980469 18.59375 5.5 C 18.382813 5.019531 18.136719 4.546875 17.875 4.09375 C 17.351563 3.191406 16.8125 2.4375 16.8125 2.4375 Z M 16 10 L 13 10 L 13 14 L 7 14 C 4.789063 14 3 15.789063 3 18 C 3 19.015625 3.375 19.949219 4 20.65625 L 4 28 L 28 28 L 28 20.65625 C 28.625 19.949219 29 19.015625 29 18 C 29 15.789063 27.210938 14 25 14 L 19 14 L 19 10 Z M 16 4.875 C 16.066406 4.984375 16.058594 4.976563 16.125 5.09375 C 16.363281 5.503906 16.617188 5.941406 16.78125 6.3125 C 16.945313 6.683594 17 7.027344 17 7 C 17 7.554688 16.554688 8 16 8 C 15.445313 8 15 7.554688 15 7 C 15 7.027344 15.054688 6.683594 15.21875 6.3125 C 15.382813 5.941406 15.636719 5.503906 15.875 5.09375 C 15.941406 4.976563 15.933594 4.984375 16 4.875 Z M 15 12 L 17 12 L 17 14 L 15 14 Z M 7 16 L 25 16 C 26.191406 16 27 16.808594 27 18 C 27 19.191406 26.191406 20 25 20 C 23.808594 20 23 19.191406 23 18 L 21 18 C 21 19.191406 20.191406 20 19 20 C 17.808594 20 17 19.191406 17 18 L 15 18 C 15 19.191406 14.191406 20 13 20 C 11.808594 20 11 19.191406 11 18 L 9 18 C 9 19.191406 8.191406 20 7 20 C 5.808594 20 5 19.191406 5 18 C 5 16.808594 5.808594 16 7 16 Z M 10 20.65625 C 10.734375 21.484375 11.804688 22 13 22 C 14.195313 22 15.265625 21.484375 16 20.65625 C 16.734375 21.484375 17.804688 22 19 22 C 20.195313 22 21.265625 21.484375 22 20.65625 C 22.734375 21.484375 23.804688 22 25 22 C 25.347656 22 25.679688 21.925781 26 21.84375 L 26 26 L 6 26 L 6 21.84375 C 6.320313 21.925781 6.652344 22 7 22 C 8.195313 22 9.265625 21.484375 10 20.65625 Z"></path></svg>}
                      />
                      <Radio.Group
                        value={formData.gender || "Homme"}
                        onChange={(value) => {
                          console.log('Gender changed to:', value);
                          handleFormChange('gender', value);
                          setValue(value); // Keep setValue in sync for any other dependencies
                        }}
                      >
                        <div className="flex items-center space-x-4 mt-2">
                          <Radio value="Homme" label="Homme" />
                          <Radio value="Femme" label="Femme" />
                          <Radio value="Enfant" label="Enfant" />
                          <Radio value="Autre" label="Autre" />
                        </div>
                      </Radio.Group>
                     
                    </div>  

 {/* État civil, CIN, Adresse */}
                    <div className="flex gap-4">
                      <Select
                        value={formData.etat_civil}
                             onChange={(value) => handleFormChange('etat_civil', value || '')}
                        placeholder="État civil"
                        data={[
                          { value: "Célibataire", label: "Célibataire" },
                          { value: "Marié(e)", label: "Marié(e)" },
                          { value: "Divorcé(e)", label: "Divorcé(e)" },
                          { value: "Veuf(ve)", label: "Veuf(ve)" },
                          { value: "Autre chose", label: "Autre chose" },
                        ]}
                        className="select w-full max-w-xs"
                      />
                      <TextInput
                        placeholder="№ CIN"
                         value={formData.cin}
                             onChange={(e) => handleFormChange('cin', e.target.value)}
                        
                       
                        disabled={formData.gender === 'Enfant'}
                        styles={{
                          input: {
                            backgroundColor: formData.gender === 'Enfant' ? '#f5f5f5' : undefined,
                            color: formData.gender === 'Enfant' ? '#999' : undefined
                          }
                        }}
                        className="input input-bordered mb-2 w-full"
                        leftSection={
                        <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 17v-10l7 10v-10"></path><path d="M15 17h5"></path><path d="M17.5 10m-2.5 0a2.5 3 0 1 0 5 0a2.5 3 0 1 0 -5 0"></path></svg>}
                      />
                      <TextInput
                        id="Adresse"
                        placeholder="Adressé par"
                        value={formData.address}
                        onChange={(e) => handleFormChange('address', e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 3 6 L 3 26 L 29 26 L 29 6 Z M 5 8 L 27 8 L 27 24 L 23.59375 24 C 23.515625 23.863281 23.550781 23.675781 23.4375 23.5625 C 23.058594 23.183594 22.523438 23 22 23 C 21.476563 23 20.941406 23.183594 20.5625 23.5625 C 20.449219 23.675781 20.484375 23.863281 20.40625 24 L 11.59375 24 C 11.515625 23.863281 11.550781 23.675781 11.4375 23.5625 C 11.058594 23.183594 10.523438 23 10 23 C 9.476563 23 8.941406 23.183594 8.5625 23.5625 C 8.449219 23.675781 8.484375 23.863281 8.40625 24 L 5 24 Z M 12 10 C 9.800781 10 8 11.800781 8 14 C 8 15.113281 8.476563 16.117188 9.21875 16.84375 C 7.886719 17.746094 7 19.285156 7 21 L 9 21 C 9 19.34375 10.34375 18 12 18 C 13.65625 18 15 19.34375 15 21 L 17 21 C 17 19.285156 16.113281 17.746094 14.78125 16.84375 C 15.523438 16.117188 16 15.113281 16 14 C 16 11.800781 14.199219 10 12 10 Z M 12 12 C 13.117188 12 14 12.882813 14 14 C 14 15.117188 13.117188 16 12 16 C 10.882813 16 10 15.117188 10 14 C 10 12.882813 10.882813 12 12 12 Z M 19 13 L 19 15 L 25 15 L 25 13 Z M 19 17 L 19 19 L 25 19 L 25 17 Z"></path></svg>}
                      />
                    </div>

   {/* Téléphone, Email */}
                    <div className="flex gap-4">
                      <InputBase
                        id="Téléphone"
                        component={IMaskInput}
                        mask="00-00-00-00-00"
                        placeholder="Téléphone"
                        value={formData.phone_number}
                        onAccept={(value) => handleFormChange('phone_number', value)}
                        unmask={true}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>}
                      />
                      <InputBase
                        id="Fixe"
                        component={IMaskInput}
                        mask="00-00-00-00-00"
                        placeholder="Téléphone fixe"
                        value={formData.landline_number}
                        onAccept={(value) => handleFormChange('landline_number', value)}
                        unmask={true}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M5 4h4l2 5-2.5 1.5a11 11 0 0 0 5 5L15 13l5 2v4a2 2 0 0 1-2 2A16 16 0 0 1 3 6a2 2 0 0 1 2-2z"></path></svg>}
                      />
                      <TextInput
                        id="Email"
                        placeholder="Email (optionnel)"
                        value={formData.email}
                        onChange={(e) => handleEmailChange(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        error={emailError}
                        styles={{
                          input: {
                            borderColor: emailError ? '#ff0000' : undefined,
                            borderWidth: emailError ? '2px' : undefined,
                            backgroundColor: emailError ? '#ffe6e6' : undefined,
                            '&:focus': {
                              borderColor: emailError ? '#ff0000' : undefined,
                              borderWidth: emailError ? '2px' : undefined,
                            }
                          }
                        }}
                        rightSection={isCheckingEmail ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        ) : emailError ? (
                          <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        ) : null}
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><g id="At"><path d="M12.09,21.925a9.846,9.846,0,0,1-3.838-.747A9.673,9.673,0,0,1,3.005,15.93,10.034,10.034,0,0,1,2.244,12a10.425,10.425,0,0,1,.695-3.8,9.606,9.606,0,0,1,2-3.169A9.269,9.269,0,0,1,8.1,2.862a10.605,10.605,0,0,1,4.175-.787,10.516,10.516,0,0,1,4.334.827A8.437,8.437,0,0,1,19.64,5.119a8.622,8.622,0,0,1,1.707,3.1,9.263,9.263,0,0,1,.377,3.487,5.809,5.809,0,0,1-1.3,3.6A3.6,3.6,0,0,1,17.7,16.473a3.628,3.628,0,0,1-2.162-.609,2.82,2.82,0,0,1-1.119-1.694l.5.106a2.582,2.582,0,0,1-1.3,1.3A4.37,4.37,0,0,1,11.746,16,3.681,3.681,0,0,1,9.88,15.54a3.2,3.2,0,0,1-1.237-1.271A3.843,3.843,0,0,1,8.2,12.4a3.88,3.88,0,0,1,.456-1.926A3.191,3.191,0,0,1,9.919,9.214a3.792,3.792,0,0,1,1.853-.443,4.716,4.716,0,0,1,1.767.364,2.622,2.622,0,0,1,1.383,1.3l-.5.5V9.461a.4.4,0,0,1,.4-.4h.232a.4.4,0,0,1,.4.4v3.518a2.723,2.723,0,0,0,.529,1.674,2.173,2.173,0,0,0,1.853.708,2.281,2.281,0,0,0,1.323-.41,2.938,2.938,0,0,0,.967-1.178,4.947,4.947,0,0,0,.437-1.852,9.439,9.439,0,0,0-.417-3.574A7.285,7.285,0,0,0,18.5,5.588a7.424,7.424,0,0,0-2.679-1.78,9.605,9.605,0,0,0-3.547-.622,9.041,9.041,0,0,0-3.758.741,8.252,8.252,0,0,0-2.773,2,8.8,8.8,0,0,0-1.72,2.838,9.27,9.27,0,0,0-.589,3.262,8.568,8.568,0,0,0,.682,3.408A8.951,8.951,0,0,0,6,18.24a8.707,8.707,0,0,0,2.785,1.892,8.515,8.515,0,0,0,3.389.682,9.851,9.851,0,0,0,2.679-.378,8.451,8.451,0,0,0,2-.831.4.4,0,0,1,.553.158l.1.192a.4.4,0,0,1-.141.526,9.832,9.832,0,0,1-2.391,1.04A10.5,10.5,0,0,1,12.09,21.925ZM11.8,14.859a2.469,2.469,0,0,0,1.786-.649,2.427,2.427,0,0,0,.675-1.839,2.414,2.414,0,0,0-.7-1.886A2.532,2.532,0,0,0,11.8,9.856a2.482,2.482,0,0,0-1.839.649,2.523,2.523,0,0,0-.65,1.866,2.4,2.4,0,0,0,.682,1.865A2.574,2.574,0,0,0,11.8,14.859Z"></path></g></svg>}
                      />
                    </div>

   {/* Docteur, Sécurité sociale */}
                    <div className="flex gap-4">
                      <Select
                        value={formData.doctor_assigned}
                             onChange={(value) => handleFormChange('doctor_assigned', value || '')}
                        placeholder="Docteur"
                        data={doctors.flatMap(doctor => {
                          // Only include doctors with valid names
                          if (!doctor.name || doctor.name.trim() === '') {
                            return [];
                          }

                          const doctorOption = {
                            value: doctor.id,
                            label: `Dr. ${doctor.name.trim()}`
                          };

                          // Add assistants for this doctor
                          const assistantOptions = (doctor.assistants || [])
                            .filter(assistant => assistant.name && assistant.name.trim() !== '')
                            .map(assistant => ({
                              value: assistant.id,
                              label: `${assistant.name.trim()} (Assistant de Dr. ${doctor.name.trim()})`
                            }));

                          return [doctorOption, ...assistantOptions];
                        })}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                      <Select
                       value={formData.social_security|| 'Aucune'}
                             onChange={(value) => handleFormChange('social_security', value || 'Aucune')}
                        
                        placeholder="Sécurité sociale"
                        data={[
                          { value: "Aucune", label: "Aucune" },
                          { value: "CNSS", label: "CNSS" },
                          { value: "AMO", label: "AMO" },
                        ]}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M4 5c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2zm4.78 3.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 2 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zM18 7c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2.78 1.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 14 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zm-2.77 4.43-1.41 1.41L18.17 16H5.83l1.58-1.59L6 13l-4 4 3.99 3.99 1.41-1.41L5.83 18h12.34l-1.58 1.58L18 20.99 22 17l-3.99-3.99z"></path></svg>}
                      />
                    </div>

 {/* Type de consultation */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        label="Type de consultation"
                        placeholder="Rechercher ou saisir..."
                         data={[
                               { value: "Consultation générale", label: "Consultation générale" },
                               { value: "Consultation spécialisée", label: "Consultation spécialisée" },
                               { value: "Contrôle", label: "Contrôle" },
                               { value: "Urgence", label: "Urgence" },
                               { value: "Téléconsultation", label: "Téléconsultation" },
                               { value: "Vaccination", label: "Vaccination" },
                               { value: "Bilan de santé", label: "Bilan de santé" }
                             ]}
                        value={formData.typeConsultation}
                        onChange={(value) => {
                          handleFormChange('typeConsultation', value || '');
                          setPatientTypeConsultation(value ?? "");
                        }}
                        searchable
                        searchValue={searchValue}
                        onSearchChange={setSearchValue}
                        clearable
                        maxDropdownHeight={280}
                        rightSectionWidth={70}
                        required
                        rightSection={
                          <span className="bg-[#4CAF50] text-white px-2 py-1 rounded text-xs">{formData.duration}</span>
                        }
                        allowDeselect
                        className="w-full"
                      />
                      <Menu width={260} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <div className="flex">
                            <TextInput
                              leftSectionPointerEvents="none"
                              leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                              placeholder="Ajouter des Consultation"
                              value={newConsultationType}
                              onChange={(e) => setNewConsultationType(e.target.value)}
                              rightSection={
                                <ActionIcon
                                  size="sm"
                                  onClick={() => {
                                    if (newConsultationType.trim()) {
                                      const newType = {
                                        value: newConsultationType,
                                        label: newConsultationType,
                                        duration: dureeDeLexamen || "15 min"
                                      };
                                      setConsultationTypes([...consultationTypes, newType]);
                                      setPatientTypeConsultation(newConsultationType);
                                      setNewConsultationType("");
                                      notifications.show({
                                        title: 'Type de consultation ajouté',
                                        message: `"${newConsultationType}" a été ajouté`,
                                        color: 'green',
                                        autoClose: 2000
                                      });
                                    }
                                  }}
                                  disabled={!newConsultationType.trim()}
                                >
                                  <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                                </ActionIcon>
                              }
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && newConsultationType.trim()) {
                                  const newType = {
                                    value: newConsultationType,
                                    label: newConsultationType,
                                    duration: dureeDeLexamen || "15 min"
                                  };
                                  setConsultationTypes([...consultationTypes, newType]);
                                  setPatientTypeConsultation(newConsultationType);
                                  setNewConsultationType("");
                                  notifications.show({
                                    title: 'Type de consultation ajouté',
                                    message: `"${newConsultationType}" a été ajouté`,
                                    color: 'green',
                                    autoClose: 2000
                                  });
                                }
                              }}
                            />
                            <Avatar
                              color={newConsultationColor}
                              radius="sm"
                              ml={4}
                              h={36}
                              onClick={openedColorPicker}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
                                style={{width: "26px", height:"26px"}}
                              >
                                <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path>
                              </svg>
                            </Avatar>
                          </div>
                        </Menu.Dropdown>
                      </Menu>
                      <Modal opened={ColorPickeropened} onClose={closeColorPicker} size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
                        <ColorPicker
                          defaultValue={newConsultationColor}
                          value={newConsultationColor}
                          onChange={setNewConsultationColor}
                          onChangeEnd={setChangeEndValue}
                          format="hex"
                          swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']}
                        />
                        <Group justify="center" mt={8}>
                          <Button
                            variant="filled"
                            w={"100%"}
                            color={`${newConsultationColor}`}
                            leftSection={<IconColorPicker stroke={1} size={18} />}
                            onClick={() => {
                              setNewConsultationColor(changeEndValue);
                              closeColorPicker();
                            }}
                          >
                            Sélectionner cette couleur
                          </Button>
                        </Group>
                      </Modal>

                      <Select
                        label="Durée"
                        value={formData.duration + " min"}
                        onChange={(value) => {
                          const minutes = parseInt(value?.replace(' min', '') || "15");
                          handleFormChange('duration', minutes);
                          setDureeDeLexamen(value ?? "");
                        }}
                        placeholder="15 min"
                        data={["10 min", "15 min", "20 min","25 min","30 min", "35 min" ,"40 min","45 min"]}
                        className="select w-full max-w-xs"
                      />
                      <Select
                        label="Agenda"
                        value={formData.agenda}
                               onChange={(value) => handleFormChange('agenda', value || '')}
                        placeholder="Ajouter des Agenda"
                         data={[
                               { value: "Cabinet", label: "Cabinet",  },
                            { value: "Center", label: "Center",  },
                            { value: "Kaders", label: "Kaders",  },
                               ]}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                            placeholder="Ajouter des Agenda"
                            value={newAgendaType}
                            onChange={(e) => setNewAgendaType(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newAgendaType.trim()) {
                                    const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                    setAgendaTypes([...agendaTypes, newAgendaOption]);
                                    setEventAganda(newAgendaType);
                                    setNewAgendaType("");
                                    notifications.show({
                                      title: 'Agenda ajouté',
                                      message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newAgendaType.trim()}
                              >
                                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newAgendaType.trim()) {
                                const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                setAgendaTypes([...agendaTypes, newAgendaOption]);
                                setEventAganda(newAgendaType);
                                setNewAgendaType("");
                                notifications.show({
                                  title: 'Agenda ajouté',
                                  message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>
                    </div>


{/* Date et heure du RDV */}
                    {!isWaitingList && !formData.addToWaitingList && (
                      <div className="mx-auto flex gap-4 mb-2">
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>Date du RDV</Text>
                        <TextInput
                          id="event-date"
                          type="date"
                          value={formData.date}
                               onChange={(e) => handleFormChange('date', e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>De*</Text>
                        <TextInput
                          id="event-time"
                          type="time"
                           value={formData.start}
                               onChange={(e) => handleFormChange('start', e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>à*</Text>
                        <TextInput
                          id="event-time-end"
                          type="time"
                          value={formData.end}
                          readOnly
                          className="input input-bordered mb-2 w-40"
                          placeholder="Heure de fin"
                        />
                        <Avatar color="#4BA3D3" radius="sm" h={36}>
                          <IconTextPlus stroke={2} size={30} className="text-[#3799CE] cursor-pointer"
                            onClick={openListRendezVous}
                          />
                        </Avatar>

                        <Modal
                          opened={ListRendezVousOpened}
                          onClose={closeListRendezVous}
                          size="xl"
                          centered
                          withCloseButton={false}
                        >
                          <RendezVousSelector onClose={closeListRendezVous} />
                        </Modal>
                      </div>
                    )}

  {/* Commentaires */}
                    <div className="flex gap-4 mb-2 -mt-2 pr-4">
                      <Textarea
                        id="event-Commentaire"
                      value={formData.comment}
                               onChange={(e) => handleFormChange('comment', e.target.value)}
                        placeholder="Commentaire ..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                      <Textarea
                        id="event-Notes"
                         value={formData.notes}
                               onChange={(e) => handleFormChange('notes', e.target.value)}
                        placeholder="Notes ..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                      <Textarea
                        id="event-Commentairelistedattente"
                        value={formData.Commentairelistedattente}
                               onChange={(e) => handleFormChange('Commentairelistedattente', e.target.value)}
                        placeholder="Commentaire (liste d'attente)..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                    </div>

                    {/* Additional fields to match the comprehensive form */}
                    <div className="flex gap-4 mb-2">
                      <TextInput
                        placeholder="Profession"
                        value={formData.profession}
                        onChange={(e) => handleFormChange('profession', e.target.value)}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                      <TextInput
                        placeholder="Lieu de naissance"
                        value={formData.birthPlace}
                        onChange={(e) => handleFormChange('birthPlace', e.target.value)}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                    </div>

                    <div className="flex gap-4 mb-2">
                      <TextInput
                        placeholder="Nom du père"
                        value={formData.fatherName}
                        onChange={(e) => handleFormChange('fatherName', e.target.value)}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                      <TextInput
                        placeholder="Nom de la mère"
                        value={formData.motherName}
                        onChange={(e) => handleFormChange('motherName', e.target.value)}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                    </div>

                    <div className="flex gap-4 mb-2">
                      <Select
                        placeholder="Groupe sanguin"
                        value={formData.bloodGroup}
                        onChange={(value) => handleFormChange('bloodGroup', value || '')}
                        data={[
                          { value: "A+", label: "A+" },
                          { value: "A-", label: "A-" },
                          { value: "B+", label: "B+" },
                          { value: "B-", label: "B-" },
                          { value: "AB+", label: "AB+" },
                          { value: "AB-", label: "AB-" },
                          { value: "O+", label: "O+" },
                          { value: "O-", label: "O-" }
                        ]}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                      <TextInput
                        placeholder="Allergies"
                        value={formData.allergies}
                        onChange={(e) => handleFormChange('allergies', e.target.value)}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>}
                      />
                    </div>

        <Group justify="space-between">
       <div className="flex items-center space-x-1">
                          <Select
                            value={eventResourceId ? eventResourceId.toString() : ""}
                            onChange={(value) => {
                              const roomNumber = Number(value) || 1;
                              setEventResourceId(roomNumber);

                              // Update form data with the selected room
                              const roomId = `room-${roomNumber === 1 ? 'a' : 'b'}`;
                              setFormData(prev => ({
                                ...prev,
                                resourceId: roomId
                              }));

                              console.log('🏠 Room selected:', { roomNumber, roomId });
                            }}
                            name="resourceId"
                            placeholder="Room"
                            data={[
                              { value: "1", label: "Room A" },
                              { value: "2", label: "Room B" },
                            ]}
                            required
                            className="select w-[370px] "
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0V0z"></path><path d="M20 4v16H4V4h16m0-2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-3.5 8.67V9c0-1.1-.9-2-2-2h-5c-1.1 0-2 .9-2 2v1.67c-.88.35-1.5 1.2-1.5 2.2V17h1.5v-1.5h9V17H18v-4.13c0-1-.62-1.85-1.5-2.2zM15 8.5v2H9v-2h6zm-7.5 4.37c0-.48.39-.87.87-.87h7.27c.48 0 .87.39.87.87V14h-9v-1.13H7.5z"></path></svg>}
                          />
                        </div>
      
          <Group justify="space-between">

                        <div className="flex items-center space-x-1">
                          <input
                            id="visit"
                            type="radio"
                            name="eventType"
                            value="visit"
                            className="peer hidden"
                            checked={eventType === "visit"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="visit"
                            className={`${
                              eventType === "visit"
                                ? "peer-checked:text-[#34D1BF]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visite de malade
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="visitor-counter"
                            type="radio"
                            name="eventType"
                            value="visitor-counter"
                            className="peer hidden"
                            checked={eventType === "visitor-counter"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="visitor-counter"
                            className={`${
                              eventType === "visitor-counter"
                                ? "peer-checked:text-[#F17105]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visitor Counter
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="completed"
                            type="radio"
                            name="eventType"
                            value="completed"
                            className="peer hidden"
                            checked={eventType === "completed"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="completed"
                            className={`${
                              eventType === "completed"
                                ? "peer-checked:text-[#3799CE]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Completed
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="diagnosis"
                            type="radio"
                            name="eventType"
                            value="diagnosis"
                            checked={eventType === "diagnosis"}
                            className="peer hidden"
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="diagnosis"
                            className={`${
                              eventType === "diagnosis"
                                ? "peer-checked:text-[#F3124E]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Re-diagnose
                            </li>
                          </label>
                        </div>
                        </Group> 
    </Group>

           {/* Switches et boutons */}
                  
                       <Group justify="space-between" mt={'-4px'}>
        <Group gap="xs">
                        <Switch
                          color="teal"
                          size="xs"
                          label="Add to Waiting List"
                      
                          checked={formData.addToWaitingList}
                          onChange={(event) => {
                            console.log('Add to Waiting List checkbox changed:', event.currentTarget.checked);
                            console.log('Current formData before change:', formData);
                            handleFormChange('addToWaitingList', event.currentTarget.checked);
                            console.log('FormData after change should have addToWaitingList:', event.currentTarget.checked);
                          }}
                          thumbIcon={
                            formData.addToWaitingList ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />

                        {/* Show remove from calendar option when adding to waiting list */}
                        {formData.addToWaitingList && (
                          <Switch
                            color="orange"
                            size="xs"
                            label="Remove from Calendar"
                            description="Remove the event from calendar when adding to waiting list"
                            checked={formData.removeFromCalendar}
                            onChange={(event) => {
                              handleFormChange('removeFromCalendar', event.currentTarget.checked);
                            }}
                            thumbIcon={
                              formData.removeFromCalendar ? (
                                <IconCheck size={12} color="var(--mantine-color-orange-6)" stroke={3} />
                              ) : null
                            }
                          />
                        )}

                        <Switch
                          checked={formData.checkedAppelvideo}
                          onChange={handleAppelvideoChange}
                          color="teal"
                          size="xs"
                          label="Appel video"
                          thumbIcon={
                            formData.checkedAppelvideo ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={formData.checkedRappelSms}
                          onChange={handleRappelSmsChange}
                          color="teal"
                          size="xs"
                          label="Rappel Sms"
                          thumbIcon={
                            formData.checkedRappelSms ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={formData.checkedRappelEmail}
                          onChange={handleRappelEmailChange}
                          color="teal"
                          size="xs"
                          label="Rappel e-mail"
                          thumbIcon={
                            formData.checkedRappelEmail ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                      </Group>
     
       <Group >
                      <Button
                        type="submit"
                        className=" mb-2 btn-Enr"
                        onClick={(e) => {
                          e.preventDefault();
                          handleSaveEvent();
                        }}
                      >
                         {/* {isEditing ? "Enregistrer" : "Ajouter"}  */}
                          Enregistrer 
                      </Button>
                      {formData && (
                        <Button
                          color="red"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (formData && formData.id) {
                              // Remove from waiting list if it exists there
                              setWaitingList(prev => prev.filter(p => p.id !== formData.id));
                              // Remove from events if it exists there
                              setEvents(prev => prev.filter(event => event.id !== formData.id));
                              // Close the modal
                              setShowEventModal(false);
                              // Show notification
                              notifications.show({
                                title: 'Supprimé',
                                message: 'L\'événement a été supprimé avec succès',
                                color: 'red',
                                autoClose: 3000
                              });
                            }
                          }}
                          className="mb-2 btn-Supp"
                        >
                          Supprimer
                        </Button>
                      )}
                      <Button
                        onClick={() => {
                          handleModalClose();
                        }}
                        className="mb-2 btn-Ann"
                      >
                        Annuler
                      </Button>
                      </Group>
    </Group>
                      </div>
                  </form>
                 </div>
                </SimpleBar>
                       </div>
                        </Modal.Body>
             </Modal.Content>
      
          
          </Modal.Root>
        )}
      </div>
        {/* Modal Pause */}
    <PauseModal
      opened={pauseModalOpened}
      onClose={() => {
        setPauseModalOpened(false);
        setIsPauseMode(false);
      }}
      onSave={(pauseData) => {
        console.log('Pause data saved:', pauseData);
        notifications.show({
          title: 'Pause ajoutée',
          message: 'La pause a été ajoutée avec succès',
          color: 'green',
          autoClose: 3000
        });
      }}
    />

    {/* Care Sheet Quick Access Modal */}
    <CareSheetQuickAccess
      opened={showCareSheetModal}
      onClose={handleCloseCareSheet}
      patientId={careSheetPatientId}
      patientName={careSheetPatientName}
      onNavigateToFullPage={handleNavigateToFullCareSheet}
    />

    {/* Generic States Quick Access Modal */}
    <GenericStatesQuickAccess
      opened={showGenericStatesModal}
      onClose={handleCloseGenericStates}
      dateRange={genericStatesDateRange}
      onNavigateToFullPage={handleNavigateToFullGenericStates}
    />

    {/* Medical Report Quick Access Modal */}
    <MedicalReportQuickAccess
      opened={showMedicalReportModal}
      onClose={handleCloseMedicalReport}
      patientId={medicalReportPatientId}
      patientName={medicalReportPatientName}
      dateRange={medicalReportDateRange}
      onNavigateToFullPage={handleNavigateToFullMedicalReport}
    />

    {/* Payment Quick Access Modal */}
    <PaymentQuickAccess
      opened={showPaymentModal}
      onClose={handleClosePayment}
      patientId={paymentPatientId}
      patientName={paymentPatientName}
      dateRange={paymentDateRange}
      onNavigateToFullPage={handleNavigateToFullPayment}
    />

    {/* Pharmacy Quick Access Modal */}
    <PharmacyQuickAccess
      opened={showPharmacyModal}
      onClose={handleClosePharmacy}
      dateRange={pharmacyDateRange}
      onNavigateToFullPage={handleNavigateToFullPharmacy}
    />

    {/* Prescriptions Quick Access Modal */}
    <PrescriptionsQuickAccess
      opened={showPrescriptionsModal}
      onClose={handleClosePrescriptions}
      patientId={prescriptionsPatientId}
      patientName={prescriptionsPatientName}
      dateRange={prescriptionsDateRange}
      onNavigateToFullPage={handleNavigateToFullPrescriptions}
    />

    {/* Parameters Quick Access Modal */}
    <ParametersQuickAccess
      opened={showParametersModal}
      onClose={handleCloseParameters}
      onNavigateToFullPage={handleNavigateToFullParameters}
    />

    {/* ✅ LUNCH EDIT MODAL - NEW */}
    {showLunchtimeEditModal && lunchtimeEditData && (
      <LunchtimeBackgroundModal
        opened={showLunchtimeEditModal}
        onClose={handleCloseLunchtimeEdit}
        onSave={handleLunchtimeEditSave}
        currentDate={lunchtimeEditData.startTime}
        selectedRoom={lunchtimeEditData.roomId}
        selectedDoctor={lunchtimeEditData.doctorId}
        existingEvents={events}
        staffOptions={doctors.map(doctor => ({
          value: doctor.id,
          label: doctor.name
        }))}
        // Pass initial data for editing
        editMode={true}
        initialData={{
          id: lunchtimeEditData.id,
          title: lunchtimeEditData.title,
          startTime: lunchtimeEditData.startTime,
          duration: lunchtimeEditData.duration,
          doctorId: lunchtimeEditData.doctorId,
          roomId: lunchtimeEditData.roomId,
          color: lunchtimeEditData.color,
          notes: lunchtimeEditData.notes,
          isRecurring: lunchtimeEditData.isRecurring
        }}
      />
    )}
    </>
  );
};

export default CalendarPatient;



