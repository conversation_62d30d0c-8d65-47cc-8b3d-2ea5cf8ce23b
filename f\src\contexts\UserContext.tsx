"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import authService from '~/services/authService';

interface UserProfile {
  id?: string;
  first_name?: string;
  firstName?: string;
  last_name?: string;
  lastName?: string;
  email?: string;
  user_type?: string;
  profile_image?: string;
  assigned_doctor?: string;
}

interface UserContextType {
  userProfile: UserProfile | null;
  setUserProfile: (profile: UserProfile | null) => void;
  isAuthenticated: boolean;
  setIsAuthenticated: (auth: boolean) => void;
  refreshUserProfile: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const refreshUserProfile = async () => {
    try {
      const isAuth = await authService.validateAuthentication();
      setIsAuthenticated(isAuth);

      if (isAuth) {
        const profile = await authService.getUserProfile();
        setUserProfile(profile);
      } else {
        setUserProfile(null);
      }
    } catch (error) {
      console.error('Error refreshing user profile:', error);
      setIsAuthenticated(false);
      setUserProfile(null);
    }
  };

  useEffect(() => {
    refreshUserProfile();
  }, []);

  const value: UserContextType = {
    userProfile,
    setUserProfile,
    isAuthenticated,
    setIsAuthenticated,
    refreshUserProfile,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};
