
'use client';
import React, { useState, useEffect } from 'react';
import { useForm } from '@mantine/form';
import { DictionaryModalsManager } from '@/components/alerte';
import patientService, { BiometricMeasureDefinition, PatientBiometricMeasurement } from '@/services/patientService';
import { patientFormService } from '@/services/patientFormService';
import { notifications } from '@mantine/notifications';
import {
  Group,
  Title,
  Button,
  Text,
  Tooltip,
  ActionIcon,
  Table,

  ScrollArea,
  Card,
  Select,
  Modal,
  Switch,
  Stack,
  Textarea,
  Radio,
  MultiSelect,
  Checkbox,
  Box,
  Input,
  TextInput,
  Menu
} from '@mantine/core';
import Link from 'next/link';
import { DatePickerInput } from '@mantine/dates';
import SimpleBar from "simplebar-react";
import { MeasurementDialog } from './MeasurementDialog';
import { MeasurementTrendsDialog } from './MeasurementTrendsDialog';
import Icon from '@mdi/react';
import {
  mdiCardAccountDetails,
  mdiTooth,
  mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
  mdiChartLine,
  mdiMinus,

  mdiAccountAlert,
  mdiArrowLeft,
  mdiPlus,
  mdiDeleteSweep,
  mdiClipboardText,
  mdiMicrophone,
  mdiHistory,
  mdiArrowRight,
  mdiViewHeadline,
  mdiChevronDown,
  mdiChevronRight,
  mdiDelete,
  mdiPencil,
  mdiCircle,
  mdiCertificate,
  mdiApps,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiFormatListBulleted,mdiAccountSearch,
} from '@mdi/js';
import { Patient, Measurement, MeasureDefinition } from './types';
import RelationForm from "./RelationForm"
// start alert
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface FichePatientProps {
  onInsuredChange?: (isInsured: boolean) => void;
  // Additional props for staff and trigger options
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  openListDesPatient?: () => void;
  onValueChange?: () => void;
  onLocationChange?: () => void;
}
//end alert
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const items = [
  {
    id: '1',
    name: 'Poids',
    data: [60, 62, 63.5, 61.8, 65],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
  {
    id: '2',
    name: 'Taille',
    data: [170, 170, 170, 170, 170],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
];
type PatientActionsProps = {
  patientId?: string;
    isFormInvalid: boolean;
    isDraft: boolean;
    onPrint?: () => void;
    onPrevious?: () => void;
    onNext?: () => void;
    onStartVisit?: () => void;
    onAppointment?: () => void;
    onCancel?: () => void;
    onSaveQuitNew?: () => void;
    onSaveQuit?: () => void;
    onGoBack: () => void;
    onAddMeasurement: () => void;
    onGoToContract: () => void;
    selectedInsurance: string;
    setSelectedInsurance: (value: string) => void;
    affiliateNumber: string;
    setAffiliateNumber: (value: string) => void;
    affiliateType: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT';
    setAffiliateType: (value: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT') => void;
    organizationOptions: { value: string; label: string }[];
     value: string | null;
    onValueChange: (val: string | null) => void;
    onAdd?: () => void;
    locked?: boolean;
     countryId: number | null;
    provinceId: number | null;
    values: { id: string; name: string } | null;
    onLocationChange: (city: { id: string; name: string } | null) => void;
    disabled?: boolean;
      onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName?: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];
   openListDesPatient: () => void;
};
// Interfaces importées depuis types.ts
export interface BiometriePatientActionsProps extends PatientActionsProps {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
   patient: Patient;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
   measures: MeasureDefinition[];
  measurements: Measurement[];

   
};
export const Biometrie = ({
  patient,
  onGoBack,
openListDesPatient,
  patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  //measures,
  //measurements,
  onInsuredChange,
  fullName = 'ABDESSALMAD AGADIR',
  staffOptions = [
    { label: 'TEST DEMO', value: 'test-demo' },
    { label: 'DEMO DEMO', value: 'demo-demo' }
  ],
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],
}: BiometriePatientActionsProps & PatientActionsProps & FichePatientProps) => {
  const disabled = isFormInvalid || isDraft;

  const [modalOpen, setModalOpen] = useState(false);
  const [measurementmodalOpen, setMeasurementModalOpen] = useState(false);
const [isRelationsModalOpen, setIsRelationsModalOpen] = useState(false);
  // Django-integrated biometric data state
  const [biometricDefinitions, setBiometricDefinitions] = useState<BiometricMeasureDefinition[]>([]);
  const [patientMeasurements, setPatientMeasurements] = useState<PatientBiometricMeasurement[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  // Enhanced form state
  const [showAddMeasurementForm, setShowAddMeasurementForm] = useState(false);
  const [editingMeasurement, setEditingMeasurement] = useState<PatientBiometricMeasurement | null>(null);

  // Form state for new measurements
  const [selectedMeasureDefinition, setSelectedMeasureDefinition] = useState<string>('');
  const [measurementValue, setMeasurementValue] = useState<string>('');
  const [measurementNotes, setMeasurementNotes] = useState<string>('');
  const [measurementDate, setMeasurementDate] = useState<Date>(new Date());

  // Legacy state (to be removed gradually)
  const [readOnly, ] = useState(false);

  // Django integration - Load data on component mount
  useEffect(() => {
    const initializeBiometricData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          // Load biometric measure definitions
          await loadBiometricDefinitions();

          // Load patient's existing measurements
          await loadPatientMeasurements();
        } else {
          setError('Django backend is not connected');
        }
      } catch (err) {
        console.error('Error initializing biometric data:', err);
        setError('Failed to connect to Django backend');
        setDjangoStatus('disconnected');
      } finally {
        setLoading(false);
      }
    };

    if (patient?.id) {
      initializeBiometricData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patient?.id]);

  // Load biometric measure definitions
  const loadBiometricDefinitions = async () => {
    try {
      console.log('🔄 Loading biometric definitions...');
      const definitions = await patientService.getBiometricMeasureDefinitions();
      setBiometricDefinitions(definitions);
      console.log('✅ Loaded biometric definitions:', definitions.length);
    } catch (error) {
      console.error('❌ Error loading biometric definitions:', error);
    }
  };

  // Load patient's biometric measurements
  const loadPatientMeasurements = async () => {
    try {
      if (patient?.id) {
        console.log(`🔄 Loading biometric measurements for patient: ${patient.id}`);
        const measurements = await patientFormService.getPatientBiometrics(patient.id);

        // Convert BiometricMeasurement[] to PatientBiometricMeasurement[] format
        const convertedMeasurements = measurements.map(measurement => ({
          id: measurement.id || '',
          measure_definition: {
            id: measurement.measurement_type,
            name: measurement.measurement_type,
            label: measurement.measurement_type,
            measurement_type: 'float' as const,
            unit: measurement.unit,
            normal_range_min: 0,
            normal_range_max: 100,
            display_order: 1,
            category: 'vital_signs' as const,
            is_required: false,
          },
          measurement_date: measurement.measured_date,
          value: measurement.value,
          notes: measurement.notes,
          is_abnormal: false,
          created_at: measurement.created_at || new Date().toISOString(),
          updated_at: measurement.created_at || new Date().toISOString(),
        }));

        setPatientMeasurements(convertedMeasurements);
        console.log('✅ Loaded biometric measurements:', convertedMeasurements.length);
      }
    } catch (error) {
      console.error('❌ Error loading patient measurements:', error);
    }
  };

  // Add new biometric measurement
  const addBiometricMeasurement = async () => {
    if (!selectedMeasureDefinition || !measurementValue || !patient?.id) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please select a measurement type and enter a value',
        color: 'red'
      });
      return;
    }

    try {
      setSubmitting(true);

      console.log('🔄 Adding biometric measurement...');
      const biometricData = {
        measure_definition_id: selectedMeasureDefinition,
        value: parseFloat(measurementValue) || 0,
        measurement_date: measurementDate.toISOString().split('T')[0],
        notes: measurementNotes,
      };

      const result = await patientFormService.createBiometricMeasurement(patient.id, biometricData);

      if (result) {
        // Reload measurements to get updated list
        await loadPatientMeasurements();

        // Clear form
        setSelectedMeasureDefinition('');
        setMeasurementValue('');
        setMeasurementNotes('');
        setMeasurementDate(new Date());

        notifications.show({
          title: 'Measurement Added',
          message: 'Biometric measurement has been saved successfully',
          color: 'green'
        });
      } else {
        throw new Error('Failed to save measurement');
      }
    } catch (error) {
      console.error('Error adding measurement:', error);
      notifications.show({
        title: 'Save Failed',
        message: 'Failed to save biometric measurement. Please try again.',
        color: 'red'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Delete biometric measurement
  const deleteBiometricMeasurement = async (measurementId: string) => {
    if (!patient?.id) return;

    try {
      setSubmitting(true);

      const success = await patientService.deletePatientBiometricMeasurement(patient.id, measurementId);

      if (success) {
        // Reload measurements to get updated list
        await loadPatientMeasurements();

        notifications.show({
          title: 'Measurement Deleted',
          message: 'Biometric measurement has been deleted successfully',
          color: 'green'
        });
      } else {
        throw new Error('Failed to delete measurement');
      }
    } catch (error) {
      console.error('Error deleting measurement:', error);
      notifications.show({
        title: 'Delete Failed',
        message: 'Failed to delete biometric measurement. Please try again.',
        color: 'red'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Update biometric measurement
  const updateBiometricMeasurement = async (measurementId: string) => {
    if (!selectedMeasureDefinition || !measurementValue || !patient?.id) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please select a measurement type and enter a value',
        color: 'red'
      });
      return;
    }

    try {
      setSubmitting(true);

      console.log('🔄 Updating biometric measurement...');
      const biometricData = {
        measure_definition_id: selectedMeasureDefinition,
        value: parseFloat(measurementValue) || 0,
        measurement_date: measurementDate.toISOString().split('T')[0],
        notes: measurementNotes,
      };

      const result = await patientFormService.updateBiometricMeasurement(patient.id, measurementId, biometricData);

      if (result) {
        // Reload measurements to get updated list
        await loadPatientMeasurements();

        // Clear form and editing state
        setSelectedMeasureDefinition('');
        setMeasurementValue('');
        setMeasurementNotes('');
        setMeasurementDate(new Date());
        setEditingMeasurement(null);

        notifications.show({
          title: 'Measurement Updated',
          message: 'Biometric measurement has been updated successfully',
          color: 'green'
        });
      } else {
        throw new Error('Failed to update measurement');
      }
    } catch (error) {
      console.error('Error updating measurement:', error);
      notifications.show({
        title: 'Update Failed',
        message: 'Failed to update biometric measurement. Please try again.',
        color: 'red'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Start editing a measurement
  const startEditingMeasurement = (measurement: PatientBiometricMeasurement) => {
    setEditingMeasurement(measurement);
    setSelectedMeasureDefinition(measurement.measure_definition.id);
    setMeasurementValue(measurement.value.toString());
    setMeasurementNotes(measurement.notes || '');
    setMeasurementDate(new Date(measurement.measurement_date));
    setShowAddMeasurementForm(true);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingMeasurement(null);
    setSelectedMeasureDefinition('');
    setMeasurementValue('');
    setMeasurementNotes('');
    setMeasurementDate(new Date());
    setShowAddMeasurementForm(false);
  };

  // Refresh biometric data
  const refreshBiometricData = async () => {
    if (!patient?.id) return;

    try {
      setLoading(true);
      await loadBiometricDefinitions();
      await loadPatientMeasurements();
      notifications.show({
        title: 'Success',
        message: 'Biometric data refreshed',
        color: 'green',
      });
    } catch (error) {
      console.error('Error refreshing biometric data:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to refresh biometric data',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for biometric data display
  const formatMeasurementValue = (measurement: PatientBiometricMeasurement): string => {
    const value = measurement.value;
    const unit = measurement.measure_definition.unit;

    if (value === null || value === undefined) return 'N/A';

    if (typeof value === 'number') {
      return `${value}${unit ? ' ' + unit : ''}`;
    }

    return `${value}${unit ? ' ' + unit : ''}`;
  };

  // const getMeasurementsByCategory = (category: string) => {
  //   return patientMeasurements.filter(
  //     measurement => measurement.measure_definition.category === category
  //   );
  // };

  // const getLatestMeasurementForDefinition = (definitionId: string) => {
  //   return patientMeasurements
  //     .filter(measurement => measurement.measure_definition.id === definitionId)
  //     .sort((a, b) => new Date(b.measurement_date).getTime() - new Date(a.measurement_date).getTime())[0];
  // };

  // Use Django data instead of legacy measures/measurements
  //const safeMeasures = biometricDefinitions;
  const safeMeasurements = patientMeasurements;

  const headers = [
    <Table.Th key="date">Date</Table.Th>,
    <Table.Th key="measurement">Measurement</Table.Th>,
    <Table.Th key="value">Value</Table.Th>,
    <Table.Th key="status">Status</Table.Th>,
    <Table.Th key="notes">Notes</Table.Th>,
    <Table.Th key="actions">Actions</Table.Th>,
  ];

  const rows =
    safeMeasurements.length === 0 ? (
      <Table.Tr>
        <Table.Td colSpan={6}>
          <Text c="dimmed" ta="center">
            {loading ? 'Loading biometric data...' : 'No biometric measurements recorded yet'}
          </Text>
        </Table.Td>
      </Table.Tr>
    ) : (
      safeMeasurements.map((measurement: PatientBiometricMeasurement) => (
        <Table.Tr key={measurement.id}>
          <Table.Td>
            {new Date(measurement.measurement_date).toLocaleDateString()}
          </Table.Td>
          <Table.Td>
            {measurement.measure_definition.label}
          </Table.Td>
          <Table.Td>
            {formatMeasurementValue(measurement)}
          </Table.Td>
          <Table.Td>
            {measurement.is_abnormal ? (
              <Text c="red" size="sm">Abnormal</Text>
            ) : measurement.is_within_normal_range === false ? (
              <Text c="orange" size="sm">Out of Range</Text>
            ) : (
              <Text c="green" size="sm">Normal</Text>
            )}
          </Table.Td>
          <Table.Td>
            {measurement.notes || '-'}
          </Table.Td>
          <Table.Td>
            <Group gap="xs">
              <ActionIcon
                variant="light"
                color="blue"
                size="sm"
                onClick={() => startEditingMeasurement(measurement)}
                disabled={submitting}
                title="Edit measurement"
              >
                <Icon path={mdiPencil} size={0.8} />
              </ActionIcon>
              <ActionIcon
                variant="light"
                color="red"
                size="sm"
                onClick={() => deleteBiometricMeasurement(measurement.id)}
                disabled={submitting}
                title="Delete measurement"
              >
                <Icon path={mdiMinus} size={0.8} />
              </ActionIcon>
            </Group>
          </Table.Td>
        </Table.Tr>
      ))
    );
    // Start Alert
    
     const [isSidebarAlert, setIsSidebarAlert] = useState(false);
      const toggleSidebarAlert = () => {
              setIsSidebarAlert(!isSidebarAlert);
            };
     
     const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
        const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
        const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
         const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
         const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);
         // État pour gérer l'effondrement de chaque nœud
         // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
         const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
           console.log('TreeItemChoixMultiple initialized with all nodes open');
           return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
         });
       
         // État pour gérer les sélections multiples
         const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
       
         // États pour la gestion des modèles
         const [showModels, setShowModels] = useState(false);
         const [showAddModel, setShowAddModel] = useState(false);
         const [modelTitle, setModelTitle] = useState('');
         const [editingModelId, setEditingModelId] = useState<string | null>(null);
         const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
       
         // États pour la reconnaissance vocale
         const [isListening, setIsListening] = useState(false);
         const [validSpeech, setValidSpeech] = useState('');
         const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
         const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
         const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut
       
         // Initialiser la reconnaissance vocale au montage du composant
         useEffect(() => {
           initSpeechRecognition();
         }, []);
       
         // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
         const toggleNodeCollapse = (nodeId: string) => {
           setCollapsedNodes(prev => {
             const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
             // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
             if (currentState === true) { // Si fermé, ouvrir
               console.log('Opening TreeItemChoixMultiple node:', nodeId);
               return {
                 ...prev,
                 [nodeId]: false // false = ouvert
               };
             } else {
               console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
               return prev; // Ne rien changer si déjà ouvert
             }
           });
         };
         interface TreeNodeChoixMultiple {
           uid: string;
           value: string;
           nodes?: TreeNodeChoixMultiple[];
         }
         function TreeItemChoixMultiple({
           node,
           collapsedNodes,
           toggleNodeCollapse,
           selectedNodes,
           toggleNodeSelection,
         }: {
           node: TreeNodeChoixMultiple;
           collapsedNodes: Record<string, boolean>;
           toggleNodeCollapse: (nodeId: string) => void;
           selectedNodes: Set<string>;
           toggleNodeSelection: (nodeId: string) => void;
         }) {
           // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
           const isCollapsed = collapsedNodes[node.uid] ?? false;
           const isSelected = selectedNodes.has(node.uid);
           // Calculer l'état indéterminé pour les nœuds parents
           const getIndeterminateState = () => {
             if (!node.nodes || node.nodes.length === 0) return false;
             const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
             return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
           };
           const isIndeterminate = getIndeterminateState();
           return (
             <Stack pl="md" gap="xs">
               <Group gap="xs" align="center">
                 {node.nodes && node.nodes.length > 0 && (
                   <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                     <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
                   </span>
                 )}
                 <Checkbox
                   label={node.value}
                   checked={isSelected}
                   indeterminate={isIndeterminate}
                   onChange={() => toggleNodeSelection(node.uid)}
                   radius="xs"
                 />
               </Group>
         
               {!isCollapsed &&
                 node.nodes?.map((child) => (
                   <TreeItemChoixMultiple
                     key={child.uid}
                     node={child}
                     collapsedNodes={collapsedNodes}
                     toggleNodeCollapse={toggleNodeCollapse}
                     selectedNodes={selectedNodes}
                     toggleNodeSelection={toggleNodeSelection}
                   />
                 ))}
             </Stack>
           );
         }
           // Fonction pour basculer la sélection d'un nœud
           const toggleNodeSelection = (nodeId: string) => {
             setSelectedNodes(prev => {
               const newSet = new Set(prev);
         
               // Trouver le nœud correspondant
               const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
                 for (const node of nodes) {
                   if (node.uid === id) return node;
                   if (node.nodes) {
                     const found = findNode(node.nodes, id);
                     if (found) return found;
                   }
                 }
                 return null;
               };
         
               const currentNode = findNode(exampleData, nodeId);
         
               if (newSet.has(nodeId)) {
                 // Désélectionner le nœud et tous ses enfants
                 newSet.delete(nodeId);
                 if (currentNode?.nodes) {
                   const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                     nodes.forEach(child => {
                       newSet.delete(child.uid);
                       if (child.nodes) {
                         removeAllChildren(child.nodes);
                       }
                     });
                   };
                   removeAllChildren(currentNode.nodes);
                 }
               } else {
                 // Sélectionner le nœud et tous ses enfants
                 newSet.add(nodeId);
                 if (currentNode?.nodes) {
                   const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                     nodes.forEach(child => {
                       newSet.add(child.uid);
                       if (child.nodes) {
                         addAllChildren(child.nodes);
                       }
                     });
                   };
                   addAllChildren(currentNode.nodes);
                 }
               }
         
               return newSet;
             });
           };
         
           // Fonction pour obtenir les sélections actuelles
           const getSelectedValues = () => {
             const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
               const result: TreeNodeChoixMultiple[] = [];
               nodes.forEach(node => {
                 result.push(node);
                 if (node.nodes) {
                   result.push(...getAllNodes(node.nodes));
                 }
               });
               return result;
             };
         
             const allNodes = getAllNodes(exampleData);
             return Array.from(selectedNodes)
               .map(id => allNodes.find(node => node.uid === id))
               .filter(Boolean)
               .map(node => node!.value);
           };
     
           // Fonctions pour sélectionner/désélectionner tous les nœuds
           const selectAllNodes = () => {
             const allNodeIds: string[] = [];
     
             const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
               nodes.forEach(node => {
                 allNodeIds.push(node.uid);
                 if (node.nodes && node.nodes.length > 0) {
                   collectAllIds(node.nodes);
                 }
               });
             };
     
             collectAllIds(exampleData);
             setSelectedNodes(new Set(allNodeIds));
           };
     
           const deselectAllNodes = () => {
             setSelectedNodes(new Set());
           };
     
           // Fonctions pour la reconnaissance vocale
           const initSpeechRecognition = () => {
             if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
               const SpeechRecognitionConstructor = (window as unknown as {
                 webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                 SpeechRecognition: new () => SpeechRecognitionInstance;
               }).webkitSpeechRecognition || (window as unknown as {
                 webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                 SpeechRecognition: new () => SpeechRecognitionInstance;
               }).SpeechRecognition;
         
               const newRecognition = new SpeechRecognitionConstructor();
         
               newRecognition.continuous = true;
               newRecognition.interimResults = true;
               newRecognition.lang = 'fr-FR';
         
               newRecognition.onstart = () => {
                 setIsListening(true);
                 setMicrophoneColor('green'); // Changer la couleur en vert
                 setInvalidSpeech('Écoute en cours...');
               };
         
               newRecognition.onresult = (event: unknown) => {
                 const speechEvent = event as {
                   resultIndex: number;
                   results: {
                     length: number;
                     [index: number]: {
                       isFinal: boolean;
                       [index: number]: { transcript: string };
                     };
                   };
                 };
         
                 let finalTranscript = '';
                 let interimTranscript = '';
         
                 for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
                   const transcript = speechEvent.results[i][0].transcript;
                   if (speechEvent.results[i].isFinal) {
                     finalTranscript += transcript;
                   } else {
                     interimTranscript += transcript;
                   }
                 }
         
                 setValidSpeech(finalTranscript);
                 setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
               };
         
               newRecognition.onerror = () => {
                 setIsListening(false);
                 setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
                 setInvalidSpeech('Erreur de reconnaissance vocale');
               };
         
               newRecognition.onend = () => {
                 setIsListening(false);
                 setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
                 setInvalidSpeech('Parlez maintenant.');
               };
         
               setRecognition(newRecognition);
             }
           };
         
           const toggleRecognition = () => {
             if (!recognition) {
               initSpeechRecognition();
               return;
             }
         
             if (isListening) {
               recognition.stop();
             } else {
               recognition.start();
             }
           };
         
           const emptyContent = () => {
             setValidSpeech('');
             setInvalidSpeech('Parlez maintenant.');
             setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
             if (recognition && isListening) {
               recognition.stop();
             }
           };
         
          
         
             // État pour savoir quelle alerte est en cours d'édition
             const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);
     
             // États pour le modal de confirmation de suppression
             const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
             const [alertToDelete, setAlertToDelete] = useState<string | null>(null);
     
             // Fonctions pour gérer les alertes
           
     
             const handleDeleteAlert = (alertId: string) => {
               console.log('Delete alert:', alertId);
               // Ouvrir le modal de confirmation
               setAlertToDelete(alertId);
               setIsDeleteConfirmModalOpen(true);
             };
     
             // Fonction pour confirmer la suppression
             const confirmDeleteAlert = () => {
               if (alertToDelete) {
                 // Supprimer l'alerte de la liste
                 setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
                 console.log('Alert deleted:', alertToDelete);
               }
               // Fermer le modal et réinitialiser
               setIsDeleteConfirmModalOpen(false);
               setAlertToDelete(null);
             };
     
             // Fonction pour annuler la suppression
             const cancelDeleteAlert = () => {
               setIsDeleteConfirmModalOpen(false);
               setAlertToDelete(null);
             };
     
             // Fonction pour obtenir la couleur selon le niveau
             const getLevelColor = (level: string) => {
               switch(level) {
                 case 'MINIMUM': return 'green';
                 case 'MEDIUM': return 'orange';
                 case 'HIGH': return 'red';
                 default: return 'gray';
               }
             };
     
     
     
             // Fonction pour créer les actions d'une alerte
             const createAlertActions = (alertId: string) => (
               <Group gap="xs">
                 <ActionIcon
                   variant="subtle"
                   color="blue"
                   size="sm"
                   onClick={() => {
                     console.log('Edit alert clicked:', alertId);
                     setCurrentEditingAlertId(alertId);
     
                     // Trouver l'alerte à éditer et pré-remplir le formulaire
                     const alertToEdit = alertsData.find(alert => alert.id === alertId);
                     console.log('Alert to edit found:', alertToEdit);
                     if (alertToEdit) {
                       // Trouver la valeur correspondante pour le trigger
                       const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';
     
                       console.log('Editing alert:', alertToEdit);
                       console.log('Trigger value found:', triggerValue);
     
                       form.setValues({
                         trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                         trigger: triggerValue,
                         level: alertToEdit.level,
                         description: alertToEdit.Description,
                         is_permanent: alertToEdit.is_permanent
                       });
     
                       console.log('Form values set for editing:', {
                         trigger_for: alertToEdit.trigger_for || [],
                         trigger: triggerValue,
                         level: alertToEdit.level,
                         description: alertToEdit.Description,
                         is_permanent: alertToEdit.is_permanent
                       });
     
                       console.log('Form values set:', form.values);
                     }
     
                     setIsAlertsAddModalOpen(true);
                     setIsSidebarAlert(true); // Ouvrir aussi la sidebar pour les sélections
                   }}
                 >
                   <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
                 </ActionIcon>
                 <ActionIcon
                   variant="subtle"
                   color="red"
                   size="sm"
                   onClick={() => {
                     console.log('Delete alert clicked:', alertId);
                     handleDeleteAlert(alertId);
                   }}
                 >
                   <Icon path={mdiDelete} size={0.8} color={'red'}/>
                 </ActionIcon>
               </Group>
             );
     
             // Fonction pour gérer la soumission du formulaire d'alerte
             const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
               console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
               console.log('Current editing alert ID:', currentEditingAlertId);
     
               if (currentEditingAlertId) {
                 // Mode édition : mettre à jour l'alerte existante
                 console.log('Editing existing alert:', currentEditingAlertId);
     
                 const updatedAlertData = {
                   id: currentEditingAlertId,
                   level: values.level,
                   is_permanent: values.is_permanent,
                   Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                   Description: values.description,
                   trigger_for: values.trigger_for
                 };
     
                 setAlertsData(prevData => {
                   const updatedData = prevData.map(alert =>
                     alert.id === currentEditingAlertId ? updatedAlertData : alert
                   );
                   console.log('Updated alerts data (edit mode):', updatedData);
                   return updatedData;
                 });
               } else {
                 // Mode ajout : créer une nouvelle alerte
                 console.log('Adding new alert');
     
                 const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                 const newAlertData = {
                   id: newAlertId,
                   level: values.level,
                   is_permanent: values.is_permanent,
                   Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                   Description: values.description,
                   trigger_for: values.trigger_for
                 };
     
                 setAlertsData(prevData => {
                   const updatedData = [...prevData, newAlertData];
                   console.log('Updated alerts data (add mode):', updatedData);
                   return updatedData;
                 });
               }
     
               // Appeler la fonction onSubmit originale si elle existe
               if (onSubmit) {
                 onSubmit(values, autoTrigger);
               }
     
               // Fermer le modal et réinitialiser le formulaire
               setIsAlertsAddModalOpen(false);
               setIsSidebarAlert(false);
               setCurrentEditingAlertId(null);
               form.reset();
             };
     
             // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
             const [alertsData, setAlertsData] = useState<AlertData[]>([]);
     
             // Créer les éléments avec les actions pour le rendu
             const elements = alertsData.map(alert => ({
               ...alert,
               Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
               Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
               Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
               Actions: createAlertActions(alert.id)
             }));
         const rowss = elements.map((element) => (
             <Table.Tr key={element.id}>
               <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
               <Table.Td>{element.Niveau}</Table.Td>
               <Table.Td>{element.Publique}</Table.Td>
               <Table.Td>{element.Permanente}</Table.Td>
               <Table.Td>{element.Description}</Table.Td>
               <Table.Td w={'100px'}>{element.Actions}</Table.Td>
             </Table.Tr>
           ));
            const form = useForm<AlertFormValues>({
             initialValues: {
               trigger_for: [],
               trigger: '',
               level: 'MINIMUM',
               description: '',
               is_permanent: false,
             },
             validate: {
               trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
               trigger: (value) => (!value ? 'Champ requis' : null),
               description: (value) => (!value ? 'Champ requis' : null),
             },
           });
           const [search, setSearch] = useState('');
         
          
         
           const handleValidate = () => {
             let textToAdd = '';
         
             if (showModels) {
               // Valider les modèles sélectionnés
               const selectedModelTexts = savedModels
                 .filter(model => model.selected === true)
                 .flatMap(model => model.selections);
               textToAdd = selectedModelTexts.join(', ');
               console.log('Selected models:', savedModels.filter(model => model.selected === true));
               console.log('Text to add from models:', textToAdd);
             } else {
               // Valider les sélections du dictionnaire
               const selectedValues = getSelectedValues();
               textToAdd = selectedValues.join(', ');
             }
         
             if (textToAdd) {
               // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
               setValidSpeech(textToAdd);
               setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"
     
               // 2. Ajouter le texte au champ description du formulaire
               const currentDescription = form.values.description || '';
               const newDescription = currentDescription
                 ? `${currentDescription}, ${textToAdd}`
                 : textToAdd;
               console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
               form.setFieldValue('description', newDescription);
     
               // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
               // Utiliser la nouvelle description mise à jour
               const combinedText = newDescription;
               console.log('Combined text for alert:', combinedText);
     
               if (currentEditingAlertId) {
                 setAlertsData(prevData =>
                   prevData.map(alert => {
                     if (alert.id === currentEditingAlertId) {
                       return {
                         ...alert,
                         Description: combinedText
                       };
                     }
                     return alert;
                   })
                 );
               } else {
                 // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
                 setAlertsData(prevData =>
                   prevData.map(alert => {
                     if (alert.id === '1') {
                       return {
                         ...alert,
                         Description: combinedText
                       };
                     }
                     return alert;
                   })
                 );
               }
             }
         
     
     
             // Fermer le modal et réinitialiser
             setIsClipboardTextModalOpen(false);
             setIsChoixMultipleModalOpen(false);
             setShowModels(false);
             setSelectedNodes(new Set());
             // Réinitialiser l'ID de l'alerte en cours d'édition
             setCurrentEditingAlertId(null);
           };
         
           const handleCancel = () => {
             // Réinitialiser tous les états
             setSelectedNodes(new Set());
             setShowModels(false);
             setShowAddModel(false);
             setModelTitle('');
             setEditingModelId(null);
             setIsClipboardTextModalOpen(false);
           };
         
           // const handleAddModel = () => {
           //   if (selectedNodes.size > 0) {
           //     setShowAddModel(true);
           //   }
           // };
         
           const handleSaveModel = () => {
             if (modelTitle.trim()) {
               if (editingModelId) {
                 // Mode édition : mettre à jour le modèle existant
                 setSavedModels(prev => prev.map(model =>
                   model.id === editingModelId
                     ? { ...model, title: modelTitle.trim() }
                     : model
                 ));
                 setEditingModelId(null);
                 console.log('Model title updated for ID:', editingModelId);
               } else {
                 // Mode création : créer un nouveau modèle
                 const selectedValues = getSelectedValues();
                 const newModel = {
                   id: `model-${Date.now()}`,
                   title: modelTitle.trim(),
                   selections: selectedValues
                 };
                 setSavedModels(prev => [...prev, newModel]);
                 setSelectedNodes(new Set());
                 console.log('New model created:', newModel);
               }
     
               setModelTitle('');
               setShowAddModel(false);
               // Afficher les modèles après sauvegarde
               setShowModels(true);
             }
           };
     
           const handleEditModel = (modelId: string) => {
             const modelToEdit = savedModels.find(model => model.id === modelId);
             if (modelToEdit) {
               setModelTitle(modelToEdit.title);
               setEditingModelId(modelId);
               setShowModels(false);
               setShowAddModel(true);
               console.log('Editing model:', modelToEdit);
             }
           };
         
           const handleDeleteModel = (modelId: string) => {
             setSavedModels(prev => prev.filter(model => model.id !== modelId));
             setSelectedNodes(prev => {
               const newSet = new Set(prev);
               newSet.delete(modelId);
               return newSet;
             });
           };
         
           const exampleData: TreeNodeChoixMultiple[] = [
             {
               uid: '1',
               value: 'Alertes',
               nodes: [
                 { uid: '1-1', value: 'Allaitante depuis:' },
                 { uid: '1-2', value: 'Allergique à l\'Aspirine' },
         
                 { uid: '1-3', value: 'Allergique à la Pénicilline' },
                 { uid: '1-4', value: 'Arthrose' },
                 { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
                 { uid: '1-6', value: 'Diabétique NID' },
                 { uid: '1-7', value: 'Enceinte depuis:' },
                 { uid: '1-8', value: 'Diabétique ID' },
                 { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
                 { uid: '1-10', value: 'Hypertension' },
                  { uid: '1-11', value: 'Hypotension' },
                 { uid: '1-12', value: 'Thyroïde' },
           
         
               ],
             },
             
           ];
         //end header
     // Interface et données pour l'arbre de la sidebar
     interface TreeNode {
       value: string;
       children?: TreeNode[];
     }
     
     const mockTree: TreeNode[] = [
       {
         value: 'Alertes',
         children: [
           { value: "Allaitante depuis:" },
           { value: "Allergique à l'Aspirine" },
           { value: "Allergique à la Pénicilline" },
           { value: "Arthrose" },
           { value: "Cardiaque Anticoagulant sintrom" },
           { value: "Cardiaque prothèse valvulaire" },
           { value: "Cardiaque trouble du rythme" },
           { value: "Diabétique ID" },
           { value: "Diabétique NID" },
           { value: "Enceinte depuis:" },
           { value: "Gastralgie : ulcère anti-inflammatoire" },
           { value: "Hypertension" },
           { value: "Hypotension" },
           { value: "Thyroïde" },
         ],
       },
     ];
     
     // Composant Tree pour la sidebar
     function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
       // Initialiser tous les nœuds comme ouverts
       const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
         const initialExpanded: Record<string, boolean> = {};
         const expandAllNodes = (nodeList: TreeNode[]) => {
           nodeList.forEach(node => {
             if (node.children && node.children.length > 0) {
               initialExpanded[node.value] = true;
               expandAllNodes(node.children);
             }
           });
         };
         expandAllNodes(nodes);
         console.log('Tree initialized with expanded nodes:', initialExpanded);
         return initialExpanded;
       });
     
       return (
         <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
           {nodes.map((node, idx) => {
             const hasChildren = node.children && node.children.length > 0;
             const isOpen = expanded[node.value] || false;
             return (
               <li key={node.value + idx}>
                 <Group gap="xs" align="center" onClick={() => {
                   // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
                   if (hasChildren && !isOpen) {
                     console.log('Opening node:', node.value);
                     setExpanded(prev => ({ ...prev, [node.value]: true }));
                   } else if (hasChildren && isOpen) {
                     console.log('Node already open, not closing:', node.value);
                   }
                 }} className="Alertesslidbar">
                   {hasChildren ? (
                     <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
                   ) : null}
                   <Text
                     onClick={() => !hasChildren && onSelect(node.value)}
                     style={{ cursor: 'pointer' ,paddingLeft:'10px'
                     }}
                   >
                     {node.value}
                   </Text>
                 </Group>
                 {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
               </li>
             );
           })}
         </ul>
       );
     }
      const [, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
     // Fonctions pour gérer la sidebar
     const handleSidebarSelect = (value: string) => {
       console.log('Selected from sidebar:', value);
     
       // 1. Ajouter la sélection au formulaire en cours d'édition
       const currentDescription = form.values.description || '';
       const newFormDescription = currentDescription
         ? `${currentDescription}, ${value}`
         : value;
       form.setFieldValue('description', newFormDescription);
     
       // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
       if (currentEditingAlertId) {
         setAlertsData(prevData =>
           prevData.map(alert => {
             if (alert.id === currentEditingAlertId) {
               const currentAlertDescription = alert.Description || '';
               const newAlertDescription = currentAlertDescription
                 ? `${currentAlertDescription}, ${value}`
                 : value;
               return {
                 ...alert,
                 Description: newAlertDescription
               };
             }
             return alert;
           })
         );
       }
     
       console.log('Added to form description:', newFormDescription);
     
       // Optionnel : fermer la sidebar après sélection
       // setIsSidebarVisible(false);
     };
     
     const handleCloseSidebar = () => {
       setIsSidebarAlert(false);
     };
       
         const FicheForm = useForm({
         initialValues: {
               file_number:1,
               category: '',
               pricing: 0,
               is_bookmarked: false,
               insured: true, // Set to true to match defaultChecked
              description:'',
              titre: '',
         },
         validate: {
           pricing: (value) => (value < 0 ? 'Last name must be at least 2 characters' : null),
         },
       });
     
       // Effect to notify parent component when insured status changes
       useEffect(() => {
         if (onInsuredChange) {
           onInsuredChange(FicheForm.values.insured);
         }
       }, [FicheForm.values.insured, onInsuredChange]);
     
       // Effect to set initial insured state when component mounts
       useEffect(() => {
         if (onInsuredChange && FicheForm.values.insured !== undefined) {
           onInsuredChange(FicheForm.values.insured);
         }
       }, [onInsuredChange, FicheForm.values.insured]); // Include dependency
      
    // End Alert 
  return (
    <>

      {/* En-tête moderne */}
       <div  className={  "bg-[#3799ce] text-white px-4 py-3 rounded-t-lg w-[100%]" }>
                     <Group justify="space-between" align="center">
                       <Group>
                         {patient ? (
                           <Icon path={mdiCardAccountDetails} size={1} />
                         ) : (
                           <Button variant="subtle" onClick={onGoBack}>
                             <Icon path={mdiArrowLeft} size={1} />
                           </Button>
                         )}
                         <Title order={2}>Fiche patient</Title>
                         <DatePickerInput placeholder="Date de création" />
                         23/06/2025
                       </Group>
                 
                       {patient && (
                         <Group>
                           <Text>{patient.first_name} {patient.last_name}</Text>
                           <Text>{patient.gender}</Text>
                           <Text>{patient.age}</Text>
                           <Text>{patient.sociale}</Text>
                           <Text>{patient.id}</Text>
                           <Text>{patient.lastVisit?.date ? new Date(patient.lastVisit.date).toLocaleDateString() : ''}</Text>
                         </Group>
                       )}
                 
                       <Group>
                       <Group>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              variant="white"
              onClick={() => setModalOpen(true)}
            >
              Ajouter des biométries
            </Button>
            <Button
              leftSection={<Icon path={mdiChartLine} size={0.8} />}
              variant="outline"
              c="white"
              onClick={() => setMeasurementModalOpen(true)}
            >
              Tendances
            </Button>
          </Group>
                 <Tooltip label="List" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                         <Menu shadow="md" width={220}>
                           <Menu.Target>
                             <Button variant="subtle">
                               <Icon path={mdiApps} size={1} color={"white"}/>
                             </Button>
                           </Menu.Target>
                           <Menu.Dropdown>
                             <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />} onClick={() => setIsAlertsModalOpen(true)}>Alerts</Menu.Item>
                           
                             <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />} onClick={() => setIsRelationsModalOpen(true)}>Relations Patient</Menu.Item>
                             <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
                             <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
                             <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
                             <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
                           </Menu.Dropdown>
                         </Menu>
                 </Tooltip>
                         <Tooltip label="Dossier d'abonnement" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                           {/* <Button variant="subtle" onClick={onGoToContract}> */}
                             <Button variant="light" component={Link} href={`/patients/patient-form/Abonnement/`} >
                             <Icon path={mdiCertificate} size={1} color={"white"}/>
                           </Button>
                         </Tooltip>
                 
                         <Tooltip label="Liste patients" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                           <Button component="a" href="/patients?tab=Complets" variant="subtle">
                             <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
                           </Button>
                         </Tooltip>
                       </Group>
                     </Group>
                 {/* menu Alerts */}
                          <Modal.Root
                             opened={isAlertsModalOpen}
                             onClose={() => setIsAlertsModalOpen(false)}
                             transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                             centered
                             size="xl"
                           > 
                         
                          <Modal.Content className="overflow-y-hidden">
                           <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                             <Modal.Title>
                               <Group>
                                 <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                   <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
                               </Group>
                             </Modal.Title>
                               <Group justify="flex-end">
                                 <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                                 onClick={() => {
                                   setIsAlertsAddModalOpen(true);
                                   toggleSidebarAlert();
                                 }}>
                          <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                 </ActionIcon>
                                         <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                       </Group>
                           </Modal.Header>
                             <Modal.Body style={{ padding: '0px' }}>
                              <div className={elements.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                                     
                                       <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                         <div className="pr-4">
                                          <Table striped highlightOnHover withTableBorder withColumnBorders>
                                            <Table.Thead>
                         <Table.Tr>
                           <Table.Th>Déclencheur</Table.Th>
                           <Table.Th>Niveau</Table.Th>
                           <Table.Th>Publique</Table.Th>
                           <Table.Th>Permanente</Table.Th>
                            <Table.Th>Description</Table.Th>
                           <Table.Th></Table.Th>
                         </Table.Tr>
                       </Table.Thead>
                       <Table.Tbody>{rowss}</Table.Tbody>
                       {elements.length === 0 && (
                         <Table.Caption>Aucun élément trouvé.</Table.Caption>
                       )}
                                           </Table>
                                         </div>
                                       </SimpleBar>
                                     </div>
                                   </Modal.Body>
                          </Modal.Content>
                           </Modal.Root>
                        
                      
      
                           {/* Modal de confirmation de suppression */}
                           <Modal.Root
                             opened={isDeleteConfirmModalOpen}
                             onClose={cancelDeleteAlert}
                             centered
                             size="sm"
                           >
                             <Modal.Content>
                               <Modal.Header>
                                 <Modal.Title>Confirmation de suppression</Modal.Title>
                                 <Modal.CloseButton />
                               </Modal.Header>
                               <Modal.Body>
                                 <Text size="md" mb="md">
                                   Êtes-vous sûr de vouloir supprimer alert ??
                                 </Text>
                                 <Group justify="flex-end" gap="sm">
                                   <Button
                                     variant="outline"
                                     color="blue"
                                     onClick={confirmDeleteAlert}
                                   >
                                     Oui
                                   </Button>
                                   <Button
                                     variant="filled"
                                     color="red"
                                     onClick={cancelDeleteAlert}
                                   >
                                     Non
                                   </Button>
                                 </Group>
                               </Modal.Body>
                             </Modal.Content>
                           </Modal.Root>
      
                     </div>
      <div className='flex'>
        <div className={isSidebarAlert ? "w-[80%]" : "w-full"}>
<div>
      {/* Django-Integrated Biometric Measurement Form */}
      <div style={{ marginTop: '16px', marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
          <Text fw={500} style={{ flex: 1 }}>Biometric Measurements</Text>
          <Group gap="xs">
            {djangoStatus === 'connected' ? (
              <Text size="xs" c="green">Django Connected</Text>
            ) : (
              <Text size="xs" c="red">Django Disconnected</Text>
            )}
          </Group>
        </div>

        {/* Error Display */}
        {error && (
          <Card shadow="0" padding="sm" radius="md" withBorder mb="md" style={{ backgroundColor: '#ffebee' }}>
            <Text c="red" size="sm">{error}</Text>
          </Card>
        )}

        {/* New/Edit Measurement Form */}
        {!readOnly && djangoStatus === 'connected' && (
          <Card shadow="0" padding="lg" radius="md" withBorder mb="md">
            <Group justify="space-between" mb="md">
              <Text fw={500}>
                {editingMeasurement ? 'Edit Measurement' : 'Add New Measurement'}
              </Text>
              <Group gap="xs">
                <Button
                  variant="light"
                  size="xs"
                  onClick={refreshBiometricData}
                  loading={loading}
                  leftSection={<Icon path={mdiHistory} size={0.6} />}
                >
                  Refresh
                </Button>
                {editingMeasurement && (
                  <Button
                    variant="light"
                    color="gray"
                    size="xs"
                    onClick={cancelEditing}
                  >
                    Cancel Edit
                  </Button>
                )}
              </Group>
            </Group>
            <Group grow>
              <Select
                label="Measurement Type"
                placeholder="Select measurement type"
                data={biometricDefinitions.map(def => ({
                  value: def.id,
                  label: `${def.label}${def.unit ? ` (${def.unit})` : ''}`
                }))}
                value={selectedMeasureDefinition}
                onChange={(value) => setSelectedMeasureDefinition(value || '')}
                disabled={submitting}
              />
              <TextInput
                label="Value"
                placeholder="Enter measurement value"
                value={measurementValue}
                onChange={(e) => setMeasurementValue(e.currentTarget.value)}
                disabled={submitting}
              />
            </Group>
            <Group grow mt="md">
              <DatePickerInput
                label="Measurement Date"
                value={measurementDate}
                onChange={(value) => {
                  if (value) {
                    setMeasurementDate(new Date(value));
                  } else {
                    setMeasurementDate(new Date());
                  }
                }}
                disabled={submitting}
              />
              <Textarea
                label="Notes (Optional)"
                placeholder="Additional notes..."
                value={measurementNotes}
                onChange={(e) => setMeasurementNotes(e.currentTarget.value)}
                disabled={submitting}
                rows={2}
              />
            </Group>
            <Group justify="flex-end" mt="md">
              <Button
                onClick={() => {
                  if (editingMeasurement) {
                    updateBiometricMeasurement(editingMeasurement.id);
                  } else {
                    addBiometricMeasurement();
                  }
                }}
                disabled={!selectedMeasureDefinition || !measurementValue || submitting}
                loading={submitting}
                leftSection={<Icon path={editingMeasurement ? mdiPencil : mdiPlus} size={0.8} />}
              >
                {editingMeasurement ? 'Update Measurement' : 'Add Measurement'}
              </Button>
            </Group>
          </Card>
        )}

        {/* Loading State */}
        {loading && (
          <Card shadow="0" padding="lg" radius="md" withBorder>
            <Group justify="center">
              <Text size="sm">Loading biometric data...</Text>
            </Group>
          </Card>
        )}
      </div>

      {/* Biometric Data Summary */}
      {djangoStatus === 'connected' && patientMeasurements.length > 0 && (
        <Card shadow="sm" padding="md" radius="md" withBorder mb="md">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="lg">Biometric Summary</Text>
            <Group gap="xs">
              <Text size="xs" c="dimmed">
                {patientMeasurements.length} measurement(s) recorded
              </Text>
              <Button
                variant="light"
                size="xs"
                onClick={refreshBiometricData}
                loading={loading}
              >
                Refresh
              </Button>
            </Group>
          </Group>

          <Group gap="md">
            {/* Latest measurements by category */}
            {['vital_signs', 'body_measurements', 'cardiovascular'].map(category => {
              const categoryMeasurements = patientMeasurements.filter(
                m => m.measure_definition.category === category
              );
              if (categoryMeasurements.length === 0) return null;

              const latest = categoryMeasurements.sort(
                (a, b) => new Date(b.measurement_date).getTime() - new Date(a.measurement_date).getTime()
              )[0];

              return (
                <Card key={category} padding="xs" withBorder style={{ minWidth: '150px' }}>
                  <Text size="xs" c="dimmed" tt="capitalize">
                    {category.replace('_', ' ')}
                  </Text>
                  <Text fw={500} size="sm">
                    {latest.measure_definition.label}
                  </Text>
                  <Text size="sm">
                    {formatMeasurementValue(latest)}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {new Date(latest.measurement_date).toLocaleDateString()}
                  </Text>
                </Card>
              );
            })}
          </Group>
        </Card>
      )}

      {/* Tableau des mesures moderne */}
      <ScrollArea>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                {headers}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {rows}
            </Table.Tbody>
          </Table>
        </Card>
      </ScrollArea>
    
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={() => {
          if (onSubmit) {
            onSubmit(form.values, false);
          }
        }}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    <MeasurementDialog
  opened={modalOpen}
  onClose={() => setModalOpen(false)}
  onSubmit={(values) => {
    console.log('Biométrie soumise:', values);
    setModalOpen(false);
  }}
/>
<MeasurementTrendsDialog
  opened={measurementmodalOpen}
  onClose={() => setMeasurementModalOpen(false)}
  patientId={patient?.id}
/>
</div>
</div>
{isSidebarAlert && (
           <Card shadow="sm" mt={'10px'} padding="lg" radius="md" withBorder className={isSidebarAlert ? "w-[20%]" : "w-full"}>
          <Box mb="sm">
            <Group>
              <Input
                placeholder="Rechercher"
                value={search}
                onChange={(e) => setSearch(e.currentTarget.value)}
                w={"70%"}
              />

              <Group justify="flex-end">
                <ActionIcon
                  variant="filled"
                  aria-label="Multiple"
                  color="#3799CE"
                  onClick={() => {
                    // Vous pouvez ajouter ici la logique pour ouvrir le modal de choix multiple
                    console.log('Open multiple choice modal');
                    setIsSidebarAlert(false);
                    setIsChoixMultipleModalOpen(true);
                     
                  }}
                >
                  <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                <ActionIcon
                  variant="filled"
                  aria-label="Annuler"
                  color="#3799CE"
                  onClick={handleCloseSidebar}
                >
                  <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
              </Group>
            </Group>
          </Box>

          <ScrollArea h={400}>
            <Tree
              nodes={mockTree.filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
              onSelect={handleSidebarSelect}
            
            />
          </ScrollArea>
           </Card>
              )}
</div>

                         {/* add Alerts */}
                          <Modal.Root
                           opened={isAlertsAddModalOpen}
                           onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         > 
                        
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    {`Alerte - ${fullName}`} 
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                    
                            <div className={rowss.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
                           
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                      <form
                                             onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                             style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                           >
                                             <Group>
                                             <MultiSelect
                                               label="Déclencher pour"
                                               data={staffOptions}
                                               {...form.getInputProps('trigger_for')}
                                               required
                                               w={"30%"}
                                             />
                                             <Select
                                               label="Déclencheur"
                                               data={triggerOptions}
                                               {...form.getInputProps('trigger')}
                                               required
                                                w={"30%"}
                                             />
                                             <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                               <Group>
                                                 <Radio value="MINIMUM" label="Minimum" />
                                                 <Radio value="MEDIUM" label="Moyen" />
                                                 <Radio value="HIGH" label="Haut" />
                                               </Group>
                                             </Radio.Group>
                                             </Group>
                                             <Group justify="space-between">
                                               <Text>Description *</Text>
                                               <Group>
                                                <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>setIsMicrophoneModalOpen(true)
                                             }>
                                             <Icon path={mdiMicrophone} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>{
                                                 console.log('Dictionary button clicked, sidebar visible:', isSidebarAlert);
                                                 setIsClipboardTextModalOpen(true);
                                                    setShowModels(true);
                                                    setShowAddModel(false);
                                                    handleCloseSidebar()
    
                                               }
                                             }>
                                             <Icon path={mdiClipboardText} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon
                                                variant="filled"
                                                aria-label="Clear Description"
                                                color="red"
                                                onClick={() => {
                                                  console.log('Clear button clicked, clearing description field');
                                                  form.setFieldValue('description', '');
                                                  console.log('Description field cleared');
                                                }}
                                              >
                                                <Icon path={mdiDeleteSweep} size={1} />
                                              </ActionIcon>
                                               </Group>
                                             </Group>
                                             <Textarea
                                               // label="Description"
                                               placeholder="Ajouter"
                                               {...form.getInputProps('description')}
                                               required
                                             />
                                           
                                             <Switch
                                               label="Permanente"
                                               {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                             />
                                     
                                             <Group justify="flex-end" mt="md">
                                               <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                                 Annuler
                                               </Button>
                                               <Button
                                                 onClick={() => {
                                                   if (form.isValid()) {
                                                     handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                                   }
                                                 }}
                                                 disabled={!form.isValid()}
                                               >
                                                 Enregistrer et déclencher
                                               </Button>
                                               <Button type="submit" disabled={!form.isValid()}>
                                                 Enregistrer
                                               </Button>
                                             </Group>
                                           </form>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                     
    
                         {/* Modal de confirmation de suppression */}
                         <Modal.Root
                           opened={isDeleteConfirmModalOpen}
                           onClose={cancelDeleteAlert}
                           centered
                           size="sm"
                         >
                           <Modal.Content>
                             <Modal.Header>
                               <Modal.Title>Confirmation de suppression</Modal.Title>
                               <Modal.CloseButton />
                             </Modal.Header>
                             <Modal.Body>
                               <Text size="md" mb="md">
                                 Êtes-vous sûr de vouloir supprimer alert ??
                               </Text>
                               <Group justify="flex-end" gap="sm">
                                 <Button
                                   variant="outline"
                                   color="blue"
                                   onClick={confirmDeleteAlert}
                                 >
                                   Oui
                                 </Button>
                                 <Button
                                   variant="filled"
                                   color="red"
                                   onClick={cancelDeleteAlert}
                                 >
                                   Non
                                 </Button>
                               </Group>
                             </Modal.Body>
                           </Modal.Content>
                         </Modal.Root>
                          {/* Modal Microphone - Reconnaissance vocale */}
                                          <Modal
                                            opened={isMicrophoneModalOpen}
                                            onClose={() => setIsMicrophoneModalOpen(false)}
                                            title="Reconnaissance vocale"
                                            size="lg"
                                            radius={0}
                                            transitionProps={{ transition: 'fade', duration: 200 }}
                                            centered
                                            withCloseButton={false}
                                            yOffset="30vh" xOffset={0}
                                            
                                          >
                                            <div style={{ padding: '20px' }}>
                                              {/* Interface de reconnaissance vocale */}
                                              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                                                <div style={{ flex: 1, marginRight: '16px' }}>
                                                  <div style={{
                                                    border: '1px solid #e0e0e0',
                                                    borderRadius: '4px',
                                                    padding: '12px',
                                                    minHeight: '80px',
                                                    backgroundColor: '#fafafa',
                                                   height:'150px'
                                                  }}>
                                                    {/* Texte valide reconnu */}
                                                    <span
                                                      style={{
                                                        color: '#2e7d32',
                                                        fontWeight: 500,
                                                        display: validSpeech ? 'inline' : 'none'
                                                      }}
                                                      contentEditable
                                                    >
                                                      {validSpeech}
                                                    </span>
                                                    {/* Texte en cours de reconnaissance */}
                                                    <span
                                                      style={{
                                                        color: '#757575',
                                                        fontStyle: 'italic'
                                                      }}
                                                    >
                                                      {invalidSpeech}
                                                    </span>
                                                  </div>
                                                </div>
                                    
                                                {/* Boutons de contrôle */}
                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color={isListening ? 'orange' : 'blue'}
                                                    size="lg"
                                                    onClick={toggleRecognition}
                                                    style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                                                  >
                                                    <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                                                  </ActionIcon>
                                    
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color="red"
                                                    size="lg"
                                                    onClick={emptyContent}
                                                  >
                                                    <Icon path={mdiDeleteSweep} size={1} />
                                                  </ActionIcon>
                                                </div>
                                              </div>
                                    
                                              {/* Boutons d'action */}
                                              <Group justify="flex-end" mt="md">
                                                <Button
                                                  variant="filled"
                                                  onClick={() => {
                                                    // Ici vous pouvez traiter le texte reconnu
                                                    console.log('Texte reconnu:', validSpeech);
                                                    setIsMicrophoneModalOpen(false);
                                                  }}
                                                >
                                                  Valider
                                                </Button>
                                                <Button
                                                  variant="outline"
                                                  color="red"
                                                  onClick={() => setIsMicrophoneModalOpen(false)}
                                                >
                                                  Annuler
                                                </Button>
                                              </Group>
                                            </div>
                                          </Modal>
                                    
                                          {/* Gestionnaire des modaux de dictionnaire */}
                                          <DictionaryModalsManager
                                            // États des modaux
                                            isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                                            isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                                            isDictionaryTreeModalOpen={isChoixMultipleModalOpen}
                         
                                            // Données
                                            modelTitle={modelTitle}
                                            savedModels={savedModels}
                                            exampleData={exampleData}
                                            selectedNodes={selectedNodes}
                                            collapsedNodes={collapsedNodes}
                                            editingModelId={editingModelId}
                         
                                            // Fonctions de gestion des états
                                            setModelTitle={setModelTitle}
                                            setIsAddModelModalOpen={setShowAddModel}
                                            setIsSavedModelsModalOpen={setShowModels}
                                            setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}
                         
                                            // Fonctions de gestion des modèles
                                            onSaveModel={handleSaveModel}
                                            onToggleModel={(modelId) => {
                                              console.log('Toggling model:', modelId);
                                              setSavedModels(prev => {
                                                const updated = prev.map(model =>
                                                  model.id === modelId
                                                    ? { ...model, selected: !model.selected }
                                                    : model
                                                );
                                                console.log('Updated savedModels:', updated);
                                                return updated;
                                              });
                                            }}
                                            onDeleteModel={handleDeleteModel}
                                            onEditModel={handleEditModel}
                         
                                            // Fonctions de gestion de l'arbre
                                            onToggleNodeCollapse={toggleNodeCollapse}
                                            onToggleNodeSelection={toggleNodeSelection}
                                            onSelectAll={selectAllNodes}
                                            onDeselectAll={deselectAllNodes}
                         
                                            // Fonctions d'action
                                            onValidate={handleValidate}
                                            onCancel={handleCancel}
                                            onCloseSidebar={() => {
                                              console.log('Closing sidebar from SavedModelsModal');
                                              setIsSidebarVisible(false);
                                            }}
                                            getSelectedValues={getSelectedValues}
                         
                                            // Composants
                                            TreeItemChoixMultiple={TreeItemChoixMultiple}
                                          />
                         
                                          {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                                          {isClipboardTextModalOpen && !showModels && !showAddModel && (
                                            <div style={{ display: 'none' }}>
                                              {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                                            </div>
                                          )}
     {/*modal Relations */}
                        <Modal.Root
                           opened={isRelationsModalOpen}
                           onClose={() => setIsRelationsModalOpen(false)}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="lg"
                           zIndex={10}
                         > 
                       
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountSupervisorCircle} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                 Relations - ABDESSALMAD AGADIR </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                               <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                               onClick={openListDesPatient}>
                        <Icon path={mdiAccountSearch} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                               </ActionIcon>
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                            <div className="py-2 pl-4 h-auto overflow-hidden" >
                                   
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                        <Card shadow="sm" padding="lg" radius="md" withBorder>
                                          <RelationForm/>
                                        </Card>
                                         
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                                  <Group justify="flex-end" m="md">
                                  <Button variant="outline" color="red" onClick={() => setIsRelationsModalOpen(false)}>
                                    Annuler
                                  </Button>
                                  <Button type="submit" disabled={!form.isValid()}>
                                    Enregistrer
                                  </Button>
                                </Group> 
                        </Modal.Content>
                         </Modal.Root>
    </>
  )
}


