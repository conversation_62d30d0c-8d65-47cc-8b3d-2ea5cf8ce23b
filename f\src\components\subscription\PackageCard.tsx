'use client';

import { Card, Text, Badge, Button, Group, Stack, List, ThemeIcon, rem } from '@mantine/core';
import { IconCheck } from '@tabler/icons-react';
import { SubscriptionPackage } from '~/services/subscriptionService';

interface PackageCardProps {
  package: SubscriptionPackage;
  selectedBillingCycle: 'monthly' | 'annual';
  onSelect: (pkg: SubscriptionPackage) => void;
  isSelected?: boolean;
}

export default function PackageCard({ 
  package: pkg, 
  selectedBillingCycle, 
  onSelect, 
  isSelected = false 
}: PackageCardProps) {
  const price = selectedBillingCycle === 'monthly' 
    ? parseFloat(pkg.price_monthly) 
    : parseFloat(pkg.price_yearly);
  
  const savings = selectedBillingCycle === 'annual' 
    ? (parseFloat(pkg.price_monthly) * 12 - parseFloat(pkg.price_yearly)).toFixed(2)
    : null;

  return (
    <Card 
      shadow="sm" 
      padding="lg" 
      radius="md" 
      withBorder
      style={{ 
        borderColor: isSelected ? 'var(--mantine-color-blue-6)' : undefined,
        borderWidth: isSelected ? '2px' : '1px',
      }}
    >
      <Stack>
        <Group justify="space-between" mt="md" mb="xs">
          <Text fw={700} size="lg">{pkg.name}</Text>
          <Badge color={pkg.name.toLowerCase().includes('premium') ? 'blue' : pkg.name.toLowerCase().includes('standard') ? 'green' : 'gray'}>
            {pkg.name.toLowerCase().includes('premium') ? 'Premium' : pkg.name.toLowerCase().includes('standard') ? 'Standard' : 'Basic'}
          </Badge>
        </Group>

        <Text size="sm" c="dimmed">
          {pkg.description || 'A comprehensive package for medical professionals.'}
        </Text>

        <Group align="flex-end" gap="xs" mt="md">
          <Text size="xl" fw={700}>${price.toFixed(2)}</Text>
          <Text size="sm" c="dimmed" mb={5}>
            /{selectedBillingCycle === 'monthly' ? 'month' : 'year'}
          </Text>
        </Group>

        {savings && (
          <Text size="sm" c="green" fw={500}>
            Save ${savings} per year
          </Text>
        )}

        <List
          spacing="sm"
          size="sm"
          mt="md"
          center
          icon={
            <ThemeIcon color="blue" size={20} radius="xl">
              <IconCheck style={{ width: rem(12), height: rem(12) }} />
            </ThemeIcon>
          }
        >
          <List.Item>Up to {pkg.max_assistants} medical assistants</List.Item>
          <List.Item>Up to {pkg.max_users} users</List.Item>
          <List.Item>Up to {pkg.max_specialties} specialties</List.Item>
          {pkg.features.map((feature, index) => (
            <List.Item key={index}>{feature}</List.Item>
          ))}
        </List>

        <Button 
          variant={isSelected ? "filled" : "outline"} 
          color="blue" 
          fullWidth 
          mt="md" 
          radius="md"
          onClick={() => onSelect(pkg)}
        >
          {isSelected ? 'Selected' : 'Select Package'}
        </Button>
      </Stack>
    </Card>
  );
}
