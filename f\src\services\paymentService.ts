/**
 * Payment Service
 * Handles all payment-related data operations including:
 * - Payment collections (Encaissements)
 * - Account balances and general account status
 * - Payment methods and transactions
 * - Payment analytics and reporting
 */

// Types for Payment data
export interface PaymentCollection {
  id: number;
  date: string;
  patient_id: string;
  patient_name: string;
  payeur: string;
  payment_method: 'cash' | 'check' | 'card' | 'transfer' | 'insurance';
  amount_collected: number;
  amount_consumed: number;
  remaining_balance: number;
  doctor_id: string;
  doctor_name: string;
  reference?: string;
  notes?: string;
  status: 'completed' | 'pending' | 'cancelled' | 'refunded';
  created_at: string;
}

export interface AccountBalance {
  id: number;
  patient_id: string;
  patient_name: string;
  total_due: number;
  total_collected: number;
  remaining_balance: number;
  last_payment_date?: string;
  last_payment_amount?: number;
  payment_history: PaymentHistory[];
  account_status: 'current' | 'overdue' | 'paid' | 'credit';
  created_at: string;
  updated_at: string;
}

export interface PaymentHistory {
  id: number;
  date: string;
  amount: number;
  method: string;
  reference?: string;
  notes?: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'cash' | 'check' | 'card' | 'transfer' | 'insurance';
  is_active: boolean;
  requires_reference: boolean;
  processing_fee?: number;
}

export interface PaymentTransaction {
  id: number;
  transaction_date: string;
  patient_id: string;
  patient_name: string;
  amount: number;
  payment_method: string;
  reference?: string;
  description: string;
  type: 'payment' | 'refund' | 'adjustment' | 'fee';
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  processed_by: string;
  created_at: string;
}

export interface PaymentAnalytics {
  id: number;
  report_date: string;
  total_collections: number;
  total_outstanding: number;
  payment_methods_breakdown: PaymentMethodBreakdown[];
  daily_collections: DailyCollection[];
  top_paying_patients: TopPayingPatient[];
  overdue_accounts: OverdueAccount[];
  collection_rate: number;
  average_payment_time: number;
}

export interface PaymentMethodBreakdown {
  method: string;
  count: number;
  total_amount: number;
  percentage: number;
}

export interface DailyCollection {
  date: string;
  total_amount: number;
  transaction_count: number;
  average_amount: number;
}

export interface TopPayingPatient {
  patient_id: string;
  patient_name: string;
  total_paid: number;
  payment_count: number;
  last_payment_date: string;
}

export interface OverdueAccount {
  patient_id: string;
  patient_name: string;
  overdue_amount: number;
  days_overdue: number;
  last_contact_date?: string;
}

export interface PaymentSummary {
  reportDate: string;
  paymentCollections: PaymentCollection[];
  accountBalances: AccountBalance[];
  paymentMethods: PaymentMethod[];
  paymentTransactions: PaymentTransaction[];
  paymentAnalytics: PaymentAnalytics;
  lastUpdate: string;
}

class PaymentService {
  private baseURL = '/api/payments';

  // Payment Collections operations
  async getPaymentCollections(patientId?: string, dateRange?: { start: string; end: string }): Promise<PaymentCollection[]> {
    try {
      let url = `${this.baseURL}/collections`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Payment collections API not available (${response.status}), using mock data`);
        return this.getMockPaymentCollections(patientId);
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Payment collections API error, using mock data:', error);
      return this.getMockPaymentCollections(patientId);
    }
  }

  async createPaymentCollection(collectionData: Omit<PaymentCollection, 'id' | 'created_at'>): Promise<PaymentCollection> {
    try {
      const response = await fetch(`${this.baseURL}/collections`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(collectionData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create payment collection: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating payment collection:', error);
      // Return mock created data
      return {
        id: Date.now(),
        ...collectionData,
        created_at: new Date().toISOString(),
      };
    }
  }

  // Account Balances operations
  async getAccountBalances(patientId?: string): Promise<AccountBalance[]> {
    try {
      let url = `${this.baseURL}/account-balances`;
      if (patientId) {
        url += `?patient_id=${patientId}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Account balances API not available (${response.status}), using mock data`);
        return this.getMockAccountBalances(patientId);
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Account balances API error, using mock data:', error);
      return this.getMockAccountBalances(patientId);
    }
  }

  // Payment Methods operations
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await fetch(`${this.baseURL}/methods`);
      if (!response.ok) {
        throw new Error(`Failed to fetch payment methods: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return this.getMockPaymentMethods();
    }
  }

  // Payment Transactions operations
  async getPaymentTransactions(patientId?: string, dateRange?: { start: string; end: string }): Promise<PaymentTransaction[]> {
    try {
      let url = `${this.baseURL}/transactions`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch payment transactions: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching payment transactions:', error);
      return this.getMockPaymentTransactions(patientId);
    }
  }

  // Payment Analytics operations
  async getPaymentAnalytics(dateRange?: { start: string; end: string }): Promise<PaymentAnalytics> {
    try {
      let url = `${this.baseURL}/analytics`;
      if (dateRange) {
        const params = new URLSearchParams({
          start: dateRange.start,
          end: dateRange.end,
        });
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch payment analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching payment analytics:', error);
      return this.getMockPaymentAnalytics();
    }
  }

  // Get comprehensive payment summary
  async getPaymentSummary(patientId?: string, dateRange?: { start: string; end: string }): Promise<PaymentSummary> {
    try {
      const [paymentCollections, accountBalances, paymentMethods, paymentTransactions, paymentAnalytics] = await Promise.all([
        this.getPaymentCollections(patientId, dateRange),
        this.getAccountBalances(patientId),
        this.getPaymentMethods(),
        this.getPaymentTransactions(patientId, dateRange),
        this.getPaymentAnalytics(dateRange),
      ]);

      return {
        reportDate: new Date().toISOString(),
        paymentCollections,
        accountBalances,
        paymentMethods,
        paymentTransactions,
        paymentAnalytics,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching payment summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockPaymentCollections(patientId?: string): PaymentCollection[] {
    const mockData: PaymentCollection[] = [
      {
        id: 1,
        date: '2024-01-20',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        payeur: 'Patient',
        payment_method: 'cash',
        amount_collected: 150,
        amount_consumed: 150,
        remaining_balance: 0,
        doctor_id: 'doc-1',
        doctor_name: 'Dr. Martin',
        reference: 'PAY-2024-001',
        status: 'completed',
        created_at: '2024-01-20T10:00:00Z',
      },
      {
        id: 2,
        date: '2024-01-21',
        patient_id: patientId || '2',
        patient_name: 'Marie Dubois',
        payeur: 'Insurance',
        payment_method: 'insurance',
        amount_collected: 300,
        amount_consumed: 250,
        remaining_balance: 50,
        doctor_id: 'doc-1',
        doctor_name: 'Dr. Martin',
        reference: 'INS-2024-002',
        status: 'completed',
        created_at: '2024-01-21T14:30:00Z',
      },
    ];

    return patientId ? mockData.filter(c => c.patient_id === patientId) : mockData;
  }

  private getMockAccountBalances(patientId?: string): AccountBalance[] {
    const mockData: AccountBalance[] = [
      {
        id: 1,
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        total_due: 500,
        total_collected: 350,
        remaining_balance: 150,
        last_payment_date: '2024-01-20',
        last_payment_amount: 150,
        payment_history: [
          { id: 1, date: '2024-01-20', amount: 150, method: 'cash', reference: 'PAY-001' },
          { id: 2, date: '2024-01-15', amount: 200, method: 'card', reference: 'PAY-002' },
        ],
        account_status: 'current',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(b => b.patient_id === patientId) : mockData;
  }

  private getMockPaymentMethods(): PaymentMethod[] {
    return [
      { id: 'cash', name: 'Espèce', type: 'cash', is_active: true, requires_reference: false },
      { id: 'check', name: 'Chèque', type: 'check', is_active: true, requires_reference: true },
      { id: 'card', name: 'Carte bancaire', type: 'card', is_active: true, requires_reference: false, processing_fee: 2.5 },
      { id: 'transfer', name: 'Virement', type: 'transfer', is_active: true, requires_reference: true },
      { id: 'insurance', name: 'Assurance', type: 'insurance', is_active: true, requires_reference: true },
    ];
  }

  private getMockPaymentTransactions(patientId?: string): PaymentTransaction[] {
    const mockData: PaymentTransaction[] = [
      {
        id: 1,
        transaction_date: '2024-01-20',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        amount: 150,
        payment_method: 'cash',
        description: 'Consultation générale',
        type: 'payment',
        status: 'completed',
        processed_by: 'Dr. Martin',
        created_at: '2024-01-20T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(t => t.patient_id === patientId) : mockData;
  }

  private getMockPaymentAnalytics(): PaymentAnalytics {
    return {
      id: 1,
      report_date: '2024-01-20',
      total_collections: 15000,
      total_outstanding: 5000,
      payment_methods_breakdown: [
        { method: 'cash', count: 45, total_amount: 6750, percentage: 45 },
        { method: 'card', count: 30, total_amount: 4500, percentage: 30 },
        { method: 'insurance', count: 20, total_amount: 3000, percentage: 20 },
        { method: 'check', count: 5, total_amount: 750, percentage: 5 },
      ],
      daily_collections: [
        { date: '2024-01-20', total_amount: 2500, transaction_count: 15, average_amount: 166.67 },
        { date: '2024-01-19', total_amount: 3200, transaction_count: 18, average_amount: 177.78 },
      ],
      top_paying_patients: [
        { patient_id: '1', patient_name: 'Jean Dupont', total_paid: 1200, payment_count: 8, last_payment_date: '2024-01-20' },
        { patient_id: '2', patient_name: 'Marie Dubois', total_paid: 950, payment_count: 6, last_payment_date: '2024-01-19' },
      ],
      overdue_accounts: [
        { patient_id: '3', patient_name: 'Pierre Martin', overdue_amount: 300, days_overdue: 15, last_contact_date: '2024-01-05' },
      ],
      collection_rate: 85.5,
      average_payment_time: 12.5,
    };
  }
}

export const paymentService = new PaymentService();
export default paymentService;
