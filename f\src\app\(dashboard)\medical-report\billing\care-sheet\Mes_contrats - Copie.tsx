<div class="md-whiteframe-z1 mn-module flex layout-column ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <span flex="" class="flex"></span>
        <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-label="new contract" ng-click="vm.addClick('SUBSCRIPTION')" mn-acl="{resource: 'contract', action: 'create'}">
            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
            <span translate-once="add_subscription" class="ng-scope">Abonnement</span>
        </button>
        <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-label="new contract" ng-click="vm.addClick('LOCATION')" mn-acl="{resource: 'contract', action: 'create'}">
            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
            <span translate-once="add_renting" class="ng-scope">Location</span>
        </button>

    </div>
</md-toolbar>
<md-content class="mn-module-body flex layout-column ng-scope _md" cg-busy="vm.promise">
    <!-- ngIf: vm.contractDefaultView == 'contract_default_list' --><mn-table ng-if="vm.contractDefaultView == 'contract_default_list'" mn-model="billing.Contract" actions="vm.actions" selectable="true" export-helpers="vm.exportHelpers" mn-query="vm.filter" order="-id" reset="vm.reset" class="ng-scope ng-isolate-scope">
    <md-sidenav class="mn-table-side-nav md-sidenav-left md-closed flex-nogrow layout-column ng-isolate-scope _md" md-component-id="module-side-nav" md-is-locked-open="vm.toggle" tabindex="-1">
        <mn-table-side-nav flex="" mn-model="vm.mnModel" layout="column" columns="vm.columns" mn-filters="vm.filters" query="vm.query" reload-handler="vm.getData" style-rules="vm.styleRules" draft-rule="vm.draftRule" class="ng-isolate-scope layout-column flex"><md-tabs flex="" md-border-bottom="" cg-busy="vm.promise" class="ng-isolate-scope flex"><md-tabs-wrapper> <md-tab-data>
      <md-tab md-on-select="vm.formatTab(true)" class="ng-scope ng-isolate-scope">
      
      
  </md-tab>

  <md-tab md-on-select="vm.formatTab(false)" class="ng-scope ng-isolate-scope">
      
      
  </md-tab>
</md-tab-data> <!-- ngIf: $mdTabsCtrl.shouldPaginate --><md-prev-button tabindex="-1" role="button" aria-label="Previous Page" aria-disabled="true" ng-class="{ 'md-disabled': !$mdTabsCtrl.canPageBack() }" ng-if="$mdTabsCtrl.shouldPaginate" ng-click="$mdTabsCtrl.previousPage()" class="ng-scope md-disabled" style=""> <md-icon md-svg-src="data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiPjxnPjxwb2x5Z29uIHBvaW50cz0iMTUuNCw3LjQgMTQsNiA4LDEyIDE0LDE4IDE1LjQsMTYuNiAxMC44LDEyICIvPjwvZz48L3N2Zz4=" role="img" aria-hidden="true"><svg version="1.1" x="0px" y="0px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fit="" height="100%" width="100%" preserveAspectRatio="xMidYMid meet" focusable="false"><g><polygon points="15.4,7.4 14,6 8,12 14,18 15.4,16.6 10.8,12 "></polygon></g></svg></md-icon> </md-prev-button><!-- end ngIf: $mdTabsCtrl.shouldPaginate --> <!-- ngIf: $mdTabsCtrl.shouldPaginate --><md-next-button tabindex="-1" role="button" aria-label="Next Page" aria-disabled="false" ng-class="{ 'md-disabled': !$mdTabsCtrl.canPageForward() }" ng-if="$mdTabsCtrl.shouldPaginate" ng-click="$mdTabsCtrl.nextPage()" class="ng-scope" style=""> <md-icon md-svg-src="data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiPjxnPjxwb2x5Z29uIHBvaW50cz0iMTUuNCw3LjQgMTQsNiA4LDEyIDE0LDE4IDE1LjQsMTYuNiAxMC44LDEyICIvPjwvZz48L3N2Zz4=" role="img" aria-hidden="true"><svg version="1.1" x="0px" y="0px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fit="" height="100%" width="100%" preserveAspectRatio="xMidYMid meet" focusable="false"><g><polygon points="15.4,7.4 14,6 8,12 14,18 15.4,16.6 10.8,12 "></polygon></g></svg></md-icon> </md-next-button><!-- end ngIf: $mdTabsCtrl.shouldPaginate --> <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)" class="md-paginated" style=""> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab  md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-716" md-tab-id="716" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-716">
          <span translate-once="custom_filter" class="ng-scope">Filtre avancé</span>
      </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-717" md-tab-id="717" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-717">
          <span translate-once="style_rules" class="ng-scope">Règles de mise en forme</span>
      </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar style="left: 0px; right: 173px;"></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
          <span translate-once="custom_filter" class="ng-scope">Filtre avancé</span>
      </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
          <span translate-once="style_rules" class="ng-scope">Règles de mise en forme</span>
      </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-716" class="_md ng-scope md-no-transition md-active" role="tabpanel" aria-labelledby="tab-item-716" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope">
      <md-content flex="" layout="column" class="ng-scope _md layout-column flex">
           <mn-table-custom-filter flex="" mn-model="vm.mnModel" layout="column" columns="vm.columns" mn-filters="vm.mnFilters" query="vm.query" reload-handler="vm.reloadHandler" class="ng-isolate-scope layout-column flex"><div class="mn-filters layout-row flex" flex="" layout="row">
    <div class="filter-items md-whiteframe-z1 flex layout-column">
        <div class="filter-select flex-none layout-row layout-align-center-center">
            <md-select ng-model="vm.currentFilter" aria-label="current filter" placeholder="Aucun filtre Enregistré" ng-disabled="!vm.mnFilters || vm.mnFilters.length==0" ng-change="vm.handleFilterChange()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" role="button" id="select_728" aria-disabled="true" disabled="disabled" aria-invalid="false"><md-select-value class="md-select-value md-select-placeholder" id="select_value_label_727" aria-hidden="true"><span>Aucun filtre Enregistré</span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_729">  <md-select-menu role="presentation" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_730">
                <!-- ngRepeat: filter in vm.mnFilters track by filter.id -->
            </md-content></md-select-menu></div></md-select>
        </div>
        <div class="filter-buttons flex-none layout-row">
            <span flex="" class="flex"></span>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="!vm.checkFiltersExist()||!vm.currentFilter['id']" aria-label="save" ng-click="vm.submit(false)" disabled="disabled">
                <md-icon md-font-icon="mdi-find-replace" md-font-set="mdi" class="ng-scope md-font mdi mdi-find-replace" role="img" aria-hidden="true"></md-icon>
                
            </button>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" aria-label="save" ng-click="vm.submit()" ng-disabled="!vm.checkFiltersExist()" disabled="disabled">
                <md-icon md-font-icon="mdi-magnify-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-magnify-plus" role="img" aria-hidden="true"></md-icon>
                
            </button>
            <button class="md-icon-button md-warn md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="!vm.filterApplied" ng-click="vm.clearFilter()" aria-label="clear filter" disabled="disabled">
                <md-icon md-font-icon="mdi-filter-remove-outline" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-remove-outline" role="img" aria-hidden="true"></md-icon>
            </button>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" aria-label="save" ng-click="vm.applyFilter(); $mdMenu.open($event)" ng-disabled="!vm.checkFiltersExist()" disabled="disabled">
                <md-icon md-font-icon="mdi-filter-outline" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-outline" role="img" aria-hidden="true"></md-icon>
            </button>
        </div>
        <md-content flex="" class="_md flex">
            <md-list flex="" role="list" class="flex">
                <!-- ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="N°.Contract" class="ng-scope">N°.Contract</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="N°.dossier" class="ng-scope">N°.dossier</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Date d'adhésion" class="ng-scope">Date d'adhésion</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Date" class="ng-scope">Date</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Démarre le" class="ng-scope">Démarre le</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Adressé par" class="ng-scope">Adressé par</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Date Fin" class="ng-scope">Date Fin</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Nom" class="ng-scope">Nom</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Prénom" class="ng-scope">Prénom</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Assurance" class="ng-scope">Assurance</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Affectation" class="ng-scope">Affectation</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Type" class="ng-scope">Type</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Ville" class="ng-scope">Ville</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Technicien" class="ng-scope">Technicien</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Montant Total" class="ng-scope">Montant Total</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Validation" class="ng-scope">Validation</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index -->
            </md-list>
        </md-content>
    </div>
</div></mn-table-custom-filter>
      </md-content>
      </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-717" class="_md ng-scope md-no-transition md-right" role="tabpanel" aria-labelledby="tab-item-717" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper><div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"></div><div class="cg-busy ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-tabs>
</mn-table-side-nav>
    </md-sidenav>
    <div class="mn-module-side-content flex layout-column">
        <md-toolbar class="md-table-toolbar md-default flex-nogrow _md _md-toolbar-transitions">
            <div class="md-toolbar-tools">
                <button class="md-icon-button md-primary filter-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.handleToggle($event)" aria-label="advanced search" tabindex="-1">
                    <md-icon md-font-icon="mdi-filter-variant" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-variant" role="img" aria-hidden="true"></md-icon>
                    
                </button>

                <md-icon md-font-icon="mdi-magnify" md-font-set="mdi" class="md-font mdi mdi-magnify" role="img" aria-label="mdi-magnify"></md-icon>
                <div flex="50" layout="row" class="global-search layout-row flex-50">
                    <input ng-model="vm.query.search_all" translate-once-placeholder="search" ng-change="vm.getData()" mn-auto-focus="vm.focus" class="ng-pristine ng-valid ng-empty ng-touched" autocomplete="off" aria-invalid="false" placeholder="Rechercher" style="">
                </div>
                <span flex="" class="flex"></span>
                <!-- ngRepeat: action in ::vm.actions['multiple'] track by $index --><button class="md-icon-button md-button ng-scope md-ink-ripple ng-hide" type="button" ng-transclude="" ng-show="(vm.selected.length > 0) || action['isStatic']" ng-repeat="action in ::vm.actions['multiple'] track by $index" ng-click="action.method(vm.selected ,action['isStatic'] ? vm.query :$event)" aria-label="action" tabindex="-1" mn-acl="{&quot;action&quot;: action.action, &quot;resource&quot;: action.resource, &quot;behavior&quot;: action.behavior}" aria-hidden="true">

                    <md-icon md-font-icon="mdi-receipt-text" md-font-set="mdi" class="ng-scope md-font mdi mdi-receipt-text" role="img" aria-hidden="true"></md-icon>
                    
                </button><!-- end ngRepeat: action in ::vm.actions['multiple'] track by $index --><!-- end ngRepeat: action in ::vm.actions['multiple'] track by $index --><!-- end ngRepeat: action in ::vm.actions['multiple'] track by $index --><!-- end ngRepeat: action in ::vm.actions['multiple'] track by $index --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-show="(vm.selected.length > 0) || action['isStatic']" ng-repeat="action in ::vm.actions['multiple'] track by $index" ng-click="action.method(vm.selected ,action['isStatic'] ? vm.query :$event)" aria-label="action" tabindex="-1" mn-acl="{&quot;action&quot;: action.action, &quot;resource&quot;: action.resource, &quot;behavior&quot;: action.behavior}" aria-hidden="false">

                    <md-icon md-font-icon="mdi-view-headline" md-font-set="mdi" class="ng-scope md-font mdi mdi-view-headline" role="img" aria-hidden="true"></md-icon>
                    
                </button><!-- end ngRepeat: action in ::vm.actions['multiple'] track by $index -->
                <!-- ngRepeat: action in vm.actions['dynamicMultiple'] track by action.label -->
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.getColumn(true)" aria-label="excel" tabindex="-1">
                    <md-icon md-font-icon="mdi-reload" md-font-set="mdi" class="ng-scope md-font mdi mdi-reload" role="img" aria-hidden="true"></md-icon>
                    
                </button>
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.exportExcel($event)" aria-label="excel" tabindex="-1">
                    <md-icon md-font-icon="mdi-file-excel" md-font-set="mdi" class="ng-scope md-font mdi mdi-file-excel" role="img" aria-hidden="true"></md-icon>
                    
                </button>
                <md-menu md-position-mode="target-right target" class="md-menu ng-scope _md">
                    <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="menu" ng-click="$mdMenu.open($event)" tabindex="-1" aria-haspopup="true" aria-expanded="false" aria-owns="menu_container_721">
                        <md-icon md-font-icon="mdi-dots-vertical" md-font-set="mdi" class="ng-scope md-font mdi mdi-dots-vertical" role="img" aria-hidden="true"></md-icon>
                    </button>
                    
                <div class="_md md-open-menu-container md-whiteframe-z2" id="menu_container_721" style="display: none;" aria-hidden="true"><md-menu-content width="3" class="mn-table-menu ng-isolate-scope" dragula="&quot;md-menu-one&quot;" dragula-model="vm.columns" role="menu">
                        <!-- ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="contract_number" class="ng-scope">N°.Contract</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="folder_number" class="ng-scope">N°.dossier</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi ng-hide mdi-check" role="img" aria-hidden="true"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="adhesion_date" class="ng-scope">Date d'adhésion</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi ng-hide mdi-check" role="img" aria-hidden="true"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="doc_date" class="ng-scope">Date</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="contract_start_at" class="ng-scope">Démarre le</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="referring_physician" class="ng-scope">Adressé par</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="end_date" class="ng-scope">Date Fin</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="patient_last_name" class="ng-scope">Nom</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="patient_first_name" class="ng-scope">Prénom</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="medical_insurance_organization" class="ng-scope">Assurance</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="contract_affiliation" class="ng-scope">Affectation</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="subscriber_type" class="ng-scope">Type</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="patient.city" class="ng-scope">Ville</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="contract_operator" class="ng-scope">Technicien</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi ng-hide mdi-check" role="img" aria-hidden="true"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="prev_bill_date" class="ng-scope">Dernière Facture</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi ng-hide mdi-check" role="img" aria-hidden="true"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="next_bill_period" class="ng-scope">Prochaine Facture</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="state" class="ng-scope">État</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="taxed_amount" class="ng-scope">Montant Total</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi ng-hide mdi-check" role="img" aria-hidden="true"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="is_valid_doc" class="ng-scope">Validation</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label -->
                    </md-menu-content></div></md-menu>
            </div>
        </md-toolbar>
        <md-table-container cg-busy="vm.tablePromise" class="flex-grow" style="position: relative;">
            <!-- ngIf: vm.columns --><table class="mn-striped ng-pristine ng-untouched ng-valid md-table ng-scope ng-isolate-scope ng-not-empty md-row-select" md-table="" md-progress="vm.promise" multiple="true" ng-if="vm.columns" md-row-select="true" ng-model="vm.selected" aria-invalid="false">
                <thead md-head="" md-order="vm.query.order" md-on-reorder="vm.onReorder" class="md-head ng-isolate-scope">
                <tr md-row="" class="md-row"><th class="md-column md-checkbox-column"><md-checkbox aria-label="Select All" ng-click="toggleAll()" ng-checked="allSelected()" ng-disabled="!getSelectableRows().length" tabindex="-1" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-scope" aria-disabled="true" aria-checked="false" disabled="disabled"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label"></div></md-checkbox></th>
                    <!-- ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="number">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="contract_number" class="ng-binding ng-scope" title="N°.Contract">
                            N°.Contract
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="folder_number">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="folder_number" class="ng-binding ng-scope" title="N°.dossier">
                            N°.dossier
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope date-column md-sort" md-order-by="start_at">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="contract_start_at" class="ng-binding ng-scope" title="Démarre le">
                            Démarre le
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="external_treating_physician.full_name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="referring_physician" class="ng-binding ng-scope" title="Adressé par">
                            Adressé par
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope date-column md-sort" md-order-by="current_instance.end_at">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="end_date" class="ng-binding ng-scope" title="Date Fin">
                            Date Fin
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="beneficiary.last_name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="patient_last_name" class="ng-binding ng-scope" title="Nom">
                            Nom
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="beneficiary.first_name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="patient_first_name" class="ng-binding ng-scope" title="Prénom">
                            Prénom
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="organization.name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="medical_insurance_organization" class="ng-binding ng-scope" title="Assurance">
                            Assurance
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="affiliation.name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="contract_affiliation" class="ng-binding ng-scope" title="Affectation">
                            Affectation
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="tariff.name">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="subscriber_type" class="ng-binding ng-scope" title="Type">
                            Type
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="patient.city" class="ng-binding ng-scope" title="Ville">
                            Ville
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    </th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="contract_operator" class="ng-binding ng-scope" title="Technicien">
                            Technicien
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    </th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope status-column">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="state" class="ng-binding ng-scope" title="État">
                            État
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    </th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope number-column md-numeric md-sort" md-order-by="taxed_amount"><md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon>

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="taxed_amount" class="ng-binding ng-scope" title="Montant Total">
                            Montant Total
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    </th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' -->

                    <!-- ngIf: ::(vm.actions['single'].length > 0) --><th md-column="" ng-if="::(vm.actions['single'].length > 0)" ng-class="'actions-column-' + vm.actions['single'].length" class="md-column ng-scope ng-isolate-scope actions-column-7"></th><!-- end ngIf: ::(vm.actions['single'].length > 0) -->
                </tr>
                </thead>
                <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="16">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide" style=""><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><tbody md-body="" class="search-tbody md-body">
                <tr md-row="" class="search-tr md-row"><td class="md-cell"></td>
                    <!-- ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope">
                        <!-- ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope md-numeric">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' -->
                    <!-- ngIf: ::(vm.actions['single'].length > 0) --><td md-cell="" ng-if="::(vm.actions['single'].length > 0)" class="action-cell md-cell ng-scope"></td><!-- end ngIf: ::(vm.actions['single'].length > 0) -->
                </tr>
                </tbody>
                <tbody md-body="" class="md-body">
                <!-- ngIf: !vm.rowCollection || vm.rowCollection.length == 0 --><tr class="no-element md-row ng-scope" md-row="" ng-if="!vm.rowCollection || vm.rowCollection.length == 0"><td class="md-cell"></td>
                    <td md-cell="" colspan="15" class="md-cell">
                        <span translate-once="no_element_to_show">Aucun élément trouvé.</span>
                    </td>
                    <td class="ng-hide"></td>
                </tr><!-- end ngIf: !vm.rowCollection || vm.rowCollection.length == 0 -->
                <!-- ngRepeat: row in vm.rowCollection track by row.id -->
                </tbody>
            </table><!-- end ngIf: vm.columns -->
        <div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"></div><div class="cg-busy ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-table-container>
        <md-table-pagination md-boundary-links="" md-page-select="" md-limit="vm.query.limit" md-page="vm.query.page" md-on-paginate="vm.onPaginate" md-total="0" class="flex-nogrow md-table-pagination ng-isolate-scope" md-limit-options="[5, 10, 15, 20]" md-label="{&quot;of&quot;:&quot;de&quot;,&quot;page&quot;:&quot;Page&quot;,&quot;rowsPerPage&quot;:&quot;Lignes par Page&quot;}"><!-- ngIf: $pagination.showPageSelect() --><div class="page-select ng-scope" ng-if="$pagination.showPageSelect()">
  <div class="label ng-binding">Page</div>

  <md-select virtual-page-select="" total="1" class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.page" md-container-class="md-pagination-select" ng-change="$pagination.onPaginationChange()" ng-disabled="$pagination.disabled" aria-label="Page" tabindex="0" role="button" aria-haspopup="listbox" id="select_736" aria-invalid="false" aria-labelledby="select_736 select_value_label_735"><md-select-value class="md-select-value" id="select_value_label_735"><span><div class="md-text ng-binding">1</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_737">  <md-select-menu role="presentation" class="_md">
    <md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_738">
      <!-- ngRepeat: page in $pageSelect.pages --><md-option ng-repeat="page in $pageSelect.pages" ng-value="page" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_743" value="1" selected="selected" aria-selected="true"><div class="md-text ng-binding">1</div></md-option><!-- end ngRepeat: page in $pageSelect.pages -->
    </md-content>
  </md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.showPageSelect() -->

<!-- ngIf: $pagination.limitOptions --><div class="limit-select ng-scope" ng-if="$pagination.limitOptions">
  <div class="label ng-binding">Lignes par Page</div>

  <md-select class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.limit" md-container-class="md-pagination-select" ng-disabled="$pagination.disabled" aria-label="Rows" placeholder="5" tabindex="0" role="button" aria-haspopup="listbox" id="select_740" aria-invalid="false" aria-labelledby="select_740 select_value_label_739"><md-select-value class="md-select-value" id="select_value_label_739"><span><div class="md-text ng-binding">15</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_741">  <md-select-menu role="presentation" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_742">
    <!-- ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_744" value="5"><div class="md-text ng-binding">5</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_745" value="10"><div class="md-text ng-binding">10</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_746" value="15" selected="selected" aria-selected="true"><div class="md-text ng-binding">15</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_747" value="20"><div class="md-text ng-binding">20</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions -->
  </md-content></md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.limitOptions -->

<div class="buttons">
  <div class="label ng-binding">0 - 0 de 0</div>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.first()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="First" disabled="disabled">
    <md-icon md-svg-icon="navigate-first.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M7 6 v12 h2 v-12 h-2z M17.41 7.41L16 6l-6 6 6 6 1.41-1.41L12.83 12z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.previous()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="Previous" disabled="disabled">
    <md-icon md-svg-icon="navigate-before.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg></md-icon>
  </button>

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.next()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Next" disabled="disabled">
    <md-icon md-svg-icon="navigate-next.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg></md-icon>
  </button>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.last()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Last" disabled="disabled">
    <md-icon md-svg-icon="navigate-last.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15 6 v12 h2 v-12 h-2z M8 6L6.59 7.41 11.17 12l-4.58 4.59L8 18l6-6z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->
</div></md-table-pagination>
    </div>
</mn-table><!-- end ngIf: vm.contractDefaultView == 'contract_default_list' -->
    <!-- ngIf: vm.contractDefaultView == 'flattened_list' -->

    <!-- ngIf: vm.contractDefaultView == 'flattened_list' -->
<div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-content>
</div>