'use client';

import {  <PERSON>ack, Paper, Group,Badge,Text,Button,Divider,} from '@mantine/core';
import { DataTable, useDataTableColumns, type DataTableSortStatus } from 'mantine-datatable'
import Icon from '@mdi/react';
import { mdiCheckboxMultipleMarked,mdiCheckboxMultipleBlankOutline, } from '@mdi/js';
import { sortBy } from 'lodash';
import { useEffect, useState } from 'react';
import { companies, type Company } from '@/data';
const PAGE_SIZE = 15;
export default function TableContent({isDental,hasMedicalCare}:{ isDental?: boolean;hasMedicalCare?: boolean;}) {
  const key = 'resize-complex-example';
  const [sortStatus, setSortStatus] = useState<DataTableSortStatus<Company>>({
    columnAccessor: 'Date',
    direction: 'asc',
  });
  const [selectedRecords, setSelectedRecords] = useState<Company[]>([]);
  useEffect(() => {
    const data = sortBy(companies, sortStatus.columnAccessor);
    setRecords(sortStatus.direction === 'desc' ? data.reverse() : data);
  }, [sortStatus]);
  const [withTableBorder, ] = useState(true);
  const [withColumnBorders, ] = useState(true);
  const props = {
    resizable: true,
    sortable: true,
    toggleable: true,
    draggable: true,
  };
  const { effectiveColumns,  } = useDataTableColumns<Company>({
    key,
    columns: [
        {
        accessor: 'Date',
        title: "Date", 
        render: () => <span>29/06/2025</span>,
        ...props,
        },
     {
        accessor: 'Type',
        render: () => <span>visit</span>,
        ...props,
        },
      {
        accessor: 'Bénéficiaire',
        ellipsis: true,
        title: "Bénéficiaire", 
        render: () => <span>ACHRAF KARIM</span>,
        ...props,
        },
         {
        accessor: 'Montant dû',
        textAlign: 'right',
        title: "Montant dû", 
        render: () => <span>800.00</span>,
        ...props,
        },
       {
        accessor: 'Montant encaissé',
        title: "Montant encaissé", 
        render: () => <span> 0.00</span>,
        ...props,
        },
        {
        accessor: 'Avancement',
        title: "Avancement", 
        render: () => <span>100</span>,
        ...props,
        },
       {
        accessor: 'État',
        title: "État", 
        render: () => <span>unpaid</span>,
        ...props,
        },
        {
        accessor: 'Reste à régler',
        title: "Reste à régler", 
        textAlign: 'right',
        render: () => <span>800.00</span>,
        ...props,
        },
         {
        accessor: 'Remise',
        title: "Remise", 
        textAlign: 'right',
        render: () => <span>800.00</span>,
        ...props,
        },


     
    ],
  });
const [page, setPage] = useState(1);

const [records, setRecords] = useState(() =>
  sortBy(companies, 'Date').slice(0, PAGE_SIZE)
);
  useEffect(() => {
    const from = (page - 1) * PAGE_SIZE;
    const to = from + PAGE_SIZE;
    setRecords(companies.slice(from, to));
  }, [page]);

   const [payables, setPayables] = useState<Company[]>(companies);
  const handleSelectAll = () => {
    setPayables(prev => prev.map(item => ({ ...item, selected: true })));
  };
   const selectedCount = payables.filter(item => item.selected).length;
     const handleDeselectAll = () => {
    setPayables(prev => prev.map(item => ({ ...item, selected: false })));
  };
  return (
    <Stack>
      <DataTable
        withTableBorder={withTableBorder}
        withColumnBorders={withColumnBorders}
        selectedRecords={selectedRecords}
        onSelectedRecordsChange={setSelectedRecords}
        storeColumnsKey={key}
        records={records}
        columns={effectiveColumns}
        sortStatus={sortStatus}
        onSortStatusChange={setSortStatus}
        totalRecords={companies.length}
      recordsPerPage={PAGE_SIZE}
      page={page}
      onPageChange={(p) => setPage(p)}
      />

            <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
                          <Group justify="space-between" align="center">
                            {/* Boutons de sélection */}
                            <Group gap="md">
                              <Button
                                leftSection={<Icon path={mdiCheckboxMultipleMarked} size={0.8} />}
                                variant="outline"
                                size="sm"
                                onClick={handleSelectAll}
                                disabled={selectedCount === payables.length}
                              >
                                Tout sélectionner
                              </Button>
              
                              <Button
                                leftSection={<Icon path={mdiCheckboxMultipleBlankOutline} size={0.8} />}
                                variant="outline"
                                size="sm"
                                onClick={handleDeselectAll}
                                disabled={selectedCount === 0}
                              >
                                Tout désélectionner
                              </Button>
                            </Group>
              
                            {/* Légende des statuts */}
                           
                                 <ul className="flex flex-wrap gap-x-4 gap-y-2 text-[var(--mantine-color-dark-0)]">
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#4CAF50"></Badge>Reglé(e)
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#FBC02D"></Badge>Reglé(e) Partialemen
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#E53935"></Badge>Non Reglé(e)
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#263238"></Badge>Dispensé(e)/Clôturé(e)
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#4527A0"></Badge>Visite simple
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#26A69A"></Badge>Plan de traitement
                                  </li>
                                   <li className={"flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80font-bold "}>   
                                    <Badge size="xs" circle color="#CDDC39"></Badge>Plan de soins
                                  </li>
                                 </ul>
                            

                          </Group>
                           
                        </Paper>    
              
    </Stack>
  );
}