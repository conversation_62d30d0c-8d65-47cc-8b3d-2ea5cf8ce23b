"use client";
import { createContext, useContext, useState, useEffect, type ReactNode } from "react";
import settingsService from "~/services/settingsService";
import { applyFontSizeSettings } from "~/utils/fontSizeUtils";

interface FontSizeContextType {
  fontSize: number;
  setFontSize: (size: number) => void;
  saveFontSize: (size: number) => Promise<void>;
}

const FontSizeContext = createContext<FontSizeContextType | undefined>(
  undefined,
);

export const FontSizeProvider = ({ children }: { children: ReactNode }) => {
  const [fontSize, setFontSize] = useState<number>(16);
  const [initialized, setInitialized] = useState<boolean>(false);

  // Load font size from settings on mount
  useEffect(() => {
    const loadFontSize = async () => {
      try {
        const settings = await settingsService.getUserSettings();
        if (settings && settings.accessibilitySettings && settings.accessibilitySettings.fontSize) {
          setFontSize(settings.accessibilitySettings.fontSize);
        }
        setInitialized(true);
      } catch (error) {
        console.error("Error loading font size from settings:", error);
        setInitialized(true);
      }
    };

    loadFontSize();
  }, []);

  // Update document font size when fontSize changes
  useEffect(() => {
    if (initialized && typeof window !== 'undefined') {
      // Apply font size settings using the shared utility
      applyFontSizeSettings(fontSize);

      // Save font size to localStorage for immediate use
      localStorage.setItem('fontSizePreference', fontSize.toString());
    }
  }, [fontSize, initialized]);

  // Function to save font size to backend
  const saveFontSize = async (size: number) => {
    try {
      // Get current settings
      const settings = await settingsService.getUserSettings();

      // Update font size
      settings.accessibilitySettings.fontSize = size;

      // Save settings
      await settingsService.saveUserSettings(settings);

      console.log("Font size saved to settings:", size);
    } catch (error) {
      console.error("Error saving font size to settings:", error);
    }
  };

  return (
    <FontSizeContext.Provider value={{ fontSize, setFontSize, saveFontSize }}>
      {children}
    </FontSizeContext.Provider>
  );
};

export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  if (context === undefined) {
    // throw new Error("useFontSize doit être utilisé dans un FontSizeProvider");
    // useFontSize must be used within a FontSizeProvider
    throw new Error("useFontSize must be used within a FontSizeProvider");
  }
  return context;
};
