import api from '@/lib/api';
import { ApiError } from './authService';

export interface License {
  id: number;
  license_number: string;
  formatted_license_number: string;
  status: 'pending' | 'active' | 'expired' | 'revoked';
  issue_date: string;
  expiry_date: string;
  days_until_expiry: number;
  days_remaining: number;
  initial_days: number;
  is_active: boolean;
  activation_date: string | null;
  is_renewed: boolean;
  transaction_id?: string;
  payment_method?: string;
  payment_status?: string;
  payment_date?: string;
  payment_amount?: string;
  payment_reference?: string;
  payment_bank?: string;
  payment_account?: string;
}

export interface LicenseActivationData {
  license_number: string;
  activation_code: string;
}

export interface LicenseRenewalData {
  license_number: string;
  duration_days?: number;
}

export interface UpdateTransactionIDData {
  license_number: string;
  transaction_id: string;
}

export interface LicenseActivationResponse {
  success: boolean;
  message: string;
  license?: License;
}

export interface LicenseRenewalResponse {
  success: boolean;
  message: string;
  license?: License;
}

export interface EmailResponse {
  success: boolean;
  message: string;
}

export interface TransactionUpdateResponse {
  success: boolean;
  message: string;
  license?: License;
}

const licenseService = {
  /**
   * Get the current user's license information
   */
  getLicense: async (): Promise<License> => {
    const response = await api.get('/api/auth/license/');
    return response.data;
  },

  /**
   * Activate a license with an activation code
   */
  activateLicense: async (data: LicenseActivationData): Promise<LicenseActivationResponse> => {
    const response = await api.post('/api/auth/license/activate/', data);
    return response.data;
  },

  /**
   * Renew a license
   */
  renewLicense: async (data: LicenseRenewalData): Promise<LicenseRenewalResponse> => {
    const response = await api.post('/api/auth/license/renew/', data);
    return response.data;
  },

  /**
   * Check if the current user has an active license
   */
  checkLicenseStatus: async (): Promise<{
    hasLicense: boolean;
    isActive: boolean;
    status?: string;
    daysRemaining?: number;
  }> => {
    try {
      const license = await licenseService.getLicense();
      return {
        hasLicense: true,
        isActive: license.is_active,
        status: license.status,
        daysRemaining: license.days_remaining || license.days_until_expiry
      };
    } catch (error) {
      const apiError = error as ApiError;
      if (apiError.response?.status === 404) {
        // No license found
        return {
          hasLicense: false,
          isActive: false
        };
      }
      throw apiError;
    }
  },

  /**
   * Get the current user's license information (alias for getLicense)
   */
  getCurrentLicense: async (): Promise<License> => {
    return licenseService.getLicense();
  },

  /**
   * Send license details to the doctor's email
   */
  sendLicenseToEmail: async (licenseNumber: string): Promise<EmailResponse> => {
    const response = await api.post(`/api/auth/license/${licenseNumber}/send-email/`);
    return response.data;
  },

  /**
   * Update the transaction ID of a license
   */
  updateTransactionID: async (data: UpdateTransactionIDData): Promise<TransactionUpdateResponse> => {
    const response = await api.post('/api/auth/license/update-transaction/', data);
    return response.data;
  }
};

export default licenseService;
