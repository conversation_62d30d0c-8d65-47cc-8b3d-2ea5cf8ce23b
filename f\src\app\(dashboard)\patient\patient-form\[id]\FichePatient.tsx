
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AvatarWithUpload from '@/components/patient/AvatarWithUpload';
import Link from 'next/link';
import dayjs from 'dayjs';
import { extractErrorMessage, logError } from '@/utils/errorUtils';
import Icon from '@mdi/react';
import { mdiPlaylistPlus,mdiPlus,mdiRefresh,mdiShare,mdiCardAccountDetails,mdiArrowLeft,mdiApps,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
  mdiHistory,
  mdiMicrophone,
  mdiClipboardText,
  mdiDeleteSweep,mdiChevronRight, mdiChevronDown, mdiViewHeadline, mdiArrowRight,
 mdiCalendarPlus ,mdiBarcode,mdiSkipPrevious,mdiSkipNext,mdiAccountAlert,mdiCircle,mdiPencil,mdiDelete,mdiAccountSearch,
 mdiCheck, mdiAlertCircle,
} from '@mdi/js';
import { useAuth } from '@/hooks/useAuth';

import { DictionaryModalsManager } from '@/components/alerte';
import RelationForm from "./RelationForm"
import patientService, { PatientAlert as ServicePatientAlert } from '@/services/patientService';
import { patientFormService, PatientFormData } from '@/services/patientFormService';
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}





// Interface for patient update data
interface PatientUpdateData {
  [key: string]: string | number | boolean | null | undefined;
}
import {
  Text,
  Container,
  Title,
  ActionIcon,
  Button,
  Group,
  Card,
 Modal,
  Tabs,
  TextInput,
  Textarea,
  Select,
  Grid,
  Paper,
  Stack,
  Divider,
  Badge,
  Loader,
  Alert,
 Switch,
Tooltip,
  Box,
  Input,
  ScrollArea,
   Table,
  Radio,
  MultiSelect,Checkbox,Menu,
} from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconUser,
  IconAddressBook,
  IconPhone,
  IconMedicalCross,
  IconCalendar,
  IconCheck,
  IconAlertCircle,
  IconEdit,
} from '@tabler/icons-react';

import SimpleBar from "simplebar-react";
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
//import { Margarine } from 'next/font/google';
// import authService from '@/services/authService';
interface FichePatientProps {
  patient?: PatientFormData; // Patient data passed from parent component
  onInsuredChange?: (isInsured: boolean) => void;
  // Additional props for staff and trigger options
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  openListDesPatient?: () => void;
}
interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  landline_number: string;
  date_of_birth: string | null;
  gender: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  medical_conditions: string;
  allergies: string;
  medications: string;
  blood_type: string;
  height: string;
  weight: string;
  profile_image?: string;
  created_at: string;
  updated_at: string;
  file_number?: number,
  category?: string,
  pricing?: number,
  is_bookmarked?: boolean,
  insured?:boolean,
  description?:string,
  titre?: string;
  nationality: string;
    spoken_languages: string;
    cine: string;
    profession: string;
    attending_physician: string;
}

interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}
type PatientActionsProps = {
  patientId?: string;
    isFormInvalid: boolean;
    isDraft: boolean;
    onPrint?: () => void;
    onPrevious?: () => void;
    onNext?: () => void;
    onStartVisit?: () => void;
    onAppointment?: () => void;
    onCancel?: () => void;
    onSaveQuitNew?: () => void;
    onSaveQuit?: () => void;
   
    onGoBack: () => void;
    onAddMeasurement: () => void;
    onGoToContract: () => void;
    selectedInsurance: string;
    setSelectedInsurance: (value: string) => void;
    affiliateNumber: string;
    setAffiliateNumber: (value: string) => void;
    affiliateType: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT';
    setAffiliateType: (value: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT') => void;
    organizationOptions: { value: string; label: string }[];
     value: string | null;
    onChang: (val: string | null) => void;
    onAdd?: () => void;
    locked?: boolean;
     countryId: number | null;
    provinceId: number | null;
    values: Location | null;
    onChange: (city: Location | null) => void;
    disabled?: boolean;
      onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName?: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];
  openListDesPatient: () => void;
};
export default function FichePatient({
   patientId,
  patient, // Add patient prop
  isFormInvalid,
 isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  onInsuredChange,
  onGoBack,
  openListDesPatient,
  fullName = 'ABDESSALMAD AGADIR',
  staffOptions = [
    { label: 'TEST DEMO', value: 'test-demo' },
    { label: 'DEMO DEMO', value: 'demo-demo' }
  ],
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],

  }: PatientActionsProps & FichePatientProps) {
  const router = useRouter();

  // Data options for select fields
  const nationalityOptions = [
    { label: 'Marocaine', value: 'Marocaine' },
    { label: 'Française', value: 'Française' },
    { label: 'Espagnole', value: 'Espagnole' },
    { label: 'Italienne', value: 'Italienne' },
    { label: 'Allemande', value: 'Allemande' },
    { label: 'Britannique', value: 'Britannique' },
    { label: 'Américaine', value: 'Américaine' },
    { label: 'Canadienne', value: 'Canadienne' },
    { label: 'Autre', value: 'Autre' },
  ];

  const languageOptions = [
    { label: 'Arabe', value: 'Arabe' },
    { label: 'Français', value: 'Français' },
    { label: 'Anglais', value: 'Anglais' },
    { label: 'Espagnol', value: 'Espagnol' },
    { label: 'Italien', value: 'Italien' },
    { label: 'Allemand', value: 'Allemand' },
    { label: 'Berbère', value: 'Berbère' },
    { label: 'Autre', value: 'Autre' },
  ];

  const pricingOptions = [
    { label: '0', value: '0' },
    { label: '50', value: '50' },
    { label: '100', value: '100' },
    { label: '150', value: '150' },
    { label: '200', value: '200' },
    { label: '250', value: '250' },
    { label: '300', value: '300' },
    { label: 'Autre', value: 'autre' },
  ];

  // start header
  
   const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
   const [isRelationsModalOpen, setIsRelationsModalOpen] = useState(false);
   const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
   const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
    const [isSidebarVisible, setIsSidebarVisible] = useState(false);
    const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
    const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);
    // État pour gérer l'effondrement de chaque nœud
    // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
    const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
      console.log('TreeItemChoixMultiple initialized with all nodes open');
      return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
    });
  
    // État pour gérer les sélections multiples
    const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
  
    // États pour la gestion des modèles
    const [showModels, setShowModels] = useState(false);
    const [showAddModel, setShowAddModel] = useState(false);
    const [modelTitle, setModelTitle] = useState('');
    const [editingModelId, setEditingModelId] = useState<string | null>(null);
    const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
  
    // États pour la reconnaissance vocale
    const [isListening, setIsListening] = useState(false);
    const [validSpeech, setValidSpeech] = useState('');
    const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
    const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
    const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut
  
    // Initialiser la reconnaissance vocale au montage du composant
    useEffect(() => {
      initSpeechRecognition();
    }, []);
  
    // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
    const toggleNodeCollapse = (nodeId: string) => {
      setCollapsedNodes(prev => {
        const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
        // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
        if (currentState === true) { // Si fermé, ouvrir
          console.log('Opening TreeItemChoixMultiple node:', nodeId);
          return {
            ...prev,
            [nodeId]: false // false = ouvert
          };
        } else {
          console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
          return prev; // Ne rien changer si déjà ouvert
        }
      });
    };
    interface TreeNodeChoixMultiple {
      uid: string;
      value: string;
      nodes?: TreeNodeChoixMultiple[];
    }
    function TreeItemChoixMultiple({
      node,
      collapsedNodes,
      toggleNodeCollapse,
      selectedNodes,
      toggleNodeSelection,
    }: {
      node: TreeNodeChoixMultiple;
      collapsedNodes: Record<string, boolean>;
      toggleNodeCollapse: (nodeId: string) => void;
      selectedNodes: Set<string>;
      toggleNodeSelection: (nodeId: string) => void;
    }) {
      // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
      const isCollapsed = collapsedNodes[node.uid] ?? false;
      const isSelected = selectedNodes.has(node.uid);
      // Calculer l'état indéterminé pour les nœuds parents
      const getIndeterminateState = () => {
        if (!node.nodes || node.nodes.length === 0) return false;
        const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
        return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
      };
      const isIndeterminate = getIndeterminateState();
      return (
        <Stack pl="md" gap="xs">
          <Group gap="xs" align="center">
            {node.nodes && node.nodes.length > 0 && (
              <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
              </span>
            )}
            <Checkbox
              label={node.value}
              checked={isSelected}
              indeterminate={isIndeterminate}
              onChange={() => toggleNodeSelection(node.uid)}
              radius="xs"
            />
          </Group>
    
          {!isCollapsed &&
            node.nodes?.map((child) => (
              <TreeItemChoixMultiple
                key={child.uid}
                node={child}
                collapsedNodes={collapsedNodes}
                toggleNodeCollapse={toggleNodeCollapse}
                selectedNodes={selectedNodes}
                toggleNodeSelection={toggleNodeSelection}
              />
            ))}
        </Stack>
      );
    }
    
    interface AlertFormValues {
      trigger_for: string[];
      trigger: string;
      level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
      description: string;
      is_permanent: boolean;
    }
      // Fonction pour basculer la sélection d'un nœud
      const toggleNodeSelection = (nodeId: string) => {
        setSelectedNodes(prev => {
          const newSet = new Set(prev);
    
          // Trouver le nœud correspondant
          const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
            for (const node of nodes) {
              if (node.uid === id) return node;
              if (node.nodes) {
                const found = findNode(node.nodes, id);
                if (found) return found;
              }
            }
            return null;
          };
    
          const currentNode = findNode(exampleData, nodeId);
    
          if (newSet.has(nodeId)) {
            // Désélectionner le nœud et tous ses enfants
            newSet.delete(nodeId);
            if (currentNode?.nodes) {
              const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                nodes.forEach(child => {
                  newSet.delete(child.uid);
                  if (child.nodes) {
                    removeAllChildren(child.nodes);
                  }
                });
              };
              removeAllChildren(currentNode.nodes);
            }
          } else {
            // Sélectionner le nœud et tous ses enfants
            newSet.add(nodeId);
            if (currentNode?.nodes) {
              const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                nodes.forEach(child => {
                  newSet.add(child.uid);
                  if (child.nodes) {
                    addAllChildren(child.nodes);
                  }
                });
              };
              addAllChildren(currentNode.nodes);
            }
          }
    
          return newSet;
        });
      };
    
      // Fonction pour obtenir les sélections actuelles
      const getSelectedValues = () => {
        const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
          const result: TreeNodeChoixMultiple[] = [];
          nodes.forEach(node => {
            result.push(node);
            if (node.nodes) {
              result.push(...getAllNodes(node.nodes));
            }
          });
          return result;
        };
    
        const allNodes = getAllNodes(exampleData);
        return Array.from(selectedNodes)
          .map(id => allNodes.find(node => node.uid === id))
          .filter(Boolean)
          .map(node => node!.value);
      };

      // Fonctions pour sélectionner/désélectionner tous les nœuds
      const selectAllNodes = () => {
        const allNodeIds: string[] = [];

        const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
          nodes.forEach(node => {
            allNodeIds.push(node.uid);
            if (node.nodes && node.nodes.length > 0) {
              collectAllIds(node.nodes);
            }
          });
        };

        collectAllIds(exampleData);
        setSelectedNodes(new Set(allNodeIds));
      };

      const deselectAllNodes = () => {
        setSelectedNodes(new Set());
      };

      // Fonctions pour la reconnaissance vocale
      const initSpeechRecognition = () => {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
          const SpeechRecognitionConstructor = (window as unknown as {
            webkitSpeechRecognition: new () => SpeechRecognitionInstance;
            SpeechRecognition: new () => SpeechRecognitionInstance;
          }).webkitSpeechRecognition || (window as unknown as {
            webkitSpeechRecognition: new () => SpeechRecognitionInstance;
            SpeechRecognition: new () => SpeechRecognitionInstance;
          }).SpeechRecognition;
    
          const newRecognition = new SpeechRecognitionConstructor();
    
          newRecognition.continuous = true;
          newRecognition.interimResults = true;
          newRecognition.lang = 'fr-FR';
    
          newRecognition.onstart = () => {
            setIsListening(true);
            setMicrophoneColor('green'); // Changer la couleur en vert
            setInvalidSpeech('Écoute en cours...');
          };
    
          newRecognition.onresult = (event: unknown) => {
            const speechEvent = event as {
              resultIndex: number;
              results: {
                length: number;
                [index: number]: {
                  isFinal: boolean;
                  [index: number]: { transcript: string };
                };
              };
            };
    
            let finalTranscript = '';
            let interimTranscript = '';
    
            for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
              const transcript = speechEvent.results[i][0].transcript;
              if (speechEvent.results[i].isFinal) {
                finalTranscript += transcript;
              } else {
                interimTranscript += transcript;
              }
            }
    
            setValidSpeech(finalTranscript);
            setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
          };
    
          newRecognition.onerror = () => {
            setIsListening(false);
            setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
            setInvalidSpeech('Erreur de reconnaissance vocale');
          };
    
          newRecognition.onend = () => {
            setIsListening(false);
            setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
            setInvalidSpeech('Parlez maintenant.');
          };
    
          setRecognition(newRecognition);
        }
      };
    
      const toggleRecognition = () => {
        if (!recognition) {
          initSpeechRecognition();
          return;
        }
    
        if (isListening) {
          recognition.stop();
        } else {
          recognition.start();
        }
      };
    
      const emptyContent = () => {
        setValidSpeech('');
        setInvalidSpeech('Parlez maintenant.');
        setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
        if (recognition && isListening) {
          recognition.stop();
        }
      };
    
       const toggleSidebar = () => {
          setIsSidebarVisible(!isSidebarVisible);
        };
    
        // État pour savoir quelle alerte est en cours d'édition
        const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);

        // États pour le modal de confirmation de suppression
        const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
        const [alertToDelete, setAlertToDelete] = useState<string | null>(null);

        // Fonctions pour gérer les alertes
      

        const handleDeleteAlert = (alertId: string) => {
          console.log('Delete alert:', alertId);
          // Ouvrir le modal de confirmation
          setAlertToDelete(alertId);
          setIsDeleteConfirmModalOpen(true);
        };

        // Fonction pour confirmer la suppression
        const confirmDeleteAlert = () => {
          if (alertToDelete) {
            // Supprimer l'alerte de la liste
            setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
            console.log('Alert deleted:', alertToDelete);
          }
          // Fermer le modal et réinitialiser
          setIsDeleteConfirmModalOpen(false);
          setAlertToDelete(null);
        };

        // Fonction pour annuler la suppression
        const cancelDeleteAlert = () => {
          setIsDeleteConfirmModalOpen(false);
          setAlertToDelete(null);
        };

        // Fonction pour obtenir la couleur selon le niveau
        const getLevelColor = (level: string) => {
          switch(level) {
            case 'MINIMUM': return 'green';
            case 'MEDIUM': return 'orange';
            case 'HIGH': return 'red';
            default: return 'gray';
          }
        };



        // Fonction pour créer les actions d'une alerte
        const createAlertActions = (alertId: string) => (
          <Group gap="xs">
            <ActionIcon
              variant="subtle"
              color="blue"
              size="sm"
              onClick={() => {
                console.log('Edit alert clicked:', alertId);
                setCurrentEditingAlertId(alertId);

                // Trouver l'alerte à éditer et pré-remplir le formulaire
                const alertToEdit = alertsData.find(alert => alert.id === alertId);
                console.log('Alert to edit found:', alertToEdit);
                if (alertToEdit) {
                  // Trouver la valeur correspondante pour le trigger
                  const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';

                  console.log('Editing alert:', alertToEdit);
                  console.log('Trigger value found:', triggerValue);

                  form.setValues({
                    trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                    trigger: triggerValue,
                    level: alertToEdit.level,
                    description: alertToEdit.Description,
                    is_permanent: alertToEdit.is_permanent
                  });

                  console.log('Form values set for editing:', {
                    trigger_for: alertToEdit.trigger_for || [],
                    trigger: triggerValue,
                    level: alertToEdit.level,
                    description: alertToEdit.Description,
                    is_permanent: alertToEdit.is_permanent
                  });

                  console.log('Form values set:', form.values);
                }

                setIsAlertsAddModalOpen(true);
                setIsSidebarVisible(true); // Ouvrir aussi la sidebar pour les sélections
              }}
            >
              <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
            </ActionIcon>
            <ActionIcon
              variant="subtle"
              color="red"
              size="sm"
              onClick={() => {
                console.log('Delete alert clicked:', alertId);
                handleDeleteAlert(alertId);
              }}
            >
              <Icon path={mdiDelete} size={0.8} color={'red'}/>
            </ActionIcon>
          </Group>
        );

        // Fonction pour gérer la soumission du formulaire d'alerte
        const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
          console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
          console.log('Current editing alert ID:', currentEditingAlertId);

          if (currentEditingAlertId) {
            // Mode édition : mettre à jour l'alerte existante
            console.log('Editing existing alert:', currentEditingAlertId);

            const updatedAlertData = {
              id: currentEditingAlertId,
              level: values.level,
              is_permanent: values.is_permanent,
              Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
              Description: values.description,
              trigger_for: values.trigger_for
            };

            setAlertsData(prevData => {
              const updatedData = prevData.map(alert =>
                alert.id === currentEditingAlertId ? updatedAlertData : alert
              );
              console.log('Updated alerts data (edit mode):', updatedData);
              return updatedData;
            });
          } else {
            // Mode ajout : créer une nouvelle alerte
            console.log('Adding new alert');

            const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const newAlertData = {
              id: newAlertId,
              level: values.level,
              is_permanent: values.is_permanent,
              Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
              Description: values.description,
              trigger_for: values.trigger_for
            };

            setAlertsData(prevData => {
              const updatedData = [...prevData, newAlertData];
              console.log('Updated alerts data (add mode):', updatedData);
              return updatedData;
            });
          }

          // Appeler la fonction onSubmit originale si elle existe
          if (onSubmit) {
            onSubmit(values, autoTrigger);
          }

          // Fermer le modal et réinitialiser le formulaire
          setIsAlertsAddModalOpen(false);
          setIsSidebarVisible(false);
          setCurrentEditingAlertId(null);
          form.reset();
        };

        // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
        const [alertsData, setAlertsData] = useState<AlertData[]>([]);

        // Créer les éléments avec les actions pour le rendu
        const elements = alertsData.map(alert => ({
          ...alert,
          Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
          Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
          Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
          Actions: createAlertActions(alert.id)
        }));
    const rows = elements.map((element) => (
        <Table.Tr key={element.id}>
          <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
          <Table.Td>{element.Niveau}</Table.Td>
          <Table.Td>{element.Publique}</Table.Td>
          <Table.Td>{element.Permanente}</Table.Td>
          <Table.Td>{element.Description}</Table.Td>
          <Table.Td w={'100px'}>{element.Actions}</Table.Td>
        </Table.Tr>
      ));
       const form = useForm<AlertFormValues>({
        initialValues: {
          trigger_for: [],
          trigger: '',
          level: 'MINIMUM',
          description: '',
          is_permanent: false,
        },
        validate: {
          trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
          trigger: (value) => (!value ? 'Champ requis' : null),
          description: (value) => (!value ? 'Champ requis' : null),
        },
      });
      const [search, setSearch] = useState('');
    
     
    
      const handleValidate = () => {
        let textToAdd = '';
    
        if (showModels) {
          // Valider les modèles sélectionnés
          const selectedModelTexts = savedModels
            .filter(model => model.selected === true)
            .flatMap(model => model.selections);
          textToAdd = selectedModelTexts.join(', ');
          console.log('Selected models:', savedModels.filter(model => model.selected === true));
          console.log('Text to add from models:', textToAdd);
        } else {
          // Valider les sélections du dictionnaire
          const selectedValues = getSelectedValues();
          textToAdd = selectedValues.join(', ');
        }
    
        if (textToAdd) {
          // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
          setValidSpeech(textToAdd);
          setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"

          // 2. Ajouter le texte au champ description du formulaire
          const currentDescription = form.values.description || '';
          const newDescription = currentDescription
            ? `${currentDescription}, ${textToAdd}`
            : textToAdd;
          console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
          form.setFieldValue('description', newDescription);

          // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
          // Utiliser la nouvelle description mise à jour
          const combinedText = newDescription;
          console.log('Combined text for alert:', combinedText);

          if (currentEditingAlertId) {
            setAlertsData(prevData =>
              prevData.map(alert => {
                if (alert.id === currentEditingAlertId) {
                  return {
                    ...alert,
                    Description: combinedText
                  };
                }
                return alert;
              })
            );
          } else {
            // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
            setAlertsData(prevData =>
              prevData.map(alert => {
                if (alert.id === '1') {
                  return {
                    ...alert,
                    Description: combinedText
                  };
                }
                return alert;
              })
            );
          }
        }
    


        // Fermer le modal et réinitialiser
        setIsClipboardTextModalOpen(false);
        setIsChoixMultipleModalOpen(false);
        setShowModels(false);
        setSelectedNodes(new Set());
        // Réinitialiser l'ID de l'alerte en cours d'édition
        setCurrentEditingAlertId(null);
      };
    
      const handleCancel = () => {
        // Réinitialiser tous les états
        setSelectedNodes(new Set());
        setShowModels(false);
        setShowAddModel(false);
        setModelTitle('');
        setEditingModelId(null);
        setIsClipboardTextModalOpen(false);
      };
    
      // const handleAddModel = () => {
      //   if (selectedNodes.size > 0) {
      //     setShowAddModel(true);
      //   }
      // };
    
      const handleSaveModel = () => {
        if (modelTitle.trim()) {
          if (editingModelId) {
            // Mode édition : mettre à jour le modèle existant
            setSavedModels(prev => prev.map(model =>
              model.id === editingModelId
                ? { ...model, title: modelTitle.trim() }
                : model
            ));
            setEditingModelId(null);
            console.log('Model title updated for ID:', editingModelId);
          } else {
            // Mode création : créer un nouveau modèle
            const selectedValues = getSelectedValues();
            const newModel = {
              id: `model-${Date.now()}`,
              title: modelTitle.trim(),
              selections: selectedValues
            };
            setSavedModels(prev => [...prev, newModel]);
            setSelectedNodes(new Set());
            console.log('New model created:', newModel);
          }

          setModelTitle('');
          setShowAddModel(false);
          // Afficher les modèles après sauvegarde
          setShowModels(true);
        }
      };

      const handleEditModel = (modelId: string) => {
        const modelToEdit = savedModels.find(model => model.id === modelId);
        if (modelToEdit) {
          setModelTitle(modelToEdit.title);
          setEditingModelId(modelId);
          setShowModels(false);
          setShowAddModel(true);
          console.log('Editing model:', modelToEdit);
        }
      };
    
      const handleDeleteModel = (modelId: string) => {
        setSavedModels(prev => prev.filter(model => model.id !== modelId));
        setSelectedNodes(prev => {
          const newSet = new Set(prev);
          newSet.delete(modelId);
          return newSet;
        });
      };
    
      const exampleData: TreeNodeChoixMultiple[] = [
        {
          uid: '1',
          value: 'Alertes',
          nodes: [
            { uid: '1-1', value: 'Allaitante depuis:' },
            { uid: '1-2', value: 'Allergique à l\'Aspirine' },
    
            { uid: '1-3', value: 'Allergique à la Pénicilline' },
            { uid: '1-4', value: 'Arthrose' },
            { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
            { uid: '1-6', value: 'Diabétique NID' },
            { uid: '1-7', value: 'Enceinte depuis:' },
            { uid: '1-8', value: 'Diabétique ID' },
            { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
            { uid: '1-10', value: 'Hypertension' },
             { uid: '1-11', value: 'Hypotension' },
            { uid: '1-12', value: 'Thyroïde' },
      
    
          ],
        },
        
      ];
    //end header
  const [, setSubmitting] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
 
  const [patientAlerts, setPatientAlerts] = useState<ServicePatientAlert[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>('personal');
//    const [is_bookmarked, setis_bookmarked] = useState(false);
// const [insured, setInsured] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { refreshUser } = useAuth();
  const [isCategorieModalOpen, setIsCategorieModalOpen] = useState(false);
const [isNationalityModalOpen, setIsNationalityModalOpen] = useState(false);
const [isLanguagesSpokenModalOpen, setLanguagesSpokenModalOpen] = useState(false);
const [isProfessionModalOpen, setProfessionModalOpen] = useState(false);



// Interface et données pour l'arbre de la sidebar
interface TreeNode {
  value: string;
  children?: TreeNode[];
}

// Convert patient alerts to tree structure
const createAlertsTree = (alerts: ServicePatientAlert[]): TreeNode[] => {
  if (!alerts || alerts.length === 0) {
    return [
      {
        value: 'Alertes',
        children: [
          { value: "No alerts found for this patient" }
        ],
      },
    ];
  }

  return [
    {
      value: 'Alertes',
      children: alerts.map(alert => ({
        value: `${alert.trigger || alert.trigger_custom || 'Unknown Alert'} - ${alert.level || 'UNKNOWN'}`
      })),
    },
  ];
};

// Composant Tree pour la sidebar
function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
  // Initialiser tous les nœuds comme ouverts
  const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
    const initialExpanded: Record<string, boolean> = {};
    const expandAllNodes = (nodeList: TreeNode[]) => {
      nodeList.forEach(node => {
        if (node.children && node.children.length > 0) {
          initialExpanded[node.value] = true;
          expandAllNodes(node.children);
        }
      });
    };
    expandAllNodes(nodes);
    console.log('Tree initialized with expanded nodes:', initialExpanded);
    return initialExpanded;
  });

  return (
    <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
      {nodes.map((node, idx) => {
        const hasChildren = node.children && node.children.length > 0;
        const isOpen = expanded[node.value] || false;
        return (
          <li key={node.value + idx}>
            <Group gap="xs" align="center" onClick={() => {
              // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
              if (hasChildren && !isOpen) {
                console.log('Opening node:', node.value);
                setExpanded(prev => ({ ...prev, [node.value]: true }));
              } else if (hasChildren && isOpen) {
                console.log('Node already open, not closing:', node.value);
              }
            }} className="Alertesslidbar">
              {hasChildren ? (
                <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
              ) : null}
              <Text
                onClick={() => !hasChildren && onSelect(node.value)}
                style={{ cursor: 'pointer' ,paddingLeft:'10px'
                }}
              >
                {node.value}
              </Text>
            </Group>
            {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
          </li>
        );
      })}
    </ul>
  );
}

// Fonctions pour gérer la sidebar
const handleSidebarSelect = (value: string) => {
  console.log('Selected from sidebar:', value);

  // 1. Ajouter la sélection au formulaire en cours d'édition
  const currentDescription = form.values.description || '';
  const newFormDescription = currentDescription
    ? `${currentDescription}, ${value}`
    : value;
  form.setFieldValue('description', newFormDescription);

  // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
  if (currentEditingAlertId) {
    setAlertsData(prevData =>
      prevData.map(alert => {
        if (alert.id === currentEditingAlertId) {
          const currentAlertDescription = alert.Description || '';
          const newAlertDescription = currentAlertDescription
            ? `${currentAlertDescription}, ${value}`
            : value;
          return {
            ...alert,
            Description: newAlertDescription
          };
        }
        return alert;
      })
    );
  }

  console.log('Added to form description:', newFormDescription);

  // Optionnel : fermer la sidebar après sélection
  // setIsSidebarVisible(false);
};

const handleCloseSidebar = () => {
  setIsSidebarVisible(false);
};
         const disabled = isFormInvalid || isDraft;
    const FicheForm = useForm({
    initialValues: {
          file_number:1,
          category: '',
          pricing: 0,
          is_bookmarked: false,
          insured: false, // Set to true to match defaultChecked
         description:'',
         titre: '',
    },
    validate: {
      pricing: (value) => (value < 0 ? 'Pricing must be positive' : null),
    },
  });

  // Effect to notify parent component when insured status changes
  useEffect(() => {
    if (onInsuredChange) {
      onInsuredChange(FicheForm.values.insured);
    }
  }, [FicheForm.values.insured, onInsuredChange]);
useEffect(() => {
  if (onInsuredChange && FicheForm.values.insured !== undefined) {
    onInsuredChange(FicheForm.values.insured);
  }
// eslint-disable-next-line react-hooks/exhaustive-deps
}, []); // Se déclenche une seule fois au montage

  const personalForm = useForm({
    initialValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      landline_number: '',
      date_of_birth: null as Date | null,
      gender: '',
      nationality: '',
    spoken_languages: '',
    cine: '',
    profession: '',
    attending_physician: '',
    },
    validate: {
      first_name: (value) => (value.trim().length < 2 ? 'First name must be at least 2 characters' : null),
      last_name: (value) => (value.trim().length < 2 ? 'Last name must be at least 2 characters' : null),
      email: (value) => (/^\S+@\S+\.\S+$/.test(value) ? null : 'Invalid email address'),
      phone_number: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid phone number'),
      landline_number: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid landline number'),
    },
  });

  const addressForm = useForm({
    initialValues: {
      address: '',
      city: '',
      state: '',
      zip_code: '',
      country: '',
    },
  });

  const emergencyForm = useForm({
    initialValues: {
      emergency_contact_name: '',
      emergency_contact_phone: '',
      emergency_contact_relationship: '',
    },
    validate: {
      emergency_contact_phone: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid phone number'),
    },
  });

  const medicalForm = useForm({
    initialValues: {
      medical_conditions: '',
      allergies: '',
      medications: '',
      blood_type: '',
      height: '',
      weight: '',
    },
  });
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use patient data passed from parent component if available
        if (patient) {
          console.log(`✅ Using patient data passed from parent component:`, patient);

          // Convert PatientFormData to UserProfile format
          const profile: UserProfile = {
            id: patient.id?.toString() || 'unknown',
            first_name: patient.first_name || '',
            last_name: patient.last_name || '',
            email: patient.email || '',
            phone_number: patient.phone_number || '',
            landline_number: patient.landline_number || '',
            date_of_birth: patient.date_of_birth || '',
            gender: patient.gender || '',
            address: patient.address || '',
            city: patient.city || '',
            state: patient.state || '',
            zip_code: patient.zip_code || '',
            country: patient.country || '',
            emergency_contact_name: patient.emergency_contact_name || '',
            emergency_contact_phone: patient.emergency_contact_phone || '',
            emergency_contact_relationship: patient.emergency_contact_relationship || '',
            medical_conditions: patient.medical_conditions || '',
            allergies: patient.allergies || '',
            medications: patient.medications || '',
            blood_type: patient.blood_type || '',
            height: patient.height || '',
            weight: patient.weight || '',
            file_number: typeof patient.file_number === 'string' ? parseInt(patient.file_number, 10) || 0 : patient.file_number || 0,
            category: patient.category || '',
            pricing: patient.pricing || 0,
            is_bookmarked: patient.is_bookmarked || false,
            insured: patient.is_insured || false,
            nationality: patient.nationality || '',
            spoken_languages: patient.spoken_languages || '',
            cine: patient.cine || '',
            profession: patient.profession || '',
            attending_physician: patient.attending_physician || '',
            created_at: patient.created_at || new Date().toISOString(),
            updated_at: patient.updated_at || new Date().toISOString()
          };

          setProfile(profile);
          console.log('✅ Patient data loaded from parent component');
          setLoading(false);
          return;
        } else {
          // Continue with API call if no patient prop provided
          console.log(`🔄 No patient data from parent, fetching from API for ID: ${patientId}`);

          // Check Django connection status
          const status = await patientService.checkDjangoBridgeStatus();
          if (status.status !== 'active') {
            throw new Error('Django backend is not connected');
          }

          // Fetch patient data from API
          const patientData = await patientFormService.getPatient(patientId || '');

          if (patientData) {
            console.log('✅ Patient data loaded from API:', patientData);

            // Convert Django patient data to UserProfile format
            const profile: UserProfile = {
              id: patientData.id?.toString() || 'unknown',
              first_name: patientData.first_name || '',
              last_name: patientData.last_name || '',
              email: patientData.email || '',
              phone_number: patientData.phone_number || '',
              landline_number: patientData.landline_number || '',
              date_of_birth: patientData.date_of_birth || '',
              gender: patientData.gender || '',
              address: patientData.address || '',
              city: patientData.city || '',
              state: patientData.state || '',
              zip_code: patientData.zip_code || '',
              country: patientData.country || '',
              emergency_contact_name: patientData.emergency_contact_name || '',
              emergency_contact_phone: patientData.emergency_contact_phone || '',
              emergency_contact_relationship: patientData.emergency_contact_relationship || '',
              medical_conditions: patientData.medical_conditions || '',
              allergies: patientData.allergies || '',
              medications: patientData.medications || '',
              blood_type: patientData.blood_type || '',
              height: patientData.height?.toString() || '',
              weight: patientData.weight?.toString() || '',
              created_at: patientData.created_at || new Date().toISOString(),
              updated_at: patientData.updated_at || new Date().toISOString(),
              file_number: typeof patientData.file_number === 'string' ? parseInt(patientData.file_number, 10) || 0 : patientData.file_number || 0,
              category: patientData.category || '',
              pricing: patientData.pricing || 0,
              is_bookmarked: patientData.is_bookmarked || false,
              insured: patientData.is_insured || false,
              nationality: patientData.nationality || '',
              spoken_languages: patientData.spoken_languages || '',
              cine: patientData.cine || '',
              profession: patientData.profession || '',
              attending_physician: patientData.attending_physician || '',
            };

          setProfile(profile);

          // Load patient alerts
          try {
            const alerts = await patientService.getPatientAlerts(patientId || '');
            setPatientAlerts(alerts || []);
          } catch (alertError) {
            console.error('Error loading patient alerts:', alertError);
            setPatientAlerts([]);
          }

          console.log('✅ Patient data loaded from API');
          } else {
            throw new Error('No patient data received from API');
          }
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Failed to load profile. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [patient, patientId]);

  // Separate useEffect to populate forms when profile changes
  useEffect(() => {
    if (profile) {
      console.log('📝 Populating forms with profile data:', profile);

      // Set form values
      FicheForm.setValues({
        file_number: profile.file_number,
        category: profile.category,
        pricing: profile.pricing,
        is_bookmarked: profile.is_bookmarked,
        insured: profile.insured,
      });

      personalForm.setValues({
        first_name: profile.first_name,
        last_name: profile.last_name,
        email: profile.email,
        phone_number: profile.phone_number,
        landline_number: profile.landline_number,
        date_of_birth: profile.date_of_birth ? new Date(profile.date_of_birth) : null,
        gender: profile.gender,
        nationality: profile.nationality,
        spoken_languages: profile.spoken_languages,
        cine: profile.cine,
        profession: profile.profession,
        attending_physician: profile.attending_physician,
      });

      addressForm.setValues({
        address: profile.address,
        city: profile.city,
        state: profile.state,
        zip_code: profile.zip_code,
        country: profile.country,
      });

      emergencyForm.setValues({
        emergency_contact_name: profile.emergency_contact_name,
        emergency_contact_phone: profile.emergency_contact_phone,
        emergency_contact_relationship: profile.emergency_contact_relationship,
      });

      medicalForm.setValues({
        medical_conditions: profile.medical_conditions,
        allergies: profile.allergies,
        medications: profile.medications,
        blood_type: profile.blood_type,
        height: profile.height,
        weight: profile.weight,
      });

      console.log('✅ All forms populated successfully');
    }
  }, [profile]); // eslint-disable-line react-hooks/exhaustive-deps

 const handleUpdateFiche = async (values: typeof FicheForm.values) => {
    try {
      setSubmitting(true);

      if (!patientId) {
        throw new Error('No patient ID available');
      }

      // Update patient data via Django backend
      const updateData: PatientUpdateData = {
        file_number: values.file_number,
        category: values.category,
        pricing: values.pricing,
        is_bookmarked: values.is_bookmarked,
        is_insured: values.insured,
      };

      console.log('🔄 Updating patient fiche data:', updateData);
      const updatedPatient = await patientFormService.updatePatient(patientId, updateData);

      if (updatedPatient) {
        // Update the local state with the response from patientFormService
        setProfile(prev => {
          if (!prev) return null;
          return {
            ...prev,
            file_number: updatedPatient.file_number ? parseInt(updatedPatient.file_number) : prev.file_number,
            // Map PatientFormData fields to UserProfile fields
            first_name: updatedPatient.first_name || prev.first_name,
            last_name: updatedPatient.last_name || prev.last_name,
            email: updatedPatient.email || prev.email,
            phone_number: updatedPatient.phone_number || prev.phone_number,
          };
        });

        notifications.show({
          title: 'Success',
          message: 'Patient information updated successfully',
          color: 'green',
          icon: <Icon path={mdiCheck} size={16} />,
        });
      } else {
        throw new Error('Failed to update patient data');
      }
    } catch (err) {
      console.error('Error updating patient info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update patient information. Please try again.',
        color: 'red',
        icon: <Icon path={mdiAlertCircle} size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };
  const handleUpdatePersonalInfo = async (values: typeof personalForm.values) => {
    try {
      setSubmitting(true);

      if (!patientId) {
        throw new Error('No patient ID available');
      }

      // Update patient personal data via Django backend
      const updateData: PatientUpdateData = {
        first_name: values.first_name,
        last_name: values.last_name,
        email: values.email,
        phone_number: values.phone_number,
        landline_number: values.landline_number,
        date_of_birth: values.date_of_birth ? values.date_of_birth.toISOString().split('T')[0] : null,
        gender: values.gender,
        nationality: values.nationality,
        spoken_languages: values.spoken_languages,
        cine: values.cine,
        profession: values.profession,
        attending_physician: values.attending_physician,
      };

      console.log('🔄 Updating patient personal data:', updateData);
      const updatedPatient = await patientFormService.updatePatient(patientId, updateData);

      if (updatedPatient) {
        // Update the local state
        setProfile(prev => {
          if (!prev) return null;
          return {
            ...prev,
            first_name: updatedPatient.first_name || prev.first_name,
            last_name: updatedPatient.last_name || prev.last_name,
            email: updatedPatient.email || prev.email,
            phone_number: updatedPatient.phone_number || prev.phone_number,
            date_of_birth: updatedPatient.date_of_birth || prev.date_of_birth,
            gender: updatedPatient.gender || prev.gender,
            // Keep existing values for fields not in PatientFormData
            nationality: prev.nationality,
            spoken_languages: prev.spoken_languages,
            cine: prev.cine,
            profession: prev.profession,
            attending_physician: prev.attending_physician,
          };
        });

        notifications.show({
          title: 'Success',
          message: 'Personal information updated successfully',
          color: 'green',
          icon: <Icon path={mdiCheck} size={16} />,
        });
      } else {
        throw new Error('Failed to update personal information');
      }
    } catch (err) {
      console.error('Error updating personal info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update personal information. Please try again.',
        color: 'red',
        icon: <Icon path={mdiAlertCircle} size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };
  const handleUpdateAddress = async (values: typeof addressForm.values) => {
    try {
      setSubmitting(true);

      if (!patientId) {
        throw new Error('No patient ID available');
      }

      // Update patient address data via Django backend
      const updateData = {
        address: values.address,
        city: values.city,
        state: values.state,
        zip_code: values.zip_code,
        country: values.country,
      };

      console.log('🔄 Updating patient address data:', updateData);
      const updatedPatient = await patientFormService.updatePatient(patientId, updateData);

      if (updatedPatient) {
        // Update the local state
        setProfile(prev => {
          if (!prev) return null;
          return {
            ...prev,
            address: updatedPatient.address || prev.address,
            // Keep existing values for fields not in PatientFormData
            city: prev.city,
            state: prev.state,
            zip_code: prev.zip_code,
            country: prev.country,
          };
        });

        notifications.show({
          title: 'Success',
          message: 'Address updated successfully',
          color: 'green',
          icon: <Icon path={mdiCheck} size={16} />,
        });
      } else {
        throw new Error('Failed to update address');
      }
    } catch (err) {
      console.error('Error updating address:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update address. Please try again.',
        color: 'red',
        icon: <Icon path={mdiAlertCircle} size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };
  const handleUpdateEmergencyContact = async (values: typeof emergencyForm.values) => {
    try {
      setSubmitting(true);
      // In a real app, you would call an API to update the emergency contact
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          emergency_contact_name: values.emergency_contact_name,
          emergency_contact_phone: values.emergency_contact_phone,
          emergency_contact_relationship: values.emergency_contact_relationship,
        };
      });
      notifications.show({
        title: 'Success',
        message: 'Emergency contact updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating emergency contact:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update emergency contact. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };
  const handleUpdateMedicalInfo = async (values: typeof medicalForm.values) => {
    try {
      setSubmitting(true);
      // In a real app, you would call an API to update the medical info
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          medical_conditions: values.medical_conditions,
          allergies: values.allergies,
          medications: values.medications,
          blood_type: values.blood_type,
          height: values.height,
          weight: values.weight,
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Medical information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating medical info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update medical information. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <Loader size="xl" />
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl" >
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
        <Button mt="md" onClick={() => router.push('/dashboard')}>
          Back to Dashboard
        </Button>
      </Container>
    );
  }
 return (
    <>
             <Group>
               <div  className={  "bg-[#3799ce] text-white px-4 py-3 rounded-t-lg w-[100%]" }>
               <Group justify="space-between" align="center">
                 <Group>
                   {profile ? (
                     <Icon path={mdiCardAccountDetails} size={1} />
                   ) : (
                     <Button variant="subtle" onClick={onGoBack}>
                       <Icon path={mdiArrowLeft} size={1} />
                     </Button>
                   )}
                   <Title order={2}>Fiche patient</Title>
                   <DatePickerInput
                     placeholder="Date de création"
                     value={profile?.created_at ? new Date(profile.created_at) : null}
                     readOnly
                   />
                   {profile?.created_at ? new Date(profile.created_at).toLocaleDateString('fr-FR') : 'Date non disponible'}
                 </Group>

                 {profile && (
                   <Group>
                     <Text>{profile.first_name} {profile.last_name}</Text>
                     <Text>{profile.gender}</Text>
                     <Text>{profile.date_of_birth ? new Date().getFullYear() - new Date(profile.date_of_birth).getFullYear() : 'N/A'}</Text>
                     <Text>{profile.cine}</Text>
                     <Text>{profile.id}</Text>
                     <Text>{profile.created_at ? new Date(profile.created_at).toLocaleDateString() : ''}</Text>
                   </Group>
                 )}
           
                 <Group>
                 
           <Tooltip label="List" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                   <Menu shadow="md" width={220}>
                     <Menu.Target>
                       <Button variant="subtle">
                         <Icon path={mdiApps} size={1} color={"white"}/>
                       </Button>
                     </Menu.Target>
                     <Menu.Dropdown>
                       <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />} onClick={() => setIsAlertsModalOpen(true)}>Alerts</Menu.Item>
                       <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}onClick={() => setIsRelationsModalOpen(true)}>Relations Patient</Menu.Item>
                       <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />} component={Link} href={`/patients/planifications/`} >Planifications</Menu.Item>
                       <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}component={Link} href={`/patients/financial-statement/`}>État financier</Menu.Item>
                       <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />} component={Link} href={`/patients/financial-statement/NouvelEncaissement/`} >Nouvel encaissement</Menu.Item>
                       <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />} component={Link} href={`/patient-dental/`}>Schéma dentaire</Menu.Item>
                     </Menu.Dropdown>
                   </Menu>
           </Tooltip>
                   <Tooltip label="Dossier d'abonnement" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                     {/* <Button variant="subtle" onClick={onGoToContract}> */}
                       <Button variant="light" component={Link} href={`/patients/patient-form/Abonnement/`} >
                       <Icon path={mdiCertificate} size={1} color={"white"}/>
                     </Button>
                   </Tooltip>
           
                   <Tooltip label="Liste patients" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                     <Button component="a" href="/patients?tab=Complets" variant="subtle">
                       <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
                     </Button>
                   </Tooltip>
                 </Group>
               </Group>
           {/* menu Alerts */}
                    <Modal.Root
                       opened={isAlertsModalOpen}
                       onClose={() => setIsAlertsModalOpen(false)}
                       transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                       centered
                       size="xl"
                     > 
                   
                    <Modal.Content className="overflow-y-hidden">
                     <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                       <Modal.Title>
                         <Group>
                           <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                             <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
                         </Group>
                       </Modal.Title>
                         <Group justify="flex-end">
                           <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                           onClick={() => 
                             {setIsAlertsAddModalOpen(true)
                           ; toggleSidebar()}}>
                    <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                           </ActionIcon>
                                   <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                 </Group>
                     </Modal.Header>
                       <Modal.Body style={{ padding: '0px' }}>
                        <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                               
                                 <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                   <div className="pr-4">
                                    <Table striped highlightOnHover withTableBorder withColumnBorders>
                                      <Table.Thead>
                   <Table.Tr>
                     <Table.Th>Déclencheur</Table.Th>
                     <Table.Th>Niveau</Table.Th>
                     <Table.Th>Publique</Table.Th>
                     <Table.Th>Permanente</Table.Th>
                      <Table.Th>Description</Table.Th>
                     <Table.Th></Table.Th>
                   </Table.Tr>
                 </Table.Thead>
                 <Table.Tbody>{rows}</Table.Tbody>
                 {rows.length === 0 && (
                   <Table.Caption>Aucun élément trouvé.</Table.Caption>
                 )}
                                     </Table>
                                   </div>
                                 </SimpleBar>
                               </div>
                             </Modal.Body>
                    </Modal.Content>
                     </Modal.Root>
                     {/* add Alerts */}
                      <Modal.Root
                       opened={isAlertsAddModalOpen}
                       onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                       transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                       centered
                       size="xl"
                     > 
                    
                    <Modal.Content className="overflow-y-hidden">
                     <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                       <Modal.Title>
                         <Group>
                           <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                             <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                {`Alerte - ${fullName}`} 
                               </Text>
                         </Group>
                       </Modal.Title>
                         <Group justify="flex-end">
                                   <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                 </Group>
                     </Modal.Header>
                       <Modal.Body style={{ padding: '0px' }}>
                
                        <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
                       
                                 <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                   <div className="pr-4">
                                  <form
                                         onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                         style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                       >
                                         <Group>
                                         <MultiSelect
                                           label="Déclencher pour"
                                           data={staffOptions}
                                           {...form.getInputProps('trigger_for')}
                                           required
                                           w={"30%"}
                                         />
                                         <Select
                                           label="Déclencheur"
                                           data={triggerOptions}
                                           {...form.getInputProps('trigger')}
                                           required
                                            w={"30%"}
                                         />
                                         <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                           <Group>
                                             <Radio value="MINIMUM" label="Minimum" />
                                             <Radio value="MEDIUM" label="Moyen" />
                                             <Radio value="HIGH" label="Haut" />
                                           </Group>
                                         </Radio.Group>
                                         </Group>
                                         <Group justify="space-between">
                                           <Text>Description *</Text>
                                           <Group>
                                            <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                         onClick={
                                           ()=>setIsMicrophoneModalOpen(true)
                                         }>
                                         <Icon path={mdiMicrophone} size={1} />
                                                 </ActionIcon>
                                          <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                         onClick={
                                           ()=>{
                                             console.log('Dictionary button clicked, sidebar visible:', isSidebarVisible);
                                             setIsClipboardTextModalOpen(true);
                                                setShowModels(true);
                                                setShowAddModel(false);

                                           }
                                         }>
                                         <Icon path={mdiClipboardText} size={1} />
                                                 </ActionIcon>
                                          <ActionIcon
                                            variant="filled"
                                            aria-label="Clear Description"
                                            color="red"
                                            onClick={() => {
                                              console.log('Clear button clicked, clearing description field');
                                              form.setFieldValue('description', '');
                                              console.log('Description field cleared');
                                            }}
                                          >
                                            <Icon path={mdiDeleteSweep} size={1} />
                                          </ActionIcon>
                                           </Group>
                                         </Group>
                                         <Textarea
                                           // label="Description"
                                           placeholder="Ajouter"
                                           {...form.getInputProps('description')}
                                           required
                                         />
                                       
                                         <Switch
                                           label="Permanente"
                                           {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                         />
                                 
                                         <Group justify="flex-end" mt="md">
                                           <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                             Annuler
                                           </Button>
                                           <Button
                                             onClick={() => {
                                               if (form.isValid()) {
                                                 handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                               }
                                             }}
                                             disabled={!form.isValid()}
                                           >
                                             Enregistrer et déclencher
                                           </Button>
                                           <Button type="submit" disabled={!form.isValid()}>
                                             Enregistrer
                                           </Button>
                                         </Group>
                                       </form>
                                   </div>
                                 </SimpleBar>
                               </div>
                             </Modal.Body>
                    </Modal.Content>
                     </Modal.Root>           
                     {/* Modal de confirmation de suppression */}
                     <Modal.Root
                       opened={isDeleteConfirmModalOpen}
                       onClose={cancelDeleteAlert}
                       centered
                       size="sm"
                     >
                       <Modal.Content>
                         <Modal.Header>
                           <Modal.Title>Confirmation de suppression</Modal.Title>
                           <Modal.CloseButton />
                         </Modal.Header>
                         <Modal.Body>
                           <Text size="md" mb="md">
                             Êtes-vous sûr de vouloir supprimer alert ??
                           </Text>
                           <Group justify="flex-end" gap="sm">
                             <Button
                               variant="outline"
                               color="blue"
                               onClick={confirmDeleteAlert}
                             >
                               Oui
                             </Button>
                             <Button
                               variant="filled"
                               color="red"
                               onClick={cancelDeleteAlert}
                             >
                               Non
                             </Button>
                           </Group>
                         </Modal.Body>
                       </Modal.Content>
                     </Modal.Root>

               </div>
                 
           
                 {/* Modal Microphone - Reconnaissance vocale */}
                 <Modal
                   opened={isMicrophoneModalOpen}
                   onClose={() => setIsMicrophoneModalOpen(false)}
                   title="Reconnaissance vocale"
                   size="lg"
                   radius={0}
                   transitionProps={{ transition: 'fade', duration: 200 }}
                   centered
                   withCloseButton={false}
                   yOffset="30vh" xOffset={0}
                   
                 >
                   <div style={{ padding: '20px' }}>
                     {/* Interface de reconnaissance vocale */}
                     <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                       <div style={{ flex: 1, marginRight: '16px' }}>
                         <div style={{
                           border: '1px solid #e0e0e0',
                           borderRadius: '4px',
                           padding: '12px',
                           minHeight: '80px',
                           backgroundColor: '#fafafa',
                          height:'150px'
                         }}>
                           {/* Texte valide reconnu */}
                           <span
                             style={{
                               color: '#2e7d32',
                               fontWeight: 500,
                               display: validSpeech ? 'inline' : 'none'
                             }}
                             contentEditable
                           >
                             {validSpeech}
                           </span>
                           {/* Texte en cours de reconnaissance */}
                           <span
                             style={{
                               color: '#757575',
                               fontStyle: 'italic'
                             }}
                           >
                             {invalidSpeech}
                           </span>
                         </div>
                       </div>
           
                       {/* Boutons de contrôle */}
                       <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                         <ActionIcon
                           variant="subtle"
                           color={isListening ? 'orange' : 'blue'}
                           size="lg"
                           onClick={toggleRecognition}
                           style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                         >
                           <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                         </ActionIcon>
           
                         <ActionIcon
                           variant="subtle"
                           color="red"
                           size="lg"
                           onClick={emptyContent}
                         >
                           <Icon path={mdiDeleteSweep} size={1} />
                         </ActionIcon>
                       </div>
                     </div>
           
                     {/* Boutons d'action */}
                     <Group justify="flex-end" mt="md">
                       <Button
                         variant="filled"
                         onClick={() => {
                           // Ici vous pouvez traiter le texte reconnu
                           console.log('Texte reconnu:', validSpeech);
                           setIsMicrophoneModalOpen(false);
                         }}
                       >
                         Valider
                       </Button>
                       <Button
                         variant="outline"
                         color="red"
                         onClick={() => setIsMicrophoneModalOpen(false)}
                       >
                         Annuler
                       </Button>
                     </Group>
                   </div>
                 </Modal>
           
                 {/* Gestionnaire des modaux de dictionnaire */}
                 <DictionaryModalsManager
                   // États des modaux
                   isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                   isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                   isDictionaryTreeModalOpen={isChoixMultipleModalOpen}

                   // Données
                   modelTitle={modelTitle}
                   savedModels={savedModels}
                   exampleData={exampleData}
                   selectedNodes={selectedNodes}
                   collapsedNodes={collapsedNodes}
                   editingModelId={editingModelId}

                   // Fonctions de gestion des états
                   setModelTitle={setModelTitle}
                   setIsAddModelModalOpen={setShowAddModel}
                   setIsSavedModelsModalOpen={setShowModels}
                   setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}

                   // Fonctions de gestion des modèles
                   onSaveModel={handleSaveModel}
                   onToggleModel={(modelId) => {
                     console.log('Toggling model:', modelId);
                     setSavedModels(prev => {
                       const updated = prev.map(model =>
                         model.id === modelId
                           ? { ...model, selected: !model.selected }
                           : model
                       );
                       console.log('Updated savedModels:', updated);
                       return updated;
                     });
                   }}
                   onDeleteModel={handleDeleteModel}
                   onEditModel={handleEditModel}

                   // Fonctions de gestion de l'arbre
                   onToggleNodeCollapse={toggleNodeCollapse}
                   onToggleNodeSelection={toggleNodeSelection}
                   onSelectAll={selectAllNodes}
                   onDeselectAll={deselectAllNodes}

                   // Fonctions d'action
                   onValidate={handleValidate}
                   onCancel={handleCancel}
                   onCloseSidebar={() => {
                     console.log('Closing sidebar from SavedModelsModal');
                     setIsSidebarVisible(false);
                   }}
                   getSelectedValues={getSelectedValues}

                   // Composants
                   TreeItemChoixMultiple={TreeItemChoixMultiple}
                 />

                 {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                 {isClipboardTextModalOpen && !showModels && !showAddModel && (
                   <div style={{ display: 'none' }}>
                     {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                   </div>
                 )}

               
   {/*modal Relations */}
                    <Modal.Root
                       opened={isRelationsModalOpen}
                       onClose={() => setIsRelationsModalOpen(false)}
                       transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                       centered
                       size="lg"
                       zIndex={10}
                     > 
                   
                    <Modal.Content className="overflow-y-hidden">
                     <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                       <Modal.Title>
                         <Group>
                           <Icon path={mdiAccountSupervisorCircle} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                             <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                             Relations - ABDESSALMAD AGADIR </Text>
                         </Group>
                       </Modal.Title>
                         <Group justify="flex-end">
                           <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                           onClick={openListDesPatient}>
                    <Icon path={mdiAccountSearch} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                           </ActionIcon>
                                   <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                 </Group>
                     </Modal.Header>
                       <Modal.Body style={{ padding: '0px' }}>
                        <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                               
                                 <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                   <div className="pr-4">
                                    <Card shadow="sm" padding="lg" radius="md" withBorder>
                                      <RelationForm/>
                                    </Card>
                                     
                                   </div>
                                 </SimpleBar>
                               </div>
                             </Modal.Body>
                              <Group justify="flex-end" m="md">
                              <Button variant="outline" color="red" onClick={() => setIsRelationsModalOpen(false)}>
                                Annuler
                              </Button>
                              <Button type="submit" disabled={!form.isValid()}>
                                Enregistrer
                              </Button>
                            </Group> 
                    </Modal.Content>
                     </Modal.Root>
               </Group>
<div className='flex'>
  <div className={isSidebarVisible ? " w-[80%]" : "w-full"}>
      <Grid gutter="md" w={'100%'} p={'10px'}>
       
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder p="lg">
      
            <Stack align="center" gap="md">
              {/* <Avatar size={120} radius={120} color="blue">
                {profile?.first_name?.[0]}{profile?.last_name?.[0]}
              </Avatar> */}
               <AvatarWithUpload
                  size={120}
                  radius={120}
                  color="blue"
                  src={profile?.profile_image
                   
                      ? `${profile.profile_image}?t=${Date.now()}`
                      : undefined}
                  initials={`${profile?.first_name?.charAt(0) || ''}${profile?.last_name?.charAt(0) || ''}`}
                  onImageUpload={async (file) => {
                    try {
                      const formData = new FormData();
                      formData.append('profile_image', file);

                      // Show loading notification
                      notifications.show({
                        id: 'uploading-image',
                        title: 'Uploading image',
                        message: 'Please wait while your image is being uploaded...',
                        loading: true,
                        autoClose: false,
                      });

                     

                      // Refresh user data in the AuthContext to update all instances of the avatar
                      await refreshUser();

                      // Show success notification
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Success',
                        message: 'Profile image updated successfully',
                        color: 'green',
                        icon: <IconCheck size={16} />,
                        autoClose: 3000,
                      });

                      
                    } catch (error: unknown) {
                      // Log the error with detailed information
                      logError('uploading profile image', error as Error);

                      // Extract the error message
                      const errorMessage = extractErrorMessage(error as Error, 'Failed to upload profile image. Please try again.');

                      // Update notification to show error
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Error',
                        message: errorMessage,
                        color: 'red',
                        icon: <IconAlertCircle size={16} />,
                        autoClose: 3000,
                      });
                    }
                  }}
                />
              <Title order={3}>{profile?.first_name} {profile?.last_name}</Title>
              <Badge size="lg">{profile?.gender || 'Not specified'}</Badge>
              <Divider w="100%" />
              <Stack w="100%" gap="xs">
                <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
                        <TextInput
                          label="N° de dossier papier"
                          placeholder="N° de dossier papier"
                          required
                          {...FicheForm.getInputProps('file_number')}
                          mb={10}
                        />
                     <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Catégorie"
                        {...FicheForm.getInputProps('category')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setIsCategorieModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                    
                      </Group >
                        <Select
                                label="Tarification"
                                placeholder="Sélectionner"
                                data={[
                                  { value: 'standard', label: 'Standard' },
                                  { value: 'premium', label: 'Premium' }
                                ]}
                                {...FicheForm.getInputProps('category')}
                                className="mb-3"
                              />
                      <Select
                          w={"100%"}
                          label="pricing"
                          data={pricingOptions}
                        {...FicheForm.getInputProps('pricing')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."

                        />
                        <Group justify="center" grow mb={10} mt={26} ml={40}>
                         <Switch
                        // defaultChecked
                        label="Favoris"
                        size="xs"
                        {...FicheForm.getInputProps('is_bookmarked')}
                       
                        />
                          <Switch
                        // defaultChecked
                        label="Assuré"
                        size="xs"
                        {...FicheForm.getInputProps('insured')}
                        />
                     </Group>
                    
                </form>
              
              </Stack>
         
              <Button 
                leftSection={<IconEdit size={16} />} 
                variant="outline" 
                fullWidth
                onClick={() => setActiveTab('personal')}
              >
                Edit Profile
              </Button>
            </Stack>
           
          </Card>
           
        </Grid.Col>
        
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Paper withBorder p="md" radius="md">
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List mb="md">
                <Tabs.Tab value="personal" leftSection={<IconUser size={14} />}>
                  Personal Information
                </Tabs.Tab>
                <Tabs.Tab value="address" leftSection={<IconAddressBook size={14} />}>
                  Address
                </Tabs.Tab>
                <Tabs.Tab value="emergency" leftSection={<IconPhone size={14} />}>
                  Emergency Contact
                </Tabs.Tab>
                <Tabs.Tab value="medical" leftSection={<IconMedicalCross size={14} />}>
                  Medical Information
                </Tabs.Tab>
                  <Tabs.Tab value="test" leftSection={<Icon path={mdiShare} size={1} color={'#3799ce'}/>} ml="auto" 
                        >  
                  <Button variant="subtle" component={Link} href={`/patient?tab=Complets`} className='NoHoverBtn'> Liste des patients</Button>
                </Tabs.Tab>
                              
              </Tabs.List>

              <Tabs.Panel value="personal">
                <form onSubmit={personalForm.onSubmit(handleUpdatePersonalInfo)}>
                  <Stack gap="md">
                    <Grid>
                       <Grid.Col span={{ base: 12, md: 4 }}>
                       <Select
                                      label="Titre"
                                      placeholder="Sélectionner"
                                      data={[
                                       { value: "m", label: "M." },
                                        { value: "mme", label: "Mme" },
                                        { value: "mlle", label: "Mlle" },
                                        { value: "dr", label: "Dr." },
                                        { value: "pr", label: "Pr." }
                                      ]}
                                      className="mb-3 "
                                    />
                                    </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="First Name"
                          placeholder="Enter first name"
                          required
                          {...personalForm.getInputProps('first_name')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="Last Name"
                          placeholder="Enter last name"
                          required
                          {...personalForm.getInputProps('last_name')}
                        />
                      </Grid.Col>
                    </Grid>
 <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <DatePickerInput
                          label="Date of Birth"
                          placeholder="Select date of birth"
                          leftSection={<IconCalendar size={16} />}
                          clearable
                          {...personalForm.getInputProps('date_of_birth')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <Select
                          label="Gender"
                          placeholder="Select gender"
                          data={[
                            { value: 'Male', label: 'Male' },
                            { value: 'Female', label: 'Female' },
                            { value: 'Other', label: 'Other' },
                            { value: 'Prefer not to say', label: 'Prefer not to say' },
                          ]}
                          clearable
                          {...personalForm.getInputProps('gender')}
                        />
                      </Grid.Col>
                    </Grid>
                              <Grid>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="CNIE"
                          placeholder="CNIE"
                          required
                          {...personalForm.getInputProps('cnie')}
                        />
                      </Grid.Col>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                       <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Profession"
                        {...personalForm.getInputProps('Profession')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
</Grid>
<Grid>
                       <Grid.Col span={{ base: 12, md: 6 }}>
                      <TextInput
                      label={
                        <>
                          Phone Number<sup>1</sup>
                        </>
                      }
                      placeholder="Téléphone"
                      {...personalForm.getInputProps('phone_number')}
                    />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label={
                        <>
                          Landline Number<sup>2</sup>
                        </>
                      }
                      placeholder="Téléphone fixe"
                      {...personalForm.getInputProps('landline_number')}
                    />
                    </Grid.Col>
</Grid>
                    <TextInput
                      label="Email"
                      placeholder="Enter email address"
                      required
                      {...personalForm.getInputProps('email')}
                    />
<Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Nationalité"
                          data={nationalityOptions}
                        {...personalForm.getInputProps('nationality')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setIsNationalityModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 6 }}>
                       <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Langues parlées"
                          data={languageOptions}
                        {...personalForm.getInputProps('spoken_languages')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setLanguagesSpokenModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
                    </Grid>
          
<Grid>
 <Grid.Col span={{ base: 12, md: 6 }}>
   <Select
                          label="Médecin traitant"
                      placeholder="Médecin traitant"
                          data={[
                            { value: 'ahmed', label: 'ahmed' },
                            { value: 'Stif', label: 'Stif' },
                            { value: 'Other', label: 'Other' },
                           
                          ]}
                          clearable
                          {...personalForm.getInputProps('attending_physician')}
                        />
                    
                    </Grid.Col>
                     <Grid.Col span={{ base: 12, md: 6 }}>
                      <TextInput
                      label="N° Passport"
                      placeholder="N° Passport"
                      {...personalForm.getInputProps('passport_number')}
                    />
                    </Grid.Col>
                    </Grid>
                  </Stack>
                   
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="address">
                <form onSubmit={addressForm.onSubmit(handleUpdateAddress)}>
                  <Stack gap="md">
                    <TextInput
                      label="Adressé par"
                      placeholder="Adressé par"
                      {...addressForm.getInputProps('address')}
                    />

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                          <Group mb={10}>
                        <Select
                          w={"68%"}
                         label="Ville"
                          placeholder="Entrez le Ville"
                          data={[
                            { value: 'AGADIR', label: 'AGADIR' },
                            { value: 'AIT BAHA', label: 'AIT BAHA' },
                            { value: 'AZILAL', label: 'AZILAL' },
                            { value: 'BERRECHID', label: 'BERRECHID' },
                         
                          ]}
                          clearable
                          {...addressForm.getInputProps('city')}
                          searchable
                          maxDropdownHeight={280}
                        
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                       
                       
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                              <Group mb={10}>
                        <Select
                          w={"68%"}
                         label="Pays"
                          placeholder="Entrez le pays"
                          {...addressForm.getInputProps('country')}
                          data={[
                            { value: 'Maroc', label: 'Maroc' },
                            { value: 'France', label: 'France' },
                            { value: 'usa', label: 'usa' },
                            
                         
                          ]}
                          clearable
                          {...addressForm.getInputProps('city')}
                          searchable
                          maxDropdownHeight={280}
                        
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      
                      </Grid.Col>
                    </Grid>

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="Code postal"
                          placeholder="Entrez le code postal"
                          {...addressForm.getInputProps('zip_code')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                     
                          <TextInput
                          label="État/Province"
                          placeholder="État/Province"
                          {...addressForm.getInputProps('state')}
                        />
                      </Grid.Col>
                    </Grid>
                    <Grid>
                          {/* if wwitsh show hide  texteara */}
                    <Grid.Col span={{ base: 12, md: 2 }} p={0} mt={12}>
                      <div style={{marginTop:'20px',padding:'0'}}>
<Switch
                        defaultChecked
                        label="A complèter"
                        size="xs"
                        {...FicheForm.getInputProps('is_complete')}
                      
                        />
                        </div>
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 10 }}>
                         <Textarea
                        placeholder="Raison"
                         {...FicheForm.getInputProps('Reason')}
                      />
    </Grid.Col>
      </Grid>   
                   <Grid>
                    <Grid.Col span={{ base: 12, md: 2 }}mt={4}>
                      <div style={{marginTop:'20px',padding:'0'}}>
                        {/* if wwitsh show hide datetim piker and texteara */}
 <Switch
                        defaultChecked
                        label="Décès"
                        size="xs"
                        {...FicheForm.getInputProps('Death')}
                        />
                        </div>
                       
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 4 }} mt={12}>
                        {/* <DateTimePicker label="Date de décès" placeholder="Date de décès" /> */}
              <DateTimePicker
      // label="Pick date and time"
                  placeholder="Pick date and time"
                  presets={[
                    { value: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'), label: 'Yesterday' },
                    { value: dayjs().format('YYYY-MM-DD HH:mm:ss'), label: 'Today' },
                    { value: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm:ss'), label: 'Tomorrow' },
                    { value: dayjs().add(1, 'month').format('YYYY-MM-DD HH:mm:ss'), label: 'Next month' },
                    { value: dayjs().add(1, 'year').format('YYYY-MM-DD HH:mm:ss'), label: 'Next year' },
                    {
                      value: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
                      label: 'Last month',
                    },
                    { value: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'), label: 'Last year' },
                  ]}
                />
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 6 }}>
                         <Textarea
                        placeholder="Raison"
                         {...FicheForm.getInputProps('Raison_de_décès')}
                      />
    </Grid.Col>
      </Grid>       
                  
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="emergency">
                <form onSubmit={emergencyForm.onSubmit(handleUpdateEmergencyContact)}>
                  <Stack gap="md">
                    <TextInput
                      label="Emergency Contact Name"
                      placeholder="Enter emergency contact name"
                      {...emergencyForm.getInputProps('emergency_contact_name')}
                    />

                    <TextInput
                      label="Emergency Contact Phone"
                      placeholder="Enter emergency contact phone"
                      {...emergencyForm.getInputProps('emergency_contact_phone')}
                    />

                    <TextInput
                      label="Relationship to Patient"
                      placeholder="Enter relationship to patient"
                      {...emergencyForm.getInputProps('emergency_contact_relationship')}
                    />


                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="medical">
                <form onSubmit={medicalForm.onSubmit(handleUpdateMedicalInfo)}>
                  <Stack gap="md">
                    <Textarea
                      label="Medical Conditions"
                      placeholder="Enter any medical conditions"
                      minRows={3}
                      {...medicalForm.getInputProps('medical_conditions')}
                    />

                    <Textarea
                      label="Allergies"
                      placeholder="Enter any allergies"
                      minRows={3}
                      {...medicalForm.getInputProps('allergies')}
                    />

                    <Textarea
                      label="Current Medications"
                      placeholder="Enter current medications"
                      minRows={3}
                      {...medicalForm.getInputProps('medications')}
                    />

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <Select
                          label="Blood Type"
                          placeholder="Select blood type"
                          data={[
                            { value: 'A+', label: 'A+' },
                            { value: 'A-', label: 'A-' },
                            { value: 'B+', label: 'B+' },
                            { value: 'B-', label: 'B-' },
                            { value: 'AB+', label: 'AB+' },
                            { value: 'AB-', label: 'AB-' },
                            { value: 'O+', label: 'O+' },
                            { value: 'O-', label: 'O-' },
                          ]}
                          clearable
                          {...medicalForm.getInputProps('blood_type')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="Height"
                          placeholder="e.g., 180 cm"
                          {...medicalForm.getInputProps('height')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="Weight"
                          placeholder="e.g., 75 kg"
                          {...medicalForm.getInputProps('weight')}
                        />
                      </Grid.Col>
                    </Grid>

                    {/* <Group justify="flex-end" mt="md">
                        <Group>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                      </Group>
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group> */}
                  </Stack>
                </form>
              </Tabs.Panel>
            </Tabs>
            
          </Paper>
        </Grid.Col>
       
      </Grid>
</div>
      {/* Sidebar */}
      {isSidebarVisible && (
        <Card shadow="sm" mt={'10px'} padding="lg" radius="md" withBorder className={isSidebarVisible ? " w-[20%]" : "w-full"}>
          <Box mb="sm">
            <Group>
              <Input
                placeholder="Rechercher"
                value={search}
                onChange={(e) => setSearch(e.currentTarget.value)}
                w={"70%"}
              />

              <Group justify="flex-end">
                <ActionIcon
                  variant="filled"
                  aria-label="Multiple"
                  color="#3799CE"
                  onClick={() => {
                    // Vous pouvez ajouter ici la logique pour ouvrir le modal de choix multiple
                    console.log('Open multiple choice modal');
                    setIsSidebarVisible(false);
                    setIsChoixMultipleModalOpen(true);
                  }}
                >
                  <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                <ActionIcon
                  variant="filled"
                  aria-label="Annuler"
                  color="#3799CE"
                  onClick={handleCloseSidebar}
                >
                  <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
              </Group>
            </Group>
          </Box>

          <ScrollArea h={400}>
            <Tree
              nodes={createAlertsTree(patientAlerts).filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
              onSelect={handleSidebarSelect}

            />
          </ScrollArea>
        </Card>
      )}

      </div>
       <div style={{marginTop:"10px" ,marginBottom:"20px", borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
                       
                        <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
                     <Group gap="xs">
                         {patientId && (
                         <>
                             <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                                 <ActionIcon variant="filled" aria-label="Settings" radius="4px"
                         onClick={onPrint}>
                             <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                         </ActionIcon>
                             </Tooltip>
                         <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                                 <ActionIcon variant="filled" aria-label="Settings"radius="4px"
                         onClick={onPrevious}>
                             <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                         </ActionIcon>
                             </Tooltip>
                         
                 <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                                 <ActionIcon variant="filled" aria-label="Settings"radius="4px"
                         onClick={onNext}>
                             <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                         </ActionIcon>
                             </Tooltip>
                         
                         </>
                         )}
                     </Group>
                 
                     <Group gap="xs" mr={10}>
                         <Tooltip label="Commencer la visite" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                         <ActionIcon variant="filled" aria-label="Settings" radius="4px"
                         onClick={onStartVisit}
                             disabled={disabled}>
                         <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                         </ActionIcon>
                         </Tooltip>
                         <Tooltip label="Ajouter un rendez-vous" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                         <ActionIcon variant="filled" aria-label="Settings"radius="4px"
                         onClick={onAppointment}
                             disabled={isFormInvalid}>
                             <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                         </ActionIcon>
                         </Tooltip>
                         <Button variant="outline" color="red" component={Link} href={`/home`} > 
                        Annuler</Button>
                       
                 
                         {patientId && (
                         <Button
                             variant="filled"
                             color="blue"
                             onClick={onSaveQuitNew}
                             disabled={isFormInvalid}
                         >
                             Enregistrer & Nouvelle fiche
                         </Button>
                         )}
                 
                         <Button
                         variant="filled"
                         color="blue"
                         onClick={onSaveQuit}
                         disabled={isFormInvalid}
                         >
                         Enregistrer et quitter
                         </Button>
                 
                         <Button
                         variant="filled"
                         color="blue"
                         type="submit"
                         disabled={isFormInvalid}
                         >
                         Enregistrer la fiche
                         </Button>
                     </Group>
                     </Group>
                       
                     </div>
       <Modal
        opened={isCategorieModalOpen}
        onClose={() => setIsCategorieModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsCategorieModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
       <Modal
        opened={isNationalityModalOpen}
        onClose={() => setIsNationalityModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsNationalityModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
        <Modal
        opened={isLanguagesSpokenModalOpen}
        onClose={() => setLanguagesSpokenModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setLanguagesSpokenModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
        <Modal
        opened={isProfessionModalOpen}
        onClose={() => setProfessionModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setProfessionModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </>
  );
}
