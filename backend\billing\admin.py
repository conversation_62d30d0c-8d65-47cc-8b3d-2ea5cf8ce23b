from django.contrib import admin
from .models import Invoice, InvoiceItem, Payment, Subscription, License


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'patient', 'status', 'total_amount', 'balance_due', 'due_date', 'created_at')
    list_filter = ('status', 'issue_date', 'due_date', 'created_at')
    search_fields = ('invoice_number', 'patient__first_name', 'patient__last_name')
    date_hierarchy = 'issue_date'
    ordering = ('-created_at',)
    inlines = [InvoiceItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('invoice_number', 'patient', 'appointment', 'status')
        }),
        ('Dates', {
            'fields': ('issue_date', 'due_date')
        }),
        ('Amounts', {
            'fields': ('subtotal', 'tax_rate', 'tax_amount', 'discount_amount', 'total_amount', 'paid_amount', 'balance_due')
        }),
        ('Additional Information', {
            'fields': ('notes', 'terms_and_conditions'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('tax_amount', 'total_amount', 'balance_due', 'created_at', 'updated_at')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_number', 'patient', 'amount', 'payment_method', 'status', 'payment_date')
    list_filter = ('payment_method', 'status', 'payment_date', 'created_at')
    search_fields = ('payment_number', 'patient__first_name', 'patient__last_name', 'transaction_id')
    date_hierarchy = 'payment_date'
    ordering = ('-payment_date',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('payment_number', 'invoice', 'patient', 'amount', 'payment_method', 'status')
        }),
        ('Processing Details', {
            'fields': ('transaction_id', 'reference_number', 'payment_date', 'processed_date')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('subscription_number', 'patient', 'plan', 'status', 'monthly_price', 'next_billing_date')
    list_filter = ('plan', 'status', 'billing_cycle', 'created_at')
    search_fields = ('subscription_number', 'patient__first_name', 'patient__last_name')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('subscription_number', 'patient', 'plan', 'status', 'billing_cycle')
        }),
        ('Pricing', {
            'fields': ('monthly_price', 'setup_fee')
        }),
        ('Dates', {
            'fields': ('start_date', 'end_date', 'next_billing_date')
        }),
        ('Features & Limits', {
            'fields': ('features', 'usage_limits'),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )


@admin.register(License)
class LicenseAdmin(admin.ModelAdmin):
    list_display = ('license_key', 'patient', 'license_type', 'status', 'expiry_date', 'activation_count')
    list_filter = ('license_type', 'status', 'issue_date', 'expiry_date')
    search_fields = ('license_key', 'patient__first_name', 'patient__last_name')
    date_hierarchy = 'issue_date'
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('license_key', 'patient', 'license_type', 'status')
        }),
        ('Validity', {
            'fields': ('issue_date', 'expiry_date')
        }),
        ('Usage', {
            'fields': ('activation_count', 'max_activations')
        }),
        ('Features & Restrictions', {
            'fields': ('features', 'restrictions'),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )
