/**
 * Dashboard Widgets for Generic States
 * Displays key metrics and analytics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
} from '@mantine/core';
import {
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconCalendarStats,
  IconCurrencyEuro,
  IconUsers,
  IconStethoscope,
  IconChartBar,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useGenericStates } from '@/hooks/useGenericStates';

interface DashboardWidgetsProps {
  dateRange?: { start: string; end: string };
  compact?: boolean;
  showTrends?: boolean;
}

const DashboardWidgets: React.FC<DashboardWidgetsProps> = ({
  dateRange,
  compact = false,
  showTrends = true,
}) => {
  const {
    activityReports,
    financialReports,
    patientAnalytics,
    appointmentAnalytics,
    loading,
    error,
    getTotalRevenue,
    getTotalPatients,
    getTopProcedures,
    getDoctorPerformance,
    getRecentTrends,
  } = useGenericStates({ 
    dateRange, 
    autoFetch: true,
    reportTypes: ['activity', 'financial', 'patient', 'appointment']
  });

  const trends = getRecentTrends();
  const topProcedures = getTopProcedures(3);
  const doctorPerformance = getDoctorPerformance().slice(0, 3);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <IconTrendingUp size={16} color="green" />;
      case 'down':
        return <IconTrendingDown size={16} color="red" />;
      default:
        return <IconMinus size={16} color="gray" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'green';
      case 'down':
        return 'red';
      default:
        return 'gray';
    }
  };

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  const totalRevenue = getTotalRevenue();
  const totalPatients = getTotalPatients();
  const totalAppointments = activityReports.reduce((sum, report) => sum + report.totalAppointments, 0);
  const completedAppointments = activityReports.reduce((sum, report) => sum + report.completedAppointments, 0);
  const completionRate = totalAppointments > 0 ? (completedAppointments / totalAppointments) * 100 : 0;

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Revenue Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconCurrencyEuro size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Revenus</Text>
            </Group>
            {showTrends && getTrendIcon(trends.revenueTrend)}
          </Group>
          <Text size="xl" fw={700} c="blue">
            {totalRevenue.toLocaleString()}€
          </Text>
          {showTrends && (
            <Text size="xs" c={getTrendColor(trends.revenueTrend)}>
              Tendance {trends.revenueTrend === 'up' ? 'à la hausse' : trends.revenueTrend === 'down' ? 'à la baisse' : 'stable'}
            </Text>
          )}
        </Card>

        {/* Patients Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="green" size="sm">
                <IconUsers size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Patients</Text>
            </Group>
            {showTrends && getTrendIcon(trends.patientTrend)}
          </Group>
          <Text size="xl" fw={700} c="green">
            {totalPatients.toLocaleString()}
          </Text>
          {patientAnalytics && (
            <Text size="xs" c="dimmed">
              +{patientAnalytics.newPatients} nouveaux
            </Text>
          )}
        </Card>

        {/* Appointments Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconCalendarStats size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Rendez-vous</Text>
            </Group>
            {showTrends && getTrendIcon(trends.appointmentTrend)}
          </Group>
          <Text size="xl" fw={700} c="orange">
            {totalAppointments.toLocaleString()}
          </Text>
          <Progress value={completionRate} size="xs" mt="xs" />
          <Text size="xs" c="dimmed">
            {completionRate.toFixed(1)}% complétés
          </Text>
        </Card>

        {/* Completion Rate Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconChartBar size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Taux de réussite</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ value: completionRate, color: 'purple' }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {completionRate.toFixed(0)}%
                </Text>
              }
            />
          </Group>
        </Card>
      </SimpleGrid>

      {!compact && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={3} spacing="md">
            {/* Top Procedures */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconStethoscope size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Top Procédures</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {topProcedures.map((procedure, index) => (
                  <Group key={procedure.name} justify="space-between">
                    <Group gap="xs">
                      <Badge size="xs" color="teal" variant="light">
                        {index + 1}
                      </Badge>
                      <Text size="xs">{procedure.name}</Text>
                    </Group>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{procedure.count}</Text>
                      <Text size="xs" c="dimmed">{procedure.revenue}€</Text>
                    </Group>
                  </Group>
                ))}
                {topProcedures.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée disponible
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Doctor Performance */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="indigo" size="sm">
                    <IconUsers size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Performance Médecins</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {doctorPerformance.map((doctor, index) => (
                  <Group key={doctor.name} justify="space-between">
                    <Group gap="xs">
                      <Badge size="xs" color="indigo" variant="light">
                        {index + 1}
                      </Badge>
                      <Text size="xs">{doctor.name}</Text>
                    </Group>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{doctor.appointments}</Text>
                      <Text size="xs" c="dimmed">{doctor.revenue}€</Text>
                    </Group>
                  </Group>
                ))}
                {doctorPerformance.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée disponible
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Financial Summary */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="yellow" size="sm">
                    <IconCurrencyEuro size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Résumé Financier</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {financialReports.slice(0, 3).map((report) => (
                  <Group key={report.id} justify="space-between">
                    <Text size="xs" tt="capitalize">{report.type}</Text>
                    <Group gap="xs">
                      <Text size="xs" fw={500} c="green">
                        {report.paidAmount.toLocaleString()}€
                      </Text>
                      {report.pendingAmount > 0 && (
                        <Text size="xs" c="orange">
                          ({report.pendingAmount.toLocaleString()}€)
                        </Text>
                      )}
                    </Group>
                  </Group>
                ))}
                {financialReports.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée disponible
                  </Text>
                )}
              </Stack>
            </Card>
          </SimpleGrid>

          {/* Patient Demographics */}
          {patientAnalytics && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>Démographie des Patients</Text>
                <Badge size="sm" color="blue">
                  {patientAnalytics.totalPatients} total
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={6}>
                  <Text size="xs" c="dimmed" mb="xs">Répartition par âge</Text>
                  <Stack gap="xs">
                    {patientAnalytics.ageGroups.map((group) => (
                      <Group key={group.ageRange} justify="space-between">
                        <Text size="xs">{group.ageRange} ans</Text>
                        <Group gap="xs">
                          <Progress 
                            value={group.percentage} 
                            size="xs" 
                            w={60}
                            color="blue"
                          />
                          <Text size="xs" fw={500}>{group.percentage}%</Text>
                        </Group>
                      </Group>
                    ))}
                  </Stack>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="xs" c="dimmed" mb="xs">Répartition par genre</Text>
                  <Group justify="center">
                    <RingProgress
                      size={120}
                      thickness={12}
                      sections={[
                        { 
                          value: (patientAnalytics.genderDistribution.male / patientAnalytics.totalPatients) * 100, 
                          color: 'blue',
                          tooltip: `Hommes: ${patientAnalytics.genderDistribution.male}`
                        },
                        { 
                          value: (patientAnalytics.genderDistribution.female / patientAnalytics.totalPatients) * 100, 
                          color: 'pink',
                          tooltip: `Femmes: ${patientAnalytics.genderDistribution.female}`
                        },
                      ]}
                      label={
                        <Text size="xs" ta="center">
                          Répartition
                        </Text>
                      }
                    />
                  </Group>
                  <Group justify="center" mt="xs" gap="md">
                    <Group gap="xs">
                      <div style={{ width: 8, height: 8, backgroundColor: 'var(--mantine-color-blue-6)', borderRadius: '50%' }} />
                      <Text size="xs">Hommes ({patientAnalytics.genderDistribution.male})</Text>
                    </Group>
                    <Group gap="xs">
                      <div style={{ width: 8, height: 8, backgroundColor: 'var(--mantine-color-pink-6)', borderRadius: '50%' }} />
                      <Text size="xs">Femmes ({patientAnalytics.genderDistribution.female})</Text>
                    </Group>
                  </Group>
                </Grid.Col>
              </Grid>
            </Card>
          )}
        </>
      )}
    </Stack>
  );
};

export default DashboardWidgets;
