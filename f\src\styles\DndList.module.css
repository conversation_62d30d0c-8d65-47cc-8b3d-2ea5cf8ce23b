/************************/
.root {
  border-top-left-radius: var(--mantine-radius-xl);
  border-bottom-left-radius: var(--mantine-radius-xl);
  padding-left: 4px;

  /* The following styles will be applied only when button is disabled */
  &[data-disabled] {
    /* You can use Mantine PostCSS mixins inside data attributes */
    @mixin light {
      border: 1px solid var(--mantine-color-gray-2);
    }

    @mixin dark {
      border: 1px solid var(--mantine-color-dark-4);
    }

    /* You can target child elements that are inside .root[data-disabled] */
    & .section[data-position="left"] {
      opacity: 0.6;
    }
  }
}

.section {
  /* Apply styles only to left section */
  &[data-position="left"] {
    --section-size: calc(var(--button-height) - 8px);

    background-color: var(--mantine-color-body);
    color: var(--mantine-color-text);
    height: var(--section-size);
    width: var(--section-size);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--mantine-radius-xl);
  }

  &[data-position="right"] {
    @mixin rtl {
      transform: rotate(180deg);
    }
  }
}
.itemIcon {
  padding: var(--mantine-spacing-xs);
  margin-right: var(--mantine-spacing-md);
}
.itemTitle {
  margin-bottom: calc(var(--mantine-spacing-xs) / 2);
}

/* .textInput {
  &::placeholder {
    color: var(--mantine-color-red-5);
    
  }
} */