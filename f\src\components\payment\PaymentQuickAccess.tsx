/**
 * Payment Quick Access Modal
 * Provides quick access to payment collections and account balances from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  SimpleGrid,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconCash,
  IconReceipt,
  IconCreditCard,
  IconChartPie,
  IconExternalLink,
  IconRefresh,
  IconPlus,
  IconEye,
  IconEdit,
} from '@tabler/icons-react';
import { usePayment } from '@/hooks/usePayment';
import PaymentWidgets from './PaymentWidgets';

// Import existing payment components
import Encaissements from '@/app/(dashboard)/payment/Encaissements';
import EtatDuComptGeneral from '@/app/(dashboard)/payment/EtatDuComptGeneral';

interface PaymentQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  patientId?: string;
  patientName?: string;
  defaultTab?: 'dashboard' | 'collections' | 'balances' | 'analytics';
  dateRange?: { start: string; end: string };
  onNavigateToFullPage?: () => void;
}

const PaymentQuickAccess: React.FC<PaymentQuickAccessProps> = ({
  opened,
  onClose,
  patientId,
  patientName,
  defaultTab = 'dashboard',
  dateRange,
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [collectionFilter, setCollectionFilter] = useState<string>('all');

  const {
    paymentCollections,
    accountBalances,
    paymentAnalytics,
    loading,
    refreshAll,
    getPatientPaymentStats,
    getCollectionTrends,
    getOverdueAccounts,
  } = usePayment({ 
    patientId, 
    dateRange, 
    autoFetch: opened,
    dataTypes: ['collections', 'balances', 'analytics']
  });

  const patientStats = patientId ? getPatientPaymentStats(patientId) : null;
  const collectionTrends = getCollectionTrends();
  const overdueAccounts = getOverdueAccounts();

  const handleRefresh = () => {
    refreshAll(patientId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <PaymentWidgets 
              patientId={patientId}
              dateRange={dateRange}
              compact={false}
              showAnalytics={true}
            />
          </Stack>
        );

      case 'collections':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconCash size={20} />
                  <Text fw={600}>Encaissements</Text>
                  <Badge color="green">{paymentCollections.length}</Badge>
                </Group>
                <Group gap="xs">
                  <Select
                    value={collectionFilter}
                    onChange={(value) => setCollectionFilter(value || 'all')}
                    data={[
                      { value: 'all', label: 'Tous' },
                      { value: 'completed', label: 'Complétés' },
                      { value: 'pending', label: 'En attente' },
                      { value: 'cash', label: 'Espèces' },
                      { value: 'card', label: 'Carte' },
                    ]}
                    size="xs"
                  />
                  <ActionIcon variant="light" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {paymentCollections
                    .filter(collection => {
                      if (collectionFilter === 'all') return true;
                      if (collectionFilter === 'completed' || collectionFilter === 'pending') {
                        return collection.status === collectionFilter;
                      }
                      return collection.payment_method === collectionFilter;
                    })
                    .map((collection) => (
                    <Card key={collection.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{collection.patient_name}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(collection.date).toLocaleDateString()} - {collection.payeur}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Mode: {collection.payment_method} | Dr. {collection.doctor_name}
                          </Text>
                          {collection.reference && (
                            <Text size="xs" c="dimmed">Réf: {collection.reference}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600} c="green">{collection.amount_collected}€</Text>
                            <Text size="xs" c="dimmed">Consommé: {collection.amount_consumed}€</Text>
                            {collection.remaining_balance > 0 && (
                              <Text size="xs" c="orange">Reste: {collection.remaining_balance}€</Text>
                            )}
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              collection.status === 'completed' ? 'green' : 
                              collection.status === 'pending' ? 'yellow' : 
                              collection.status === 'cancelled' ? 'red' : 'gray'
                            }
                          >
                            {collection.status}
                          </Badge>
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {paymentCollections.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun encaissement disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'balances':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconReceipt size={20} />
                  <Text fw={600}>État des Comptes</Text>
                  <Badge color="orange">{accountBalances.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {accountBalances.map((balance) => (
                    <Card key={balance.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{balance.patient_name}</Text>
                          <Text size="xs" c="dimmed">
                            Dernière mise à jour: {new Date(balance.updated_at).toLocaleDateString()}
                          </Text>
                          {balance.last_payment_date && (
                            <Text size="xs" c="dimmed">
                              Dernier paiement: {new Date(balance.last_payment_date).toLocaleDateString()} 
                              ({balance.last_payment_amount}€)
                            </Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600}>Dû: {balance.total_due}€</Text>
                            <Text size="xs" c="green">Encaissé: {balance.total_collected}€</Text>
                            <Text size="xs" c={balance.remaining_balance > 0 ? "orange" : "green"}>
                              Reste: {balance.remaining_balance}€
                            </Text>
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              balance.account_status === 'current' ? 'green' : 
                              balance.account_status === 'overdue' ? 'red' : 
                              balance.account_status === 'paid' ? 'blue' : 'gray'
                            }
                          >
                            {balance.account_status}
                          </Badge>
                        </Group>
                      </Group>
                      {balance.payment_history.length > 0 && (
                        <div style={{ marginTop: '8px' }}>
                          <Text size="xs" c="dimmed" mb="xs">Historique récent:</Text>
                          <Stack gap="xs">
                            {balance.payment_history.slice(0, 2).map((payment) => (
                              <Group key={payment.id} justify="space-between">
                                <Text size="xs">{new Date(payment.date).toLocaleDateString()}</Text>
                                <Text size="xs">{payment.amount}€ ({payment.method})</Text>
                              </Group>
                            ))}
                          </Stack>
                        </div>
                      )}
                    </Card>
                  ))}
                  {accountBalances.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun compte disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'analytics':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconChartPie size={20} />
                  <Text fw={600}>Analytiques de Paiement</Text>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                {paymentAnalytics ? (
                  <SimpleGrid cols={2} spacing="md">
                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Répartition par Mode</Text>
                      <Stack gap="xs">
                        {paymentAnalytics.payment_methods_breakdown.map((method) => (
                          <Group key={method.method} justify="space-between">
                            <Text size="xs" tt="capitalize">{method.method}</Text>
                            <Badge size="xs" color="blue">
                              {method.count} ({method.percentage}%)
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Collections Journalières</Text>
                      <Stack gap="xs">
                        {paymentAnalytics.daily_collections.slice(0, 5).map((daily) => (
                          <Group key={daily.date} justify="space-between">
                            <Text size="xs">{new Date(daily.date).toLocaleDateString()}</Text>
                            <Badge size="xs" color="green">
                              {daily.total_amount}€ ({daily.transaction_count})
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Top Patients Payeurs</Text>
                      <Stack gap="xs">
                        {paymentAnalytics.top_paying_patients.map((patient) => (
                          <Group key={patient.patient_id} justify="space-between">
                            <Text size="xs">{patient.patient_name}</Text>
                            <Badge size="xs" color="purple">
                              {patient.total_paid}€ ({patient.payment_count})
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Comptes en Retard</Text>
                      <Stack gap="xs">
                        {paymentAnalytics.overdue_accounts.map((overdue) => (
                          <Group key={overdue.patient_id} justify="space-between">
                            <Text size="xs">{overdue.patient_name}</Text>
                            <Badge size="xs" color="red">
                              {overdue.overdue_amount}€ ({overdue.days_overdue}j)
                            </Badge>
                          </Group>
                        ))}
                        {paymentAnalytics.overdue_accounts.length === 0 && (
                          <Text size="xs" c="dimmed" ta="center">Aucun compte en retard</Text>
                        )}
                      </Stack>
                    </Card>
                  </SimpleGrid>
                ) : (
                  <Text size="sm" c="dimmed" ta="center" p="xl">
                    Aucune donnée analytique disponible
                  </Text>
                )}
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  const totalCollections = paymentCollections.reduce((sum, c) => sum + c.amount_collected, 0);
  const totalOutstanding = accountBalances.reduce((sum, b) => sum + b.remaining_balance, 0);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconCash size={20} />
          <Text fw={600}>Gestion des Paiements</Text>
          {patientName && <Text c="dimmed">- {patientName}</Text>}
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Accès rapide aux encaissements et comptes
          </Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconCash size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Encaissements</Text>
                <Text size="sm" fw={600}>{totalCollections.toLocaleString()}€</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconReceipt size={16} color="orange" />
              <div>
                <Text size="xs" c="dimmed">Soldes</Text>
                <Text size="sm" fw={600}>{totalOutstanding.toLocaleString()}€</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconCreditCard size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Transactions</Text>
                <Text size="sm" fw={600}>{paymentCollections.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconChartPie size={16} color="purple" />
              <div>
                <Text size="xs" c="dimmed">Taux</Text>
                <Text size="sm" fw={600}>{paymentAnalytics?.collection_rate.toFixed(1) || 0}%</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconChartPie size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab 
              value="collections" 
              leftSection={<IconCash size={16} />}
              rightSection={<Badge size="xs" color="green">{paymentCollections.length}</Badge>}
            >
              Encaissements
            </Tabs.Tab>
            <Tabs.Tab 
              value="balances" 
              leftSection={<IconReceipt size={16} />}
              rightSection={<Badge size="xs" color="orange">{accountBalances.length}</Badge>}
            >
              Comptes
            </Tabs.Tab>
            <Tabs.Tab value="analytics" leftSection={<IconChartPie size={16} />}>
              Analytiques
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default PaymentQuickAccess;
