"use client";

import React, { useState } from 'react';
import {
  Button,
  TextInput,
  Group,
  ActionIcon,
  Modal,
  Stack,
  Text,
  Card,
  Textarea,
  Grid,
  Select,
  Tabs,
  ColorPicker,
  NumberInput,
  Checkbox,
  
  Divider
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconHeart,
  IconStethoscope,
  IconColorPicker,
  IconUsers,
  
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { DataTable } from 'mantine-datatable';

// Types
interface ProtocoleSoin {
  id: string;
  titre: string;
  description: string;
}

interface ActeSoin {
  id: string;
  titre: string;
  groupeActe: string;
  couleur: string;
  acte: string;
  motif: string;
}

interface GroupeActe {
  id: string;
  titre: string;
  couleur: string;
}

interface Procedure {
  id: string;
  code: string;
  nom: string;
  honoraire: number;
  servicesDesignes: string;
  codeNGAP: string;
  codeCCAM: string;
  tnr: string;
  modalite: string;
  remboursable: boolean;
}

interface Acte {
  id: string;
  code: string;
  description: string;
  duree: number;
  couleur: string;
  couleurRayee: string;
  agendaParDefaut: string;
  servicesDesignes: string;
  couleurSombre: boolean;
}

interface Modalite {
  id: string;
  valeur: string;
  description: string;
}

// Types pour les formulaires
interface ProtocoleFormData {
  titre: string;
  groupes: string[];
}

interface ActeSoinFormData {
  titre: string;
  groupeActe: string;
  acte: string;
  motif: string;
  couleur: string;
}

interface GroupeActeFormData {
  titre: string;
  couleur: string;
}

interface ProcedureFormData {
  code: string;
  nom: string;
  honoraire: number;
  servicesDesignes: string;
  codeNGAP: string;
  codeCCAM: string;
  tnr: string;
  modalite: string;
  remboursable: boolean;
}

interface ActeFormData {
  code: string;
  description: string;
  duree: number;
  couleur: string;
  couleurRayee: string;
  agendaParDefaut: string;
  servicesDesignes: string;
  couleurSombre: boolean;
}

interface ModaliteFormData {
  valeur: string;
  description: string;
}

const PlanDeSoins = () => {
  // États pour les onglets et modales
  const [activeTab, setActiveTab] = useState('general');
  const [protocoleModalOpened, { open: openProtocoleModal, close: closeProtocoleModal }] = useDisclosure(false);
  const [acteSoinModalOpened, { open: openActeSoinModal, close: closeActeSoinModal }] = useDisclosure(false);
  const [groupeActeModalOpened, { open: openGroupeActeModal, close: closeGroupeActeModal }] = useDisclosure(false);
  const [procedureModalOpened, { open: openProcedureModal, close: closeProcedureModal }] = useDisclosure(false);
  const [acteModalOpened, { open: openActeModal, close: closeActeModal }] = useDisclosure(false);
  const [modaliteModalOpened, { open: openModaliteModal, close: closeModaliteModal }] = useDisclosure(false);

  // États pour les données
  const [searchQuery, setSearchQuery] = useState('');
  const [protocolesSoin, setProtocolesSoin] = useState<ProtocoleSoin[]>([]);
  const [actesSoin, setActesSoin] = useState<ActeSoin[]>([]);
  const [groupesActe, setGroupesActe] = useState<GroupeActe[]>([]);
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [actes, setActes] = useState<Acte[]>([]);
  const [modalites, setModalites] = useState<Modalite[]>([]);

  // Formulaires
  const protocoleForm = useForm<ProtocoleFormData>({
    initialValues: {
      titre: '',
      groupes: []
    },
    validate: {
      titre: (value) => (!value ? 'Le titre est requis' : null)
    }
  });

  const acteSoinForm = useForm<ActeSoinFormData>({
    initialValues: {
      titre: '',
      groupeActe: '',
      acte: '',
      motif: '',
      couleur: '#3b82f6'
    },
    validate: {
      titre: (value) => (!value ? 'Le titre est requis' : null),
      groupeActe: (value) => (!value ? 'Le groupe d\'acte est requis' : null),
      acte: (value) => (!value ? 'L\'acte est requis' : null),
      motif: (value) => (!value ? 'Le motif est requis' : null)
    }
  });

  const groupeActeForm = useForm<GroupeActeFormData>({
    initialValues: {
      titre: '',
      couleur: '#3b82f6'
    },
    validate: {
      titre: (value) => (!value ? 'Le titre est requis' : null)
    }
  });

  const procedureForm = useForm<ProcedureFormData>({
    initialValues: {
      code: '',
      nom: '',
      honoraire: 0,
      servicesDesignes: '',
      codeNGAP: '',
      codeCCAM: '',
      tnr: '',
      modalite: '',
      remboursable: false
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      nom: (value) => (!value ? 'Le nom est requis' : null)
    }
  });

  const acteForm = useForm<ActeFormData>({
    initialValues: {
      code: '',
      description: '',
      duree: 15,
      couleur: '#3b82f6',
      couleurRayee: '#ef4444',
      agendaParDefaut: '',
      servicesDesignes: '',
      couleurSombre: false
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      description: (value) => (!value ? 'La description est requise' : null)
    }
  });

  const modaliteForm = useForm<ModaliteFormData>({
    initialValues: {
      valeur: '',
      description: ''
    },
    validate: {
      valeur: (value) => (!value ? 'La valeur est requise' : null)
    }
  });

  // Gestionnaires pour les onglets
  const handleTabChange = (value: string | null) => {
    if (value) setActiveTab(value);
  };

  // Gestionnaires de soumission
  const handleProtocoleSubmit = (values: ProtocoleFormData) => {
    const newProtocole: ProtocoleSoin = {
      id: Date.now().toString(),
      titre: values.titre,
      description: ''
    };
    setProtocolesSoin(prev => [...prev, newProtocole]);
    closeProtocoleModal();
    protocoleForm.reset();
  };

  const handleActeSoinSubmit = (values: ActeSoinFormData) => {
    const newActeSoin: ActeSoin = {
      id: Date.now().toString(),
      ...values
    };
    setActesSoin(prev => [...prev, newActeSoin]);
    closeActeSoinModal();
    acteSoinForm.reset();
  };

  const handleGroupeActeSubmit = (values: GroupeActeFormData) => {
    const newGroupeActe: GroupeActe = {
      id: Date.now().toString(),
      ...values
    };
    setGroupesActe(prev => [...prev, newGroupeActe]);
    closeGroupeActeModal();
    groupeActeForm.reset();
  };

  const handleProcedureSubmit = (values: ProcedureFormData) => {
    const newProcedure: Procedure = {
      id: Date.now().toString(),
      ...values
    };
    setProcedures(prev => [...prev, newProcedure]);
    closeProcedureModal();
    procedureForm.reset();
  };

  const handleActeSubmit = (values: ActeFormData) => {
    const newActe: Acte = {
      id: Date.now().toString(),
      ...values
    };
    setActes(prev => [...prev, newActe]);
    closeActeModal();
    acteForm.reset();
  };

  const handleModaliteSubmit = (values: ModaliteFormData) => {
    const newModalite: Modalite = {
      id: Date.now().toString(),
      ...values
    };
    setModalites(prev => [...prev, newModalite]);
    closeModaliteModal();
    modaliteForm.reset();
  };

  // Fonction pour obtenir le bouton d'action selon l'onglet actif
  const getActionButton = () => {
    switch (activeTab) {
      case 'protocoles-soin':
        return (
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={openProtocoleModal}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Protocole de soins
          </Button>
        );
      case 'actes-soin':
        return (
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={openActeSoinModal}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Acte de soins
          </Button>
        );
      case 'groupes-actes':
        return (
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={openGroupeActeModal}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Groupe d&apos;acte
          </Button>
        );
      default:
        return null;
    }
  };

  // Colonnes pour les tables
  const protocoleColumns = [
    { accessor: 'titre', title: 'Titre' },
    { accessor: 'description', title: 'Description' }
  ];

  const acteSoinColumns = [
    { accessor: 'titre', title: 'Titre' },
    { accessor: 'groupeActe', title: 'Groupe d\'acte' },
    { accessor: 'couleur', title: 'Couleur', render: (record: ActeSoin) => (
      <div className="flex items-center gap-2">
        <div
          className="w-4 h-4 rounded"
          style={{ backgroundColor: record.couleur }}
        />
      </div>
    )},
    { accessor: 'acte', title: 'Acte' },
    { accessor: 'motif', title: 'Motif' }
  ];

  const groupeActeColumns = [
    { accessor: 'titre', title: 'Titre' },
    { accessor: 'couleur', title: 'Couleur', render: (record: GroupeActe) => (
      <div className="flex items-center gap-2">
        <div
          className="w-4 h-4 rounded"
          style={{ backgroundColor: record.couleur }}
        />
      </div>
    )}
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconHeart size={32} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-800">Paramétrage des plan de soins</h1>
        </div>
        {getActionButton()}
      </div>

      {/* Onglets principaux */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tabs.List>
            <Tabs.Tab value="general">Général</Tabs.Tab>
            <Tabs.Tab value="protocoles-soin">Protocoles de soin</Tabs.Tab>
            <Tabs.Tab value="actes-soin">Actes de soin</Tabs.Tab>
            <Tabs.Tab value="groupes-actes">Groupes d&apos;actes</Tabs.Tab>
          </Tabs.List>

          {/* Onglet Général */}
          <Tabs.Panel value="general" pt="md">
            <div className="flex items-center justify-between mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-96"
              />
            </div>

            <DataTable
              columns={[
                { accessor: 'titre', title: 'Titre' },
                { accessor: 'description', title: 'Description' }
              ]}
              records={[]}
              noRecordsText="Aucun élément trouvé."
              minHeight={200}
            />
          </Tabs.Panel>

          {/* Onglet Protocoles de soin */}
          <Tabs.Panel value="protocoles-soin" pt="md">
            <div className="flex items-center justify-between mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-96"
              />
            </div>

            <DataTable
              columns={protocoleColumns}
              records={protocolesSoin}
              noRecordsText="Aucun élément trouvé."
              minHeight={200}
            />
          </Tabs.Panel>

          {/* Onglet Actes de soin */}
          <Tabs.Panel value="actes-soin" pt="md">
            <div className="flex items-center justify-between mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-96"
              />
            </div>

            <DataTable
              columns={acteSoinColumns}
              records={actesSoin}
              noRecordsText="Aucun élément trouvé."
              minHeight={200}
            />
          </Tabs.Panel>

          {/* Onglet Groupes d'actes */}
          <Tabs.Panel value="groupes-actes" pt="md">
            <div className="flex items-center justify-between mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-96"
              />
            </div>

            <DataTable
              columns={groupeActeColumns}
              records={groupesActe}
              noRecordsText="Aucun élément trouvé."
              minHeight={200}
            />
          </Tabs.Panel>
        </Tabs>
      </Card>

      {/* Modal Nouveau protocole */}
      <Modal
        opened={protocoleModalOpened}
        onClose={closeProtocoleModal}
        title={
          <div className="flex items-center gap-2">
            <IconHeart size={20} className="text-white" />
            <Text fw={600} className="text-white">Nouveau protocole</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={protocoleForm.onSubmit(handleProtocoleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Titre du protocole de soins"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...protocoleForm.getInputProps('titre')}
            />

            <div className="bg-blue-50 p-4 rounded-md">
              <div className="flex items-center gap-2 mb-3">
                <IconUsers size={20} className="text-blue-500" />
                <Text fw={600} className="text-blue-700">Groupes</Text>
              </div>
              <Text size="sm" className="text-gray-600 mb-2">Aucun élément trouvé.</Text>
            </div>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeProtocoleModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer et quitter
              </Button>
              <Button type="submit" color="blue">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Acte de soins */}
      <Modal
        opened={acteSoinModalOpened}
        onClose={closeActeSoinModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-white" />
            <Text fw={600} className="text-white">Acte de soins</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={acteSoinForm.onSubmit(handleActeSoinSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Titre"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...acteSoinForm.getInputProps('titre')}
            />

            <Select
              label="Groupe d'acte"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              data={groupesActe.map(g => ({ value: g.id, label: g.titre }))}
              {...acteSoinForm.getInputProps('groupeActe')}
            />

            <div className="flex gap-2">
              <Select
                label="Acte"
                placeholder=""
                required
                styles={{ label: { color: 'red' } }}
                data={actes.map(a => ({ value: a.id, label: a.description }))}
                className="flex-1"
                {...acteSoinForm.getInputProps('acte')}
              />
              <ActionIcon
                size="lg"
                className="mt-6 bg-blue-500 hover:bg-blue-600"
                onClick={openActeModal}
              >
                <IconPlus size={16} />
              </ActionIcon>
            </div>

            <div className="flex gap-2">
              <Select
                label="Motif"
                placeholder=""
                required
                styles={{ label: { color: 'red' } }}
                data={[]}
                className="flex-1"
                {...acteSoinForm.getInputProps('motif')}
              />
              <div className="flex items-center mt-6">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-gray-500 rounded-full" />
                </div>
              </div>
            </div>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeActeSoinModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Groupe d'acte */}
      <Modal
        opened={groupeActeModalOpened}
        onClose={closeGroupeActeModal}
        title={
          <div className="flex items-center gap-2">
            <IconUsers size={20} className="text-white" />
            <Text fw={600} className="text-white">Groupe d&apos;acte</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={groupeActeForm.onSubmit(handleGroupeActeSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Titre"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...groupeActeForm.getInputProps('titre')}
            />

            <div>
              <Text size="sm" fw={500} mb={5}>Couleur</Text>
              <div className="flex items-center gap-2">
                <IconColorPicker size={16} />
                <ColorPicker
                  format="hex"
                  {...groupeActeForm.getInputProps('couleur')}
                />
              </div>
            </div>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeGroupeActeModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Procédure */}
      <Modal
        opened={procedureModalOpened}
        onClose={closeProcedureModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-white" />
            <Text fw={600} className="text-white">Procédure</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={procedureForm.onSubmit(handleProcedureSubmit)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...procedureForm.getInputProps('code')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Nom"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...procedureForm.getInputProps('nom')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Honoraire"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...procedureForm.getInputProps('honoraire')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Services désignés"
                  placeholder=""
                  data={[]}
                  {...procedureForm.getInputProps('servicesDesignes')}
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code NGAP"
                  placeholder=""
                  {...procedureForm.getInputProps('codeNGAP')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Code CCAM"
                  placeholder=""
                  {...procedureForm.getInputProps('codeCCAM')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="TNR"
                  placeholder=""
                  {...procedureForm.getInputProps('tnr')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <div className="flex gap-2">
                  <Select
                    label="Modalité"
                    placeholder=""
                    data={modalites.map(m => ({ value: m.id, label: m.valeur }))}
                    className="flex-1"
                    {...procedureForm.getInputProps('modalite')}
                  />
                  <ActionIcon
                    size="lg"
                    className="mt-6 bg-blue-500 hover:bg-blue-600"
                    onClick={openModaliteModal}
                  >
                    <IconPlus size={16} />
                  </ActionIcon>
                </div>
              </Grid.Col>
            </Grid>

            <Checkbox
              label="Remboursable"
              {...procedureForm.getInputProps('remboursable', { type: 'checkbox' })}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeProcedureModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Actes */}
      <Modal
        opened={acteModalOpened}
        onClose={closeActeModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-white" />
            <Text fw={600} className="text-white">Actes</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={acteForm.onSubmit(handleActeSubmit)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...acteForm.getInputProps('code')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Textarea
                  label="Description"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...acteForm.getInputProps('description')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <div className="flex items-center gap-2">
                  <NumberInput
                    label="Durée (min)"
                    placeholder=""
                    min={1}
                    defaultValue={15}
                    className="flex-1"
                    {...acteForm.getInputProps('duree')}
                  />
                  <div className="flex items-center gap-2 mt-6">
                    <IconColorPicker size={16} />
                    <Text size="sm">Couleur</Text>
                  </div>
                </div>
              </Grid.Col>
              <Grid.Col span={6}>
                <div className="flex items-center gap-2">
                  <IconColorPicker size={16} />
                  <Text size="sm">Couleur rayée</Text>
                </div>
              </Grid.Col>
            </Grid>

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <div className="flex gap-2">
                  <Select
                    label="Agenda par défaut"
                    placeholder=""
                    data={[]}
                    className="flex-1"
                    {...acteForm.getInputProps('agendaParDefaut')}
                  />
                  <ActionIcon
                    size="lg"
                    className="mt-6 bg-blue-500 hover:bg-blue-600"
                    onClick={openProcedureModal}
                  >
                    <IconPlus size={16} />
                  </ActionIcon>
                </div>
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Services désignés"
                  placeholder=""
                  data={[]}
                  {...acteForm.getInputProps('servicesDesignes')}
                />
              </Grid.Col>
            </Grid>

            <Checkbox
              label="Couleur sombre"
              {...acteForm.getInputProps('couleurSombre', { type: 'checkbox' })}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeActeModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Ajouter Modalité */}
      <Modal
        opened={modaliteModalOpened}
        onClose={closeModaliteModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Modalité</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={modaliteForm.onSubmit(handleModaliteSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Valeur"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...modaliteForm.getInputProps('valeur')}
            />

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...modaliteForm.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeModaliteModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default PlanDeSoins;
