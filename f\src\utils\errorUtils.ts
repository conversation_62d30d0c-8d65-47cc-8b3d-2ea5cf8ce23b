// Define types for better error handling
interface ErrorResponse {
  data?: string | Record<string, unknown> | { detail?: string; message?: string; error?: string };
  status?: number;
}

interface ApiError {
  response?: ErrorResponse;
  message?: string;
}

export type ErrorInput = ApiError | Error | string | null | undefined;

/**
 * Extracts a detailed error message from various error object formats
 *
 * @param error - The error object from a try/catch block
 * @param defaultMessage - Default message to show if no error details can be extracted
 * @returns A string with the most detailed error message available
 */
export const extractErrorMessage = (error: ErrorInput, defaultMessage: string = 'An error occurred'): string => {
  if (!error) return defaultMessage;

  // Handle string errors directly
  if (typeof error === 'string') {
    return error;
  }

  // Type guard to check if error has response property
  const hasResponse = (err: ErrorInput): err is ApiError => {
    return typeof err === 'object' && err !== null && 'response' in err;
  };

  // Type guard to check if error has message property
  const hasMessage = (err: ErrorInput): err is Error | ApiError => {
    return typeof err === 'object' && err !== null && 'message' in err;
  };

  if (hasResponse(error)) {
    // Handle string error responses
    if (error.response?.data && typeof error.response.data === 'string') {
      return error.response.data;
    }

    // Handle field-specific validation errors (common in Django REST Framework)
    if (error.response?.data && typeof error.response.data === 'object' && error.response.data !== null) {
      const data = error.response.data as Record<string, unknown>;

      // Check if it's not a standard error response
      if (!('detail' in data) && !('message' in data) && !('error' in data)) {
        const fieldErrors = Object.entries(data)
          .map(([field, msgs]) => {
            if (Array.isArray(msgs)) {
              return `${field}: ${msgs.join(', ')}`;
            } else if (typeof msgs === 'string') {
              return `${field}: ${msgs}`;
            } else if (msgs && typeof msgs === 'object') {
              return `${field}: ${JSON.stringify(msgs)}`;
            }
            return null;
          })
          .filter(Boolean)
          .join('\n');

        if (fieldErrors) {
          return `Validation errors:\n${fieldErrors}`;
        }
      }

      // Try to extract standard error messages
      const errorData = data as { detail?: string; message?: string; error?: string };
      if (errorData.detail) return errorData.detail;
      if (errorData.message) return errorData.message;
      if (errorData.error) return errorData.error;
    }
  }

  // Try to get message from Error object
  if (hasMessage(error) && error.message) {
    return error.message;
  }

  return defaultMessage;
};

/**
 * Logs an error with detailed information to the console
 *
 * @param context - The context where the error occurred (e.g., 'updating user')
 * @param error - The error object
 */
export const logError = (context: string, error: ErrorInput): void => {
  console.error(`Error ${context}:`, error);

  const errorMessage = extractErrorMessage(error);
  console.log(`Detailed error message (${context}):`, errorMessage);

  // Type guard to check if error has response property
  const hasResponse = (err: ErrorInput): err is ApiError => {
    return typeof err === 'object' && err !== null && 'response' in err;
  };

  // Log additional details if available
  if (hasResponse(error)) {
    if (error.response?.status) {
      console.log(`Status code: ${error.response.status}`);
    }

    if (error.response?.data) {
      console.log('Response data:', error.response.data);
    }
  }
};
