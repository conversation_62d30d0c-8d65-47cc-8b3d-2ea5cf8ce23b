/**
 * Test Django Backend Staff Endpoints
 * Testing actual backend to discover real doctor and assistant names
 */

const http = require('http');

console.log('🧪 TESTING DJANGO BACKEND STAFF ENDPOINTS');
console.log('=' * 60);

// Test configuration
const API_BASE = 'http://127.0.0.1:8000/api';

// Make HTTP request function
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonData,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: data,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

// Test 1: Test Available Endpoints
async function testAvailableEndpoints() {
    console.log('\n🔍 TEST 1: TESTING AVAILABLE DJANGO ENDPOINTS');
    console.log('-'.repeat(50));
    
    const endpoints = [
        '/users/',
        '/users/doctors/',
        '/users/patients/',
        '/appointments/',
        '/appointments/pauses/'
    ];
    
    const results = {};
    
    for (const endpoint of endpoints) {
        try {
            console.log(`Testing ${endpoint}...`);
            const response = await makeRequest(`${API_BASE}${endpoint}`);
            
            results[endpoint] = {
                status: response.status,
                available: response.status < 400,
                dataType: typeof response.data,
                hasResults: response.data?.results ? response.data.results.length : 0,
                error: response.status >= 400 ? response.statusText : null
            };
            
            if (response.status < 400) {
                console.log(`  ✅ ${endpoint}: ${response.status} - Available`);
                if (response.data?.results) {
                    console.log(`    📊 Found ${response.data.results.length} items`);
                }
            } else {
                console.log(`  ❌ ${endpoint}: ${response.status} - ${response.statusText}`);
            }
        } catch (error) {
            console.log(`  ❌ ${endpoint}: Connection Error - ${error.message}`);
            results[endpoint] = {
                status: 0,
                available: false,
                error: error.message
            };
        }
    }
    
    return results;
}

// Test 2: Test Doctors Endpoint
async function testDoctorsEndpoint() {
    console.log('\n🔍 TEST 2: TESTING DOCTORS ENDPOINT');
    console.log('-'.repeat(50));
    
    try {
        const response = await makeRequest(`${API_BASE}/users/doctors/`);
        
        console.log(`Status: ${response.status}`);
        
        if (response.status < 400) {
            console.log('✅ Doctors endpoint is working!');
            
            const doctors = response.data?.results || response.data || [];
            console.log(`📊 Found ${Array.isArray(doctors) ? doctors.length : 'unknown'} doctors`);
            
            if (Array.isArray(doctors) && doctors.length > 0) {
                console.log('\n👨‍⚕️ DOCTORS FOUND:');
                doctors.forEach((doctor, index) => {
                    console.log(`${index + 1}. Doctor:`);
                    console.log(`   ID: ${doctor.id}`);
                    console.log(`   Name: ${doctor.first_name} ${doctor.last_name}`);
                    console.log(`   Email: ${doctor.email}`);
                    console.log(`   User Type: ${doctor.user_type}`);
                    console.log(`   Active: ${doctor.is_active}`);
                    if (doctor.assistants && doctor.assistants.length > 0) {
                        console.log(`   Assistants: ${doctor.assistants.length}`);
                        doctor.assistants.forEach((assistant, i) => {
                            console.log(`     ${i + 1}. ${assistant.first_name} ${assistant.last_name} (${assistant.email})`);
                        });
                    }
                    console.log('');
                });
                
                return doctors;
            } else {
                console.log('⚠️ No doctors found in response');
                return [];
            }
        } else {
            console.log(`❌ Doctors endpoint error: ${response.status} - ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Error testing doctors endpoint: ${error.message}`);
        return null;
    }
}

// Test 3: Test Users Endpoint for Assistants
async function testUsersEndpoint() {
    console.log('\n🔍 TEST 3: TESTING USERS ENDPOINT FOR ASSISTANTS');
    console.log('-'.repeat(50));
    
    try {
        const response = await makeRequest(`${API_BASE}/users/`);
        
        console.log(`Status: ${response.status}`);
        
        if (response.status < 400) {
            console.log('✅ Users endpoint is working!');
            
            const users = response.data?.results || response.data || [];
            console.log(`📊 Found ${Array.isArray(users) ? users.length : 'unknown'} users`);
            
            if (Array.isArray(users) && users.length > 0) {
                // Filter for assistants
                const assistants = users.filter(user => user.user_type === 'assistant');
                const doctors = users.filter(user => user.user_type === 'doctor');
                
                console.log(`\n👨‍⚕️ DOCTORS FROM USERS ENDPOINT: ${doctors.length}`);
                doctors.forEach((doctor, index) => {
                    console.log(`${index + 1}. Dr. ${doctor.first_name} ${doctor.last_name} (${doctor.email})`);
                    console.log(`   ID: ${doctor.id}`);
                    console.log(`   Active: ${doctor.is_active}`);
                });
                
                console.log(`\n👩‍💼 ASSISTANTS FOUND: ${assistants.length}`);
                assistants.forEach((assistant, index) => {
                    console.log(`${index + 1}. ${assistant.first_name} ${assistant.last_name} (${assistant.email})`);
                    console.log(`   ID: ${assistant.id}`);
                    console.log(`   Assigned Doctor: ${assistant.assigned_doctor || 'None'}`);
                    console.log(`   Active: ${assistant.is_active}`);
                });
                
                return { doctors, assistants, all: users };
            } else {
                console.log('⚠️ No users found in response');
                return { doctors: [], assistants: [], all: [] };
            }
        } else {
            console.log(`❌ Users endpoint error: ${response.status} - ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Error testing users endpoint: ${error.message}`);
        return null;
    }
}

// Test 4: Generate Staff Options Based on Real Data
function generateStaffOptionsFromRealData(doctorsData, usersData) {
    console.log('\n🔍 TEST 4: GENERATING STAFF OPTIONS FROM REAL DATA');
    console.log('-'.repeat(50));
    
    const staffOptions = [];
    
    // Use doctors from doctors endpoint if available, otherwise from users endpoint
    const doctors = doctorsData || (usersData ? usersData.doctors : []);
    const assistants = usersData ? usersData.assistants : [];
    
    console.log(`Processing ${doctors.length} doctors and ${assistants.length} assistants`);
    
    // Add doctors
    doctors.forEach(doctor => {
        const firstName = doctor.first_name || '';
        const lastName = doctor.last_name || '';
        const fullName = firstName && lastName ? 
            `${firstName} ${lastName}` : 
            (firstName || lastName || doctor.email?.split('@')[0] || 'Unknown');
        
        staffOptions.push({
            label: `Dr. ${fullName}`.trim(),
            value: doctor.id,
            type: 'doctor',
            active: doctor.is_active !== false,
            source: 'backend'
        });
    });
    
    // Add assistants
    assistants.forEach(assistant => {
        const firstName = assistant.first_name || '';
        const lastName = assistant.last_name || '';
        const fullName = firstName && lastName ? 
            `${firstName} ${lastName}` : 
            (firstName || lastName || assistant.email?.split('@')[0] || 'Unknown');
        
        staffOptions.push({
            label: `${fullName} (Assistant)`.trim(),
            value: assistant.id,
            type: 'assistant',
            active: assistant.is_active !== false,
            assignedDoctor: assistant.assigned_doctor,
            source: 'backend'
        });
    });
    
    console.log('\n📋 GENERATED STAFF OPTIONS:');
    staffOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label}`);
        console.log(`   Value: ${option.value}`);
        console.log(`   Type: ${option.type}`);
        console.log(`   Active: ${option.active}`);
        if (option.assignedDoctor) {
            console.log(`   Assigned Doctor: ${option.assignedDoctor}`);
        }
        console.log('');
    });
    
    return staffOptions;
}

// Test 5: Compare with Current Hardcoded Data
function compareWithHardcodedData(realStaffOptions) {
    console.log('\n🔍 TEST 5: COMPARING WITH CURRENT HARDCODED DATA');
    console.log('-'.repeat(50));
    
    const hardcodedOptions = [
        { label: 'Dr. doctor morade', value: '0359bdc6-1235-4a31-a095-097007f0b415', type: 'doctor' },
        { label: 'Dr. ggg gggg', value: 'da8adc01-e567-491b-b027-19760707105b', type: 'doctor' },
        { label: 'Dr. test -2', value: '657d3f41-5019-42a9-8bc1-eb84948e75b8', type: 'doctor' }
    ];
    
    console.log('HARDCODED OPTIONS:');
    hardcodedOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label} (${option.value})`);
    });
    
    console.log('\nREAL BACKEND OPTIONS:');
    realStaffOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label} (${option.value})`);
    });
    
    console.log('\nCOMPARISON:');
    
    // Check if hardcoded IDs exist in real data
    hardcodedOptions.forEach(hardcoded => {
        const found = realStaffOptions.find(real => real.value === hardcoded.value);
        if (found) {
            console.log(`✅ Match found: ${hardcoded.label} -> ${found.label}`);
        } else {
            console.log(`❌ No match: ${hardcoded.label} (${hardcoded.value}) not found in backend`);
        }
    });
    
    // Check for additional real data not in hardcoded
    const additionalOptions = realStaffOptions.filter(real => 
        !hardcodedOptions.some(hardcoded => hardcoded.value === real.value)
    );
    
    if (additionalOptions.length > 0) {
        console.log('\n📈 ADDITIONAL STAFF FROM BACKEND:');
        additionalOptions.forEach((option, index) => {
            console.log(`${index + 1}. ${option.label} (${option.type})`);
        });
    }
    
    return {
        matches: hardcodedOptions.filter(hardcoded => 
            realStaffOptions.some(real => real.value === hardcoded.value)
        ),
        mismatches: hardcodedOptions.filter(hardcoded => 
            !realStaffOptions.some(real => real.value === hardcoded.value)
        ),
        additional: additionalOptions
    };
}

// Main test runner
async function runDjangoBackendTests() {
    console.log('🚀 RUNNING DJANGO BACKEND STAFF TESTS');
    console.log('='.repeat(60));
    
    try {
        // Test all endpoints
        const endpointResults = await testAvailableEndpoints();
        
        // Test doctors specifically
        const doctorsData = await testDoctorsEndpoint();
        
        // Test users for assistants
        const usersData = await testUsersEndpoint();
        
        // Generate real staff options
        const realStaffOptions = generateStaffOptionsFromRealData(doctorsData, usersData);
        
        // Compare with hardcoded data
        const comparison = compareWithHardcodedData(realStaffOptions);
        
        console.log('\n📊 DJANGO BACKEND TEST SUMMARY');
        console.log('='.repeat(40));
        console.log(`Endpoints tested: ${Object.keys(endpointResults).length}`);
        console.log(`Available endpoints: ${Object.values(endpointResults).filter(r => r.available).length}`);
        console.log(`Real doctors found: ${doctorsData ? doctorsData.length : 0}`);
        console.log(`Real assistants found: ${usersData ? usersData.assistants.length : 0}`);
        console.log(`Total real staff options: ${realStaffOptions.length}`);
        console.log(`Hardcoded matches: ${comparison.matches.length}`);
        console.log(`Additional backend staff: ${comparison.additional.length}`);
        
        console.log('\n🎯 RECOMMENDATIONS:');
        if (realStaffOptions.length > 0) {
            console.log('✅ Backend has real staff data - use it instead of hardcoded!');
            console.log('✅ Implement dynamic staff loading from backend');
            if (comparison.additional.length > 0) {
                console.log('✅ Backend has more staff than hardcoded - discover all available');
            }
        } else {
            console.log('⚠️ No real staff data found - check backend configuration');
        }
        
        return {
            endpointResults,
            doctorsData,
            usersData,
            realStaffOptions,
            comparison
        };
        
    } catch (error) {
        console.error('❌ Error running Django backend tests:', error.message);
        return null;
    }
}

// Run the tests
runDjangoBackendTests().then(results => {
    if (results) {
        console.log('\n✅ DJANGO BACKEND TESTING COMPLETE');
        console.log('Use the discovered real staff data to replace hardcoded options');
    } else {
        console.log('\n❌ DJANGO BACKEND TESTING FAILED');
        console.log('Check Django server is running on http://127.0.0.1:8000');
    }
});