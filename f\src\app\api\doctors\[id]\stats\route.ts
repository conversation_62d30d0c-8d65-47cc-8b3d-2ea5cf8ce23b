import { NextRequest, NextResponse } from 'next/server';

// Define the Doctor<PERSON>tat<PERSON> type
interface DoctorStats {
  total_patients: number;
  new_patients: number;
  upcoming_appointments: number;
  completed_appointments: number;
}

// Mock data for doctor stats
const mockDoctorStats: Record<string, DoctorStats> = {
  'e62a0736-0ad9-4f12-a63b-e67c90183231': {
    total_patients: 1245,
    new_patients: 32,
    upcoming_appointments: 78,
    completed_appointments: 45,
  },
  'a279d849-e5d5-403f-9ddf-dfa2a5e981da': {
    total_patients: 876,
    new_patients: 18,
    upcoming_appointments: 42,
    completed_appointments: 23,
  },
  '3': {
    total_patients: 654,
    new_patients: 12,
    upcoming_appointments: 35,
    completed_appointments: 28,
  }
};

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const doctorId = params.id;
    console.log(`API: Fetching stats for doctor with ID: ${doctorId}`);

    // In a real application, you would fetch this data from a database or external API
    // For now, we'll use mock data
    const stats = mockDoctorStats[doctorId];

    if (!stats) {
      console.log(`API: Stats for doctor with ID ${doctorId} not found`);
      return NextResponse.json(
        { error: 'Doctor stats not found' },
        { status: 404 }
      );
    }

    console.log(`API: Found doctor stats:`, stats);
    return NextResponse.json(stats);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
