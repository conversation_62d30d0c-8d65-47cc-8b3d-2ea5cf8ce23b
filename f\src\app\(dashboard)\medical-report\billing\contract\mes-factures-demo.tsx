'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { MesFactures } from './Mes_facuers';

export default function MesFacturesDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Recherche de factures:\nTerme: "${query.searchAll}"\nPage: ${query.page}\nLignes: ${query.limit}\nFiltres: ${Object.keys(query.filters).length} actifs\nRecherche par colonne: ${Object.keys(query.search).length} actives`);
  };

  const handleAddInvoice = () => {
    console.log('Ajouter facture');
    alert(`Création d'une nouvelle facture:\nOuverture du formulaire de saisie\nChamps: N°. Facture, Date, Bénéficiaire, Montant Total, Assurance, Mode de paiement`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export:', format);
    alert(`Export Excel des factures en cours...\nFormat: ${format.toUpperCase()}\nTéléchargement démarré`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action:', action, 'Items:', items);
    const actionLabels: { [key: string]: string } = {
      'reload': 'Actualiser'
    };
    const actionLabel = actionLabels[action] || action;
    alert(`Action: ${actionLabel}\nNombre d'éléments sélectionnés: ${items.length}\nTraitement en cours...`);
  };

  const handleEdit = (item: any) => {
    console.log('Modifier facture:', item);
    alert(`Modification de la facture:\nN°: ${item.invoiceNumber}\nBénéficiaire: ${item.beneficiary}\nMontant: ${item.totalAmount.toFixed(2)}€\nOuverture du formulaire d'édition...`);
  };

  const handlePrint = (item: any) => {
    console.log('Imprimer facture:', item);
    alert(`Impression de la facture:\nN°: ${item.invoiceNumber}\nBénéficiaire: ${item.beneficiary}\nMontant: ${item.totalAmount.toFixed(2)}€\nGénération du PDF en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onAddInvoice={handleAddInvoice}
          onExport={handleExport}
          onAction={handleAction}
          onEdit={handleEdit}
          onPrint={handlePrint}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MesFacturesLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onAddInvoice={() => console.log('Add invoice')}
          onExport={(format) => console.log('Export:', format)}
          onAction={(action, items) => console.log('Action:', action, items)}
          onEdit={(item) => console.log('Edit:', item)}
          onPrint={(item) => console.log('Print:', item)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MesFacturesWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      invoiceNumber: 'FAC-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      totalAmount: 1250.00,
      insurance: 'CPAM',
      paymentMode: 'Espèce'
    },
    {
      id: '2',
      invoiceNumber: 'FAC-002',
      date: new Date('2025-07-01'),
      beneficiary: 'DUPONT Marie',
      totalAmount: 850.00,
      insurance: 'Mutuelle XYZ',
      paymentMode: 'Carte'
    },
    {
      id: '3',
      invoiceNumber: 'FAC-003',
      date: new Date('2025-06-30'),
      beneficiary: 'BERNARD Paul',
      totalAmount: 2100.00,
      insurance: '',
      paymentMode: 'Chèque'
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    alert(`Recherche dans ${sampleItems.length} factures:\nTerme: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nFiltres actifs: ${Object.keys(query.filters).length}\nRecherche par colonne: ${Object.keys(query.search).length}`);
  };

  const handleAddInvoice = () => {
    console.log('Ajouter facture avec données');
    alert(`Nouvelle facture:\n\nProchain numéro: FAC-${String(sampleItems.length + 1).padStart(3, '0')}\nFormulaire de création ouvert\nDonnées pré-remplies disponibles\nMontant total actuel: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export avec données:', format);
    alert(`Export Excel de ${sampleItems.length} factures:\n\nContenu:\n- Total des factures: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Bénéficiaires: ${new Set(sampleItems.map(item => item.beneficiary)).size}\n- Modes de paiement: ${new Set(sampleItems.map(item => item.paymentMode)).size}\n- Période: ${new Date(Math.min(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')} - ${new Date(Math.max(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')}\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action avec données:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'reload': `Actualisation des données:\n- ${sampleItems.length} factures rechargées\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}\n- Total: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€`
    };
    
    const message = actionMessages[action] || `Action ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  const handleEdit = (item: any) => {
    console.log('Modifier avec données:', item);
    alert(`Modification de la facture ${item.invoiceNumber}:\n\nDétails actuels:\n- Bénéficiaire: ${item.beneficiary}\n- Date: ${item.date.toLocaleDateString('fr-FR')}\n- Montant: ${item.totalAmount.toFixed(2)}€\n- Assurance: ${item.insurance || 'Aucune'}\n- Mode de paiement: ${item.paymentMode}\n\nOuverture du formulaire d'édition...`);
  };

  const handlePrint = (item: any) => {
    console.log('Imprimer avec données:', item);
    alert(`Impression de la facture ${item.invoiceNumber}:\n\nContenu du document:\n- Bénéficiaire: ${item.beneficiary}\n- Date: ${item.date.toLocaleDateString('fr-FR')}\n- Montant TTC: ${item.totalAmount.toFixed(2)}€\n- Assurance: ${item.insurance || 'Particulier'}\n- Mode de paiement: ${item.paymentMode}\n\nGénération du PDF en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddInvoice={handleAddInvoice}
          onExport={handleExport}
          onAction={handleAction}
          onEdit={handleEdit}
          onPrint={handlePrint}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres
export function MesFacturesFiltersDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtres:', query);
    if (query.searchAll) {
      alert(`Recherche avancée:\nTerme: "${query.searchAll}"\nRecherche dans: N°. Facture, Bénéficiaires, Assurances, etc.`);
    }
    if (Object.keys(query.filters).length > 0) {
      alert(`Filtres appliqués:\n${Object.entries(query.filters).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`);
    }
    if (Object.keys(query.search).length > 0) {
      alert(`Recherche par colonne:\n${Object.entries(query.search).map(([key, value]) => `- ${key}: "${value}"`).join('\n')}`);
    }
  };

  const handleAddInvoice = () => {
    console.log('Ajouter avec filtres');
    alert(`Nouvelle facture:\nLes filtres actuels seront conservés après création`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddInvoice={handleAddInvoice}
          onExport={(format) => alert(`Export ${format} avec filtres appliqués`)}
          onAction={(action, items) => console.log('Action filtres:', action, items)}
          onEdit={(item) => console.log('Edit filtres:', item)}
          onPrint={(item) => console.log('Print filtres:', item)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function MesFacturesPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les factures`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={false}
          items={[]}
          total={150} // Simule 150 factures pour tester la pagination
          onQueryChange={handleQueryChange}
          onAddInvoice={() => console.log('Add pagination')}
          onExport={(format) => console.log('Export pagination:', format)}
          onAction={(action, items) => console.log('Action pagination:', action, items)}
          onEdit={(item) => console.log('Edit pagination:', item)}
          onPrint={(item) => console.log('Print pagination:', item)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MesFacturesErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleAddInvoice = () => {
    console.log('Ajouter avec validation');
    if (confirm('Êtes-vous sûr de vouloir créer une nouvelle facture ?')) {
      alert('Facture créée avec succès !');
    }
  };

  const handleEdit = (item: any) => {
    console.log('Modifier avec validation:', item);
    if (confirm(`Êtes-vous sûr de vouloir modifier la facture ${item.invoiceNumber} ?`)) {
      alert('Facture modifiée avec succès !');
    }
  };

  const handlePrint = (item: any) => {
    console.log('Imprimer avec validation:', item);
    if (confirm(`Êtes-vous sûr de vouloir imprimer la facture ${item.invoiceNumber} ?`)) {
      alert('Impression en cours...');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesFactures
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddInvoice={handleAddInvoice}
          onExport={(format) => {
            if (confirm(`Êtes-vous sûr de vouloir exporter en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onAction={(action, items) => console.log('Action avec validation:', action, items)}
          onEdit={handleEdit}
          onPrint={handlePrint}
        />
      </div>
    </MantineProvider>
  );
}
