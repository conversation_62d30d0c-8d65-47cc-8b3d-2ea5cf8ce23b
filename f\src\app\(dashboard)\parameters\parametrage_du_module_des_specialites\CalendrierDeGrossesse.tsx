"use client";

import React, { useState, useMemo } from 'react';
import {
  <PERSON>ton,
  Tabs,
  TextInput,
  Group,
  ActionIcon,
  Modal,
  Stack,
  Text,
  Badge,
  Card,
  NumberInput,
  Select,
  Switch,
  ColorPicker
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconCalendar,
  IconSettings,
  IconX
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DataTable } from 'mantine-datatable';

// Types
interface PregnancyExam {
  id: string;
  titre: string;
  motif: string;
  periode: string;
  type: 'examen' | 'marqueur';
  duree: number;
  semaine_debut: number;
  semaine_fin: number;
  couleur?: string;
  couleur_rayee?: boolean;
  agenda_defaut?: string;
  services_designes?: string;
  couleur_sombre?: boolean;
}

interface ExamFormData {
  titre: string;
  motif: string;
  duree: number;
  semaine_debut: number;
  semaine_fin: number;
}

interface ActFormData {
  code: string;
  description: string;
  duree: number;
  couleur: string;
  couleur_rayee: string;
  agenda_defaut: string;
  services_designes: string;
  couleur_sombre: boolean;
}

interface MarkerFormData {
  titre: string;
  mois: number;
}

const CalendrierDeGrossesse = () => {
  // États pour les modales
  const [examModalOpened, { open: openExamModal, close: closeExamModal }] = useDisclosure(false);
  const [actModalOpened, { open: openActModal, close: closeActModal }] = useDisclosure(false);
  const [markerModalOpened, { open: openMarkerModal, close: closeMarkerModal }] = useDisclosure(false);
  const [editMode, setEditMode] = useState(false);

  // États pour les données
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<string | null>('calendrier');
  const [currentExam, setCurrentExam] = useState<ExamFormData>({
    titre: '',
    motif: 'Consultation',
    duree: 15,
    semaine_debut: 0,
    semaine_fin: 0
  });
  const [currentAct, setCurrentAct] = useState<ActFormData>({
    code: '',
    description: '',
    duree: 15,
    couleur: '#ffffff',
    couleur_rayee: '#ffffff',
    agenda_defaut: '',
    services_designes: '',
    couleur_sombre: false
  });
  const [currentMarker, setCurrentMarker] = useState<MarkerFormData>({
    titre: '',
    mois: 0
  });

  // Données d'exemple pour les examens de grossesse
  const [pregnancyExams] = useState<PregnancyExam[]>([
    {
      id: '1',
      titre: 'Echographie N°1',
      motif: 'Echographie',
      periode: 'Du 10 S.A. au 14 S.A.',
      type: 'examen',
      duree: 30,
      semaine_debut: 10,
      semaine_fin: 14
    },
    {
      id: '2',
      titre: 'Consultation et 1e Bilan sanguin',
      motif: 'Consultation',
      periode: 'Du 11 S.A. au 15 S.A.',
      type: 'examen',
      duree: 20,
      semaine_debut: 11,
      semaine_fin: 15
    },
    {
      id: '3',
      titre: '4ème Mois',
      motif: '',
      periode: '-',
      type: 'marqueur',
      duree: 0,
      semaine_debut: 16,
      semaine_fin: 19
    },
    {
      id: '4',
      titre: 'Dépistage de la trisomie 21',
      motif: 'Dépistage',
      periode: 'Du 15 S.A. au 18 S.A.',
      type: 'examen',
      duree: 15,
      semaine_debut: 15,
      semaine_fin: 18
    },
    {
      id: '5',
      titre: 'Echographie N°2',
      motif: 'Echographie',
      periode: 'Du 22 S.A. au 24 S.A.',
      type: 'examen',
      duree: 30,
      semaine_debut: 22,
      semaine_fin: 24
    },
    {
      id: '6',
      titre: '2e Bilan sanguin (Hépatite B)',
      motif: 'Bilan sanguin',
      periode: 'Du 22 S.A. au 26 S.A.',
      type: 'examen',
      duree: 15,
      semaine_debut: 22,
      semaine_fin: 26
    },
    {
      id: '7',
      titre: '6ème Mois',
      motif: '',
      periode: '-',
      type: 'marqueur',
      duree: 0,
      semaine_debut: 24,
      semaine_fin: 27
    },
    {
      id: '8',
      titre: 'Détermination du groupe sanguin',
      motif: 'Bilan sanguin',
      periode: 'Du 30 S.A. au 34 S.A.',
      type: 'examen',
      duree: 10,
      semaine_debut: 30,
      semaine_fin: 34
    },
    {
      id: '9',
      titre: 'Echographie N°3',
      motif: 'Echographie',
      periode: 'Du 32 S.A. au 34 S.A.',
      type: 'examen',
      duree: 30,
      semaine_debut: 32,
      semaine_fin: 34
    },
    {
      id: '10',
      titre: '8ème Mois',
      motif: '',
      periode: '-',
      type: 'marqueur',
      duree: 0,
      semaine_debut: 32,
      semaine_fin: 35
    },
    {
      id: '11',
      titre: '9ème Mois',
      motif: '',
      periode: '-',
      type: 'marqueur',
      duree: 0,
      semaine_debut: 36,
      semaine_fin: 40
    }
  ]);

  // Filtrage des données
  const filteredExams = useMemo(() => {
    return pregnancyExams.filter(exam =>
      exam.titre.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exam.motif.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exam.periode.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [pregnancyExams, searchQuery]);

  // Gestionnaires pour les modales
  const handleNewExam = () => {
    setEditMode(false);
    setCurrentExam({
      titre: '',
      motif: 'Consultation',
      duree: 15,
      semaine_debut: 0,
      semaine_fin: 0
    });
    openExamModal();
  };

  const handleEditExam = (exam: PregnancyExam) => {
    setEditMode(true);
    setCurrentExam({
      titre: exam.titre,
      motif: exam.motif,
      duree: exam.duree,
      semaine_debut: exam.semaine_debut,
      semaine_fin: exam.semaine_fin
    });
    openExamModal();
  };

  const handleNewMarker = () => {
    setEditMode(false);
    setCurrentMarker({
      titre: '',
      mois: 0
    });
    openMarkerModal();
  };

  const handleNewAct = () => {
    setEditMode(false);
    setCurrentAct({
      code: '',
      description: '',
      duree: 15,
      couleur: '#ffffff',
      couleur_rayee: '#ffffff',
      agenda_defaut: '',
      services_designes: '',
      couleur_sombre: false
    });
    openActModal();
  };

  const handleSaveExam = () => {
    console.log('Sauvegarde de l&apos;examen:', currentExam);
    closeExamModal();
  };

  const handleSaveMarker = () => {
    console.log('Sauvegarde du marqueur:', currentMarker);
    closeMarkerModal();
  };

  const handleSaveAct = () => {
    console.log('Sauvegarde de l&apos;acte:', currentAct);
    closeActModal();
  };

  // Colonnes pour le tableau
  const columns = [
    {
      accessor: 'titre',
      title: 'Calendrier de grossesse - Configuration',
      width: 300,
      render: (record: PregnancyExam) => (
        <div className="flex items-center gap-2">
          {record.type === 'marqueur' ? (
            <Badge color="blue" variant="filled" size="sm">
              M
            </Badge>
          ) : (
            <Badge color="green" variant="filled" size="sm">
              E
            </Badge>
          )}
          <Text size="sm" fw={500}>{record.titre}</Text>
        </div>
      ),
    },
    {
      accessor: 'periode',
      title: 'Période',
      width: 200,
      textAlign: 'center' as const,
    },
    {
      accessor: 'actions',
      title: '',
      width: 120,
      textAlign: 'center' as const,
      render: (record: PregnancyExam) => (
        <Group gap="xs" justify="center">
          {record.type === 'marqueur' ? (
            <Badge color="blue" variant="filled" size="sm">
              M
            </Badge>
          ) : (
            <Badge color="green" variant="filled" size="sm">
              E
            </Badge>
          )}
          <ActionIcon
            variant="subtle"
            color="blue"
            size="sm"
            onClick={() => handleEditExam(record)}
          >
            <IconEdit size={16} />
          </ActionIcon>
          <ActionIcon
            variant="subtle"
            color="red"
            size="sm"
          >
            <IconTrash size={16} />
          </ActionIcon>
        </Group>
      ),
    },
  ];

  return (
    <div className="w-full">
      {/* En-tête avec boutons */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <IconCalendar size={20} />
          <Text size="lg" fw={600}>Calendrier de grossesse - Configuration</Text>
        </div>
        <Group gap="sm">
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            onClick={handleNewExam}
          >
            Nouvel examen ou acte
          </Button>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="orange"
            onClick={handleNewMarker}
          >
            Nouveau marqueur
          </Button>
        </Group>
      </div>

      {/* Onglets */}
      <Tabs value={activeTab} onChange={setActiveTab} variant="outline" radius="md">
        <Tabs.List>
          <Tabs.Tab value="calendrier" leftSection={<IconCalendar size={16} />}>
            Calendrier
          </Tabs.Tab>
          <Tabs.Tab value="general" leftSection={<IconSettings size={16} />}>
            Général
          </Tabs.Tab>
        </Tabs.List>

        {/* Contenu de l'onglet Calendrier */}
        <Tabs.Panel value="calendrier" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            {/* Barre de recherche */}
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>

            {/* Tableau */}
            <div className="bg-white rounded-lg">
              <DataTable
                withTableBorder
                borderRadius="sm"
                withColumnBorders
                striped
                highlightOnHover
                records={filteredExams}
                columns={columns}
                minHeight={400}
                noRecordsText="Aucun enregistrement trouvé"
              />
            </div>
          </Card>
        </Tabs.Panel>

        {/* Contenu de l'onglet Général */}
        <Tabs.Panel value="general" pt="md">
          <div className="space-y-6">
            {/* Paramètres généraux du bloc */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Text size="lg" fw={600} mb="md">
                Paramètres généraux du bloc
              </Text>

              <Text size="sm" c="dimmed" mb="lg">
                N.S: tous paramètres en &quot;nombre de semaines&quot; (depuis la date des dernières règles)
              </Text>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* N.S - Date de conception */}
                <div>
                  <Text size="sm" fw={500} mb="xs">N.S - Date de conception</Text>
                  <NumberInput
                    value={40}
                    min={0}
                    max={50}
                    w="100%"
                  />
                </div>

                {/* N.S - Terme théorique */}
                <div>
                  <Text size="sm" fw={500} mb="xs">N.S - Terme théorique</Text>
                  <NumberInput
                    value={40}
                    min={0}
                    max={50}
                    w="100%"
                  />
                </div>

                {/* Erreur standard en jour */}
                <div>
                  <Text size="sm" fw={500} mb="xs">Erreur standard en jour</Text>
                  <Group gap="xs" align="center">
                    <NumberInput
                      value={5}
                      min={0}
                      max={30}
                      w={80}
                    />
                    <Switch
                      label="Terme actuel apd date de conception"
                      checked={true}
                      size="sm"
                    />
                  </Group>
                </div>
              </div>

              <Group gap="md" mt="lg">
                <Select
                  label="Dictionnaire du champs observation"
                  placeholder="Sélectionner un dictionnaire"
                  data={['Dictionnaire 1', 'Dictionnaire 2', 'Dictionnaire 3']}
                  w={250}
                />
                <Select
                  label="Dictionnaire du champs commentaire"
                  placeholder="Sélectionner un dictionnaire"
                  data={['Dictionnaire 1', 'Dictionnaire 2', 'Dictionnaire 3']}
                  w={250}
                />
              </Group>
            </Card>

            {/* Paramètres pour la fiche médicale */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Text size="lg" fw={600} mb="md">
                Paramètres pour la fiche médicale
              </Text>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="gray"
                    checked={false}
                  />
                  <Text size="sm">Ignorer Le sexe du patient</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="blue"
                    checked={true}
                  />
                  <Text size="sm">Afficher/Cacher tous les champs</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="gray"
                    checked={false}
                  />
                  <Text size="sm">Afficher/Cacher Geste</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="gray"
                    checked={false}
                  />
                  <Text size="sm">Afficher/Cacher Pare</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="gray"
                    checked={false}
                  />
                  <Text size="sm">Afficher/Cacher Acc. Prématurée</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="blue"
                    checked={true}
                  />
                  <Text size="sm">Afficher/Cacher Fausse couche</Text>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    size="sm"
                    color="blue"
                    checked={true}
                  />
                  <Text size="sm">Afficher/Cacher G. Extra-utérus</Text>
                </div>
              </div>
            </Card>
          </div>
        </Tabs.Panel>
      </Tabs>

      {/* Modal Nouvel examen ou acte */}
      <Modal
        opened={examModalOpened}
        onClose={closeExamModal}
        title={
          <Group gap="sm">
            <IconCalendar size={20} />
            <Text fw={600}>
              {editMode ? "Modification d&apos;examen ou acte" : "Nouvel examen ou acte"}
            </Text>
          </Group>
        }
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Titre"
            placeholder="Titre de l'examen"
            value={currentExam.titre}
            onChange={(e) => setCurrentExam(prev => ({ ...prev, titre: e.currentTarget.value }))}
            required
            rightSection={
              <Text size="xs" c="dimmed">
                À base des dernières règles
              </Text>
            }
          />

          <div>
            <Text size="sm" fw={500} mb="xs">Motif</Text>
            <Select
              data={['Consultation', 'Echographie', 'Bilan sanguin', 'Dépistage']}
              value={currentExam.motif}
              onChange={(value) => setCurrentExam(prev => ({ ...prev, motif: value || 'Consultation' }))}
              placeholder="Sélectionner un motif"
            />
          </div>

          <Group grow>
            <NumberInput
              label="Durée (min)"
              value={currentExam.duree}
              onChange={(value) => setCurrentExam(prev => ({ ...prev, duree: Number(value) || 15 }))}
              min={5}
              max={180}
              step={5}
              rightSection={
                <Group gap="xs">
                  <Text size="xs" c="green">15min</Text>
                  <ActionIcon size="sm" variant="subtle" color="green" onClick={handleNewAct}>
                    <IconPlus size={12} />
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="red">
                    <IconX size={12} />
                  </ActionIcon>
                </Group>
              }
            />
          </Group>

          <Group grow>
            <div>
              <Text size="sm" fw={500} mb="xs">Semaine d&apos;aménorrhée</Text>
              <Group gap="xs" align="end">
                <NumberInput
                  placeholder="0"
                  value={currentExam.semaine_debut}
                  onChange={(value) => setCurrentExam(prev => ({ ...prev, semaine_debut: Number(value) || 0 }))}
                  min={0}
                  max={42}
                  w={80}
                />
                <Text size="sm" c="dimmed">Semaine d&apos;aménorrhée</Text>
              </Group>
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs">Semaine d&apos;aménorrhée</Text>
              <Group gap="xs" align="end">
                <NumberInput
                  placeholder="0"
                  value={currentExam.semaine_fin}
                  onChange={(value) => setCurrentExam(prev => ({ ...prev, semaine_fin: Number(value) || 0 }))}
                  min={0}
                  max={42}
                  w={80}
                />
              </Group>
            </div>
          </Group>

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeExamModal}>
              Annuler
            </Button>
            <Button onClick={handleSaveExam} color="blue">
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Actes */}
      <Modal
        opened={actModalOpened}
        onClose={closeActModal}
        title={
          <Group gap="sm">
            <IconSettings size={20} />
            <Text fw={600}>Actes</Text>
          </Group>
        }
        size="lg"
        centered
      >
        <Stack gap="md">
          <Group grow>
            <TextInput
              label="Code"
              placeholder="Code de l'acte"
              value={currentAct.code}
              onChange={(e) => setCurrentAct(prev => ({ ...prev, code: e.currentTarget.value }))}
              required
            />
            <TextInput
              label="Description"
              placeholder="Description de l'acte"
              value={currentAct.description}
              onChange={(e) => setCurrentAct(prev => ({ ...prev, description: e.currentTarget.value }))}
              required
            />
          </Group>

          <Group grow>
            <NumberInput
              label="Durée (min)"
              value={currentAct.duree}
              onChange={(value) => setCurrentAct(prev => ({ ...prev, duree: Number(value) || 15 }))}
              min={5}
              max={180}
              step={5}
              rightSection={<Text size="xs">15</Text>}
            />
            <div>
              <Text size="sm" fw={500} mb="xs">Couleur</Text>
              <Group gap="xs">
                <ColorPicker
                  value={currentAct.couleur}
                  onChange={(color) => setCurrentAct(prev => ({ ...prev, couleur: color }))}
                  size="sm"
                />
                <Text size="sm">Couleur</Text>
              </Group>
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs">Couleur rayée</Text>
              <Group gap="xs">
                <ColorPicker
                  value={currentAct.couleur_rayee}
                  onChange={(color) => setCurrentAct(prev => ({ ...prev, couleur_rayee: color }))}
                  size="sm"
                />
                <Text size="sm">Couleur rayée</Text>
              </Group>
            </div>
          </Group>

          <Group grow>
            <Select
              label="Agenda par défaut"
              placeholder="Sélectionner un agenda"
              data={['Agenda 1', 'Agenda 2', 'Agenda 3']}
              value={currentAct.agenda_defaut}
              onChange={(value) => setCurrentAct(prev => ({ ...prev, agenda_defaut: value || '' }))}
            />
            <Select
              label="Services désignés"
              placeholder="Sélectionner des services"
              data={['Service 1', 'Service 2', 'Service 3']}
              value={currentAct.services_designes}
              onChange={(value) => setCurrentAct(prev => ({ ...prev, services_designes: value || '' }))}
            />
          </Group>

          <Switch
            label="Couleur sombre"
            checked={currentAct.couleur_sombre}
            onChange={(event) => setCurrentAct(prev => ({ ...prev, couleur_sombre: event.currentTarget.checked }))}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeActModal}>
              Annuler
            </Button>
            <Button onClick={handleSaveAct} color="blue">
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Nouveau marqueur */}
      <Modal
        opened={markerModalOpened}
        onClose={closeMarkerModal}
        title={
          <Group gap="sm">
            <IconCalendar size={20} />
            <Text fw={600}>Nouveau marqueur</Text>
          </Group>
        }
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Titre"
            placeholder="Titre du marqueur"
            value={currentMarker.titre}
            onChange={(e) => setCurrentMarker(prev => ({ ...prev, titre: e.currentTarget.value }))}
            required
            rightSection={
              <Text size="xs" c="dimmed">
                À base des dernières règles
              </Text>
            }
          />

          <div>
            <Text size="sm" fw={500} mb="xs">Mois</Text>
            <NumberInput
              placeholder="0"
              value={currentMarker.mois}
              onChange={(value) => setCurrentMarker(prev => ({ ...prev, mois: Number(value) || 0 }))}
              min={0}
              max={9}
              w={100}
            />
          </div>

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeMarkerModal}>
              Annuler
            </Button>
            <Button onClick={handleSaveMarker} color="blue">
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default CalendrierDeGrossesse;
