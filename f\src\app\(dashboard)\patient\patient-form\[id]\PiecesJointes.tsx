


'use client';
import React, { useState, useEffect } from 'react';
import { useForm } from '@mantine/form';
import { Group, Title, Button, Text,Badge, Tooltip, Menu, ActionIcon ,Stack,Checkbox,Select,Table,Modal,Radio,MultiSelect,Textarea,Switch,
  ScrollArea,Box,Card,Input,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import AttachmentManager from './AttachmentManager'
import Icon from '@mdi/react';
import { DictionaryModalsManager } from '@/components/alerte';
import { patientFormService, PatientAttachment } from '@/services/patientFormService';
import patientService from '@/services/patientService';
import { notifications } from '@mantine/notifications';
import SimpleBar from "simplebar-react";
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,mdiUpload,
  mdiCertificate,
  mdiFormatListBulleted,
   mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,mdiFolderMultiple,
  mdiCalendarPlus,mdiChevronRight,mdiChevronDown,mdiPencil,mdiDelete,mdiCircle,mdiMicrophone,mdiClipboardText,mdiDeleteSweep,mdiPlaylistCheck,
  mdiPlus,mdiHistory,mdiViewHeadline,mdiArrowRight,mdiAccountSearch,
} from '@mdi/js';
import RelationForm from "./RelationForm"
import { Patient, PatientActionsProps } from './types';
// start alert
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface FichePatientProps {
  onInsuredChange?: (isInsured: boolean) => void;
  fullName?: string;
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  openListDesPatient?: () => void;
  onSubmit?: (values: AlertFormValues, autoTrigger: boolean) => void;
}
//end alert
// Remove old types - AttachmentManager now handles its own data from Django

export interface PiecesJointesPatientActionsProps extends PatientActionsProps {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: Patient;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
openListDesPatient: () => void;
};
export const PiecesJointes =({
  patient,
  onGoBack,
  onGoToContract,
   patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
 openListDesPatient,
  onInsuredChange,
  fullName = 'ABDESSALMAD AGADIR',
  staffOptions = [
    { label: 'TEST DEMO', value: 'test-demo' },
    { label: 'DEMO DEMO', value: 'demo-demo' }
  ],
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],
}: PiecesJointesPatientActionsProps & PatientActionsProps & FichePatientProps) => {
  const disabled = isFormInvalid || isDraft;

  // Enhanced attachment management state
  const [attachmentStats, setAttachmentStats] = useState({
    totalFiles: 0,
    totalSize: 0,
    categories: {} as Record<string, number>,
    recentUploads: 0
  });
  const [loading, setLoading] = useState(false);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load attachment statistics
  useEffect(() => {
    const loadAttachmentStats = async () => {
      if (!patientId) return;

      try {
        setLoading(true);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          console.log(`📊 Loading attachment statistics for patient: ${patientId}`);
          const attachments = await patientFormService.getPatientAttachments(patientId);

          // Calculate statistics
          const stats = {
            totalFiles: attachments.length,
            totalSize: attachments.reduce((sum, att) => sum + (att.file_size || 0), 0),
            categories: attachments.reduce((acc, att) => {
              const category = att.category || 'other';
              acc[category] = (acc[category] || 0) + 1;
              return acc;
            }, {} as Record<string, number>),
            recentUploads: attachments.filter(att => {
              if (!att.created_at) return false;
              const uploadDate = new Date(att.created_at);
              const weekAgo = new Date();
              weekAgo.setDate(weekAgo.getDate() - 7);
              return uploadDate > weekAgo;
            }).length
          };

          setAttachmentStats(stats);
          console.log('📊 Attachment statistics loaded:', stats);
        }
      } catch (error) {
        console.error('❌ Error loading attachment statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAttachmentStats();
  }, [patientId, refreshTrigger]);

  // Function to refresh attachment data
  const refreshAttachments = () => {
    setRefreshTrigger(prev => prev + 1);
    notifications.show({
      title: 'Refreshing',
      message: 'Attachment data refreshed',
      color: 'blue',
    });
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // AttachmentManager now handles its own state and Django integration
  
 // Start Alert
   const [search, setSearch] = useState('');
      const [isSidebarAlert, setIsSidebarAlert] = useState(false);
       const toggleSidebarAlert = () => {
               setIsSidebarAlert(!isSidebarAlert);
             };
      const [isRelationsModalOpen, setIsRelationsModalOpen] = useState(false);
      const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
         const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
         const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
          const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
          const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);
          // État pour gérer l'effondrement de chaque nœud
          // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
          const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
            console.log('TreeItemChoixMultiple initialized with all nodes open');
            return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
          });
        
          // État pour gérer les sélections multiples
          const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
        
          // États pour la gestion des modèles
          const [showModels, setShowModels] = useState(false);
          const [showAddModel, setShowAddModel] = useState(false);
          const [modelTitle, setModelTitle] = useState('');
          const [editingModelId, setEditingModelId] = useState<string | null>(null);
          const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
        
          // États pour la reconnaissance vocale
          const [isListening, setIsListening] = useState(false);
          const [validSpeech, setValidSpeech] = useState('');
          const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
          const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
          const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut
        
          // Initialiser la reconnaissance vocale au montage du composant
          useEffect(() => {
            initSpeechRecognition();
          }, []);
        
          // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
          const toggleNodeCollapse = (nodeId: string) => {
            setCollapsedNodes(prev => {
              const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
              // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
              if (currentState === true) { // Si fermé, ouvrir
                console.log('Opening TreeItemChoixMultiple node:', nodeId);
                return {
                  ...prev,
                  [nodeId]: false // false = ouvert
                };
              } else {
                console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
                return prev; // Ne rien changer si déjà ouvert
              }
            });
          };
          interface TreeNodeChoixMultiple {
            uid: string;
            value: string;
            nodes?: TreeNodeChoixMultiple[];
          }
          function TreeItemChoixMultiple({
            node,
            collapsedNodes,
            toggleNodeCollapse,
            selectedNodes,
            toggleNodeSelection,
          }: {
            node: TreeNodeChoixMultiple;
            collapsedNodes: Record<string, boolean>;
            toggleNodeCollapse: (nodeId: string) => void;
            selectedNodes: Set<string>;
            toggleNodeSelection: (nodeId: string) => void;
          }) {
            // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
            const isCollapsed = collapsedNodes[node.uid] ?? false;
            const isSelected = selectedNodes.has(node.uid);
            // Calculer l'état indéterminé pour les nœuds parents
            const getIndeterminateState = () => {
              if (!node.nodes || node.nodes.length === 0) return false;
              const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
              return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
            };
            const isIndeterminate = getIndeterminateState();
            return (
              <Stack pl="md" gap="xs">
                <Group gap="xs" align="center">
                  {node.nodes && node.nodes.length > 0 && (
                    <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                      <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
                    </span>
                  )}
                  <Checkbox
                    label={node.value}
                    checked={isSelected}
                    indeterminate={isIndeterminate}
                    onChange={() => toggleNodeSelection(node.uid)}
                    radius="xs"
                  />
                </Group>
          
                {!isCollapsed &&
                  node.nodes?.map((child) => (
                    <TreeItemChoixMultiple
                      key={child.uid}
                      node={child}
                      collapsedNodes={collapsedNodes}
                      toggleNodeCollapse={toggleNodeCollapse}
                      selectedNodes={selectedNodes}
                      toggleNodeSelection={toggleNodeSelection}
                    />
                  ))}
              </Stack>
            );
          }
            // Fonction pour basculer la sélection d'un nœud
            const toggleNodeSelection = (nodeId: string) => {
              setSelectedNodes(prev => {
                const newSet = new Set(prev);
          
                // Trouver le nœud correspondant
                const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
                  for (const node of nodes) {
                    if (node.uid === id) return node;
                    if (node.nodes) {
                      const found = findNode(node.nodes, id);
                      if (found) return found;
                    }
                  }
                  return null;
                };
          
                const currentNode = findNode(exampleData, nodeId);
          
                if (newSet.has(nodeId)) {
                  // Désélectionner le nœud et tous ses enfants
                  newSet.delete(nodeId);
                  if (currentNode?.nodes) {
                    const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                      nodes.forEach(child => {
                        newSet.delete(child.uid);
                        if (child.nodes) {
                          removeAllChildren(child.nodes);
                        }
                      });
                    };
                    removeAllChildren(currentNode.nodes);
                  }
                } else {
                  // Sélectionner le nœud et tous ses enfants
                  newSet.add(nodeId);
                  if (currentNode?.nodes) {
                    const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                      nodes.forEach(child => {
                        newSet.add(child.uid);
                        if (child.nodes) {
                          addAllChildren(child.nodes);
                        }
                      });
                    };
                    addAllChildren(currentNode.nodes);
                  }
                }
          
                return newSet;
              });
            };
          
            // Fonction pour obtenir les sélections actuelles
            const getSelectedValues = () => {
              const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
                const result: TreeNodeChoixMultiple[] = [];
                nodes.forEach(node => {
                  result.push(node);
                  if (node.nodes) {
                    result.push(...getAllNodes(node.nodes));
                  }
                });
                return result;
              };
          
              const allNodes = getAllNodes(exampleData);
              return Array.from(selectedNodes)
                .map(id => allNodes.find(node => node.uid === id))
                .filter(Boolean)
                .map(node => node!.value);
            };
      
            // Fonctions pour sélectionner/désélectionner tous les nœuds
            const selectAllNodes = () => {
              const allNodeIds: string[] = [];
      
              const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
                nodes.forEach(node => {
                  allNodeIds.push(node.uid);
                  if (node.nodes && node.nodes.length > 0) {
                    collectAllIds(node.nodes);
                  }
                });
              };
      
              collectAllIds(exampleData);
              setSelectedNodes(new Set(allNodeIds));
            };
      
            const deselectAllNodes = () => {
              setSelectedNodes(new Set());
            };
      
            // Fonctions pour la reconnaissance vocale
            const initSpeechRecognition = () => {
              if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognitionConstructor = (window as unknown as {
                  webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                  SpeechRecognition: new () => SpeechRecognitionInstance;
                }).webkitSpeechRecognition || (window as unknown as {
                  webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                  SpeechRecognition: new () => SpeechRecognitionInstance;
                }).SpeechRecognition;
          
                const newRecognition = new SpeechRecognitionConstructor();
          
                newRecognition.continuous = true;
                newRecognition.interimResults = true;
                newRecognition.lang = 'fr-FR';
          
                newRecognition.onstart = () => {
                  setIsListening(true);
                  setMicrophoneColor('green'); // Changer la couleur en vert
                  setInvalidSpeech('Écoute en cours...');
                };
          
                newRecognition.onresult = (event: unknown) => {
                  const speechEvent = event as {
                    resultIndex: number;
                    results: {
                      length: number;
                      [index: number]: {
                        isFinal: boolean;
                        [index: number]: { transcript: string };
                      };
                    };
                  };
          
                  let finalTranscript = '';
                  let interimTranscript = '';
          
                  for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
                    const transcript = speechEvent.results[i][0].transcript;
                    if (speechEvent.results[i].isFinal) {
                      finalTranscript += transcript;
                    } else {
                      interimTranscript += transcript;
                    }
                  }
          
                  setValidSpeech(finalTranscript);
                  setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
                };
          
                newRecognition.onerror = () => {
                  setIsListening(false);
                  setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
                  setInvalidSpeech('Erreur de reconnaissance vocale');
                };
          
                newRecognition.onend = () => {
                  setIsListening(false);
                  setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
                  setInvalidSpeech('Parlez maintenant.');
                };
          
                setRecognition(newRecognition);
              }
            };
          
            const toggleRecognition = () => {
              if (!recognition) {
                initSpeechRecognition();
                return;
              }
          
              if (isListening) {
                recognition.stop();
              } else {
                recognition.start();
              }
            };
          
            const emptyContent = () => {
              setValidSpeech('');
              setInvalidSpeech('Parlez maintenant.');
              setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
              if (recognition && isListening) {
                recognition.stop();
              }
            };
          
           
          
              // État pour savoir quelle alerte est en cours d'édition
              const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);
      
              // États pour le modal de confirmation de suppression
              const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
              const [alertToDelete, setAlertToDelete] = useState<string | null>(null);
      
              // Fonctions pour gérer les alertes
            
      
              const handleDeleteAlert = (alertId: string) => {
                console.log('Delete alert:', alertId);
                // Ouvrir le modal de confirmation
                setAlertToDelete(alertId);
                setIsDeleteConfirmModalOpen(true);
              };
      
              // Fonction pour confirmer la suppression
              const confirmDeleteAlert = () => {
                if (alertToDelete) {
                  // Supprimer l'alerte de la liste
                  setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
                  console.log('Alert deleted:', alertToDelete);
                }
                // Fermer le modal et réinitialiser
                setIsDeleteConfirmModalOpen(false);
                setAlertToDelete(null);
              };
      
              // Fonction pour annuler la suppression
              const cancelDeleteAlert = () => {
                setIsDeleteConfirmModalOpen(false);
                setAlertToDelete(null);
              };
      
              // Fonction pour obtenir la couleur selon le niveau
              const getLevelColor = (level: string) => {
                switch(level) {
                  case 'MINIMUM': return 'green';
                  case 'MEDIUM': return 'orange';
                  case 'HIGH': return 'red';
                  default: return 'gray';
                }
              };
      
      
      
              // Fonction pour créer les actions d'une alerte
              const createAlertActions = (alertId: string) => (
                <Group gap="xs">
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    size="sm"
                    onClick={() => {
                      console.log('Edit alert clicked:', alertId);
                      setCurrentEditingAlertId(alertId);
      
                      // Trouver l'alerte à éditer et pré-remplir le formulaire
                      const alertToEdit = alertsData.find(alert => alert.id === alertId);
                      console.log('Alert to edit found:', alertToEdit);
                      if (alertToEdit) {
                        // Trouver la valeur correspondante pour le trigger
                        const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';
      
                        console.log('Editing alert:', alertToEdit);
                        console.log('Trigger value found:', triggerValue);
      
                        form.setValues({
                          trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                          trigger: triggerValue,
                          level: alertToEdit.level,
                          description: alertToEdit.Description,
                          is_permanent: alertToEdit.is_permanent
                        });
      
                        console.log('Form values set for editing:', {
                          trigger_for: alertToEdit.trigger_for || [],
                          trigger: triggerValue,
                          level: alertToEdit.level,
                          description: alertToEdit.Description,
                          is_permanent: alertToEdit.is_permanent
                        });
      
                        console.log('Form values set:', form.values);
                      }
      
                      setIsAlertsAddModalOpen(true);
                      setIsSidebarAlert(true); // Ouvrir aussi la sidebar pour les sélections
                    }}
                  >
                    <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
                  </ActionIcon>
                  <ActionIcon
                    variant="subtle"
                    color="red"
                    size="sm"
                    onClick={() => {
                      console.log('Delete alert clicked:', alertId);
                      handleDeleteAlert(alertId);
                    }}
                  >
                    <Icon path={mdiDelete} size={0.8} color={'red'}/>
                  </ActionIcon>
                </Group>
              );
      
              // Fonction pour gérer la soumission du formulaire d'alerte
              const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
                console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
                console.log('Current editing alert ID:', currentEditingAlertId);
      
                if (currentEditingAlertId) {
                  // Mode édition : mettre à jour l'alerte existante
                  console.log('Editing existing alert:', currentEditingAlertId);
      
                  const updatedAlertData = {
                    id: currentEditingAlertId,
                    level: values.level,
                    is_permanent: values.is_permanent,
                    Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                    Description: values.description,
                    trigger_for: values.trigger_for
                  };
      
                  setAlertsData(prevData => {
                    const updatedData = prevData.map(alert =>
                      alert.id === currentEditingAlertId ? updatedAlertData : alert
                    );
                    console.log('Updated alerts data (edit mode):', updatedData);
                    return updatedData;
                  });
                } else {
                  // Mode ajout : créer une nouvelle alerte
                  console.log('Adding new alert');
      
                  const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                  const newAlertData = {
                    id: newAlertId,
                    level: values.level,
                    is_permanent: values.is_permanent,
                    Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                    Description: values.description,
                    trigger_for: values.trigger_for
                  };
      
                  setAlertsData(prevData => {
                    const updatedData = [...prevData, newAlertData];
                    console.log('Updated alerts data (add mode):', updatedData);
                    return updatedData;
                  });
                }
      
                // Appeler la fonction onSubmit originale si elle existe
                if (onSubmit) {
                  onSubmit(values, autoTrigger);
                }
      
                // Fermer le modal et réinitialiser le formulaire
                setIsAlertsAddModalOpen(false);
                setIsSidebarAlert(false);
                setCurrentEditingAlertId(null);
                form.reset();
              };
      
              // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
              const [alertsData, setAlertsData] = useState<AlertData[]>([]);
      
              // Créer les éléments avec les actions pour le rendu
              const elements = alertsData.map(alert => ({
                ...alert,
                Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
                Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
                Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
                Actions: createAlertActions(alert.id)
              }));
          const rowss = elements.map((element) => (
              <Table.Tr key={element.id}>
                <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
                <Table.Td>{element.Niveau}</Table.Td>
                <Table.Td>{element.Publique}</Table.Td>
                <Table.Td>{element.Permanente}</Table.Td>
                <Table.Td>{element.Description}</Table.Td>
                <Table.Td w={'100px'}>{element.Actions}</Table.Td>
              </Table.Tr>
            ));
             const form = useForm<AlertFormValues>({
              initialValues: {
                trigger_for: [],
                trigger: '',
                level: 'MINIMUM',
                description: '',
                is_permanent: false,
              },
              validate: {
                trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
                trigger: (value) => (!value ? 'Champ requis' : null),
                description: (value) => (!value ? 'Champ requis' : null),
              },
            });          
            const handleValidate = () => {
              let textToAdd = '';
          
              if (showModels) {
                // Valider les modèles sélectionnés
                const selectedModelTexts = savedModels
                  .filter(model => model.selected === true)
                  .flatMap(model => model.selections);
                textToAdd = selectedModelTexts.join(', ');
                console.log('Selected models:', savedModels.filter(model => model.selected === true));
                console.log('Text to add from models:', textToAdd);
              } else {
                // Valider les sélections du dictionnaire
                const selectedValues = getSelectedValues();
                textToAdd = selectedValues.join(', ');
              }
          
              if (textToAdd) {
                // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
                setValidSpeech(textToAdd);
                setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"
      
                // 2. Ajouter le texte au champ description du formulaire
                const currentDescription = form.values.description || '';
                const newDescription = currentDescription
                  ? `${currentDescription}, ${textToAdd}`
                  : textToAdd;
                console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
                form.setFieldValue('description', newDescription);
      
                // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
                // Utiliser la nouvelle description mise à jour
                const combinedText = newDescription;
                console.log('Combined text for alert:', combinedText);
      
                if (currentEditingAlertId) {
                  setAlertsData(prevData =>
                    prevData.map(alert => {
                      if (alert.id === currentEditingAlertId) {
                        return {
                          ...alert,
                          Description: combinedText
                        };
                      }
                      return alert;
                    })
                  );
                } else {
                  // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
                  setAlertsData(prevData =>
                    prevData.map(alert => {
                      if (alert.id === '1') {
                        return {
                          ...alert,
                          Description: combinedText
                        };
                      }
                      return alert;
                    })
                  );
                }
              }
          
      
      
              // Fermer le modal et réinitialiser
              setIsClipboardTextModalOpen(false);
              setIsChoixMultipleModalOpen(false);
              setShowModels(false);
              setSelectedNodes(new Set());
              // Réinitialiser l'ID de l'alerte en cours d'édition
              setCurrentEditingAlertId(null);
            };
          
            const handleCancel = () => {
              // Réinitialiser tous les états
              setSelectedNodes(new Set());
              setShowModels(false);
              setShowAddModel(false);
              setModelTitle('');
              setEditingModelId(null);
              setIsClipboardTextModalOpen(false);
            };
          
            // const handleAddModel = () => {
            //   if (selectedNodes.size > 0) {
            //     setShowAddModel(true);
            //   }
            // };
          
            const handleSaveModel = () => {
              if (modelTitle.trim()) {
                if (editingModelId) {
                  // Mode édition : mettre à jour le modèle existant
                  setSavedModels(prev => prev.map(model =>
                    model.id === editingModelId
                      ? { ...model, title: modelTitle.trim() }
                      : model
                  ));
                  setEditingModelId(null);
                  console.log('Model title updated for ID:', editingModelId);
                } else {
                  // Mode création : créer un nouveau modèle
                  const selectedValues = getSelectedValues();
                  const newModel = {
                    id: `model-${Date.now()}`,
                    title: modelTitle.trim(),
                    selections: selectedValues
                  };
                  setSavedModels(prev => [...prev, newModel]);
                  setSelectedNodes(new Set());
                  console.log('New model created:', newModel);
                }
      
                setModelTitle('');
                setShowAddModel(false);
                // Afficher les modèles après sauvegarde
                setShowModels(true);
              }
            };
      
            const handleEditModel = (modelId: string) => {
              const modelToEdit = savedModels.find(model => model.id === modelId);
              if (modelToEdit) {
                setModelTitle(modelToEdit.title);
                setEditingModelId(modelId);
                setShowModels(false);
                setShowAddModel(true);
                console.log('Editing model:', modelToEdit);
              }
            };
          
            const handleDeleteModel = (modelId: string) => {
              setSavedModels(prev => prev.filter(model => model.id !== modelId));
              setSelectedNodes(prev => {
                const newSet = new Set(prev);
                newSet.delete(modelId);
                return newSet;
              });
            };
          
            const exampleData: TreeNodeChoixMultiple[] = [
              {
                uid: '1',
                value: 'Alertes',
                nodes: [
                  { uid: '1-1', value: 'Allaitante depuis:' },
                  { uid: '1-2', value: 'Allergique à l\'Aspirine' },
          
                  { uid: '1-3', value: 'Allergique à la Pénicilline' },
                  { uid: '1-4', value: 'Arthrose' },
                  { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
                  { uid: '1-6', value: 'Diabétique NID' },
                  { uid: '1-7', value: 'Enceinte depuis:' },
                  { uid: '1-8', value: 'Diabétique ID' },
                  { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
                  { uid: '1-10', value: 'Hypertension' },
                   { uid: '1-11', value: 'Hypotension' },
                  { uid: '1-12', value: 'Thyroïde' },
            
          
                ],
              },
              
            ];
          //end header
      // Interface et données pour l'arbre de la sidebar
      interface TreeNode {
        value: string;
        children?: TreeNode[];
      }
      
      const mockTree: TreeNode[] = [
        {
          value: 'Alertes',
          children: [
            { value: "Allaitante depuis:" },
            { value: "Allergique à l'Aspirine" },
            { value: "Allergique à la Pénicilline" },
            { value: "Arthrose" },
            { value: "Cardiaque Anticoagulant sintrom" },
            { value: "Cardiaque prothèse valvulaire" },
            { value: "Cardiaque trouble du rythme" },
            { value: "Diabétique ID" },
            { value: "Diabétique NID" },
            { value: "Enceinte depuis:" },
            { value: "Gastralgie : ulcère anti-inflammatoire" },
            { value: "Hypertension" },
            { value: "Hypotension" },
            { value: "Thyroïde" },
          ],
        },
      ];
      
      // Composant Tree pour la sidebar
      function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
        // Initialiser tous les nœuds comme ouverts
        const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
          const initialExpanded: Record<string, boolean> = {};
          const expandAllNodes = (nodeList: TreeNode[]) => {
            nodeList.forEach(node => {
              if (node.children && node.children.length > 0) {
                initialExpanded[node.value] = true;
                expandAllNodes(node.children);
              }
            });
          };
          expandAllNodes(nodes);
          console.log('Tree initialized with expanded nodes:', initialExpanded);
          return initialExpanded;
        });
      
        return (
          <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
            {nodes.map((node, idx) => {
              const hasChildren = node.children && node.children.length > 0;
              const isOpen = expanded[node.value] || false;
              return (
                <li key={node.value + idx}>
                  <Group gap="xs" align="center" onClick={() => {
                    // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
                    if (hasChildren && !isOpen) {
                      console.log('Opening node:', node.value);
                      setExpanded(prev => ({ ...prev, [node.value]: true }));
                    } else if (hasChildren && isOpen) {
                      console.log('Node already open, not closing:', node.value);
                    }
                  }} className="Alertesslidbar">
                    {hasChildren ? (
                      <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
                    ) : null}
                    <Text
                      onClick={() => !hasChildren && onSelect(node.value)}
                      style={{ cursor: 'pointer' ,paddingLeft:'10px'
                      }}
                    >
                      {node.value}
                    </Text>
                  </Group>
                  {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
                </li>
              );
            })}
          </ul>
        );
      }
       const [, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
      // Fonctions pour gérer la sidebar
      const handleSidebarSelect = (value: string) => {
        console.log('Selected from sidebar:', value);
      
        // 1. Ajouter la sélection au formulaire en cours d'édition
        const currentDescription = form.values.description || '';
        const newFormDescription = currentDescription
          ? `${currentDescription}, ${value}`
          : value;
        form.setFieldValue('description', newFormDescription);
      
        // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
        if (currentEditingAlertId) {
          setAlertsData(prevData =>
            prevData.map(alert => {
              if (alert.id === currentEditingAlertId) {
                const currentAlertDescription = alert.Description || '';
                const newAlertDescription = currentAlertDescription
                  ? `${currentAlertDescription}, ${value}`
                  : value;
                return {
                  ...alert,
                  Description: newAlertDescription
                };
              }
              return alert;
            })
          );
        }
      
        console.log('Added to form description:', newFormDescription);
      
        // Optionnel : fermer la sidebar après sélection
        // setIsSidebarVisible(false);
      };
      
      const handleCloseSidebar = () => {
        setIsSidebarAlert(false);
      };
        
          const FicheForm = useForm({
          initialValues: {
                file_number:1,
                category: '',
                pricing: 0,
                is_bookmarked: false,
                insured: true, // Set to true to match defaultChecked
               description:'',
               titre: '',
          },
          validate: {
            pricing: (value) => (value < 0 ? 'Last name must be at least 2 characters' : null),
          },
        });
      
        // Effect to notify parent component when insured status changes
        useEffect(() => {
          if (onInsuredChange) {
            onInsuredChange(FicheForm.values.insured);
          }
        }, [FicheForm.values.insured, onInsuredChange]);
      
        // Effect to set initial insured state when component mounts
        useEffect(() => {
          if (onInsuredChange && FicheForm.values.insured !== undefined) {
            onInsuredChange(FicheForm.values.insured);
          }
        }, [onInsuredChange, FicheForm.values.insured]); // Include dependency
       
     // End Alert 
  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
       <Group justify="space-between" align="center">
         <Group>
           {patient ? (
             <Icon path={mdiCardAccountDetails} size={1} />
           ) : (
             <Button variant="subtle" onClick={onGoBack}>
               <Icon path={mdiArrowLeft} size={1} color={"white"}/>
             </Button>
           )}
           <Title order={2}>Fiche patient</Title>
           <DatePickerInput placeholder="Date de création" />
           23/06/2025
         </Group>
   
         {patient && (
           <Group>
             <Text>{patient.full_name}</Text>
             <Text>{patient.gender}</Text>
             <Text>{patient.age}</Text>
             <Text>{patient.default_insurance}</Text>
             <Text>{patient.file_number}</Text>
             <Text>{patient.last_visit}</Text>
           </Group>
         )}
   
         <Group>
          
         
           <Menu shadow="md" width={220}>
             <Menu.Target>
               <Button variant="subtle">
                 <Icon path={mdiApps} size={1} color={"white"}/>
               </Button>
             </Menu.Target>
             <Menu.Dropdown>
               <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}onClick={() => setIsAlertsModalOpen(true)}>Alerts</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />} onClick={() => setIsRelationsModalOpen(true)}>Relations Patient</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
             </Menu.Dropdown>
           </Menu>
   
           <Tooltip label="Contrat" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
             <Button variant="subtle" onClick={onGoToContract}>
               <Icon path={mdiCertificate} size={1} color={"white"}/>
             </Button>
           </Tooltip>
   
           <Tooltip label="Liste patients" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
             <Button component="a" href="/pratisoft/patient" variant="subtle">
               <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
             </Button>
           </Tooltip>
         </Group>
       </Group>
       
       </div>
       <div className='flex'>
{/* --------------------------Start Content -----------className='h-[90%]'-------------------*/}
 <div className={isSidebarAlert ? "w-[80%] h-100" : "w-full h-100"}>

 {/* Attachment Statistics Dashboard */}
 {djangoStatus === 'connected' && (
   <Card shadow="sm" padding="md" radius="md" withBorder mb="md">
     <Group justify="space-between" mb="sm">
       <Text fw={600} size="lg">Attachment Overview</Text>
       <Group gap="xs">
         <Button
           variant="light"
           size="xs"
           onClick={refreshAttachments}
           loading={loading}
           leftSection={<Icon path={mdiHistory} size={0.6} />}
         >
           Refresh
         </Button>
         <Text size="xs" c={djangoStatus === 'connected' ? 'green' : 'red'}>
           {djangoStatus === 'connected' ? 'Django Connected' : 'Django Disconnected'}
         </Text>
       </Group>
     </Group>

     <Group gap="md">
       {/* Total Files */}
       <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
         <Text size="xs" c="dimmed">Total Files</Text>
         <Text fw={600} size="lg">{attachmentStats.totalFiles}</Text>
       </Card>

       {/* Total Size */}
       <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
         <Text size="xs" c="dimmed">Total Size</Text>
         <Text fw={600} size="lg">{formatFileSize(attachmentStats.totalSize)}</Text>
       </Card>

       {/* Recent Uploads */}
       <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
         <Text size="xs" c="dimmed">Recent (7 days)</Text>
         <Text fw={600} size="lg" c={attachmentStats.recentUploads > 0 ? 'green' : 'gray'}>
           {attachmentStats.recentUploads}
         </Text>
       </Card>

       {/* Categories */}
       {Object.keys(attachmentStats.categories).length > 0 && (
         <Card padding="xs" withBorder style={{ minWidth: '150px' }}>
           <Text size="xs" c="dimmed" mb="xs">Categories</Text>
           <Stack gap="xs">
             {Object.entries(attachmentStats.categories).map(([category, count]) => (
               <Group key={category} justify="space-between">
                 <Text size="xs" tt="capitalize">{category.replace('_', ' ')}</Text>
                 <Badge size="xs" variant="light">{count}</Badge>
               </Group>
             ))}
           </Stack>
         </Card>
       )}

       {/* Quick Actions */}
       <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
         <Text size="xs" c="dimmed" mb="xs">Quick Actions</Text>
         <Stack gap="xs">
           <Button
             variant="light"
             size="xs"
             fullWidth
             onClick={() => {
               // Trigger file input click
               const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
               if (fileInput) fileInput.click();
             }}
             leftSection={<Icon path={mdiUpload} size={0.6} />}
           >
             Upload
           </Button>
           <Button
             variant="light"
             size="xs"
             fullWidth
             color="orange"
             onClick={() => {
               // Show bulk operations modal
               notifications.show({
                 title: 'Bulk Operations',
                 message: 'Feature coming soon',
                 color: 'blue',
               });
             }}
             leftSection={<Icon path={mdiFolderMultiple} size={0.6} />}
           >
             Bulk Ops
           </Button>
         </Stack>
       </Card>
     </Group>
   </Card>
 )}

<AttachmentManager
  patientId={patientId || "12345"}
  fullName={patient?.full_name || "Patient"}
  onAttachmentsChange={refreshAttachments}
/>
</div>
{isSidebarAlert && (
           <Card shadow="sm" mt={'10px'} padding="lg" radius="md" withBorder className={isSidebarAlert ? "w-[20%]" : "w-full"}>
          <Box mb="sm">
            <Group>
              <Input
                placeholder="Rechercher"
                value={search}
                onChange={(e) => setSearch(e.currentTarget.value)}
                w={"70%"}
              />

              <Group justify="flex-end">
                <ActionIcon
                  variant="filled"
                  aria-label="Multiple"
                  color="#3799CE"
                  onClick={() => {
                    // Vous pouvez ajouter ici la logique pour ouvrir le modal de choix multiple
                    console.log('Open multiple choice modal');
                    setIsSidebarAlert(false);
                    setIsChoixMultipleModalOpen(true);
                     
                  }}
                >
                  <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                <ActionIcon
                  variant="filled"
                  aria-label="Annuler"
                  color="#3799CE"
                  onClick={handleCloseSidebar}
                >
                  <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
              </Group>
            </Group>
          </Box>

          <ScrollArea h={400}>
            <Tree
              nodes={mockTree.filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
              onSelect={handleSidebarSelect}
            
            />
          </ScrollArea>
           </Card>
              )}
{/* -------------------------end  Content---------------------------------*/}
</div>
{/* menu Alerts */}
                        <Modal.Root
                           opened={isAlertsModalOpen}
                           onClose={() => setIsAlertsModalOpen(false)}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         > 
                       
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                               <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                               onClick={() => 
                                 {setIsAlertsAddModalOpen(true)
                               ; toggleSidebarAlert()}}>
                        <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                               </ActionIcon>
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} 
                                        onClick={handleCloseSidebar}
                                       />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                            <div className={rowss.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                                   
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                        <Table striped highlightOnHover withTableBorder withColumnBorders>
                                          <Table.Thead>
                       <Table.Tr>
                         <Table.Th>Déclencheur</Table.Th>
                         <Table.Th>Niveau</Table.Th>
                         <Table.Th>Publique</Table.Th>
                         <Table.Th>Permanente</Table.Th>
                          <Table.Th>Description</Table.Th>
                         <Table.Th></Table.Th>
                       </Table.Tr>
                     </Table.Thead>
                     <Table.Tbody>{rowss}</Table.Tbody>
                     {rowss.length === 0 && (
                       <Table.Caption>Aucun élément trouvé.</Table.Caption>
                     )}
                                         </Table>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                         {/* add Alerts */}
                          <Modal.Root
                           opened={isAlertsAddModalOpen}
                           onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         > 
                        
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    {`Alerte - ${fullName}`} 
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                    
                            <div className={rowss.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
                           
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                      <form
                                             onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                             style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                           >
                                             <Group>
                                             <MultiSelect
                                               label="Déclencher pour"
                                               data={staffOptions}
                                               {...form.getInputProps('trigger_for')}
                                               required
                                               w={"30%"}
                                             />
                                             <Select
                                               label="Déclencheur"
                                               data={triggerOptions}
                                               {...form.getInputProps('trigger')}
                                               required
                                                w={"30%"}
                                             />
                                             <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                               <Group>
                                                 <Radio value="MINIMUM" label="Minimum" />
                                                 <Radio value="MEDIUM" label="Moyen" />
                                                 <Radio value="HIGH" label="Haut" />
                                               </Group>
                                             </Radio.Group>
                                             </Group>
                                             <Group justify="space-between">
                                               <Text>Description *</Text>
                                               <Group>
                                                <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>setIsMicrophoneModalOpen(true)
                                             }>
                                             <Icon path={mdiMicrophone} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>{
                                                 console.log('Dictionary button clicked, sidebar visible:', isSidebarAlert);
                                                 setIsClipboardTextModalOpen(true);
                                                    setShowModels(true);
                                                    setShowAddModel(false);
                                                    handleCloseSidebar()
    
                                               }
                                             }>
                                             <Icon path={mdiClipboardText} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon
                                                variant="filled"
                                                aria-label="Clear Description"
                                                color="red"
                                                onClick={() => {
                                                  console.log('Clear button clicked, clearing description field');
                                                  form.setFieldValue('description', '');
                                                  console.log('Description field cleared');
                                                }}
                                              >
                                                <Icon path={mdiDeleteSweep} size={1} />
                                              </ActionIcon>
                                               </Group>
                                             </Group>
                                             <Textarea
                                               // label="Description"
                                               placeholder="Ajouter"
                                               {...form.getInputProps('description')}
                                               required
                                             />
                                           
                                             <Switch
                                               label="Permanente"
                                               {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                             />
                                     
                                             <Group justify="flex-end" mt="md">
                                               <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                                 Annuler
                                               </Button>
                                               <Button
                                                 onClick={() => {
                                                   if (form.isValid()) {
                                                     handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                                   }
                                                 }}
                                                 disabled={!form.isValid()}
                                               >
                                                 Enregistrer et déclencher
                                               </Button>
                                               <Button type="submit" disabled={!form.isValid()}>
                                                 Enregistrer
                                               </Button>
                                             </Group>
                                           </form>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                           {/* Choix multiple */}
                          <Modal.Root
                           opened={isChoixMultipleModalOpen}
                           onClose={() => {setIsChoixMultipleModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         >  
                           <Modal.Overlay />
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    Choix multiple
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                                   <div className="py-2 pl-4 h-[300px]">
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
               
                      <Stack>
                       {/* Boutons de contrôle */}
                       <Group justify="space-between" mb="sm">
                         <Button
                           size="xs"
                           variant="light"
                           onClick={() => {
                             // Sélectionner tous les nœuds
                             const getAllNodeIds = (nodes: TreeNodeChoixMultiple[]): string[] => {
                               const ids: string[] = [];
                               nodes.forEach(node => {
                                 ids.push(node.uid);
                                 if (node.nodes) {
                                   ids.push(...getAllNodeIds(node.nodes));
                                 }
                               });
                               return ids;
                             };
                             setSelectedNodes(new Set(getAllNodeIds(exampleData)));
                           }}
                         >
                           Tout sélectionner
                         </Button>
                         <Button
                           size="xs"
                           variant="light"
                           color="red"
                           onClick={() => setSelectedNodes(new Set())}
                         >
                           Tout désélectionner
                         </Button>
                       </Group>
               
                       {exampleData.map((node) => (
                         <TreeItemChoixMultiple
                           key={node.uid}
                           node={node}
                           collapsedNodes={collapsedNodes}
                           toggleNodeCollapse={toggleNodeCollapse}
                           selectedNodes={selectedNodes}
                           toggleNodeSelection={toggleNodeSelection}
                         />
                       ))}
                     </Stack>
               
                     <Group justify="space-between" mt="md">
                       <Group>
                         <Text size="sm" c="dimmed">
                           {selectedNodes.size} élément{selectedNodes.size !== 1 ? 's' : ''} sélectionné{selectedNodes.size !== 1 ? 's' : ''}
                         </Text>
                       
                       </Group>
                       <Group>
                         {selectedNodes.size > 0 && (
                           <Button
                             variant="filled"
                             color="blue"
                             onClick={() => {
                               // Sauvegarder automatiquement comme nouveau modèle
                               const selectedValues = getSelectedValues();
                               const timestamp = new Date().toLocaleString('fr-FR', {
                                 day: '2-digit',
                                 month: '2-digit',
                                 year: 'numeric',
                                 hour: '2-digit',
                                 minute: '2-digit'
                               });
                               const autoTitle = `Modèle ${timestamp}`;
    
                               const newModel = {
                                 id: `model-${Date.now()}`,
                                 title: autoTitle,
                                 selections: selectedValues
                               };
    
                               setSavedModels(prev => [...prev, newModel]);
                               console.log('Model saved:', newModel);
    
                               // Passer à la vue des modèles
                               setShowModels(true);
                               setSelectedNodes(new Set());
                             }}
                           >
                             Ajouter model
                           </Button>
                         )}
                         <Button onClick={handleValidate} disabled={selectedNodes.size === 0}>
                           Valider ({selectedNodes.size})
                         </Button>
                         <Button variant="outline" color="red" onClick={handleCancel}>
                           Annuler
                         </Button>
                       </Group>
                     </Group>
                     
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
    
                         {/* Modal de confirmation de suppression */}
                         <Modal.Root
                           opened={isDeleteConfirmModalOpen}
                           onClose={cancelDeleteAlert}
                           centered
                           size="sm"
                         >
                           <Modal.Content>
                             <Modal.Header>
                               <Modal.Title>Confirmation de suppression</Modal.Title>
                               <Modal.CloseButton />
                             </Modal.Header>
                             <Modal.Body>
                               <Text size="md" mb="md">
                                 Êtes-vous sûr de vouloir supprimer alert ??
                               </Text>
                               <Group justify="flex-end" gap="sm">
                                 <Button
                                   variant="outline"
                                   color="blue"
                                   onClick={confirmDeleteAlert}
                                 >
                                   Oui
                                 </Button>
                                 <Button
                                   variant="filled"
                                   color="red"
                                   onClick={cancelDeleteAlert}
                                 >
                                   Non
                                 </Button>
                               </Group>
                             </Modal.Body>
                           </Modal.Content>
                         </Modal.Root>
                          {/* Modal Microphone - Reconnaissance vocale */}
                                          <Modal
                                            opened={isMicrophoneModalOpen}
                                            onClose={() => setIsMicrophoneModalOpen(false)}
                                            title="Reconnaissance vocale"
                                            size="lg"
                                            radius={0}
                                            transitionProps={{ transition: 'fade', duration: 200 }}
                                            centered
                                            withCloseButton={false}
                                            yOffset="30vh" xOffset={0}
                                            
                                          >
                                            <div style={{ padding: '20px' }}>
                                              {/* Interface de reconnaissance vocale */}
                                              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                                                <div style={{ flex: 1, marginRight: '16px' }}>
                                                  <div style={{
                                                    border: '1px solid #e0e0e0',
                                                    borderRadius: '4px',
                                                    padding: '12px',
                                                    minHeight: '80px',
                                                    backgroundColor: '#fafafa',
                                                   height:'150px'
                                                  }}>
                                                    {/* Texte valide reconnu */}
                                                    <span
                                                      style={{
                                                        color: '#2e7d32',
                                                        fontWeight: 500,
                                                        display: validSpeech ? 'inline' : 'none'
                                                      }}
                                                      contentEditable
                                                    >
                                                      {validSpeech}
                                                    </span>
                                                    {/* Texte en cours de reconnaissance */}
                                                    <span
                                                      style={{
                                                        color: '#757575',
                                                        fontStyle: 'italic'
                                                      }}
                                                    >
                                                      {invalidSpeech}
                                                    </span>
                                                  </div>
                                                </div>
                                    
                                                {/* Boutons de contrôle */}
                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color={isListening ? 'orange' : 'blue'}
                                                    size="lg"
                                                    onClick={toggleRecognition}
                                                    style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                                                  >
                                                    <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                                                  </ActionIcon>
                                    
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color="red"
                                                    size="lg"
                                                    onClick={emptyContent}
                                                  >
                                                    <Icon path={mdiDeleteSweep} size={1} />
                                                  </ActionIcon>
                                                </div>
                                              </div>
                                    
                                              {/* Boutons d'action */}
                                              <Group justify="flex-end" mt="md">
                                                <Button
                                                  variant="filled"
                                                  onClick={() => {
                                                    // Ici vous pouvez traiter le texte reconnu
                                                    console.log('Texte reconnu:', validSpeech);
                                                    setIsMicrophoneModalOpen(false);
                                                  }}
                                                >
                                                  Valider
                                                </Button>
                                                <Button
                                                  variant="outline"
                                                  color="red"
                                                  onClick={() => setIsMicrophoneModalOpen(false)}
                                                >
                                                  Annuler
                                                </Button>
                                              </Group>
                                            </div>
                                          </Modal>
                                    
                                          {/* Gestionnaire des modaux de dictionnaire */}
                                          <DictionaryModalsManager
                                            // États des modaux
                                            isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                                            isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                                            isDictionaryTreeModalOpen={isChoixMultipleModalOpen}
                         
                                            // Données
                                            modelTitle={modelTitle}
                                            savedModels={savedModels}
                                            exampleData={exampleData}
                                            selectedNodes={selectedNodes}
                                            collapsedNodes={collapsedNodes}
                                            editingModelId={editingModelId}
                         
                                            // Fonctions de gestion des états
                                            setModelTitle={setModelTitle}
                                            setIsAddModelModalOpen={setShowAddModel}
                                            setIsSavedModelsModalOpen={setShowModels}
                                            setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}
                         
                                            // Fonctions de gestion des modèles
                                            onSaveModel={handleSaveModel}
                                            onToggleModel={(modelId) => {
                                              console.log('Toggling model:', modelId);
                                              setSavedModels(prev => {
                                                const updated = prev.map(model =>
                                                  model.id === modelId
                                                    ? { ...model, selected: !model.selected }
                                                    : model
                                                );
                                                console.log('Updated savedModels:', updated);
                                                return updated;
                                              });
                                            }}
                                            onDeleteModel={handleDeleteModel}
                                            onEditModel={handleEditModel}
                         
                                            // Fonctions de gestion de l'arbre
                                            onToggleNodeCollapse={toggleNodeCollapse}
                                            onToggleNodeSelection={toggleNodeSelection}
                                            onSelectAll={selectAllNodes}
                                            onDeselectAll={deselectAllNodes}
                         
                                            // Fonctions d'action
                                            onValidate={handleValidate}
                                            onCancel={handleCancel}
                                            onCloseSidebar={() => {
                                              console.log('Closing sidebar from SavedModelsModal');
                                              setIsSidebarVisible(false);
                                            }}
                                            getSelectedValues={getSelectedValues}
                         
                                            // Composants
                                            TreeItemChoixMultiple={TreeItemChoixMultiple}
                                          />
                         
                                          {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                                          {isClipboardTextModalOpen && !showModels && !showAddModel && (
                                            <div style={{ display: 'none' }}>
                                              {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                                            </div>
                                          )}
     {/*modal Relations */}
                        <Modal.Root
                           opened={isRelationsModalOpen}
                           onClose={() => setIsRelationsModalOpen(false)}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="lg"
                           zIndex={10}
                         > 
                       
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountSupervisorCircle} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                 Relations - ABDESSALMAD AGADIR </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                               <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                               onClick={openListDesPatient}>
                        <Icon path={mdiAccountSearch} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                               </ActionIcon>
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                            <div className="py-2 pl-4 h-auto overflow-hidden" >
                                   
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                        <Card shadow="sm" padding="lg" radius="md" withBorder>
                                          <RelationForm/>
                                        </Card>
                                         
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                                  <Group justify="flex-end" m="md">
                                  <Button variant="outline" color="red" onClick={() => setIsRelationsModalOpen(false)}>
                                    Annuler
                                  </Button>
                                  <Button type="submit" disabled={!form.isValid()}>
                                    Enregistrer
                                  </Button>
                                </Group> 
                        </Modal.Content>
                         </Modal.Root>
    {/* footer */}
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    </>
  )
}






