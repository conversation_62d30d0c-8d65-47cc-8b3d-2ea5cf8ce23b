
"use client";
import { useState, useEffect } from "react";
import React from "react";
import { useSearchParams, useParams } from "next/navigation";
import Icon from '@mdi/react';
import {mdiClipboardFlowOutline,mdiReceiptText,mdiCurrencyUsd, mdiFileDocument,mdiCertificate } from '@mdi/js';
import MetaSeo from"./MetaSeo"
import { Patient as CalendarPatient } from '@/types/typesCalendarPatient';
import { Patient as LocalPatient } from './types';
type CombinedPatient = CalendarPatient & LocalPatient;
import  FluxDeFaturation from"./Flux_de_faturation"
import  MesFactures from"./Mes_facuers"
import  Mes_devis_list from "./Mes_devis_list"
import  Mes_reglements from "./Mes_reglements"
import  Mes_depenses from "./Mes_depenses"
import Mes_contrats from "./Mes_contrats"
import "~/styles/tab.css";
const tabMapping: { [key: string]: number } = {
  'FluxDeFaturation': 1,
  'Mes_facuers': 2,
  'Mes_devis_list': 3,
   'Mes_reglements': 4,
  'Mes_depenses': 5,
  'Mes_contrats': 6,

};

function  AppointmentsPage() {

  const [toggleState, setToggleState] = useState(1);
    const searchParams = useSearchParams();
    const params = useParams();

    const patientId = params.id as string;
  const mockPatient = {
    id: patientId,
    title: "Mr",
    name: "Doe",
    prenom: "John",
    first_name: "John",
    last_name: "Doe",
    full_name: "John Doe",
    birth_date: "1993-01-01",
    appointmentDate: new Date().toISOString(),
    appointmentTime: "10:00",
    appointmentEndTime: "11:00",
    consultationDuration: 60,
    email: "<EMAIL>",
    age: 30,
    phone_numbers: "123456789",
    socialSecurity: "123456789",
    duration: "60",
    agenda: "General",
    comment: "",
    address: "123 Main St",
    etatCivil: "Single",
    etatAganda: "Active",
    patientTitle: "Mr",
    notes: "",
    date: new Date().toISOString(),
    docteur: "Dr. Smith",
    event_Title: "Consultation",
    gender: "M",
    sociale: "123456789",
    typeConsultation: "Consultation",
    commentairelistedattente: "",
    resourceId: 1,
    type: "visit" as const,
    eventType: "visit" as const,
    start: new Date(),
    end: new Date(),
    // Additional fields for local Patient type
    default_insurance: "Basic",
    file_number: "F001",
     last_visit: "2025-06-01",
  lastVisit: {
    date: new Date("2025-06-01"),
    notes: "Dernière consultation de suivi"
  },
 
  } as CombinedPatient;
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);

const icons = [
  { icon: <Icon path={mdiClipboardFlowOutline} size={1} key="FluxDeFaturation" />, label: "Flux de faturation" },
{ icon: <Icon path={mdiReceiptText} size={1} key="Mes_facuers" />, label: "Mes factures" },
{ icon: <Icon path={mdiFileDocument} size={1} key="Mes_devis_list" />, label: "Mes devis" },
{ icon: <Icon path={mdiCurrencyUsd} size={1} key="Mes_reglements" />, label: "Mes réglements" },
{ icon: <Icon path={mdiCurrencyUsd} size={1} key="Mes_depenses" />, label: "Mes depenses" },
{ icon: <Icon path={mdiCertificate} size={1} key="Mes_contrats" />, label: "Mes contrats" },




];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
     case 1:
      return (<div className="w-full p-4"><FluxDeFaturation  /></div> )
    case 2:
       return (<div className="w-full p-4"><MesFactures /> </div>)
   case 3:
      return (<div className="w-full p-4"><Mes_devis_list /></div> )
       case 4:
      return (<div className="w-full p-4"><Mes_reglements /> </div>)
       case 5:
      return (<div className="w-full p-4"><Mes_depenses /></div> )
      case 6:
      return (<div className="w-full py-4"><Mes_contrats /> </div>)
    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 
