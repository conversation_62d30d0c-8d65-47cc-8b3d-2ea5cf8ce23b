/**
 * Custom hook for managing mutuelles (insurance) data
 * Provides easy access to mutuelles CRUD operations
 */

import { useState, useEffect, useCallback } from 'react';
import { careSheetService, MutuelleData } from '@/services/careSheetService';

interface UseMutuellesOptions {
  patientId?: string;
  autoFetch?: boolean;
}

interface UseMutuellesReturn {
  // Data
  mutuelles: MutuelleData[];
  
  // Loading states
  loading: boolean;
  
  // Error state
  error: string | null;
  
  // Actions
  fetchMutuelles: (patientId?: string) => Promise<void>;
  createMutuelle: (mutuelleData: Omit<MutuelleData, 'id'>) => Promise<void>;
  updateMutuelle: (id: number, mutuelleData: Partial<MutuelleData>) => Promise<void>;
  deleteMutuelle: (id: number) => Promise<void>;
  refreshMutuelles: () => Promise<void>;
  
  // Utility functions
  getMutuellesByPatient: (patientId: string) => MutuelleData[];
  getMutuellesByStatus: (status: 'validee' | 'non-validee' | 'en-attente') => MutuelleData[];
  getTotalMontant: () => number;
  getValidatedMontant: () => number;
}

export const useMutuelles = (options: UseMutuellesOptions = {}): UseMutuellesReturn => {
  const { patientId, autoFetch = true } = options;

  // State
  const [mutuelles, setMutuelles] = useState<MutuelleData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchMutuelles = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await careSheetService.getMutuelles(targetPatientId || patientId);
      setMutuelles(data);
    } catch (err) {
      setError(`Failed to fetch mutuelles: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [patientId]);

  const createMutuelle = useCallback(async (mutuelleData: Omit<MutuelleData, 'id'>) => {
    setError(null);
    try {
      const newMutuelle = await careSheetService.createMutuelle(mutuelleData);
      setMutuelles(prev => [newMutuelle, ...prev]);
    } catch (err) {
      setError(`Failed to create mutuelle: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const updateMutuelle = useCallback(async (id: number, mutuelleData: Partial<MutuelleData>) => {
    setError(null);
    try {
      const updatedMutuelle = await careSheetService.updateMutuelle(id, mutuelleData);
      setMutuelles(prev => prev.map(mutuelle => mutuelle.id === id ? updatedMutuelle : mutuelle));
    } catch (err) {
      setError(`Failed to update mutuelle: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const deleteMutuelle = useCallback(async (id: number) => {
    setError(null);
    try {
      await careSheetService.deleteMutuelle(id);
      setMutuelles(prev => prev.filter(mutuelle => mutuelle.id !== id));
    } catch (err) {
      setError(`Failed to delete mutuelle: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const refreshMutuelles = useCallback(async () => {
    await fetchMutuelles();
  }, [fetchMutuelles]);

  // Utility functions
  const getMutuellesByPatient = useCallback((targetPatientId: string) => {
    return mutuelles.filter(m => m.patientId === targetPatientId);
  }, [mutuelles]);

  const getMutuellesByStatus = useCallback((status: 'validee' | 'non-validee' | 'en-attente') => {
    return mutuelles.filter(m => m.etat === status);
  }, [mutuelles]);

  const getTotalMontant = useCallback(() => {
    return mutuelles.reduce((sum, m) => sum + m.montant, 0);
  }, [mutuelles]);

  const getValidatedMontant = useCallback(() => {
    return mutuelles
      .filter(m => m.etat === 'validee')
      .reduce((sum, m) => sum + m.montant, 0);
  }, [mutuelles]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      fetchMutuelles();
    }
  }, [autoFetch, fetchMutuelles]);

  return {
    // Data
    mutuelles,
    
    // Loading states
    loading,
    
    // Error state
    error,
    
    // Actions
    fetchMutuelles,
    createMutuelle,
    updateMutuelle,
    deleteMutuelle,
    refreshMutuelles,
    
    // Utility functions
    getMutuellesByPatient,
    getMutuellesByStatus,
    getTotalMontant,
    getValidatedMontant,
  };
};

export default useMutuelles;
