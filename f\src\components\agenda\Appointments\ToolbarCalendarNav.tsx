import React from 'react'
import {  Tooltip,  } from "@mantine/core";
import { IconFileTypography,IconBookmark ,IconFilter2} from '@tabler/icons-react';
// import {
//     FileType2,
//     Bookmark,
//     ListFilter,
//   } from "lucide-react";
  import Icon from '@mdi/react';
import { mdiCalendarMonth ,mdiCalendarMonthOutline,mdiMagnify,mdiAccountGroupOutline,mdiCalendarTextOutline, mdiCalendarPlusOutline } from '@mdi/js';
export const ToolbarCalendarNav = () => {
  return (
    <>
      <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
              
                <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                <IconFileTypography stroke={1.5}  className="h-4 w-4  hover:text-[#3799CE]" />
                </h2>
                
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
              
                <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                <IconBookmark stroke={1.75} className="mr-2 h-4 w-4 hover:text-[#3799CE]" />{" "}
                </h2>
                
            </Tooltip>
       
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
            <Icon path={mdiCalendarTextOutline} size={1} className="h-4 w-4 hover:text-[#3799CE]" />
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              <Icon path={mdiAccountGroupOutline} size={1}  className="h-4 w-4 hover:text-[#3799CE]" /> 
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              <Icon path={mdiCalendarMonthOutline} size={1}  className="h-4 w-4 hover:text-[#3799CE]" /> 
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              <Icon path={mdiCalendarMonth} size={1} className="h-4 w-4 hover:text-[#3799CE]" /> 
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Gérer événement"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              {/* <PiCalendarPlusLight className="h-4 w-4 hover:text-[#3799CE]" />  */}
             <Icon path={mdiCalendarPlusOutline} size={1} className="h-4 w-4 hover:text-[#3799CE]"/>
            </h2> 
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Chercher Des rendez-Vous"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              <Icon path={mdiMagnify} size={1} className="h-4 w-4 hover:text-[#3799CE]" />
            </h2>
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              <IconFilter2 stroke={2} className="h-3.5 w-3.5 hover:text-[#3799CE]" />
            </h2>
            </Tooltip>
    </>
  )
}
