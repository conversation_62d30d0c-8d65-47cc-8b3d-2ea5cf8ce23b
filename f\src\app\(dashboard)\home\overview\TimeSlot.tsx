import React, { cloneElement, isValidElement, useState, useEffect } from "react";
import type { ReactElement } from "react";
import moment from "moment";
export function addZero(num: number): string {
  return num < 10 ? `0${num}` : num.toString();
}
interface CustomDivProps extends React.HTMLAttributes<HTMLDivElement> {
  "data-time": string;
}
type TimeSlotProps = {
  children: React.ReactElement<CustomDivProps> | React.ReactNode;
  value: Date;
  step: number;
  isRender: boolean;
  
};
const TimeSlot: React.FC<TimeSlotProps> = ({
  children,
  value,
  step,
  isRender,
}) => {
  const [now, setNow] = useState<Date | null>(null);

  // Initialize current time only on client side
  useEffect(() => {
    setNow(new Date());

    // Update time every minute if this is the current time slot
    const interval = setInterval(() => {
      setNow(new Date());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Return early if not hydrated yet
  if (!now) {
    return (
      <div className="relative h-8 border-b border-gray-200">
        {isValidElement(children) ? cloneElement(children) : children}
      </div>
    );
  }

  const slotStart = moment(value);
  const slotEnd = moment(value).add(step, 'minutes');
  const currentMoment = moment(now);

  // Check if current time is within this time slot
  const isCurrentTimeSlot = currentMoment.isBetween(slotStart, slotEnd, null, '[)');

  const currentTime = `${addZero(now.getHours())}:${addZero(now.getMinutes())}`;

  const calculateIndicatorPosition = (): number => {
    if (!isCurrentTimeSlot) return 0;

    // Calculate how many minutes have passed since the slot started
    const minutesIntoSlot = currentMoment.diff(slotStart, 'minutes', true);

    // Convert to percentage within the slot (0-100%)
    const percentage = (minutesIntoSlot / step) * 100;

    return Math.max(0, Math.min(100, percentage));
  };

  // Style properties only valid for CSS
  const timeIndicatorStyle: React.CSSProperties =
    isCurrentTimeSlot && isRender
      ? {
          top: `${calculateIndicatorPosition()}%`,
         
        }
      : {};

  if (!isValidElement(children)) {
    return null;
  }

  // Cast children to ReactElement<CustomDivProps>
  const childrenElement = children as ReactElement<CustomDivProps>;

  // Create the time indicator element with inline styles for the label
  const timeIndicatorElement = isCurrentTimeSlot && isRender ? (
    <span className="time-indicator" style={timeIndicatorStyle}>
      <span
        className="label"
        style={{
          position: 'absolute',
          backgroundColor: '#06b1bd',
          color: 'white',
          padding: '2px 8px',
          borderRadius: '10px',
          fontSize: '10px',
          fontWeight: '600',
          top: '0 !important',//50%
          //left: '108px !important',//108
          transform: 'translateY(-50%)',
          zIndex: 1000,
          whiteSpace: 'nowrap',
          minWidth: '36px',
          textAlign: 'center',
          lineHeight: '14px',
          height: '18px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.15)',
          pointerEvents: 'none'
        }}
      >
        {currentTime}
      </span>
    </span>
  ) : null;



  return cloneElement(childrenElement, {
    style: { ...(childrenElement.props.style ?? {}), ...timeIndicatorStyle },
    "data-time": moment(value).format("HH:mm"),
    className: `${childrenElement.props.className || ''} ${isCurrentTimeSlot && isRender ? 'current-time' : ''}`.trim(),
    children: (
      <>
        {childrenElement.props.children}
        {timeIndicatorElement}
      </>
    ),
  });
};

export default TimeSlot;
