'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Box,
  Radio,
  Modal,
  TextInput,
  Table,
  ScrollArea
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiAccountDetails,
  mdiMagnify,
  mdiAlertCircleOutline
} from '@mdi/js';

// Types et interfaces
interface PatientState {
  name: string;
  label: string;
  type: 'general' | 'procedure' | 'dental' | 'medical' | 'encasement' | 'payment';
  deactivated?: boolean;
}

interface PatientQuery {
  patient: Patient | null;
  start: Date;
  end: Date;
}

interface Patient {
  id: string;
  full_name: string;
  phone?: string;
  email?: string;
}

interface EtatsDuPatientProps {
  onQueryChange?: (query: PatientQuery) => void;
  onStateChange?: (state: PatientState) => void;
  onPatientSelect?: (patient: Patient | null) => void;
}

export const EtatsDuPatient: React.FC<EtatsDuPatientProps> = ({
  onQueryChange,
  onStateChange,
  onPatientSelect
}) => {
  // États locaux
  const [query, setQuery] = useState<PatientQuery>({
    patient: null,
    start: new Date(),
    end: new Date()
  });

  const [selectedState, setSelectedState] = useState<PatientState>({
    name: 'general_account',
    label: 'Etat du Compte général',
    type: 'general'
  });

  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [patientModalOpened, setPatientModalOpened] = useState(false);
  const [patientSearch, setPatientSearch] = useState('');

  // États disponibles
  const states: PatientState[] = [
    { name: 'general_account', label: 'Etat du Compte général', type: 'general' },
    { name: 'procedures', label: 'Les actes', type: 'procedure' },
    { name: 'dental_procedures', label: 'Les actes dentaires', type: 'dental' },
    { name: 'medical_procedures', label: 'les actes de soins', type: 'medical' },
    { name: 'encasements', label: 'Encaissements', type: 'encasement' },
    { name: 'payments', label: 'Paiements', type: 'payment' }
  ];

  // Patients mockés pour la démonstration
  const mockPatients: Patient[] = [
    { id: '1', full_name: 'Ahmed Benali', phone: '**********', email: '<EMAIL>' },
    { id: '2', full_name: 'Fatima Zahra', phone: '**********', email: '<EMAIL>' },
    { id: '3', full_name: 'Mohamed Alami', phone: '**********', email: '<EMAIL>' },
    { id: '4', full_name: 'Aicha Bennani', phone: '**********', email: '<EMAIL>' },
    { id: '5', full_name: 'Youssef Tazi', phone: '**********', email: '<EMAIL>' }
  ];

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<PatientQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleStateChange = (state: PatientState) => {
    setSelectedState(state);
    onStateChange?.(state);
  };

  const handlePatientSelect = (patient: Patient | null) => {
    setSelectedPatient(patient);
    setPatientModalOpened(false);
    setPatientSearch('');
    handleQueryChange({ patient });
    onPatientSelect?.(patient);
  };

  const filteredPatients = mockPatients.filter(patient =>
    patient.full_name.toLowerCase().includes(patientSearch.toLowerCase())
  );

  const showEmptyState = !selectedPatient;

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiAccountDetails} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>États du patient</Text>
          </Group>
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md">
          {/* Recherche de patient */}
          <Box>
            <Text size="sm" fw={500} mb="xs">Patient</Text>
            <Group gap="xs">
              <TextInput
                placeholder="Choisir un patient"
                value={selectedPatient?.full_name || ''}
                readOnly
                style={{ width: 200 }}
                onClick={() => setPatientModalOpened(true)}
                styles={{
                  input: {
                    cursor: 'pointer'
                  }
                }}
              />
              <ActionIcon
                variant="subtle"
                onClick={() => setPatientModalOpened(true)}
                title="Rechercher un patient"
              >
                <Icon path={mdiMagnify} size={0.8} />
              </ActionIcon>
            </Group>
          </Box>

          {/* Date picker "Du" */}
          <DateInput
            label="Du"
            value={query.start}
            onChange={(value) => value && handleQueryChange({ start: value as unknown as Date })}
            style={{ width: 200 }}
          />

          {/* Date picker "Au" */}
          <DateInput
            label="Au"
            value={query.end}
            onChange={(value) => value && handleQueryChange({ end: value as unknown as Date })}
            style={{ width: 200 }}
          />

          {/* Source de données */}
          <Box style={{ marginLeft: 12 }}>
            <Text size="sm" fw={500} mb="xs">Source de données</Text>
            <Radio.Group
              value={selectedState.name}
              onChange={(value) => {
                const state = states.find(s => s.name === value);
                if (state) handleStateChange(state);
              }}
            >
              <Group>
                {states.map((state) => (
                  <Radio
                    key={state.name}
                    value={state.name}
                    label={state.label}
                    disabled={state.deactivated}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>
        </Group>
      </Paper>

      {/* Contenu principal */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {showEmptyState ? (
          <Box p="xl" style={{ textAlign: 'center', marginTop: '20%' }}>
            <Group justify="center" gap="md" mb="md">
              <Icon path={mdiAlertCircleOutline} size={1.5} color="#868e96" />
              <Text size="lg" c="dimmed">Aucun élément sélectionné.</Text>
            </Group>
            <Text size="sm" c="dimmed">
              Veuillez sélectionner un patient pour afficher ses états.
            </Text>
          </Box>
        ) : (
          <Box p="md">
            <Text size="lg" fw={500} mb="md">
              États de {selectedPatient.full_name}
            </Text>
            <Text size="sm" c="dimmed" mb="lg">
              Source: {selectedState.label}
            </Text>

            {/* Ici on pourrait afficher les données réelles du patient */}
            <Box p="xl" style={{ textAlign: 'center', border: '2px dashed #e9ecef', borderRadius: 8 }}>
              <Text size="md" c="dimmed">
                Les données du patient seront affichées ici selon la source sélectionnée.
              </Text>
            </Box>
          </Box>
        )}
      </Box>

      {/* Modal de sélection de patient */}
      <Modal
        opened={patientModalOpened}
        onClose={() => setPatientModalOpened(false)}
        title="Sélectionner un patient"
        size="lg"
      >
        <Box>
          <TextInput
            placeholder="Rechercher un patient..."
            value={patientSearch}
            onChange={(event) => setPatientSearch(event.currentTarget.value)}
            leftSection={<Icon path={mdiMagnify} size={0.8} />}
            mb="md"
          />

          <ScrollArea style={{ height: 300 }}>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Nom complet</Table.Th>
                  <Table.Th>Téléphone</Table.Th>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Action</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredPatients.map((patient) => (
                  <Table.Tr key={patient.id}>
                    <Table.Td>{patient.full_name}</Table.Td>
                    <Table.Td>{patient.phone}</Table.Td>
                    <Table.Td>{patient.email}</Table.Td>
                    <Table.Td>
                      <ActionIcon
                        variant="subtle"
                        onClick={() => handlePatientSelect(patient)}
                        title="Sélectionner"
                      >
                        <Icon path={mdiMagnify} size={0.8} />
                      </ActionIcon>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {filteredPatients.length === 0 && (
            <Text size="sm" c="dimmed" ta="center" mt="md">
              Aucun patient trouvé
            </Text>
          )}
        </Box>
      </Modal>
    </Paper>
  );
};

export default EtatsDuPatient;
