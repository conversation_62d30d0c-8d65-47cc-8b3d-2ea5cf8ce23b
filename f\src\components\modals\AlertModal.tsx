'use client';
import { useState } from 'react';
import {
  Modal,
  Stack,
  Text,
  Group,
  Button,
  ActionIcon,
  Textarea,
  Select,
  Radio,
  Switch,
  MultiSelect,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import Icon from '@mdi/react';
import {
  mdiAccountAlert,
  mdiMicrophone,
  mdiClipboardText,
  mdiDeleteSweep,
} from '@mdi/js';
import SimpleBar from "simplebar-react";

interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface AlertModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];
  onOpenMicrophone?: () => void;
  onOpenDictionary?: () => void;
}

const AlertModal = ({ 
  opened, 
  onClose, 
  onSubmit, 
  fullName, 
  staffOptions, 
  triggerOptions,
  onOpenMicrophone,
  onOpenDictionary
}: AlertModalProps) => {
  const form = useForm<AlertFormValues>({
    initialValues: {
      trigger_for: [],
      trigger: '',
      level: 'MINIMUM',
      description: '',
      is_permanent: false,
    },
    validate: {
      trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
      trigger: (value) => (!value ? 'Champ requis' : null),
      description: (value) => (!value ? 'Champ requis' : null),
    },
  });

  const handleSubmit = (autoTrigger: boolean) => {
    if (form.isValid()) {
      onSubmit(form.values, autoTrigger);
      form.reset();
      onClose();
    }
  };

  const handleCancel = () => {
    form.reset();
    onClose();
  };

  const clearDescription = () => {
    form.setFieldValue('description', '');
  };

  return (
    <Modal
      opened={opened}
      onClose={handleCancel}
      size="lg"
      radius={0}
      transitionProps={{ transition: 'fade', duration: 200 }}
      centered
      withCloseButton={false}
    >
      <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
        <Modal.Title>
          <Group>
            <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
            <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
              {`Alerte - ${fullName}`} 
            </Text>
          </Group>
        </Modal.Title>
        <Group justify="flex-end">
          <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
        </Group>
      </Modal.Header>
      
      <Modal.Body style={{ padding: '0px' }}>
        <div className="py-2 pl-4 h-[300px]">
          <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
            <div className="pr-4">
              <form
                onSubmit={form.onSubmit((values) => onSubmit(values, false))}
                style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
              >
                <Group>
                  <MultiSelect
                    label="Déclencher pour"
                    data={staffOptions}
                    {...form.getInputProps('trigger_for')}
                    required
                    w={"30%"}
                  />
                  <Select
                    label="Déclencheur"
                    data={triggerOptions}
                    {...form.getInputProps('trigger')}
                    required
                    w={"30%"}
                  />
                  <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                    <Group>
                      <Radio value="MINIMUM" label="Minimum" />
                      <Radio value="MEDIUM" label="Moyen" />
                      <Radio value="HIGH" label="Haut" />
                    </Group>
                  </Radio.Group>
                </Group>
                
                <Group justify="space-between">
                  <Text>Description *</Text>
                  <Group>
                    <ActionIcon 
                      variant="subtle" 
                      aria-label="Microphone" 
                      color="#3799CE" 
                      onClick={onOpenMicrophone}
                    >
                      <Icon path={mdiMicrophone} size={1} />
                    </ActionIcon>
                    <ActionIcon 
                      variant="subtle" 
                      aria-label="Dictionary" 
                      color="#3799CE" 
                      onClick={onOpenDictionary}
                    >
                      <Icon path={mdiClipboardText} size={1} />
                    </ActionIcon>
                    <ActionIcon 
                      variant="subtle" 
                      aria-label="Clear" 
                      color="red" 
                      onClick={clearDescription}
                    >
                      <Icon path={mdiDeleteSweep} size={1} />
                    </ActionIcon>
                  </Group>
                </Group>
                
                <Textarea
                  placeholder="Ajouter"
                  {...form.getInputProps('description')}
                  required
                />
              
                <Switch
                  label="Permanente"
                  {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                />
        
                <Group justify="flex-end" mt="md">
                  <Button color="gray" onClick={handleCancel}>
                    Annuler
                  </Button>
                  <Button
                    onClick={() => handleSubmit(true)}
                  >
                    Enregistrer et déclencher
                  </Button>
                  <Button
                    onClick={() => handleSubmit(false)}
                  >
                    Enregistrer
                  </Button>
                </Group>
              </form>
            </div>
          </SimpleBar>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default AlertModal;
