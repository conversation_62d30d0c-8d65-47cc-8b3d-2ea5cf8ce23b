'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { CalendrierDaccouchement } from './Calendrier_daccouchement';

export default function CalendrierDaccouchementDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    console.log(`Période sélectionnée: du ${startDate} au ${endDate}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} du calendrier d'accouchement en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression du calendrier d\'accouchement en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'patient_name': 'Nom du patient',
      'birth_date': 'Date de naissance',
      'last_menstrual_date': 'Date des dernières règles',
      'modified_conception_date': 'Date de conception modifier',
      'theoretical_term': 'Terme théorique'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <CalendrierDaccouchement
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function CalendrierDaccouchementLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <CalendrierDaccouchement
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onStateChange={(state) => console.log('State:', state)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
            onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function CalendrierDaccouchementWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données du calendrier d\'accouchement chargées pour:', query);
    }, 1000);
  };

  const handleStateChange = (state: any) => {
    console.log('Changement d\'état du calendrier:', state);
    if (state.name === 'pregnancy_delivery') {
      console.log('Mode calendrier des accouchements activé');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} du calendrier d'accouchement avec les données des patientes...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression du calendrier d\'accouchement avec les données des patientes...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'patient_name': 'Tri des patientes par nom',
      'birth_date': 'Tri par date de naissance',
      'last_menstrual_date': 'Tri par date des dernières règles',
      'modified_conception_date': 'Tri par date de conception modifiée',
      'theoretical_term': 'Tri par terme théorique'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <CalendrierDaccouchement
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec période personnalisée
export function CalendrierDaccouchementCustomPeriodDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query période personnalisée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    
    // Calculer la différence en jours
    const diffTime = Math.abs(new Date(query.end).getTime() - new Date(query.start).getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    alert(`Calendrier d'accouchement pour la période du ${startDate} au ${endDate} (${diffDays} jours)`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <CalendrierDaccouchement
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={(state) => console.log('State période personnalisée:', state)}
            onExport={(format) => alert(`Export ${format} pour période personnalisée`)}
            onPrint={() => alert('Impression pour période personnalisée')}
            onSort={(columnId, direction) => console.log('Sort période personnalisée:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function CalendrierDaccouchementErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec gestion d\'erreurs:', query);
    
    // Simuler une validation de période
    const startDate = new Date(query.start);
    const endDate = new Date(query.end);
    
    if (endDate < startDate) {
      alert('Erreur: La date de fin ne peut pas être antérieure à la date de début');
      return;
    }
    
    // Simuler une période trop longue
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      alert('Attention: La période sélectionnée est très longue (plus d\'un an). Cela peut affecter les performances.');
    }
    
    console.log('Période validée, chargement des données...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <CalendrierDaccouchement
            loading={false}
            onQueryChange={handleQueryChange}
            onStateChange={(state) => console.log('State avec validation:', state)}
            onExport={(format) => {
              console.log(`Export ${format} avec validation`);
              if (confirm(`Êtes-vous sûr de vouloir exporter le calendrier d'accouchement en ${format.toUpperCase()} ?`)) {
                alert('Export en cours...');
              }
            }}
            onPrint={() => {
              console.log('Impression avec validation');
              if (confirm('Êtes-vous sûr de vouloir imprimer le calendrier d\'accouchement ?')) {
                alert('Impression en cours...');
              }
            }}
            onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
