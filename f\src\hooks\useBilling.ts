/**
 * Custom hook for managing billing data
 * Provides easy access to billing CRUD operations
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  billingService, 
  Invoice, 
  Quote, 
  Payment, 
  Expense, 
  Contract,
  BillingStats 
} from '@/services/billingService';

interface UseBillingOptions {
  autoFetch?: boolean;
  dataTypes?: ('invoices' | 'quotes' | 'payments' | 'expenses' | 'contracts')[];
}

interface UseBillingReturn {
  // Data
  invoices: Invoice[];
  quotes: Quote[];
  payments: Payment[];
  expenses: Expense[];
  contracts: Contract[];
  
  // Loading states
  loading: boolean;
  invoicesLoading: boolean;
  quotesLoading: boolean;
  paymentsLoading: boolean;
  expensesLoading: boolean;
  contractsLoading: boolean;
  
  // Error state
  error: string | null;
  
  // Invoice actions
  fetchInvoices: (filters?: { status?: string; patientId?: string }) => Promise<void>;
  createInvoice: (invoiceData: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateInvoice: (id: string, invoiceData: Partial<Invoice>) => Promise<void>;
  deleteInvoice: (id: string) => Promise<void>;
  
  // Quote actions
  fetchQuotes: (filters?: { status?: string; patientId?: string }) => Promise<void>;
  createQuote: (quoteData: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  
  // Payment actions
  fetchPayments: (invoiceId?: string) => Promise<void>;
  createPayment: (paymentData: Omit<Payment, 'id' | 'createdAt'>) => Promise<void>;
  
  // Expense actions
  fetchExpenses: (filters?: { category?: string; status?: string }) => Promise<void>;
  createExpense: (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  
  // Contract actions
  fetchContracts: (filters?: { status?: string; clientId?: string }) => Promise<void>;
  createContract: (contractData: Omit<Contract, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  
  // Utility functions
  refreshAll: () => Promise<void>;
  getTotalRevenue: () => number;
  getPendingPayments: () => number;
  getOverdueInvoices: () => Invoice[];
}

export const useBilling = (options: UseBillingOptions = {}): UseBillingReturn => {
  const { autoFetch = true, dataTypes = ['invoices', 'quotes', 'payments', 'expenses', 'contracts'] } = options;

  // State
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  
  const [loading, setLoading] = useState(false);
  const [invoicesLoading, setInvoicesLoading] = useState(false);
  const [quotesLoading, setQuotesLoading] = useState(false);
  const [paymentsLoading, setPaymentsLoading] = useState(false);
  const [expensesLoading, setExpensesLoading] = useState(false);
  const [contractsLoading, setContractsLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Invoice functions
  const fetchInvoices = useCallback(async (filters?: { status?: string; patientId?: string }) => {
    setInvoicesLoading(true);
    setError(null);
    try {
      const data = await billingService.getInvoices(filters);
      setInvoices(data);
    } catch (err) {
      setError(`Failed to fetch invoices: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setInvoicesLoading(false);
    }
  }, []);

  const createInvoice = useCallback(async (invoiceData: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>) => {
    setError(null);
    try {
      const newInvoice = await billingService.createInvoice(invoiceData);
      setInvoices(prev => [newInvoice, ...prev]);
    } catch (err) {
      setError(`Failed to create invoice: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const updateInvoice = useCallback(async (id: string, invoiceData: Partial<Invoice>) => {
    setError(null);
    try {
      const updatedInvoice = await billingService.updateInvoice(id, invoiceData);
      setInvoices(prev => prev.map(invoice => invoice.id === id ? updatedInvoice : invoice));
    } catch (err) {
      setError(`Failed to update invoice: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const deleteInvoice = useCallback(async (id: string) => {
    setError(null);
    try {
      await billingService.deleteInvoice(id);
      setInvoices(prev => prev.filter(invoice => invoice.id !== id));
    } catch (err) {
      setError(`Failed to delete invoice: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Quote functions
  const fetchQuotes = useCallback(async (filters?: { status?: string; patientId?: string }) => {
    setQuotesLoading(true);
    setError(null);
    try {
      const data = await billingService.getQuotes(filters);
      setQuotes(data);
    } catch (err) {
      setError(`Failed to fetch quotes: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setQuotesLoading(false);
    }
  }, []);

  const createQuote = useCallback(async (quoteData: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>) => {
    setError(null);
    try {
      const newQuote = await billingService.createQuote(quoteData);
      setQuotes(prev => [newQuote, ...prev]);
    } catch (err) {
      setError(`Failed to create quote: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Payment functions
  const fetchPayments = useCallback(async (invoiceId?: string) => {
    setPaymentsLoading(true);
    setError(null);
    try {
      const data = await billingService.getPayments(invoiceId);
      setPayments(data);
    } catch (err) {
      setError(`Failed to fetch payments: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setPaymentsLoading(false);
    }
  }, []);

  const createPayment = useCallback(async (paymentData: Omit<Payment, 'id' | 'createdAt'>) => {
    setError(null);
    try {
      const newPayment = await billingService.createPayment(paymentData);
      setPayments(prev => [newPayment, ...prev]);
    } catch (err) {
      setError(`Failed to create payment: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Expense functions
  const fetchExpenses = useCallback(async (filters?: { category?: string; status?: string }) => {
    setExpensesLoading(true);
    setError(null);
    try {
      const data = await billingService.getExpenses(filters);
      setExpenses(data);
    } catch (err) {
      setError(`Failed to fetch expenses: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setExpensesLoading(false);
    }
  }, []);

  const createExpense = useCallback(async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    setError(null);
    try {
      const newExpense = await billingService.createExpense(expenseData);
      setExpenses(prev => [newExpense, ...prev]);
    } catch (err) {
      setError(`Failed to create expense: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Contract functions
  const fetchContracts = useCallback(async (filters?: { status?: string; clientId?: string }) => {
    setContractsLoading(true);
    setError(null);
    try {
      const data = await billingService.getContracts(filters);
      setContracts(data);
    } catch (err) {
      setError(`Failed to fetch contracts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setContractsLoading(false);
    }
  }, []);

  const createContract = useCallback(async (contractData: Omit<Contract, 'id' | 'createdAt' | 'updatedAt'>) => {
    setError(null);
    try {
      const newContract = await billingService.createContract(contractData);
      setContracts(prev => [newContract, ...prev]);
    } catch (err) {
      setError(`Failed to create contract: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const refreshAll = useCallback(async () => {
    setLoading(true);
    try {
      const promises = [];
      if (dataTypes.includes('invoices')) promises.push(fetchInvoices());
      if (dataTypes.includes('quotes')) promises.push(fetchQuotes());
      if (dataTypes.includes('payments')) promises.push(fetchPayments());
      if (dataTypes.includes('expenses')) promises.push(fetchExpenses());
      if (dataTypes.includes('contracts')) promises.push(fetchContracts());
      
      await Promise.all(promises);
    } finally {
      setLoading(false);
    }
  }, [dataTypes, fetchInvoices, fetchQuotes, fetchPayments, fetchExpenses, fetchContracts]);

  const getTotalRevenue = useCallback(() => {
    return invoices
      .filter(invoice => invoice.status === 'paid')
      .reduce((sum, invoice) => sum + invoice.total, 0);
  }, [invoices]);

  const getPendingPayments = useCallback(() => {
    return invoices
      .filter(invoice => invoice.status === 'sent')
      .reduce((sum, invoice) => sum + invoice.total, 0);
  }, [invoices]);

  const getOverdueInvoices = useCallback(() => {
    const today = new Date();
    return invoices.filter(invoice => 
      invoice.status === 'sent' && new Date(invoice.dueDate) < today
    );
  }, [invoices]);

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      refreshAll();
    }
  }, [autoFetch, refreshAll]);

  return {
    // Data
    invoices,
    quotes,
    payments,
    expenses,
    contracts,
    
    // Loading states
    loading,
    invoicesLoading,
    quotesLoading,
    paymentsLoading,
    expensesLoading,
    contractsLoading,
    
    // Error state
    error,
    
    // Actions
    fetchInvoices,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    fetchQuotes,
    createQuote,
    fetchPayments,
    createPayment,
    fetchExpenses,
    createExpense,
    fetchContracts,
    createContract,
    
    // Utility functions
    refreshAll,
    getTotalRevenue,
    getPendingPayments,
    getOverdueInvoices,
  };
};

export default useBilling;
