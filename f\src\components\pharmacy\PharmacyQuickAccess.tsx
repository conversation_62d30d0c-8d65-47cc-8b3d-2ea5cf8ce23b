/**
 * Pharmacy Quick Access Modal
 * Provides quick access to pharmacy inventory and medication data from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  SimpleGrid,
  ActionIcon,
  Tooltip,
  TextInput,
} from '@mantine/core';
import {
  IconPill,
  IconPackage,
  IconShoppingCart,
  IconTruck,
  IconChartPie,
  IconExternalLink,
  IconRefresh,
  IconPlus,
  IconEye,
  IconEdit,
  IconSearch,
} from '@tabler/icons-react';
import { usePharmacy } from '@/hooks/usePharmacy';
import PharmacyWidgets from './PharmacyWidgets';

// Import existing pharmacy components
import Pharmacie from '@/app/(dashboard)/pharmacy/Pharmacie/Pharmacie';
import Achat from '@/app/(dashboard)/pharmacy/Achat/Achat';
import Vent from '@/app/(dashboard)/pharmacy/Vent/Vent';

interface PharmacyQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  defaultTab?: 'dashboard' | 'medications' | 'inventory' | 'purchases' | 'sales' | 'analytics';
  dateRange?: { start: string; end: string };
  onNavigateToFullPage?: () => void;
}

const PharmacyQuickAccess: React.FC<PharmacyQuickAccessProps> = ({
  opened,
  onClose,
  defaultTab = 'dashboard',
  dateRange,
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedFamily, setSelectedFamily] = useState<string>('all');

  const {
    medications,
    inventory,
    purchaseOrders,
    salesOrders,
    suppliers,
    medicationFamilies,
    analytics,
    loading,
    refreshAll,
    getLowStockItems,
    getExpiredItems,
    getPendingPurchases,
    getTopSellingMedications,
    getPharmacyStats,
  } = usePharmacy({ 
    dateRange, 
    autoFetch: opened,
    dataTypes: ['medications', 'inventory', 'purchases', 'sales', 'analytics']
  });

  const pharmacyStats = getPharmacyStats();
  const lowStockItems = getLowStockItems();
  const expiredItems = getExpiredItems();
  const pendingPurchases = getPendingPurchases();
  const topMedications = getTopSellingMedications(5);

  const handleRefresh = () => {
    refreshAll();
  };

  const filteredMedications = medications.filter(med => {
    const matchesSearch = searchTerm === '' || 
      med.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      med.generic_name?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFamily = selectedFamily === 'all' || med.family_id === selectedFamily;
    return matchesSearch && matchesFamily;
  });

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <PharmacyWidgets 
              dateRange={dateRange}
              compact={false}
              showInventoryAlerts={true}
            />
          </Stack>
        );

      case 'medications':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconPill size={20} />
                  <Text fw={600}>Médicaments</Text>
                  <Badge color="blue">{filteredMedications.length}</Badge>
                </Group>
                <Group gap="xs">
                  <TextInput
                    placeholder="Rechercher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    leftSection={<IconSearch size={14} />}
                    size="xs"
                    style={{ width: 200 }}
                  />
                  <Select
                    value={selectedFamily}
                    onChange={(value) => setSelectedFamily(value || 'all')}
                    data={[
                      { value: 'all', label: 'Toutes familles' },
                      ...medicationFamilies.map(f => ({ value: f.id, label: f.name }))
                    ]}
                    size="xs"
                  />
                  <ActionIcon variant="light" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {filteredMedications.map((medication) => (
                    <Card key={medication.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{medication.name}</Text>
                          <Text size="xs" c="dimmed">
                            {medication.generic_name && `${medication.generic_name} | `}
                            {medication.dosage} | {medication.form}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Famille: {medication.family_name} | Fournisseur: {medication.supplier_name}
                          </Text>
                          {medication.active_ingredient && (
                            <Text size="xs" c="dimmed">Principe actif: {medication.active_ingredient}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={
                              medication.status === 'active' ? 'green' : 
                              medication.status === 'out_of_stock' ? 'red' : 'gray'
                            }
                          >
                            {medication.status}
                          </Badge>
                          {medication.prescription_required && (
                            <Badge size="xs" color="orange">Prescription</Badge>
                          )}
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {filteredMedications.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun médicament trouvé
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'inventory':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconPackage size={20} />
                  <Text fw={600}>Inventaire</Text>
                  <Badge color="green">{inventory.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {inventory.map((item) => (
                    <Card key={item.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{item.medication_name}</Text>
                          <Text size="xs" c="dimmed">
                            Dépôt: {item.depot_name} | Emplacement: {item.location}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Lot: {item.batch_number} | Expiration: {new Date(item.expiry_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600} c={item.current_stock <= item.minimum_stock ? 'orange' : 'green'}>
                              {item.current_stock}
                            </Text>
                            <Text size="xs" c="dimmed">Min: {item.minimum_stock}</Text>
                            <Text size="xs" c="dimmed">Max: {item.maximum_stock}</Text>
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              item.status === 'in_stock' ? 'green' : 
                              item.status === 'low_stock' ? 'orange' : 
                              item.status === 'out_of_stock' ? 'red' : 'gray'
                            }
                          >
                            {item.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {inventory.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun article en stock
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'purchases':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconTruck size={20} />
                  <Text fw={600}>Commandes d'Achat</Text>
                  <Badge color="orange">{purchaseOrders.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {purchaseOrders.map((order) => (
                    <Card key={order.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{order.order_number}</Text>
                          <Text size="xs" c="dimmed">
                            Fournisseur: {order.supplier_name}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Commande: {new Date(order.order_date).toLocaleDateString()} | 
                            Livraison: {new Date(order.expected_delivery).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {order.items.length} article(s)
                          </Text>
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600}>{order.total_amount.toLocaleString()}€</Text>
                            <Text size="xs" c="dimmed">Par: {order.created_by}</Text>
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              order.status === 'received' ? 'green' : 
                              order.status === 'confirmed' ? 'blue' : 
                              order.status === 'sent' ? 'orange' : 
                              order.status === 'cancelled' ? 'red' : 'gray'
                            }
                          >
                            {order.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {purchaseOrders.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune commande d'achat
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'sales':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconShoppingCart size={20} />
                  <Text fw={600}>Ventes</Text>
                  <Badge color="purple">{salesOrders.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {salesOrders.map((order) => (
                    <Card key={order.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{order.order_number}</Text>
                          <Text size="xs" c="dimmed">
                            Client: {order.customer_name || 'Client anonyme'}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Commande: {new Date(order.order_date).toLocaleDateString()}
                            {order.delivery_date && ` | Livraison: ${new Date(order.delivery_date).toLocaleDateString()}`}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {order.items.length} article(s)
                          </Text>
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600}>{order.total_amount.toLocaleString()}€</Text>
                            <Text size="xs" c="dimmed">Par: {order.created_by}</Text>
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              order.status === 'delivered' ? 'green' : 
                              order.status === 'confirmed' ? 'blue' : 
                              order.status === 'cancelled' ? 'red' : 'gray'
                            }
                          >
                            {order.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {salesOrders.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune vente enregistrée
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'analytics':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconChartPie size={20} />
                  <Text fw={600}>Analytiques Pharmacie</Text>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                {analytics ? (
                  <SimpleGrid cols={2} spacing="md">
                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Top Ventes</Text>
                      <Stack gap="xs">
                        {topMedications.map((item, index) => (
                          <Group key={item.medication.id} justify="space-between">
                            <Text size="xs">{item.medication.name}</Text>
                            <Badge size="xs" color="purple">
                              {item.sales} unités
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Performance Fournisseurs</Text>
                      <Stack gap="xs">
                        {analytics.supplier_performance.map((supplier) => (
                          <Group key={supplier.supplier_id} justify="space-between">
                            <Text size="xs">{supplier.supplier_name}</Text>
                            <Badge size="xs" color="blue">
                              {((supplier.on_time_deliveries / supplier.total_orders) * 100).toFixed(0)}%
                            </Badge>
                          </Group>
                        ))}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Alertes d'Expiration</Text>
                      <Stack gap="xs">
                        {analytics.expiry_alerts.slice(0, 5).map((alert) => (
                          <Group key={alert.medication_id} justify="space-between">
                            <Text size="xs">{alert.medication_name}</Text>
                            <Badge 
                              size="xs" 
                              color={alert.days_to_expiry <= 30 ? 'red' : 'orange'}
                            >
                              {alert.days_to_expiry}j
                            </Badge>
                          </Group>
                        ))}
                        {analytics.expiry_alerts.length === 0 && (
                          <Text size="xs" c="dimmed" ta="center">Aucune alerte</Text>
                        )}
                      </Stack>
                    </Card>

                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={500} mb="xs">Tendances</Text>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="xs">Rotation stock</Text>
                          <Text size="xs" fw={500}>{analytics.stock_turnover_rate.toFixed(1)}x</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Valeur totale</Text>
                          <Text size="xs" fw={500}>{analytics.total_stock_value.toLocaleString()}€</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs">Articles actifs</Text>
                          <Text size="xs" fw={500}>{analytics.total_medications}</Text>
                        </Group>
                      </Stack>
                    </Card>
                  </SimpleGrid>
                ) : (
                  <Text size="sm" c="dimmed" ta="center" p="xl">
                    Aucune donnée analytique disponible
                  </Text>
                )}
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconPill size={20} />
          <Text fw={600}>Gestion Pharmacie</Text>
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Accès rapide à l'inventaire et aux médicaments
          </Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconPill size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Médicaments</Text>
                <Text size="sm" fw={600}>{pharmacyStats.totalMedications}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconPackage size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Valeur Stock</Text>
                <Text size="sm" fw={600}>{(pharmacyStats.totalStockValue / 1000).toFixed(0)}k€</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconTruck size={16} color="orange" />
              <div>
                <Text size="xs" c="dimmed">Commandes</Text>
                <Text size="sm" fw={600}>{pendingPurchases.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconShoppingCart size={16} color="purple" />
              <div>
                <Text size="xs" c="dimmed">Ventes</Text>
                <Text size="sm" fw={600}>{(pharmacyStats.monthlyRevenue / 1000).toFixed(0)}k€</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconChartPie size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab 
              value="medications" 
              leftSection={<IconPill size={16} />}
              rightSection={<Badge size="xs" color="blue">{medications.length}</Badge>}
            >
              Médicaments
            </Tabs.Tab>
            <Tabs.Tab 
              value="inventory" 
              leftSection={<IconPackage size={16} />}
              rightSection={<Badge size="xs" color="green">{inventory.length}</Badge>}
            >
              Inventaire
            </Tabs.Tab>
            <Tabs.Tab 
              value="purchases" 
              leftSection={<IconTruck size={16} />}
              rightSection={<Badge size="xs" color="orange">{purchaseOrders.length}</Badge>}
            >
              Achats
            </Tabs.Tab>
            <Tabs.Tab 
              value="sales" 
              leftSection={<IconShoppingCart size={16} />}
              rightSection={<Badge size="xs" color="purple">{salesOrders.length}</Badge>}
            >
              Ventes
            </Tabs.Tab>
            <Tabs.Tab value="analytics" leftSection={<IconChartPie size={16} />}>
              Analytiques
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default PharmacyQuickAccess;
