// components/CustomTable.tsx
import {
  Table, TextInput, Button, Menu, Checkbox, Pagination,  ScrollArea,
  Group, Box, Flex, Text, Loader
} from '@mantine/core';
import { IconSearch, IconDotsVertical, IconReload, IconFileSpreadsheet } from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import patientService from '@/services/patientService';
import { notifications } from '@mantine/notifications';

type Column = {
  key: string;
  label: string;
  isShown: boolean;
  isSearchable?: boolean;
  isOrderable?: boolean;
};

interface PatientTableData {
  id: string;
  lastName: string;
  firstName: string;
  email: string;
  phone_number: string;
  date_of_birth: string;
  gender: string;
  created_at: string;
  file_number?: number;
  category?: string;
  is_active: boolean;
}

interface CustomTableProps {
  patientId?: string;
  showPatientData?: boolean;
}

const initialColumns: Column[] = [
  { key: 'lastName', label: 'Nom', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'firstName', label: 'Prénom', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'email', label: 'Email', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'phone_number', label: 'Téléphone', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'date_of_birth', label: 'Date de naissance', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'gender', label: 'Genre', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'file_number', label: 'N° Dossier', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'category', label: 'Catégorie', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'created_at', label: 'Date création', isShown: true, isSearchable: true, isOrderable: true },
];

const CustomTable = ({ patientId, showPatientData = false }: CustomTableProps) => {
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [searchValues, setSearchValues] = useState<Record<string, string>>({});
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [patientData, setPatientData] = useState<PatientTableData[]>([]);
  const [totalPages, setTotalPages] = useState(1);

  // Load patient data from Django
  useEffect(() => {
    if (showPatientData) {
      loadPatientData();
    }
  }, [showPatientData, page, query]);

  const loadPatientData = async () => {
    try {
      setLoading(true);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        throw new Error('Django backend is not connected');
      }

      // Load patients list from Django
      const patients = await patientService.getPatients({
        page: page,
        search: query,
        page_size: 10
      });

      if (patients && Array.isArray(patients)) {
        // Transform Django patient data to table format
        const tableData: PatientTableData[] = patients.map((patient: any) => ({
          id: patient.id || '',
          lastName: patient.last_name || '',
          firstName: patient.first_name || '',
          email: patient.email || '',
          phone_number: patient.phone_number || '',
          date_of_birth: patient.date_of_birth || '',
          gender: patient.gender || '',
          created_at: patient.date_joined ? new Date(patient.date_joined).toLocaleDateString() : '',
          file_number: patient.file_number || 0,
          category: patient.category || '',
          is_active: patient.is_active || false,
        }));

        setPatientData(tableData);
        setTotalPages(Math.ceil(patients.length / 10));
      }
    } catch (error) {
      console.error('Error loading patient data:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load patient data. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleColumnVisibility = (key: string) => {
    setColumns(prev =>
      prev.map(col => col.key === key ? { ...col, isShown: !col.isShown } : col)
    );
  };

  const handleExportExcel = async () => {
    try {
      setLoading(true);
      // TODO: Implement real Excel export functionality
      // This could export the current patient data to Excel format
      notifications.show({
        title: 'Export',
        message: 'Excel export functionality will be implemented soon.',
        color: 'blue',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export data.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReload = () => {
    if (showPatientData) {
      loadPatientData();
    }
  };

  const filteredColumns = columns.filter(c => c.isShown);

  return (
    <Box>
      <Flex justify="space-between" align="center" mb="md">
        <Group>
          <Button
            variant="light"
            leftSection={<IconReload size={16} />}
            onClick={handleReload}
            loading={loading}
          >
            Recharger
          </Button>
          <Button
            variant="light"
            leftSection={<IconFileSpreadsheet size={16} />}
            onClick={handleExportExcel}
            loading={loading}
          >
            Exporter
          </Button>
        </Group>
        <TextInput
          leftSection={<IconSearch size={16} />}
          placeholder="Rechercher globalement"
          value={query}
          onChange={(e) => setQuery(e.currentTarget.value)}
        />
        <Menu shadow="md" width={220}>
          <Menu.Target>
            <Button variant="light"><IconDotsVertical size={18} /></Button>
          </Menu.Target>
          <Menu.Dropdown>
            {columns.map(col => (
              <Menu.Item key={col.key} rightSection={
                <Checkbox checked={col.isShown} readOnly />
              } onClick={() => toggleColumnVisibility(col.key)}>
                {col.label}
              </Menu.Item>
            ))}
          </Menu.Dropdown>
        </Menu>
      </Flex>

      <ScrollArea>
        <Table striped withRowBorders withColumnBorders>
          <Table.Thead>
            <Table.Tr>
              {filteredColumns.map(col => (
                <Table.Th key={col.key}>{col.label}</Table.Th>
              ))}
            </Table.Tr>
            <Table.Tr>
              {filteredColumns.map(col => (
                <Table.Th key={col.key}>
                  {col.isSearchable && (
                    <TextInput
                      placeholder="Rechercher"
                      value={searchValues[col.key] || ''}
                      onChange={(e) =>
                        setSearchValues({
                          ...searchValues,
                          [col.key]: e.currentTarget.value,
                        })
                      }
                    />
                  )}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {loading ? (
              <Table.Tr>
                <Table.Td colSpan={filteredColumns.length}>
                  <Group justify="center" p="md">
                    <Loader size="sm" />
                    <Text size="sm">Loading patient data...</Text>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ) : patientData.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={filteredColumns.length}>
                  <Text ta="center" c="dimmed" p="md">
                    {showPatientData ? 'No patients found.' : 'No data to display.'}
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              patientData.map((patient) => (
                <Table.Tr key={patient.id}>
                  {filteredColumns.map((col) => (
                    <Table.Td key={col.key}>
                      {patient[col.key as keyof PatientTableData]?.toString() || '-'}
                    </Table.Td>
                  ))}
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </ScrollArea>

      <Pagination total={totalPages} value={page} onChange={setPage} mt="md" />
    </Box>
  );
};

export default CustomTable;
