import React, { useState } from 'react';
import {
  Button,
  Card,
  Group,
  Stack,
  Table,
  Text,
  TextInput,
  ActionIcon,
  Tooltip,
  Modal,
  Alert,
  NumberInput,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconAlertCircle,
} from '@tabler/icons-react';

// Types pour les données
interface Procedure {
  id: number;
  code: string;
  name: string;
  fee: number;
}

interface Catalogue {
  id: number;
  name: string;
  procedures: Procedure[];
}

const Catalogues_de_procedures = () => {
  // États pour la gestion des données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCatalogue, setSelectedCatalogue] = useState<Catalogue | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isProcedureModalOpen, setIsProcedureModalOpen] = useState(false);
  const [editingCatalogue, setEditingCatalogue] = useState<Catalogue | null>(null);
  const [editingProcedure, setEditingProcedure] = useState<Procedure | null>(null);

  // États pour le formulaire catalogue
  const [catalogueFormData, setCatalogueFormData] = useState({
    name: '',
  });

  // États pour le formulaire procédure
  const [procedureFormData, setProcedureFormData] = useState({
    code: '',
    name: '',
    fee: 0,
  });

  // Données de test pour les catalogues
  const [catalogues, setCatalogues] = useState<Catalogue[]>([
    {
      id: 1,
      name: 'Consultations générales',
      procedures: [
        { id: 1, code: 'CONS001', name: 'Consultation générale', fee: 50 },
        { id: 2, code: 'CONS002', name: 'Consultation spécialisée', fee: 80 },
      ],
    },
    {
      id: 2,
      name: 'Examens médicaux',
      procedures: [
        { id: 3, code: 'EXAM001', name: 'Radiographie', fee: 120 },
        { id: 4, code: 'EXAM002', name: 'Échographie', fee: 150 },
      ],
    },
  ]);

  // Filtrage des catalogues
  const filteredCatalogues = catalogues.filter(catalogue =>
    catalogue.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Ouverture du modal pour nouveau/édition catalogue
  const openCatalogueModal = (catalogue?: Catalogue) => {
    if (catalogue) {
      setEditingCatalogue(catalogue);
      setCatalogueFormData({
        name: catalogue.name,
      });
    } else {
      setEditingCatalogue(null);
      setCatalogueFormData({
        name: '',
      });
    }
    setIsModalOpen(true);
  };

  // Fermeture du modal catalogue
  const closeCatalogueModal = () => {
    setIsModalOpen(false);
    setEditingCatalogue(null);
    setCatalogueFormData({
      name: '',
    });
  };

  // Ouverture du modal pour nouveau/édition procédure
  const openProcedureModal = (procedure?: Procedure) => {
    if (procedure) {
      setEditingProcedure(procedure);
      setProcedureFormData({
        code: procedure.code,
        name: procedure.name,
        fee: procedure.fee,
      });
    } else {
      setEditingProcedure(null);
      setProcedureFormData({
        code: '',
        name: '',
        fee: 0,
      });
    }
    setIsProcedureModalOpen(true);
  };

  // Fermeture du modal procédure
  const closeProcedureModal = () => {
    setIsProcedureModalOpen(false);
    setEditingProcedure(null);
    setProcedureFormData({
      code: '',
      name: '',
      fee: 0,
    });
  };

  // Sauvegarde d'un catalogue
  const saveCatalogue = () => {
    if (!catalogueFormData.name.trim()) {
      return;
    }

    if (editingCatalogue) {
      // Modification
      setCatalogues(prev =>
        prev.map(catalogue =>
          catalogue.id === editingCatalogue.id
            ? { ...catalogue, name: catalogueFormData.name }
            : catalogue
        )
      );
      if (selectedCatalogue?.id === editingCatalogue.id) {
        setSelectedCatalogue(prev => prev ? { ...prev, name: catalogueFormData.name } : null);
      }
    } else {
      // Création
      const newCatalogue: Catalogue = {
        id: Date.now(),
        name: catalogueFormData.name,
        procedures: [],
      };
      setCatalogues(prev => [...prev, newCatalogue]);
    }
    closeCatalogueModal();
  };

  // Sauvegarde d'une procédure
  const saveProcedure = () => {
    if (!procedureFormData.code.trim() || !procedureFormData.name.trim() || !selectedCatalogue) {
      return;
    }

    if (editingProcedure) {
      // Modification
      const updatedCatalogue = {
        ...selectedCatalogue,
        procedures: selectedCatalogue.procedures.map(proc =>
          proc.id === editingProcedure.id
            ? { ...proc, ...procedureFormData }
            : proc
        ),
      };
      setSelectedCatalogue(updatedCatalogue);
      setCatalogues(prev =>
        prev.map(cat =>
          cat.id === selectedCatalogue.id ? updatedCatalogue : cat
        )
      );
    } else {
      // Création
      const newProcedure: Procedure = {
        id: Date.now(),
        ...procedureFormData,
      };
      const updatedCatalogue = {
        ...selectedCatalogue,
        procedures: [...selectedCatalogue.procedures, newProcedure],
      };
      setSelectedCatalogue(updatedCatalogue);
      setCatalogues(prev =>
        prev.map(cat =>
          cat.id === selectedCatalogue.id ? updatedCatalogue : cat
        )
      );
    }
    closeProcedureModal();
  };

  // Suppression d'un catalogue
  const deleteCatalogue = (id: number) => {
    setCatalogues(prev => prev.filter(catalogue => catalogue.id !== id));
    if (selectedCatalogue?.id === id) {
      setSelectedCatalogue(null);
    }
  };

  // Suppression d'une procédure
  const deleteProcedure = (id: number) => {
    if (!selectedCatalogue) return;

    const updatedCatalogue = {
      ...selectedCatalogue,
      procedures: selectedCatalogue.procedures.filter(proc => proc.id !== id),
    };
    setSelectedCatalogue(updatedCatalogue);
    setCatalogues(prev =>
      prev.map(cat =>
        cat.id === selectedCatalogue.id ? updatedCatalogue : cat
      )
    );
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* Sidebar gauche avec les catalogues */}
      <div className="w-64 bg-white border-r border-gray-200">
        {/* Header avec bouton nouveau */}
        <div className="bg-blue-500 text-white p-3">
          <Group justify="space-between" align="center">
            <Text size="sm" fw={600}>
              Catalogues de procédures
            </Text>
            <Tooltip label="Ajouter catalogue">
              <ActionIcon
                variant="subtle"
                color="white"
                size="sm"
                onClick={() => openCatalogueModal()}
              >
                <IconPlus size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </div>

        {/* Section Catalogues avec recherche */}
        <div className="p-3">
          <div className="bg-blue-100 p-2 rounded mb-3">
            <Group gap="xs" align="center">
              <IconSearch size={16} className="text-blue-600" />
              <Text size="sm" fw={500} className="text-blue-800">
                Catalogues
              </Text>
            </Group>
          </div>

          <TextInput
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftSection={<IconSearch size={16} />}
            size="sm"
            className="mb-3"
          />

          {/* Liste des catalogues */}
          <Stack gap="xs">
            {filteredCatalogues.length > 0 ? (
              filteredCatalogues.map((catalogue) => (
                <Card
                  key={catalogue.id}
                  padding="xs"
                  radius="sm"
                  className={`cursor-pointer border transition-colors ${
                    selectedCatalogue?.id === catalogue.id
                      ? 'bg-blue-50 border-blue-300'
                      : 'hover:bg-gray-50 border-gray-200'
                  }`}
                  onClick={() => setSelectedCatalogue(catalogue)}
                >
                  <Group justify="space-between" align="center">
                    <Text size="sm" className="flex-1">
                      {catalogue.name}
                    </Text>
                    <Group gap="xs">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            openCatalogueModal(catalogue);
                          }}
                        >
                          <IconEdit size={12} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteCatalogue(catalogue.id);
                          }}
                        >
                          <IconTrash size={12} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Group>
                </Card>
              ))
            ) : (
              <Alert
                icon={<IconAlertCircle size={16} />}
                color="yellow"
                variant="light"
                size="sm"
              >
                <Text size="sm">Aucun élément trouvé</Text>
              </Alert>
            )}
          </Stack>
        </div>
      </div>

      {/* Zone de contenu principal */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Header avec bouton Ajouter catalogue */}
        <div className="bg-blue-500 text-white p-3 flex justify-between items-center">
          <Text size="lg" fw={600}>
            Catalogues de procédures
          </Text>
          <Button
            variant="subtle"
            color="white"
            leftSection={<IconPlus size={16} />}
            onClick={() => openCatalogueModal()}
            className="text-white hover:bg-blue-600"
          >
            Ajouter catalogue
          </Button>
        </div>

        {/* Contenu principal */}
        <div className="flex-1 p-6">
          {selectedCatalogue ? (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Stack gap="md">
                {/* Nom */}
                <div>
                  <Text size="sm" fw={500} mb="xs" className="text-red-500">
                    Nom *
                  </Text>
                  <TextInput
                    value={selectedCatalogue.name}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>

                {/* Tableau des procédures */}
                <div>
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                  >
                    <Table.Thead className="bg-blue-50">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 text-left">
                          Code
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 text-left">
                          Nom
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 text-left">
                          Honoraire
                        </Table.Th>
                        <Table.Th className="text-center w-20">
                          Actions
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {selectedCatalogue.procedures.length > 0 ? (
                        selectedCatalogue.procedures.map((procedure) => (
                          <Table.Tr key={procedure.id}>
                            <Table.Td className="border-r border-gray-200 text-sm">
                              {procedure.code}
                            </Table.Td>
                            <Table.Td className="border-r border-gray-200 text-sm">
                              {procedure.name}
                            </Table.Td>
                            <Table.Td className="border-r border-gray-200 text-sm">
                              {procedure.fee} €
                            </Table.Td>
                            <Table.Td className="text-center">
                              <Group gap="xs" justify="center">
                                <Tooltip label="Modifier">
                                  <ActionIcon
                                    variant="subtle"
                                    color="blue"
                                    size="sm"
                                    onClick={() => openProcedureModal(procedure)}
                                  >
                                    <IconEdit size={14} />
                                  </ActionIcon>
                                </Tooltip>
                                <Tooltip label="Supprimer">
                                  <ActionIcon
                                    variant="subtle"
                                    color="red"
                                    size="sm"
                                    onClick={() => deleteProcedure(procedure.id)}
                                  >
                                    <IconTrash size={14} />
                                  </ActionIcon>
                                </Tooltip>
                              </Group>
                            </Table.Td>
                          </Table.Tr>
                        ))
                      ) : (
                        <Table.Tr>
                          <Table.Td colSpan={4} className="text-center text-gray-500 py-8">
                            Aucun élément trouvé
                          </Table.Td>
                        </Table.Tr>
                      )}
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Boutons d'action */}
                <Group justify="flex-end" gap="sm" mt="md">
                  <Button
                    variant="outline"
                    color="blue"
                    leftSection={<IconPlus size={16} />}
                    onClick={() => openProcedureModal()}
                  >
                    Ajouter Procédures
                  </Button>
                  <Button
                    color="blue"
                    onClick={() => saveCatalogue()}
                  >
                    Enregistrer
                  </Button>
                </Group>
              </Stack>
            </Card>
          ) : (
            <div className="flex items-center justify-center h-full">
              <Text size="lg" className="text-gray-500">
                Sélectionnez un catalogue pour voir les détails
              </Text>
            </div>
          )}
        </div>
      </div>

      {/* Modal pour ajouter/modifier un catalogue */}
      <Modal
        opened={isModalOpen}
        onClose={closeCatalogueModal}
        title={editingCatalogue ? 'Modifier le catalogue' : 'Nouveau catalogue'}
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Nom"
            placeholder="Entrez le nom du catalogue"
            value={catalogueFormData.name}
            onChange={(e) => setCatalogueFormData({ ...catalogueFormData, name: e.target.value })}
            required
          />

          <Group justify="flex-end" gap="sm" mt="md">
            <Button variant="outline" onClick={closeCatalogueModal}>
              Annuler
            </Button>
            <Button onClick={saveCatalogue} disabled={!catalogueFormData.name.trim()}>
              {editingCatalogue ? 'Modifier' : 'Ajouter'}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal pour ajouter/modifier une procédure */}
      <Modal
        opened={isProcedureModalOpen}
        onClose={closeProcedureModal}
        title={editingProcedure ? 'Modifier la procédure' : 'Nouvelle procédure'}
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Code"
            placeholder="Entrez le code"
            value={procedureFormData.code}
            onChange={(e) => setProcedureFormData({ ...procedureFormData, code: e.target.value })}
            required
          />

          <TextInput
            label="Nom"
            placeholder="Entrez le nom"
            value={procedureFormData.name}
            onChange={(e) => setProcedureFormData({ ...procedureFormData, name: e.target.value })}
            required
          />

          <NumberInput
            label="Honoraire"
            placeholder="Entrez l'honoraire"
            value={procedureFormData.fee}
            onChange={(value) => setProcedureFormData({ ...procedureFormData, fee: Number(value) || 0 })}
            min={0}
            step={0.01}
            decimalScale={2}
            suffix=" €"
            required
          />

          <Group justify="flex-end" gap="sm" mt="md">
            <Button variant="outline" onClick={closeProcedureModal}>
              Annuler
            </Button>
            <Button
              onClick={saveProcedure}
              disabled={!procedureFormData.code.trim() || !procedureFormData.name.trim()}
            >
              {editingProcedure ? 'Modifier' : 'Ajouter'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Catalogues_de_procedures;
