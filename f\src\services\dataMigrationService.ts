import api from '../lib/api';
import axios, { AxiosProgressEvent, AxiosRequestConfig } from 'axios';

// Define interfaces for API parameters
export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

export interface ApiData {
  [key: string]: unknown;
}

// Helper function to make API calls with fallback to direct service
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: ApiData | FormData,
  params?: ApiParams,
  options?: Partial<AxiosRequestConfig>
) => {
  // Try the API gateway endpoint first
  try {
    const config = {
      params,
      ...options
    };

    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      const response = await api[method](endpoint, data, config);
      return response.data;
    }
  } catch (gatewayError) {
    console.warn(`API gateway endpoint failed for ${method.toUpperCase()} ${endpoint}, trying direct service:`, gatewayError);

    // Fall back to direct service endpoint
    const directServiceUrl = process.env.NEXT_PUBLIC_PATIENT_SERVICE_URL || 'http://localhost:8002';
    const fullUrl = `${directServiceUrl}${endpoint}`;

    try {
      if (method === 'get') {
        const response = await axios.get(fullUrl, {
          params,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          ...options
        });
        return response.data;
      } else {
        const response = await axios[method](fullUrl, data, {
          params,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          ...options
        });
        return response.data;
      }
    } catch (directError) {
      console.error(`Direct service call failed for ${method.toUpperCase()} ${endpoint}:`, directError);
      throw directError;
    }
  }
};

export interface ImportResult {
  success: boolean;
  message: string;
  details?: string;
  stats?: {
    total: number;
    imported: number;
    skipped: number;
    errors: number;
  };
}

const dataMigrationService = {
  /**
   * Import patient data from a file (JSON, Excel, or CSV)
   */
  async importPatientData(file: File, onProgress?: (progress: number) => void): Promise<ImportResult> {
    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Set up progress tracking
      const config = {
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            if (onProgress) {
              onProgress(percentCompleted);
            }
          }
        }
      };

      // Send the file to the API
      const result = await makeApiCall('/api/data-migration/import-patients/', 'post', formData, undefined, config);

      return {
        success: true,
        message: 'Patient data imported successfully',
        stats: result.stats,
        details: result.details
      };
    } catch (error) {
      console.error('Error importing patient data:', error);

      return {
        success: false,
        message: 'Failed to import patient data',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  /**
   * Import location data from a JSON file
   */
  async importLocationData(file: File, onProgress?: (progress: number) => void): Promise<ImportResult> {
    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Set up progress tracking
      const config = {
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            if (onProgress) {
              onProgress(percentCompleted);
            }
          }
        }
      };

      // Send the file to the API
      const result = await makeApiCall('/api/data-migration/import-locations/', 'post', formData, undefined, config);

      return {
        success: true,
        message: 'Location data imported successfully',
        stats: result.stats,
        details: result.details
      };
    } catch (error) {
      console.error('Error importing location data:', error);

      return {
        success: false,
        message: 'Failed to import location data',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  /**
   * Export patient data to a file (JSON, Excel, or PDF)
   */
  async exportPatientData(format: 'json' | 'xlsx' | 'pdf'): Promise<Blob> {
    try {
      // Request the file from the API
      const response = await api.get('/api/data-migration/export-patients/', {
        params: { format },
        responseType: 'blob'
      });

      return response.data;
    } catch (error) {
      console.error('Error exporting patient data:', error);
      throw error;
    }
  }
};

export default dataMigrationService;
