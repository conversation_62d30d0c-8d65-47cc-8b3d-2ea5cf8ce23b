
import { Modal,  Text, Group, Button, ActionIcon, Checkbox } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiPlaylistCheck, mdiPlus, mdiDelete, mdiPencil } from '@mdi/js';
import { SavedModel } from './types';
import SimpleBar from "simplebar-react";
import { Alert } from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
interface SavedModelsModalProps {
  opened: boolean;
  onClose: () => void;
  savedModels: SavedModel[];
  onToggleModel: (modelId: string) => void;
  onDeleteModel: (modelId: string) => void;
  onEditModel: (modelId: string) => void;
  onValidate: () => void;
  onCancel: () => void;
  onNewModel: () => void;
}

export const SavedModelsModal: React.FC<SavedModelsModalProps> = ({
  opened,
  onClose,
  savedModels,
  onToggleModel,
  onDeleteModel,
  onEditModel,
  onValidate,
  onCancel,
  onNewModel
}) => {
  // Debug: Log de l'état d'ouverture
  console.log('SavedModelsModal render:', { opened, savedModelsLength: savedModels.length });
  const icon = <IconInfoCircle />;
  return (
    <>
 
    <Modal.Root
      opened={opened}
      onClose={onClose}
      transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
      centered
      size="lg"
    > 
    <Modal.Overlay />
    <Modal.Content className="overflow-y-hidden">
      <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
        <Modal.Title>
          <Group>
            <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
              <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                  Modèles de dictionnaire
                </Text>
          </Group>
        </Modal.Title>
          <Group justify="flex-end">
             <ActionIcon
            variant="subtle"
            color="blue"
            onClick={() => {
              console.log('ActionIcon clicked - calling onNewModel');
              onNewModel();
            }}
          >
            <Icon path={mdiPlus} size={0.8} color={'white'}/>
          </ActionIcon>
            <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
          </Group>
      </Modal.Header>
          <Modal.Body style={{ padding: '0px' }}>
                      
    <div className={savedModels.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
              <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                <div className="pr-4">
          
          {savedModels.length === 0 ? (
            <Group >
              {/* <Text c="dimmed">Aucun modèle à afficher</Text> */}
               <Alert variant="light" w={'100%'} color="yellow" title="" icon={icon}>
      Aucun modèle à afficher
    </Alert>
            </Group>
          ) : (
            savedModels.map((model) => (
              <Group key={model.id} justify="space-between" className='border border-[var(--mantine-color-default-border)] rounded-md p-2'>
                <Group>
                  <Checkbox
                    checked={model.selected || false}
                    onChange={() => onToggleModel(model.id)}
radius="xs"
                  />
                  <Text>{model.title}</Text>
                </Group>
  <Group justify="flex-end">
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={() => onEditModel(model.id)}
                    title="Éditer le titre"
                  >
                    <Icon path={mdiPencil} size={0.8} />
                  </ActionIcon>
                <ActionIcon
                  variant="subtle"
                  color="red"
                  onClick={() => onDeleteModel(model.id)}
                >
                  <Icon path={mdiDelete} size={0.8} />
                </ActionIcon>
</Group>
              </Group>
            ))
          )}

          <Group justify="flex-end" mt="md">
            {/* <Button
              variant="outline"
              color="blue"
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={onNewModel}
            >
              Nouveau modèle
            </Button> */}
            <Group>
              <Button onClick={onValidate}  color="#3799CE">
                Valider
              </Button>
              <Button variant="outline" color="red" onClick={onCancel}>
                Annuler
              </Button>
            </Group>
          </Group>
       
                </div>
              </SimpleBar>
            </div>
          </Modal.Body>
    </Modal.Content>
    </Modal.Root>
    </>
  );
};
