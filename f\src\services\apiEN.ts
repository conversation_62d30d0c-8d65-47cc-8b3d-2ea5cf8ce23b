/**
 * API service for backend integration
 * Provides methods to interact with the Django backend
 */

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>oint<PERSON>,
  DoctorPause,
  WaitingListItem,
  CalendarEvent,
  ApiResponse,
  ApiParams,
  ApiData,
  ApiMethod,
  PatientListResponse,
  PatientSearchResponse,
  PatientStatsResponse,
  AppointmentListResponse,
  CalendarEventsResponse,
  WaitingListResponse,
  PauseListResponse,
  DoctorPausesResponse,
  SuccessResponse,
  CreateResponse,
  ErrorResponse,
  PatientFormData,
  AppointmentFormData,
  PauseFormData
} from '@/types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api';

// API helper function
async function apiRequest<T = unknown>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  // Debug logging for appointments API calls
  if (endpoint.includes('/appointments/')) {
    console.log('🌐 API REQUEST:', {
      url: url,
      endpoint: endpoint,
      method: options.method || 'GET'
    });
  }

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    console.log(`Making API request to ${url}:`, {
      method: defaultOptions.method || 'GET',
      headers: defaultOptions.headers,
      body: defaultOptions.body
    });

    const response = await fetch(url, defaultOptions);

    if (!response.ok) {
      let errorData: ErrorResponse;
      let responseText = '';

      try {
        responseText = await response.text();
        if (responseText.trim()) {
          // Check if response is HTML (Django error page)
          if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
            console.error('Received HTML error page from server:', responseText.substring(0, 200) + '...');
            errorData = {
              error: `Server Error (${response.status}): ${response.statusText}`,
              details: {
                type: 'server_error',
                message: 'The server returned an HTML error page instead of JSON. Check server logs for details.'
              }
            };
          } else {
            errorData = JSON.parse(responseText);
          }
        } else {
          errorData = { error: `HTTP ${response.status}: ${response.statusText}` };
        }
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        errorData = {
          error: responseText || `HTTP ${response.status}: ${response.statusText}`,
          details: {
            parseError: parseError instanceof Error ? parseError.message : String(parseError),
            rawResponse: responseText?.substring(0, 200) + '...'
          }
        };
      }

      console.error(`API Error for ${endpoint}:`, {
        status: response.status,
        statusText: response.statusText,
        errorData,
        responseText,
        url,
        headers: Object.fromEntries(response.headers.entries())
      });

      // Try to extract meaningful error message
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

      if (errorData && typeof errorData === 'object') {
        if (errorData.error) {
          errorMessage = errorData.error;
        } else {
          // Handle Django validation errors
          const errors = Object.entries(errorData)
            .filter(([key]) => key !== 'error')
            .map(([field, messages]) => {
              if (Array.isArray(messages)) {
                return `${field}: ${messages.join(', ')}`;
              }
              return `${field}: ${messages}`;
            });

          if (errors.length > 0) {
            errorMessage = errors.join('; ');
          } else if (responseText) {
            errorMessage = responseText;
          }
        }
      } else if (responseText) {
        errorMessage = responseText;
      }

      throw new Error(errorMessage);
    }

    // Handle DELETE requests that return 204 No Content
    if (options.method === 'DELETE' && response.status === 204) {
      // Return undefined or empty object for DELETE requests
      return undefined as T;
    }

    // Handle responses with no content
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      // If response is not JSON, return undefined or empty object
      return undefined as T;
    }

    // Check if response body is empty before parsing
    const responseText = await response.text();
    if (!responseText) {
      return undefined as T;
    }

    return JSON.parse(responseText) as T;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// Patient API methods
export const patientAPI = {
  // Create a new patient
  create: async (patientData: PatientFormData): Promise<Patient> => {
    return apiRequest<Patient>('/api/users/patients/create-from-frontend/', {
      method: 'POST',
      body: JSON.stringify(patientData),
    });
  },

  // Quick create patient for calendar
  quickCreate: async (patientData: PatientFormData): Promise<CreateResponse<{ patient_id: string; name: string; email: string }>> => {
    return apiRequest<CreateResponse<{ patient_id: string; name: string; email: string }>>('/api/users/patients/quick-create/', {
      method: 'POST',
      body: JSON.stringify(patientData),
    });
  },

  // Get patient list
  list: async (params?: {
    doctor_id?: string;
    status?: string;
    search?: string;
  }): Promise<PatientListResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.search) queryParams.append('search', params.search);

    const endpoint = `/api/users/patients/list/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest<PatientListResponse>(endpoint);
  },

  // Get patient details
  get: async (patientId: string): Promise<Patient> => {
    return apiRequest<Patient>(`/api/users/patients/${patientId}/detail/`);
  },

  // Update patient
  update: async (patientId: string, patientData: Partial<Patient>): Promise<Patient> => {
    return apiRequest<Patient>(`/api/users/patients/${patientId}/update/`, {
      method: 'PUT',
      body: JSON.stringify(patientData),
    });
  },

  // Delete patient
  delete: async (patientId: string): Promise<void> => {
    return apiRequest(`/api/users/patients/${patientId}/enhanced/`, {
      method: 'DELETE',
    });
  },

  // Search patients
  search: async (query: string): Promise<PatientSearchResponse> => {
    return apiRequest<PatientSearchResponse>(`/api/users/patients/search/?q=${encodeURIComponent(query)}`);
  },

  // Get patient statistics
  stats: async (): Promise<PatientStatsResponse> => {
    return apiRequest<PatientStatsResponse>('/api/users/patients/stats/');
  },

  // Get doctors and assistants
  getDoctors: async (): Promise<{ results: Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    user_type: string;
    assigned_doctor?: string;
    assistants?: Array<{ id: string; first_name: string; last_name: string; email: string; }>;
  }> }> => {
    try {
      console.log('🔍 Calling getDoctors API endpoint: /api/users/doctors/');
      const response = await apiRequest('/api/users/doctors/') as unknown;
      console.log('🔍 getDoctors API response:', response);
      
      // Type-safe response handling
      let doctorResults: Array<{
        id: string;
        first_name: string;
        last_name: string;
        email: string;
        user_type?: string;
      }> = [];
      
      // Handle response structure safely
      if (Array.isArray(response)) {
        console.log('🔍 Response is array, using directly');
        doctorResults = response;
      } else if (response && typeof response === 'object' && 'results' in response) {
        console.log('🔍 Response has results property');
        const typedResponse = response as { results: typeof doctorResults };
        doctorResults = typedResponse.results || [];
      } else {
        console.warn('⚠️ Unexpected response structure, returning empty results');
        return { results: [] };
      }
      
      // Get current user info to filter only relevant doctors
      const currentUserId = typeof window !== 'undefined' ? localStorage.getItem('userId') : null;
      const currentUserEmail = typeof window !== 'undefined' ? localStorage.getItem('email') : null;
      
      console.log('🔍 Current user filter:', { currentUserId, currentUserEmail });
      
      // Filter to show only the current user (owner) if they are in the doctors list
      const filteredDoctors = doctorResults.filter(doctor => {
        const isCurrentUser = doctor.id === currentUserId || doctor.email === currentUserEmail;
        if (isCurrentUser) {
          console.log('🔍 Found current user as doctor:', doctor);
        }
        return isCurrentUser;
      });
      
      // Transform to expected format with user_type
      const transformedResults = filteredDoctors.map(doctor => ({
        id: doctor.id,
        first_name: doctor.first_name,
        last_name: doctor.last_name,
        email: doctor.email,
        user_type: 'doctor', // Set explicitly since backend doesn't return it
        assistants: []
      }));
      
      console.log('🔍 Filtered to current user only:', transformedResults);
      
      return { results: transformedResults };
    } catch (error) {
      console.error('❌ getDoctors API failed:', error);
      console.log('🔄 Falling back to current user from localStorage');
      
      // Fallback: use current user info from localStorage
      const currentUserId = typeof window !== 'undefined' ? localStorage.getItem('userId') : null;
      const currentUserFirstName = typeof window !== 'undefined' ? localStorage.getItem('firstName') : null;
      const currentUserLastName = typeof window !== 'undefined' ? localStorage.getItem('lastName') : null;
      const currentUserEmail = typeof window !== 'undefined' ? localStorage.getItem('email') : null;
      const currentUserType = typeof window !== 'undefined' ? localStorage.getItem('userType') : null;
      
      if (currentUserId && currentUserType === 'doctor') {
        return {
          results: [
            {
              id: currentUserId,
              first_name: currentUserFirstName || 'Current',
              last_name: currentUserLastName || 'User',
              email: currentUserEmail || '<EMAIL>',
              user_type: 'doctor',
              assistants: []
            }
          ]
        };
      }
      
      // If no current user info available, return empty
      return { results: [] };
    }
  },

  // Debug function to test the getDoctors endpoint
  testGetDoctors: async (): Promise<unknown> => {
    try {
      console.log('🧪 Testing getDoctors endpoint directly...');
      const response = await fetch('http://127.0.0.1:8000/api/users/doctors/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('🧪 Response status:', response.status);
      console.log('🧪 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('🧪 Error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('🧪 Raw response data:', data);
      console.log('🧪 Data type:', typeof data);
      console.log('🧪 Is array:', Array.isArray(data));
      console.log('🧪 Has results:', data?.results ? 'Yes' : 'No');
      
      if (data?.results) {
        console.log('🧪 Results length:', data.results.length);
        console.log('🧪 First result:', data.results[0]);
      }
      
      return data;
    } catch (error) {
      console.error('🧪 Test getDoctors failed:', error);
      throw error;
    }
  },

  // Get assistants assigned to current doctor
  getAssistants: async (): Promise<{ results: Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    user_type: string;
    assigned_doctor?: string;
  }> }> => {
    try {
      console.log('🔍 Calling getAssistants API endpoint: /api/users/assistants/');
      const response = await apiRequest('/api/users/assistants/') as unknown;
      console.log('🔍 getAssistants API response:', response);

      // Handle different response formats
      if (Array.isArray(response)) {
        return { results: response };
      } else if (response && typeof response === 'object' && 'results' in response) {
        return response as { results: Array<{
          id: string;
          first_name: string;
          last_name: string;
          email: string;
          user_type: string;
          assigned_doctor?: string;
        }> };
      } else {
        console.warn('Unexpected response format from assistants API:', response);
        return { results: [] };
      }
    } catch (error) {
      console.error('❌ getAssistants API failed:', error);
      return { results: [] };
    }
  },
};

// Appointment API methods
export const appointmentAPI = {
  // Create appointment
  create: async (appointmentData: AppointmentFormData): Promise<Appointment> => {
    return apiRequest<Appointment>('/api/appointments/', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
  },

  // Get appointments
  list: async (params?: {
    start_date?: string;
    end_date?: string;
    doctor_id?: string;
    status?: string;
    is_waiting_list?: boolean;
    limit?: number;
  }): Promise<AppointmentListResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.is_waiting_list !== undefined) queryParams.append('is_waiting_list', params.is_waiting_list.toString());

    // Set a high limit to get all appointments, or use provided limit
    const limit = params?.limit || 1000;
    queryParams.append('limit', limit.toString());

    const endpoint = `/api/appointments/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest<AppointmentListResponse>(endpoint);
  },

  // Get appointment details
  get: async (appointmentId: string): Promise<Appointment> => {
    return apiRequest<Appointment>(`/api/appointments/${appointmentId}/`);
  },

  // Update appointment
  update: async (appointmentId: string, appointmentData: Partial<Appointment>): Promise<Appointment> => {
    return apiRequest<Appointment>(`/api/appointments/${appointmentId}/`, {
      method: 'PUT',
      body: JSON.stringify(appointmentData),
    });
  },

  // Delete appointment
  delete: async (appointmentId: string): Promise<void> => {
    console.log('📡 DELETE appointment request:', {
      appointmentId: appointmentId,
      endpoint: `/api/appointments/${appointmentId}/`,
      method: 'DELETE'
    });
    
    try {
      const result = await apiRequest<void>(`/api/appointments/${appointmentId}/`, {
        method: 'DELETE',
      });
      
      console.log('✅ DELETE appointment response:', result);
      return result as void;
    } catch (error) {
      console.error('❌ DELETE appointment error:', error);
      throw error;
    }
  },

  // Reschedule appointment (using basic update)
  reschedule: async (appointmentId: string, newDate: string, newTime: string): Promise<Appointment> => {
    return apiRequest<Appointment>(`/appointments/${appointmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify({
        appointment_date: newDate,
        appointment_time: newTime,
        status: 'rescheduled'
      }),
    });
  },

  // Cancel appointment (using basic update)
  cancel: async (appointmentId: string, reason?: string): Promise<Appointment> => {
    return apiRequest<Appointment>(`/appointments/${appointmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify({
        status: 'cancelled',
        notes: reason ? `Cancelled: ${reason}` : 'Cancelled'
      }),
    });
  },

  // Complete appointment (using basic update)
  complete: async (appointmentId: string, notes?: string): Promise<Appointment> => {
    return apiRequest<Appointment>(`/appointments/${appointmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify({
        status: 'completed',
        notes: notes || 'Completed'
      }),
    });
  },

  // Get calendar events (using basic list)
  getCalendarEvents: async (params?: {
    start_date?: string;
    end_date?: string;
    doctor_id?: string;
  }): Promise<CalendarEventsResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id);

    const endpoint = `/api/appointments/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiRequest<AppointmentListResponse>(endpoint);

    // Handle both array response and paginated response
    const appointments = Array.isArray(response) ? response : (response.results || []);

    // Convert appointments to calendar events format
    const events = appointments.map((appointment: Appointment) => ({
      id: appointment.id,
      title: appointment.title,
      start: `${appointment.appointment_date}T${appointment.appointment_time}`,
      end: `${appointment.appointment_date}T${appointment.appointment_time}`, // Will be calculated on frontend
      description: appointment.description || '',
      color: appointment.color || '#3b82f6',
      status: appointment.status,
      type: appointment.appointment_type,
      room: appointment.room || '',
      resourceId: appointment.resource_id || '',
      patient_phone: appointment.patient_phone || '',
      doctor: appointment.doctor_assigned || '',
    }));

    return { events };
  },
};

// Waiting List API methods
export const waitingListAPI = {
  // Get waiting list
  get: async (doctorId?: string): Promise<WaitingListResponse> => {
    try {
      const endpoint = `/api/appointments/waiting-list/${doctorId ? '?doctor_id=' + doctorId : ''}`;
      return await apiRequest<WaitingListResponse>(endpoint);
    } catch (error) {
      console.warn('Waiting list endpoint not available, returning empty list:', error);
      // Return empty waiting list as fallback
      return {
        waiting_list: [],
        count: 0,
        message: 'Waiting list endpoint not available'
      };
    }
  },

  // Add to waiting list
  add: async (patientData: Partial<WaitingListItem & PatientFormData>): Promise<CreateResponse<{ patient_id: string }>> => {
    try {
      return await apiRequest<CreateResponse<{ patient_id: string }>>('/api/appointments/waiting-list/add/', {
        method: 'POST',
        body: JSON.stringify(patientData),
      });
    } catch (error) {
      console.warn('Waiting list add endpoint not available, creating local response:', error);
      // Return mock response for local handling
      return {
        id: `local-waiting-${Date.now()}`,
        data: { patient_id: `local-patient-${Date.now()}` },
        message: 'Added to local waiting list (backend endpoint not available)'
      };
    }
  },

  // Move to calendar
  moveToCalendar: async (appointmentId: string, appointmentDate: string, appointmentTime: string, room?: string, resourceId?: string): Promise<{ appointment: Appointment; message: string }> => {
    return apiRequest(`/appointments/waiting-list/${appointmentId}/move-to-calendar/`, {
      method: 'POST',
      body: JSON.stringify({
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        room,
        resource_id: resourceId,
      }),
    });
  },

  // Remove from waiting list
  remove: async (appointmentId: string): Promise<{ message: string }> => {
    return apiRequest(`/appointments/waiting-list/${appointmentId}/remove/`, {
      method: 'DELETE',
    });
  },

  // Update waiting list item
  update: async (appointmentId: string, updateData: Partial<WaitingListItem>): Promise<{ appointment: Appointment; message: string }> => {
    return apiRequest(`/appointments/waiting-list/${appointmentId}/update/`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Activate appointment
  activate: async (appointmentId: string): Promise<{ appointment: Appointment; message: string }> => {
    return apiRequest(`/appointments/waiting-list/${appointmentId}/activate/`, {
      method: 'POST',
    });
  },
};

// Doctor Pause API methods
export const pauseAPI = {
  // Create pause
  create: async (pauseData: PauseFormData): Promise<CreateResponse<DoctorPause>> => {
    return apiRequest<CreateResponse<DoctorPause>>('/api/appointments/pause-modal/create/', {
      method: 'POST',
      body: JSON.stringify(pauseData),
    });
  },

  // Get pauses
  list: async (params?: {
    doctor_id?: string;
    start_date?: string;
    end_date?: string;
    current_only?: boolean;
  }): Promise<{ results: DoctorPause[] }> => {
    const queryParams = new URLSearchParams();
    if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id);
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.current_only) queryParams.append('current_only', params.current_only.toString());

    // Try the calendar events endpoint first since it works
    try {
      console.log('🔍 Trying calendar events endpoint for pauses...');
      const calendarEndpoint = `/api/appointments/pauses/calendar_events/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      const calendarResponse = await apiRequest(calendarEndpoint) as unknown;
      
      // Type-safe handling of calendar response
      if (calendarResponse && typeof calendarResponse === 'object' && 'events' in calendarResponse) {
        const typedCalendarResponse = calendarResponse as {
          events: Array<{
            id: string;
            title: string;
            start: string;
            end: string;
            doctor?: string;
            duration_minutes?: number;
            notes?: string;
            is_recurring?: boolean;
            room?: string;
            resource_id?: string;
            color?: string;
          }>;
        };
        
        // Convert calendar events back to pause format - FIXED: Include room, resource_id, and color
        const pauses: DoctorPause[] = typedCalendarResponse.events.map((event) => ({
          id: event.id.replace('pause_', ''),
          doctor: event.doctor || params?.doctor_id || '',
          doctor_name: event.doctor || 'Unknown Doctor',
          title: event.title,
          date_from: event.start,
          date_to: event.end,
          duration_minutes: event.duration_minutes || 60,
          notes: event.notes || '',
          is_recurring: event.is_recurring || false,
          // CRITICAL: Preserve room, resource_id, and color fields
          room: event.room || undefined,
          resource_id: event.resource_id || undefined,
          color: event.color || undefined
        }));
        
        console.log('🔍 Successfully converted calendar events to pauses:', pauses);
        return { results: pauses };
      }
    } catch (calendarError) {
      console.warn('⚠️ Calendar events endpoint also failed:', calendarError);
    }
    
    // Fallback: try the direct list endpoint
    const endpoint = `/api/appointments/pauses/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    try {
      console.log('🔍 Calling pause API endpoint:', endpoint);
      const response = await apiRequest(endpoint) as { results: DoctorPause[] };
      console.log('🔍 Pause API response:', response);
      return response;
    } catch (error: unknown) {
      const apiError = error as Error;
      console.warn('⚠️ Pause API failed:', apiError.message);
      
      // Check if it's a 404 or similar "not found" error
      if (apiError.message?.includes('404') || apiError.message?.includes('Not found')) {
        console.log('🔄 Pause API endpoint not available, returning empty results');
        return { results: [] };
      }
      
      // For other errors, still return empty results but log the error
      console.error('❌ Unexpected pause API error:', apiError);
      return { results: [] };
    }
  },

  // Get doctor pauses
  getByDoctor: async (doctorId: string, startDate?: string, endDate?: string): Promise<{
    doctor: { id: string; name: string; email: string };
    pauses: DoctorPause[];
  }> => {
    const queryParams = new URLSearchParams();
    if (startDate) queryParams.append('start_date', startDate);
    if (endDate) queryParams.append('end_date', endDate);

    const endpoint = `/api/appointments/pauses/doctor/${doctorId}/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest(endpoint);
  },

  // Update pause
  update: async (pauseId: string, pauseData: Partial<DoctorPause>): Promise<{ pause: DoctorPause; message: string }> => {
    return apiRequest(`/api/appointments/pauses/${pauseId}/`, {
      method: 'PUT',
      body: JSON.stringify(pauseData),
    });
  },

  // Delete pause
  delete: async (pauseId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/appointments/pauses/${pauseId}/`, {
      method: 'DELETE',
    });
  },

  // Get calendar events for pauses
  getCalendarEvents: async (params?: {
    doctor_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<{ events: Array<{
    id: string;
    title: string;
    start: string;
    end: string;
    color: string;
    type: string;
    doctor: string;
    notes: string;
    duration_minutes: number;
    is_recurring: boolean;
    editable: boolean;
    className: string;
  }> }> => {
    const queryParams = new URLSearchParams();
    if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id);
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const endpoint = `/api/appointments/pauses/calendar_events/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest(endpoint);
  },

  // Get current pauses
  getCurrent: async (): Promise<{ current_pauses: DoctorPause[] }> => {
    return apiRequest('/api/appointments/pauses/current_pauses/');
  },
};
