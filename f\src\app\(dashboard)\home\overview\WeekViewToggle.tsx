'use client';

import React, { useState } from 'react';
import WeekView from './WeekView/WeekView';
import EnhancedWeekView from './EnhancedWeekView';
import { CalendarEvent } from '../CalendarPatient';
import '@/styles/enhanced-calendar.css';

interface WeekViewToggleProps {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick: (date: Date, hour: number, minute?: number, roomId?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
  onEventDrop?: (event: CalendarEvent, start: Date, end: Date) => void;
  onWaitingListUpdate?: () => void;
}

const WeekViewToggle: React.FC<WeekViewToggleProps> = ({
  currentDate,
  events,
  onTimeSlotClick,
  onEventClick,
  onDateChange,
  onNavigate,
  onEventDrop,
  onWaitingListUpdate
}) => {
  const [useEnhancedView, setUseEnhancedView] = useState(false);

  // Wrapper function to adapt WeekView's expected signature
  const handleClassicTimeSlotClick = (date: Date, hour: number, minute: number, roomId: string) => {
    onTimeSlotClick(date, hour, minute, roomId);
  };

  // Get week stats
  const weekEvents = events.filter(event => {
    const eventDate = new Date(event.start);
    const weekStart = new Date(currentDate);
    weekStart.setDate(currentDate.getDate() - currentDate.getDay());
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    
    return eventDate >= weekStart && eventDate <= weekEnd;
  });

  return (
    <div className="week-view-container h-full flex flex-col">
      {/* Toggle Controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-800">Vue Hebdomadaire</h3>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Vue:</span>
            <div className="flex bg-white rounded-lg border p-1">
              <button
                onClick={() => setUseEnhancedView(false)}
                className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                  !useEnhancedView
                    ? 'bg-blue-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Classique
              </button>
              <button
                onClick={() => setUseEnhancedView(true)}
                className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                  useEnhancedView
                    ? 'bg-blue-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Améliorée
              </button>
            </div>
          </div>
        </div>

        {/* View Info */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Salle A</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Salle B</span>
          </div>
          <div className="font-medium">
            {weekEvents.length} rendez-vous cette semaine
          </div>
        </div>
      </div>

      {/* View Content */}
      <div className="flex-1 relative">
        {useEnhancedView ? (
          <div className="h-full">
            <EnhancedWeekView
              currentDate={currentDate}
              events={events}
              onTimeSlotClick={onTimeSlotClick}
              onEventClick={onEventClick}
              onDateChange={onDateChange}
              onNavigate={onNavigate}
            />
          </div>
        ) : (
          <div className="h-full">
            <WeekView
              currentDate={currentDate}
              events={events}
              onTimeSlotClick={handleClassicTimeSlotClick}
              onDateChange={onDateChange}
              onNavigate={onNavigate}
              onEventEdit={onEventClick}
              onWaitingListUpdate={onWaitingListUpdate}
            />
          </div>
        )}

        {/* View Badge */}
        <div className="absolute top-4 right-4 z-10">
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            useEnhancedView
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-blue-100 text-blue-800 border border-blue-200'
          }`}>
            {useEnhancedView ? '🚀 Vue Améliorée' : '📅 Vue Classique'}
          </div>
        </div>
      </div>

      {/* Feature Comparison */}
      {useEnhancedView && (
        <div className="p-3 bg-green-50 border-t border-green-200">
          <div className="flex items-center gap-2 text-sm text-green-800">
            <span className="font-medium">✨ Fonctionnalités semaine améliorées:</span>
            <span>Statistiques hebdomadaires</span>
            <span>•</span>
            <span>Filtrage par salle</span>
            <span>•</span>
            <span>Navigation optimisée</span>
            <span>•</span>
            <span>Vue d&apos;ensemble</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default WeekViewToggle;
