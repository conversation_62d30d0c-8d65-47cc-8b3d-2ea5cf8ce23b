'use client';
import React, { useState, useEffect } from 'react';
import SimpleBar from "simplebar-react";
import Link from 'next/link';
import { useDisclosure } from '@mantine/hooks';
import { DictionaryModalsManager } from '@/components/alerte';
import { TextInput,
  Title, Divider,ScrollArea ,Tooltip,Card,Select,Button, Modal,  Group,  Text, ActionIcon, Menu, Box, Switch,Stack,Textarea,Radio,MultiSelect,Table,Input, Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { Checkbox, RenderTreeNodePayload } from '@mantine/core';
import { Patient } from '@/types/typesCalendarPatient';
import { DatePickerInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiCardAccountDetails,
  mdiApps,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCertificate,
  mdiFormatListBulleted,
  mdiTooth,
  mdiAccountAlert,
  mdiCurrencyUsd,
  mdiCashMultiple,
  mdiPlus,
  mdiDelete,
  mdiCircle,mdiBarcode,mdiSkipPrevious,mdiSkipNext,mdiCalendarPlus,mdiAccountSearch,
} from '@mdi/js';
import RelationForm from "./RelationForm"
import { AutocompleteChipInput } from './AutocompleteChipInput';
import { notifications } from '@mantine/notifications';
import patientService, { PatientAlert } from '@/services/patientService';
import { patientFormService, MedicalDataCategory, MedicalDataItem, PatientMedicalData } from '@/services/patientFormService';

// Django-integrated patient data will be loaded dynamically
// Removed fake patient data
type PatientActionsProps = {
  patientId?: string;
    isFormInvalid: boolean;
    isDraft: boolean;
    onPrint?: () => void;
    onPrevious?: () => void;
    onNext?: () => void;
    onStartVisit?: () => void;
    onAppointment?: () => void;
    onCancel?: () => void;
    onSaveQuitNew?: () => void;
    onSaveQuit?: () => void;
   
    onGoBack: () => void;
    onAddMeasurement: () => void;
    onGoToContract: () => void;
    selectedInsurance: string;
    setSelectedInsurance: (value: string) => void;
    affiliateNumber: string;
    setAffiliateNumber: (value: string) => void;
    affiliateType: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT';
    setAffiliateType: (value: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT') => void;
    organizationOptions: { value: string; label: string }[];
     value: string | null;
    onValueChange: (val: string | null) => void;
    onAdd?: () => void;
    locked?: boolean;
     countryId: number | null;
    provinceId: number | null;
    values: { id: string; name: string } | null;
    onChange: (city: { id: string; name: string } | null) => void;
    disabled?: boolean;
      onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName?: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];
  openListDesPatient: () => void;
};
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface FichePatientProps {
  onInsuredChange?: (isInsured: boolean) => void;
  // Django integration props
  patientData?: Patient | null;
  enableDjangoSync?: boolean;
  onPatientDataChange?: (data: Patient) => void;
  // Additional props for staff and trigger options
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  onValueChange?: () => void;
}
//end alert
// Types pour la reconnaissance vocale
interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionEvent {
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent {
  error: string;
}

// SpeechRecognition interface is defined in VoiceRecognitionDialog.tsx

// Speech recognition types are declared in VoiceRecognitionDialog.tsx
import { mdiViewGrid ,mdiArrowLeft,mdiArrowRight,mdiPlusBox,mdiHistory,mdiMicrophone,mdiClipboardText,mdiDeleteSweep,mdiViewHeadline,
mdiArrowRightBoldBox,mdiPlaylistCheck,mdiChevronDown,mdiChevronRight,mdiClose,mdiPencil
} from '@mdi/js';
import { TreeNodeData, Tree } from '@mantine/core';
import { IconChevronDown, IconSearch } from '@tabler/icons-react';

const renderTreeNode = ({
  node,
  expanded,
  hasChildren,
  elementProps,
  tree,
}: RenderTreeNodePayload) => {
  const checked = tree.isNodeChecked(node.value);
  const indeterminate = tree.isNodeIndeterminate(node.value);

  return (
    <Group gap="xs" {...elementProps}>
      <Checkbox.Indicator
        checked={checked}
        indeterminate={indeterminate}
        onClick={() => (!checked ? tree.checkNode(node.value) : tree.uncheckNode(node.value))}
      />

      <Group gap={5} onClick={() => tree.toggleExpanded(node.value)}>
        <span>{node.label}</span>

        {hasChildren && (
          <IconChevronDown
            size={14}
            style={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
          />
        )}
      </Group>
    </Group>
  );
};
export const dataFacteurs: TreeNodeData[] = [
  {
    label: 'Facteurs de risque',
    value: 'src',
    children: [
          { label: 'Age', value: 'Age' },
          { label: 'Alcool', value: 'Alcool' },
          { label: 'Avoir une alimentation riche en sucres', value: 'Avoir une alimentation riche en sucres' },
          { label: 'Avoir une mauvaise hygiène bucco-dentaire', value: 'Avoir une mauvaise hygiène bucco-dentaire' },
          { label: 'Diabète', value: 'Diabète' },
          { label: "Etre en perte d'\autonomie", value: "Etre en perte d'\autonomie" },
          { label: 'La génétique', value: 'La génétique' },
          { label: 'La position des dents', value: 'La position des dents' },
          { label: 'Le grincement des dents (Bruxine)', value: 'Le grincement des dents (Bruxine)' },

          { label: 'Le syndrome de sjogrem', value: 'Le syndrome de sjogrem' },
          { label: 'Les traitements radiothérapie', value: 'Les traitements radiothérapie' },
           { label: 'L’HTA (hypertension artérielle)', value: 'L’HTA (hypertension artérielle)' },
            { label: 'Prise de certains médicaments:', value: 'Prise de certains médicaments:' },
             { label: 'Souffrir de sécheresse de la bouche', value: 'Souffrir de sécheresse de la bouche' },
              { label: 'Stress', value: 'Stress' },
               { label: 'Tabac', value: 'Tabac' },
                { label: 'Tabagisme', value: 'Tabagisme' },
               
    ],
  },
];
 export const dataAllergies: TreeNodeData[] = [
  {
    label: 'Allergies médicamenteuses',
    value: 'Allergies médicamenteuses',
    children: [
      {
        label: 'Antalgiques',
        value: 'Antalgiques',
        children: [
          { label: 'Anesthésiques locaux', value: 'Anesthésiques locaux' },
          { label: 'Aspirine', value: 'Aspirine' },
          
        ],
      },
    ],
  },
  {
    label: 'Antibiotiques',
    value: 'Antibiotiques',
    children: [
     
          { label: 'Céphalosporines', value: 'Céphalosporines' },
          { label: 'Cyclines', value: 'Cyclines' },
           { label: 'Pénicillines et dérivés', value: 'Pénicillines et dérivés' },
          { label: 'Sulfamides', value: 'Sulfamides' },
       
     
     
    ],
  },
 
];

export const dataAntecedents: TreeNodeData[] = [
  {
    label: 'Antécédents',
    value: 'Antécédents',
    children: [
      {
        label: 'Familiaux',
        value: 'Familiaux',
        children: [
          { label: 'Angine de poitrine', value: 'Angine de poitrine' },
          { label: 'Diabète', value: 'Diabète' },
          { label: 'Dyslipidémie', value: 'Dyslipidémie' },
          { label: 'HTA', value: 'HTA' },
          { label: 'Infarctus', value: 'Infarctus' },
          { label: 'Mort subite', value: 'Mort subite' },
        ],
      },
      {
        label: 'Personnels',
        value: 'Personnels',
        children: [
          { label: 'Affections digestives', value: 'Affections digestives' },
          { label: 'Angines fréquentes', value: 'Angines fréquentes' },
          { label: 'Asthme', value: 'Asthme' },
          { label: 'Bronchite chronique', value: 'Bronchite chronique' },
          { label: 'Maladie rénale', value: 'Maladie rénale' },
          { label: 'Rhumatisme articulaire aigu', value: 'Rhumatisme articulaire aigu' },
          { label: 'Tuberculose', value: 'Tuberculose' },
        ],
      },
    ],
  },
];

export const FicheMedicale = ({
    patientId,
  isFormInvalid,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  onInsuredChange,
  onGoBack,
  openListDesPatient,
  isDraft,
  // Django integration props
  patientData = null,
  enableDjangoSync = false,
  onPatientDataChange,
  staffOptions = [
    { label: 'TEST DEMO', value: 'test-demo' },
    { label: 'DEMO DEMO', value: 'demo-demo' }
  ],
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],

}: PatientActionsProps  & FichePatientProps) => {
 const disabled = isFormInvalid || isDraft;
    // Fonction pour obtenir la date d'aujourd'hui au format DD/MM/YYYY
    const getTodayDate = () => {
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const year = today.getFullYear();
      return `${day}/${month}/${year}`;
    };

    const [isFacteursVisible, setIsFacteursVisible] = useState(false); // State to control sidebar visibility
   const toggleFacteursSidebar = () => {
         const newState = !isFacteursVisible;
         setIsFacteursVisible(newState);
         // Si on ouvre Facteurs, fermer les autres
         if (newState) {
           setIsAllergiesVisible(false);
           setIsAntecedentsVisible(false);
         }
       };
  const [opened, { open, close }] = useDisclosure(false);
  const [isAllergiesVisible, setIsAllergiesVisible] = useState(false); // State to control sidebar visibility
  const toggleAllergiesSidebar = () => {
         const newState = !isAllergiesVisible;
         setIsAllergiesVisible(newState);
         // Si on ouvre Allergies, fermer les autres
         if (newState) {
           setIsFacteursVisible(false);
           setIsAntecedentsVisible(false);
         }
       };
  const [openedAllergies, {  close:Allergiesclose }] = useDisclosure(false);

const [isAntecedentsVisible, setIsAntecedentsVisible] = useState(false); // State to control sidebar visibility
  const toggleAntecedentsSidebar = () => {
         const newState = !isAntecedentsVisible;
         setIsAntecedentsVisible(newState);
         // Si on ouvre Antécédents, fermer les autres
         if (newState) {
           setIsFacteursVisible(false);
           setIsAllergiesVisible(false);
         }
       };
  const [openedAntecedents, { open:Antecedentsopen, close:Antecedentsclose }] = useDisclosure(false);
   const [isRelationsModalOpen, setIsRelationsModalOpen] = useState(false);
// const [isTraitementVisible, setIsTraitementVisible] = useState(false); // State to control sidebar visibility
  // const toggleTraitementSidebar = () => {
  //        setIsTraitementVisible(!isTraitementVisible);
  //      };
   // État pour l'expansion des facteurs de risque
    const [facteursExpanded, setFacteursExpanded] = useState(true);
     // État pour l'expansion des facteurs de risque
    const [AllergiesExpanded, setAllergiesExpanded] = useState(true);
      // État pour l'expansion des Antecedents de risque
    const [AntecedentsExpanded, setAntecedentsExpanded] = useState(true);
    // État pour gérer les facteurs sélectionnés (multiple)
    const [selectedFacteurs, setSelectedFacteurs] = useState<string[]>([]);
     // État pour gérer les facteurs sélectionnés (multiple)
    const [selectedAllergies, setSelectedAllergies] = useState<string[]>([]);
     // État pour gérer les Antecedents sélectionnés (multiple)
    const [selectedAntecedents, setSelectedAntecedents] = useState<string[]>([]);
    // États pour les modals
    const [historyModalOpened, setHistoryModalOpened] = useState(false);
    const [microphoneModalOpened, setMicrophoneModalOpened] = useState(false);
    const [modelsModalOpened, setModelsModalOpened] = useState(false);
    // État pour le contenu du textarea
    const [textareaContent, setTextareaContent] = useState('');
    const [textareaContentAllergies, setTextareaContentAllergies] = useState('');
     const [textareaContentAntecedents, setTextareaContentAntecedents] = useState('');
    // État pour la date sélectionnée
    const [selectedDate, setSelectedDate] = useState<string>(getTodayDate());
    // État pour l'historique des sélections
    const [historyEntries, setHistoryEntries] = useState<Array<{date: string, content: string}>>([
      { date: '26/06/2025', content: 'Age , La position des dents , Tabac' },
      { date: '27/06/2025', content: 'Alcool , Diabète , La génétique' }
    ]);

    // État pour gérer les données des facteurs (copie modifiable)
    const [currentDataFacteurs, setCurrentDataFacteurs] = useState<TreeNodeData[]>(dataFacteurs);

    // Django integration state
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [currentPatient, setCurrentPatient] = useState<Patient | null>(patientData);
    const [patientAlerts, setPatientAlerts] = useState<PatientAlert[]>([]);
    const [patientMedicalData, setPatientMedicalData] = useState<PatientMedicalData[]>([]);
    const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

    // Backend medical data state
    const [medicalDataCategories, setMedicalDataCategories] = useState<MedicalDataCategory[]>([]);
    const [medicalDataItems, setMedicalDataItems] = useState<MedicalDataItem[]>([]);
    const [submitting, setSubmitting] = useState(false);
    // État pour gérer les données des Allergies (copie modifiable)
    const [, setCurrentDataAllergies] = useState<TreeNodeData[]>(dataAllergies);
 const [, setCurrentDataAntecedents] = useState<TreeNodeData[]>(dataAntecedents);

    // États pour la gestion des modèles
    const [models, setModels] = useState<Array<{id: string, name: string, content: string}>>([]);
    
    const [newModelName, setNewModelName] = useState('');
    const [newModelContent, setNewModelContent] = useState('');

    // États pour l'interface de reconnaissance vocale améliorée
 

    // États pour les contenus des différentes sections
    //const [allergiesContent, setAllergiesContent] = useState('');
    // const [antecedentsContent, setAntecedentsContent] = useState('');
    // const [traitementContent, setTraitementContent] = useState('');
    // const [pathologiesContent, setPathologiesContent] = useState('');

    // États pour le composant Chips
    const [chips, setChips] = useState<Array<{id?: string, label: string}>>([
      { id: '1', label: 'Artesunate – amodiaquine winthrop 50 mg / 135 mg' }
    ]);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [searchText, setSearchText] = useState('');
    //const [readOnly, ] = useState(false);
    // const [autocompleteData] = useState([
    //   'Paracétamol 500mg',
    //   'Ibuprofène 400mg',
    //   'Amoxicilline 1g',
    //   'Doliprane 1000mg',
    //   'Artesunate – amodiaquine winthrop 50 mg / 135 mg',
    //   'Aspirine 100mg',
    //   'Oméprazole 20mg'
    // ]);

    // États pour les pathologies - Django integration
    const [pathologyItems, setPathologyItems] = useState<string[]>([]);
    const [allergyItems, setAllergyItems] = useState<string[]>([]);
    const [medicationItems, setMedicationItems] = useState<string[]>([]);
   // const [conditionItems, setConditionItems] = useState<string[]>([]);

    // Legacy pathology state (to be removed)
    const [pathologyChips, setPathologyChips] = useState<Array<{id?: string, label: string}>>([]);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [pathologySearchText, setPathologySearchText] = useState('');
    // const [pathologyAutocompleteData] = useState([
    //   'Hypertension artérielle',
    //   'Diabète type 2',
    //   'Asthme bronchique',
    //   'Insuffisance cardiaque',
    //   'Arthrose',
    //   'Ostéoporose',
    //   'Migraine',
    //   'Dépression',
    //   'Anxiété généralisée',
    //   'Reflux gastro-œsophagien'
    // ]);

    // Fonction pour gérer la sélection/déselection des facteurs
    const handleFacteurSelection = (facteur: string) => {
      setSelectedFacteurs(prev => {
        const isSelected = prev.includes(facteur);
        let newSelection;

        if (isSelected) {
          // Retirer le facteur s'il est déjà sélectionné
          newSelection = prev.filter(f => f !== facteur);
        } else {
          // Ajouter le facteur s'il n'est pas sélectionné
          newSelection = [...prev, facteur];
        }

        // Mettre à jour le contenu du textarea
        setTextareaContent(newSelection.join(' , '));
        return newSelection;
      });
    };
// Fonction pour gérer la sélection/déselection des Allergies
    const handleAllergiesSelection = (node: string) => {
   
      setSelectedAllergies(prev => {
        const isSelected = prev.includes(node);
        let newSelection;

        if (isSelected) {
          // Retirer le facteur s'il est déjà sélectionné
          newSelection = prev.filter(f => f !== node);
        } else {
          // Ajouter le facteur s'il n'est pas sélectionné
          newSelection = [...prev, node];
        }

        // Mettre à jour le contenu du textarea
        setTextareaContentAllergies(newSelection.join(' , '));
        return newSelection;
      });
    };
    // Fonction pour gérer la sélection/déselection des Antecedents
    const handleAntecedentsSelection = (node: string) => {
   
      setSelectedAntecedents(prev => {
        const isSelected = prev.includes(node);
        let newSelection;

        if (isSelected) {
          // Retirer le facteur s'il est déjà sélectionné
          newSelection = prev.filter(f => f !== node);
        } else {
          // Ajouter le facteur s'il n'est pas sélectionné
          newSelection = [...prev, node];
        }

        // Mettre à jour le contenu du textarea
        setTextareaContentAntecedents(newSelection.join(' , '));
        return newSelection;
      });
    };
    // Fonction pour sauvegarder les sélections avec la date
    const saveSelectionToHistory = async (contentToSave?: string) => {
      const content = contentToSave || textareaContent;
      if (content.trim()) {
        const newEntry = {
          date: selectedDate,
          content: content
        };
        setHistoryEntries(prev => [newEntry, ...prev]);

        // Save to Django if enabled
        if (enableDjangoSync) {
          await savePatientMedicalData('risk_factors', content);
        }
      }
    };
  // Fonction pour sauvegarder les sélections avec la date
    const saveSelectionToAllergies = async (contentToSave?: string) => {
      const content = contentToSave || textareaContentAllergies;
      if (content.trim()) {
        const newEntry = {
          date: selectedDate,
          content: content
        };
        setHistoryEntries(prev => [newEntry, ...prev]);

        // Save to Django if enabled
        if (enableDjangoSync) {
          await savePatientMedicalData('allergies', content);
        }
      }
    };
 // Fonction pour sauvegarder les sélections avec la date
    const saveSelectionToAntecedents = async (contentToSave?: string) => {
      const content = contentToSave || textareaContentAntecedents;
      if (content.trim()) {
        const newEntry = {
          date: selectedDate,
          content: content
        };
        setHistoryEntries(prev => [newEntry, ...prev]);

        // Save to Django if enabled
        if (enableDjangoSync) {
          await savePatientMedicalData('medical_conditions', content);
        }
      }
    };
    // Fonction pour ajouter un nouveau facteur à la liste
    const addNewFacteur = async () => {
      if (textareaContent.trim()) {
        const newFacteur = {
          label: textareaContent.trim(),
          value: textareaContent.trim()
        };

        setCurrentDataFacteurs(prev => {
          const newData = [...prev];
          if (newData[0] && newData[0].children) {
            newData[0].children = [...newData[0].children, newFacteur];
          }
          return newData;
        });

        // Sauvegarder dans l'historique et backend
        await saveSelectionToHistory();

        // Ne pas vider le textarea - garder le contenu
        // setTextareaContent('');
      }
    };
 // Fonction pour ajouter un nouveau Allergies à la liste
    const addNewAllergies = async () => {
      if (textareaContentAllergies.trim()) {
        const newAllergies = {
          label: textareaContentAllergies.trim(),
          value: textareaContentAllergies.trim()
        };

        setCurrentDataAllergies(prev => {
          const newData = [...prev];
          if (newData[0] && newData[0].children) {
            newData[0].children = [...newData[0].children, newAllergies];
          }
          return newData;
        });

        // Sauvegarder dans l'historique et backend
        await saveSelectionToAllergies();

        // Ne pas vider le textarea - garder le contenu
        // setTextareaContent('');
      }
    };
    
// Fonction pour ajouter un nouveau Antecedents à la liste
    const addNewAntecedents = async () => {
      if (textareaContentAntecedents.trim()) {
        const newAntecedents = {
          label: textareaContentAntecedents.trim(),
          value: textareaContentAntecedents.trim()
        };

        setCurrentDataAntecedents(prev => {
          const newData = [...prev];
          if (newData[0] && newData[0].children) {
            newData[0].children = [...newData[0].children, newAntecedents];
          }
          return newData;
        });

        // Sauvegarder dans l'historique et backend
        await saveSelectionToAntecedents();

        // Ne pas vider le textarea - garder le contenu
        // setTextareaContent('');
      }
    };
   

    // Fonction pour démarrer/arrêter la reconnaissance vocale
    const toggleSpeechRecognition = () => {
      console.log('toggleSpeechRecognition appelée, recognition:', !!recognition, 'isListening:', isListening);

      if (!recognition) {
        console.log('Initialisation de la reconnaissance vocale...');
        initSpeechRecognition();
        return;
      }

      try {
        if (isListening) {
          console.log('Arrêt de la reconnaissance vocale');
          recognition.stop();
        } else {
          console.log('Démarrage de la reconnaissance vocale');
          recognition.start();
        }
      } catch (error) {
        console.error('Erreur lors du toggle:', error);
      }
    };

    // Django integration functions
    useEffect(() => {
      if (enableDjangoSync && patientId) {
        loadPatientData();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [enableDjangoSync, patientId]);

    const loadPatientData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status !== 'active') {
          console.warn('Django backend is not connected, using fallback mode');
          setDjangoStatus('disconnected');
          return;
        }

        if (patientId) {
          try {
            // Load patient details with error handling
            const patient = await patientService.getPatientDetail(patientId);
            if (patient) {
              // Convert Django Patient to Calendar Patient format
              const calendarPatient: Patient = {
                id: patient.id,
                title: "Mr", // Default value
                name: patient.last_name || '',
                prenom: patient.first_name || '',
                first_name: patient.first_name || '',
                last_name: patient.last_name || '',
                birth_date: patient.date_of_birth || '',
                appointmentDate: new Date().toISOString(),
                appointmentTime: "11:00",
                appointmentEndTime: "12:00",
                consultationDuration: 60,
                email: patient.email || '',
                age: patient.age ? parseInt(patient.age) : 0,
                phone_numbers: patient.phone_number || '',
                socialSecurity: patient.national_id_number || '',
                duration: "60",
                agenda: patient.agenda || "General",
                comment: patient.comments || "",
                address: patient.address || "",
                etatCivil: patient.marital_status || "Single",
                etatAganda: patient.status || "Active",
                patientTitle: "Mr",
                notes: patient.additional_notes || "",
                date: new Date().toISOString(),
                docteur: patient.doctor_assigned || "Dr. Smith",
                event_Title: "Consultation",
                gender: patient.gender || "M",
                sociale: patient.national_id_number || "",
                typeConsultation: patient.visit_type || "Consultation",
                commentairelistedattente: "",
                resourceId: 1,
                type: "visit" as const,
                eventType: "visit" as const,
                start: new Date(),
                end: new Date()
              };

              setCurrentPatient(calendarPatient);
              if (onPatientDataChange) {
                onPatientDataChange(calendarPatient);
              }
            }
          } catch (patientError) {
            console.warn('Patient details not available:', patientError);
            // Create a fallback patient object
            const fallbackPatient: Patient = {
              id: patientId,
              title: "Mr",
              name: "Patient",
              prenom: "Unknown",
              first_name: "Unknown",
              last_name: "Patient",
              birth_date: '',
              appointmentDate: new Date().toISOString(),
              appointmentTime: "10:00",
              appointmentEndTime: "11:00",
              consultationDuration: 60,
              email: '',
              age: 0,
              phone_numbers: '',
              socialSecurity: '',
              duration: "60",
              agenda: "General",
              comment: "",
              address: "",
              etatCivil: "Single",
              etatAganda: "Active",
              patientTitle: "Mr",
              notes: "",
              date: new Date().toISOString(),
              docteur: "Dr. Smith",
              event_Title: "Consultation",
              gender: "M",
              sociale: "",
              typeConsultation: "Consultation",
              commentairelistedattente: "",
              resourceId: 1,
              type: "visit" as const,
              eventType: "visit" as const,
              start: new Date(),
              end: new Date()
            };
            setCurrentPatient(fallbackPatient);
          }

          // Load patient alerts with error handling
          try {
            const alerts = await patientService.getPatientAlerts(patientId);
            if (alerts) {
              setPatientAlerts(alerts);
            }
          } catch (alertsError) {
            console.warn('Patient alerts not available:', alertsError);
            setPatientAlerts([]);
          }

          // Load patient medical data with error handling using patientFormService
          try {
            console.log(`🔄 Fetching medical data for patient: ${patientId}`);

            // Load medical data categories
            const categories = await patientFormService.getMedicalDataCategories();
            setMedicalDataCategories(categories);

            // Load medical data items
            const items = await patientFormService.getMedicalDataItems();
            setMedicalDataItems(items);

            // Load patient's medical data
            const patientMedData = await patientFormService.getPatientMedicalData(patientId);
            setPatientMedicalData(patientMedData);

            // Update UI based on loaded data
            if (patientMedData && patientMedData.length > 0) {
              // Extract risk factors
              const riskFactors = patientMedData
                .filter(data => data.medical_item.category.category_type === 'risk_factor')
                .map(data => data.medical_item.name);
              if (riskFactors.length > 0) {
                setSelectedFacteurs(riskFactors);
                setTextareaContent(riskFactors.join(' , '));
              }

              // Extract allergies
              const allergies = patientMedData
                .filter(data => data.medical_item.category.category_type === 'allergy')
                .map(data => data.medical_item.name);
              if (allergies.length > 0) {
                setSelectedAllergies(allergies);
                setTextareaContentAllergies(allergies.join(' , '));
              }

              // Extract medical conditions/antecedents
              const conditions = patientMedData
                .filter(data => ['condition', 'family_history', 'personal_history'].includes(data.medical_item.category.category_type))
                .map(data => data.medical_item.name);
              if (conditions.length > 0) {
                setSelectedAntecedents(conditions);
                setTextareaContentAntecedents(conditions.join(' , '));
              }
            }
          } catch (medicalError) {
            console.warn('Patient medical data not available:', medicalError);
            setPatientMedicalData([]);
            setMedicalDataCategories([]);
            setMedicalDataItems([]);
          }
        }
      } catch (error) {
        console.error('Error loading patient data:', error);
        setError('Django backend connection failed. Using offline mode.');
        setDjangoStatus('disconnected');
      } finally {
        setLoading(false);
      }
    };

    const savePatientMedicalData = async (dataType: string, content: string) => {
      if (!patientId || !content.trim()) return;

      try {
        setSubmitting(true);

        // Check if Django is connected
        if (djangoStatus !== 'connected') {
          notifications.show({
            title: 'Warning',
            message: `Django backend not connected. ${dataType} data saved locally only.`,
            color: 'orange',
          });
          return;
        }

        // Map dataType to category_type
        const categoryTypeMap: Record<string, string> = {
          'risk_factors': 'risk_factor',
          'allergies': 'allergy',
          'medical_conditions': 'condition',
          'family_history': 'family_history',
          'personal_history': 'personal_history'
        };

        const categoryType = categoryTypeMap[dataType] || 'condition';

        // Split content by comma and save each item separately
        const items = content.split(',').map(item => item.trim()).filter(item => item.length > 0);

        for (const item of items) {
          try {
            const medicalData = {
              medical_item_name: item,
              category_type: categoryType,
              notes: `Added via medical form - ${dataType}`,
              severity: 'mild' as const,
              start_date: new Date().toISOString().split('T')[0],
              is_active: true
            };

            console.log('🔄 Saving medical data:', medicalData);
            await patientFormService.addPatientMedicalData(patientId, medicalData);
          } catch (itemError) {
            console.error(`Error saving item "${item}":`, itemError);
          }
        }

        notifications.show({
          title: 'Success',
          message: `${dataType} data saved to patient record`,
          color: 'green',
        });

        // Reload medical data
        await loadPatientData();
      } catch (error) {
        console.error('Error saving medical data:', error);
        notifications.show({
          title: 'Error',
          message: `Failed to save ${dataType} data`,
          color: 'red',
        });
      } finally {
        setSubmitting(false);
      }
    };

    // Function to delete medical data
    const deletePatientMedicalData = async (medicalDataId: string, itemName: string) => {
      if (!patientId) return;

      try {
        setSubmitting(true);

        await patientFormService.removePatientMedicalData(patientId, medicalDataId);

        notifications.show({
          title: 'Success',
          message: `"${itemName}" removed from patient record`,
          color: 'green',
        });

        // Reload medical data
        await loadPatientData();
      } catch (error) {
        console.error('Error deleting medical data:', error);
        notifications.show({
          title: 'Error',
          message: `Failed to remove "${itemName}"`,
          color: 'red',
        });
      } finally {
        setSubmitting(false);
      }
    };

    // Function to refresh medical data
    const refreshMedicalData = async () => {
      if (!patientId) return;

      try {
        setLoading(true);
        await loadPatientData();
        notifications.show({
          title: 'Success',
          message: 'Medical data refreshed',
          color: 'green',
        });
      } catch (error) {
        console.error('Error refreshing medical data:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to refresh medical data',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour ajouter un nouveau modèle
    const addNewModel = () => {
      if (newModelName.trim() && newModelContent.trim()) {
        const newModel = {
          id: Date.now().toString(),
          name: newModelName.trim(),
          content: newModelContent.trim()
        };

        setModels(prev => [...prev, newModel]);
        setNewModelName('');
        setNewModelContent('');
        setShowAddModel(false);
      }
    };

    // Fonction pour appliquer un modèle
    const applyModel = (modelContent: string) => {
      setTextareaContent(modelContent);
      setModelsModalOpened(false);
    };

    // Fonction pour vider le contenu de reconnaissance vocale
    const emptyVoiceContent = () => {
      setValidSpeech('');
      setInvalidSpeech('Parlez maintenant.');
      setTextareaContent('');
    };

    // Fonction pour valider et appliquer le texte reconnu
    const submitVoiceRecognition = () => {
      if (validSpeech.trim()) {
        setTextareaContent(prev => prev + ' ' + validSpeech);
        saveSelectionToHistory(validSpeech);
      }
      setMicrophoneModalOpened(false);
      emptyVoiceContent();
    };

    // Fonction pour annuler la reconnaissance vocale
    const cancelVoiceRecognition = () => {
      setMicrophoneModalOpened(false);
      emptyVoiceContent();
    };

    // Fonctions pour gérer les chips
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const addChip = (item: string) => {
      if (item.trim() && !chips.find(chip => chip.label === item)) {
        setChips(prev => [...prev, { label: item }]);
        setSearchText('');
      }
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const removeChip = (index: number) => {
      setChips(prev => prev.filter((_, i) => i !== index));
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const editChip = (chip: {id?: string, label: string}, event: React.MouseEvent) => {
      event.stopPropagation();
      // Logique d'édition du chip
      console.log('Éditer chip:', chip);
    };

    // Fonctions pour gérer les pathologies
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const addPathologyChip = (item: string) => {
      if (item.trim() && !pathologyChips.find(chip => chip.label === item)) {
        setPathologyChips(prev => [...prev, { label: item }]);
        setPathologySearchText('');
      }
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const removePathologyChip = (index: number) => {
      setPathologyChips(prev => prev.filter((_, i) => i !== index));
    };

    // Effet pour initialiser la reconnaissance vocale
    useEffect(() => {
      initSpeechRecognition();
    }, []);
     // Start Alert
              const [, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
              const [isSidebarAlert, setIsSidebarAlert] = useState(false);
               const toggleSidebarAlert = () => {
                       setIsSidebarAlert(!isSidebarAlert);
                     };
              
              const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
                 const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
                 const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
                  const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
                  const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);
                  // État pour gérer l'effondrement de chaque nœud
                  // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
                  const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
                    console.log('TreeItemChoixMultiple initialized with all nodes open');
                    return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
                  });
                
                  // État pour gérer les sélections multiples
                  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
                
                  // États pour la gestion des modèles
                  const [showModels, setShowModels] = useState(false);
                  const [showAddModel, setShowAddModel] = useState(false);
                  const [modelTitle, setModelTitle] = useState('');
                  const [editingModelId, setEditingModelId] = useState<string | null>(null);
                  const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
                
                  // États pour la reconnaissance vocale
                  const [isListening, setIsListening] = useState(false);
                  const [validSpeech, setValidSpeech] = useState('');
                  const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
                  const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
                  const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut
                
                  // Initialiser la reconnaissance vocale au montage du composant
                  useEffect(() => {
                    initSpeechRecognition();
                  }, []);
                
                  // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
                  const toggleNodeCollapse = (nodeId: string) => {
                    setCollapsedNodes(prev => {
                      const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
                      // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
                      if (currentState === true) { // Si fermé, ouvrir
                        console.log('Opening TreeItemChoixMultiple node:', nodeId);
                        return {
                          ...prev,
                          [nodeId]: false // false = ouvert
                        };
                      } else {
                        console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
                        return prev; // Ne rien changer si déjà ouvert
                      }
                    });
                  };
                  interface TreeNodeChoixMultiple {
                    uid: string;
                    value: string;
                    nodes?: TreeNodeChoixMultiple[];
                  }
                  function TreeItemChoixMultiple({
                    node,
                    collapsedNodes,
                    toggleNodeCollapse,
                    selectedNodes,
                    toggleNodeSelection,
                  }: {
                    node: TreeNodeChoixMultiple;
                    collapsedNodes: Record<string, boolean>;
                    toggleNodeCollapse: (nodeId: string) => void;
                    selectedNodes: Set<string>;
                    toggleNodeSelection: (nodeId: string) => void;
                  }) {
                    // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
                    const isCollapsed = collapsedNodes[node.uid] ?? false;
                    const isSelected = selectedNodes.has(node.uid);
                    // Calculer l'état indéterminé pour les nœuds parents
                    const getIndeterminateState = () => {
                      if (!node.nodes || node.nodes.length === 0) return false;
                      const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
                      return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
                    };
                    const isIndeterminate = getIndeterminateState();
                    return (
                      <Stack pl="md" gap="xs">
                        <Group gap="xs" align="center">
                          {node.nodes && node.nodes.length > 0 && (
                            <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                              <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
                            </span>
                          )}
                          <Checkbox
                            label={node.value}
                            checked={isSelected}
                            indeterminate={isIndeterminate}
                            onChange={() => toggleNodeSelection(node.uid)}
                            radius="xs"
                          />
                        </Group>
                  
                        {!isCollapsed &&
                          node.nodes?.map((child) => (
                            <TreeItemChoixMultiple
                              key={child.uid}
                              node={child}
                              collapsedNodes={collapsedNodes}
                              toggleNodeCollapse={toggleNodeCollapse}
                              selectedNodes={selectedNodes}
                              toggleNodeSelection={toggleNodeSelection}
                            />
                          ))}
                      </Stack>
                    );
                  }
                  
                  interface AlertFormValues {
                    trigger_for: string[];
                    trigger: string;
                    level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
                    description: string;
                    is_permanent: boolean;
                  }

                  const exampleData: TreeNodeChoixMultiple[] = [
                    {
                      uid: '1',
                      value: 'Alertes',
                      nodes: [
                        { uid: '1-1', value: 'Allaitante depuis:' },
                        { uid: '1-2', value: 'Allergique à l\'Aspirine' },
                        { uid: '1-3', value: 'Allergique à la Pénicilline' },
                        { uid: '1-4', value: 'Arthrose' },
                        { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
                        { uid: '1-6', value: 'Diabétique NID' },
                        { uid: '1-7', value: 'Enceinte depuis:' },
                        { uid: '1-8', value: 'Diabétique ID' },
                        { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
                        { uid: '1-10', value: 'Hypertension' },
                        { uid: '1-11', value: 'Hypotension' },
                        { uid: '1-12', value: 'Thyroïde' },
                      ],
                    },
                  ];
                    // Fonction pour basculer la sélection d'un nœud
                    const toggleNodeSelection = (nodeId: string) => {
                      setSelectedNodes(prev => {
                        const newSet = new Set(prev);
                  
                        // Trouver le nœud correspondant
                        const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
                          for (const node of nodes) {
                            if (node.uid === id) return node;
                            if (node.nodes) {
                              const found = findNode(node.nodes, id);
                              if (found) return found;
                            }
                          }
                          return null;
                        };
                  
                        const currentNode = findNode(exampleData, nodeId);
                  
                        if (newSet.has(nodeId)) {
                          // Désélectionner le nœud et tous ses enfants
                          newSet.delete(nodeId);
                          if (currentNode?.nodes) {
                            const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                              nodes.forEach(child => {
                                newSet.delete(child.uid);
                                if (child.nodes) {
                                  removeAllChildren(child.nodes);
                                }
                              });
                            };
                            removeAllChildren(currentNode.nodes);
                          }
                        } else {
                          // Sélectionner le nœud et tous ses enfants
                          newSet.add(nodeId);
                          if (currentNode?.nodes) {
                            const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                              nodes.forEach(child => {
                                newSet.add(child.uid);
                                if (child.nodes) {
                                  addAllChildren(child.nodes);
                                }
                              });
                            };
                            addAllChildren(currentNode.nodes);
                          }
                        }
                  
                        return newSet;
                      });
                    };
                  
                    // Fonction pour obtenir les sélections actuelles
                    const getSelectedValues = () => {
                      const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
                        const result: TreeNodeChoixMultiple[] = [];
                        nodes.forEach(node => {
                          result.push(node);
                          if (node.nodes) {
                            result.push(...getAllNodes(node.nodes));
                          }
                        });
                        return result;
                      };
                  
                      const allNodes = getAllNodes(exampleData);
                      return Array.from(selectedNodes)
                        .map(id => allNodes.find(node => node.uid === id))
                        .filter(Boolean)
                        .map(node => node!.value);
                    };
              
                    // Fonctions pour sélectionner/désélectionner tous les nœuds
                    const selectAllNodes = () => {
                      const allNodeIds: string[] = [];
              
                      const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
                        nodes.forEach(node => {
                          allNodeIds.push(node.uid);
                          if (node.nodes && node.nodes.length > 0) {
                            collectAllIds(node.nodes);
                          }
                        });
                      };
              
                      collectAllIds(exampleData);
                      setSelectedNodes(new Set(allNodeIds));
                    };
              
                    const deselectAllNodes = () => {
                      setSelectedNodes(new Set());
                    };
              
                    // Fonctions pour la reconnaissance vocale
                    const initSpeechRecognition = () => {
                      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                        const SpeechRecognitionConstructor = (window as unknown as {
                          webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                          SpeechRecognition: new () => SpeechRecognitionInstance;
                        }).webkitSpeechRecognition || (window as unknown as {
                          webkitSpeechRecognition: new () => SpeechRecognitionInstance;
                          SpeechRecognition: new () => SpeechRecognitionInstance;
                        }).SpeechRecognition;
                  
                        const newRecognition = new SpeechRecognitionConstructor();
                  
                        newRecognition.continuous = true;
                        newRecognition.interimResults = true;
                        newRecognition.lang = 'fr-FR';
                  
                        newRecognition.onstart = () => {
                          setIsListening(true);
                          setMicrophoneColor('green'); // Changer la couleur en vert
                          setInvalidSpeech('Écoute en cours...');
                        };
                  
                        newRecognition.onresult = (event: unknown) => {
                          const speechEvent = event as {
                            resultIndex: number;
                            results: {
                              length: number;
                              [index: number]: {
                                isFinal: boolean;
                                [index: number]: { transcript: string };
                              };
                            };
                          };
                  
                          let finalTranscript = '';
                          let interimTranscript = '';
                  
                          for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
                            const transcript = speechEvent.results[i][0].transcript;
                            if (speechEvent.results[i].isFinal) {
                              finalTranscript += transcript;
                            } else {
                              interimTranscript += transcript;
                            }
                          }
                  
                          setValidSpeech(finalTranscript);
                          setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
                        };
                  
                        newRecognition.onerror = () => {
                          setIsListening(false);
                          setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
                          setInvalidSpeech('Erreur de reconnaissance vocale');
                        };
                  
                        newRecognition.onend = () => {
                          setIsListening(false);
                          setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
                          setInvalidSpeech('Parlez maintenant.');
                        };
                  
                        setRecognition(newRecognition);
                      }
                    };
                  
                    const toggleRecognition = () => {
                      if (!recognition) {
                        initSpeechRecognition();
                        return;
                      }
                  
                      if (isListening) {
                        recognition.stop();
                      } else {
                        recognition.start();
                      }
                    };
                  
                    const emptyContent = () => {
                      setValidSpeech('');
                      setInvalidSpeech('Parlez maintenant.');
                      setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
                      if (recognition && isListening) {
                        recognition.stop();
                      }
                    };
                  
                   
                  
                      // État pour savoir quelle alerte est en cours d'édition
                      const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);
              
                      // États pour le modal de confirmation de suppression
                      const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
                      const [alertToDelete, setAlertToDelete] = useState<string | null>(null);
              
                      // Fonctions pour gérer les alertes
                    
              
                      const handleDeleteAlert = (alertId: string) => {
                        console.log('Delete alert:', alertId);
                        // Ouvrir le modal de confirmation
                        setAlertToDelete(alertId);
                        setIsDeleteConfirmModalOpen(true);
                      };
              
                      // Fonction pour confirmer la suppression
                      const confirmDeleteAlert = () => {
                        if (alertToDelete) {
                          // Supprimer l'alerte de la liste
                          setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
                          console.log('Alert deleted:', alertToDelete);
                        }
                        // Fermer le modal et réinitialiser
                        setIsDeleteConfirmModalOpen(false);
                        setAlertToDelete(null);
                      };
              
                      // Fonction pour annuler la suppression
                      const cancelDeleteAlert = () => {
                        setIsDeleteConfirmModalOpen(false);
                        setAlertToDelete(null);
                      };
              
                      // Fonction pour obtenir la couleur selon le niveau
                      const getLevelColor = (level: string) => {
                        switch(level) {
                          case 'MINIMUM': return 'green';
                          case 'MEDIUM': return 'orange';
                          case 'HIGH': return 'red';
                          default: return 'gray';
                        }
                      };
              
              
              
                      // Fonction pour créer les actions d'une alerte
                      const createAlertActions = (alertId: string) => (
                        <Group gap="xs">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={() => {
                              console.log('Edit alert clicked:', alertId);
                              setCurrentEditingAlertId(alertId);
              
                              // Trouver l'alerte à éditer et pré-remplir le formulaire
                              const alertToEdit = alertsData.find(alert => alert.id === alertId);
                              console.log('Alert to edit found:', alertToEdit);
                              if (alertToEdit) {
                                // Trouver la valeur correspondante pour le trigger
                                const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';
              
                                console.log('Editing alert:', alertToEdit);
                                console.log('Trigger value found:', triggerValue);
              
                                form.setValues({
                                  trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                                  trigger: triggerValue,
                                  level: alertToEdit.level,
                                  description: alertToEdit.Description,
                                  is_permanent: alertToEdit.is_permanent
                                });
              
                                console.log('Form values set for editing:', {
                                  trigger_for: alertToEdit.trigger_for || [],
                                  trigger: triggerValue,
                                  level: alertToEdit.level,
                                  description: alertToEdit.Description,
                                  is_permanent: alertToEdit.is_permanent
                                });
              
                                console.log('Form values set:', form.values);
                              }
              
                              setIsAlertsAddModalOpen(true);
                              setIsSidebarAlert(true); // Ouvrir aussi la sidebar pour les sélections
                            }}
                          >
                            <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
                          </ActionIcon>
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => {
                              console.log('Delete alert clicked:', alertId);
                              handleDeleteAlert(alertId);
                            }}
                          >
                            <Icon path={mdiDelete} size={0.8} color={'red'}/>
                          </ActionIcon>
                        </Group>
                      );
              
                      // Fonction pour gérer la soumission du formulaire d'alerte
                      const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
                        console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
                        console.log('Current editing alert ID:', currentEditingAlertId);
              
                        if (currentEditingAlertId) {
                          // Mode édition : mettre à jour l'alerte existante
                          console.log('Editing existing alert:', currentEditingAlertId);
              
                          const updatedAlertData = {
                            id: currentEditingAlertId,
                            level: values.level,
                            is_permanent: values.is_permanent,
                            Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                            Description: values.description,
                            trigger_for: values.trigger_for
                          };
              
                          setAlertsData(prevData => {
                            const updatedData = prevData.map(alert =>
                              alert.id === currentEditingAlertId ? updatedAlertData : alert
                            );
                            console.log('Updated alerts data (edit mode):', updatedData);
                            return updatedData;
                          });
                        } else {
                          // Mode ajout : créer une nouvelle alerte
                          console.log('Adding new alert');
              
                          const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                          const newAlertData = {
                            id: newAlertId,
                            level: values.level,
                            is_permanent: values.is_permanent,
                            Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
                            Description: values.description,
                            trigger_for: values.trigger_for
                          };
              
                          setAlertsData(prevData => {
                            const updatedData = [...prevData, newAlertData];
                            console.log('Updated alerts data (add mode):', updatedData);
                            return updatedData;
                          });
                        }
              
                        // Appeler la fonction onSubmit originale si elle existe
                        if (onSubmit) {
                          onSubmit(values, autoTrigger);
                        }
              
                        // Fermer le modal et réinitialiser le formulaire
                        setIsAlertsAddModalOpen(false);
                        setIsSidebarAlert(false);
                        setCurrentEditingAlertId(null);
                        form.reset();
                      };
              
                      // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
                      const [alertsData, setAlertsData] = useState<AlertData[]>([]);
              
                      // Créer les éléments avec les actions pour le rendu
                      const elements = alertsData.map(alert => ({
                        ...alert,
                        Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
                        Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
                        Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
                        Actions: createAlertActions(alert.id)
                      }));
                  const rows = elements.map((element) => (
                      <Table.Tr key={element.id}>
                        <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
                        <Table.Td>{element.Niveau}</Table.Td>
                        <Table.Td>{element.Publique}</Table.Td>
                        <Table.Td>{element.Permanente}</Table.Td>
                        <Table.Td>{element.Description}</Table.Td>
                        <Table.Td w={'100px'}>{element.Actions}</Table.Td>
                      </Table.Tr>
                    ));
                     const form = useForm<AlertFormValues>({
                      initialValues: {
                        trigger_for: [],
                        trigger: '',
                        level: 'MINIMUM',
                        description: '',
                        is_permanent: false,
                      },
                      validate: {
                        trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
                        trigger: (value) => (!value ? 'Champ requis' : null),
                        description: (value) => (!value ? 'Champ requis' : null),
                      },
                    });
                    const [search, setSearch] = useState('');
                  
                   
                  
                    const handleValidate = () => {
                      let textToAdd = '';
                  
                      if (showModels) {
                        // Valider les modèles sélectionnés
                        const selectedModelTexts = savedModels
                          .filter(model => model.selected === true)
                          .flatMap(model => model.selections);
                        textToAdd = selectedModelTexts.join(', ');
                        console.log('Selected models:', savedModels.filter(model => model.selected === true));
                        console.log('Text to add from models:', textToAdd);
                      } else {
                        // Valider les sélections du dictionnaire
                        const selectedValues = getSelectedValues();
                        textToAdd = selectedValues.join(', ');
                      }
                  
                      if (textToAdd) {
                        // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
                        setValidSpeech(textToAdd);
                        setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"
              
                        // 2. Ajouter le texte au champ description du formulaire
                        const currentDescription = form.values.description || '';
                        const newDescription = currentDescription
                          ? `${currentDescription}, ${textToAdd}`
                          : textToAdd;
                        console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
                        form.setFieldValue('description', newDescription);
              
                        // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
                        // Utiliser la nouvelle description mise à jour
                        const combinedText = newDescription;
                        console.log('Combined text for alert:', combinedText);
              
                        if (currentEditingAlertId) {
                          setAlertsData(prevData =>
                            prevData.map(alert => {
                              if (alert.id === currentEditingAlertId) {
                                return {
                                  ...alert,
                                  Description: combinedText
                                };
                              }
                              return alert;
                            })
                          );
                        } else {
                          // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
                          setAlertsData(prevData =>
                            prevData.map(alert => {
                              if (alert.id === '1') {
                                return {
                                  ...alert,
                                  Description: combinedText
                                };
                              }
                              return alert;
                            })
                          );
                        }
                      }
                  
              
              
                      // Fermer le modal et réinitialiser
                      setIsClipboardTextModalOpen(false);
                      setIsChoixMultipleModalOpen(false);
                      setShowModels(false);
                      setSelectedNodes(new Set());
                      // Réinitialiser l'ID de l'alerte en cours d'édition
                      setCurrentEditingAlertId(null);
                    };
                  
                    const handleCancel = () => {
                      // Réinitialiser tous les états
                      setSelectedNodes(new Set());
                      setShowModels(false);
                      setShowAddModel(false);
                      setModelTitle('');
                      setEditingModelId(null);
                      setIsClipboardTextModalOpen(false);
                    };
                  
                    // const handleAddModel = () => {
                    //   if (selectedNodes.size > 0) {
                    //     setShowAddModel(true);
                    //   }
                    // };
                  
                    const handleSaveModel = () => {
                      if (modelTitle.trim()) {
                        if (editingModelId) {
                          // Mode édition : mettre à jour le modèle existant
                          setSavedModels(prev => prev.map(model =>
                            model.id === editingModelId
                              ? { ...model, title: modelTitle.trim() }
                              : model
                          ));
                          setEditingModelId(null);
                          console.log('Model title updated for ID:', editingModelId);
                        } else {
                          // Mode création : créer un nouveau modèle
                          const selectedValues = getSelectedValues();
                          const newModel = {
                            id: `model-${Date.now()}`,
                            title: modelTitle.trim(),
                            selections: selectedValues
                          };
                          setSavedModels(prev => [...prev, newModel]);
                          setSelectedNodes(new Set());
                          console.log('New model created:', newModel);
                        }
              
                        setModelTitle('');
                        setShowAddModel(false);
                        // Afficher les modèles après sauvegarde
                        setShowModels(true);
                      }
                    };
              
                    const handleEditModel = (modelId: string) => {
                      const modelToEdit = savedModels.find(model => model.id === modelId);
                      if (modelToEdit) {
                        setModelTitle(modelToEdit.title);
                        setEditingModelId(modelId);
                        setShowModels(false);
                        setShowAddModel(true);
                        console.log('Editing model:', modelToEdit);
                      }
                    };
                  
                    const handleDeleteModel = (modelId: string) => {
                      setSavedModels(prev => prev.filter(model => model.id !== modelId));
                      setSelectedNodes(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(modelId);
                        return newSet;
                      });
                    };
                  

                  //end header
              // Interface et données pour l'arbre de la sidebar
              interface TreeNode {
                value: string;
                children?: TreeNode[];
              }
              
              // Convert Django patient alerts to tree structure
              const createAlertsTreeFromDjango = (): TreeNode[] => {
                if (!enableDjangoSync || patientAlerts.length === 0) {
                  return [
                    {
                      value: 'Alertes',
                      children: [
                        { value: "No alerts found for this patient" }
                      ],
                    },
                  ];
                }

                return [
                  {
                    value: 'Alertes',
                    children: patientAlerts.map(alert => ({
                      value: `${alert.trigger || alert.trigger_custom || 'Unknown Alert'} - ${alert.level || 'UNKNOWN'}`
                    })),
                  },
                ];
              };

              const alertsTree = createAlertsTreeFromDjango();
              
              // Composant CustomTree pour la sidebar
              function CustomTree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
                // Initialiser tous les nœuds comme ouverts
                const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
                  const initialExpanded: Record<string, boolean> = {};
                  const expandAllNodes = (nodeList: TreeNode[]) => {
                    nodeList.forEach(node => {
                      if (node.children && node.children.length > 0) {
                        initialExpanded[node.value] = true;
                        expandAllNodes(node.children);
                      }
                    });
                  };
                  expandAllNodes(nodes);
                  console.log('Tree initialized with expanded nodes:', initialExpanded);
                  return initialExpanded;
                });
              
                return (
                  <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
                    {nodes.map((node, idx) => {
                      const hasChildren = node.children && node.children.length > 0;
                      const isOpen = expanded[node.value] || false;
                      return (
                        <li key={node.value + idx}>
                          <Group gap="xs" align="center" onClick={() => {
                            // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
                            if (hasChildren && !isOpen) {
                              console.log('Opening node:', node.value);
                              setExpanded(prev => ({ ...prev, [node.value]: true }));
                            } else if (hasChildren && isOpen) {
                              console.log('Node already open, not closing:', node.value);
                            }
                          }} className="Alertesslidbar">
                            {hasChildren ? (
                              <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
                            ) : null}
                            <Text
                              onClick={() => !hasChildren && onSelect(node.value)}
                              style={{ cursor: 'pointer' ,paddingLeft:'10px'
                              }}
                            >
                              {node.value}
                            </Text>
                          </Group>
                          {hasChildren && isOpen && <CustomTree nodes={node.children!} onSelect={onSelect} />}
                        </li>
                      );
                    })}
                  </ul>
                );
              }
              
              // Fonctions pour gérer la sidebar
              const handleSidebarSelect = (value: string) => {
                console.log('Selected from sidebar:', value);
              
                // 1. Ajouter la sélection au formulaire en cours d'édition
                const currentDescription = form.values.description || '';
                const newFormDescription = currentDescription
                  ? `${currentDescription}, ${value}`
                  : value;
                form.setFieldValue('description', newFormDescription);
              
                // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
                if (currentEditingAlertId) {
                  setAlertsData(prevData =>
                    prevData.map(alert => {
                      if (alert.id === currentEditingAlertId) {
                        const currentAlertDescription = alert.Description || '';
                        const newAlertDescription = currentAlertDescription
                          ? `${currentAlertDescription}, ${value}`
                          : value;
                        return {
                          ...alert,
                          Description: newAlertDescription
                        };
                      }
                      return alert;
                    })
                  );
                }
              
                console.log('Added to form description:', newFormDescription);
              
                // Optionnel : fermer la sidebar après sélection
                // setIsSidebarVisible(false);
              };
              
              const handleCloseSidebar = () => {
                setIsSidebarAlert(false);
              };
                
                  const FicheForm = useForm({
                  initialValues: {
                        file_number:1,
                        category: '',
                        pricing: 0,
                        is_bookmarked: false,
                        insured: true, // Set to true to match defaultChecked
                       description:'',
                       titre: '',
                  },
                  validate: {
                    pricing: (value) => (value < 0 ? 'Last name must be at least 2 characters' : null),
                  },
                });
              
                // Effect to notify parent component when insured status changes
                useEffect(() => {
                  if (onInsuredChange) {
                    onInsuredChange(FicheForm.values.insured);
                  }
                }, [FicheForm.values.insured, onInsuredChange]);
              
                // Effect to set initial insured state when component mounts
                useEffect(() => {
                  if (onInsuredChange && FicheForm.values.insured !== undefined) {
                    onInsuredChange(FicheForm.values.insured);
                  }
                }, [onInsuredChange, FicheForm.values.insured]); // Include dependency
               
             // End Alert 
const renderAllergies = (nodes: TreeNodeData[]) => {
  return nodes.map((node, index) => (
    <div key={index} style={{ marginLeft: '10px', marginBottom: '4px' }}>
      <Text
        size="sm"
        style={{
          color: selectedAllergies.includes(String(node.label)) ? '#3799ce' : '#666666',
          cursor: 'pointer',
          padding: '1px 2px 1px 4px',
          backgroundColor: selectedAllergies.includes(String(node.label)) ? '#e3f2fd' : 'transparent',
          borderRadius: '4px',
        }}
        onClick={() => handleAllergiesSelection(String(node.label))}
      >
        {node.label}
      </Text>

      {node.children && node.children.length > 0 && (
        <div style={{ marginLeft: '10px' }}>{renderAllergies(node.children)}</div>
      )}
    </div>
  ));
};
const renderAntecedents = (nodes: TreeNodeData[]) => {
  return nodes.map((node, index) => (
    <div key={index} style={{ marginLeft: '10px', marginBottom: '4px' }}>
      <Text
        size="sm"
        style={{
          color: selectedAntecedents.includes(String(node.label)) ? '#3799ce' : '#666666',
          cursor: 'pointer',
          padding: '1px 2px 1px 4px',
          backgroundColor: selectedAntecedents.includes(String(node.label)) ? '#e3f2fd' : 'transparent',
          borderRadius: '4px',
        }}
        onClick={() => handleAntecedentsSelection(String(node.label))}
      >
        {node.label}
      </Text>

      {node.children && node.children.length > 0 && (
        <div style={{ marginLeft: '10px' }}>{renderAntecedents(node.children)}</div>
      )}
    </div>
  ));
};

  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
                <Group justify="space-between" align="center">
                  <Group>
                    {patientId ? (
                      <Icon path={mdiCardAccountDetails} size={1} />
                    ) : (
                      <Button variant="subtle" onClick={onGoBack}>
                        <Icon path={mdiArrowLeft} size={1} color={"white"}/>
                      </Button>
                    )}
                    <Title order={2}>Fiche patient</Title>
                    <DatePickerInput placeholder="Date de création" />
                    23/06/2025
                  </Group>
            
                  {currentPatient && (
                    <Group>
                      <Text>{currentPatient.first_name} {currentPatient.last_name}</Text>
                      <Text>{currentPatient.gender}</Text>
                      <Text>{currentPatient.age}</Text>
                      <Text>{currentPatient.sociale}</Text>
                      <Text>{currentPatient.id}</Text>
                      <Text>{currentPatient.lastVisit?.date ? new Date(currentPatient.lastVisit.date).toLocaleDateString() : ''}</Text>
                    </Group>
                  )}
            
                  <Group>
                     <Group>
                   
                      
                        
                     </Group>
                    <Divider size="sm" orientation="vertical" />
                    <Menu shadow="md" width={220}>
                      <Menu.Target>
                        <Button variant="subtle">
                          <Icon path={mdiApps} size={1} color={"white"}/>
                        </Button>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />} onClick={() => setIsAlertsModalOpen(true)}>Alerts</Menu.Item>
                        <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />} onClick={() => setIsRelationsModalOpen(true)}>Relations Patient</Menu.Item>
                        <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
                        <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
                        <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
                        <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
            
                    <Tooltip label="Contrat" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                      <Button variant="subtle" 
                      // onClick={onGoToContract}
                      >
                        <Icon path={mdiCertificate} size={1} color={"white"}/>
                      </Button>
                    </Tooltip>
            
                    <Tooltip label="Liste patients" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
                      <Button component="a" href="/pratisoft/patient" variant="subtle">
                        <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
                      </Button>
                    </Tooltip>
                  </Group>
                </Group>
                
                </div>
                <div className='flex'>
                  
                    <div className={isSidebarAlert ? "w-[80%] " : "w-full p-2"}>
                      <Card shadow="sm" padding="lg" radius="md" withBorder>
    <Group justify="space-between" mb={20}>
      <Group>
       <Icon path={mdiViewGrid} size={1} />
         <Text fw={700}>Fiche médicale</Text>
         {enableDjangoSync && (
           <Group gap="xs">
             <Text size="xs" c={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'gray'}>
               Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
             </Text>
             {currentPatient && (
               <Text size="xs" c="dimmed">
                 Patient: {currentPatient.first_name} {currentPatient.last_name}
               </Text>
             )}
           </Group>
         )}
      </Group>

      {/* Medical Data Summary */}
      {enableDjangoSync && patientMedicalData.length > 0 && (
        <Group gap="xs">
          <Button
            variant="light"
            size="xs"
            onClick={refreshMedicalData}
            loading={loading}
          >
            Refresh Data
          </Button>
          <Text size="xs" c="dimmed">
            {patientMedicalData.length} medical record(s) loaded
          </Text>
        </Group>
      )}
      <Group justify="flex-end">
      <Icon path={mdiArrowLeft} size={1} />
     <Select
      //placeholder="getTodayDate"
      data={[ getTodayDate()]}
      value={selectedDate}
      onChange={(value) => setSelectedDate(value || getTodayDate())}
    />
      <Icon path={mdiArrowRight} size={1} />
    </Group>
    </Group>

    {error && (
      <Alert color="red" mb="md">
        {error}
      </Alert>
    )}
    {/* Backend Medical Data Display */}
    {enableDjangoSync && patientMedicalData.length > 0 && (
      <Card withBorder shadow="sm" p="md" mb="md">
        <Group justify="space-between" mb="sm">
          <Text fw={600} size="lg">Current Medical Data</Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              onClick={refreshMedicalData}
              loading={loading}
            >
              Refresh
            </Button>
            <Text size="xs" c="dimmed">
              {patientMedicalData.length} record(s)
            </Text>
          </Group>
        </Group>

        <Stack gap="xs">
          {patientMedicalData.map((data) => (
            <Group key={data.id} justify="space-between" p="xs" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
              <Group gap="xs">
                <Text size="sm" fw={500}>
                  {data.medical_item.category.name}:
                </Text>
                <Text size="sm">
                  {data.medical_item.name}
                </Text>
                {data.severity && (
                  <Text size="xs" c={data.severity === 'severe' ? 'red' : data.severity === 'moderate' ? 'orange' : 'gray'}>
                    ({data.severity})
                  </Text>
                )}
              </Group>
              <Group gap="xs">
                <Text size="xs" c="dimmed">
                  {data.start_date ? new Date(data.start_date).toLocaleDateString() : ''}
                </Text>
                <ActionIcon
                  variant="subtle"
                  color="red"
                  size="sm"
                  onClick={() => deletePatientMedicalData(data.id, data.medical_item.name)}
                  loading={submitting}
                >
                  <Icon path={mdiDelete} size={0.7} />
                </ActionIcon>
              </Group>
            </Group>
          ))}
        </Stack>
      </Card>
    )}

    <Group>
   <div className={isFacteursVisible || isAllergiesVisible || isAntecedentsVisible  ? 'w-[79%] -mt-22 ':'w-[100%] '}>
    {/* --------------------------1-------------------------  */}
   <Group  w={"100%"}>
 
  <div className={ isFacteursVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Facteurs de risque</Text>
    </Group>
   <Group justify="flex-end" gap="sm">
        {textareaContent.trim() && (
          <ActionIcon
            variant="transparent"
            onClick={addNewFacteur}
            title="Ajouter à la liste"
            loading={submitting}
          >
            <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
          </ActionIcon>
        )}
        <ActionIcon variant="transparent" onClick={() => setHistoryModalOpened(true)}>
          <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => setMicrophoneModalOpened(true)}>
          <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => setModelsModalOpened(true)}>
          <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => {
          setTextareaContent('');
          setSelectedFacteurs([]);
        }}>
          <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
        </ActionIcon>
      </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
      value={textareaContent}
      onChange={(event) => setTextareaContent(event.currentTarget.value)}
      onClick={(event) => {
        event.preventDefault();
        toggleFacteursSidebar();
      }}
    />
</div>
     
       {/* Django-Integrated Allergies Component */}
       <div className={ isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
         <AutocompleteChipInput
           label="Allergies médicamenteuses"
           patientId={patientId || ''}
           categoryType="allergy"
           value={allergyItems}
           onChange={setAllergyItems}
           placeholder="Search for allergies..."
           autoSaveToDjango={true}
           onDataChange={(data) => {
             console.log('Allergy data updated:', data);
           }}
         />
     </div>
        
     
 </Group>
 {/* ---------------------------2----------------------- */}
  <Group  w={"100%"}>
  <div className={ isAntecedentsVisible ? 'w-[49%]':'w-[49%]'}>
       <Group  justify="space-between" gap="sm"  > 
         <Group gap="sm">
       <Text fw={500}> Antécédents</Text>
         </Group>
        <Group justify="flex-end" gap="sm">
             {textareaContentAntecedents.trim() && (
               <ActionIcon variant="transparent" onClick={addNewAntecedents} title="Ajouter à la liste">
                 <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
               </ActionIcon>
             )}
             <ActionIcon variant="transparent" onClick={() => setHistoryModalOpened(true)}>
               <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
             </ActionIcon>
             <ActionIcon variant="transparent" onClick={() => setMicrophoneModalOpened(true)}>
               <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
             </ActionIcon>
             <ActionIcon variant="transparent" onClick={() => setModelsModalOpened(true)}>
               <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
             </ActionIcon>
             <ActionIcon variant="transparent" onClick={() => {
               setTextareaContentAntecedents('');
               setSelectedAntecedents([]);
             }}>
               <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
             </ActionIcon>
           </Group>
        </Group>
         <Textarea
           size="xl"
           radius="md"
           placeholder="Ajouter"
           mt={10}
           mb={10}
           value={textareaContentAntecedents}
           onChange={(event) => setTextareaContentAntecedents(event.currentTarget.value)}
           onClick={(event) => {
             event.preventDefault();
             toggleAntecedentsSidebar();
           }}
         />
     </div>
      {/* Django-Integrated Medications Component */}
      <div style={{ marginTop: '16px', marginBottom: '16px' }} className='w-[49%]'>
        <AutocompleteChipInput
          label="Traitement en cours"
          patientId={patientId || ''}
          categoryType="medication"
          value={medicationItems}
          onChange={setMedicationItems}
          placeholder="Search for medications..."
          autoSaveToDjango={true}
          onDataChange={(data) => {
            console.log('Medication data updated:', data);
          }}
        />
      </div>

 </Group>
 {/* ---------------------------3--------------------------- */}
  <Group  w={"100%"}>
      {/* Django-Integrated Pathologies Component */}
      <div style={{ marginBottom: '16px' }} className='w-[49%]'>
        <AutocompleteChipInput
          label="Pathologies"
          patientId={patientId || ''}
          categoryType="pathology"
          value={pathologyItems}
          onChange={setPathologyItems}
          placeholder="Search for pathologies..."
          autoSaveToDjango={true}
          onDataChange={(data) => {
            console.log('Pathology data updated:', data);
          }}
        />
      </div>
 </Group>
   </div>
   {/* --------------------------1-------------------------  */}
    {isFacteursVisible &&(
    <div className='w-[20%]' >
      
        {/* <div style={{ backgroundColor: '#f9f9f9' }} className='p-4'> */}
          <Card shadow="sm" padding="lg" radius="md" withBorder h={"572px"}>
              <Group justify="space-between" mb={"md"}>
           <TextInput
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
        <Group gap={"8px"}>
           <ActionIcon variant="filled"   onClick={open}>
             <Icon path={mdiViewHeadline} size={1} />
          </ActionIcon>
             <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleFacteursSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}    />
       </ActionIcon>
      </Group>
      </Group>
          <div>
            <Group mb="xs" style={{ cursor: 'pointer' }} onClick={() => setFacteursExpanded(!facteursExpanded)}>
              <Icon
                path={facteursExpanded ? mdiChevronDown : mdiChevronRight}
                size={0.8}
              />
              <Text fw={500} size="sm">Facteurs de risque</Text>
            </Group>
            {facteursExpanded && (
              <div style={{ marginLeft: '20px' }}>
             {currentDataFacteurs[0]?.children?.map((facteur, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    <Text
                      size="sm"
                      style={{
                        color: selectedFacteurs.includes(String(facteur.label)) ? '#3799ce' : '#666666',
                        cursor: 'pointer',
                        padding: "1px 2px 1px 4px",
                        backgroundColor: selectedFacteurs.includes(String(facteur.label)) ? '#e3f2fd' : 'transparent',
                        borderRadius: '4px'
                      }}
                      onClick={() => handleFacteurSelection(String(facteur.label))}
                      className='dataFacteursSelect'
                    >
                      {facteur.label}
                    </Text>

                  </div>
                ))}
         </div>
                )}
          </div>
          </Card>
        {/* </div>  */}
 </div>
    )}
 {isAllergiesVisible &&(
    <div className='w-[20%]' >
      
        {/* <div style={{ backgroundColor: '#f9f9f9' }} className='p-4'> */}
          <Card shadow="sm" padding="lg" radius="md" withBorder h={"572px"}>
              <Group justify="space-between" mb={"md"}>
           <TextInput
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
        <Group gap={"8px"}>
           <ActionIcon variant="filled"   onClick={open}>
             <Icon path={mdiViewHeadline} size={1} />
          </ActionIcon>
             <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAllergiesSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}    />
       </ActionIcon>
      </Group>
      </Group>
          <div>
            <Group mb="xs" style={{ cursor: 'pointer' }} onClick={() => setAllergiesExpanded(!AllergiesExpanded)}>
              <Icon
                path={AllergiesExpanded ? mdiChevronDown : mdiChevronRight}
                size={0.8}
              />
              <Text fw={500} size="sm">Allergies médicamenteuses</Text>
            </Group>
            {AllergiesExpanded && (
              <div style={{ marginLeft: '20px' }}>
         {renderAllergies(dataAllergies)}
         </div>
                )}
          </div>
          </Card>
        {/* </div>  Antacedents*/}
 </div>
    )}
     {/* ---------------------------2----------------------- */}
      {isAntecedentsVisible &&(
        <div className='w-[20%]' >
          <Card shadow="sm" padding="lg" radius="md" withBorder h={"572px"}>
              <Group justify="space-between" mb={"md"}>
           <TextInput
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
        <Group gap={"8px"}>
           <ActionIcon variant="filled"   onClick={Antecedentsopen}>
             <Icon path={mdiViewHeadline} size={1} />
          </ActionIcon>
             <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAntecedentsSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}    />
       </ActionIcon>
      </Group>
      </Group>
          <div>
            <Group mb="xs" style={{ cursor: 'pointer' }} onClick={() => setAntecedentsExpanded(!AntecedentsExpanded)}>
              <Icon
                path={AntecedentsExpanded ? mdiChevronDown : mdiChevronRight}
                size={0.8}
              />
              <Text fw={500} size="sm">Antécédents</Text>
            </Group>
            {AntecedentsExpanded && (
              <div style={{ marginLeft: '20px' }}>
         {renderAntecedents(dataAntecedents)}
         </div>
                )}
          </div>
          </Card>
        {/* </div>  Antacedents*/}
 </div>
        )}
     {/* ---------------------------3--------------------------- */}
    </Group>
    </Card>
    </div>
  {isSidebarAlert && (
                       <Card shadow="sm" mt={'10px'} padding="lg" radius="md" withBorder className={isSidebarAlert ? "w-[20%]" : "w-full"}>
                      <Box mb="sm">
                        <Group>
                          <Input
                            placeholder="Rechercher"
                            value={search}
                            onChange={(e) => setSearch(e.currentTarget.value)}
                            w={"70%"}
                          />
            
                          <Group justify="flex-end">
                            <ActionIcon
                              variant="filled"
                              aria-label="Multiple"
                              color="#3799CE"
                              onClick={() => {
                                // Vous pouvez ajouter ici la logique pour ouvrir le modal de choix multiple
                                console.log('Open multiple choice modal');
                                setIsSidebarAlert(false);
                                setIsChoixMultipleModalOpen(true);
                                 
                              }}
                            >
                              <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                            </ActionIcon>
                            <ActionIcon
                              variant="filled"
                              aria-label="Annuler"
                              color="#3799CE"
                              onClick={handleCloseSidebar}
                            >
                              <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                            </ActionIcon>
                          </Group>
                        </Group>
                      </Box>
            
                      <ScrollArea h={400}>
                        <CustomTree
                          nodes={alertsTree.filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
                          onSelect={handleSidebarSelect}
                        />
                      </ScrollArea>
                       </Card>
                          )}
</div>
     <Modal opened={opened} onClose={close} title={<><Icon path={mdiViewHeadline} size={1} color="#3799ce" /> Choix multiple</>} centered size="md">
       <div style={{ padding: '10px' }}>
         <Group mb="md">
           <Icon path={mdiChevronDown} size={0.8} />
           <Checkbox
             checked={selectedFacteurs.length === currentDataFacteurs[0]?.children?.length}
             indeterminate={selectedFacteurs.length > 0 && selectedFacteurs.length < (currentDataFacteurs[0]?.children?.length || 0)}
             onChange={(event) => {
               if (event.currentTarget.checked) {
                 // Sélectionner tous
                 const allFacteurs = currentDataFacteurs[0]?.children?.map(f => String(f.label)) || [];
                 setSelectedFacteurs(allFacteurs);
                 setTextareaContent(allFacteurs.join(' , '));
               } else {
                 // Désélectionner tous
                 setSelectedFacteurs([]);
                 setTextareaContent('');
               }
             }}
           />
           <Text fw={500}>Facteurs de risque</Text>
         </Group>
         <div style={{ marginLeft: '20px' }}>
           {currentDataFacteurs[0]?.children?.map((facteur, index) => (
             <Group key={index} mb="xs" style={{ marginLeft: '20px' }}>
               <Checkbox
                 checked={selectedFacteurs.includes(String(facteur.label))}
                 onChange={() => handleFacteurSelection(String(facteur.label))}
               />
               <Text size="sm">{facteur.label}</Text>
             </Group>
           ))}
         </div>
         <Group justify="flex-end" mt="md">
           <Button color="blue" onClick={() => {
             // Mettre à jour le textarea avec les sélections
             const selectedText = selectedFacteurs.join(' , ');
             setTextareaContent(selectedText);

             // Sauvegarder dans l'historique si il y a des sélections
             if (selectedFacteurs.length > 0) {
               saveSelectionToHistory(selectedText);
             }

             close();
           }}>Valider</Button>
           <Button variant="outline" color="red" onClick={close}>Annuler</Button>
         </Group>
       </div>
      </Modal>
       <Modal opened={openedAllergies} onClose={Allergiesclose} title={<> <Group><Icon path={mdiPlaylistCheck} size={1} /> Choix multiple</Group></>} centered>
       <Tree data={dataAllergies} levelOffset={23} expandOnClick={false} renderNode={renderTreeNode} />
      </Modal>
 {/* ---------------------------2----------------------- */}
  <Modal opened={openedAntecedents} onClose={Antecedentsclose} title="Authentication" centered>
       <Icon path={mdiPlaylistCheck} size={1} /> Choix multiple
      </Modal>
     
       {/* --------------------------3----------------------- */}
        
      


      {/* Modal Historique */}
            <Modal
              opened={historyModalOpened}
              onClose={() => setHistoryModalOpened(false)}
              title={
                <Group>
                  <Icon path={mdiHistory} size={1} color="#3799ce" />
                  <Text>Dictionnaire : Historique du champ</Text>
                </Group>
              }
              centered
              size="md"
            >
              <div>
                {historyEntries.map((entry, index) => (
                  <Group key={index} mb="md" style={{ backgroundColor: '#e3f2fd', padding: '8px', borderRadius: '4px' }}>
                    <Text size="sm" fw={500} c="blue">{entry.date}</Text>
                    <Text size="sm">{entry.content}</Text>
                    <Group ml="auto">
                      <ActionIcon variant="transparent" size="sm" onClick={() => setTextareaContent(entry.content)}>
                        <Icon path={mdiPlusBox} size={0.8} color="#3799ce" />
                      </ActionIcon>
                      <ActionIcon variant="transparent" size="sm">
                        <Icon path={mdiHistory} size={0.8} color="#3799ce" />
                      </ActionIcon>
                    </Group>
                  </Group>
                ))}
              </div>
            </Modal>
      
            {/* Modal Microphone - Interface de reconnaissance vocale */}
            <Modal
              opened={microphoneModalOpened}
              onClose={cancelVoiceRecognition}
              title=""
              centered
              size="lg"
              withCloseButton={false}
            >
              <div style={{ padding: '20px' }}>
                {/* Interface de reconnaissance vocale */}
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginBottom: '20px' }}>
                  <div style={{ flex: 1, marginRight: '16px' }}>
                    <div style={{
                      border: '1px solid #e0e0e0',
                      borderRadius: '4px',
                      padding: '12px',
                      minHeight: '80px',
                      backgroundColor: '#fafafa'
                    }}>
                      {/* Texte valide reconnu */}
                      <span
                        style={{
                          color: '#2e7d32',
                          fontWeight: 500,
                          display: validSpeech ? 'inline' : 'none'
                        }}
                        contentEditable={true}
                        suppressContentEditableWarning={true}
                        onBlur={(e) => setValidSpeech(e.currentTarget.textContent || '')}
                      >
                        {validSpeech}
                      </span>

                      {/* Message d'état */}
                      <span
                        style={{
                          color: isListening ? '#ff9800' : '#666',
                          fontStyle: 'italic',
                          display: !validSpeech ? 'inline' : 'none'
                        }}
                      >
                        {invalidSpeech}
                      </span>
                    </div>
                  </div>

                  {/* Boutons de contrôle */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <ActionIcon
                      variant="filled"
                      color={isListening ? "orange" : "blue"}
                      size="lg"
                      onClick={toggleSpeechRecognition}
                      title="Microphone"
                    >
                      <Icon path={mdiMicrophone} size={1} />
                    </ActionIcon>

                    <ActionIcon
                      variant="filled"
                      color="red"
                      size="lg"
                      onClick={emptyVoiceContent}
                      title="Vider"
                    >
                      <Icon path={mdiClose} size={1} />
                    </ActionIcon>
                  </div>
                </div>

                {/* Boutons d'action */}
                <Group justify="flex-end" mt="md">
                  <Button
                    variant="filled"
                    color="blue"
                    onClick={submitVoiceRecognition}
                    disabled={!validSpeech.trim()}
                  >
                    Valider
                  </Button>
                  <Button
                    variant="outline"
                    color="red"
                    onClick={cancelVoiceRecognition}
                  >
                    Annuler
                  </Button>
                </Group>
              </div>
            </Modal>
      
            {/* Modal Modèles */}
            <Modal
              opened={modelsModalOpened}
              onClose={() => setModelsModalOpened(false)}
              title={
                <Group>
                  <Icon path={mdiClipboardText} size={1} color="#3799ce" />
                  <Text>Modèles de dictionnaire</Text>
                  <Group ml="auto">
                    <ActionIcon variant="transparent" onClick={() => setShowAddModel(true)}>
                      <Icon path={mdiPlusBox} size={1} color="#3799ce" />
                    </ActionIcon>
                  </Group>
                </Group>
              }
              centered
              size="md"
            >
              {/* Formulaire d'ajout de modèle */}
              {showAddModel && (
                <div style={{ backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '4px', marginBottom: '16px' }}>
                  <Text size="sm" fw={500} mb="xs">Ajouter un nouveau modèle</Text>
                  <TextInput
                    placeholder="Nom du modèle"
                    value={newModelName}
                    onChange={(event) => setNewModelName(event.currentTarget.value)}
                    mb="xs"
                  />
                  <Textarea
                    placeholder="Contenu du modèle"
                    value={newModelContent}
                    onChange={(event) => setNewModelContent(event.currentTarget.value)}
                    minRows={3}
                    mb="xs"
                  />
                  <Group justify="flex-end">
                    <Button size="xs" variant="outline" onClick={() => setShowAddModel(false)}>
                      Annuler
                    </Button>
                    <Button size="xs" onClick={addNewModel}>
                      Enregistrer
                    </Button>
                  </Group>
                </div>
              )}

              {/* Liste des modèles */}
              {models.length === 0 ? (
                <div style={{ backgroundColor: '#fff3cd', padding: '16px', borderRadius: '4px', marginBottom: '16px' }}>
                  <Group>
                    <Icon path={mdiHistory} size={1} color="#ff9800" />
                    <Text size="sm" c="#856404">Aucun modèle à afficher</Text>
                  </Group>
                </div>
              ) : (
                <div style={{ marginBottom: '16px' }}>
                  {models.map((model) => (
                    <div key={model.id} style={{ backgroundColor: '#e3f2fd', padding: '12px', borderRadius: '4px', marginBottom: '8px' }}>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500} c="blue">{model.name}</Text>
                          <Text size="xs" c="dimmed">{model.content}</Text>
                        </div>
                        <ActionIcon variant="transparent" onClick={() => applyModel(model.content)}>
                          <Icon path={mdiPlusBox} size={0.8} color="#3799ce" />
                        </ActionIcon>
                      </Group>
                    </div>
                  ))}
                </div>
              )}

              <Group justify="flex-end">
                <Button variant="filled" color="blue" onClick={() => setModelsModalOpened(false)}>
                  Valider
                </Button>
                <Button variant="outline" color="red" onClick={() => setModelsModalOpened(false)}>
                  Annuler
                </Button>
              </Group>
            </Modal>
            
                          {/* menu Alerts */}
                                                 <Modal.Root
                                                    opened={isAlertsModalOpen}
                                                    onClose={() => setIsAlertsModalOpen(false)}
                                                    transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                                                    centered
                                                    size="xl"
                                                  > 
                                                
                                                 <Modal.Content className="overflow-y-hidden">
                                                  <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                                                    <Modal.Title>
                                                      <Group>
                                                        <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                                          <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                                            Alerts - {currentPatient ? `${currentPatient.first_name || ''} ${currentPatient.last_name || ''}`.trim() || 'Patient' : 'Patient'}
                                                          </Text>
                                                      </Group>
                                                    </Modal.Title>
                                                      <Group justify="flex-end">
                                                        <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                                                        onClick={() => 
                                                          {setIsAlertsAddModalOpen(true)
                                                        ; toggleSidebarAlert()}}>
                                                 <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                                        </ActionIcon>
                                                                <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} 
                                                                 onClick={handleCloseSidebar}
                                                                />
                                                              </Group>
                                                  </Modal.Header>
                                                    <Modal.Body style={{ padding: '0px' }}>
                                                     <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                                                            
                                                              <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                                                <div className="pr-4">
                                                                 <Table striped highlightOnHover withTableBorder withColumnBorders>
                                                                   <Table.Thead>
                                                <Table.Tr>
                                                  <Table.Th>Déclencheur</Table.Th>
                                                  <Table.Th>Niveau</Table.Th>
                                                  <Table.Th>Publique</Table.Th>
                                                  <Table.Th>Permanente</Table.Th>
                                                   <Table.Th>Description</Table.Th>
                                                  <Table.Th></Table.Th>
                                                </Table.Tr>
                                              </Table.Thead>
                                              <Table.Tbody>{rows}</Table.Tbody>
                                              {rows.length === 0 && (
                                                <Table.Caption>Aucun élément trouvé.</Table.Caption>
                                              )}
                                                                  </Table>
                                                                </div>
                                                              </SimpleBar>
                                                            </div>
                                                          </Modal.Body>
                                                 </Modal.Content>
                                                  </Modal.Root>
                                                  {/* add Alerts */}
                                                   <Modal.Root
                                                    opened={isAlertsAddModalOpen}
                                                    onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                                                    transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                                                    centered
                                                    size="xl"
                                                  > 
                                                 
                                                 <Modal.Content className="overflow-y-hidden">
                                                  <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                                                    <Modal.Title>
                                                      <Group>
                                                        <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                                          <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                                             {`Alerte - ${currentPatient ? `${currentPatient.first_name || ''} ${currentPatient.last_name || ''}`.trim() || 'Patient' : 'Patient'}`}
                                                            </Text>
                                                      </Group>
                                                    </Modal.Title>
                                                      <Group justify="flex-end">
                                                                <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                                              </Group>
                                                  </Modal.Header>
                                                    <Modal.Body style={{ padding: '0px' }}>
                                             
                                                     <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
                                                    
                                                              <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                                                <div className="pr-4">
                                                               <form
                                                                      onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                                                      style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                                                    >
                                                                      <Group>
                                                                      <MultiSelect
                                                                        label="Déclencher pour"
                                                                        data={staffOptions}
                                                                        {...form.getInputProps('trigger_for')}
                                                                        required
                                                                        w={"30%"}
                                                                      />
                                                                      <Select
                                                                        label="Déclencheur"
                                                                        data={triggerOptions}
                                                                        {...form.getInputProps('trigger')}
                                                                        required
                                                                         w={"30%"}
                                                                      />
                                                                      <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                                                        <Group>
                                                                          <Radio value="MINIMUM" label="Minimum" />
                                                                          <Radio value="MEDIUM" label="Moyen" />
                                                                          <Radio value="HIGH" label="Haut" />
                                                                        </Group>
                                                                      </Radio.Group>
                                                                      </Group>
                                                                      <Group justify="space-between">
                                                                        <Text>Description *</Text>
                                                                        <Group>
                                                                         <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                                                      onClick={
                                                                        ()=>setIsMicrophoneModalOpen(true)
                                                                      }>
                                                                      <Icon path={mdiMicrophone} size={1} />
                                                                              </ActionIcon>
                                                                       <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                                                      onClick={
                                                                        ()=>{
                                                                          console.log('Dictionary button clicked, sidebar visible:', isSidebarAlert);
                                                                          setIsClipboardTextModalOpen(true);
                                                                             setShowModels(true);
                                                                             setShowAddModel(false);
                                                                             handleCloseSidebar()
                             
                                                                        }
                                                                      }>
                                                                      <Icon path={mdiClipboardText} size={1} />
                                                                              </ActionIcon>
                                                                       <ActionIcon
                                                                         variant="filled"
                                                                         aria-label="Clear Description"
                                                                         color="red"
                                                                         onClick={() => {
                                                                           console.log('Clear button clicked, clearing description field');
                                                                           form.setFieldValue('description', '');
                                                                           console.log('Description field cleared');
                                                                         }}
                                                                       >
                                                                         <Icon path={mdiDeleteSweep} size={1} />
                                                                       </ActionIcon>
                                                                        </Group>
                                                                      </Group>
                                                                      <Textarea
                                                                        // label="Description"
                                                                        placeholder="Ajouter"
                                                                        {...form.getInputProps('description')}
                                                                        required
                                                                      />
                                                                    
                                                                      <Switch
                                                                        label="Permanente"
                                                                        {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                                                      />
                                                              
                                                                      <Group justify="flex-end" mt="md">
                                                                        <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                                                          Annuler
                                                                        </Button>
                                                                        <Button
                                                                          onClick={() => {
                                                                            if (form.isValid()) {
                                                                              handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                                                            }
                                                                          }}
                                                                          disabled={!form.isValid()}
                                                                        >
                                                                          Enregistrer et déclencher
                                                                        </Button>
                                                                        <Button type="submit" disabled={!form.isValid()}>
                                                                          Enregistrer
                                                                        </Button>
                                                                      </Group>
                                                                    </form>
                                                                </div>
                                                              </SimpleBar>
                                                            </div>
                                                          </Modal.Body>
                                                 </Modal.Content>
                                                  </Modal.Root>
                                             
                             
                                                  {/* Modal de confirmation de suppression */}
                                                  <Modal.Root
                                                    opened={isDeleteConfirmModalOpen}
                                                    onClose={cancelDeleteAlert}
                                                    centered
                                                    size="sm"
                                                  >
                                                    <Modal.Content>
                                                      <Modal.Header>
                                                        <Modal.Title>Confirmation de suppression</Modal.Title>
                                                        <Modal.CloseButton />
                                                      </Modal.Header>
                                                      <Modal.Body>
                                                        <Text size="md" mb="md">
                                                          Êtes-vous sûr de vouloir supprimer alert ??
                                                        </Text>
                                                        <Group justify="flex-end" gap="sm">
                                                          <Button
                                                            variant="outline"
                                                            color="blue"
                                                            onClick={confirmDeleteAlert}
                                                          >
                                                            Oui
                                                          </Button>
                                                          <Button
                                                            variant="filled"
                                                            color="red"
                                                            onClick={cancelDeleteAlert}
                                                          >
                                                            Non
                                                          </Button>
                                                        </Group>
                                                      </Modal.Body>
                                                    </Modal.Content>
                                                  </Modal.Root>
                                                   {/* Modal Microphone - Reconnaissance vocale */}
                                                                   <Modal
                                                                     opened={isMicrophoneModalOpen}
                                                                     onClose={() => setIsMicrophoneModalOpen(false)}
                                                                     title="Reconnaissance vocale"
                                                                     size="lg"
                                                                     radius={0}
                                                                     transitionProps={{ transition: 'fade', duration: 200 }}
                                                                     centered
                                                                     withCloseButton={false}
                                                                     yOffset="30vh" xOffset={0}
                                                                     
                                                                   >
                                                                     <div style={{ padding: '20px' }}>
                                                                       {/* Interface de reconnaissance vocale */}
                                                                       <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                                                                         <div style={{ flex: 1, marginRight: '16px' }}>
                                                                           <div style={{
                                                                             border: '1px solid #e0e0e0',
                                                                             borderRadius: '4px',
                                                                             padding: '12px',
                                                                             minHeight: '80px',
                                                                             backgroundColor: '#fafafa',
                                                                            height:'150px'
                                                                           }}>
                                                                             {/* Texte valide reconnu */}
                                                                             <span
                                                                               style={{
                                                                                 color: '#2e7d32',
                                                                                 fontWeight: 500,
                                                                                 display: validSpeech ? 'inline' : 'none'
                                                                               }}
                                                                               contentEditable
                                                                             >
                                                                               {validSpeech}
                                                                             </span>
                                                                             {/* Texte en cours de reconnaissance */}
                                                                             <span
                                                                               style={{
                                                                                 color: '#757575',
                                                                                 fontStyle: 'italic'
                                                                               }}
                                                                             >
                                                                               {invalidSpeech}
                                                                             </span>
                                                                           </div>
                                                                         </div>
                                                             
                                                                         {/* Boutons de contrôle */}
                                                                         <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                                                           <ActionIcon
                                                                             variant="subtle"
                                                                             color={isListening ? 'orange' : 'blue'}
                                                                             size="lg"
                                                                             onClick={toggleRecognition}
                                                                             style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                                                                           >
                                                                             <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                                                                           </ActionIcon>
                                                             
                                                                           <ActionIcon
                                                                             variant="subtle"
                                                                             color="red"
                                                                             size="lg"
                                                                             onClick={emptyContent}
                                                                           >
                                                                             <Icon path={mdiDeleteSweep} size={1} />
                                                                           </ActionIcon>
                                                                         </div>
                                                                       </div>
                                                             
                                                                       {/* Boutons d'action */}
                                                                       <Group justify="flex-end" mt="md">
                                                                         <Button
                                                                           variant="filled"
                                                                           onClick={() => {
                                                                             // Ici vous pouvez traiter le texte reconnu
                                                                             console.log('Texte reconnu:', validSpeech);
                                                                             setIsMicrophoneModalOpen(false);
                                                                           }}
                                                                         >
                                                                           Valider
                                                                         </Button>
                                                                         <Button
                                                                           variant="outline"
                                                                           color="red"
                                                                           onClick={() => setIsMicrophoneModalOpen(false)}
                                                                         >
                                                                           Annuler
                                                                         </Button>
                                                                       </Group>
                                                                     </div>
                                                                   </Modal>
                                                             
                                                                   {/* Gestionnaire des modaux de dictionnaire */}
                                                                   <DictionaryModalsManager
                                                                     // États des modaux
                                                                     isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                                                                     isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                                                                     isDictionaryTreeModalOpen={isChoixMultipleModalOpen}
                                                  
                                                                     // Données
                                                                     modelTitle={modelTitle}
                                                                     savedModels={savedModels}
                                                                     exampleData={exampleData}
                                                                     selectedNodes={selectedNodes}
                                                                     collapsedNodes={collapsedNodes}
                                                                     editingModelId={editingModelId}
                                                  
                                                                     // Fonctions de gestion des états
                                                                     setModelTitle={setModelTitle}
                                                                     setIsAddModelModalOpen={setShowAddModel}
                                                                     setIsSavedModelsModalOpen={setShowModels}
                                                                     setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}
                                                  
                                                                     // Fonctions de gestion des modèles
                                                                     onSaveModel={handleSaveModel}
                                                                     onToggleModel={(modelId) => {
                                                                       console.log('Toggling model:', modelId);
                                                                       setSavedModels(prev => {
                                                                         const updated = prev.map(model =>
                                                                           model.id === modelId
                                                                             ? { ...model, selected: !model.selected }
                                                                             : model
                                                                         );
                                                                         console.log('Updated savedModels:', updated);
                                                                         return updated;
                                                                       });
                                                                     }}
                                                                     onDeleteModel={handleDeleteModel}
                                                                     onEditModel={handleEditModel}
                                                  
                                                                     // Fonctions de gestion de l'arbre
                                                                     onToggleNodeCollapse={toggleNodeCollapse}
                                                                     onToggleNodeSelection={toggleNodeSelection}
                                                                     onSelectAll={selectAllNodes}
                                                                     onDeselectAll={deselectAllNodes}
                                                  
                                                                     // Fonctions d'action
                                                                     onValidate={handleValidate}
                                                                     onCancel={handleCancel}
                                                                     onCloseSidebar={() => {
                                                                       console.log('Closing sidebar from SavedModelsModal');
                                                                       setIsSidebarVisible(false);
                                                                     }}
                                                                     getSelectedValues={getSelectedValues}
                                                  
                                                                     // Composants
                                                                     TreeItemChoixMultiple={TreeItemChoixMultiple}
                                                                   />
                                                  
                                                                   {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                                                                   {isClipboardTextModalOpen && !showModels && !showAddModel && (
                                                                     <div style={{ display: 'none' }}>
                                                                       {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                                                                     </div>
                                                                   )}
      <div style={{marginTop:"10px" ,marginBottom:"20px", borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
                           
          <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
        <Group gap="xs">
            {patientId && (
            <>
                <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                    <ActionIcon variant="filled" aria-label="Settings" radius="4px"
            onClick={onPrint}>
                <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
            </ActionIcon>
                </Tooltip>
            <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                    <ActionIcon variant="filled" aria-label="Settings"radius="4px"
            onClick={onPrevious}>
                <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
            </ActionIcon>
                </Tooltip>
            
    <Tooltip label="Imprimer le code-barres" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]"> 
                    <ActionIcon variant="filled" aria-label="Settings"radius="4px"
            onClick={onNext}>
                <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
            </ActionIcon>
                </Tooltip>
            
            </>
            )}
        </Group>
    
        <Group gap="xs" mr={10}>
            <Tooltip label="Commencer la visite" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
            <ActionIcon variant="filled" aria-label="Settings" radius="4px"
            onClick={onStartVisit}
                disabled={disabled}>
            <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
            </ActionIcon>
            </Tooltip>
            <Tooltip label="Ajouter un rendez-vous" style={{color:"var(--mantine-color-text)"}} className="bg-[var(--tooltip-bg)]">
            <ActionIcon variant="filled" aria-label="Settings"radius="4px"
            onClick={onAppointment}
                disabled={isFormInvalid}>
                <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
            </ActionIcon>
            </Tooltip>
            <Button variant="outline" color="red" component={Link} href={`/home`} > 
          Annuler</Button>
          
    
            {patientId && (
            <Button
                variant="filled"
                color="blue"
                onClick={onSaveQuitNew}
                disabled={isFormInvalid}
            >
                Enregistrer & Nouvelle fiche
            </Button>
            )}
    
            <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuit}
            disabled={isFormInvalid}
            >
            Enregistrer et quitter
            </Button>
    
            <Button
            variant="filled"
            color="blue"
            type="submit"
            disabled={isFormInvalid}
            >
            Enregistrer la fiche
            </Button>
        </Group>
        </Group>
          
        </div>

         {/*modal Relations */}
                            <Modal.Root
                               opened={isRelationsModalOpen}
                               onClose={() => setIsRelationsModalOpen(false)}
                               transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                               centered
                               size="lg"
                               zIndex={10}
                             > 
                           
                            <Modal.Content className="overflow-y-hidden">
                             <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                               <Modal.Title>
                                 <Group>
                                   <Icon path={mdiAccountSupervisorCircle} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                     <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                     Relations - {currentPatient ? `${currentPatient.first_name || ''} ${currentPatient.last_name || ''}`.trim() || 'Patient' : 'Patient'} </Text>
                                 </Group>
                               </Modal.Title>
                                 <Group justify="flex-end">
                                   <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                                   onClick={openListDesPatient}>
                            <Icon path={mdiAccountSearch} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                   </ActionIcon>
                                           <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                         </Group>
                             </Modal.Header>
                               <Modal.Body style={{ padding: '0px' }}>
                                <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                                       
                                         <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                           <div className="pr-4">
                                            <Card shadow="sm" padding="lg" radius="md" withBorder>
                                              <RelationForm
                                                patientId={patientId}
                                                enableDjangoSync={enableDjangoSync}
                                                readOnly={loading}
                                              />
                                            </Card>
                                             
                                           </div>
                                         </SimpleBar>
                                       </div>
                                     </Modal.Body>
                                      <Group justify="flex-end" m="md">
                                      <Button variant="outline" color="red" onClick={() => setIsRelationsModalOpen(false)}>
                                        Annuler
                                      </Button>
                                      <Button type="submit" disabled={!form.isValid()}>
                                        Enregistrer
                                      </Button>
                                    </Group> 
                            </Modal.Content>
                             </Modal.Root>
    </>  
  )
}
