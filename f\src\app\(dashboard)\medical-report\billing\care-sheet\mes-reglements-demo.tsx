'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { MesReglements } from './Mes_reglements';

export default function MesReglementsDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Recherche de règlements:\nTerme: "${query.searchAll}"\nPage: ${query.page}\nLignes: ${query.limit}\nFiltres: ${Object.keys(query.filters).length} actifs\nRecherche par colonne: ${Object.keys(query.search).length} actives`);
  };

  const handleAddPayment = () => {
    console.log('Ajouter règlement');
    alert(`Création d'un nouveau règlement:\nOuverture du formulaire de saisie\nChamps: N°.Réglement, Date, Bénéficiaire, Payeur, Montant dû, Montant consommé, Reliquat`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export:', format);
    alert(`Export Excel des règlements en cours...\nFormat: ${format.toUpperCase()}\nTéléchargement démarré`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action:', action, 'Items:', items);
    const actionLabels: { [key: string]: string } = {
      'reload': 'Actualiser'
    };
    const actionLabel = actionLabels[action] || action;
    alert(`Action: ${actionLabel}\nNombre d'éléments sélectionnés: ${items.length}\nTraitement en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onAddPayment={handleAddPayment}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MesReglementsLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onAddPayment={() => console.log('Add payment')}
          onExport={(format) => console.log('Export:', format)}
          onAction={(action, items) => console.log('Action:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MesReglementsWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      paymentNumber: 'REG-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      payer: 'CPAM',
      totalAmount: 1250.00,
      consumedAmount: 850.00,
      outstandingAmount: 400.00
    },
    {
      id: '2',
      paymentNumber: 'REG-002',
      date: new Date('2025-07-01'),
      beneficiary: 'DUPONT Marie',
      payer: 'Mutuelle XYZ',
      totalAmount: 750.00,
      consumedAmount: 750.00,
      outstandingAmount: 0.00
    },
    {
      id: '3',
      paymentNumber: 'REG-003',
      date: new Date('2025-06-30'),
      beneficiary: 'BERNARD Paul',
      payer: 'Particulier',
      totalAmount: 2100.00,
      consumedAmount: 1500.00,
      outstandingAmount: 600.00
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    alert(`Recherche dans ${sampleItems.length} règlements:\nTerme: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nFiltres actifs: ${Object.keys(query.filters).length}\nRecherche par colonne: ${Object.keys(query.search).length}`);
  };

  const handleAddPayment = () => {
    console.log('Ajouter règlement avec données');
    alert(`Nouveau règlement:\n\nProchain numéro: REG-${String(sampleItems.length + 1).padStart(3, '0')}\nFormulaire de création ouvert\nDonnées pré-remplies disponibles\nMontant total actuel: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export avec données:', format);
    alert(`Export Excel de ${sampleItems.length} règlements:\n\nContenu:\n- Total des règlements: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Montant consommé: ${sampleItems.reduce((sum, item) => sum + item.consumedAmount, 0).toFixed(2)}€\n- Reliquat total: ${sampleItems.reduce((sum, item) => sum + item.outstandingAmount, 0).toFixed(2)}€\n- Bénéficiaires: ${new Set(sampleItems.map(item => item.beneficiary)).size}\n- Payeurs: ${new Set(sampleItems.map(item => item.payer)).size}\n- Période: ${new Date(Math.min(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')} - ${new Date(Math.max(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')}\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action avec données:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'reload': `Actualisation des données:\n- ${sampleItems.length} règlements rechargés\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}\n- Total: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Reliquat: ${sampleItems.reduce((sum, item) => sum + item.outstandingAmount, 0).toFixed(2)}€`
    };
    
    const message = actionMessages[action] || `Action ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddPayment={handleAddPayment}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres
export function MesReglementsFiltersDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtres:', query);
    if (query.searchAll) {
      alert(`Recherche avancée:\nTerme: "${query.searchAll}"\nRecherche dans: N°.Réglement, Bénéficiaires, Payeurs, etc.`);
    }
    if (Object.keys(query.filters).length > 0) {
      alert(`Filtres appliqués:\n${Object.entries(query.filters).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`);
    }
    if (Object.keys(query.search).length > 0) {
      alert(`Recherche par colonne:\n${Object.entries(query.search).map(([key, value]) => `- ${key}: "${value}"`).join('\n')}`);
    }
  };

  const handleAddPayment = () => {
    console.log('Ajouter avec filtres');
    alert(`Nouveau règlement:\nLes filtres actuels seront conservés après création`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddPayment={handleAddPayment}
          onExport={(format) => alert(`Export ${format} avec filtres appliqués`)}
          onAction={(action, items) => console.log('Action filtres:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function MesReglementsPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les règlements`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={[]}
          total={150} // Simule 150 règlements pour tester la pagination
          onQueryChange={handleQueryChange}
          onAddPayment={() => console.log('Add pagination')}
          onExport={(format) => console.log('Export pagination:', format)}
          onAction={(action, items) => console.log('Action pagination:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MesReglementsErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleAddPayment = () => {
    console.log('Ajouter avec validation');
    if (confirm('Êtes-vous sûr de vouloir créer un nouveau règlement ?')) {
      alert('Règlement créé avec succès !');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddPayment={handleAddPayment}
          onExport={(format) => {
            if (confirm(`Êtes-vous sûr de vouloir exporter en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onAction={(action, items) => console.log('Action avec validation:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données financières
export function MesReglementsFinancialDemo() {
  const sampleItems = [
    {
      id: '1',
      paymentNumber: 'REG-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      payer: 'CPAM',
      totalAmount: 1250.00,
      consumedAmount: 850.00,
      outstandingAmount: 400.00
    },
    {
      id: '2',
      paymentNumber: 'REG-002',
      date: new Date('2025-07-01'),
      beneficiary: 'DUPONT Marie',
      payer: 'Mutuelle XYZ',
      totalAmount: 750.00,
      consumedAmount: 750.00,
      outstandingAmount: 0.00
    },
    {
      id: '3',
      paymentNumber: 'REG-003',
      date: new Date('2025-06-30'),
      beneficiary: 'BERNARD Paul',
      payer: 'Particulier',
      totalAmount: 2100.00,
      consumedAmount: 1500.00,
      outstandingAmount: 600.00
    }
  ];

  const totalAmount = sampleItems.reduce((sum, item) => sum + item.totalAmount, 0);
  const totalConsumed = sampleItems.reduce((sum, item) => sum + item.consumedAmount, 0);
  const totalOutstanding = sampleItems.reduce((sum, item) => sum + item.outstandingAmount, 0);

  const handleQueryChange = (query: any) => {
    console.log('Requête financière:', query);
    alert(`Analyse financière des règlements:\n\nRecherche: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\n\nRésumé financier:\n- Total des règlements: ${totalAmount.toFixed(2)}€\n- Montant consommé: ${totalConsumed.toFixed(2)}€ (${((totalConsumed / totalAmount) * 100).toFixed(1)}%)\n- Reliquat total: ${totalOutstanding.toFixed(2)}€ (${((totalOutstanding / totalAmount) * 100).toFixed(1)}%)\n\nStatistiques:\n- Nombre de règlements: ${sampleItems.length}\n- Montant moyen: ${(totalAmount / sampleItems.length).toFixed(2)}€\n- Reliquat moyen: ${(totalOutstanding / sampleItems.length).toFixed(2)}€`);
  };

  const handleAddPayment = () => {
    console.log('Ajouter règlement financier');
    alert(`Nouveau règlement financier:\n\nProchain numéro: REG-${String(sampleItems.length + 1).padStart(3, '0')}\n\nContexte financier actuel:\n- Total des règlements: ${totalAmount.toFixed(2)}€\n- Reliquat en attente: ${totalOutstanding.toFixed(2)}€\n- Taux de consommation: ${((totalConsumed / totalAmount) * 100).toFixed(1)}%\n\nFormulaire de saisie ouvert...`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export financier:', format);
    alert(`Export Excel financier:\n\nRapport détaillé:\n- ${sampleItems.length} règlements\n- Période: ${new Date(Math.min(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')} - ${new Date(Math.max(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')}\n\nMontants:\n- Total: ${totalAmount.toFixed(2)}€\n- Consommé: ${totalConsumed.toFixed(2)}€\n- Reliquat: ${totalOutstanding.toFixed(2)}€\n\nAnalyse par payeur:\n- CPAM: ${sampleItems.filter(item => item.payer === 'CPAM').reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Mutuelles: ${sampleItems.filter(item => item.payer.includes('Mutuelle')).reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Particuliers: ${sampleItems.filter(item => item.payer === 'Particulier').reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action financière:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'reload': `Actualisation des données financières:\n\nDonnées rechargées:\n- ${sampleItems.length} règlements\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}\n\nSynthèse financière:\n- Total: ${totalAmount.toFixed(2)}€\n- Consommé: ${totalConsumed.toFixed(2)}€ (${((totalConsumed / totalAmount) * 100).toFixed(1)}%)\n- Reliquat: ${totalOutstanding.toFixed(2)}€ (${((totalOutstanding / totalAmount) * 100).toFixed(1)}%)\n\nIndicateurs:\n- Taux de recouvrement: ${((totalConsumed / totalAmount) * 100).toFixed(1)}%\n- Montant moyen par règlement: ${(totalAmount / sampleItems.length).toFixed(2)}€`
    };
    
    const message = actionMessages[action] || `Action financière ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesReglements
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddPayment={handleAddPayment}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}
