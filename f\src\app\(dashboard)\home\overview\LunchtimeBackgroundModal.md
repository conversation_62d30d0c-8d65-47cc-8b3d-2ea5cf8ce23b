# Lunchtime Background Modal Documentation

## Overview

The `LunchtimeBackgroundModal` is an enhanced component for scheduling lunch breaks for doctors in both Room A and Room B within the calendar system. It provides advanced functionality including template saving/loading, doctor assignment, and room selection.

## Features

### 1. **Doctor and Room Assignment**
- Select specific doctors from a dropdown list
- Assign lunch breaks to Room A or Room B
- Automatic room-to-resource mapping (Room A = resourceId 1, Room B = resourceId 2)

### 2. **Template Management**
- Save lunch break configurations as reusable templates
- Load previously saved templates
- Delete unwanted templates
- Templates are stored in localStorage for persistence

### 3. **Advanced Scheduling**
- Date and time picker for precise scheduling
- Customizable duration (15-180 minutes in 15-minute increments)
- Color customization for visual distinction
- Optional notes and recurring options

### 4. **Calendar Integration**
- Seamlessly integrates with the existing calendar system
- Events are properly formatted for the calendar
- Supports the existing lunch event deletion functionality

## Usage

### Opening the Modal

The modal can be opened in two ways:

1. **From the enhanced button**: Click the "Pause Avancée" button next to the existing lunch duration button
2. **Programmatically**: Call `openLunchtimeModalWithContext(roomId?, doctorId?)`

### Creating a Lunch Break

1. **Basic Information**:
   - Enter a title (defaults to "🍽️ Pause Déjeuner")
   - Select start date and time
   - Set duration in minutes

2. **Assignment**:
   - Choose a doctor from the dropdown
   - Select Room A or Room B
   - Pick a color for the event

3. **Optional Settings**:
   - Add notes for additional information
   - Enable recurring option to save as a backend pause
   - Save as template for future use

### Template Management

#### Saving Templates
1. Fill out the lunch break form
2. Click "Nouveau modèle" in the templates section
3. Enter a template name
4. Click "Sauvegarder"

#### Loading Templates
1. Find the desired template in the saved templates list
2. Click the reload icon (🔄) next to the template
3. The form will be populated with the template data

#### Deleting Templates
1. Click the trash icon (🗑️) next to the template you want to delete
2. The template will be removed from localStorage

## Technical Implementation

### Component Structure

```typescript
interface LunchtimeBackgroundModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (lunchData: LunchEventData) => void;
  currentDate: Date;
  selectedRoom?: string;
  selectedDoctor?: string;
}
```

### Data Flow

1. **Input**: User fills out the form with lunch break details
2. **Processing**: Component validates data and formats it for the calendar
3. **Output**: Calls `onSave` callback with properly formatted `LunchEventData`
4. **Integration**: Parent component receives data and adds it to the calendar

### Storage

- **Templates**: Stored in `localStorage` under the key `lunchtime_templates`
- **Doctors**: Fetched from the backend via `userAPI.getDoctors()`
- **Recurring Pauses**: Optionally saved to backend via `pauseAPI.create()`

## Integration Points

### With Calendar System
- Events are formatted as `CalendarEvent` objects
- Proper room mapping (room-a/room-b to resourceId 1/2)
- Lunch events are marked with `lunchTime: true`

### With Doctor Management
- Fetches active doctors from the backend
- Supports doctor assignment to lunch breaks
- Resolves doctor names for display

### With Existing Lunch Functionality
- Complements the existing simple lunch duration modal
- Uses the same event addition mechanism (`onEventAdd`)
- Maintains compatibility with existing lunch event deletion

## Error Handling

- **Doctor Loading**: Shows notification if doctors cannot be loaded
- **Form Validation**: Requires doctor selection before saving
- **Template Management**: Validates template names before saving
- **API Errors**: Graceful handling of backend API failures

## Styling and UI

- **Consistent Design**: Uses Mantine components for consistency
- **Color Coding**: Visual preview of selected colors
- **Responsive Layout**: Adapts to different screen sizes
- **Accessibility**: Proper labels and keyboard navigation

## Future Enhancements

1. **Backend Template Storage**: Move templates from localStorage to backend
2. **Recurring Patterns**: Advanced recurring options (daily, weekly, etc.)
3. **Conflict Detection**: Check for scheduling conflicts
4. **Bulk Operations**: Create multiple lunch breaks at once
5. **Integration with Doctor Schedules**: Respect doctor availability

## Troubleshooting

### Common Issues

1. **Modal doesn't open**: Check that `openLunchtimeModal` is properly called
2. **Doctors not loading**: Verify backend API is accessible
3. **Templates not saving**: Check localStorage permissions
4. **Events not appearing**: Ensure `onEventAdd` callback is properly connected

### Debug Information

The component logs important events to the console:
- Doctor loading status
- Template operations
- Event creation and saving
- API call results

## Code Example

```typescript
// Opening the modal with context
const handleOpenLunchModal = (roomId: string, doctorId: string) => {
  openLunchtimeModalWithContext(roomId, doctorId);
};

// Handling saved lunch data
const handleLunchtimeSave = (lunchData: LunchEventData) => {
  // Convert to calendar event and add to calendar
  const calendarEvent = {
    ...lunchData,
    // Additional calendar-specific properties
  };
  onEventAdd(calendarEvent);
};
```

This enhanced lunchtime modal provides a comprehensive solution for managing doctor lunch breaks with advanced features while maintaining seamless integration with the existing calendar system.
