'use client';
import { useMemo } from 'react';
// Import du composant Nouvel_encaissement_modal
// import Nouvel_encaissement_modal from './Nouvel_encaissement_modal';
import { DataTable, type DataTableSortStatus } from 'mantine-datatable';
import sortBy from 'lodash/sortBy';
import { useEffect, useState } from 'react';
// import { companies, type Company } from '~/data';
import Tablerecords from './Tablerecords'
import classes from './RowExpansionExampleSimple.module.css'
import Icon from '@mdi/react';
import { mdiAccountCash,mdiDetails, mdiReload } from '@mdi/js';
import { usePayment } from '@/hooks/usePayment';
import { notifications } from '@mantine/notifications';
import patientService from '@/services/patientService';
import {
  Box,
  Card,
  Group,
  Button,
  TextInput,
  // Table,
  Text,
Stack,
  ActionIcon,
  Radio,
  Select,
  Switch,
  Loader,
  <PERSON>ert,
  Badge,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  // IconChevronDown,
  // IconChevronUp,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconAlertCircle,

} from '@tabler/icons-react';
const PAGE_SIZES = [5,10, 15, 20,25 ,100];
// Interface pour les données de compte
interface CompteData {
  id: string;
  nomComplet: string;
  montantDu: number;
  montantEncaisse: number;
  resteARegler: number;
  // Additional backend fields
  patient_id?: string;
  account_status?: 'current' | 'overdue' | 'paid' | 'credit';
  last_payment_date?: string;
  last_payment_amount?: number;
  payment_count?: number;
  created_at?: string;
  updated_at?: string;
  type?: 'patient' | 'organization';
  insurance_provider?: string;
}

// Interface for account statistics
interface AccountStats {
  totalAccounts: number;
  totalDue: number;
  totalCollected: number;
  totalRemaining: number;
  overdueAccounts: number;
  paidAccounts: number;
  currentAccounts: number;
  patientAccounts: number;
  organizationAccounts: number;
}

const EtatDuComptGeneral = () => {
  // États pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('Patient');
  const [afficherComptesDebiteurs, setAfficherComptesDebiteurs] = useState(true);

  const [itemsPerPage, ] = useState(10);
  // const [sortField, setSortField] = useState<string>('');
  // const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Backend State
  const [comptesPatients, setComptesPatients] = useState<CompteData[]>([]);
  const [comptesOrganismes, setComptesOrganismes] = useState<CompteData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [accountStats, setAccountStats] = useState<AccountStats>({
    totalAccounts: 0,
    totalDue: 0,
    totalCollected: 0,
    totalRemaining: 0,
    overdueAccounts: 0,
    paidAccounts: 0,
    currentAccounts: 0,
    patientAccounts: 0,
    organizationAccounts: 0
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Use payment hook for backend integration
  const {
    accountBalances,
    loading: balancesLoading,
    error: balancesError,
    fetchAccountBalances,
    refreshAll
  } = usePayment({
    autoFetch: true,
    dataTypes: ['balances']
  });

  // Load account balances from backend
  useEffect(() => {
    const loadAccountData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          console.log('💰 Loading account balances from backend...');

          // Transform account balances to compte format
          const patientComptes: CompteData[] = accountBalances.map(balance => ({
            id: balance.id.toString(),
            nomComplet: balance.patient_name,
            montantDu: balance.total_due,
            montantEncaisse: balance.total_collected,
            resteARegler: balance.remaining_balance,
            // Additional backend fields
            patient_id: balance.patient_id,
            account_status: balance.account_status,
            last_payment_date: balance.last_payment_date,
            last_payment_amount: balance.last_payment_amount,
            payment_count: balance.payment_history?.length || 0,
            created_at: balance.created_at,
            updated_at: balance.updated_at,
            type: 'patient'
          }));

          setComptesPatients(patientComptes);

          // For now, use mock data for organizations until we have organization balance API
          const organizationComptes: CompteData[] = getMockOrganizationData();
          setComptesOrganismes(organizationComptes);

          // Calculate statistics
          const allComptes = [...patientComptes, ...organizationComptes];
          const stats: AccountStats = {
            totalAccounts: allComptes.length,
            totalDue: allComptes.reduce((sum, item) => sum + item.montantDu, 0),
            totalCollected: allComptes.reduce((sum, item) => sum + item.montantEncaisse, 0),
            totalRemaining: allComptes.reduce((sum, item) => sum + item.resteARegler, 0),
            overdueAccounts: patientComptes.filter(c => c.account_status === 'overdue').length,
            paidAccounts: patientComptes.filter(c => c.account_status === 'paid').length,
            currentAccounts: patientComptes.filter(c => c.account_status === 'current').length,
            patientAccounts: patientComptes.length,
            organizationAccounts: organizationComptes.length
          };

          setAccountStats(stats);
          console.log('✅ Account balances loaded:', patientComptes.length, 'Statistics:', stats);
        } else {
          // Fallback to mock data when Django is not connected
          setComptesPatients(getMockPatientData());
          setComptesOrganismes(getMockOrganizationData());
          console.warn('⚠️ Django not connected, using mock data');
        }
      } catch (error) {
        console.error('❌ Error loading account balances:', error);
        setError('Failed to load account balances from backend');
        setComptesPatients(getMockPatientData());
        setComptesOrganismes(getMockOrganizationData());
      } finally {
        setLoading(false);
      }
    };

    loadAccountData();
  }, [refreshTrigger]); // Remove accountBalances from dependencies to prevent infinite loop

  // Mock data functions
  const getMockPatientData = (): CompteData[] => [
    {
      id: '1',
      nomComplet: 'Mr OUARHOU ANIS',
      montantDu: 2000.00,
      montantEncaisse: 1500.00,
      resteARegler: 500.00,
      type: 'patient',
      account_status: 'current'
    },
    {
      id: '2',
      nomComplet: 'OUARHOU TEST',
      montantDu: 3000.00,
      montantEncaisse: 0.00,
      resteARegler: 3000.00,
      type: 'patient',
      account_status: 'overdue'
    },
    {
      id: '3',
      nomComplet: 'CHOUMITA IDRISS',
      montantDu: 1200.00,
      montantEncaisse: 0.00,
      resteARegler: 1200.00,
      type: 'patient',
      account_status: 'overdue'
    },
    {
      id: '4',
      nomComplet: 'TEST DEMO',
      montantDu: 3560.00,
      montantEncaisse: 200.00,
      resteARegler: 3360.00,
      type: 'patient',
      account_status: 'current'
    },
    {
      id: '5',
      nomComplet: 'FALAHI MOHAMED',
      montantDu: 69200.00,
      montantEncaisse: 0.00,
      resteARegler: 69200.00,
      type: 'patient',
      account_status: 'overdue'
    }
  ];

  const getMockOrganizationData = (): CompteData[] => [
    {
      id: '101',
      nomComplet: 'CNSS',
      montantDu: 45000.00,
      montantEncaisse: 35000.00,
      resteARegler: 10000.00,
      type: 'organization',
      account_status: 'current'
    },
    {
      id: '102',
      nomComplet: 'CNOPS',
      montantDu: 32000.00,
      montantEncaisse: 28000.00,
      resteARegler: 4000.00,
      type: 'organization',
      account_status: 'current'
    },
    {
      id: '103',
      nomComplet: 'RAMED',
      montantDu: 15000.00,
      montantEncaisse: 12000.00,
      resteARegler: 3000.00,
      type: 'organization',
      account_status: 'current'
    },
    {
      id: '104',
      nomComplet: 'Mutuelle Générale',
      montantDu: 25000.00,
      montantEncaisse: 20000.00,
      resteARegler: 5000.00,
      type: 'organization',
      account_status: 'current'
    }
  ];

  // Refresh function
  const refreshAccounts = () => {
    setRefreshTrigger(prev => prev + 1);
    refreshAll();
    notifications.show({
      title: 'Actualisation',
      message: 'Données des comptes actualisées',
      color: 'blue',
    });
  };
// --------------------------
// Sélectionner les données selon le type de filtre
const comptesData = filterType === 'Patient' ? comptesPatients : comptesOrganismes;

const [sortStatus, setSortStatus] = useState<DataTableSortStatus<CompteData>>({
  columnAccessor: 'nomComplet',
  direction: 'asc',
});

const [pageSize, setPageSize] = useState(PAGE_SIZES[1]);
const [page, setPage] = useState(1);
  // Filtrer les données selon les critères
const filteredComptes = useMemo(() => {
  return comptesData.filter(compte => {
    const matchesSearch = compte.nomComplet.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDebiteurs = !afficherComptesDebiteurs || compte.resteARegler > 0;
    return matchesSearch && matchesDebiteurs;
  });
}, [comptesData, searchTerm, afficherComptesDebiteurs]);

const sortedComptes = useMemo(() => {
  const sorted = sortBy(filteredComptes, sortStatus.columnAccessor);
  return sortStatus.direction === 'desc' ? sorted.reverse() : sorted;
}, [filteredComptes, sortStatus]);

const paginatedComptes = useMemo(() => {
  const start = (page - 1) * pageSize;
  return sortedComptes.slice(start, start + pageSize);
}, [sortedComptes, page, pageSize]);
useEffect(() => {
  setPage(1);
}, [searchTerm, afficherComptesDebiteurs, pageSize]);
// ---------------------------
  
  // Calcul de la pagination
  const totalPages = Math.ceil(filteredComptes.length / itemsPerPage);

  // const currentComptes = filteredComptes.slice(startIndex, startIndex + itemsPerPage);

  // Calcul du total de la balance
  const totalBalance = filteredComptes.reduce((sum, item) => sum + item.resteARegler, 0);

  const [, setRecords] = useState(sortBy(comptesData, 'nomComplet')&& comptesData.slice(0, pageSize));

  useEffect(() => {
    const data = sortBy(comptesData, sortStatus.columnAccessor) as CompteData[];
    setRecords(sortStatus.direction === 'desc' ? data.reverse() : data);
  }, [sortStatus]);
  useEffect(() => {
    setPage(1);
  }, [pageSize]);
  useEffect(() => {
    const from = (page - 1) * pageSize;
    const to = from + pageSize;
    setRecords(comptesData.slice(from, to));
  }, [page, pageSize])

  // const [expandedRecordIds, ] = useState<Set<number>>(new Set());

  // const toggleRowExpansion = (recordId: number) => {
  //   setExpandedRecordIds((prev) => {
  //     const newSet = new Set(prev);
  //     if (newSet.has(recordId)) {
  //       newSet.delete(recordId);
  //     } else {
  //       newSet.add(recordId);
  //     }
  //     return newSet;
  //   });
  // };

  // const isRowExpanded = (recordId: number) => expandedRecordIds.has(recordId);
  const [expandedIds, setExpandedIds] = useState<string[]>([]);

  const toggleRow = (id: string) => {
    setExpandedIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };
  return (
    <Box className="h-screen flex flex-col bg-gray-50 w-full" >
      {/* Header */}
       <Card
             shadow="none"
             padding="md"
             radius={0}
             // className="bg-white border-b border-gray-200"
              bg="blue.6" px="md" py="sm" w={'100%'}
           >
             <Group justify="space-between" align="center">
               <Group align="center" gap="sm">
                 <Icon path={mdiAccountCash} size={1} color={'white'}/>
                 <Text size="lg" fw={600} className="text-gray-800" c="white">
                   État du Compte général
                 </Text>
               </Group>
     
               <Button
                 size="sm"
                 variant="subtle"
                 color="white"
                 leftSection={<IconPlus size={16} />}
                 className="bg-blue-500 hover:bg-blue-600"
                 // onClick={() => setIsNouvelEncaissementModalOpen(true)}
                 component='a'
                 href='/patient/financial-statement/NouvelEncaissement'
               >
                 Nouvel encaissement
               </Button>
             </Group>
        </Card>
      {/* Account Statistics Dashboard */}
      {djangoStatus === 'connected' && (
        <Card shadow="none" padding="md" radius={0} className="bg-blue-50 border-b border-gray-200">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="lg">Account Balance Overview</Text>
            <Group gap="xs">
              <Button
                variant="light"
                size="xs"
                onClick={refreshAccounts}
                loading={loading}
                leftSection={<Icon path={mdiReload} size={0.6} />}
              >
                Refresh
              </Button>
              <Text size="xs" c={djangoStatus === 'connected' ? 'green' : 'red'}>
                {djangoStatus === 'connected' ? 'Django Connected' : 'Django Disconnected'}
              </Text>
            </Group>
          </Group>

          <Group gap="md">
            {/* Total Accounts */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Accounts</Text>
              <Text fw={600} size="lg">{accountStats.totalAccounts}</Text>
            </Card>

            {/* Total Due */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Due</Text>
              <Text fw={600} size="lg" c="red">{accountStats.totalDue.toFixed(2)} DH</Text>
            </Card>

            {/* Total Collected */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Collected</Text>
              <Text fw={600} size="lg" c="green">{accountStats.totalCollected.toFixed(2)} DH</Text>
            </Card>

            {/* Total Remaining */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Total Remaining</Text>
              <Text fw={600} size="lg" c="orange">{accountStats.totalRemaining.toFixed(2)} DH</Text>
            </Card>

            {/* Account Types */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed" mb="xs">Account Types</Text>
              <Group gap="xs">
                <Badge size="xs" color="blue">{accountStats.patientAccounts} Patients</Badge>
                <Badge size="xs" color="purple">{accountStats.organizationAccounts} Orgs</Badge>
              </Group>
            </Card>

            {/* Account Status */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed" mb="xs">Account Status</Text>
              <Group gap="xs">
                <Badge size="xs" color="green">{accountStats.paidAccounts} Paid</Badge>
                <Badge size="xs" color="orange">{accountStats.currentAccounts} Current</Badge>
                <Badge size="xs" color="red">{accountStats.overdueAccounts} Overdue</Badge>
              </Group>
            </Card>
          </Group>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card shadow="none" padding="md" radius={0} className="bg-white">
          <div className="text-center">
            <Loader size="md" />
            <Text size="sm" c="dimmed" mt="xs">Loading account balances...</Text>
          </div>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red" className="m-4">
          {error}
        </Alert>
      )}

      {/* Filtres */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group gap="md" align="end" justify="space-between" w={"100%"}>
          {/* Recherche */}
          <div className='w-[33%] '>
          <TextInput
            placeholder="Rechercher"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftSection={<IconSearch size={16} />}
           w={"100%"}
            size="sm"
          />
</div>
          {/* Radio buttons pour Patient/Organisme */}
        <div className='w-[40%] '>
      

          {/* Toggle pour afficher les comptes débiteurs */}
          <Group gap="xs" align="center">
                <Radio.Group
            value={filterType}
            onChange={setFilterType}
            size="sm"
          >
            <Group gap="md">
              <Radio value="Patient" label="Patient" size="md" />
              <Radio value="Organisme/Tiers payant" label="Organisme/Tiers payant" size="md"/>
            </Group>
          </Radio.Group>
            <Switch
              checked={afficherComptesDebiteurs}
              onChange={(event) => setAfficherComptesDebiteurs(event.currentTarget.checked)}
              size="sm"
              color="blue"
            />
            <Text size="md" className="text-gray-700">
              Afficher les comptes débiteurs
            </Text>
          </Group>
</div>
          {/* Balance */}
         <div className='w-[15%] '>
          <Group gap="xs" align="center" className="ml-auto">
            <Text size="xl" fw={700} className="text-gray-700">
              Balance :
            </Text>
            <Text size="sm" fw={700} c={"green"}>
              {totalBalance.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </Text>
          </Group>
          </div>
        </Group>
      </Card>
    
      {/* Footer avec pagination */}
     
     
       <div className="m-4  overflow-hidden">
     <DataTable
      withTableBorder
      withColumnBorders
      records={paginatedComptes}
      columns={[
        {
        accessor: 'nomComplet',
        title: (
          <Group gap="xs" justify="space-between">
                  <Text  size="md" fw={700}  ml='md'>
                  {filterType === 'Patient' ? 'Nom complet' : 'Organisme'}
                  </Text>
                </Group>
        ),
        render: (record) => <Text size="sm">{record.nomComplet}</Text>,
        width: '80%',
        sortable: true,
      },
        { accessor: 'montantDu',title: (
          <Group gap="xs" justify="space-between">
                  <Text  size="md" fw={700}  ml='md'>
                  Montant dû
                  
                  </Text>
                </Group>
        ),
        render: (record) => <Text size="sm">{record.montantDu}</Text>,
         width: '20%' },
        { accessor: 'montantEncaisse',title: (
          <Group gap="xs" justify="space-between">
                  <Text  size="md" fw={700}  ml='md'>
                  Montant encaissé
                  
                  </Text>
                </Group>
        ),
         render: (record) => <Text size="sm">{record.montantEncaisse}</Text>,
         width: 180, sortable: true },
        { accessor: 'resteARegler', title: (
          <Group gap="xs" justify="space-between">
                   <Text  size="md" fw={700}  ml='md'>
                  Reste à régler
                
                  </Text>
                </Group>
        ),
        render: (record) => <Text size="sm" c={record.resteARegler > 0 ? "red" : "gray"}>
                      {record.resteARegler.toFixed(2)}
                    </Text>,
        textAlign: 'right', sortable: true },
       
         {
        accessor: 'actions',
        title: "",
        textAlign: 'right',
        render: (record) => (
         <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            mr={"12px"}
            // onClick={() => toggleRowExpansion(record.id)}
            onClick={() => toggleRow(record.id)}
          >
            {/* <Icon path={mdiDetails} size={1} color={isRowExpanded(record.id) ? '#3799CE' : '#ADB5BD'} /> */}
              <Icon path={mdiDetails} 
              // color={expandedRecordIds.has(record.id) ? '#3799CE' : '#ADB5BD'} 
              color={expandedIds.includes(record.id) ? '#3799CE' : '#ADB5BD'}
              />
          </ActionIcon>
        ),
      },
      ]}
    noRecordsText={paginatedComptes.length === 0 ? "Aucun élément trouvé" : ""}
    emptyState={
    <div>
      <style>
        {`.mantine-datatable-empty-state-icon { display: none !important; }`}
      </style>
     
    </div>
  }
     sortStatus={sortStatus}
      onSortStatusChange={setSortStatus}
      totalRecords={filteredComptes.length}
      recordsPerPage={pageSize}
      onRecordsPerPageChange={setPageSize}
      recordsPerPageOptions={[5, 10, 25, 50, 100]}
      page={page}
      onPageChange={setPage}
      paginationActiveBackgroundColor="grape"
       recordsPerPageLabel="Lignes par Page" 
      paginationText={({ from, to, totalRecords }) => (
    <>
     <Card
        shadow="none"
        padding="sm"
        radius={0}
        bg={'#F9FAFB'}
      >
    <Group justify="space-between" align="center">
      <Group gap="xs" align="center" wrap="nowrap">
        {/* Boutons de navigation */}
          <Group gap="sm" align="center">
          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            disabled={page === 1}
            onClick={() => setPage(1)}
          >
            <IconChevronsLeft size={14} />
          </ActionIcon>
          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            disabled={page === 1}
            onClick={() => setPage(page - 1)}
          >
            <IconChevronLeft size={14} />
          </ActionIcon>
          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            disabled={page === totalPages}
            onClick={() => setPage(page + 1)}
          >
            <IconChevronRight size={14} />
          </ActionIcon>
          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            disabled={page === totalPages}
            onClick={() => setPage(totalPages)}
          >
            <IconChevronsRight size={14} />
          </ActionIcon>
        </Group>
      </Group>

      <Group gap="xs" align="center" ml="auto">
        <span>Affichage</span>
        <span className="text-[#3799CE]">{` ${from} - ${to} / ${totalRecords}`}</span>
       
        <Select
          value={page.toString()}
          onChange={(value) => setPage(Number(value) || 1)}
          data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
          size="xs"
          className="w-16"
        />
      </Group>
      </Group>
      </Card>
    </>
      )}
    //    rowExpansion={{
    //   content: ({  }) => (
    //     <Stack className={classes.details} p="xs" gap={6} my={16}  mx={12}>
    //       <Tablerecords/>
    //     </Stack>
    //   ),
    // }}
   rowExpansion={{
        expanded: {
          recordIds: expandedIds,
          onRecordIdsChange: setExpandedIds,
        },
        content: ({ record }) => (
          <Stack className={classes.details} p="xs" gap={6} my={16} mx={12}>
            <Tablerecords record={record} />
          </Stack>
        ),
      }}
    />
   </div>
    </Box>
  );
};

export default EtatDuComptGeneral;
