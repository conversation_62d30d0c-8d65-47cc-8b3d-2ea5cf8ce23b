"use client";
import React, { useState } from 'react';
import { Select, Group, Text, Avatar, Loader } from '@mantine/core';
import { useLanguage } from '~/contexts/LanguageContext';
import { Locales, supportedLocales } from '~/i18n/settings';
import { notifications } from '@mantine/notifications';

// Language options with flags and labels
const languageOptions = [
  {
    value: 'fr' as Locales,
    label: 'Français',
    flag: '/flags/fr.svg',
    description: 'French'
  },
  {
    value: 'en' as Locales,
    label: 'English',
    flag: '/flags/us.svg',
    description: 'English'
  },
  {
    value: 'ar' as Locales,
    label: 'العربية',
    flag: '/flags/ma.svg',
    description: 'Arabic'
  }
];

interface LanguageSelectorProps {
  label?: string;
  description?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  label = "Language", 
  description = "Select your preferred language" 
}) => {
  const { language, saveLanguage, isLoading } = useLanguage();
  const [saving, setSaving] = useState(false);

  const handleLanguageChange = async (value: string | null) => {
    if (!value || !supportedLocales.includes(value as Locales)) {
      return;
    }

    const newLanguage = value as Locales;
    
    if (newLanguage === language) {
      return; // No change needed
    }

    try {
      setSaving(true);
      await saveLanguage(newLanguage);
      
      notifications.show({
        title: 'Language Updated',
        message: `Language changed to ${languageOptions.find(opt => opt.value === newLanguage)?.label}`,
        color: 'green',
      });
    } catch (error) {
      console.error('Error saving language:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save language preference. Please try again.',
        color: 'red',
      });
    } finally {
      setSaving(false);
    }
  };

  // Custom render function for select items
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderSelectOption = ({ option }: { option: any }) => {
    const langOption = languageOptions.find(lang => lang.value === option.value);
    if (!langOption) return option.label;

    return (
      <Group gap="sm">
        <Avatar src={langOption.flag} size={20} radius="sm" />
        <div>
          <Text size="sm">{langOption.label}</Text>
          <Text size="xs" opacity={0.6}>{langOption.description}</Text>
        </div>
      </Group>
    );
  };

  // Custom render function for selected value
  // const renderSelectValue = ({ option }: { option: any }) => {
  //   const langOption = languageOptions.find(lang => lang.value === option?.value);
  //   if (!langOption) return option?.label || '';

  //   return (
  //     <Group gap="sm">
  //       <Avatar src={langOption.flag} size={20} radius="sm" />
  //       <Text size="sm">{langOption.label}</Text>
  //     </Group>
  //   );
  // };

  const selectData = languageOptions.map(lang => ({
    value: lang.value,
    label: lang.label,
  }));

  return (
    <div>
      <Select
        label={label}
        description={description}
        placeholder="Select language"
        data={selectData}
        value={language}
        onChange={handleLanguageChange}
        disabled={isLoading || saving}
        rightSection={saving ? <Loader size={16} /> : undefined}
        renderOption={renderSelectOption}
        comboboxProps={{
          transitionProps: { transition: 'pop', duration: 200 },
        }}
        leftSection={
          language ? (
            <Avatar 
              src={languageOptions.find(lang => lang.value === language)?.flag} 
              size={20} 
              radius="sm" 
            />
          ) : undefined
        }
        styles={{
          input: {
            paddingLeft: language ? '3rem' : undefined,
          },
        }}
      />
      
      {saving && (
        <Text size="xs" c="dimmed" mt="xs">
          Applying language changes...
        </Text>
      )}
    </div>
  );
};

export default LanguageSelector;
