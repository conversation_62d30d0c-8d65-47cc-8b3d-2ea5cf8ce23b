'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { CalendarEvent } from '../CalendarPatient';

// Configure moment localizer
const localizer = momentLocalizer(moment);

// Configure French locale
moment.locale('fr', {
  months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),
  monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),
  weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),
  weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),
  weekdaysMin: 'Di_Lu_Ma_Me_Je_Ve_Sa'.split('_'),
});

interface EnhancedAgendaViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick: (date: Date, hour: number, minute?: number, roomId?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
}

// Custom agenda event component
const CustomAgendaEvent = ({ event }: { event: CalendarEvent }) => {
  return (
    <div className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded">
      <div 
        className="w-3 h-3 rounded-full flex-shrink-0"
        style={{ backgroundColor: event.color || '#3b82f6' }}
      ></div>
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm text-gray-900 truncate">
          {event.title}
        </div>
        <div className="text-xs text-gray-500">
          {event.roomId === 'room-a' ? 'Salle A' : 'Salle B'} • {event.duration}min
        </div>
        {event.desc && (
          <div className="text-xs text-gray-400 truncate mt-1">
            {event.desc}
          </div>
        )}
      </div>
      <div className="text-right flex-shrink-0">
        <div className="text-sm font-medium text-gray-900">
          {moment(event.start).format('HH:mm')}
        </div>
        <div className="text-xs text-gray-500">
          {moment(event.start).format('DD/MM')}
        </div>
      </div>
    </div>
  );
};

const EnhancedAgendaView: React.FC<EnhancedAgendaViewProps> = ({
  currentDate,
  events,
  onTimeSlotClick,
  onEventClick,
  onDateChange,
  onNavigate
}) => {
  const [selectedRoom, setSelectedRoom] = useState<string>('all');
  const [dateRange, setDateRange] = useState<'week' | 'month' | 'custom'>('week');
  const [sortBy, setSortBy] = useState<'date' | 'room' | 'patient'>('date');

  // Room configuration
  const rooms = useMemo(() => [
    { id: 'room-a', name: 'Salle A', color: '#3b82f6' },
    { id: 'room-b', name: 'Salle B', color: '#10b981' },
  ], []);

  // Calculate date range based on selection
  const { startDate, endDate } = useMemo(() => {
    const current = moment(currentDate);
    
    switch (dateRange) {
      case 'week':
        return {
          startDate: current.clone().startOf('week').toDate(),
          endDate: current.clone().endOf('week').toDate()
        };
      case 'month':
        return {
          startDate: current.clone().startOf('month').toDate(),
          endDate: current.clone().endOf('month').toDate()
        };
      default:
        return {
          startDate: current.clone().startOf('week').toDate(),
          endDate: current.clone().endOf('week').toDate()
        };
    }
  }, [currentDate, dateRange]);

  // Filter and sort events
  const filteredEvents = useMemo(() => {
    let filtered = events.filter(event => {
      const eventMoment = moment(event.start);
      return eventMoment.isBetween(moment(startDate), moment(endDate), 'day', '[]');
    });

    // Filter by room if not 'all'
    if (selectedRoom !== 'all') {
      filtered = filtered.filter(event => event.roomId === selectedRoom);
    }

    // Sort events
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return moment(a.start).valueOf() - moment(b.start).valueOf();
        case 'room':
          const roomA = a.roomId || '';
          const roomB = b.roomId || '';
          if (roomA !== roomB) {
            return roomA.localeCompare(roomB);
          }
          return moment(a.start).valueOf() - moment(b.start).valueOf();
        case 'patient':
          const titleA = a.title || '';
          const titleB = b.title || '';
          if (titleA !== titleB) {
            return titleA.localeCompare(titleB);
          }
          return moment(a.start).valueOf() - moment(b.start).valueOf();
        default:
          return moment(a.start).valueOf() - moment(b.start).valueOf();
      }
    });

    return filtered;
  }, [events, startDate, endDate, selectedRoom, sortBy]);

  // Convert events to react-big-calendar format
  const calendarEvents = useMemo(() => {
    return filteredEvents.map(event => ({
      ...event,
      start: event.start,
      end: event.end || new Date(event.start.getTime() + (event.duration || 30) * 60000),
      resource: event.roomId,
    }));
  }, [filteredEvents]);

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    onEventClick(event);
  }, [onEventClick]);

  // Handle navigation
  const handleNavigate = useCallback((newDate: Date) => {
    if (onDateChange) {
      onDateChange(newDate);
    }
  }, [onDateChange]);

  // Group events by date for better display
  const eventsByDate = useMemo(() => {
    const grouped: { [key: string]: CalendarEvent[] } = {};
    
    filteredEvents.forEach(event => {
      const dateKey = moment(event.start).format('YYYY-MM-DD');
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });

    return Object.entries(grouped).map(([date, dayEvents]) => ({
      date: moment(date).toDate(),
      events: dayEvents
    })).sort((a, b) => moment(a.date).valueOf() - moment(b.date).valueOf());
  }, [filteredEvents]);

  // Custom formats
  const formats = {
    agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('DD MMM')} - ${moment(end).format('DD MMM YYYY')}`;
    },
    agendaDateFormat: 'dddd DD MMMM',
    agendaTimeFormat: 'HH:mm',
    agendaTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
    },
  };

  return (
    <div className="enhanced-agenda-view h-full flex flex-col">
      {/* Header Controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-800">
            Agenda - {moment(startDate).format('DD MMM')} au {moment(endDate).format('DD MMM YYYY')}
          </h3>
          
          {/* Date Range Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Période:</span>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as 'week' | 'month' | 'custom')}
              className="px-2 py-1 text-xs border rounded"
            >
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>

          {/* Room Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Salle:</span>
            <div className="flex gap-1">
              <button
                onClick={() => setSelectedRoom('all')}
                className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                  selectedRoom === 'all'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Toutes
              </button>
              {rooms.map(room => (
                <button
                  key={room.id}
                  onClick={() => setSelectedRoom(room.id)}
                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                    selectedRoom === room.id
                      ? 'text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  style={{
                    backgroundColor: selectedRoom === room.id ? room.color : undefined
                  }}
                >
                  {room.name}
                </button>
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Trier par:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'room' | 'patient')}
              className="px-2 py-1 text-xs border rounded"
            >
              <option value="date">Date</option>
              <option value="room">Salle</option>
              <option value="patient">Patient</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="text-sm text-gray-600">
          <span className="font-medium">{filteredEvents.length}</span> rendez-vous
        </div>
      </div>

      {/* Calendar - Agenda View */}
      <div className="flex-1 p-4">
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          defaultView="agenda"
          view="agenda"
          views={['agenda']}
          date={currentDate}
          onNavigate={handleNavigate}
          formats={formats}
          onSelectEvent={handleSelectEvent}
          length={30} // Show 30 days in agenda
          components={{
            agenda: {
              event: CustomAgendaEvent,
            },
          }}
          messages={{
            allDay: 'Toute la journée',
            previous: 'Précédent',
            next: 'Suivant',
            today: "Aujourd'hui",
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour',
            agenda: 'Agenda',
            date: 'Date',
            time: 'Heure',
            event: 'Événement',
            noEventsInRange: 'Aucun rendez-vous pour cette période.',
          }}
          className="rbc-calendar-enhanced"
        />
      </div>

      {/* Debug Info */}
      <div className="p-2 bg-blue-50 border-t text-xs text-blue-700">
        <strong>🔍 Enhanced Agenda View:</strong><br/>
        Période: {moment(startDate).format('DD/MM')} - {moment(endDate).format('DD/MM/YYYY')} | 
        Total events: {events.length} | Filtered: {filteredEvents.length} | 
        Room: {selectedRoom} | Sort: {sortBy}
      </div>
    </div>
  );
};

export default EnhancedAgendaView;
