'use client';

import React, { useState, useEffect } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  Box,
  Loader,
  TextInput,
  Select,
  Menu,
  Pa<PERSON>ation,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiFileMultiple,
  mdiMagnify,
  mdiChevronUp,
  mdiChevronDown,
  mdiEye,
  mdiPencil,
  mdiDelete,
  mdiPrinter,
  mdiDotsVertical,
  mdiReload,
  mdiAlertCircle,
  mdiPlus
} from '@mdi/js';
import { notifications } from '@mantine/notifications';
import { useMedicalReport } from '@/hooks/useMedicalReport';
import patientService from '@/services/patientService';

// Types et interfaces
interface Patient {
  id: string;
  full_name: string;
  gender: string;
  age: string;
}

interface ExamTemplate {
  id: string;
  title: string;
  color?: string;
}

interface ExamReport {
  id: string;
  exam_date: string;
  patient?: Patient;
  patient_id?: string;
  patient_name?: string;
  template_id: string;
  template_title: string;
  indication: string;
  findings?: string;
  diagnosis?: string;
  recommendations?: string;
  doctor_id?: string;
  doctor_name?: string;
  status?: 'draft' | 'completed' | 'reviewed' | 'archived';
  visit_id?: string;
  is_model?: boolean;
  created_at: string;
  updated_at?: string;
}

// Interface for medical report statistics
interface MedicalReportStats {
  totalReports: number;
  draftReports: number;
  completedReports: number;
  reviewedReports: number;
  archivedReports: number;
  todayReports: number;
  thisWeekReports: number;
  thisMonthReports: number;
  templateBreakdown: Record<string, number>;
}

interface SearchQuery {
  exam_date: string;
  patient_name: string;
  template_id: string;
  indication: string;
}

interface SortOrder {
  field: string;
  direction: 'asc' | 'desc';
}

interface PaginationQuery {
  page: number;
  limit: number;
}

interface ComptesRendusProps {
  examList?: ExamReport[];
  templates?: ExamTemplate[];
  visitContext?: boolean;
  loading?: boolean;
  onViewExam?: (exam: ExamReport) => void;
  onEditExam?: (exam: ExamReport) => void;
  onDeleteExam?: (exam: ExamReport) => void;
  onPrintExam?: (exam: ExamReport) => void;
  onSearch?: (query: SearchQuery) => void;
  onSort?: (order: SortOrder) => void;
  onPaginate?: (pagination: PaginationQuery) => void;
}

export const Comptes_rendus: React.FC<ComptesRendusProps> = ({
  examList = [],
  templates = [],
  visitContext = false,
  loading = false,
  onViewExam,
  onEditExam,
  onDeleteExam,
  onPrintExam,
  onSearch,
  onSort,
  onPaginate
}) => {
  // États locaux
  const [searchQuery, setSearchQuery] = useState<SearchQuery>({
    exam_date: '',
    patient_name: '',
    template_id: '',
    indication: ''
  });

  const [sortOrder, setSortOrder] = useState<SortOrder>({
    field: 'exam_date',
    direction: 'desc'
  });

  const [pagination, setPagination] = useState<PaginationQuery>({
    page: 1,
    limit: 10
  });

  // Backend State
  const [backendExamList, setBackendExamList] = useState<ExamReport[]>([]);
  const [backendTemplates, setBackendTemplates] = useState<ExamTemplate[]>([]);
  const [backendLoading, setBackendLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [medicalReportStats, setMedicalReportStats] = useState<MedicalReportStats>({
    totalReports: 0,
    draftReports: 0,
    completedReports: 0,
    reviewedReports: 0,
    archivedReports: 0,
    todayReports: 0,
    thisWeekReports: 0,
    thisMonthReports: 0,
    templateBreakdown: {}
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Use medical report hook for backend integration
  const {
    examReports,
    templates: hookTemplates,
    refreshAll,
    createExamReport,
    updateExamReport,
    deleteExamReport
  } = useMedicalReport({
    autoFetch: true,
    reportTypes: ['reports']
  });

  // Load medical reports from backend
  useEffect(() => {
    const loadMedicalReportData = async () => {
      try {
        setBackendLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          console.log('📋 Loading medical reports from backend...');

          // Transform exam reports to local format
          const transformedReports: ExamReport[] = examReports.map(report => ({
            id: report.id,
            exam_date: new Date(report.exam_date).toLocaleDateString('fr-FR'),
            patient: report.patient_name ? {
              id: report.patient_id,
              full_name: report.patient_name,
              gender: 'M', // Would come from patient data
              age: '35 ans' // Would be calculated from patient data
            } : undefined,
            patient_id: report.patient_id,
            patient_name: report.patient_name,
            template_id: report.template_id,
            template_title: report.template_title,
            indication: report.indication,
            findings: report.findings,
            diagnosis: report.diagnosis,
            recommendations: report.recommendations,
            doctor_id: report.doctor_id,
            doctor_name: report.doctor_name,
            status: report.status,
            created_at: report.created_at,
            updated_at: report.updated_at
          }));

          setBackendExamList(transformedReports);

          // Transform templates
          const transformedTemplates: ExamTemplate[] = hookTemplates.map(template => ({
            id: template.id,
            title: template.title,
            color: template.color || '#1976d2'
          }));

          setBackendTemplates(transformedTemplates);

          // Calculate statistics
          const stats: MedicalReportStats = {
            totalReports: transformedReports.length,
            draftReports: transformedReports.filter(r => r.status === 'draft').length,
            completedReports: transformedReports.filter(r => r.status === 'completed').length,
            reviewedReports: transformedReports.filter(r => r.status === 'reviewed').length,
            archivedReports: transformedReports.filter(r => r.status === 'archived').length,
            todayReports: transformedReports.filter(r => isToday(r.exam_date)).length,
            thisWeekReports: transformedReports.filter(r => isThisWeek(r.exam_date)).length,
            thisMonthReports: transformedReports.filter(r => isThisMonth(r.exam_date)).length,
            templateBreakdown: transformedReports.reduce((acc, report) => {
              acc[report.template_title] = (acc[report.template_title] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)
          };

          setMedicalReportStats(stats);
          console.log('✅ Medical reports loaded:', transformedReports.length, 'Statistics:', stats);
        } else {
          // Fallback to mock data when Django is not connected
          setBackendExamList(getMockExamList());
          setBackendTemplates(getMockTemplates());
          console.warn('⚠️ Django not connected, using mock data');
        }
      } catch (error) {
        console.error('❌ Error loading medical reports:', error);
        setError('Failed to load medical reports from backend');
        setBackendExamList(getMockExamList());
        setBackendTemplates(getMockTemplates());
      } finally {
        setBackendLoading(false);
      }
    };

    loadMedicalReportData();
  }, [examReports, hookTemplates, refreshTrigger]);

  // Helper functions for date calculations
  const isToday = (dateString: string): boolean => {
    const today = new Date().toLocaleDateString('fr-FR');
    return dateString === today;
  };

  const isThisWeek = (dateString: string): boolean => {
    const date = new Date(dateString.split('/').reverse().join('-'));
    const today = new Date();
    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
    return date >= weekStart;
  };

  const isThisMonth = (dateString: string): boolean => {
    const date = new Date(dateString.split('/').reverse().join('-'));
    const today = new Date();
    return date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
  };

  // Mock data functions
  const getMockTemplates = (): ExamTemplate[] => [
    { id: '1', title: 'Consultation générale', color: '#1976d2' },
    { id: '2', title: 'Contrôle post-opératoire', color: '#388e3c' },
    { id: '3', title: 'Urgence', color: '#d32f2f' },
    { id: '4', title: 'Bilan pré-opératoire', color: '#f57c00' },
    { id: '5', title: 'Radiologie', color: '#7b1fa2' }
  ];

  const getMockExamList = (): ExamReport[] => [
    {
      id: '1',
      exam_date: '16/05/2025',
      patient: {
        id: '1',
        full_name: 'AHMED BENALI',
        gender: 'M',
        age: '35 ans'
      },
      template_id: '1',
      template_title: 'Consultation générale',
      indication: 'Douleur dentaire persistante',
      status: 'completed',
      visit_id: '1',
      is_model: false,
      created_at: '2025-05-16'
    },
    {
      id: '2',
      exam_date: '15/05/2025',
      patient: {
        id: '2',
        full_name: 'FATIMA ZAHRA',
        gender: 'F',
        age: '28 ans'
      },
      template_id: '2',
      template_title: 'Contrôle post-opératoire',
      indication: 'Suivi extraction dent de sagesse',
      visit_id: '2',
      is_model: false,
      created_at: '2025-05-15'
    },
    {
      id: '3',
      exam_date: '14/05/2025',
      patient: {
        id: '3',
        full_name: 'MOHAMED ALAMI',
        gender: 'M',
        age: '45 ans'
      },
      template_id: '3',
      template_title: 'Urgence',
      indication: 'Traumatisme dentaire',
      visit_id: '3',
      is_model: false,
      created_at: '2025-05-14'
    },
    {
      id: '4',
      exam_date: '13/05/2025',
      patient: {
        id: '4',
        full_name: 'AICHA MANSOURI',
        gender: 'F',
        age: '42 ans'
      },
      template_id: '4',
      template_title: 'Bilan pré-opératoire',
      indication: 'Préparation chirurgie implantaire',
      visit_id: '4',
      is_model: false,
      created_at: '2025-05-13'
    }
  ];

  // Refresh function
  const refreshMedicalReports = () => {
    setRefreshTrigger(prev => prev + 1);
    refreshAll();
    notifications.show({
      title: 'Actualisation',
      message: 'Données des comptes rendus actualisées',
      color: 'blue',
    });
  };

  // Use backend data if available, otherwise use props or mock data
  const currentExamList = examList.length > 0 ? examList :
                          backendExamList.length > 0 ? backendExamList :
                          getMockExamList();
  const currentTemplates = templates.length > 0 ? templates :
                           backendTemplates.length > 0 ? backendTemplates :
                           getMockTemplates();

  // Filtrage et tri des examens
  const filteredExams = currentExamList.filter(exam => {
    const matchesDate = !searchQuery.exam_date || exam.exam_date.includes(searchQuery.exam_date);
    const matchesPatient = !searchQuery.patient_name ||
      (exam.patient && exam.patient.full_name.toLowerCase().includes(searchQuery.patient_name.toLowerCase()));
    const matchesTemplate = !searchQuery.template_id || exam.template_id === searchQuery.template_id;
    const matchesIndication = !searchQuery.indication ||
      exam.indication.toLowerCase().includes(searchQuery.indication.toLowerCase());

    return matchesDate && matchesPatient && matchesTemplate && matchesIndication;
  });

  const sortedExams = [...filteredExams].sort((a, b) => {
    const aValue = a[sortOrder.field as keyof ExamReport] as string;
    const bValue = b[sortOrder.field as keyof ExamReport] as string;

    if (sortOrder.direction === 'asc') {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  const paginatedExams = sortedExams.slice(
    (pagination.page - 1) * pagination.limit,
    pagination.page * pagination.limit
  );

  // Gestionnaires d'événements
  const handleSearchChange = (field: keyof SearchQuery, value: string) => {
    const newQuery = { ...searchQuery, [field]: value };
    setSearchQuery(newQuery);
    onSearch?.(newQuery);
  };

  const handleSort = (field: string) => {
    const newDirection: 'asc' | 'desc' =
      sortOrder.field === field && sortOrder.direction === 'asc' ? 'desc' : 'asc';
    const newOrder = { field, direction: newDirection };
    setSortOrder(newOrder);
    onSort?.(newOrder);
  };

  const handlePaginate = (page: number) => {
    const newPagination = { ...pagination, page };
    setPagination(newPagination);
    onPaginate?.(newPagination);
  };

  const handleLimitChange = (limit: string | null) => {
    if (limit) {
      const newPagination = { page: 1, limit: parseInt(limit) };
      setPagination(newPagination);
      onPaginate?.(newPagination);
    }
  };

  const handleViewExam = (exam: ExamReport) => {
    console.log('Voir examen:', exam);
    onViewExam?.(exam);
  };

  const handleEditExam = async (exam: ExamReport) => {
    console.log('Éditer examen:', exam);

    // Call the prop callback if provided
    if (onEditExam) {
      onEditExam(exam);
      return;
    }

    // Default behavior: navigate to edit page or open edit modal
    // For now, we'll show a notification with the exam details
    notifications.show({
      title: 'Édition',
      message: `Édition de l'examen du ${exam.exam_date} pour ${exam.patient_name}. Fonctionnalité d'édition disponible via l'API backend.`,
      color: 'blue'
    });

    // TODO: Implement edit modal or navigation to edit page
    // Example: router.push(`/medical-reports/edit/${exam.id}`);
    // Or: setEditingExam(exam); setShowEditModal(true);
  };

  const handleDeleteExam = async (exam: ExamReport) => {
    console.log('Supprimer examen:', exam);

    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'examen du ${exam.exam_date} ?`)) {
      return;
    }

    // Call the prop callback if provided
    if (onDeleteExam) {
      onDeleteExam(exam);
      return;
    }

    // Default behavior: delete via backend
    try {
      // Delete via the hook
      await deleteExamReport(exam.id);

      // Also remove from local state
      setBackendExamList(prev => prev.filter(e => e.id !== exam.id));

      notifications.show({
        title: 'Suppression',
        message: `Examen du ${exam.exam_date} supprimé avec succès`,
        color: 'green'
      });

      // Refresh data
      refreshAll();
    } catch (error) {
      console.error('❌ Error deleting exam:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la suppression de l\'examen',
        color: 'red'
      });
    }
  };

  const handlePrintExam = (exam: ExamReport) => {
    console.log('Imprimer examen:', exam);
    onPrintExam?.(exam);
  };

  const getSortIcon = (field: string) => {
    if (sortOrder.field !== field) return null;
    return sortOrder.direction === 'asc' ? mdiChevronUp : mdiChevronDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          <Group gap="md">
            <Icon path={mdiFileMultiple} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Comptes rendus</Text>
          </Group>
          <Group gap="xs">
            <Button
              variant="light"
              size="sm"
              onClick={refreshMedicalReports}
              loading={backendLoading}
              leftSection={<Icon path={mdiReload} size={0.8} />}
            >
              Actualiser
            </Button>
            <Button
              variant="filled"
              size="sm"
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={async () => {
                try {
                  await createExamReport({
                    exam_date: new Date().toISOString(),
                    patient_id: '',
                    patient_name: '',
                    template_id: '',
                    template_title: 'Nouveau rapport',
                    indication: '',
                    findings: '',
                    diagnosis: '',
                    recommendations: '',
                    doctor_id: '',
                    doctor_name: '',
                    status: 'draft'
                  });

                  notifications.show({
                    title: 'Succès',
                    message: 'Nouveau rapport médical créé avec succès',
                    color: 'green'
                  });

                  // Refresh the data
                  refreshAll();
                } catch (error) {
                  console.error('❌ Error creating exam report:', error);
                  notifications.show({
                    title: 'Erreur',
                    message: 'Erreur lors de la création du rapport médical',
                    color: 'red'
                  });
                }
              }}
            >
              Nouveau rapport
            </Button>
          </Group>
        </Group>
      </Paper>

      {/* Medical Report Statistics Dashboard */}
      {djangoStatus === 'connected' && (
        <Card shadow="none" padding="md" radius={0} className="bg-blue-50 border-b border-gray-200">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="lg">Medical Reports Overview</Text>
            <Text size="xs" c={djangoStatus === 'connected' ? 'green' : 'red'}>
              {djangoStatus === 'connected' ? 'Django Connected' : 'Django Disconnected'}
            </Text>
          </Group>

          <Group gap="md">
            {/* Total Reports */}
            <Card padding="xs" withBorder style={{ minWidth: '120px' }}>
              <Text size="xs" c="dimmed">Total Reports</Text>
              <Text fw={600} size="lg">{medicalReportStats.totalReports}</Text>
            </Card>

            {/* Report Status */}
            <Card padding="xs" withBorder style={{ minWidth: '160px' }}>
              <Text size="xs" c="dimmed" mb="xs">Report Status</Text>
              <Group gap="xs">
                <Badge size="xs" color="gray">{medicalReportStats.draftReports} Draft</Badge>
                <Badge size="xs" color="green">{medicalReportStats.completedReports} Complete</Badge>
                <Badge size="xs" color="blue">{medicalReportStats.reviewedReports} Reviewed</Badge>
              </Group>
            </Card>

            {/* Time-based Stats */}
            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">Today&apos;s Reports</Text>
              <Text fw={600} size="lg" c={medicalReportStats.todayReports > 0 ? 'green' : 'gray'}>
                {medicalReportStats.todayReports}
              </Text>
            </Card>

            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">This Week</Text>
              <Text fw={600} size="lg" c="blue">{medicalReportStats.thisWeekReports}</Text>
            </Card>

            <Card padding="xs" withBorder style={{ minWidth: '140px' }}>
              <Text size="xs" c="dimmed">This Month</Text>
              <Text fw={600} size="lg" c="orange">{medicalReportStats.thisMonthReports}</Text>
            </Card>

            {/* Top Template */}
            <Card padding="xs" withBorder style={{ minWidth: '160px' }}>
              <Text size="xs" c="dimmed">Most Used Template</Text>
              <Text fw={600} size="sm">
                {Object.entries(medicalReportStats.templateBreakdown)
                  .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
              </Text>
            </Card>
          </Group>
        </Card>
      )}

      {/* Loading State */}
      {(backendLoading || loading) && (
        <Card shadow="none" padding="md" radius={0} className="bg-white">
          <div className="text-center">
            <Loader size="md" />
            <Text size="sm" c="dimmed" mt="xs">Loading medical reports...</Text>
          </div>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert icon={<Icon path={mdiAlertCircle} size={0.8} />} title="Error" color="red" className="m-4">
          {error}
        </Alert>
      )}

      {/* Tableau principal */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th
                style={{ cursor: 'pointer' }}
                onClick={() => handleSort('exam_date')}
              >
                <Group gap="xs">
                  <Text>Date</Text>
                  {getSortIcon('exam_date') && (
                    <Icon path={getSortIcon('exam_date')!} size={0.6} />
                  )}
                </Group>
              </Table.Th>

              {!visitContext && (
                <Table.Th>
                  <Text>Nom complet</Text>
                </Table.Th>
              )}

              <Table.Th>
                <Text>Type d&apos;examen</Text>
              </Table.Th>

              <Table.Th
                style={{ cursor: 'pointer' }}
                onClick={() => handleSort('indication')}
              >
                <Group gap="xs">
                  <Text>Indication</Text>
                  {getSortIcon('indication') && (
                    <Icon path={getSortIcon('indication')!} size={0.6} />
                  )}
                </Group>
              </Table.Th>

              <Table.Th></Table.Th>
            </Table.Tr>

            {/* Ligne de recherche */}
            <Table.Tr>
              <Table.Td>
                <TextInput
                  placeholder="Rechercher"
                  value={searchQuery.exam_date}
                  onChange={(event) => handleSearchChange('exam_date', event.currentTarget.value)}
                  leftSection={<Icon path={mdiMagnify} size={0.8} />}
                  size="sm"
                />
              </Table.Td>

              {!visitContext && (
                <Table.Td>
                  <TextInput
                    placeholder="Rechercher"
                    value={searchQuery.patient_name}
                    onChange={(event) => handleSearchChange('patient_name', event.currentTarget.value)}
                    leftSection={<Icon path={mdiMagnify} size={0.8} />}
                    size="sm"
                  />
                </Table.Td>
              )}

              <Table.Td>
                <Select
                  placeholder="Type d'examen"
                  data={[
                    { value: '', label: 'Tous les types' },
                    ...currentTemplates.map(template => ({
                      value: template.id,
                      label: template.title
                    }))
                  ]}
                  value={searchQuery.template_id}
                  onChange={(value) => handleSearchChange('template_id', value || '')}
                  size="sm"
                  clearable
                />
              </Table.Td>

              <Table.Td>
                <TextInput
                  placeholder="Rechercher"
                  value={searchQuery.indication}
                  onChange={(event) => handleSearchChange('indication', event.currentTarget.value)}
                  leftSection={<Icon path={mdiMagnify} size={0.8} />}
                  size="sm"
                />
              </Table.Td>

              <Table.Td></Table.Td>
            </Table.Tr>
          </Table.Thead>

          {loading && (
            <Table.Thead>
              <Table.Tr>
                <Table.Td colSpan={visitContext ? 4 : 5}>
                  <Box p="md" style={{ textAlign: 'center' }}>
                    <Loader size="sm" />
                  </Box>
                </Table.Td>
              </Table.Tr>
            </Table.Thead>
          )}

          <Table.Tbody>
            {paginatedExams.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={visitContext ? 4 : 5} style={{ textAlign: 'center' }}>
                  <Text c="dimmed">Aucun élément trouvé.</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              paginatedExams.map((exam) => (
                <Table.Tr key={exam.id}>
                  <Table.Td>{exam.exam_date}</Table.Td>

                  {!visitContext && exam.patient && (
                    <Table.Td>{exam.patient.full_name}</Table.Td>
                  )}

                  <Table.Td>{exam.template_title}</Table.Td>
                  <Table.Td>{exam.indication}</Table.Td>

                  <Table.Td>
                    <Menu shadow="md" width={200}>
                      <Menu.Target>
                        <ActionIcon variant="subtle">
                          <Icon path={mdiDotsVertical} size={0.8} />
                        </ActionIcon>
                      </Menu.Target>

                      <Menu.Dropdown>
                        <Menu.Item
                          leftSection={<Icon path={mdiEye} size={0.8} />}
                          onClick={() => handleViewExam(exam)}
                        >
                          Voir
                        </Menu.Item>

                        <Menu.Item
                          leftSection={<Icon path={mdiPencil} size={0.8} />}
                          onClick={() => handleEditExam(exam)}
                        >
                          Éditer
                        </Menu.Item>

                        <Menu.Item
                          leftSection={<Icon path={mdiPrinter} size={0.8} />}
                          onClick={() => handlePrintExam(exam)}
                        >
                          Imprimer
                        </Menu.Item>

                        <Menu.Divider />

                        <Menu.Item
                          leftSection={<Icon path={mdiDelete} size={0.8} />}
                          onClick={() => handleDeleteExam(exam)}
                          color="red"
                        >
                          Supprimer
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </Box>

      {/* Pagination */}
      <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
        <Group justify="space-between" align="center">
          <Group gap="md">
            <Text size="sm">
              {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, filteredExams.length)} de {filteredExams.length}
            </Text>

            <Group gap="xs">
              <Text size="sm">Lignes par page:</Text>
              <Select
                value={pagination.limit.toString()}
                onChange={handleLimitChange}
                data={['5', '10', '15', '20', '25']}
                size="sm"
                style={{ width: 80 }}
              />
            </Group>
          </Group>

          <Pagination
            value={pagination.page}
            onChange={handlePaginate}
            total={Math.ceil(filteredExams.length / pagination.limit)}
            size="sm"
            withEdges
          />
        </Group>
      </Paper>
    </Paper>
  );
};
