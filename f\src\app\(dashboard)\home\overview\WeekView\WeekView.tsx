"use client";
import { useEffect,useCallback, useState } from "react";
import moment from "moment";
import "moment/locale/fr";
import classes from "@/styles/DndList.module.css";
import { Group, Button,Text ,Tabs,FloatingIndicator,Container,Badge,ThemeIcon, Avatar,Divider,Tooltip, } from '@mantine/core';
import Icon from '@mdi/react';

import {mdiLogout, mdiChevronLeft, mdiChevronRight, mdiFileTree,mdiAccountNetwork,mdiBookmarkOutline,mdiCalendarRange,mdiCalendarPlus,mdiMagnify,mdiCalendarMonth} from '@mdi/js';

import { IconStethoscope, IconTextPlus,IconCalendarClock,IconChevronDown, IconRefresh } from '@tabler/icons-react';
import "./WeekView.css";
import SelectLaSemaine from "./SelectLaSemaine";
// Types
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
}

// Utility functions
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true
  });
};

const getWeekDays = (date: Date): Date[] => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day;
  start.setDate(diff);
  
  const days = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(start);
    day.setDate(start.getDate() + i);
    days.push(day);
  }
  return days;
};

const getWorkWeekDays = (date: Date): Date[] => {
  return getWeekDays(date).slice(1, 6); // Monday to Friday
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const getEventsForDay = (events: CalendarEvent[], day: Date): CalendarEvent[] => {
  return events.filter(event => isSameDay(event.start, day));
};

// Event Component
const EventComponent = ({ 
  event, 
  onDragStart,
  onEdit 
}: { 
  event: CalendarEvent;
  onDragStart?: (event: CalendarEvent) => void;
  onEdit?: (event: CalendarEvent) => void;
}) => (
  <div
    draggable={!!onDragStart}
    onDragStart={() => onDragStart?.(event)}
    onClick={() => onEdit?.(event)}
    className="p-1 mb-1 text-xs rounded cursor-pointer hover:opacity-80 transition-opacity"
    style={{ backgroundColor: event.color || '#3b82f6', color: 'white' }}
  >
    <div className="font-medium truncate">{event.title}</div>
    <div className="text-xs opacity-90">
      {formatTime(event.start)} - {formatTime(event.end)}
    </div>
  </div>
);

// Week/Work Week View Component
const WeekView = ({
  currentDate,
  events,
  isWorkWeek = false,
  onTimeSlotClick ,
  onNavigate,
  onDateChange,
  label,
}: {
  currentDate: Date;
  events: CalendarEvent[];
  isWorkWeek?: boolean;
  onTimeSlotClick: (date: Date, hour: number) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
  onDateChange?: (date: Date) => void;
  label:string,
}) => {
  const days = isWorkWeek ? getWorkWeekDays(currentDate) : getWeekDays(currentDate);
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const today = new Date();


// Handle navigation
  const handleNavigate = useCallback((direction: 'prev' | 'next' | 'today') => {
    onNavigate?.(direction);
  }, [onNavigate]);

  // Handle date change for SelectLaSemaine
  const handleDateChange = useCallback((newDate: Date) => {
    setselectedDateW(newDate);
    onDateChange?.(newDate);
  }, [onDateChange]);
   const messages = {
    today: "Aujourd’hui",
    previous: "Précédent",
    next: "Suivant",
    month: "Mois",
    week: "Semaine",
    day: "Jour",
    agenda: "Agenda",
    noEventsInRange: "Aucun événement prévu",
  };
   const [selectedDateW, setselectedDateW] = useState(new Date());
      useEffect(() => {
        setselectedDateW(moment(label, "DD MMMM YYYY").toDate());
      }, [label]);
        
   // Get current date title
  const getCurrentDateTitle = () => {
    
       const startOfWeek = new Date(currentDate);
        const day = startOfWeek.getDay();
        const diff = startOfWeek.getDate() - day;
        startOfWeek.setDate(diff);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${endOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}`;
    
    
  };
  // tab content Visites Actives
    

      const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [value, setValue] = useState<string | null>('1');
  const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});
  const setControlRef = (val: string) => (node: HTMLButtonElement) => {
    controlsRefs[val] = node;
    setControlsRefs(controlsRefs);
  };
  return (
    <>
         <div className="h-[40px] bg-[#E6E9EC] flex my-2 p-1">
            <div className="w-[50%]">
               <Group>
                 <div className="flex justify-start">
                                               {/* liste d'attente */}
                             <Icon path={mdiFileTree} size={1}    className="bg-[#E6E9EC] text-[#3799CE] cursor-pointer"/> 
                                                   <Divider orientation="vertical" color="#3799CE" h={20} mt={4}/>
                            <Avatar size={26}  radius={30} variant="filled" color="cyan" className="border-2 rounded-full border-[#fff] mr-[2px] ">
                              <Text fz="sm" fw={500} c={"white"}>
                               0
                              </Text>
                              </Avatar>
                            <ThemeIcon className="cursor-pointer">
                            <IconTextPlus stroke={2} size={30} className="bg-[#E6E9EC] text-[#3799CE] "
                                              />
                                              </ThemeIcon>
                                              </div>
                                            <Group gap={0}>  
                   <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize text-[#797C7F]">
                             <IconCalendarClock stroke={1.5} className="mr-1 h-3.5 w-3.5 " />
                             {getCurrentDateTitle()}  
                           </h3>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                        </Group>
                  <Button
                        size="xs"
                        className="HoverButton"
                        rightSection={<IconChevronDown stroke={2} />}
                        // onClick={open}
                      >
                        La durée du déjeuner
                      </Button>
                </Group>
            </div>
            <div className="w-[50%]">
               <Group justify="flex-end">
                     <Button
                      size="xs"
                      className="HoverButton"
                      rightSection={<IconChevronDown stroke={2} />}
                      // onClick={openedStartofwork}
                    >
                      Début des travaux
                    </Button>
                    <Group>
                      <Tooltip
                          label="Changer ressource"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                          
                            {/* <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                            <IconFileTypography stroke={1.75} className="h-4 w-4  hover:text-[#3799CE]" />
                            </h2> */}
                            <Icon path={mdiAccountNetwork} size={1} color={"#797C7F"}/>
                        </Tooltip>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                           <Tooltip
                          label="Filtrer par motifs"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                            <Icon path={mdiBookmarkOutline} size={1} color={"#797C7F"}/>
                        </Tooltip>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                          <Tooltip
                          label="Nouvel événement"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                          
                       
                           <Icon path={mdiCalendarRange} size={1} color={"#797C7F"} />
                    
                        </Tooltip>
                         <Tooltip
                          label="List des événements"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiCalendarMonth} size={1} color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                         <Tooltip
                          label="Planifier des rendez-vous"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiCalendarPlus} size={1}  color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                           <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                         <Tooltip
                          label="Chercher des rendez-vous"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiMagnify} size={1}  color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                     
                      </Group>
                    {/* Refresh button for appointments */}
                    <Button
                      variant="light"
                      size="xs"
                      // onClick={fetchTodayAppointments}
                      // loading={loadingAppointments}
                      leftSection={<IconRefresh size={14} />}
                      color="#15aabf"
                    >
                      Rafraîchir
                    </Button>
                </Group>
            </div>
            </div>
      
    <div className="flex flex-col h-full">
      <div className="flex">
      <div className="overflow-hidden w-[70%]">
          {/* Navigation Header */}
           <Group justify="space-between" p={4} className="border-2 border-[#3799CE] rounded-t-md bg-[#3799CE]">
               <Button variant="filled" size="sm" onClick={() => handleNavigate("today")} className=" rounded-md  text-[var(--text-tab)] ButtonHover" fz="md">
                    {messages.today}
                  </Button>
                     
                   
  <div className="flex items-center">
                   <button className="btn-sm  mr-1" onClick={() => handleNavigate('prev')}> <Icon path={mdiChevronLeft} size={1} color={"white"}/></button>
                   <Text fw={550} c={"var(--text-daisy-white)"}> {getCurrentDateTitle()}</Text>
                   <button className="btn-sm  ml-1" onClick={() => handleNavigate('next')}><Icon path={mdiChevronRight} size={1} color={"white"}/></button>
                 </div>
  <SelectLaSemaine
              date={selectedDateW}
              setter={handleDateChange}
              label={moment(selectedDateW).format("DD MMMM YYYY")} // Ensure this represents the start of the week
            /> 
           
    </Group>
      <div className="flex border-b ">
        <div className="w-16 p-2 border-r"></div>
        {days.map(day => (
          <div 
            key={day.toDateString()} 
            className={`flex-1 p-2 text-center border-r ${
              isSameDay(day, today) ? 'bg-blue-50' : ''
            }`}
          >
            <div className="font-semibold">{day.toLocaleDateString('en-US', { weekday: 'short' })}</div>
            <div className="text-sm">{day.getDate()}</div>
          </div>
        ))}
      </div>
      <div className="flex-1 overflow-y-auto">
        {hours.map(hour => (
          <div key={hour} className="flex border-b" style={{ minHeight: '60px' }}>
            <div className="w-16 p-1 text-xs text-gray-500 border-r">
              {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
            </div>
            {days.map(day => {
              const dayEvents = getEventsForDay(events, day).filter(event => 
                event.start.getHours() === hour
              );
              return (
                <div 
                  key={`${day.toDateString()}-${hour}`}
                  className="flex-1 p-1 border-r hover:bg-gray-50 cursor-pointer"
                  onClick={() => onTimeSlotClick(day, hour)}
                >
                  {dayEvents.map(event => (
                    <EventComponent key={event.id} event={event} />
                  ))}
                </div>
              );
            })}
          </div>
        ))}
      </div>
      </div>
       <div className="overflow-hidden w-[30%] mx-1">
     <Tabs variant="none" value={value} onChange={setValue}>
         <Tabs.List ref={setRootRef} className={classes.list}>
                 <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] flex w-full justify-between items-center"style={{ position: "relative" }}>
<span className="text-sm font-medium capitalize flex gap-2">
        <IconStethoscope stroke={1.25} />
       <Text size="md"> Visites Actives</Text>
      </span>  
 <div className="flex gap-2">
      <Tabs.Tab
    value="1"
    ref={setControlRef('1')}
    styles={{
      tab: {
        zIndex: 1,
        fontWeight: 500,
        transition: 'all 100ms ease',
        color: '#ffffff',
        padding: '8px 12px',
      
      },
    }}
  >
    Room A
  </Tabs.Tab>
        <Tabs.Tab
          value="2"
          ref={setControlRef('2')}
          styles={{
            tab: {
              zIndex: 1,
              fontWeight: 500,
              transition: 'all 100ms ease',
              color: '#ffffff',
              padding: '8px 12px',
             
            },
          }}
        >
          Room B
        </Tabs.Tab>
     
 <FloatingIndicator
          target={value ? controlsRefs[value] : null}
          parent={rootRef}
          styles={{
            root: {
              color: 'red',
              backgroundColor: '#15AABF',
              borderRadius: 'var(--mantine-radius-md)',
              borderBottom: '2px solid #3799CE',
              boxShadow: 'var(--mantine-shadow-lg)',
            },
          }}
        />
      </div>
                 </div>
           
      
       
       

        
      </Tabs.List>

      <Tabs.Panel value="1">
        <Container
                    fluid
                    w={"100%"}
                    className="rounded-b-lg bg-[--content-background]"
                    >
                       <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
            <Text c="dimmed" ta="center" mt="xl">Aucun patient dans la salle d&apos;attente.</Text>
          </div>
                    </Container>
        </Tabs.Panel>
      <Tabs.Panel value="2">
         <Container
                    fluid
                    w={"100%"}
                    className="rounded-b-lg bg-[--content-background]"
                    >
                       <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
            <Text c="dimmed" ta="center" mt="xl">Second tab content.</Text>
          </div>
                    </Container>
        </Tabs.Panel>
      
    </Tabs>  
    {/* Salle de présence    */}
    <div>
   <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] " >
     <Group justify="space-between">
     <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
         <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
              <Text size="md"> Salle de présence</Text>    

          </span>
       
          </span>
           <Group justify="flex-end">
               <Badge size="lg" circle radius="sm" color="red"> 0 </Badge>
                 <Badge size="lg" circle radius="sm" color="green"> 0 </Badge>
      </Group>
      </Group>
     
           
    </div>
  <Container
                    fluid
                    w={"100%"}
                    className="rounded-b-lg bg-[--content-background]"
                    >
                       <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
            <Text c="dimmed" ta="center" mt="xl">Aucune visites actives</Text>
          </div>
                    </Container>
                    </div>
  {/* Historique journalier */}
    <div>
   <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] " >
     <Group justify="space-between">
     <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <Icon path={mdiLogout} size={1} />
                 
 <Text size="md"> Historique journalier</Text>  
          </span>
       
          </span>
           <Group justify="flex-end">
               <Badge size="lg" circle radius="sm" color="red"> 0 </Badge>
                 <Badge size="lg" circle radius="sm" color="indigo"> 0 </Badge>
      </Group>
      </Group>
    </div>
  <Container
                    fluid
                    w={"100%"}
                    className="rounded-b-lg bg-[--content-background]"
                    >
                       <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
            <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          </div>
                    </Container>
                    </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default WeekView;
