# Generated by Django 4.2.7 on 2025-08-01 15:13

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('appointment_type', models.CharField(choices=[('consultation', 'Consultation'), ('follow_up', 'Follow-up'), ('emergency', 'Emergency'), ('routine_checkup', 'Routine Checkup'), ('procedure', 'Procedure'), ('surgery', 'Surgery'), ('cleaning', 'Cleaning'), ('other', 'Other')], default='consultation', max_length=20)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show'), ('rescheduled', 'Rescheduled')], default='scheduled', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10)),
                ('appointment_date', models.DateField()),
                ('appointment_time', models.TimeField()),
                ('end_time', models.TimeField(blank=True, null=True)),
                ('duration_minutes', models.PositiveIntegerField(default=30, validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(480)])),
                ('room', models.CharField(blank=True, max_length=50, null=True)),
                ('equipment_needed', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('reason_for_visit', models.TextField(blank=True, null=True)),
                ('symptoms', models.TextField(blank=True, null=True)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('insurance_covered', models.BooleanField(default=False)),
                ('reminder_sent', models.BooleanField(default=False)),
                ('reminder_sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_appointments', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'doctor'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_appointments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(limit_choices_to={'user_type': 'patient'}, on_delete=django.db.models.deletion.CASCADE, related_name='patient_appointments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Appointment',
                'verbose_name_plural': 'Appointments',
                'ordering': ['appointment_date', 'appointment_time'],
            },
        ),
        migrations.CreateModel(
            name='DentistryAppointment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('procedure_type', models.CharField(choices=[('cleaning', 'Cleaning'), ('filling', 'Filling'), ('crown', 'Crown'), ('root_canal', 'Root Canal'), ('extraction', 'Extraction'), ('implant', 'Implant'), ('orthodontics', 'Orthodontics'), ('whitening', 'Whitening'), ('checkup', 'Checkup'), ('emergency', 'Emergency'), ('other', 'Other')], default='checkup', max_length=20)),
                ('tooth_numbers', models.CharField(blank=True, help_text='Comma-separated tooth numbers (e.g., 1,2,3)', max_length=100, null=True)),
                ('quadrant', models.CharField(blank=True, choices=[('upper_right', 'Upper Right'), ('upper_left', 'Upper Left'), ('lower_right', 'Lower Right'), ('lower_left', 'Lower Left'), ('full_mouth', 'Full Mouth')], max_length=20, null=True)),
                ('anesthesia_required', models.BooleanField(default=False)),
                ('anesthesia_type', models.CharField(blank=True, max_length=50, null=True)),
                ('pre_medication', models.TextField(blank=True, null=True)),
                ('post_care_instructions', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('follow_up_notes', models.TextField(blank=True, null=True)),
                ('materials_used', models.TextField(blank=True, null=True)),
                ('lab_work_required', models.BooleanField(default=False)),
                ('lab_instructions', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_details', to='appointments.appointment')),
            ],
            options={
                'verbose_name': 'Dentistry Appointment',
                'verbose_name_plural': 'Dentistry Appointments',
            },
        ),
        migrations.CreateModel(
            name='AppointmentReminder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reminder_type', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('phone', 'Phone Call'), ('push', 'Push Notification')], default='email', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=10)),
                ('send_at', models.DateTimeField()),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('subject', models.CharField(blank=True, max_length=200, null=True)),
                ('message', models.TextField()),
                ('attempts', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='appointments.appointment')),
            ],
            options={
                'verbose_name': 'Appointment Reminder',
                'verbose_name_plural': 'Appointment Reminders',
                'ordering': ['send_at'],
            },
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['appointment_date', 'appointment_time'], name='appointment_appoint_c5b816_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['patient'], name='appointment_patient_94a7ef_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['doctor'], name='appointment_doctor__649ad1_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['status'], name='appointment_status_8fe9d7_idx'),
        ),
    ]
