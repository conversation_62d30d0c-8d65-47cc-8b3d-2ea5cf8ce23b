'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Button,
  TextInput,
  Group,
  Text,
  Modal,
  Table,
  ActionIcon,
  Switch,
  Select,
  NumberInput
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconMenu2,
  IconPlus,
  IconRefresh,
  IconX
} from '@tabler/icons-react';

// Types
interface SequenceModule {
  id: number;
  module: string;
  isActive: boolean;
  prefix: string;
  incrementBy: number;
  startBy: number;
  padding: number;
  lastValue: number;
  nextValue: number;
  suffix: string;
}

interface NewSequence {
  name: string;
  module: string;
  prefix: string;
  suffix: string;
  incrementBy: number;
  startBy: number;
  padding: number;
  nextValue: number;
}

const FacturationStock: React.FC = () => {
  const [sequences, setSequences] = useState<SequenceModule[]>([
    { id: 1, module: 'Factures Patient - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 5, nextValue: 6, suffix: '' },
    { id: 2, module: 'Mouvement du stock - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 3, module: 'Bons de commande - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 4, module: 'Bons de réception - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 5, module: 'Bons de déposition - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 6, module: 'Bons de retour - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 7, module: 'Bons de livraison - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 8, module: 'Inventaire - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 9, module: 'Demandes de prix - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 10, module: 'Devis - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 11, module: 'Transformation - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 12, module: 'Facture d\'achat - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 13, module: 'Avoir - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 14, module: 'A nouveaux - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 15, module: 'Règlement-stock - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 16, module: 'Règlement-facturation - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 17, module: 'N°. Avoir Billing/CreditNote - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' },
    { id: 18, module: 'N°. Contract Contract - Model1', isActive: true, prefix: '', incrementBy: 1, startBy: 1, padding: 1, lastValue: 1, nextValue: 1, suffix: '' }
  ]);

  const [newSequence, setNewSequence] = useState<NewSequence>({
    name: '',
    module: '',
    prefix: '',
    suffix: '',
    incrementBy: 1,
    startBy: 1,
    padding: 1,
    nextValue: 1
  });

  // Modals
  const [newSequenceModalOpened, { open: openNewSequenceModal, close: closeNewSequenceModal }] = useDisclosure(false);

  const handleToggleSequence = (id: number) => {
    setSequences(prev => prev.map(seq => 
      seq.id === id ? { ...seq, isActive: !seq.isActive } : seq
    ));
  };

  const handleRefresh = () => {
    notifications.show({
      title: 'Actualisation',
      message: 'Tableau actualisé avec succès',
      color: 'blue'
    });
  };

  const handleSaveNewSequence = () => {
    if (!newSequence.name.trim() || !newSequence.module.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le nom et le module sont obligatoires',
        color: 'red'
      });
      return;
    }

    const newSeq: SequenceModule = {
      id: sequences.length + 1,
      module: `${newSequence.name} - ${newSequence.module}`,
      isActive: true,
      prefix: newSequence.prefix,
      incrementBy: newSequence.incrementBy,
      startBy: newSequence.startBy,
      padding: newSequence.padding,
      lastValue: newSequence.startBy,
      nextValue: newSequence.nextValue,
      suffix: newSequence.suffix
    };

    setSequences(prev => [...prev, newSeq]);
    
    notifications.show({
      title: 'Succès',
      message: 'Nouvelle séquence créée avec succès',
      color: 'green'
    });

    closeNewSequenceModal();
    setNewSequence({
      name: '',
      module: '',
      prefix: '',
      suffix: '',
      incrementBy: 1,
      startBy: 1,
      padding: 1,
      nextValue: 1
    });
  };

  const moduleOptions = [
    { value: 'Model1', label: 'Model1' },
    { value: 'Model2', label: 'Model2' },
    { value: 'Model3', label: 'Model3' }
  ];

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-blue-500 text-white px-6 py-4 flex items-center gap-3">
        <IconMenu2 size={24} />
        <Title order={2} className="text-white font-medium">
          Numéros de séquence
        </Title>
        <div className="flex-1" />
        <Button
          leftSection={<IconPlus size={16} />}
          variant="filled"
          color="blue"
          onClick={openNewSequenceModal}
          className="bg-blue-400 hover:bg-blue-300"
        >
          Nouvelle séquence
        </Button>
      </div>

      {/* Table */}
      <div className="flex-1 p-6">
        <Table>
          <Table.Thead>
            <Table.Tr className="bg-gray-100">
              <Table.Th>Module</Table.Th>
              <Table.Th>Par défaut</Table.Th>
              <Table.Th>Préfixe</Table.Th>
              <Table.Th>Incrémenter Par</Table.Th>
              <Table.Th>Démarrer Par</Table.Th>
              <Table.Th>Remplissage</Table.Th>
              <Table.Th>Dernière valeur</Table.Th>
              <Table.Th>Prochaine valeur</Table.Th>
              <Table.Th>Suffixe</Table.Th>
              <Table.Th></Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {sequences.map((sequence) => (
              <Table.Tr key={sequence.id} className="hover:bg-gray-50">
                <Table.Td className="text-blue-600">{sequence.module}</Table.Td>
                <Table.Td>
                  <Switch
                    checked={sequence.isActive}
                    onChange={() => handleToggleSequence(sequence.id)}
                    color="blue"
                    size="sm"
                  />
                </Table.Td>
                <Table.Td className="text-blue-600">{sequence.prefix || '-'}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.incrementBy}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.startBy}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.padding}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.lastValue}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.nextValue}</Table.Td>
                <Table.Td className="text-blue-600">{sequence.suffix || '-'}</Table.Td>
                <Table.Td>
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={handleRefresh}
                  >
                    <IconRefresh size={16} />
                  </ActionIcon>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>

      {/* New Sequence Modal */}
      <Modal
        opened={newSequenceModalOpened}
        onClose={closeNewSequenceModal}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconMenu2 size={20} />
            <Text fw={500}>Nouvelle séquence</Text>
          </div>
        }
        size="lg"
        padding={0}
        closeButtonProps={{
          icon: <IconX size={20} />,
          className: 'text-white hover:bg-blue-400'
        }}
      >
        <div className="p-6">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-red-500">
                Nom *
              </Text>
              <TextInput
                value={newSequence.name}
                onChange={(e) => setNewSequence(prev => ({ ...prev, name: e.target.value }))}
                className="border-b-2 border-red-500"
                placeholder="Nom de la séquence"
              />
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Module *
              </Text>
              <Select
                value={newSequence.module}
                onChange={(value) => setNewSequence(prev => ({ ...prev, module: value || '' }))}
                data={moduleOptions}
                placeholder="Sélectionner un module"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Préfixe
              </Text>
              <TextInput
                value={newSequence.prefix}
                onChange={(e) => setNewSequence(prev => ({ ...prev, prefix: e.target.value }))}
                placeholder="Préfixe"
              />
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Suffixe
              </Text>
              <TextInput
                value={newSequence.suffix}
                onChange={(e) => setNewSequence(prev => ({ ...prev, suffix: e.target.value }))}
                placeholder="Suffixe"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Incrémenter Par
              </Text>
              <NumberInput
                value={newSequence.incrementBy}
                onChange={(value) => setNewSequence(prev => ({ ...prev, incrementBy: Number(value) || 1 }))}
                min={1}
                placeholder="1"
              />
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Démarrer Par
              </Text>
              <NumberInput
                value={newSequence.startBy}
                onChange={(value) => setNewSequence(prev => ({ ...prev, startBy: Number(value) || 1 }))}
                min={1}
                placeholder="1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Remplissage
              </Text>
              <NumberInput
                value={newSequence.padding}
                onChange={(value) => setNewSequence(prev => ({ ...prev, padding: Number(value) || 1 }))}
                min={1}
                placeholder="1"
              />
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-500">
                Prochaine valeur
              </Text>
              <NumberInput
                value={newSequence.nextValue}
                onChange={(value) => setNewSequence(prev => ({ ...prev, nextValue: Number(value) || 1 }))}
                min={1}
                placeholder="1"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="default"
              onClick={closeNewSequenceModal}
              className="text-gray-600"
            >
              Annuler
            </Button>
            <Button
              color="red"
              onClick={handleSaveNewSequence}
            >
              Enregistrer
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default FacturationStock;
