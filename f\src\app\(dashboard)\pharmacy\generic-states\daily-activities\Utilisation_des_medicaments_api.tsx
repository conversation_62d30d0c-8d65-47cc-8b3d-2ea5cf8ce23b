'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Radio,
  Menu,
  Alert
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiPill,
  mdiFilterMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface MedicineColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface MedicineQuery {
  start: Date;
  end: Date;
}

interface MedicineState {
  name: string;
  label: string;
  type: 'latest_prescriptions';
  deactivated?: boolean;
}

interface MedicineFilter {
  showAdvancedFilter: boolean;
}

interface UtilisationDesMedicamentsApiProps {
  loading?: boolean;
  onQueryChange?: (query: MedicineQuery) => void;
  onStateChange?: (state: MedicineState) => void;
  onFilterChange?: (filter: MedicineFilter) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const UtilisationDesMedicamentsAPI: React.FC<UtilisationDesMedicamentsApiProps> = ({
  loading = false,
  onQueryChange,
  onStateChange,
  onFilterChange,
  onExport,
  onPrint,
  onSort
}) => {
  // États locaux
  const [query, setQuery] = useState<MedicineQuery>({
    start: new Date(),
    end: new Date()
  });

  const [selectedState, setSelectedState] = useState<MedicineState>({
    name: 'latest_prescriptions',
    label: 'Dernières prescription',
    type: 'latest_prescriptions'
  });

  const [filter, setFilter] = useState<MedicineFilter>({
    showAdvancedFilter: true
  });

  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: MedicineColumn[] = [
    { id: 'medicine_name_sum', label: 'medicine_name_sum', sortable: true }
  ];

  // États disponibles
  const states: MedicineState[] = [
    { name: 'latest_prescriptions', label: 'Dernières prescription', type: 'latest_prescriptions' }
  ];

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<MedicineQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleStateChange = (state: MedicineState) => {
    setSelectedState(state);
    onStateChange?.(state);
  };

  const handleFilterChange = (newFilter: Partial<MedicineFilter>) => {
    const updatedFilter = { ...filter, ...newFilter };
    setFilter(updatedFilter);
    onFilterChange?.(updatedFilter);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiPill} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Utilisation des médicaments</Text>
          </Group>

          {/* Bouton filtre avancé */}
          {filter.showAdvancedFilter && (
            <ActionIcon
              variant="subtle"
              onClick={() => handleFilterChange({ showAdvancedFilter: !filter.showAdvancedFilter })}
              title="Filtre avancé"
            >
              <Icon path={mdiFilterMultiple} size={0.8} />
            </ActionIcon>
          )}
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md" wrap="wrap">
          {/* Date picker "Du" */}
          <DateInput
            label="Du"
            value={query.start}
            onChange={(value) => value && handleQueryChange({ start: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          {/* Date picker "Au" */}
          <DateInput
            label="Au"
            value={query.end}
            onChange={(value) => value && handleQueryChange({ end: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          {/* Source de données */}
          <Box style={{ marginLeft: 12 }}>
            <Text size="sm" fw={500} mb="xs">Source de données</Text>
            <Radio.Group
              value={selectedState.name}
              onChange={(value) => {
                const state = states.find(s => s.name === value);
                if (state) handleStateChange(state);
              }}
            >
              <Group>
                {states.map((state) => (
                  <Radio
                    key={state.name}
                    value={state.name}
                    label={state.label}
                    disabled={state.deactivated}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>
        </Group>
      </Paper>

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <Menu shadow="md" width={250}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Format">
                      <Icon path={mdiTableEdit} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatLetterMatches} size={0.8} />}
                    >
                      Format de cellule
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatColorHighlight} size={0.8} />}
                    >
                      La mise en forme conditionnelle
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Champs">
                  <Icon path={mdiTableSettings} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Tableau des données */}
            <ScrollArea style={{ height: 'calc(100vh - 350px)' }}>
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    {/* Filtres de lignes */}
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Nom commercial</Text>
                    </Table.Th>
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Date</Text>
                    </Table.Th>
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Nom du patient</Text>
                    </Table.Th>
                    {columns.map((column) => (
                      <Table.Th
                        key={column.id}
                        style={{
                          minWidth: 150,
                          backgroundColor: '#f8f9fa',
                          cursor: column.sortable ? 'pointer' : 'default'
                        }}
                        onClick={() => column.sortable && handleSort(column.id)}
                      >
                        <Group gap="xs" justify="space-between">
                          <Text size="sm" fw={500}>{column.label}</Text>
                          {column.sortable && (
                            <Icon
                              path={getSortIcon(column.id) || mdiArrowUp}
                              size={0.6}
                              style={{
                                opacity: getSortIcon(column.id) ? 1 : 0.3
                              }}
                            />
                          )}
                        </Group>
                      </Table.Th>
                    ))}
                    {/* Colonnes vides supplémentaires */}
                    {Array.from({ length: 8 }, (_, index) => (
                      <Table.Th
                        key={`empty-col-${index}`}
                        style={{
                          minWidth: 100,
                          backgroundColor: '#f8f9fa'
                        }}
                      />
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {/* Ligne Total */}
                  <Table.Tr style={{ backgroundColor: '#e8f5e8' }}>
                    <Table.Td style={{ fontWeight: 'bold' }}>
                      <Group gap="xs" justify="space-between">
                        <Text fw={500}>Total</Text>
                        <Icon
                          path={getSortIcon('total') || mdiArrowUp}
                          size={0.6}
                          style={{
                            opacity: getSortIcon('total') ? 1 : 0.3
                          }}
                        />
                      </Group>
                    </Table.Td>
                    {/* Cellules vides pour les filtres */}
                    {Array.from({ length: 2 }, (_, index) => (
                      <Table.Td key={`filter-total-${index}`} />
                    ))}
                    {columns.map((column) => (
                      <Table.Td key={column.id} style={{ textAlign: 'center' }}>
                        <Text size="sm" c="dimmed">-</Text>
                      </Table.Td>
                    ))}
                    {/* Cellules vides */}
                    {Array.from({ length: 8 }, (_, index) => (
                      <Table.Td key={`empty-total-${index}`} />
                    ))}
                  </Table.Tr>

                  {/* Lignes vides pour remplir l'espace */}
                  {Array.from({ length: 20 }, (_, index) => (
                    <Table.Tr key={`empty-row-${index}`}>
                      {Array.from({ length: 12 }, (_, cellIndex) => (
                        <Table.Td key={cellIndex} style={{ height: 30 }} />
                      ))}
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Message d'état vide */}
            <Box
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1
              }}
            >
              <Alert
                icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                title="Aucune donnée disponible"
                color="gray"
                variant="light"
                style={{ maxWidth: 400 }}
              >
                <Text size="sm" c="dimmed">
                  Aucun médicament trouvé pour la période et la source de données sélectionnées.
                  Veuillez vérifier les filtres ou sélectionner une autre période.
                </Text>
              </Alert>
            </Box>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default UtilisationDesMedicamentsAPI;
