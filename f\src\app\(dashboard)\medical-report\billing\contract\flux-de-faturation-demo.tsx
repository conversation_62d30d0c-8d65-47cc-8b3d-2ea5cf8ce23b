'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { FluxDeFaturation } from './Flux_de_faturation';

export default function FluxDeFaturationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    const startDate = query.startDate ? query.startDate.toLocaleDateString('fr-FR') : 'Non définie';
    const endDate = query.endDate ? query.endDate.toLocaleDateString('fr-FR') : 'Non définie';
    alert(`Flux de facturation:\nRecherche: "${query.searchAll}"\nDu: ${startDate}\nAu: ${endDate}\nPage: ${query.page}\nLignes: ${query.limit}`);
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer:', item);
    alert(`Facturation pour:\nPatient: ${item.patient.fullName}\nDate: ${item.createdAt.toLocaleDateString('fr-FR')}\nSource: ${item.source.label}\nStatut: ${item.status.label}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function FluxDeFaturationLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onInvoice={(item) => console.log('Invoice:', item)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function FluxDeFaturationWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      createdAt: new Date('2025-07-02T09:30:00'),
      patient: {
        name: 'MARTIN Jean',
        fullName: 'MARTIN Jean'
      },
      insurance: 'CPAM',
      source: {
        type: 'visit' as const,
        label: 'Visite',
        color: '#d717ec'
      },
      documentNumber: 'DOC-2025-001',
      status: {
        type: 'not_billed' as const,
        label: 'Non Facturé',
        color: 'red',
        icon: 'mdi-file-document'
      }
    },
    {
      id: '2',
      createdAt: new Date('2025-07-02T14:15:00'),
      patient: {
        name: 'DUPONT Marie',
        fullName: 'DUPONT Marie'
      },
      insurance: 'Mutuelle XYZ',
      source: {
        type: 'appointment' as const,
        label: 'Rendez-vous',
        color: '#2196f3'
      },
      documentNumber: 'DOC-2025-002',
      status: {
        type: 'partially_billed' as const,
        label: 'Partiellement facturé',
        color: 'orange',
        icon: 'mdi-file-document'
      }
    },
    {
      id: '3',
      createdAt: new Date('2025-07-02T16:45:00'),
      patient: {
        name: 'BERNARD Paul',
        fullName: 'BERNARD Paul'
      },
      insurance: '',
      source: {
        type: 'consultation' as const,
        label: 'Consultation',
        color: '#4caf50'
      },
      documentNumber: 'DOC-2025-003',
      status: {
        type: 'fully_billed' as const,
        label: 'Entièrement facturé',
        color: 'green',
        icon: 'mdi-file-document'
      }
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    const startDate = query.startDate ? query.startDate.toLocaleDateString('fr-FR') : 'Non définie';
    const endDate = query.endDate ? query.endDate.toLocaleDateString('fr-FR') : 'Non définie';
    alert(`Flux de facturation avec données:\nRecherche: "${query.searchAll}"\nPériode: ${startDate} - ${endDate}\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nTotal: ${sampleItems.length} éléments`);
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer avec données:', item);
    alert(`Facturation détaillée:\n\nPatient: ${item.patient.fullName}\nDate de création: ${item.createdAt.toLocaleDateString('fr-FR')} à ${item.createdAt.toLocaleTimeString('fr-FR')}\nAssurance: ${item.insurance || 'Aucune'}\nSource: ${item.source.label}\nN° Document: ${item.documentNumber || 'Non défini'}\nStatut: ${item.status.label}\n\nAction: Générer la facture`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec recherche
export function FluxDeFaturationSearchDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Recherche:', query);
    if (query.searchAll) {
      alert(`Recherche dans le flux de facturation:\nTerme: "${query.searchAll}"\nRecherche dans: Patients, Assurances, Documents, Statuts`);
    }
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer recherche:', item);
    alert(`Facturation depuis recherche:\nPatient trouvé: ${item.patient.fullName}\nAction: Créer facture`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres de date
export function FluxDeFaturationDateFilterDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtre de date:', query);
    if (query.startDate || query.endDate) {
      const startDate = query.startDate ? query.startDate.toLocaleDateString('fr-FR') : 'Début';
      const endDate = query.endDate ? query.endDate.toLocaleDateString('fr-FR') : 'Fin';
      alert(`Filtre par date:\nPériode: ${startDate} - ${endDate}\nRecherche des facturations dans cette période`);
    }
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer avec filtre:', item);
    alert(`Facturation filtrée:\nPatient: ${item.patient.fullName}\nDans la période sélectionnée`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function FluxDeFaturationPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation dans le flux:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les résultats de facturation`);
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer pagination:', item);
    alert(`Facturation depuis pagination:\nPatient: ${item.patient.fullName}\nPage courante: Traitement de la facture`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={[]}
          total={150} // Simule 150 éléments pour tester la pagination
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function FluxDeFaturationErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.startDate && query.endDate && query.startDate > query.endDate) {
      alert('Erreur: La date de début ne peut pas être postérieure à la date de fin.');
      return;
    }
    
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleInvoice = (item: any) => {
    console.log('Facturer avec validation:', item);
    if (confirm(`Êtes-vous sûr de vouloir facturer ${item.patient.fullName} ?`)) {
      alert('Facture générée avec succès !');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec statuts multiples
export function FluxDeFaturationStatusDemo() {
  const sampleItemsWithStatuses = [
    {
      id: '1',
      createdAt: new Date('2025-07-02T09:30:00'),
      patient: { name: 'MARTIN Jean', fullName: 'MARTIN Jean' },
      insurance: 'CPAM',
      source: { type: 'visit' as const, label: 'Visite', color: '#d717ec' },
      documentNumber: 'DOC-001',
      status: { type: 'not_billed' as const, label: 'Non Facturé', color: 'red', icon: 'mdi-file-document' }
    },
    {
      id: '2',
      createdAt: new Date('2025-07-02T14:15:00'),
      patient: { name: 'DUPONT Marie', fullName: 'DUPONT Marie' },
      insurance: 'Mutuelle XYZ',
      source: { type: 'appointment' as const, label: 'RDV', color: '#2196f3' },
      documentNumber: 'DOC-002',
      status: { type: 'partially_billed' as const, label: 'Partiellement facturé', color: 'orange', icon: 'mdi-file-document' }
    },
    {
      id: '3',
      createdAt: new Date('2025-07-02T16:45:00'),
      patient: { name: 'BERNARD Paul', fullName: 'BERNARD Paul' },
      insurance: '',
      source: { type: 'consultation' as const, label: 'Consultation', color: '#4caf50' },
      documentNumber: 'DOC-003',
      status: { type: 'fully_billed' as const, label: 'Entièrement facturé', color: 'green', icon: 'mdi-file-document' }
    }
  ];

  const handleInvoice = (item: any) => {
    console.log('Facturer par statut:', item);
    const statusMessages: { [key: string]: string } = {
      'not_billed': 'Créer une nouvelle facture',
      'partially_billed': 'Compléter la facturation',
      'fully_billed': 'Voir la facture existante'
    };
    
    const action = statusMessages[item.status.type] || 'Action de facturation';
    alert(`${action}:\nPatient: ${item.patient.fullName}\nStatut: ${item.status.label}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <FluxDeFaturation
          loading={false}
          items={sampleItemsWithStatuses}
          total={sampleItemsWithStatuses.length}
          onQueryChange={(query) => console.log('Query statuts:', query)}
          onInvoice={handleInvoice}
        />
      </div>
    </MantineProvider>
  );
}
