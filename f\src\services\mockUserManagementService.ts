import { UserAccount, SubscriptionPackage } from './userManagementService';
import { getStoredData, storeData, STORAGE_KEYS } from '@/utils/mockDataStorage';

// Default mock data for user accounts
const defaultUserAccounts: UserAccount[] = [
  {
    id: '1',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    user_type: 'doctor',
    status: 'active',
    created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
    created_by: 'self',
    phone_number: '+****************'
  },
  {
    id: '3',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    user_type: 'patient',
    status: 'active',
    created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    created_by: '1',
    phone_number: '+****************'
  }
];

// Get stored user accounts or use defaults
const getMockUserAccounts = (): UserAccount[] => {
  return getStoredData<UserAccount[]>(STORAGE_KEYS.ASSISTANT_ACCOUNTS, defaultUserAccounts);
};

// Update stored user accounts
const updateMockUserAccounts = (accounts: UserAccount[]): void => {
  storeData(STORAGE_KEYS.ASSISTANT_ACCOUNTS, accounts);
};

// Default mock data for subscription packages
const defaultSubscriptionPackages: SubscriptionPackage[] = [
  {
    id: '1',
    name: 'Basic',
    max_patients: 50,
    max_assistants: 1,
    max_staff: 1,
    price_monthly: 99,
    price_yearly: 999,
    features: [
      '50 Patient Accounts',
      '1 Assistant Account',
      '1 Staff Account',
      'Basic Appointment Scheduling',
      'Patient Records Management'
    ],
    is_current: false
  },
  {
    id: '2',
    name: 'Professional',
    max_patients: 200,
    max_assistants: 3,
    max_staff: 3,
    price_monthly: 199,
    price_yearly: 1999,
    features: [
      '200 Patient Accounts',
      '3 Assistant Accounts',
      '3 Staff Accounts',
      'Advanced Appointment Scheduling',
      'Patient Records Management',
      'Billing Integration',
      'Email Notifications'
    ],
    is_current: true
  },
  {
    id: '3',
    name: 'Enterprise',
    max_patients: 500,
    max_assistants: 10,
    max_staff: 10,
    price_monthly: 399,
    price_yearly: 3999,
    features: [
      '500 Patient Accounts',
      '10 Assistant Accounts',
      '10 Staff Accounts',
      'Advanced Appointment Scheduling',
      'Patient Records Management',
      'Billing Integration',
      'Email & SMS Notifications',
      'Custom Branding',
      'Priority Support'
    ],
    is_current: false
  }
];

// Get stored subscription packages or use defaults
const getMockSubscriptionPackages = (): SubscriptionPackage[] => {
  return getStoredData<SubscriptionPackage[]>('mock_subscription_packages', defaultSubscriptionPackages);
};

// Update stored subscription packages
const updateMockSubscriptionPackages = (packages: SubscriptionPackage[]): void => {
  storeData('mock_subscription_packages', packages);
};

// Direct mock implementation
const mockUserManagementService = {
  async getUserAccounts(): Promise<UserAccount[]> {
    console.log('MOCK: Fetching user accounts');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored accounts and return only assistant accounts
    const accounts = getMockUserAccounts();
    const assistantAccounts = accounts.filter(user => user.user_type === 'assistant');
    console.log('MOCK: Retrieved assistant accounts from storage:', assistantAccounts);

    return assistantAccounts;
  },

  async createUserAccount(data: Partial<UserAccount>): Promise<UserAccount | null> {
    console.log('MOCK: Creating user account with data:', data);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get current accounts
    const accounts = getMockUserAccounts();

    // Create a new user with the provided data
    const newUser: UserAccount = {
      id: `${Date.now()}`, // Use timestamp for unique ID
      email: data.email || '<EMAIL>',
      first_name: data.first_name || 'New',
      last_name: data.last_name || 'User',
      phone_number: data.phone_number || '',
      user_type: data.user_type || 'assistant',
      status: data.status as 'active' | 'inactive' | 'pending' || 'pending',
      created_at: new Date().toISOString(),
      created_by: '1' // Assuming the doctor is creating the account
    };

    // Add the new user to the accounts
    accounts.push(newUser);

    // Update stored accounts
    updateMockUserAccounts(accounts);
    console.log('MOCK: User created and stored successfully:', newUser);

    return newUser;
  },

  async updateUserAccount(id: string, data: Partial<UserAccount>): Promise<UserAccount | null> {
    console.log('MOCK: Updating user account with id:', id, 'data:', data);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get current accounts
    const accounts = getMockUserAccounts();

    // Find the user to update
    const userIndex = accounts.findIndex(user => user.id === id);
    if (userIndex === -1) {
      console.log('MOCK: User not found');
      return null;
    }

    // Create a copy of the user with the updated data
    const updatedUser: UserAccount = {
      ...accounts[userIndex],
      ...data
    };

    // Update the accounts
    accounts[userIndex] = updatedUser;

    // Update stored accounts
    updateMockUserAccounts(accounts);
    console.log('MOCK: User updated and stored successfully:', updatedUser);

    return updatedUser;
  },

  async deleteUserAccount(id: string, userType?: string): Promise<boolean> {
    console.log('MOCK: Deleting user account with id:', id);
    console.log('MOCK: User type (if provided):', userType || 'not specified');

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get current accounts
    const accounts = getMockUserAccounts();

    // Find the user to delete
    const userIndex = accounts.findIndex(user => user.id === id);
    if (userIndex === -1) {
      console.log('MOCK: User not found');
      return false;
    }

    // Log the user being deleted
    console.log('MOCK: Found user to delete:', accounts[userIndex]);

    // Remove the user from the accounts
    accounts.splice(userIndex, 1);

    // Update stored accounts
    updateMockUserAccounts(accounts);
    console.log('MOCK: User deleted and storage updated successfully');

    return true;
  },

  async getSubscriptionPackages(): Promise<SubscriptionPackage[]> {
    console.log('MOCK: Fetching subscription packages');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const packages = getMockSubscriptionPackages();
    console.log('MOCK: Retrieved subscription packages from storage:', packages);

    return packages;
  },

  async getCurrentSubscription(): Promise<SubscriptionPackage> {
    console.log('MOCK: Fetching current subscription');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const packages = getMockSubscriptionPackages();

    const currentPackage = packages.find(pkg => pkg.is_current);
    console.log('MOCK: Retrieved current subscription from storage:', currentPackage);

    return currentPackage || packages[0];
  },

  async updateSubscription(packageId: string): Promise<SubscriptionPackage> {
    console.log('MOCK: Updating subscription to package:', packageId);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const packages = getMockSubscriptionPackages();

    // Reset all packages to not current
    packages.forEach(pkg => pkg.is_current = false);

    // Find the package to update
    const packageIndex = packages.findIndex(pkg => pkg.id === packageId);
    if (packageIndex === -1) {
      console.log('MOCK: Package not found');
      throw new Error('Subscription package not found');
    }

    // Set the selected package as current
    packages[packageIndex].is_current = true;

    // Update stored packages
    updateMockSubscriptionPackages(packages);
    console.log('MOCK: Subscription updated and stored successfully');

    return packages[packageIndex];
  },

  async getUserCounts(): Promise<{ assistants: number; patients: number; staff: number }> {
    console.log('MOCK: Fetching user counts');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored accounts
    const accounts = getMockUserAccounts();

    const assistantCount = accounts.filter(user => user.user_type === 'assistant').length;
    const patientCount = accounts.filter(user => user.user_type === 'patient').length;
    const staffCount = accounts.filter(user => user.user_type === 'staff').length;

    const counts = {
      assistants: assistantCount,
      patients: patientCount,
      staff: staffCount
    };

    console.log('MOCK: Retrieved user counts from storage:', counts);
    return counts;
  }
};

export default mockUserManagementService;
