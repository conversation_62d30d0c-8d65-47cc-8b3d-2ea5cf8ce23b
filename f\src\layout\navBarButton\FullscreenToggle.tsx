import React from 'react'
import { useFullscreen } from '@mantine/hooks';

import { IconArrowsMaximize,IconArrowsMinimize } from '@tabler/icons-react';
import { ActionIcon, Tooltip } from "@mantine/core";
const ICON_SIZE = 20;
const FullscreenToggle = () => {
    const { toggle, fullscreen } = useFullscreen();
  return (
    <>
    

     <Tooltip
        label={fullscreen ? "Réduire l'écran" : "Plein écran"}
        style={{color:"var(--mantine-color-text)"}}  className="bg-[var(--tooltip-bg)]"
        withArrow
      >
        <Tooltip
          label={fullscreen ? "Réduire l'écran" : "Plein écran"}
          style={{color:"var(--mantine-color-text)"}}  className="bg-[var(--tooltip-bg)]"
          withArrow
        >
          <ActionIcon
            size="lg"
            onClick={toggle}
            className="h-10 navBarButtonicon focus:outline-none "
            style={{color:"var(--mantine-color-text)"}}
          >
            {fullscreen ? (
              <IconArrowsMinimize size={ICON_SIZE} className=" navBarButtonicon " />
            ) : (
              <IconArrowsMaximize size={ICON_SIZE} className=" navBarButtonicon "/>
            )}
          </ActionIcon>
        </Tooltip>
      </Tooltip>
    </>
  )
}

export default FullscreenToggle