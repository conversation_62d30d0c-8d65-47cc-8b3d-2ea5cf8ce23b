/**
 * Utility functions to clean up test data and reset the application to a clean state
 */

import { STORAGE_KEYS } from '@/utils/mockDataStorage';

/**
 * Remove all test users with @example.com emails from localStorage
 */
export function cleanupTestUsers(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // List of test emails to remove
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    // Clean up assistant accounts
    const assistantData = localStorage.getItem(STORAGE_KEYS.ASSISTANT_ACCOUNTS);
    if (assistantData) {
      const accounts = JSON.parse(assistantData);
      const cleanedAccounts = accounts.filter((user: { email: string }) =>
        !testEmails.includes(user.email) && !user.email.includes('@example.com')
      );
      localStorage.setItem(STORAGE_KEYS.ASSISTANT_ACCOUNTS, JSON.stringify(cleanedAccounts));
      console.log('Cleaned up test users from assistant accounts');
    }

    // Clean up real user accounts cache
    const realUserData = localStorage.getItem(STORAGE_KEYS.REAL_USER_ACCOUNTS);
    if (realUserData) {
      const accounts = JSON.parse(realUserData);
      const cleanedAccounts = accounts.filter((user: { email: string }) =>
        !testEmails.includes(user.email) && !user.email.includes('@example.com')
      );
      localStorage.setItem(STORAGE_KEYS.REAL_USER_ACCOUNTS, JSON.stringify(cleanedAccounts));
      console.log('Cleaned up test users from real user accounts cache');
    }

    // Clean up doctor-specific caches
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.includes('real_user_accounts_')) {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const accounts = JSON.parse(data);
            const cleanedAccounts = accounts.filter((user: { email: string }) =>
              !testEmails.includes(user.email) && !user.email.includes('@example.com')
            );
            localStorage.setItem(key, JSON.stringify(cleanedAccounts));
          } catch (error) {
            console.warn(`Error cleaning cache key ${key}:`, error);
          }
        }
      }
    }

    console.log('Successfully cleaned up all test user data');
  } catch (error) {
    console.warn('Error during test data cleanup:', error);
  }
}

/**
 * Reset all mock data to empty state (for fresh start)
 */
export function resetMockData(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear all mock data keys
    const mockKeys = [
      STORAGE_KEYS.ASSISTANT_ACCOUNTS,
      STORAGE_KEYS.SUBSCRIPTION_PACKAGES,
      STORAGE_KEYS.USER_COUNTS,
      'mock_subscription_packages'
    ];

    mockKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('Reset all mock data to empty state');
  } catch (error) {
    console.warn('Error resetting mock data:', error);
  }
}

/**
 * Initialize clean mock data without test users
 */
export function initializeCleanMockData(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Initialize with empty arrays instead of test data
    const cleanData = {
      [STORAGE_KEYS.ASSISTANT_ACCOUNTS]: [],
      [STORAGE_KEYS.USER_COUNTS]: {
        assistants: 0,
        staff: 0,
        patients: 0
      }
    };

    Object.entries(cleanData).forEach(([key, value]) => {
      localStorage.setItem(key, JSON.stringify(value));
    });

    console.log('Initialized clean mock data without test users');
  } catch (error) {
    console.warn('Error initializing clean mock data:', error);
  }
}

/**
 * Complete cleanup and reset function
 */
export function performCompleteCleanup(): void {
  console.log('Starting complete cleanup of test data...');

  // Step 1: Clean up test users
  cleanupTestUsers();

  // Step 2: Reset mock data
  resetMockData();

  // Step 3: Initialize clean data
  initializeCleanMockData();

  console.log('Complete cleanup finished');
}
