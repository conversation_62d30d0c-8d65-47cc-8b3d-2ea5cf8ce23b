import React from 'react'
import { Box, Button, Group, Title,} from '@mantine/core';
import Icon from '@mdi/react';
import { mdiCurrencyUsd,mdiFormatListBulleted } from '@mdi/js';
import Link from 'next/link';
export const HederEncaissement = (

) => {
  return (
   <Box className="mn-module-header" bg="blue.6" px="md" py="sm" w={'100%'}>
               <Group justify="space-between">
                 <Group>
                   <Icon path={mdiCurrencyUsd} size={1.2} className="mn-header-icon" color={"#ffffff"} />
                   <Title order={2} c={"#ffffff"}>
                     
                     Nouvel encaissement
                   </Title>
                 </Group>
         
                 <Link href="/payment/" passHref >
                   <Button
                     leftSection={<Icon path={mdiFormatListBulleted} size={1} />}
                     variant="subtle"
                     color={"#ffffff"}
                   >
                     Recettes
                   </Button>
                 </Link>
               </Group>
           
             </Box> 
  )
}
