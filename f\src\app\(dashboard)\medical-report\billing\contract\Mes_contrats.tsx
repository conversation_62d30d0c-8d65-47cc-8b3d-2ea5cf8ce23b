'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  TextInput,
  Button,
  Checkbox,
  Tabs,
  Drawer,
  ScrollArea,
  Divider,
  Menu,
  Pagination,
  Select,
  Box,
  Loader,
  Alert,
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiPlus,
  mdiFilterVariant,
  mdiMagnify,
  mdiReceiptText,
  mdiViewHeadline,
  mdiReload,
  mdiFileExcel,
  mdiDotsVertical,
  mdiFindReplace,
  mdiMagnifyPlus,
  mdiFilterRemoveOutline,
  mdiFilterOutline,
  mdiCheck
} from '@mdi/js';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconRefresh, IconTrash } from '@tabler/icons-react';

// Import billing hook
import { useBilling } from '@/hooks/useBilling';
import { ContractFilters } from './types/filters';

// Types et interfaces
interface ContractColumn {
  id: string;
  label: string;
  isFilter: boolean;
  isShown: boolean;
  isRequired?: boolean;
  type?: 'text' | 'date' | 'number' | 'boolean';
}

// interface ContractFilter {
//   id: string;
//   name: string;
//   applied: boolean;
// }

interface ContractQuery {
  searchAll: string;
  page: number;
  limit: number;
  filters: ContractFilters;
}

interface ContractItem {
  id: string;
  contractNumber: string;
  fileNumber: string;
  joinDate: Date;
  date: Date;
  startDate: Date;
  referredBy: string;
  endDate: Date;
  lastName: string;
  firstName: string;
  insurance: string;
  assignment: string;
  type: string;
  city: string;
  technician: string;
  totalAmount: number;
  validation: boolean;
}

interface MesContratsProps {
  loading?: boolean;
  items?: ContractItem[];
  total?: number;
  onQueryChange?: (query: ContractQuery) => void;
  onAddContract?: (type: 'SUBSCRIPTION' | 'LOCATION') => void;
  onExport?: (format: 'excel') => void;
  onAction?: (action: string, items: ContractItem[]) => void;
}

export const MesContrats: React.FC<MesContratsProps> = ({
  loading = false,
  items = [],
  total = 0,
  onQueryChange,
  onAddContract,
  onExport,
  onAction
}) => {
  // Use billing hook for backend integration
  const {
    contracts,
    loading: billingLoading,
    error: billingError,
    refreshAll,
    createContract,
  } = useBilling({
    autoFetch: true,
    dataTypes: ['contracts']
  });

  // États locaux
  const [query, setQuery] = useState<ContractQuery>({
    searchAll: '',
    page: 1,
    limit: 15,
    filters: {}
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('filters');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refreshAll();
      notifications.show({
        title: 'Actualisation',
        message: 'Données des contrats actualisées avec succès',
        color: 'blue'
      });
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de l\'actualisation des contrats',
        color: 'red'
      });
    }
  };

  // Convert contracts to ContractItem format
  const contractItemsFromBackend: ContractItem[] = contracts.map(contract => ({
    id: contract.id,
    contractNumber: `CONT-${contract.id}`,
    fileNumber: `FILE-${contract.id}`,
    joinDate: new Date(contract.startDate),
    date: new Date(contract.startDate),
    startDate: new Date(contract.startDate),
    referredBy: 'Référent par défaut',
    endDate: new Date(contract.endDate),
    lastName: contract.clientName.split(' ')[1] || 'Nom',
    firstName: contract.clientName.split(' ')[0] || 'Prénom',
    insurance: 'Assurance par défaut',
    assignment: 'Affectation par défaut',
    type: 'SUBSCRIPTION',
    city: 'Ville par défaut',
    technician: 'Technicien par défaut',
    totalAmount: contract.value,
    validation: contract.status === 'active',
  }));

  // Use backend data, then props, then sample data as fallback
  const displayLoading = billingLoading || loading;

  // Configuration des colonnes
  const columns: ContractColumn[] = [
    { id: 'contractNumber', label: 'N°.Contract', isFilter: false, isShown: true, isRequired: true },
    { id: 'fileNumber', label: 'N°.dossier', isFilter: false, isShown: true },
    { id: 'joinDate', label: 'Date d\'adhésion', isFilter: false, isShown: true, type: 'date' },
    { id: 'date', label: 'Date', isFilter: false, isShown: true, type: 'date' },
    { id: 'startDate', label: 'Démarre le', isFilter: false, isShown: true, type: 'date' },
    { id: 'referredBy', label: 'Adressé par', isFilter: false, isShown: true },
    { id: 'endDate', label: 'Date Fin', isFilter: false, isShown: true, type: 'date' },
    { id: 'lastName', label: 'Nom', isFilter: false, isShown: true },
    { id: 'firstName', label: 'Prénom', isFilter: false, isShown: true },
    { id: 'insurance', label: 'Assurance', isFilter: false, isShown: true },
    { id: 'assignment', label: 'Affectation', isFilter: false, isShown: true },
    { id: 'type', label: 'Type', isFilter: false, isShown: true },
    { id: 'city', label: 'Ville', isFilter: false, isShown: true },
    { id: 'technician', label: 'Technicien', isFilter: false, isShown: true },
    { id: 'totalAmount', label: 'Montant Total', isFilter: false, isShown: true, type: 'number' },
    { id: 'validation', label: 'Validation', isFilter: false, isShown: true, type: 'boolean' }
  ];

  // Données d'exemple
  const sampleItems: ContractItem[] = [
    {
      id: '1',
      contractNumber: '1',
      fileNumber: '',
      joinDate: new Date('2021-04-13'),
      date: new Date('2021-04-13'),
      startDate: new Date('2021-04-13'),
      referredBy: '',
      endDate: new Date('2022-04-13'),
      lastName: 'EL KANBI',
      firstName: 'ANAS',
      insurance: '',
      assignment: '',
      type: '',
      city: '',
      technician: '',
      totalAmount: 0.00,
      validation: false
    }
  ];

  // Use backend data, then props, then sample data as fallback
  const displayItems = contractItemsFromBackend.length > 0 ? contractItemsFromBackend :
                      items.length > 0 ? items : sampleItems;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<ContractQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleSearchChange = (value: string) => {
    handleQueryChange({ searchAll: value, page: 1 });
  };

  const handlePageChange = (page: number) => {
    handleQueryChange({ page });
  };

  const handleLimitChange = (limit: string | null) => {
    if (limit) {
      handleQueryChange({ limit: parseInt(limit), page: 1 });
    }
  };

  const handleAddContract = (type: 'SUBSCRIPTION' | 'LOCATION') => {
    console.log('Ajouter contrat:', type);
    onAddContract?.(type);
  };

  const handleExport = () => {
    console.log('Exporter Excel');
    onExport?.('excel');
  };

  const handleAction = (action: string) => {
    console.log('Action:', action, 'Items:', selectedItems);
    onAction?.(action, displayItems.filter(item => selectedItems.includes(item.id)));
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === displayItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(displayItems.map(item => item.id));
    }
  };

  const handleColumnFilter = (columnId: string, isFilter: boolean) => {
    console.log('Filtre colonne:', columnId, isFilter);
  };

  const handleColumnVisibility = (columnId: string) => {
    console.log('Visibilité colonne:', columnId);
  };

  const totalPages = Math.ceil(total / query.limit);
  const startItem = (query.page - 1) * query.limit + 1;
  const endItem = Math.min(query.page * query.limit, total);

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header avec boutons d'ajout */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#f8f9fa' }}>
        <Group justify="space-between" gap="md">
          <Group gap="md">
            <Text size="lg" fw={600}>Mes Contrats</Text>
            {displayLoading && <Loader size="sm" />}
          </Group>
          <Group gap="md">
            <ActionIcon
              variant="subtle"
              color="blue"
              size="lg"
              onClick={handleRefresh}
              loading={displayLoading}
              title="Actualiser"
            >
              <IconRefresh size={18} />
            </ActionIcon>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={() => handleAddContract('SUBSCRIPTION')}
              variant="filled"
            >
              Abonnement
            </Button>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={() => handleAddContract('LOCATION')}
              variant="filled"
            >
              Location
            </Button>
          </Group>
        </Group>
      </Paper>

      {/* Error Alert */}
      {billingError && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Erreur"
          color="red"
          variant="light"
          style={{ margin: '1rem' }}
        >
          {billingError}
        </Alert>
      )}
   {/* Toolbar */}
          <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <ActionIcon
                  variant="subtle"
                  onClick={() => setSidebarOpen(true)}
                  title="Filtre avancé"
                >
                  <Icon path={mdiFilterVariant} size={0.8} />
                </ActionIcon>

                <Group gap="xs">
                  <Icon path={mdiMagnify} size={0.8} />
                  <TextInput
                    placeholder="Rechercher"
                    value={query.searchAll}
                    onChange={(event) => handleSearchChange(event.currentTarget.value)}
                    style={{ width: 300 }}
                  />
                </Group>
              </Group>

              <Group gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={() => handleAction('receipt')}
                  title="Reçu"
                  style={{ display: selectedItems.length > 0 ? 'block' : 'none' }}
                >
                  <Icon path={mdiReceiptText} size={0.8} />
                </ActionIcon>

                <ActionIcon
                  variant="subtle"
                  onClick={() => handleAction('view')}
                  title="Voir"
                >
                  <Icon path={mdiViewHeadline} size={0.8} />
                </ActionIcon>

                <ActionIcon
                  variant="subtle"
                  onClick={() => handleAction('reload')}
                  title="Actualiser"
                >
                  <Icon path={mdiReload} size={0.8} />
                </ActionIcon>

                <ActionIcon
                  variant="subtle"
                  onClick={handleExport}
                  title="Exporter Excel"
                >
                  <Icon path={mdiFileExcel} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Plus d'options">
                      <Icon path={mdiDotsVertical} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    {columns.map((column) => (
                      <Menu.Item
                        key={column.id}
                        leftSection={column.isShown ? <Icon path={mdiCheck} size={0.6} /> : null}
                        onClick={() => handleColumnVisibility(column.id)}
                        disabled={column.isRequired}
                      >
                        {column.label}
                      </Menu.Item>
                    ))}
                  </Menu.Dropdown>
                </Menu>
              </Group>
            </Group>
          </Paper>
      <Box style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Sidebar avec filtres */}
        <Drawer
          opened={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          position="left"
          size="400px"
          title="Filtres et Options"
          overlayProps={{ opacity: 0.5, blur: 4 }}
        >
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'filters')}>
            <Tabs.List>
              <Tabs.Tab value="filters">Filtre avancé</Tabs.Tab>
              <Tabs.Tab value="style">Règles de mise en forme</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="filters" pt="md">
              <ScrollArea style={{ height: 'calc(100vh - 200px)' }}>
                <Group justify="space-between" mb="md">
                  <Select
                    placeholder="Aucun filtre Enregistré"
                    data={[]}
                    disabled
                    style={{ flex: 1 }}
                  />
                </Group>

                <Group justify="flex-end" mb="md" gap="xs">
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFindReplace} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiMagnifyPlus} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="red" disabled>
                    <Icon path={mdiFilterRemoveOutline} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFilterOutline} size={0.8} />
                  </ActionIcon>
                </Group>

                {columns.map((column) => (
                  <Box key={column.id} mb="sm">
                    <Checkbox
                      label={column.label}
                      checked={column.isFilter}
                      onChange={(event) => handleColumnFilter(column.id, event.currentTarget.checked)}
                      size="sm"
                    />
                    {column.id !== columns[columns.length - 1].id && <Divider my="xs" />}
                  </Box>
                ))}
              </ScrollArea>
            </Tabs.Panel>

            <Tabs.Panel value="style" pt="md">
              <Text size="sm" c="dimmed">Règles de mise en forme</Text>
            </Tabs.Panel>
          </Tabs>
        </Drawer>

        {/* Contenu principal */}
        <Box style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
       

          {/* Tableau */}
          <Box style={{ flex: 1, overflow: 'auto' }}>
            {loading ? (
              <Box p="xl" style={{ textAlign: 'center' }}>
                <Loader size="lg" />
              </Box>
            ) : (
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: 50 }}>
                      <Checkbox
                        checked={selectedItems.length === displayItems.length && displayItems.length > 0}
                        indeterminate={selectedItems.length > 0 && selectedItems.length < displayItems.length}
                        onChange={handleSelectAll}
                        size="sm"
                      />
                    </Table.Th>
                    {columns.filter(col => col.isShown).map((column) => (
                      <Table.Th key={column.id} style={{ minWidth: 80 }}>
                        <Text size="sm" fw={500}>{column.label}</Text>
                      </Table.Th>
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {displayItems.map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onChange={() => handleSelectItem(item.id)}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm" ta={"left"}>{item.contractNumber}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.fileNumber}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.joinDate.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.date.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.startDate.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.referredBy}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.endDate.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.lastName}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.firstName}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.insurance}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.assignment}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.type}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.city}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.technician}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.totalAmount.toFixed(2)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Checkbox checked={item.validation} readOnly size="sm" />
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Box>

          {/* Pagination */}
          <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <Text size="sm">Page</Text>
                <Pagination
                  value={query.page}
                  onChange={handlePageChange}
                  total={totalPages}
                  size="sm"
                  disabled={loading}
                />
              </Group>

              <Group gap="md">
                <Text size="sm">Lignes par Page</Text>
                <Select
                  value={query.limit.toString()}
                  onChange={handleLimitChange}
                  data={['5', '10', '15', '20']}
                  size="sm"
                  style={{ width: 80 }}
                  disabled={loading}
                />
              </Group>

              <Text size="sm" c="dimmed">
                {total > 0 ? `${startItem} - ${endItem} de ${total}` : '0 - 0 de 0'}
              </Text>
            </Group>
          </Paper>
        </Box>
      </Box>
    </Paper>
  );
};

export default MesContrats;
