'use client';
import React, { useState } from 'react';
import {
  Group,
  Table,
  Text,
  Card,
  Box,
  TextInput,

  Checkbox,
  Button,
  Select,
  Pagination,
  ActionIcon,
  Tooltip,
  Modal,
} from '@mantine/core';
import {
  IconSearch,
 
  IconPlus,
  IconPrinter,
  IconDownload,
  IconEye,
  IconEdit,
  IconTrash,
  IconSettings,
} from '@tabler/icons-react';

// Import du composant Abonnement_form
import Abonnement_form from './Abonnement_form';

// Interface pour les données de contrat
interface ContratData {
  id: number;
  numeroContrat: string;
  numeroDossier: string;
  demarrage: string;
  dateFin: string;
  nom: string;
  prenom: string;
  assurance: string;
  affectation: string;
  type: string;
  ville: string;
  technicien: string;
  etat: string;
  montant: number;
}

const Mes_contrats = () => {
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');

  const [selectedContrats, setSelectedContrats] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [isAbonnementModalOpen, setIsAbonnementModalOpen] = useState(false);

  // Données d'exemple pour les contrats
  const contratsData: ContratData[] = [
    {
      id: 1,
      numeroContrat: 'Rechercher',
      numeroDossier: 'Rechercher',
      demarrage: 'Recherc...',
      dateFin: 'Recherc...',
      nom: 'Recherc...',
      prenom: 'Recherc...',
      assurance: 'Rechercher',
      affectation: 'Rechercher',
      type: 'Rech...',
      ville: 'R...',
      technicien: 'Recherc...',
      etat: 'Rech...',
      montant: 0.00
    },
    {
      id: 2,
      numeroContrat: '1',
      numeroDossier: '',
      demarrage: '13/04/2021',
      dateFin: '13/04/2022',
      nom: 'EL KANBI',
      prenom: 'ANAS',
      assurance: '',
      affectation: '',
      type: '',
      ville: '',
      technicien: '',
      etat: '',
      montant: 0.00
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredContrats = contratsData.filter(contrat =>
    contrat.numeroContrat.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contrat.numeroDossier.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contrat.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contrat.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contrat.assurance.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contrat.affectation.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gestion de la sélection
  const handleSelectContrat = (id: number) => {
    setSelectedContrats(prev =>
      prev.includes(id)
        ? prev.filter(contratId => contratId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedContrats.length === filteredContrats.length) {
      setSelectedContrats([]);
    } else {
      setSelectedContrats(filteredContrats.map(contrat => contrat.id));
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredContrats.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentContrats = filteredContrats.slice(startIndex, endIndex);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec onglets */}
     

      {/* Barre de recherche et boutons d'action */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          {/* Boutons Abonnement et Location à gauche */}
          <Group gap="xs" align="center">
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsAbonnementModalOpen(true)}
            >
              Abonnement
            </Button>
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Location
            </Button>
          </Group>

          {/* Barre de recherche et icônes d'action à droite */}
          <Group align="center" gap="sm">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />

            {/* Icônes d'action */}
            <Group gap="xs">
              <Tooltip label="Imprimer">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconPrinter size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Télécharger">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Voir">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconEye size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Modifier">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconEdit size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Supprimer">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Paramètres">
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                >
                  <IconSettings size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedContrats.length === filteredContrats.length && filteredContrats.length > 0}
                  indeterminate={selectedContrats.length > 0 && selectedContrats.length < filteredContrats.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N°.Contrat
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N° dossier
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Demar...
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date Fin
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Prénom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Assurance
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Affectation
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Type
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Ville
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Technicien
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                État
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                Mo...
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentContrats.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={14} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentContrats.map((contrat) => (
                <Table.Tr key={contrat.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      checked={selectedContrats.includes(contrat.id)}
                      onChange={() => handleSelectContrat(contrat.id)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.numeroContrat}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.numeroDossier}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.demarrage}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.dateFin}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.nom}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.prenom}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.assurance}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.affectation}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.type}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.ville}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.technicien}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {contrat.etat}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-right">
                    <Text size="sm" className="text-gray-800">
                      {contrat.montant.toFixed(2)}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 15)}
              data={['15', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {filteredContrats.length > 0
                ? `1 - 1 de 1`
                : '0 - 0 de 0'
              }
            </Text>
          </Group>

          <Pagination
            total={totalPages || 1}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création d'abonnement */}
      <Modal
        opened={isAbonnementModalOpen}
        onClose={() => setIsAbonnementModalOpen(false)}
        title="Nouvel Abonnement"
        size="95%"
        centered
        className="modal-abonnement"
      >
        <Abonnement_form />
      </Modal>
    </Box>
  );
};

export default Mes_contrats;
