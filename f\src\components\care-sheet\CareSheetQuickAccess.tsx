/**
 * Care Sheet Quick Access Modal
 * Provides quick access to care-sheet functionality from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  ActionIcon,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
} from '@mantine/core';
import {
  IconFileText,
  IconCalendarEvent,
  IconMedicalCross,
  IconCurrencyEuro,
  IconX,
  IconPlus,
  IconExternalLink,
} from '@tabler/icons-react';
import { useCareSheet } from '@/hooks/useCareSheet';
import CareSheetSummary from './CareSheetSummary';

// Import existing care-sheet components
import { MutuellesId } from '@/app/(dashboard)/care-sheet/MutuellesId';
import Liste_des_visites_modal from '@/app/(dashboard)/care-sheet/Liste_des_visites_modal';
import Liste_des_procedures_modal from '@/app/(dashboard)/care-sheet/Liste_des_procedures_modal';
import Liste_des_devis_modal from '@/app/(dashboard)/care-sheet/Liste_des_devis_modal';

interface CareSheetQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  patientId: string;
  patientName?: string;
  defaultTab?: 'summary' | 'mutuelles' | 'visits' | 'procedures' | 'devis';
  onNavigateToFullPage?: () => void;
}

const CareSheetQuickAccess: React.FC<CareSheetQuickAccessProps> = ({
  opened,
  onClose,
  patientId,
  patientName,
  defaultTab = 'summary',
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [subModalOpen, setSubModalOpen] = useState<{
    visits: boolean;
    procedures: boolean;
    devis: boolean;
  }>({
    visits: false,
    procedures: false,
    devis: false,
  });

  const {
    mutuelles,
    visits,
    procedures,
    devis,
    loading,
    getPatientCareSheetStats,
  } = useCareSheet({ patientId, autoFetch: opened });

  const stats = getPatientCareSheetStats(patientId);

  const handleViewDetails = (section: 'mutuelles' | 'visits' | 'procedures' | 'devis') => {
    if (section === 'mutuelles') {
      setActiveTab('mutuelles');
    } else {
      setSubModalOpen(prev => ({ ...prev, [section]: true }));
    }
  };

  const handleCloseSubModal = (section: 'visits' | 'procedures' | 'devis') => {
    setSubModalOpen(prev => ({ ...prev, [section]: false }));
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'summary':
        return (
          <CareSheetSummary
            patientId={patientId}
            patientName={patientName}
            compact={false}
            showActions={true}
            onViewDetails={handleViewDetails}
            onRefresh={() => {}}
          />
        );

      case 'mutuelles':
        return (
          <Card padding="md" radius="md" withBorder>
            <MutuellesId />
          </Card>
        );

      case 'visits':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconCalendarEvent size={20} />
                  <Text fw={600}>Visites du Patient</Text>
                  <Badge color="green">{visits.length}</Badge>
                </Group>
                <Button
                  size="xs"
                  leftSection={<IconPlus size={14} />}
                  onClick={() => setSubModalOpen(prev => ({ ...prev, visits: true }))}
                >
                  Voir Détails
                </Button>
              </Group>
              
              <ScrollArea h={300}>
                <Stack gap="xs">
                  {visits.map((visit) => (
                    <Card key={visit.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{visit.typeVisite}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(visit.date).toLocaleDateString()} - {visit.docteur}
                          </Text>
                          <Text size="xs">{visit.description}</Text>
                        </div>
                        <Badge size="sm" color={visit.statut === 'terminee' ? 'green' : 'yellow'}>
                          {visit.statut}
                        </Badge>
                      </Group>
                    </Card>
                  ))}
                  {visits.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune visite enregistrée pour ce patient
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'procedures':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconMedicalCross size={20} />
                  <Text fw={600}>Procédures du Patient</Text>
                  <Badge color="orange">{procedures.length}</Badge>
                </Group>
                <Button
                  size="xs"
                  leftSection={<IconPlus size={14} />}
                  onClick={() => setSubModalOpen(prev => ({ ...prev, procedures: true }))}
                >
                  Voir Détails
                </Button>
              </Group>
              
              <ScrollArea h={300}>
                <Stack gap="xs">
                  {procedures.map((procedure) => (
                    <Card key={procedure.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{procedure.typeProcedure}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(procedure.date).toLocaleDateString()} - {procedure.dentiste}
                          </Text>
                          <Text size="xs">{procedure.description}</Text>
                          {procedure.dent && (
                            <Text size="xs" c="blue">Dent: {procedure.dent}</Text>
                          )}
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge size="sm" color={procedure.statut === 'terminee' ? 'green' : 'yellow'}>
                            {procedure.statut}
                          </Badge>
                          <Text size="xs" fw={500}>{procedure.cout}€</Text>
                        </div>
                      </Group>
                    </Card>
                  ))}
                  {procedures.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune procédure enregistrée pour ce patient
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'devis':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconFileText size={20} />
                  <Text fw={600}>Devis du Patient</Text>
                  <Badge color="purple">{devis.length}</Badge>
                </Group>
                <Button
                  size="xs"
                  leftSection={<IconPlus size={14} />}
                  onClick={() => setSubModalOpen(prev => ({ ...prev, devis: true }))}
                >
                  Voir Détails
                </Button>
              </Group>
              
              <ScrollArea h={300}>
                <Stack gap="xs">
                  {devis.map((devisItem) => (
                    <Card key={devisItem.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{devisItem.numeroDevis}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(devisItem.date).toLocaleDateString()}
                          </Text>
                          <Text size="xs">{devisItem.description}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge size="sm" color={devisItem.statut === 'accepte' ? 'green' : 'yellow'}>
                            {devisItem.statut}
                          </Badge>
                          <Text size="xs" fw={500}>{devisItem.montantTotal}€</Text>
                        </div>
                      </Group>
                    </Card>
                  ))}
                  {devis.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun devis enregistré pour ce patient
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Modal
        opened={opened}
        onClose={onClose}
        title={
          <Group gap="xs">
            <IconFileText size={20} />
            <Text fw={600}>Dossier de Soins</Text>
            {patientName && <Text c="dimmed">- {patientName}</Text>}
          </Group>
        }
        size="xl"
        centered
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Stack gap="md">
          {/* Header Actions */}
          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              Accès rapide aux informations de soins du patient
            </Text>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>

          {/* Tabs */}
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'summary')}>
            <Tabs.List>
              <Tabs.Tab value="summary" leftSection={<IconFileText size={16} />}>
                Résumé
              </Tabs.Tab>
              <Tabs.Tab 
                value="mutuelles" 
                leftSection={<IconCurrencyEuro size={16} />}
                rightSection={<Badge size="xs" color="blue">{stats.totalMutuelles}</Badge>}
              >
                Mutuelles
              </Tabs.Tab>
              <Tabs.Tab 
                value="visits" 
                leftSection={<IconCalendarEvent size={16} />}
                rightSection={<Badge size="xs" color="green">{stats.totalVisits}</Badge>}
              >
                Visites
              </Tabs.Tab>
              <Tabs.Tab 
                value="procedures" 
                leftSection={<IconMedicalCross size={16} />}
                rightSection={<Badge size="xs" color="orange">{stats.totalProcedures}</Badge>}
              >
                Procédures
              </Tabs.Tab>
              <Tabs.Tab 
                value="devis" 
                leftSection={<IconFileText size={16} />}
                rightSection={<Badge size="xs" color="purple">{stats.totalDevis}</Badge>}
              >
                Devis
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value={activeTab} pt="md">
              {renderTabContent()}
            </Tabs.Panel>
          </Tabs>
        </Stack>
      </Modal>

      {/* Sub-modals for detailed views */}
      <Liste_des_visites_modal
        opened={subModalOpen.visits}
        onClose={() => handleCloseSubModal('visits')}
      />
      
      <Liste_des_procedures_modal
        opened={subModalOpen.procedures}
        onClose={() => handleCloseSubModal('procedures')}
      />
      
      <Liste_des_devis_modal
        opened={subModalOpen.devis}
        onClose={() => handleCloseSubModal('devis')}
      />
    </>
  );
};

export default CareSheetQuickAccess;
