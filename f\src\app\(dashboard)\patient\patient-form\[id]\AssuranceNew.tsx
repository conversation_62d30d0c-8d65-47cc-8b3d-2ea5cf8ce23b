'use client';
import { useState, useEffect } from 'react';
import { 
  Title, Divider,  Button, Modal, Group, Text, 
  ActionIcon, Stack, Loader, 
  TextInput, Select, NumberInput, Checkbox, Textarea,
  Grid, Badge
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import Icon from '@mdi/react';
import {
 mdiArrowLeft, mdiPlus, 
   mdiAccountSearch, 
} from '@mdi/js';
import { AssuranceContent } from "./AssuranceContentNew";
import { InsuranceTable } from "./AssurancesPatientNew";
import { Patient } from '@/types/typesCalendarPatient';
import patientService, { PatientInsurance } from '@/services/patientService';
import { patientFormService } from '@/services/patientFormService';
import { IconCheck, IconAlertCircle } from '@tabler/icons-react';

// Insurance Management Interfaces
interface FichePatientProps {
  onInsuredChange?: (isInsured: boolean) => void;
  // Additional props for staff and trigger options
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  openListDesPatient?: () => void;
}

interface InsuranceFormData {
  provider_name: string;
  policy_number: string;
  group_number: string;
  coverage_type: string;
  effective_date: Date | null;
  expiry_date: Date | null;
  dental_coverage: boolean;
  medical_coverage: boolean;
  copay_amount: number;
  deductible_amount: number;
  provider_phone: string;
  provider_website: string;
  notes: string;
}

type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
  onSimpleSubmit?: () => void;
  patient: Patient;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
  openListDesPatient: () => void;
};

export const Assurance = ({
  patient,
  onGoBack,

  patientId,

  onInsuredChange,
}: PatientActionsProps & FichePatientProps) => {
  // State management for insurance
  const [insurances, setInsurances] = useState<PatientInsurance[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAssurancesPatientVisible, setIsAssurancesPatientVisible] = useState(false);
  const [showAddInsuranceModal, setShowAddInsuranceModal] = useState(false);
  const [editingInsurance, setEditingInsurance] = useState<PatientInsurance | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [submitting, setSubmitting] = useState(false);
  
  // Form for adding/editing insurance
  const insuranceForm = useForm<InsuranceFormData>({
    initialValues: {
      provider_name: '',
      policy_number: '',
      group_number: '',
      coverage_type: '',
      effective_date: null,
      expiry_date: null,
      dental_coverage: false,
      medical_coverage: true,
      copay_amount: 0,
      deductible_amount: 0,
      provider_phone: '',
      provider_website: '',
      notes: '',
    },
  });

  // Load patient insurances on component mount
  useEffect(() => {
    const loadPatientInsurances = async () => {
      if (!patientId) return;
      
      try {
        setLoading(true);
        
        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');
        
        // Load patient insurances using patientFormService
        console.log(`🔄 Fetching insurances for patient: ${patientId}`);
        const patientInsurances = await patientFormService.getPatientInsurances(patientId);
        console.log('📥 Received insurances from API:', patientInsurances);

        // Handle empty or null response
        if (!patientInsurances || !Array.isArray(patientInsurances)) {
          console.warn('⚠️ No insurances found or invalid response format');
          setInsurances([]);
          if (onInsuredChange) {
            onInsuredChange(false);
          }
          return;
        }

        // Convert patientFormService format to component format
        const convertedInsurances: PatientInsurance[] = patientInsurances.map(insurance => ({
          id: insurance.id,
          patient: insurance.patient,
          provider_name: insurance.insurance_company,
          policy_number: insurance.policy_number,
          group_number: insurance.group_number,
          coverage_type: insurance.coverage_type,
          effective_date: insurance.effective_date,
          expiry_date: insurance.expiry_date,
          dental_coverage: insurance.coverage_type === 'dental',
          medical_coverage: insurance.coverage_type === 'medical' || !insurance.coverage_type,
          copay_amount: 0,
          deductible_amount: 0,
          provider_phone: '',
          provider_website: '',
          is_active: insurance.is_primary || true,
          notes: '',
          created_at: insurance.created_at,
          updated_at: insurance.updated_at,
        }));

        setInsurances(convertedInsurances);
        
        // Update parent component about insurance status
        if (onInsuredChange) {
          onInsuredChange(patientInsurances.length > 0);
        }
      } catch (error) {
        console.error('❌ Error loading patient insurances:', error);
        setDjangoStatus('disconnected');
        setInsurances([]); // Set empty array on error

        // Update parent component about insurance status
        if (onInsuredChange) {
          onInsuredChange(false);
        }

        notifications.show({
          title: 'Error',
          message: 'Failed to load patient insurances. Please try refreshing the page.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setLoading(false);
      }
    };

    loadPatientInsurances();
  }, [patientId, onInsuredChange]);

  // Handle insurance creation
  const handleCreateInsurance = async (values: InsuranceFormData) => {
    console.log('🎯 handleCreateInsurance called with values:', values);
    if (!patientId) {
      console.error('❌ No patient ID provided');
      return;
    }

    setSubmitting(true);
    try {
      // Helper function to convert date to string
      const formatDate = (date: Date | string | null | undefined): string => {
        if (!date) return '';
        if (date instanceof Date) {
          return date.toISOString().split('T')[0];
        }
        if (typeof date === 'string') {
          return date.split('T')[0];
        }
        return '';
      };

      const insuranceData = {
        ...values,
        effective_date: formatDate(values.effective_date),
        expiry_date: formatDate(values.expiry_date),
      };

      console.log('🔄 Creating patient insurance - Raw form values:', values);
      console.log('🔄 Creating patient insurance - Processed data:', insuranceData);

      // Validate required fields
      if (!insuranceData.provider_name) {
        notifications.show({
          title: 'Validation Error',
          message: 'Provider name is required',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Convert to patientFormService format and create
      const formServiceData = {
        patient: patientId,
        insurance_company: insuranceData.provider_name,
        policy_number: insuranceData.policy_number || '',
        group_number: insuranceData.group_number || '',
        coverage_type: insuranceData.dental_coverage ? 'dental' : 'medical',
        effective_date: insuranceData.effective_date || '',
        expiry_date: insuranceData.expiry_date || '',
        is_primary: true, // Default to primary
      };

      console.log('📤 Sending to API:', formServiceData);

      // Validate the API data has required fields
      if (!formServiceData.insurance_company) {
        console.error('❌ Missing insurance_company in API data');
        notifications.show({
          title: 'Data Error',
          message: 'Insurance company name is missing',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      const createdInsurance = await patientFormService.createPatientInsurance(patientId, formServiceData);

      // Convert back to component format with all required fields
      const newInsurance: PatientInsurance = {
        ...insuranceData,
        id: createdInsurance.id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      if (newInsurance) {
        setInsurances(prev => [...prev, newInsurance]);
        setShowAddInsuranceModal(false);
        insuranceForm.reset();
        
        notifications.show({
          title: 'Success',
          message: 'Insurance added successfully',
          color: 'green',
          icon: <IconCheck size={16} />,
        });

        // Update parent component
        if (onInsuredChange) {
          onInsuredChange(true);
        }
      }
    } catch (error) {
      console.error('Error creating insurance:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to add insurance',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle insurance update
  const handleUpdateInsurance = async (values: InsuranceFormData) => {
    if (!patientId || !editingInsurance?.id) return;

    setSubmitting(true);
    try {
      // Helper function to convert date to string
      const formatDate = (date: Date | string | null | undefined): string => {
        if (!date) return '';
        if (date instanceof Date) {
          return date.toISOString().split('T')[0];
        }
        if (typeof date === 'string') {
          return date.split('T')[0];
        }
        return '';
      };

      const insuranceData = {
        ...values,
        effective_date: formatDate(values.effective_date),
        expiry_date: formatDate(values.expiry_date),
      };

      console.log('🔄 Updating patient insurance - Raw form values:', values);
      console.log('🔄 Updating patient insurance - Processed data:', insuranceData);
      console.log('📝 Editing insurance ID:', editingInsurance.id);
      console.log('👤 Patient ID:', patientId);

      // Validate required fields
      if (!insuranceData.provider_name) {
        notifications.show({
          title: 'Validation Error',
          message: 'Provider name is required',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Convert to patientFormService format and update
      const formServiceData = {
        patient: patientId,
        insurance_company: insuranceData.provider_name,
        policy_number: insuranceData.policy_number || '',
        group_number: insuranceData.group_number || '',
        coverage_type: insuranceData.dental_coverage ? 'dental' : 'medical',
        effective_date: insuranceData.effective_date || '',
        expiry_date: insuranceData.expiry_date || '',
        is_primary: true,
      };

      console.log('📤 Sending update to API:', formServiceData);

      // Validate the API data has required fields
      if (!formServiceData.insurance_company) {
        console.error('❌ Missing insurance_company in update API data');
        notifications.show({
          title: 'Data Error',
          message: 'Insurance company name is missing',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Use patientFormService for consistency
      const updatedInsurance = await patientFormService.updatePatientInsurance(
        patientId,
        editingInsurance.id,
        formServiceData
      );

      if (updatedInsurance) {
        // Convert back to component format
        const convertedInsurance: PatientInsurance = {
          ...insuranceData,
          id: updatedInsurance.id,
          is_active: true,
          created_at: editingInsurance.created_at,
          updated_at: new Date().toISOString(),
        };

        setInsurances(prev =>
          prev.map(ins => ins.id === editingInsurance.id ? convertedInsurance : ins)
        );
        setEditingInsurance(null);
        setShowAddInsuranceModal(false);
        insuranceForm.reset();

        notifications.show({
          title: 'Success',
          message: 'Insurance updated successfully',
          color: 'green',
          icon: <IconCheck size={16} />,
        });
      }
    } catch (error) {
      console.error('Error updating insurance:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update insurance',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle insurance deletion
  const handleDeleteInsurance = async (insuranceId: string) => {
    if (!patientId) return;

    try {
      console.log('🗑️ Deleting patient insurance:', insuranceId);

      // Use patientFormService for consistency
      await patientFormService.deletePatientInsurance(patientId, insuranceId);

      setInsurances(prev => prev.filter(ins => ins.id !== insuranceId));

      notifications.show({
        title: 'Success',
        message: 'Insurance deleted successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // Update parent component
      const remainingInsurances = insurances.filter(ins => ins.id !== insuranceId);
      if (onInsuredChange) {
        onInsuredChange(remainingInsurances.length > 0);
      }
    } catch (error) {
      console.error('Error deleting insurance:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete insurance',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  // Refresh insurance data
  const refreshInsurances = async () => {
    if (!patientId) return;

    try {
      console.log('🔄 Refreshing insurance data...');
      const patientInsurances = await patientFormService.getPatientInsurances(patientId);

      // Convert patientFormService format to component format
      const convertedInsurances: PatientInsurance[] = patientInsurances.map(insurance => ({
        id: insurance.id,
        patient: insurance.patient,
        provider_name: insurance.insurance_company,
        policy_number: insurance.policy_number,
        group_number: insurance.group_number,
        coverage_type: insurance.coverage_type,
        effective_date: insurance.effective_date,
        expiry_date: insurance.expiry_date,
        dental_coverage: insurance.coverage_type === 'dental',
        medical_coverage: insurance.coverage_type === 'medical' || !insurance.coverage_type,
        copay_amount: 0,
        deductible_amount: 0,
        provider_phone: '',
        provider_website: '',
        is_active: insurance.is_primary || true,
        notes: '',
        created_at: insurance.created_at,
        updated_at: insurance.updated_at,
      }));

      setInsurances(convertedInsurances);

      // Update parent component about insurance status
      if (onInsuredChange) {
        onInsuredChange(convertedInsurances.length > 0);
      }
    } catch (error) {
      console.error('Error refreshing insurances:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to refresh insurance data',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  // Open edit modal
  const handleEditInsurance = (insurance: PatientInsurance) => {
    setEditingInsurance(insurance);
    insuranceForm.setValues({
      provider_name: insurance.provider_name,
      policy_number: insurance.policy_number,
      group_number: insurance.group_number || '',
      coverage_type: insurance.coverage_type || '',
      effective_date: insurance.effective_date ? new Date(insurance.effective_date) : null,
      expiry_date: insurance.expiry_date ? new Date(insurance.expiry_date) : null,
      dental_coverage: insurance.dental_coverage,
      medical_coverage: insurance.medical_coverage,
      copay_amount: insurance.copay_amount || 0,
      deductible_amount: insurance.deductible_amount || 0,
      provider_phone: insurance.provider_phone || '',
      provider_website: insurance.provider_website || '',
      notes: insurance.notes || '',
    });
    setShowAddInsuranceModal(true);
  };

  const toggleAssurancesPatient = () => {
    setIsAssurancesPatientVisible(!isAssurancesPatientVisible);
  };

  if (loading) {
    return (
      <Stack align="center" justify="center" h={400}>
        <Loader size="lg" />
        <Text>Loading patient insurances...</Text>
      </Stack>
    );
  }

  return (
    <div style={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Main Content */}
      <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
        {/* Header */}
        <Group justify="space-between" mb="md">
          <div>
            <Group gap="sm">
              <ActionIcon variant="light" onClick={onGoBack}>
                <Icon path={mdiArrowLeft} size={1} />
              </ActionIcon>
              <Title order={2}>Patient Insurance Management</Title>
            </Group>
            <Text c="dimmed" size="sm">
              Manage insurance information for {patient?.first_name} {patient?.last_name}
            </Text>
            <Group gap="xs" mt="xs">
              <Badge 
                color={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'yellow'}
                variant="light"
              >
                Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
              </Badge>
              <Badge color={insurances.length > 0 ? 'green' : 'gray'} variant="light">
                {insurances.length} Insurance{insurances.length !== 1 ? 's' : ''}
              </Badge>
            </Group>
          </div>
          
          <Group>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={() => {
                setEditingInsurance(null);
                insuranceForm.reset();
                setShowAddInsuranceModal(true);
              }}
              disabled={djangoStatus !== 'connected'}
            >
              Add Insurance
            </Button>
            <Button
              variant="light"
              leftSection={<Icon path={mdiAccountSearch} size={0.8} />}
              onClick={toggleAssurancesPatient}
            >
              View Details
            </Button>
          </Group>
        </Group>

        <Divider mb="md" />

        {/* Insurance Content */}
        <AssuranceContent 
          patient={patient}
          insurances={insurances}
          onEditInsurance={handleEditInsurance}
          onDeleteInsurance={handleDeleteInsurance}
          djangoConnected={djangoStatus === 'connected'}
        />
      </div>

      {/* Sidebar */}
      {isAssurancesPatientVisible && (
        <div style={{ 
          width: '400px', 
          borderLeft: '1px solid #e0e0e0', 
          backgroundColor: '#f8f9fa',
          overflowY: 'auto'
        }}>
          <InsuranceTable 
            patient={patient}
            insurances={insurances}
            onClose={toggleAssurancesPatient}
          />
        </div>
      )}

      {/* Add/Edit Insurance Modal */}
      <Modal
        opened={showAddInsuranceModal}
        onClose={() => {
          setShowAddInsuranceModal(false);
          setEditingInsurance(null);
          insuranceForm.reset();
        }}
        title={editingInsurance ? 'Edit Insurance' : 'Add New Insurance'}
        size="lg"
      >
        <form onSubmit={insuranceForm.onSubmit(editingInsurance ? handleUpdateInsurance : handleCreateInsurance)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Provider Name"
                  placeholder="Insurance Company Name"
                  required
                  {...insuranceForm.getInputProps('provider_name')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Policy Number"
                  placeholder="Policy Number"
                  required
                  {...insuranceForm.getInputProps('policy_number')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Group Number"
                  placeholder="Group Number (Optional)"
                  {...insuranceForm.getInputProps('group_number')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Coverage Type"
                  placeholder="Select coverage type"
                  data={[
                    { value: 'individual', label: 'Individual' },
                    { value: 'family', label: 'Family' },
                    { value: 'employee', label: 'Employee' },
                    { value: 'dependent', label: 'Dependent' },
                  ]}
                  {...insuranceForm.getInputProps('coverage_type')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <DatePickerInput
                  label="Effective Date"
                  placeholder="Select effective date"
                  {...insuranceForm.getInputProps('effective_date')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <DatePickerInput
                  label="Expiry Date"
                  placeholder="Select expiry date"
                  {...insuranceForm.getInputProps('expiry_date')}
                />
              </Grid.Col>
            </Grid>

            <Group>
              <Checkbox
                label="Medical Coverage"
                {...insuranceForm.getInputProps('medical_coverage', { type: 'checkbox' })}
              />
              <Checkbox
                label="Dental Coverage"
                {...insuranceForm.getInputProps('dental_coverage', { type: 'checkbox' })}
              />
            </Group>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Copay Amount ($)"
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  {...insuranceForm.getInputProps('copay_amount')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Deductible Amount ($)"
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  {...insuranceForm.getInputProps('deductible_amount')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Provider Phone"
                  placeholder="Phone Number"
                  {...insuranceForm.getInputProps('provider_phone')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Provider Website"
                  placeholder="Website URL"
                  {...insuranceForm.getInputProps('provider_website')}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Notes"
              placeholder="Additional notes about this insurance..."
              rows={3}
              {...insuranceForm.getInputProps('notes')}
            />

            <Group justify="flex-end">
              <Button
                variant="light"
                onClick={() => {
                  setShowAddInsuranceModal(false);
                  setEditingInsurance(null);
                  insuranceForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const currentValues = insuranceForm.values;
                  console.log('🐛 DEBUG - Current form values:', currentValues);
                  console.log('🐛 DEBUG - Form validation state:', insuranceForm.isValid());
                  console.log('🐛 DEBUG - Form errors:', insuranceForm.errors);
                  console.log('🐛 DEBUG - Patient ID:', patientId);
                  console.log('🐛 DEBUG - Editing insurance:', editingInsurance);
                }}
              >
                Debug
              </Button>
              <Button type="submit" loading={submitting}>
                {editingInsurance ? 'Update Insurance' : 'Add Insurance'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default Assurance;
