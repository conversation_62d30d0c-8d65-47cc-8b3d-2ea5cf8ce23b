'use client';
import React, { useState, useMemo } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  ActionIcon,
  Text,
  Select,
  Pagination,
  Checkbox,
  Modal,
  Stack,
  Grid,
  Badge,
  Textarea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconBriefcase,
} from '@tabler/icons-react';

// Interface pour les affaires
interface Affaire {
  id: string;
  code: string;
  libelle: string;
  statut: 'Actif' | 'Inactif' | 'En cours' | 'Terminé';
  dateCreation: Date;
  description?: string;
  responsable?: string;
}

const Listdesaffaires = () => {
  // États pour la gestion de l'interface
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [selectedAffaires, setSelectedAffaires] = useState<string[]>([]);
  const [opened, { open, close }] = useDisclosure(false);
  const [editingAffaire, setEditingAffaire] = useState<Affaire | null>(null);

  // Données d'exemple des affaires
  const [affaires, setAffaires] = useState<Affaire[]>([
    {
      id: '1',
      code: 'AFF001',
      libelle: 'Projet Pharmacie Centrale',
      statut: 'En cours',
      dateCreation: new Date('2024-01-15'),
      description: 'Mise en place du système de gestion de la pharmacie centrale',
      responsable: 'Dr. Ahmed',
    },
    {
      id: '2',
      code: 'AFF002',
      libelle: 'Inventaire Annuel',
      statut: 'Actif',
      dateCreation: new Date('2024-02-01'),
      description: 'Inventaire complet des médicaments',
      responsable: 'Mme. Fatima',
    },
    {
      id: '3',
      code: 'AFF003',
      libelle: 'Formation Personnel',
      statut: 'Terminé',
      dateCreation: new Date('2024-01-10'),
      description: 'Formation du personnel sur les nouveaux protocoles',
      responsable: 'Dr. Hassan',
    },
    {
      id: '4',
      code: 'AFF004',
      libelle: 'Audit Qualité',
      statut: 'Inactif',
      dateCreation: new Date('2024-03-01'),
      description: 'Audit qualité des processus pharmaceutiques',
      responsable: 'Dr. Aicha',
    },
    {
      id: '5',
      code: 'AFF005',
      libelle: 'Digitalisation',
      statut: 'En cours',
      dateCreation: new Date('2024-02-15'),
      description: 'Digitalisation des processus de commande',
      responsable: 'M. Youssef',
    },
  ]);

  // Formulaire pour ajouter/modifier une affaire
  const form = useForm({
    initialValues: {
      code: '',
      libelle: '',
      statut: 'Actif' as Affaire['statut'],
      dateCreation: new Date(),
      description: '',
      responsable: '',
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      libelle: (value) => (!value ? 'Le libellé est requis' : null),
    },
  });

  // Filtrage des affaires
  const filteredAffaires = useMemo(() => {
    return affaires.filter(affaire =>
      affaire.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      affaire.libelle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      affaire.statut.toLowerCase().includes(searchQuery.toLowerCase()) ||
      affaire.responsable?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [affaires, searchQuery]);

  // Pagination
  const totalPages = Math.ceil(filteredAffaires.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentAffaires = filteredAffaires.slice(startIndex, endIndex);

  // Fonction pour obtenir la couleur du badge selon le statut
  const getStatusColor = (statut: Affaire['statut']) => {
    switch (statut) {
      case 'Actif':
        return 'green';
      case 'En cours':
        return 'blue';
      case 'Terminé':
        return 'gray';
      case 'Inactif':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Gestion de la sélection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAffaires(currentAffaires.map(a => a.id));
    } else {
      setSelectedAffaires([]);
    }
  };

  const handleSelectAffaire = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedAffaires(prev => [...prev, id]);
    } else {
      setSelectedAffaires(prev => prev.filter(aId => aId !== id));
    }
  };

  // Gestion du formulaire
  const handleSubmit = (values: typeof form.values) => {
    const newAffaire: Affaire = {
      id: editingAffaire?.id || Date.now().toString(),
      code: values.code,
      libelle: values.libelle,
      statut: values.statut,
      dateCreation: values.dateCreation,
      description: values.description,
      responsable: values.responsable,
    };

    if (editingAffaire) {
      setAffaires(prev => prev.map(a => a.id === editingAffaire.id ? newAffaire : a));
      notifications.show({
        title: 'Succès',
        message: 'Affaire modifiée avec succès',
        color: 'green',
      });
    } else {
      setAffaires(prev => [...prev, newAffaire]);
      notifications.show({
        title: 'Succès',
        message: 'Nouvelle affaire ajoutée avec succès',
        color: 'green',
      });
    }

    form.reset();
    setEditingAffaire(null);
    close();
  };

  // Gestion de l'édition
  const handleEdit = (affaire: Affaire) => {
    setEditingAffaire(affaire);
    form.setValues({
      code: affaire.code,
      libelle: affaire.libelle,
      statut: affaire.statut,
      dateCreation: affaire.dateCreation,
      description: affaire.description || '',
      responsable: affaire.responsable || '',
    });
    open();
  };

  // Gestion de la suppression
  const handleDelete = (id: string) => {
    setAffaires(prev => prev.filter(a => a.id !== id));
    setSelectedAffaires(prev => prev.filter(aId => aId !== id));
    notifications.show({
      title: 'Succès',
      message: 'Affaire supprimée avec succès',
      color: 'red',
    });
  };

  // Ouvrir le modal pour ajouter une nouvelle affaire
  const handleAdd = () => {
    setEditingAffaire(null);
    form.reset();
    open();
  };

  return (
    <div className="w-full">
      <Paper p="md" withBorder>
        {/* Header */}
        <Group justify="space-between" mb="lg">
          <Group align="center">
            <IconBriefcase size={24} color="blue" />
            <Title order={3} c="blue">
              Liste des affaires
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleAdd}
            color="blue"
          >
            Affaire
          </Button>
        </Group>

        {/* Search Bar */}
        <Group mb="md">
          <TextInput
            placeholder="Rechercher"
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.currentTarget.value)}
            style={{ flex: 1 }}
          />
        </Group>

        {/* Table */}
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  checked={selectedAffaires.length === currentAffaires.length && currentAffaires.length > 0}
                  indeterminate={selectedAffaires.length > 0 && selectedAffaires.length < currentAffaires.length}
                  onChange={(event) => handleSelectAll(event.currentTarget.checked)}
                />
              </Table.Th>
              <Table.Th>Code</Table.Th>
              <Table.Th>Libellé</Table.Th>
              <Table.Th>Statut</Table.Th>
              <Table.Th>Responsable</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentAffaires.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} style={{ textAlign: 'center', padding: '2rem' }}>
                  <Text c="dimmed">Aucune affaire trouvée</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentAffaires.map((affaire) => (
                <Table.Tr key={affaire.id}>
                  <Table.Td>
                    <Checkbox
                      checked={selectedAffaires.includes(affaire.id)}
                      onChange={(event) => handleSelectAffaire(affaire.id, event.currentTarget.checked)}
                    />
                  </Table.Td>
                  <Table.Td>{affaire.code}</Table.Td>
                  <Table.Td>{affaire.libelle}</Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(affaire.statut)} variant="filled" size="sm">
                      {affaire.statut}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{affaire.responsable}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        color="blue"
                        variant="subtle"
                        onClick={() => handleEdit(affaire)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon
                        color="red"
                        variant="subtle"
                        onClick={() => handleDelete(affaire.id)}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>

        {/* Pagination */}
        {totalPages > 1 && (
          <Group justify="space-between" mt="md">
            <Text size="sm" c="dimmed">
              Affichage de {startIndex + 1} à {Math.min(endIndex, filteredAffaires.length)} sur {filteredAffaires.length} affaires
            </Text>
            <Pagination
              value={currentPage}
              onChange={setCurrentPage}
              total={totalPages}
              size="sm"
            />
          </Group>
        )}
      </Paper>

      {/* Modal pour ajouter/modifier une affaire */}
      <Modal
        opened={opened}
        onClose={close}
        title={editingAffaire ? 'Modifier l\'affaire' : 'Nouvelle affaire'}
        size="lg"
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder="Code de l'affaire"
                  {...form.getInputProps('code')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Statut"
                  placeholder="Sélectionner un statut"
                  data={[
                    { value: 'Actif', label: 'Actif' },
                    { value: 'Inactif', label: 'Inactif' },
                    { value: 'En cours', label: 'En cours' },
                    { value: 'Terminé', label: 'Terminé' },
                  ]}
                  {...form.getInputProps('statut')}
                  required
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Libellé"
              placeholder="Libellé de l'affaire"
              {...form.getInputProps('libelle')}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <DatePickerInput
                  label="Date de création"
                  placeholder="Sélectionner une date"
                  {...form.getInputProps('dateCreation')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Responsable"
                  placeholder="Nom du responsable"
                  {...form.getInputProps('responsable')}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Description"
              placeholder="Description de l'affaire"
              rows={4}
              {...form.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit" color="blue">
                {editingAffaire ? 'Modifier' : 'Ajouter'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default Listdesaffaires;