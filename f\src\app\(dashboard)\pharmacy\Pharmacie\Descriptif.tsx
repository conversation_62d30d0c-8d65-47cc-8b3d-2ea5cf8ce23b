'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Tabs,
  Text,
} from '@mantine/core';
import { RichTextEditor, Link } from '@mantine/tiptap';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { notifications } from '@mantine/notifications';
import {
  IconFileText,
  IconList,
  IconDeviceFloppy,
  IconPhoto,
  IconPaperclip,
  IconHistory,
  IconChartBar,
} from '@tabler/icons-react';

const Descriptif = () => {
  const [activeTab, setActiveTab] = useState<string>('descriptif');

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link,
    ],
    content: '',
    onUpdate: ({ editor }) => {
      // Handle content updates if needed
    },
  });

  const handleCancel = () => {
    notifications.show({
      title: 'Annulé',
      message: 'Les modifications ont été annulées',
      color: 'red',
    });
  };

  const handleRegisterAndQuit = () => {
    notifications.show({
      title: 'Enregistré et quitté',
      message: 'Le descriptif a été enregistré et fermé',
      color: 'green',
    });
  };

  const handleRegister = () => {
    notifications.show({
      title: 'Descriptif enregistré',
      message: 'Le descriptif a été enregistré',
      color: 'green',
    });
  };

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconFileText size={24} className="text-white" />
            <Title order={3} className="text-white">
              Fiche article
            </Title>
          </Group>
          <Button
            variant="filled"
            className="bg-blue-500 hover:bg-blue-600"
            leftSection={<IconList size={16} />}
          >
            Liste des articles
          </Button>
        </Group>
      </Paper>

      {/* Tabs Section */}
      <Paper p="md" mb="md" withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'descriptif')}>
          <Tabs.List mb="md">
            <Tabs.Tab value="fiche-article" leftSection={<IconPhoto size={16} />}>
              Fiche article
            </Tabs.Tab>
            <Tabs.Tab value="descriptif" leftSection={<IconFileText size={16} />}>
              Descriptif
            </Tabs.Tab>
            <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
              Pièces jointes
            </Tabs.Tab>
            <Tabs.Tab value="historique-achat" leftSection={<IconHistory size={16} />}>
              Historique Achat
            </Tabs.Tab>
            <Tabs.Tab value="historique-sortie" leftSection={<IconHistory size={16} />}>
              Historique Sortie
            </Tabs.Tab>
            <Tabs.Tab value="statistique" leftSection={<IconChartBar size={16} />}>
              Statistique
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="descriptif">
            {/* Rich Text Editor */}
            <RichTextEditor editor={editor} style={{ minHeight: '400px' }}>
              <RichTextEditor.Toolbar sticky stickyOffset={60}>
                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Bold />
                  <RichTextEditor.Italic />
                  <RichTextEditor.Underline />
                  <RichTextEditor.Strikethrough />
                  <RichTextEditor.ClearFormatting />
                  <RichTextEditor.Highlight />
                  <RichTextEditor.Code />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.H1 />
                  <RichTextEditor.H2 />
                  <RichTextEditor.H3 />
                  <RichTextEditor.H4 />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Blockquote />
                  <RichTextEditor.Hr />
                  <RichTextEditor.BulletList />
                  <RichTextEditor.OrderedList />
                  <RichTextEditor.Subscript />
                  <RichTextEditor.Superscript />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Link />
                  <RichTextEditor.Unlink />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.AlignLeft />
                  <RichTextEditor.AlignCenter />
                  <RichTextEditor.AlignJustify />
                  <RichTextEditor.AlignRight />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Undo />
                  <RichTextEditor.Redo />
                </RichTextEditor.ControlsGroup>
              </RichTextEditor.Toolbar>

              <RichTextEditor.Content />
            </RichTextEditor>
          </Tabs.Panel>

          <Tabs.Panel value="fiche-article">
            <Text>Fiche article content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="pieces-jointes">
            <Text>Pièces jointes content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="historique-achat">
            <Text>Historique Achat content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="historique-sortie">
            <Text>Historique Sortie content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="statistique">
            <Text>Statistique content</Text>
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Action Buttons */}
      <Paper p="md" withBorder>
        <Group justify="flex-end">
          <Button
            variant="filled"
            color="red"
            onClick={handleCancel}
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="gray"
            onClick={handleRegisterAndQuit}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer et quitter
          </Button>
          <Button
            variant="filled"
            color="gray"
            onClick={handleRegister}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer
          </Button>
        </Group>
      </Paper>
    </div>
  );
};

export default Descriptif;
