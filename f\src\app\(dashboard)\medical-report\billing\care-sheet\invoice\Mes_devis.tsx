'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  Textarea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconFileText,
  IconSearch,
  IconPlus,
  IconMinus,
  IconList,
  IconEye,
  IconMessageCircle,
} from '@tabler/icons-react';

const Mes_devis = () => {
  // États pour les filtres et données
  const [numeroDevis, setNumeroDevis] = useState('1');
  const [dateDevis, setDateDevis] = useState<Date | null>(new Date('2022-09-18'));
  const [validite, setValidite] = useState('1 jours');
  const [motif, setMotif] = useState('');
  const [beneficiaire, setBeneficiaire] = useState('Patient');
  const [nomComplet, setNomComplet] = useState('');
  const [commentaire, setCommentaire] = useState('');

  // États pour les éléments du devis
  const [elementsDevis, ] = useState([
    { code: 'Aucun élément trouvé', description: '', qte: 0, prix: 0, montant: 0 }
  ]);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Calcul du total
  const total = elementsDevis.reduce((sum, element) => sum + element.montant, 0);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconFileText size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Devis N°: {numeroDevis}
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Liste des devis">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconList size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* N° Devis */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  N°. Devis *
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={numeroDevis}
                  onChange={(e) => setNumeroDevis(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Date */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Date *
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  value={dateDevis}
                  onChange={setDateDevis}
                  size="xs"
                  className="w-full"
                  placeholder="Sélectionner une date"
                />
              </div>
            </div>

            {/* Validité */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Validité *
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={validite}
                  onChange={(e) => setValidite(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Motif */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Motif
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={motif}
                  onChange={(e) => setMotif(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Bénéficiaire */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Bénéficiaire
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={beneficiaire}
                  onChange={setBeneficiaire}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="Patient" label="Patient" />
                    <Radio value="Organisme" label="Organisme" />
                    <Radio value="Autre" label="Autre" />
                  </Stack>
                </Radio.Group>
              </div>
            </div>

            {/* Nom complet */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Nom complet *
                </Text>
              </div>
              <div className="p-2">
                <Group gap="xs">
                  <TextInput
                    value={nomComplet}
                    onChange={(e) => setNomComplet(e.target.value)}
                    size="xs"
                    className="flex-1"
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconSearch size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du contenu */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Barre d'outils avec boutons */}
          <div className="bg-gray-100 border-b border-gray-300 p-2">
            <Group gap="xs" className="flex flex-wrap">
              <Button
                size="xs"
                variant="light"
                color="blue"
                leftSection={<IconEye size={14} />}
                className="text-xs"
              >
                Visites
              </Button>
              <Button
                size="xs"
                variant="light"
                color="orange"
                leftSection={<IconSearch size={14} />}
                className="text-xs"
              >
                Actes
              </Button>
              <Button
                size="xs"
                variant="light"
                color="purple"
                leftSection={<IconFileText size={14} />}
                className="text-xs"
              >
                Autres
              </Button>
              <Button
                size="xs"
                variant="light"
                color="green"
                leftSection={<IconMessageCircle size={14} />}
                className="text-xs"
              >
                Commentaire
              </Button>
            </Group>
          </div>

          {/* Tableau des éléments */}
          <div className="flex-1 overflow-auto">
            <Table
              striped={false}
              highlightOnHover={true}
              withTableBorder={true}
              withColumnBorders={true}
              className="h-full"
            >
              <Table.Thead className="bg-gray-50 sticky top-0">
                <Table.Tr>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-32">
                    Code
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                    Description
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-20">
                    Qté
                  </Table.Th>
                  <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                    Prix
                  </Table.Th>
                  <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-24">
                    Montant
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {elementsDevis.map((element, index) => (
                  <Table.Tr key={index} className="hover:bg-gray-50">
                    <Table.Td className="border-r border-gray-300 text-sm">
                      {element.code}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 text-sm">
                      {element.description}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 text-sm text-center">
                      {element.qte}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-300 text-sm text-right">
                      {element.prix.toFixed(2)}
                    </Table.Td>
                    <Table.Td className="text-sm text-right">
                      {element.montant.toFixed(2)}
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </div>

          {/* Section pagination et total */}
          <div className="border-t border-gray-300 bg-gray-50 p-3">
            <Group justify="space-between" align="center">
              <Group gap="sm" align="center">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(Number(value) || 1)}
                  data={['1', '2', '3', '4', '5']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(Number(value) || 5)}
                  data={['5', '10', '20', '50']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                <Text size="sm" className="text-gray-600">K</Text>
                <Group gap="xs">
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    size="sm"
                    className="text-gray-500"
                  >
                    <IconMinus size={14} />
                  </ActionIcon>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    size="sm"
                    className="text-gray-500"
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </Group>

              <Text size="sm" fw={600} className="text-gray-800">
                Total : {total.toFixed(2)}
              </Text>
            </Group>
          </div>
        </div>
      </div>

      {/* Section commentaire */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <div className="border border-gray-300 rounded">
          <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
            <Text size="sm" fw={500} className="text-gray-700">
              Commentaire
            </Text>
          </div>
          <div className="p-2">
            <Textarea
              value={commentaire}
              onChange={(e) => setCommentaire(e.target.value)}
              size="xs"
              className="w-full"
              rows={3}
            />
          </div>
        </div>
      </Card>

      {/* Boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-gray-50 border-t border-gray-200"
      >
        <Group justify="flex-end" gap="sm">
          <Button
            variant="outline"
            color="red"
            size="sm"
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="blue"
            size="sm"
          >
            Enregistrer et quitter
          </Button>
          <Button
            color="blue"
            size="sm"
          >
            Enregistrer
          </Button>
        </Group>
      </Card>
    </Box>
  );
};

export default Mes_devis;
