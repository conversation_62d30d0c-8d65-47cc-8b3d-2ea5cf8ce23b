import React, { useState,  } from 'react';
import {
  Stack,
  Title,
  Paper,
  Tabs,
  Group,
  Text,
  Select,
  TextInput,
  Button,
  Grid,
  Checkbox,
  Divider,
  Table,
  ActionIcon,
  Tooltip,
Modal
} from '@mantine/core';
import {
  IconSettings,
  IconPlus,
  IconX,
  IconEdit,
  IconStarFilled,
  IconStar
} from '@tabler/icons-react';

// Types
interface CustomField {
  id: string;
  titre: string;
  slug: string;
  largeur: number;
  obligatoire: boolean;
  type: 'text' | 'select' | 'number' | 'date' | 'textarea';
}

interface RequiredField {
  id: string;
  numero: number;
  nomDuChamp: string;
  champObligatoire: boolean;
}

interface TableColumn {
  id: string;
  numero: number;
  nomDuChamp: string;
  affiche: boolean;
  triParDefaut: boolean;
}

interface PatientModuleSettings {
  defaultValues: {
    pays: string;
    ville: string;
    etatCivil: string;
    celibataire: string;
    prefecture: string;
    organisatrice: string;
    profession: string;
    employe: string;
    aucune: string;
  };
  formatAge: string;
  dictionnaireAlertes: string;
  dictionnaireNotifications: string;
  resumePatient: {
    ongletDefaut: string;
    ongletsDesactives: string[];
  };
  ficheMedicale: {
    afficherNombreEnfants: boolean;
    afficherGroupeSanguin: boolean;
    afficherMorphologie: boolean;
    afficherChampsPathologie: boolean;
    afficherChampsGrossesse: boolean;
  };
}

const Module_patient = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [modalOpened, setModalOpened] = useState(false);
  const [requiredFields, setRequiredFields] = useState<RequiredField[]>([
    { id: '1', numero: 1, nomDuChamp: 'Photo du patient', champObligatoire: false },
    { id: '2', numero: 2, nomDuChamp: 'Titre', champObligatoire: false },
    { id: '3', numero: 3, nomDuChamp: 'Nom', champObligatoire: false },
    { id: '4', numero: 4, nomDuChamp: 'Prénom', champObligatoire: false },
    { id: '5', numero: 5, nomDuChamp: 'Date de naissance', champObligatoire: false },
    { id: '6', numero: 6, nomDuChamp: 'Sexe', champObligatoire: false },
    { id: '7', numero: 7, nomDuChamp: 'CIN', champObligatoire: false },
    { id: '8', numero: 8, nomDuChamp: 'Téléphone', champObligatoire: false },
    { id: '9', numero: 9, nomDuChamp: 'État civil', champObligatoire: false },
    { id: '10', numero: 10, nomDuChamp: 'Profession', champObligatoire: false },
    { id: '11', numero: 11, nomDuChamp: 'Médecin traitant', champObligatoire: false },
    { id: '12', numero: 12, nomDuChamp: 'Adressé par', champObligatoire: false },
    { id: '13', numero: 13, nomDuChamp: 'Contact d\'urgence', champObligatoire: false },
    { id: '14', numero: 14, nomDuChamp: 'Contacte d\'urgence numéro', champObligatoire: false },
    { id: '15', numero: 15, nomDuChamp: 'Pays', champObligatoire: false },
    { id: '16', numero: 16, nomDuChamp: 'Ville', champObligatoire: false },
    { id: '17', numero: 17, nomDuChamp: 'Email', champObligatoire: false },
    { id: '18', numero: 18, nomDuChamp: 'Adresse', champObligatoire: false },
    { id: '19', numero: 19, nomDuChamp: 'Commentaire', champObligatoire: false },
    { id: '20', numero: 20, nomDuChamp: 'Motif de consultation', champObligatoire: false },
  ]);

  const [tableColumns, setTableColumns] = useState<TableColumn[]>([
    { id: '1', numero: 1, nomDuChamp: 'Date de création', affiche: true, triParDefaut: false },
    { id: '2', numero: 2, nomDuChamp: 'Nom', affiche: true, triParDefaut: false },
    { id: '3', numero: 3, nomDuChamp: 'Prénom', affiche: true, triParDefaut: false },
    { id: '4', numero: 4, nomDuChamp: 'Date de naissance', affiche: true, triParDefaut: false },
    { id: '5', numero: 5, nomDuChamp: 'Âge', affiche: true, triParDefaut: false },
    { id: '6', numero: 6, nomDuChamp: 'CIN', affiche: true, triParDefaut: false },
    { id: '7', numero: 7, nomDuChamp: 'Dernière visite', affiche: false, triParDefaut: false },
    { id: '8', numero: 8, nomDuChamp: 'Téléphone', affiche: false, triParDefaut: false },
    { id: '9', numero: 9, nomDuChamp: 'Ville', affiche: false, triParDefaut: false },
    { id: '10', numero: 10, nomDuChamp: 'Assurance', affiche: false, triParDefaut: false },
    { id: '11', numero: 11, nomDuChamp: 'N° de dossier papier', affiche: true, triParDefaut: false },
    { id: '12', numero: 12, nomDuChamp: 'État du Compte général', affiche: false, triParDefaut: false },
  ]);
  const [settings, setSettings] = useState<PatientModuleSettings>({
    defaultValues: {
      pays: 'MAROC',
      ville: 'CASABLANCA',
      etatCivil: '',
      celibataire: 'Employé(e)',
      prefecture: '',
      organisatrice: '',
      profession: 'Employé(e)',
      employe: '',
      aucune: 'Aucune',
    },
    formatAge: '(years) ans (months) mois (days) jours',
    dictionnaireAlertes: 'Alertes',
    dictionnaireNotifications: 'Notifications',
    resumePatient: {
      ongletDefaut: 'Démographiques',
      ongletsDesactives: ['Onglets désactivés'],
    },
    ficheMedicale: {
      afficherNombreEnfants: false,
      afficherGroupeSanguin: false,
      afficherMorphologie: false,
      afficherChampsPathologie: false,
      afficherChampsGrossesse: false,
    },
  });

  const handleDefaultValueChange = (field: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      defaultValues: {
        ...prev.defaultValues,
        [field]: value,
      },
    }));
  };

  const handleCheckboxChange = (field: string, checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      ficheMedicale: {
        ...prev.ficheMedicale,
        [field]: checked,
      },
    }));
  };

  const renderDefaultValueField = (
    label: string,
    field: string,
    type: 'select' | 'text' = 'text',
    options?: string[]
  ) => {
    const value = settings.defaultValues[field as keyof typeof settings.defaultValues];
    
    return (
      <Grid.Col span={6}>
        <Group gap="sm" align="flex-end">
          <div style={{ flex: 1 }}>
            <Text size="sm" fw={500} mb={5}>
              {label}
            </Text>
            {type === 'select' ? (
              <Select
                data={options || []}
                value={value}
                onChange={(val) => handleDefaultValueChange(field, val || '')}
                size="sm"
                styles={{
                  input: {
                    borderBottom: '2px solid #3b82f6',
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderRadius: 0,
                    backgroundColor: 'transparent',
                  },
                }}
              />
            ) : (
              <TextInput
                value={value}
                onChange={(event) => handleDefaultValueChange(field, event.currentTarget.value)}
                size="sm"
                styles={{
                  input: {
                    borderBottom: '2px solid #3b82f6',
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderRadius: 0,
                    backgroundColor: 'transparent',
                  },
                }}
              />
            )}
          </div>
          <Group gap={4}>
            <Button variant="subtle" size="xs" p={4}>
              <IconPlus size={12} />
            </Button>
            <Button variant="subtle" size="xs" p={4} color="red">
              <IconX size={12} />
            </Button>
          </Group>
        </Group>
      </Grid.Col>
    );
  };

  return (
    <Stack gap="lg" className="w-full" mb={60}>
      {/* Header */}
      <Paper 
        className="bg-blue-500 text-white" 
        p="md" 
        style={{ 
          borderRadius: '8px 8px 0 0',
          backgroundColor: '#3b82f6'
        }}
      >
        <Group align="center" gap="sm">
          <IconSettings size={20} className="text-white" />
          <Title order={4} className="text-white font-medium">
            Module patient
          </Title>
        </Group>
      </Paper>

      {/* Tabs */}
      <Paper 
        withBorder 
        style={{ 
          borderRadius: '0 0 8px 8px',
          borderTop: 'none'
        }}
      >
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'general')}>
          <Tabs.List>
            <Tabs.Tab value="general">
              Général
            </Tabs.Tab>
            <Tabs.Tab value="champs-personnalises">
              Champs personnalisés
            </Tabs.Tab>
            <Tabs.Tab value="champs-obligatoires">
              Champs obligatoires - tablette
            </Tabs.Tab>
            <Tabs.Tab value="colonnes-table">
              Colonnes de table patient
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="general" pt="md" px="md">
            <Stack gap="xl">
              {/* Valeurs par défaut */}
              <div>
                <Title order={5} mb="md" className="text-gray-700">
                  Valeurs par défaut
                </Title>
                <Grid>
                  {renderDefaultValueField('Pays', 'pays', 'select', ['MAROC', 'FRANCE', 'ESPAGNE'])}
                  {renderDefaultValueField('Ville', 'ville', 'select', ['CASABLANCA', 'RABAT', 'FES'])}
                  {renderDefaultValueField('État civil', 'etatCivil', 'select', ['Marié(e)', 'Célibataire', 'Divorcé(e)'])}
                  {renderDefaultValueField('Célibataire', 'celibataire', 'select', ['Employé(e)', 'Étudiant(e)', 'Retraité(e)'])}
                  {renderDefaultValueField('Préfecture', 'prefecture', 'select', ['Casablanca', 'Rabat', 'Fès'])}
                  {renderDefaultValueField('Organisatrice', 'organisatrice')}
                  {renderDefaultValueField('Profession', 'profession', 'select', ['Employé(e)', 'Médecin', 'Ingénieur'])}
                  {renderDefaultValueField('Employé(e)', 'employe')}
                  {renderDefaultValueField('Aucune', 'aucune', 'select', ['Aucune', 'Autre'])}
                </Grid>
              </div>

              <Divider />

              {/* Format d'âge */}
              <div>
                <Group gap="sm" align="flex-end">
                  <div style={{ flex: 1 }}>
                    <Text size="sm" fw={500} mb={5}>
                      Format d&apos;âge
                    </Text>
                    <TextInput
                      value={settings.formatAge}
                      onChange={(event) => setSettings(prev => ({ ...prev, formatAge: event.currentTarget.value }))}
                      size="sm"
                      styles={{
                        input: {
                          borderBottom: '2px solid #3b82f6',
                          borderTop: 'none',
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 0,
                          backgroundColor: 'transparent',
                        },
                      }}
                    />
                  </div>
                </Group>
              </div>

              <Divider />

              {/* Dictionnaires */}
              <Grid>
                <Grid.Col span={6}>
                  <Group gap="sm" align="flex-end">
                    <div style={{ flex: 1 }}>
                      <Text size="sm" fw={500} mb={5}>
                        Dictionnaire de description d&apos;alerte
                      </Text>
                      <Select
                        data={['Alertes', 'Warnings', 'Notifications']}
                        value={settings.dictionnaireAlertes}
                        onChange={(val) => setSettings(prev => ({ ...prev, dictionnaireAlertes: val || '' }))}
                        size="sm"
                        styles={{
                          input: {
                            borderBottom: '2px solid #3b82f6',
                            borderTop: 'none',
                            borderLeft: 'none',
                            borderRight: 'none',
                            borderRadius: 0,
                            backgroundColor: 'transparent',
                          },
                        }}
                      />
                    </div>
                    <Group gap={4}>
                      <Button variant="subtle" size="xs" p={4}>
                        <IconPlus size={12} />
                      </Button>
                      <Button variant="subtle" size="xs" p={4} color="red">
                        <IconX size={12} />
                      </Button>
                    </Group>
                  </Group>
                </Grid.Col>

                <Grid.Col span={6}>
                  <Group gap="sm" align="flex-end">
                    <div style={{ flex: 1 }}>
                      <Text size="sm" fw={500} mb={5}>
                        Dictionnaire de description des notifications
                      </Text>
                      <Select
                        data={['Notifications', 'Messages', 'Alertes']}
                        value={settings.dictionnaireNotifications}
                        onChange={(val) => setSettings(prev => ({ ...prev, dictionnaireNotifications: val || '' }))}
                        size="sm"
                        styles={{
                          input: {
                            borderBottom: '2px solid #3b82f6',
                            borderTop: 'none',
                            borderLeft: 'none',
                            borderRight: 'none',
                            borderRadius: 0,
                            backgroundColor: 'transparent',
                          },
                        }}
                      />
                    </div>
                    <Group gap={4}>
                      <Button variant="subtle" size="xs" p={4}>
                        <IconPlus size={12} />
                      </Button>
                      <Button variant="subtle" size="xs" p={4} color="red">
                        <IconX size={12} />
                      </Button>
                    </Group>
                  </Group>
                </Grid.Col>
              </Grid>

              <Divider />

              {/* Résumé patient */}
              <div>
                <Title order={5} mb="md" className="text-gray-700">
                  Résumé patient
                </Title>
                <Grid>
                  <Grid.Col span={6}>
                    <Text size="sm" fw={500} mb={5}>
                      Onglet par défaut
                    </Text>
                    <Select
                      data={['Démographiques', 'Médical', 'Contacts', 'Assurance']}
                      value={settings.resumePatient.ongletDefaut}
                      onChange={(val) => setSettings(prev => ({
                        ...prev,
                        resumePatient: {
                          ...prev.resumePatient,
                          ongletDefaut: val || 'Démographiques'
                        }
                      }))}
                      size="sm"
                      styles={{
                        input: {
                          borderBottom: '2px solid #3b82f6',
                          borderTop: 'none',
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 0,
                          backgroundColor: 'transparent',
                        },
                      }}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text size="sm" fw={500} mb={5}>
                      Onglets désactivés
                    </Text>
                    <Select
                      data={['Onglets désactivés', 'Médical', 'Contacts', 'Assurance']}
                      value={settings.resumePatient.ongletsDesactives[0] || ''}
                      onChange={(val) => setSettings(prev => ({
                        ...prev,
                        resumePatient: {
                          ...prev.resumePatient,
                          ongletsDesactives: val ? [val] : []
                        }
                      }))}
                      size="sm"
                      styles={{
                        input: {
                          borderBottom: '2px solid #3b82f6',
                          borderTop: 'none',
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 0,
                          backgroundColor: 'transparent',
                        },
                      }}
                    />
                  </Grid.Col>
                </Grid>
              </div>

              <Divider />

              {/* Fiche médicale */}
              <div>
                <Title order={5} mb="md" className="text-gray-700">
                  Fiche médicale
                </Title>
                <Stack gap="sm">
                  <Checkbox
                    label="Afficher le nombre des enfants"
                    checked={settings.ficheMedicale.afficherNombreEnfants}
                    onChange={(event) => handleCheckboxChange('afficherNombreEnfants', event.currentTarget.checked)}
                    size="sm"
                  />
                  <Checkbox
                    label="Afficher le groupe sanguin"
                    checked={settings.ficheMedicale.afficherGroupeSanguin}
                    onChange={(event) => handleCheckboxChange('afficherGroupeSanguin', event.currentTarget.checked)}
                    size="sm"
                  />
                  <Checkbox
                    label="Afficher la morphologie"
                    checked={settings.ficheMedicale.afficherMorphologie}
                    onChange={(event) => handleCheckboxChange('afficherMorphologie', event.currentTarget.checked)}
                    size="sm"
                  />
                  <Checkbox
                    label="Afficher le champs des pathologie"
                    checked={settings.ficheMedicale.afficherChampsPathologie}
                    onChange={(event) => handleCheckboxChange('afficherChampsPathologie', event.currentTarget.checked)}
                    size="sm"
                  />
                  <Checkbox
                    label="Afficher les champs de grossesse"
                    checked={settings.ficheMedicale.afficherChampsGrossesse}
                    onChange={(event) => handleCheckboxChange('afficherChampsGrossesse', event.currentTarget.checked)}
                    size="sm"
                  />
                </Stack>
              </div>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="champs-personnalises" pt="md" px="md">
            <Stack gap="md">
              {/* Header with Add button */}
              <Group justify="space-between" align="center">
                <Title order={5} className="text-gray-700">
                  Champs personnalisés
                </Title>
                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="filled"
                  color="blue"
                  size="sm"
                  onClick={() => {
                    // Handle add custom field
                    console.log('Add custom field');
                     setModalOpened(true)
                  }}
                >
                  Ajouter un champ
                </Button>
              </Group>

              {/* Custom Fields Table */}
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr className="bg-gray-50">
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Titre
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Slug
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Largeur
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Obligatoire
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Type
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center">
                      Actions
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {customFields.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={6} className="text-center py-8 text-gray-500">
                        Aucun champ personnalisé configuré
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    customFields.map((field) => (
                      <Table.Tr key={field.id} className="hover:bg-gray-50">
                        <Table.Td className="font-medium border-r border-gray-200">
                          {field.titre}
                        </Table.Td>
                        <Table.Td className="border-r border-gray-200">
                          {field.slug}
                        </Table.Td>
                        <Table.Td className="text-center border-r border-gray-200">
                          {field.largeur}%
                        </Table.Td>
                        <Table.Td className="text-center border-r border-gray-200">
                          <Text size="sm" className={field.obligatoire ? 'text-green-600' : 'text-red-500'}>
                            {field.obligatoire ? 'Oui' : 'Non'}
                          </Text>
                        </Table.Td>
                        <Table.Td className="text-center border-r border-gray-200">
                          <Text size="sm" className="capitalize">
                            {field.type}
                          </Text>
                        </Table.Td>
                        <Table.Td className="text-center">
                          <Group gap="xs" justify="center">
                            <Tooltip label="Modifier">
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                size="sm"
                                onClick={() => {
                                  // Handle edit action
                                  console.log('Edit field:', field.id);
                                }}
                              >
                                <IconEdit size={16} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Supprimer">
                              <ActionIcon
                                variant="subtle"
                                color="red"
                                size="sm"
                                onClick={() => {
                                  // Handle delete action
                                  setCustomFields(prev => prev.filter(f => f.id !== field.id));
                                }}
                              >
                                <IconX size={16} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="champs-obligatoires" pt="md" px="md">
            <Stack gap="md">
              {/* Header */}
              <Title order={5} className="text-gray-700">
                Champs obligatoires - tablette
              </Title>

              {/* Required Fields Table */}
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr className="bg-gray-50">
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Numéro
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Nom du champ
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center">
                      Champ obligatoire
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {requiredFields.map((field) => (
                    <Table.Tr key={field.id} className="hover:bg-gray-50">
                      <Table.Td className="text-center border-r border-gray-200">
                        {field.numero}
                      </Table.Td>
                      <Table.Td className="border-r border-gray-200">
                        {field.nomDuChamp}
                      </Table.Td>
                      <Table.Td className="text-center">
                        <Checkbox
                          checked={field.champObligatoire}
                          onChange={(event) => {
                            setRequiredFields(prev =>
                              prev.map(f =>
                                f.id === field.id
                                  ? { ...f, champObligatoire: event.currentTarget.checked }
                                  : f
                              )
                            );
                          }}
                          size="sm"
                        />
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="colonnes-table" pt="md" px="md">
            <Stack gap="md">
              {/* Header */}
              <Title order={5} className="text-gray-700">
                Colonnes de table patient
              </Title>

              {/* Table Columns Table */}
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr className="bg-gray-50">
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Nom du champ
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Affiché
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center">
                      Tri par défaut
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {tableColumns.map((column) => (
                    <Table.Tr key={column.id} className="hover:bg-gray-50">
                      <Table.Td className="border-r border-gray-200">
                        {column.nomDuChamp}
                      </Table.Td>
                      <Table.Td className="text-center border-r border-gray-200">
                        <Checkbox
                          checked={column.affiche}
                          onChange={(event) => {
                            setTableColumns(prev =>
                              prev.map(col =>
                                col.id === column.id
                                  ? { ...col, affiche: event.currentTarget.checked }
                                  : col
                              )
                            );
                          }}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td className="text-center">
                        {/* <Checkbox
                          checked={column.triParDefaut}
                          onChange={(event) => {
                            setTableColumns(prev =>
                              prev.map(col =>
                                col.id === column.id
                                  ? { ...col, triParDefaut: event.currentTarget.checked }
                                  : col
                              )
                            );
                          }}
                          size="sm"
                        /> */}
                         <ActionIcon
                    variant="subtle"
                    size="sm"
                    
                    className="text-gray-400 hover:text-yellow-500"
                  >
                    {column.triParDefaut ? (
                      <IconStarFilled size={16} className="text-green-500" />
                    ) : (
                      <IconStar size={16} className="text-gray-400" />
                    )}
                  </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Paper>
       {/* Modal for Edit specialty */}
            <Modal
              opened={modalOpened}
              onClose={() => setModalOpened(false)}
              title="Nouvelle spécialité"
              size="md"
              centered
            >
             
            </Modal>
    </Stack>
  );
};

export default Module_patient;
