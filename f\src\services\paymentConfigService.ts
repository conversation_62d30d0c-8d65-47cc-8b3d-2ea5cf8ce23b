import api from '../lib/api';

export interface PaymentConfiguration {
  id: number;
  name: string;
  description: string | null;
  bank_name: string;
  account_number: string;
  account_holder: string;
  basic_price_6months: number;
  basic_price_annual: number;
  standard_price_6months: number;
  standard_price_annual: number;
  premium_price_6months: number;
  premium_price_annual: number;
  basic_max_users: number;
  basic_max_assistants: number;
  standard_max_users: number;
  standard_max_assistants: number;
  premium_max_users: number;
  premium_max_assistants: number;
  enable_bank_transfer: boolean;
  enable_credit_card: boolean;
  enable_paypal: boolean;
  support_email: string;
  sales_email: string;
  updated_at: string;
}

const paymentConfigService = {
  /**
   * Get the active payment configuration
   */
  getActiveConfig: async (): Promise<PaymentConfiguration> => {
    try {
      const response = await api.get('/api/auth/payment-config/active/');
      return response.data;
    } catch (error) {
      console.error('Error fetching payment configuration:', error);
      
      // Return default configuration if API call fails
      return {
        id: 0,
        name: 'Default Configuration',
        description: 'Default payment configuration',
        bank_name: 'Medical Bank',
        account_number: '1234-5678-9012-3456',
        account_holder: 'Medical Portal Inc.',
        basic_price_6months: 199.00,
        basic_price_annual: 299.00,
        standard_price_6months: 299.00,
        standard_price_annual: 399.00,
        premium_price_6months: 399.00,
        premium_price_annual: 499.00,
        basic_max_users: 3,
        basic_max_assistants: 2,
        standard_max_users: 5,
        standard_max_assistants: 3,
        premium_max_users: 10,
        premium_max_assistants: 5,
        enable_bank_transfer: true,
        enable_credit_card: true,
        enable_paypal: false,
        support_email: '<EMAIL>',
        sales_email: '<EMAIL>',
        updated_at: new Date().toISOString()
      };
    }
  },
  
  /**
   * Get all payment configurations (admin only)
   */
  getAllConfigs: async (): Promise<PaymentConfiguration[]> => {
    const response = await api.get('/api/auth/payment-config/');
    return response.data;
  },
  
  /**
   * Create a new payment configuration (admin only)
   */
  createConfig: async (data: Partial<PaymentConfiguration>): Promise<PaymentConfiguration> => {
    const response = await api.post('/api/auth/payment-config/', data);
    return response.data;
  },
  
  /**
   * Update a payment configuration (admin only)
   */
  updateConfig: async (id: number, data: Partial<PaymentConfiguration>): Promise<PaymentConfiguration> => {
    const response = await api.patch(`/api/auth/payment-config/${id}/`, data);
    return response.data;
  },
  
  /**
   * Set a payment configuration as active (admin only)
   */
  setActiveConfig: async (id: number): Promise<PaymentConfiguration> => {
    const response = await api.post(`/api/auth/payment-config/${id}/set-active/`);
    return response.data;
  }
};

export default paymentConfigService;
