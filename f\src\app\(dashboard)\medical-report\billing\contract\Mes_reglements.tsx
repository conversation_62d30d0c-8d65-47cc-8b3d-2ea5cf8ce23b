'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  TextInput,
  Button,
  Checkbox,
  Tabs,
  Drawer,
  ScrollArea,
  Divider,
  Menu,
  Pagination,
  Select,
  Box,
  Loader,
  Alert,
  // Tooltip
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiPlus,
  mdiFilterVariant,
  mdiMagnify,
  mdiReload,
  mdiFileExcel,
  mdiDotsVertical,
  mdiFindReplace,
  mdiMagnifyPlus,
  mdiFilterRemoveOutline,
  mdiFilterOutline,
  mdiCheck
} from '@mdi/js';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconRefresh, IconTrash } from '@tabler/icons-react';

// Import billing hook
import { useBilling } from '@/hooks/useBilling';
import { PaymentFilters } from './types/filters';

// Types et interfaces
interface PaymentColumn {
  id: string;
  label: string;
  isFilter: boolean;
  isShown: boolean;
  isRequired?: boolean;
  type?: 'text' | 'date' | 'number' | 'boolean';
}

interface PaymentQuery {
  searchAll: string;
  page: number;
  limit: number;
  filters: PaymentFilters;
  search: { [key: string]: string };
}

interface PaymentItem {
  id: string;
  paymentNumber: string;
  date: Date;
  beneficiary: string;
  payer: string;
  totalAmount: number;
  consumedAmount: number;
  outstandingAmount: number;
}

interface MesReglementsProps {
  loading?: boolean;
  items?: PaymentItem[];
  total?: number;
  onQueryChange?: (query: PaymentQuery) => void;
  onAddPayment?: () => void;
  onExport?: (format: 'excel') => void;
  onAction?: (action: string, items: PaymentItem[]) => void;
}

export const MesReglements: React.FC<MesReglementsProps> = ({
  loading = false,
  items = [],
  total = 0,
  onQueryChange,
  onAddPayment,
  onExport,
  onAction
}) => {
  // Use billing hook for backend integration
  const {
    payments,
    loading: billingLoading,
    error: billingError,
    refreshAll,
    createPayment,
  } = useBilling({
    autoFetch: true,
    dataTypes: ['payments']
  });

  // États locaux
  const [query, setQuery] = useState<PaymentQuery>({
    searchAll: '',
    page: 1,
    limit: 15,
    filters: {},
    search: {}
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('filters');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refreshAll();
      notifications.show({
        title: 'Actualisation',
        message: 'Données des règlements actualisées avec succès',
        color: 'blue'
      });
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de l\'actualisation des règlements',
        color: 'red'
      });
    }
  };

  // Convert payments to PaymentItem format
  const paymentItemsFromBackend: PaymentItem[] = payments.map(payment => ({
    id: payment.id,
    paymentNumber: payment.reference || `PAY-${payment.id}`,
    date: new Date(payment.date),
    beneficiary: 'Patient', // Default value
    payer: 'Patient', // Default value
    totalAmount: payment.amount,
    consumedAmount: payment.amount,
    outstandingAmount: 0,
  }));

  // Use backend data or fallback to props
  const displayTotal = paymentItemsFromBackend.length > 0 ? paymentItemsFromBackend.length : total;
  const displayLoading = billingLoading || loading;

  // Configuration des colonnes
  const columns: PaymentColumn[] = [
    { id: 'paymentNumber', label: 'N°.Réglement', isFilter: false, isShown: true, isRequired: true },
    { id: 'date', label: 'Date', isFilter: false, isShown: true, type: 'date' },
    { id: 'beneficiary', label: 'Bénéficiaire', isFilter: false, isShown: true },
    { id: 'payer', label: 'Payeur', isFilter: false, isShown: true },
    { id: 'totalAmount', label: 'Montant dû', isFilter: false, isShown: true, type: 'number' },
    { id: 'consumedAmount', label: 'Montant consommé', isFilter: false, isShown: true, type: 'number' },
    { id: 'outstandingAmount', label: 'Reliquat', isFilter: false, isShown: true, type: 'number' }
  ];

  // Données d'exemple (fallback)
  const sampleItems: PaymentItem[] = [
    {
      id: '1',
      paymentNumber: 'REG-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      payer: 'CPAM',
      totalAmount: 1250.00,
      consumedAmount: 850.00,
      outstandingAmount: 400.00
    }
  ];

  // Use backend data, then props, then sample data as fallback
  const finalDisplayItems = paymentItemsFromBackend.length > 0 ? paymentItemsFromBackend :
                           items.length > 0 ? items : sampleItems;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<PaymentQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleSearchChange = (value: string) => {
    handleQueryChange({ searchAll: value, page: 1 });
  };

  const handlePageChange = (page: number) => {
    handleQueryChange({ page });
  };

  const handleLimitChange = (limit: string | null) => {
    if (limit) {
      handleQueryChange({ limit: parseInt(limit), page: 1 });
    }
  };

  const handleAddPayment = () => {
    console.log('Ajouter règlement');
    onAddPayment?.();
  };

  const handleExport = () => {
    console.log('Exporter Excel');
    onExport?.('excel');
  };

  const handleAction = (action: string) => {
    console.log('Action:', action, 'Items:', selectedItems);
    onAction?.(action, finalDisplayItems.filter(item => selectedItems.includes(item.id)));
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === finalDisplayItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(finalDisplayItems.map(item => item.id));
    }
  };

  const handleColumnFilter = (columnId: string, isFilter: boolean) => {
    console.log('Filtre colonne:', columnId, isFilter);
  };

  const handleColumnVisibility = (columnId: string) => {
    console.log('Visibilité colonne:', columnId);
  };

  const totalPages = Math.ceil(total / query.limit);
  const startItem = (query.page - 1) * query.limit + 1;
  const endItem = Math.min(query.page * query.limit, total);

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header avec bouton d'ajout */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#f8f9fa' }}>
        <Group justify="space-between" gap="md">
          <Group gap="md">
            <Text size="lg" fw={600}>Mes Règlements</Text>
            {displayLoading && <Loader size="sm" />}
          </Group>
          <Group gap="md">
            <ActionIcon
              variant="subtle"
              color="blue"
              size="lg"
              onClick={handleRefresh}
              loading={displayLoading}
              title="Actualiser"
            >
              <IconRefresh size={18} />
            </ActionIcon>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              onClick={handleAddPayment}
              variant="filled"
            >
              Réglement
            </Button>
          </Group>
        </Group>
      </Paper>

      {/* Error Alert */}
      {billingError && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Erreur"
          color="red"
          variant="light"
          style={{ margin: '1rem' }}
        >
          {billingError}
        </Alert>
      )}

      <Box style={{  overflow: 'hidden' }}>
        {/* Sidebar avec filtres */}
        <Drawer
          opened={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          position="left"
          size="400px"
          title="Filtres et Options"
          overlayProps={{ opacity: 0.5, blur: 4 }}
        >
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'filters')}>
            <Tabs.List>
              <Tabs.Tab value="filters">Filtre avancé</Tabs.Tab>
              <Tabs.Tab value="style">Règles de mise en forme</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="filters" pt="md">
              <ScrollArea style={{ height: 'calc(100vh - 200px)' }}>
                <Group justify="space-between" mb="md">
                  <Select
                    placeholder="Aucun filtre Enregistré"
                    data={[]}
                    disabled
                    style={{ flex: 1 }}
                  />
                </Group>

                <Group justify="flex-end" mb="md" gap="xs">
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFindReplace} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiMagnifyPlus} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="red" disabled>
                    <Icon path={mdiFilterRemoveOutline} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFilterOutline} size={0.8} />
                  </ActionIcon>
                </Group>

                {columns.map((column) => (
                  <Box key={column.id} mb="sm">
                    <Checkbox
                      label={column.label}
                      checked={column.isFilter}
                      onChange={(event) => handleColumnFilter(column.id, event.currentTarget.checked)}
                      size="sm"
                    />
                    {column.id !== columns[columns.length - 1].id && <Divider my="xs" />}
                  </Box>
                ))}
              </ScrollArea>
            </Tabs.Panel>

            <Tabs.Panel value="style" pt="md">
              <Text size="sm" c="dimmed">Règles de mise en forme</Text>
            </Tabs.Panel>
          </Tabs>
        </Drawer>
 </Box>
        {/* Contenu principal */}
        <Box style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Toolbar */}
          <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <ActionIcon
                  variant="subtle"
                  onClick={() => setSidebarOpen(true)}
                  title="Filtre avancé"
                >
                  <Icon path={mdiFilterVariant} size={0.8} />
                </ActionIcon>

                <Group gap="xs">
                  <Icon path={mdiMagnify} size={0.8} />
                  <TextInput
                    placeholder="Rechercher"
                    value={query.searchAll}
                    onChange={(event) => handleSearchChange(event.currentTarget.value)}
                    style={{ width: 300 }}
                  />
                </Group>
              </Group>

              <Group gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={() => handleAction('reload')}
                  title="Actualiser"
                >
                  <Icon path={mdiReload} size={0.8} />
                </ActionIcon>

                <ActionIcon
                  variant="subtle"
                  onClick={handleExport}
                  title="Exporter Excel"
                >
                  <Icon path={mdiFileExcel} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Plus d'options">
                      <Icon path={mdiDotsVertical} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    {columns.map((column) => (
                      <Menu.Item
                        key={column.id}
                        leftSection={column.isShown ? <Icon path={mdiCheck} size={0.6} /> : null}
                        onClick={() => handleColumnVisibility(column.id)}
                        disabled={column.isRequired}
                      >
                        {column.label}
                      </Menu.Item>
                    ))}
                  </Menu.Dropdown>
                </Menu>
              </Group>
            </Group>
          </Paper>

          {/* Tableau */}
          <Box style={{ flex: 1, overflow: 'auto' }}>
            {loading ? (
              <Box p="xl" style={{ textAlign: 'center' }}>
                <Loader size="lg" />
              </Box>
            ) : (
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: 50 }}>
                      <Checkbox
                        checked={selectedItems.length === finalDisplayItems.length && finalDisplayItems.length > 0}
                        indeterminate={selectedItems.length > 0 && selectedItems.length < finalDisplayItems.length}
                        onChange={handleSelectAll}
                        size="sm"
                      />
                    </Table.Th>
                    {columns.filter(col => col.isShown).map((column) => (
                      <Table.Th key={column.id} style={{ minWidth: 120 }}>
                        <Text size="sm" fw={500}>{column.label}</Text>
                      </Table.Th>
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {finalDisplayItems.map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onChange={() => handleSelectItem(item.id)}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.paymentNumber}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.date.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.beneficiary}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.payer}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.totalAmount.toFixed(2)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.consumedAmount.toFixed(2)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.outstandingAmount.toFixed(2)}</Text>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          {/* Pagination */}
          <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <Text size="sm">Page</Text>
                <Pagination
                  value={query.page}
                  onChange={handlePageChange}
                  total={totalPages}
                  size="sm"
                  disabled={loading}
                />
              </Group>

              <Group gap="md">
                <Text size="sm">Lignes par Page</Text>
                <Select
                  value={query.limit.toString()}
                  onChange={handleLimitChange}
                  data={['5', '10', '15', '20']}
                  size="sm"
                  style={{ width: 80 }}
                  disabled={loading}
                />
              </Group>

              <Text size="sm" c="dimmed">
                {total > 0 ? `${startItem} - ${endItem} de ${total}` : '0 - 0 de 0'}
              </Text>
            </Group>
          </Paper>
        </Box>
      </Box>
    </Paper>
  );
};

export default MesReglements;
