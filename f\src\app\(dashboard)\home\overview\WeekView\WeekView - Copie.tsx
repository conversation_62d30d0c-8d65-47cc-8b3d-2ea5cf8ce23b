"use client";
import { useEffect,useCallback, useState } from "react";
import moment from "moment";
import "moment/locale/fr";
import { Group, Button,Text } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiChevronLeft, mdiChevronRight, } from '@mdi/js';
import "./WeekView.css";
import SelectLaSemaine from "./SelectLaSemaine";
// Types
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
}

// Utility functions
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true
  });
};

const getWeekDays = (date: Date): Date[] => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day;
  start.setDate(diff);
  
  const days = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(start);
    day.setDate(start.getDate() + i);
    days.push(day);
  }
  return days;
};

const getWorkWeekDays = (date: Date): Date[] => {
  return getWeekDays(date).slice(1, 6); // Monday to Friday
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const getEventsForDay = (events: CalendarEvent[], day: Date): CalendarEvent[] => {
  return events.filter(event => isSameDay(event.start, day));
};

// Event Component
const EventComponent = ({ 
  event, 
  onDragStart,
  onEdit 
}: { 
  event: CalendarEvent;
  onDragStart?: (event: CalendarEvent) => void;
  onEdit?: (event: CalendarEvent) => void;
}) => (
  <div
    draggable={!!onDragStart}
    onDragStart={() => onDragStart?.(event)}
    onClick={() => onEdit?.(event)}
    className="p-1 mb-1 text-xs rounded cursor-pointer hover:opacity-80 transition-opacity"
    style={{ backgroundColor: event.color || '#3b82f6', color: 'white' }}
  >
    <div className="font-medium truncate">{event.title}</div>
    <div className="text-xs opacity-90">
      {formatTime(event.start)} - {formatTime(event.end)}
    </div>
  </div>
);

// Week/Work Week View Component
const WeekView = ({ 
  currentDate, 
  events, 
  isWorkWeek = false,
  onTimeSlotClick ,
  onNavigate,
  label,
}: {
  currentDate: Date;
  events: CalendarEvent[];
  isWorkWeek?: boolean;
  onTimeSlotClick: (date: Date, hour: number) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
  label:string,
}) => {
  const days = isWorkWeek ? getWorkWeekDays(currentDate) : getWeekDays(currentDate);
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const today = new Date();


// Handle navigation
  const handleNavigate = useCallback((direction: 'prev' | 'next' | 'today') => {
    onNavigate?.(direction);
  }, [onNavigate]);
   const messages = {
    today: "Aujourd’hui",
    previous: "Précédent",
    next: "Suivant",
    month: "Mois",
    week: "Semaine",
    day: "Jour",
    agenda: "Agenda",
    noEventsInRange: "Aucun événement prévu",
  };
   const [selectedDateW, setselectedDateW] = useState(new Date());
      useEffect(() => {
        setselectedDateW(moment(label, "DD MMMM YYYY").toDate());
      }, [label]);
        const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value;
 
  };
   // Get current date title
  const getCurrentDateTitle = () => {
    
       const startOfWeek = new Date(currentDate);
        const day = startOfWeek.getDay();
        const diff = startOfWeek.getDate() - day;
        startOfWeek.setDate(diff);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${endOfWeek.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}`;
    
    
  };
  return (
    <>
        {/* Navigation Header */}
           <Group justify="space-between" p={4} className="border-2 border-[#3799CE] rounded-t-md bg-[#3799CE]">
             <div className="mx-auto w-[70px]">
                      <Button
                        variant="filled"
                        size="sm"
                        onClick={() => handleNavigate("today")}
                        className="ho rounded-md bg-[var(--color-gray-tab)] text-[var(--text-tab)]"
                        fz="xs"
                      >
                        {messages.today}
                      </Button>
                    </div>
  <div className="flex items-center">
                   <button className="btn-sm  mr-1" onClick={() => handleNavigate('prev')}> <Icon path={mdiChevronLeft} size={1} color={"white"}/></button>
                   <Text fw={550} c={"var(--text-daisy-white)"}> {getCurrentDateTitle()}</Text>
                   <button className="btn-sm  ml-1" onClick={() => handleNavigate('next')}><Icon path={mdiChevronRight} size={1} color={"white"}/></button>
                 </div>
  <SelectLaSemaine
              date={selectedDateW}
              setter={handleDateChange}
              label={moment(selectedDateW).format("DD MMMM YYYY")} // Ensure this represents the start of the week
            /> 
           
    </Group>
    <div className="flex flex-col h-full">
      <div className="flex border-b ">
        <div className="w-16 p-2 border-r"></div>
        {days.map(day => (
          <div 
            key={day.toDateString()} 
            className={`flex-1 p-2 text-center border-r ${
              isSameDay(day, today) ? 'bg-blue-50' : ''
            }`}
          >
            <div className="font-semibold">{day.toLocaleDateString('en-US', { weekday: 'short' })}</div>
            <div className="text-sm">{day.getDate()}</div>
          </div>
        ))}
      </div>
      <div className="flex-1 overflow-y-auto">
        {hours.map(hour => (
          <div key={hour} className="flex border-b" style={{ minHeight: '60px' }}>
            <div className="w-16 p-1 text-xs text-gray-500 border-r">
              {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
            </div>
            {days.map(day => {
              const dayEvents = getEventsForDay(events, day).filter(event => 
                event.start.getHours() === hour
              );
              return (
                <div 
                  key={`${day.toDateString()}-${hour}`}
                  className="flex-1 p-1 border-r hover:bg-gray-50 cursor-pointer"
                  onClick={() => onTimeSlotClick(day, hour)}
                >
                  {dayEvents.map(event => (
                    <EventComponent key={event.id} event={event} />
                  ))}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
    </>
  );
};

export default WeekView;
