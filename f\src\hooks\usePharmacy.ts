/**
 * Custom hook for managing pharmacy data
 * Provides easy access to medications, inventory, purchases, sales, and analytics
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  pharmacyService, 
  Medication,
  InventoryItem,
  PurchaseOrder,
  SalesOrder,
  Supplier,
  MedicationFamily,
  StockMovement,
  PharmacyAnalytics,
  PharmacySummary
} from '@/services/pharmacyService';

interface UsePharmacyOptions {
  dateRange?: { start: string; end: string };
  autoFetch?: boolean;
  refreshInterval?: number;
  dataTypes?: string[];
}

interface UsePharmacyReturn {
  // Data
  medications: Medication[];
  inventory: InventoryItem[];
  purchaseOrders: PurchaseOrder[];
  salesOrders: SalesOrder[];
  suppliers: Supplier[];
  medicationFamilies: MedicationFamily[];
  stockMovements: StockMovement[];
  analytics: PharmacyAnalytics | null;
  summary: PharmacySummary | null;
  
  // Loading states
  loading: boolean;
  medicationsLoading: boolean;
  inventoryLoading: boolean;
  purchasesLoading: boolean;
  salesLoading: boolean;
  analyticsLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchMedications: (familyId?: string, supplierId?: string) => Promise<void>;
  fetchInventory: (depotId?: string, lowStockOnly?: boolean) => Promise<void>;
  fetchPurchaseOrders: (status?: string, supplierId?: string) => Promise<void>;
  fetchSalesOrders: (status?: string) => Promise<void>;
  fetchSuppliers: () => Promise<void>;
  fetchMedicationFamilies: () => Promise<void>;
  fetchStockMovements: (medicationId?: string) => Promise<void>;
  fetchAnalytics: () => Promise<void>;
  fetchSummary: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Utility functions
  getMedicationsByFamily: (familyId: string) => Medication[];
  getMedicationsBySupplier: (supplierId: string) => Medication[];
  getLowStockItems: () => InventoryItem[];
  getExpiredItems: () => InventoryItem[];
  getPendingPurchases: () => PurchaseOrder[];
  getRecentSales: (days?: number) => SalesOrder[];
  getStockValue: () => number;
  getTopSellingMedications: (limit?: number) => Array<{ medication: Medication; sales: number }>;
  getSupplierPerformance: () => Array<{ supplier: Supplier; performance: number }>;
  getInventoryAlerts: () => {
    lowStock: InventoryItem[];
    expired: InventoryItem[];
    nearExpiry: InventoryItem[];
  };
  getPharmacyStats: () => {
    totalMedications: number;
    totalStockValue: number;
    lowStockCount: number;
    expiredCount: number;
    pendingOrders: number;
    monthlyRevenue: number;
    stockTurnover: number;
  };
}

export const usePharmacy = (options: UsePharmacyOptions = {}): UsePharmacyReturn => {
  const { dateRange, autoFetch = true, refreshInterval, dataTypes } = options;

  // State
  const [medications, setMedications] = useState<Medication[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [salesOrders, setSalesOrders] = useState<SalesOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [medicationFamilies, setMedicationFamilies] = useState<MedicationFamily[]>([]);
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([]);
  const [analytics, setAnalytics] = useState<PharmacyAnalytics | null>(null);
  const [summary, setSummary] = useState<PharmacySummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [medicationsLoading, setMedicationsLoading] = useState(false);
  const [inventoryLoading, setInventoryLoading] = useState(false);
  const [purchasesLoading, setPurchasesLoading] = useState(false);
  const [salesLoading, setSalesLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchMedications = useCallback(async (familyId?: string, supplierId?: string) => {
    setMedicationsLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getMedications(familyId, supplierId);
      setMedications(data);
    } catch (err) {
      setError(`Failed to fetch medications: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setMedicationsLoading(false);
    }
  }, []);

  const fetchInventory = useCallback(async (depotId?: string, lowStockOnly?: boolean) => {
    setInventoryLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getInventory(depotId, lowStockOnly);
      setInventory(data);
    } catch (err) {
      setError(`Failed to fetch inventory: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setInventoryLoading(false);
    }
  }, []);

  const fetchPurchaseOrders = useCallback(async (status?: string, supplierId?: string) => {
    setPurchasesLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getPurchaseOrders(status, supplierId);
      setPurchaseOrders(data);
    } catch (err) {
      setError(`Failed to fetch purchase orders: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setPurchasesLoading(false);
    }
  }, []);

  const fetchSalesOrders = useCallback(async (status?: string) => {
    setSalesLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getSalesOrders(status, dateRange);
      setSalesOrders(data);
    } catch (err) {
      setError(`Failed to fetch sales orders: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setSalesLoading(false);
    }
  }, [dateRange]);

  const fetchSuppliers = useCallback(async () => {
    setError(null);
    try {
      const data = await pharmacyService.getSuppliers();
      setSuppliers(data);
    } catch (err) {
      setError(`Failed to fetch suppliers: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, []);

  const fetchMedicationFamilies = useCallback(async () => {
    setError(null);
    try {
      const data = await pharmacyService.getMedicationFamilies();
      setMedicationFamilies(data);
    } catch (err) {
      setError(`Failed to fetch medication families: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, []);

  const fetchStockMovements = useCallback(async (medicationId?: string) => {
    setError(null);
    try {
      const data = await pharmacyService.getStockMovements(medicationId, dateRange);
      setStockMovements(data);
    } catch (err) {
      setError(`Failed to fetch stock movements: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, [dateRange]);

  const fetchAnalytics = useCallback(async () => {
    setAnalyticsLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getPharmacyAnalytics(dateRange);
      setAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch pharmacy analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setAnalyticsLoading(false);
    }
  }, [dateRange]);

  const fetchSummary = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await pharmacyService.getPharmacySummary(dateRange);
      setSummary(data);
      setMedications(data.medications);
      setInventory(data.inventory);
      setPurchaseOrders(data.purchaseOrders);
      setSalesOrders(data.salesOrders);
      setSuppliers(data.suppliers);
      setMedicationFamilies(data.medicationFamilies);
      setStockMovements(data.stockMovements);
      setAnalytics(data.analytics);
    } catch (err) {
      setError(`Failed to fetch pharmacy summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  const refreshAll = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchMedications(),
        fetchInventory(),
        fetchPurchaseOrders(),
        fetchSalesOrders(),
        fetchSuppliers(),
        fetchMedicationFamilies(),
        fetchStockMovements(),
        fetchAnalytics(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchMedications, fetchInventory, fetchPurchaseOrders, fetchSalesOrders, fetchSuppliers, fetchMedicationFamilies, fetchStockMovements, fetchAnalytics]);

  // Utility functions
  const getMedicationsByFamily = useCallback((familyId: string) => {
    return medications.filter(m => m.family_id === familyId);
  }, [medications]);

  const getMedicationsBySupplier = useCallback((supplierId: string) => {
    return medications.filter(m => m.supplier_id === supplierId);
  }, [medications]);

  const getLowStockItems = useCallback(() => {
    return inventory.filter(item => item.current_stock <= item.minimum_stock);
  }, [inventory]);

  const getExpiredItems = useCallback(() => {
    const today = new Date();
    return inventory.filter(item => new Date(item.expiry_date) < today);
  }, [inventory]);

  const getPendingPurchases = useCallback(() => {
    return purchaseOrders.filter(order => order.status === 'sent' || order.status === 'confirmed');
  }, [purchaseOrders]);

  const getRecentSales = useCallback((days: number = 30) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    return salesOrders.filter(order => new Date(order.order_date) >= cutoffDate);
  }, [salesOrders]);

  const getStockValue = useCallback(() => {
    return inventory.reduce((total, item) => total + (item.current_stock * item.unit_cost), 0);
  }, [inventory]);

  const getTopSellingMedications = useCallback((limit: number = 5) => {
    const salesMap = new Map<string, number>();
    
    salesOrders.forEach(order => {
      order.items.forEach(item => {
        const current = salesMap.get(item.medication_id) || 0;
        salesMap.set(item.medication_id, current + item.quantity);
      });
    });

    return Array.from(salesMap.entries())
      .map(([medicationId, sales]) => {
        const medication = medications.find(m => m.id === medicationId);
        return medication ? { medication, sales } : null;
      })
      .filter(Boolean)
      .sort((a, b) => b!.sales - a!.sales)
      .slice(0, limit) as Array<{ medication: Medication; sales: number }>;
  }, [medications, salesOrders]);

  const getSupplierPerformance = useCallback(() => {
    return suppliers.map(supplier => {
      const supplierOrders = purchaseOrders.filter(order => order.supplier_id === supplier.id);
      const onTimeDeliveries = supplierOrders.filter(order => order.status === 'received').length;
      const performance = supplierOrders.length > 0 ? (onTimeDeliveries / supplierOrders.length) * 100 : 0;
      
      return { supplier, performance };
    }).sort((a, b) => b.performance - a.performance);
  }, [suppliers, purchaseOrders]);

  const getInventoryAlerts = useCallback(() => {
    const today = new Date();
    const nearExpiryDate = new Date();
    nearExpiryDate.setDate(today.getDate() + 30); // 30 days from now

    return {
      lowStock: getLowStockItems(),
      expired: getExpiredItems(),
      nearExpiry: inventory.filter(item => {
        const expiryDate = new Date(item.expiry_date);
        return expiryDate > today && expiryDate <= nearExpiryDate;
      }),
    };
  }, [inventory, getLowStockItems, getExpiredItems]);

  const getPharmacyStats = useCallback(() => {
    const lowStockItems = getLowStockItems();
    const expiredItems = getExpiredItems();
    const pendingOrders = getPendingPurchases();
    const recentSales = getRecentSales(30);
    const monthlyRevenue = recentSales.reduce((sum, order) => sum + order.total_amount, 0);

    return {
      totalMedications: medications.length,
      totalStockValue: getStockValue(),
      lowStockCount: lowStockItems.length,
      expiredCount: expiredItems.length,
      pendingOrders: pendingOrders.length,
      monthlyRevenue,
      stockTurnover: analytics?.stock_turnover_rate || 0,
    };
  }, [medications, getLowStockItems, getExpiredItems, getPendingPurchases, getRecentSales, getStockValue, analytics]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (dataTypes && dataTypes.length > 0) {
        // Fetch specific data types
        dataTypes.forEach(type => {
          switch (type) {
            case 'medications':
              fetchMedications();
              break;
            case 'inventory':
              fetchInventory();
              break;
            case 'purchases':
              fetchPurchaseOrders();
              break;
            case 'sales':
              fetchSalesOrders();
              break;
            case 'suppliers':
              fetchSuppliers();
              break;
            case 'analytics':
              fetchAnalytics();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, dataTypes, refreshAll, fetchMedications, fetchInventory, fetchPurchaseOrders, fetchSalesOrders, fetchSuppliers, fetchAnalytics]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    medications,
    inventory,
    purchaseOrders,
    salesOrders,
    suppliers,
    medicationFamilies,
    stockMovements,
    analytics,
    summary,
    
    // Loading states
    loading,
    medicationsLoading,
    inventoryLoading,
    purchasesLoading,
    salesLoading,
    analyticsLoading,
    
    // Error state
    error,
    
    // Actions
    fetchMedications,
    fetchInventory,
    fetchPurchaseOrders,
    fetchSalesOrders,
    fetchSuppliers,
    fetchMedicationFamilies,
    fetchStockMovements,
    fetchAnalytics,
    fetchSummary,
    refreshAll,
    
    // Utility functions
    getMedicationsByFamily,
    getMedicationsBySupplier,
    getLowStockItems,
    getExpiredItems,
    getPendingPurchases,
    getRecentSales,
    getStockValue,
    getTopSellingMedications,
    getSupplierPerformance,
    getInventoryAlerts,
    getPharmacyStats,
  };
};

export default usePharmacy;
