'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { ActivitEjournaliere } from './ActivitEjournaliere';

export default function ActivitEjournaliereDemo() {
  const mockSummary = {
    total: 0.00,
    encasement_total: 0.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Activité changée:', activity);
  };

  const handleProcedureTypeChange = (type: number) => {
    console.log('Type de procédure changé:', type);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression en cours...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActivitEjournaliere
            cycle="daily"
            summary={mockSummary}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={handleProcedureTypeChange}
            onExport={handleExport}
            onPrint={handlePrint}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function ActivitEjournaliereLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActivitEjournaliere
            cycle="daily"
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onActivityChange={(activity) => console.log('Activity:', activity)}
            onProcedureTypeChange={(type) => console.log('Type:', type)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle mensuel
export function ActivitEjournaliereMensuelleDemo() {
  const mockSummaryMensuelle = {
    total: 2500.00,
    encasement_total: 2100.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActivitEjournaliere
            cycle="monthly"
            summary={mockSummaryMensuelle}
            loading={false}
            onQueryChange={(query) => console.log('Query mensuelle:', query)}
            onActivityChange={(activity) => console.log('Activity mensuelle:', activity)}
            onProcedureTypeChange={(type) => console.log('Type mensuel:', type)}
            onExport={(format) => alert(`Export mensuel ${format}`)}
            onPrint={() => alert('Impression mensuelle')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données d'activité
export function ActivitEjournaliereWithDataDemo() {
  const mockSummaryWithData = {
    total: 4500.00,
    encasement_total: 3800.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données chargées pour:', query);
    }, 1000);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Changement d\'activité:', activity);
    // Simuler le rechargement des données selon l'activité
    if (activity.type === 'encasement') {
      console.log('Chargement des données d\'encaissement...');
    } else if (activity.type === 'procedure') {
      console.log('Chargement des données de procédures...');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActivitEjournaliere
            cycle="daily"
            summary={mockSummaryWithData}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={(type) => {
              console.log('Type de procédure:', type);
              const typeLabels = ['Chiffre d\'affaire', 'Nombre d\'exécution', 'Les deux'];
              alert(`Type sélectionné: ${typeLabels[type]}`);
            }}
            onExport={(format) => {
              console.log(`Export ${format} avec données`);
              alert(`Préparation de l'export ${format.toUpperCase()} avec les données d'activité...`);
            }}
            onPrint={() => {
              console.log('Impression avec données');
              alert('Préparation de l\'impression du rapport d\'activité journalière...');
            }}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle annuel (sans date picker)
export function ActivitEjournaliereAnnuelleDemo() {
  const mockSummaryAnnuelle = {
    total: 125000.00,
    encasement_total: 98000.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActivitEjournaliere
            cycle="annual"
            summary={mockSummaryAnnuelle}
            loading={false}
            onQueryChange={(query) => console.log('Query annuelle:', query)}
            onActivityChange={(activity) => console.log('Activity annuelle:', activity)}
            onProcedureTypeChange={(type) => console.log('Type annuel:', type)}
            onExport={(format) => alert(`Export annuel ${format}`)}
            onPrint={() => alert('Impression rapport annuel')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
