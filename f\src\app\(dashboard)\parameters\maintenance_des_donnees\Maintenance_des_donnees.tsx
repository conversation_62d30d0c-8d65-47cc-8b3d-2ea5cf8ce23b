import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle,  } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import BackupsDeDonnees from './BackupsDeDonnees'
import FusionDesPatients from "./FusionDesPatients"


// Mapping des sous-onglets pour Configuration de l'application
const subtabMapping: { [key: string]: string } = {
  'BackupsDeDonnees': 'BackupsDeDonnees',
  'FusionDesPatients': 'FusionDesPatients',
  

};

const Maintenance_des_donnees = () => {
  const [activeTab, setActiveTab] = useState('BackupsDeDonnees');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'BackupsDeDonnees')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="BackupsDeDonnees" leftSection={<IconPhoto size={12} />}>
             BackupsDeDonnees
           </Tabs.Tab>
           <Tabs.Tab value="FusionDesPatients" leftSection={<IconMessageCircle size={12} />}>
           FusionDesPatients
           </Tabs.Tab>
         
          
         </Tabs.List>
         <Tabs.Panel value="BackupsDeDonnees" ml={20}>
           <BackupsDeDonnees/>
         </Tabs.Panel>

         <Tabs.Panel value="FusionDesPatients" ml={20}>
           <FusionDesPatients/>
         </Tabs.Panel>

       
       </Tabs>
  )
};





export default Maintenance_des_donnees
