'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Menu,
  Alert,
  Select
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiCashRefund,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiFilterVariant,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface AgedBalanceColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface AgedBalanceFilter {
  id: string;
  label: string;
  value: string;
  options: string[];
}

interface BalanceAgeeProps {
  loading?: boolean;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onFilterChange?: (filterId: string, value: string) => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const BalanceAgee: React.FC<BalanceAgeeProps> = ({
  loading = false,
  onExport,
  onPrint,
  onFilterChange,
  onSort
}) => {
  // États locaux
  const [filters, setFilters] = useState<{ [key: string]: string }>({
    invoice_number: ''
  });

  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: AgedBalanceColumn[] = [
    { id: 'invoice_number', label: 'N°. Facture', sortable: true },
    { id: 'plus_15', label: '+15j', sortable: true },
    { id: 'plus_30', label: '+30j', sortable: true },
    { id: 'plus_60', label: '+60j', sortable: true },
    { id: 'plus_120', label: '+120j', sortable: true }
  ];

  // Configuration des filtres
  const filterConfig: AgedBalanceFilter = {
    id: 'invoice_number',
    label: 'N°. Facture',
    value: filters.invoice_number,
    options: []
  };

  // Gestionnaires d'événements
  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleFilterChange = (filterId: string, value: string) => {
    setFilters(prev => ({ ...prev, [filterId]: value }));
    onFilterChange?.(filterId, value);
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiCashRefund} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Balance âgée</Text>
          </Group>
        </Group>
      </Paper>

      {/* Contenu principal */}
      <Box style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Sidebar avec filtres */}
        <Paper
          withBorder
          style={{
            width: 200,
            borderRight: '1px solid #e9ecef',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Filtre N°. Facture */}
          <Box p="sm">
            <Paper withBorder style={{ border: '1px solid #dee2e6' }}>
              <Box
                p="xs"
                style={{
                  backgroundColor: '#f8f9fa',
                  borderBottom: '1px solid #dee2e6'
                }}
              >
                <Group gap="xs" align="center">
                  <Icon path={mdiFilterVariant} size={0.6} />
                  <Text size="sm" fw={500}>{filterConfig.label}</Text>
                </Group>
              </Box>
              <Box p="xs">
                <Select
                  placeholder="Sélectionner"
                  data={filterConfig.options}
                  value={filterConfig.value}
                  onChange={(value) => handleFilterChange(filterConfig.id, value || '')}
                  size="xs"
                  style={{ width: '100%' }}
                />
              </Box>
            </Paper>
          </Box>
        </Paper>

        {/* Zone principale du tableau */}
        <Box style={{ flex: 1, overflow: 'auto' }}>
          {loading ? (
            <Box p="xl" style={{ textAlign: 'center' }}>
              <Loader size="lg" />
            </Box>
          ) : (
            <Box style={{ position: 'relative' }}>
              {/* Toolbar */}
              <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
                <Group justify="flex-end" gap="xs">
                  <ActionIcon
                    variant="subtle"
                    onClick={handlePrint}
                    title="Imprimer"
                  >
                    <Icon path={mdiPrinter} size={0.8} />
                  </ActionIcon>

                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <ActionIcon variant="subtle" title="Exporter">
                        <Icon path={mdiDatabaseExport} size={0.8} />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                        onClick={() => handleExport('excel')}
                      >
                        Pour Excel
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                        onClick={() => handleExport('pdf')}
                      >
                        PDF
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>

                  <Menu shadow="md" width={250}>
                    <Menu.Target>
                      <ActionIcon variant="subtle" title="Format">
                        <Icon path={mdiTableEdit} size={0.8} />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<Icon path={mdiFormatLetterMatches} size={0.8} />}
                      >
                        Format de cellule
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<Icon path={mdiFormatColorHighlight} size={0.8} />}
                      >
                        La mise en forme conditionnelle
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>

                  <ActionIcon variant="subtle" title="Champs">
                    <Icon path={mdiTableSettings} size={0.8} />
                  </ActionIcon>

                  <ActionIcon variant="subtle" title="Options">
                    <Icon path={mdiCog} size={0.8} />
                  </ActionIcon>
                </Group>
              </Paper>

              {/* Tableau des données */}
              <ScrollArea style={{ height: 'calc(100vh - 200px)' }}>
                <Table striped highlightOnHover withTableBorder withColumnBorders>
                  <Table.Thead>
                    <Table.Tr>
                      {columns.map((column) => (
                        <Table.Th
                          key={column.id}
                          style={{
                            minWidth: 100,
                            backgroundColor: '#f8f9fa',
                            cursor: column.sortable ? 'pointer' : 'default'
                          }}
                          onClick={() => column.sortable && handleSort(column.id)}
                        >
                          <Group gap="xs" justify="space-between">
                            <Text size="sm" fw={500}>{column.label}</Text>
                            {column.sortable && (
                              <Icon
                                path={getSortIcon(column.id) || mdiArrowUp}
                                size={0.6}
                                style={{
                                  opacity: getSortIcon(column.id) ? 1 : 0.3
                                }}
                              />
                            )}
                          </Group>
                        </Table.Th>
                      ))}
                      {/* Colonnes vides supplémentaires */}
                      {Array.from({ length: 7 }, (_, index) => (
                        <Table.Th
                          key={`empty-col-${index}`}
                          style={{
                            minWidth: 100,
                            backgroundColor: '#f8f9fa'
                          }}
                        />
                      ))}
                    </Table.Tr>
                  </Table.Thead>

                  <Table.Tbody>
                    {/* Ligne Total */}
                    <Table.Tr style={{ backgroundColor: '#e8f5e8' }}>
                      <Table.Td style={{ fontWeight: 'bold' }}>
                        <Group gap="xs" justify="space-between">
                          <Text fw={500}>Total</Text>
                          <Icon
                            path={getSortIcon('total') || mdiArrowUp}
                            size={0.6}
                            style={{
                              opacity: getSortIcon('total') ? 1 : 0.3
                            }}
                          />
                        </Group>
                      </Table.Td>
                      {columns.slice(1).map((column) => (
                        <Table.Td key={column.id} style={{ textAlign: 'center' }}>
                          <Text size="sm" c="dimmed">-</Text>
                        </Table.Td>
                      ))}
                      {/* Cellules vides */}
                      {Array.from({ length: 7 }, (_, index) => (
                        <Table.Td key={`empty-total-${index}`} />
                      ))}
                    </Table.Tr>

                    {/* Lignes vides pour remplir l'espace */}
                    {Array.from({ length: 20 }, (_, index) => (
                      <Table.Tr key={`empty-row-${index}`}>
                        {Array.from({ length: 12 }, (_, cellIndex) => (
                          <Table.Td key={cellIndex} style={{ height: 30 }} />
                        ))}
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Message d'état vide */}
              <Box
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1
                }}
              >
                <Alert
                  icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                  title="Aucune donnée disponible"
                  color="gray"
                  variant="light"
                  style={{ maxWidth: 400 }}
                >
                  <Text size="sm" c="dimmed">
                    Aucune balance âgée trouvée.
                    Veuillez vérifier les filtres ou la période sélectionnée.
                  </Text>
                </Alert>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default BalanceAgee;
