/**
 * Custom hook for managing prescriptions data
 * Provides easy access to prescriptions, medication history, templates, and refills
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  prescriptionsService, 
  Prescription,
  MedicationHistory,
  PrescriptionTemplate,
  PrescriptionRefill,
  DrugInteraction,
  PrescriptionAnalytics,
  PrescriptionsSummary
} from '@/services/prescriptionsService';

interface UsePrescriptionsOptions {
  patientId?: string;
  dateRange?: { start: string; end: string };
  autoFetch?: boolean;
  refreshInterval?: number;
  dataTypes?: string[];
}

interface UsePrescriptionsReturn {
  // Data
  prescriptions: Prescription[];
  medicationHistory: MedicationHistory[];
  prescriptionTemplates: PrescriptionTemplate[];
  prescriptionRefills: PrescriptionRefill[];
  drugInteractions: DrugInteraction[];
  analytics: PrescriptionAnalytics | null;
  summary: PrescriptionsSummary | null;
  
  // Loading states
  loading: boolean;
  prescriptionsLoading: boolean;
  historyLoading: boolean;
  templatesLoading: boolean;
  refillsLoading: boolean;
  analyticsLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchPrescriptions: (patientId?: string, status?: string) => Promise<void>;
  fetchMedicationHistory: (patientId?: string) => Promise<void>;
  fetchPrescriptionTemplates: (category?: string) => Promise<void>;
  fetchPrescriptionRefills: (prescriptionId?: string, patientId?: string) => Promise<void>;
  checkDrugInteractions: (medications: string[]) => Promise<void>;
  fetchAnalytics: () => Promise<void>;
  fetchSummary: (patientId?: string) => Promise<void>;
  refreshAll: (patientId?: string) => Promise<void>;
  createPrescription: (prescriptionData: Omit<Prescription, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  
  // Utility functions
  getPrescriptionsByPatient: (patientId: string) => Prescription[];
  getActivePrescriptions: () => Prescription[];
  getExpiredPrescriptions: () => Prescription[];
  getPrescriptionsByMedication: (medicationName: string) => Prescription[];
  getMedicationHistoryByPatient: (patientId: string) => MedicationHistory[];
  getRefillsByPrescription: (prescriptionId: string) => PrescriptionRefill[];
  getPendingRefills: () => PrescriptionRefill[];
  getPatientPrescriptionStats: (patientId: string) => {
    totalPrescriptions: number;
    activePrescriptions: number;
    expiredPrescriptions: number;
    totalRefills: number;
    pendingRefills: number;
    complianceRate: number;
    lastPrescriptionDate: string | null;
    mostCommonMedication: string;
  };
  getPrescriptionTrends: () => {
    totalPrescriptions: number;
    activePrescriptions: number;
    monthlyAverage: number;
    mostPrescribedMedication: string;
    controlledSubstances: number;
    drugInteractionsFound: number;
  };
  getTemplatesByCategory: (category: string) => PrescriptionTemplate[];
  getMostUsedTemplates: (limit?: number) => PrescriptionTemplate[];
}

export const usePrescriptions = (options: UsePrescriptionsOptions = {}): UsePrescriptionsReturn => {
  const { patientId, dateRange, autoFetch = true, refreshInterval, dataTypes } = options;

  // State
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [medicationHistory, setMedicationHistory] = useState<MedicationHistory[]>([]);
  const [prescriptionTemplates, setPrescriptionTemplates] = useState<PrescriptionTemplate[]>([]);
  const [prescriptionRefills, setPrescriptionRefills] = useState<PrescriptionRefill[]>([]);
  const [drugInteractions, setDrugInteractions] = useState<DrugInteraction[]>([]);
  const [analytics, setAnalytics] = useState<PrescriptionAnalytics | null>(null);
  const [summary, setSummary] = useState<PrescriptionsSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [prescriptionsLoading, setPrescriptionsLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [refillsLoading, setRefillsLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchPrescriptions = useCallback(async (targetPatientId?: string, status?: string) => {
    setPrescriptionsLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getPrescriptions(targetPatientId || patientId, status, dateRange);
      setPrescriptions(data);
    } catch (err) {
      setError(`Failed to fetch prescriptions: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setPrescriptionsLoading(false);
    }
  }, [patientId, dateRange]);

  const fetchMedicationHistory = useCallback(async (targetPatientId?: string) => {
    setHistoryLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getMedicationHistory(targetPatientId || patientId);
      setMedicationHistory(data);
    } catch (err) {
      setError(`Failed to fetch medication history: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setHistoryLoading(false);
    }
  }, [patientId]);

  const fetchPrescriptionTemplates = useCallback(async (category?: string) => {
    setTemplatesLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getPrescriptionTemplates(category);
      setPrescriptionTemplates(data);
    } catch (err) {
      setError(`Failed to fetch prescription templates: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setTemplatesLoading(false);
    }
  }, []);

  const fetchPrescriptionRefills = useCallback(async (prescriptionId?: string, targetPatientId?: string) => {
    setRefillsLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getPrescriptionRefills(prescriptionId, targetPatientId || patientId);
      setPrescriptionRefills(data);
    } catch (err) {
      setError(`Failed to fetch prescription refills: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setRefillsLoading(false);
    }
  }, [patientId]);

  const checkDrugInteractions = useCallback(async (medications: string[]) => {
    setError(null);
    try {
      const data = await prescriptionsService.checkDrugInteractions(medications);
      setDrugInteractions(data);
    } catch (err) {
      setError(`Failed to check drug interactions: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, []);

  const fetchAnalytics = useCallback(async () => {
    setAnalyticsLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getPrescriptionAnalytics(dateRange);
      setAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch prescription analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setAnalyticsLoading(false);
    }
  }, [dateRange]);

  const fetchSummary = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await prescriptionsService.getPrescriptionsSummary(targetPatientId || patientId, dateRange);
      setSummary(data);
      setPrescriptions(data.prescriptions);
      setMedicationHistory(data.medicationHistory);
      setPrescriptionTemplates(data.prescriptionTemplates);
      setPrescriptionRefills(data.prescriptionRefills);
      setDrugInteractions(data.drugInteractions);
      setAnalytics(data.analytics);
    } catch (err) {
      setError(`Failed to fetch prescriptions summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [patientId, dateRange]);

  const refreshAll = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    try {
      await Promise.all([
        fetchPrescriptions(targetPatientId),
        fetchMedicationHistory(targetPatientId),
        fetchPrescriptionTemplates(),
        fetchPrescriptionRefills(undefined, targetPatientId),
        fetchAnalytics(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchPrescriptions, fetchMedicationHistory, fetchPrescriptionTemplates, fetchPrescriptionRefills, fetchAnalytics]);

  const createPrescription = useCallback(async (prescriptionData: Omit<Prescription, 'id' | 'created_at' | 'updated_at'>) => {
    setError(null);
    try {
      const newPrescription = await prescriptionsService.createPrescription(prescriptionData);
      setPrescriptions(prev => [newPrescription, ...prev]);
    } catch (err) {
      setError(`Failed to create prescription: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const getPrescriptionsByPatient = useCallback((targetPatientId: string) => {
    return prescriptions.filter(p => p.patient_id === targetPatientId);
  }, [prescriptions]);

  const getActivePrescriptions = useCallback(() => {
    return prescriptions.filter(p => p.status === 'active');
  }, [prescriptions]);

  const getExpiredPrescriptions = useCallback(() => {
    return prescriptions.filter(p => p.status === 'expired');
  }, [prescriptions]);

  const getPrescriptionsByMedication = useCallback((medicationName: string) => {
    return prescriptions.filter(p => p.medication_name.toLowerCase().includes(medicationName.toLowerCase()));
  }, [prescriptions]);

  const getMedicationHistoryByPatient = useCallback((targetPatientId: string) => {
    return medicationHistory.filter(h => h.patient_id === targetPatientId);
  }, [medicationHistory]);

  const getRefillsByPrescription = useCallback((prescriptionId: string) => {
    return prescriptionRefills.filter(r => r.prescription_id === prescriptionId);
  }, [prescriptionRefills]);

  const getPendingRefills = useCallback(() => {
    return prescriptionRefills.filter(r => r.status === 'requested' || r.status === 'approved');
  }, [prescriptionRefills]);

  const getPatientPrescriptionStats = useCallback((targetPatientId: string) => {
    const patientPrescriptions = getPrescriptionsByPatient(targetPatientId);
    const patientHistory = getMedicationHistoryByPatient(targetPatientId);
    const patientRefills = prescriptionRefills.filter(r => 
      patientPrescriptions.some(p => p.id === r.prescription_id)
    );

    const activePrescriptions = patientPrescriptions.filter(p => p.status === 'active').length;
    const expiredPrescriptions = patientPrescriptions.filter(p => p.status === 'expired').length;
    const pendingRefills = patientRefills.filter(r => r.status === 'requested' || r.status === 'approved').length;

    const lastPrescription = patientPrescriptions
      .sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())[0];

    // Find most common medication
    const medicationCounts = patientPrescriptions.reduce((acc, p) => {
      acc[p.medication_name] = (acc[p.medication_name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostCommonMedication = Object.entries(medicationCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

    // Calculate compliance rate (simplified)
    const completedPrescriptions = patientPrescriptions.filter(p => p.status === 'completed').length;
    const complianceRate = patientPrescriptions.length > 0 ? 
      (completedPrescriptions / patientPrescriptions.length) * 100 : 0;

    return {
      totalPrescriptions: patientPrescriptions.length,
      activePrescriptions,
      expiredPrescriptions,
      totalRefills: patientRefills.length,
      pendingRefills,
      complianceRate,
      lastPrescriptionDate: lastPrescription?.date_prescribed || null,
      mostCommonMedication,
    };
  }, [getPrescriptionsByPatient, getMedicationHistoryByPatient, prescriptionRefills]);

  const getPrescriptionTrends = useCallback(() => {
    const activePrescriptions = getActivePrescriptions().length;
    const controlledSubstances = prescriptions.filter(p => p.is_controlled_substance).length;

    // Find most prescribed medication
    const medicationCounts = prescriptions.reduce((acc, p) => {
      acc[p.medication_name] = (acc[p.medication_name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostPrescribedMedication = Object.entries(medicationCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

    // Calculate monthly average (simplified)
    const monthlyAverage = analytics?.prescription_trends.reduce((sum, trend) => sum + trend.total_prescriptions, 0) / 
      (analytics?.prescription_trends.length || 1) || 0;

    return {
      totalPrescriptions: prescriptions.length,
      activePrescriptions,
      monthlyAverage,
      mostPrescribedMedication,
      controlledSubstances,
      drugInteractionsFound: drugInteractions.length,
    };
  }, [prescriptions, getActivePrescriptions, analytics, drugInteractions]);

  const getTemplatesByCategory = useCallback((category: string) => {
    return prescriptionTemplates.filter(t => t.category === category);
  }, [prescriptionTemplates]);

  const getMostUsedTemplates = useCallback((limit: number = 5) => {
    return prescriptionTemplates
      .sort((a, b) => b.usage_count - a.usage_count)
      .slice(0, limit);
  }, [prescriptionTemplates]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (dataTypes && dataTypes.length > 0) {
        // Fetch specific data types
        dataTypes.forEach(type => {
          switch (type) {
            case 'prescriptions':
              fetchPrescriptions();
              break;
            case 'history':
              fetchMedicationHistory();
              break;
            case 'templates':
              fetchPrescriptionTemplates();
              break;
            case 'refills':
              fetchPrescriptionRefills();
              break;
            case 'analytics':
              fetchAnalytics();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, dataTypes, refreshAll, fetchPrescriptions, fetchMedicationHistory, fetchPrescriptionTemplates, fetchPrescriptionRefills, fetchAnalytics]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    prescriptions,
    medicationHistory,
    prescriptionTemplates,
    prescriptionRefills,
    drugInteractions,
    analytics,
    summary,
    
    // Loading states
    loading,
    prescriptionsLoading,
    historyLoading,
    templatesLoading,
    refillsLoading,
    analyticsLoading,
    
    // Error state
    error,
    
    // Actions
    fetchPrescriptions,
    fetchMedicationHistory,
    fetchPrescriptionTemplates,
    fetchPrescriptionRefills,
    checkDrugInteractions,
    fetchAnalytics,
    fetchSummary,
    refreshAll,
    createPrescription,
    
    // Utility functions
    getPrescriptionsByPatient,
    getActivePrescriptions,
    getExpiredPrescriptions,
    getPrescriptionsByMedication,
    getMedicationHistoryByPatient,
    getRefillsByPrescription,
    getPendingRefills,
    getPatientPrescriptionStats,
    getPrescriptionTrends,
    getTemplatesByCategory,
    getMostUsedTemplates,
  };
};

export default usePrescriptions;
