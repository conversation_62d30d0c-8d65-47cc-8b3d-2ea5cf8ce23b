"use client";
import { useEffect } from "react";
import { useState } from 'react';
import SimpleBar from "simplebar-react";

import { FontSizeProvider, useFontSize } from "~/contexts/FontSizeContext";
import { LanguageProvider } from "~/contexts/LanguageContext";
import { PatientProvider } from "~/contexts/PatientContext";
import classes from '~/styles/layout.module.css';
import Navbar from '~/layout/navbar/Navbar';
import { SideNavbar } from "~/layout/sideNavbar/SideNavbar"
import Header from '~/layout/header/Header';
import Footer from '~/layout/footer/Footer';
import NavBarButton from '~/layout/navBarButton/NavBarButton';


interface DashboardLayoutProps {
  children: React.ReactNode;
}
const GlobalFontSize: React.FC = () => {
  const { fontSize } = useFontSize();
    useEffect(() => {
      document.documentElement.style.fontSize = `${fontSize}px`;
    }, [fontSize]);
  
    return null;
  };

  

export default function RootLayout({ children }: DashboardLayoutProps) {
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const toggleSidebar = () => setSidebarVisible(!sidebarVisible);
  const toggle = () => setSidebarVisible(!sidebarVisible);
  return (
      <SimpleBar className="simplebar-scrollable-y h-[calc(100vh)] lg:h-[calc(100vh)]">
       <FontSizeProvider>
         <LanguageProvider>
           <PatientProvider>
             <GlobalFontSize />
             <Header toggleSidebar={toggleSidebar} />
           <div className={classes.pageContainer}>
             <nav className={`${classes.navbar} ${classes.navbarDesktop} ${sidebarVisible ? classes.navbarHidden : ''}`}>
               <Navbar toggleSidebar={toggle} />
             </nav>

             <div className={`${classes.sidebarContainer} ${sidebarVisible ? classes.sidebarVisible : classes.sidebarHidden}`}>
               <SideNavbar toggleSidebar={toggleSidebar} />
             </div>

             <main className={`${classes.mainContent} ${sidebarVisible ? classes.mainContentWithSidebar : ''}`}>
               <div className="flex w-full min-h-screen">
                 <div className={`${classes.tabsButton} flex-1 w-full`} style={{ width: '100%', maxWidth: '100%' }}>
                   <div className="w-full" style={{ width: '100%', maxWidth: '100%' }}>
                     {children}
                   </div>
                 </div>
                 <NavBarButton/>
               </div>
             </main>
           </div>
           <Footer sidebarVisible={sidebarVisible} />
           </PatientProvider>
         </LanguageProvider>
       </FontSizeProvider>
      </SimpleBar>
  );
}