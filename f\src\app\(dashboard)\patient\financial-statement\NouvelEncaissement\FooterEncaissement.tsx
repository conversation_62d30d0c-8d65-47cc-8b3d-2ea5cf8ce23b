import React from "react";
import { <PERSON><PERSON><PERSON>, Button ,Group} from '@mantine/core';
import { mdiDelete, mdiPrinter,  mdiFilePdfBox, mdiShare } from "@mdi/js";
import Icon from "@mdi/react";

interface Props {
  modelId: number | null;
  reset: number;
  noShare: boolean;
  amount: number;
  total: number;
  templates: any[];
  closed: boolean;
  encasementFormInvalid: boolean;
  onDelete: () => void;
  onPrint: () => void;
  onPrintPdf: () => void;
  onShareMail: () => void;
  onDissociate: () => void;
  onHeadInvalidate: () => void;
  onGoBack: () => void;
  onSubmit: (close: boolean) => void;
}

const FooterEncaissement: React.FC<Props> = ({
  modelId,
  reset,
  noShare,
  amount,
  total,
 
  closed,
  encasementFormInvalid,
  onDelete,
  onPrint,
  onPrintPdf,
  onShareMail,
  onDissociate,
  onHeadInvalidate,
  onGoBack,
  onSubmit,
}) => {
  const hasModel = Boolean(modelId);
 
  const isSaveDisabled = encasementFormInvalid || amount < total || amount === 0;

  return (
    <div className="mn-module-actions flex-noshrink justify-between" style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        
        <Group>
         <Tooltip label="Supprimer">
        <span>
          <Button
            color="error"
            disabled={!hasModel}
            onClick={onDelete}
            aria-label="delete"
          >
            <Icon path={mdiDelete} size={1} />
          </Button>
        </span>
      </Tooltip>

      <Tooltip label="Imprimer">
        <span>
          <Button
            color="primary"
            disabled={true} // encasementFormInvalid || true
            onClick={onPrint}
            aria-label="print"
          >
            <Icon path={mdiPrinter} size={1} />
          </Button>
        </span>
      </Tooltip>

      <Tooltip label="Imprimer PDF">
        <span>
          <Button
            color="primary"
            disabled={true} // encasementFormInvalid || true
            onClick={onPrintPdf}
            aria-label="print pdf"
          >
            <Icon path={mdiFilePdfBox} size={1} />
          </Button>
        </span>
      </Tooltip>

      {!noShare && (
        <Tooltip label="Partager par mail">
          <span>
            <Button
              color="primary"
              disabled={true} // encasementFormInvalid || true
              onClick={onShareMail}
              aria-label="share"
            >
              <Icon path={mdiShare} size={1} />
            </Button>
          </span>
        </Tooltip>
      )}
        </Group>
        <Group>
      <Button
        variant="contained"
        color="error"
        disabled={!hasModel || reset > 0}
        onClick={onDissociate}
      >
        Dissocier
      </Button>

      <Button
        variant="contained"
        color="error"
        disabled={!hasModel || reset > 1}
        onClick={onHeadInvalidate}
      >
        Dévalider l&apos;en-tête
      </Button>

      <Button variant="contained" color="error" onClick={onGoBack}>
        Annuler
      </Button>

      {!closed && (
        <>
          <Button
            variant="contained"
            color="primary"
            disabled={isSaveDisabled}
            onClick={() => onSubmit(true)}
          >
            Enregistrer et quitter
          </Button>

          <Button
            variant="contained"
            color="primary"
            type="submit"
            disabled={isSaveDisabled}
          >
            Enregistrer
          </Button>
        </>
      )}
        </Group>
       
    </div>
  );
};

export default FooterEncaissement;
