import { notifications } from '@mantine/notifications';
import api from '../lib/api';
import { applyFontSizeSettings, applyLanguageSettings } from '../utils/fontSizeUtils';
import { Locales, supportedLocales } from '../i18n/settings';

// Define the interface for working hours settings
export interface WorkingHoursSettings {
  morningStart: string;
  morningEnd: string;
  afternoonStart: string;
  afternoonEnd: string;
  breakEnabled: boolean;
  breakDuration: number;
}

// Define interfaces for vacation periods, holiday dates, and emergency dates
export interface VacationPeriod {
  startDate: Date | null;
  endDate: Date | null;
  description: string;
}

export interface HolidayDate {
  date: Date | null;
  description: string;
}

export interface EmergencyDate {
  date: Date | null;
  reason: string;
}

// Define the interface for work days settings
export interface WorkDaysSettings {
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturdayFirstHalf: boolean;
  vacationDays: number;
  holidays: number;
  emergencyDays: number;
  vacationPeriods: Array<VacationPeriod>;
  holidayDates: Array<HolidayDate>;
  emergencyDates: Array<EmergencyDate>;
}

// Define the interface for user settings
export interface UserSettings {
  doctorId?: string;
  theme: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  marketingEmails: boolean;
  language: string;
  timeFormat: string;
  dateFormat: string;
  calendarView: string;
  calendarStartHour: number;
  calendarEndHour: number;
  workingHoursSettings: WorkingHoursSettings;
  workDaysSettings: WorkDaysSettings;
  privacySettings: {
    showProfileToOtherDoctors: boolean;
    shareAnonymizedDataForResearch: boolean;
    allowPatientFeedback: boolean;
  };
  accessibilitySettings: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
    fontSize: number;
  };
}

// Create a default settings object
export const defaultSettings: UserSettings = {
  doctorId: 'default',
  theme: 'light',
  emailNotifications: true,
  smsNotifications: true,
  appointmentReminders: true,
  marketingEmails: false,
  language: 'en',
  timeFormat: '12h',
  dateFormat: 'MM/DD/YYYY',
  calendarView: 'month',
  calendarStartHour: 8,
  calendarEndHour: 18,
  workingHoursSettings: {
    morningStart: '09:00',
    morningEnd: '12:00',
    afternoonStart: '13:00',
    afternoonEnd: '17:00',
    breakEnabled: true,
    breakDuration: 60,
  },
  workDaysSettings: {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturdayFirstHalf: true,
    vacationDays: 20,
    holidays: 10,
    emergencyDays: 5,
    vacationPeriods: [],
    holidayDates: [],
    emergencyDates: [],
  },
  privacySettings: {
    showProfileToOtherDoctors: true,
    shareAnonymizedDataForResearch: true,
    allowPatientFeedback: true,
  },
  accessibilitySettings: {
    highContrast: false,
    largeText: false,
    screenReader: false,
    fontSize: 16,
  },
};


// Helper function to get the current doctor ID from the authenticated user
const getCurrentDoctorId = async (): Promise<string> => {
  try {
    const response = await api.get('/api/auth/me/');
    const userId = response.data.id || response.data.uuid;

    if (userId) {
      return userId.toString();
    }

    throw new Error('No user ID found in profile');
  } catch (error) {
    console.error('Error getting current doctor ID:', error);
    throw new Error('Unable to get current doctor ID');
  }
};

// Settings service
const settingsService = {
  // Get user settings from user profile (temporary solution)
  async getUserSettings(): Promise<UserSettings> {
    try {
      const doctorId = await getCurrentDoctorId();
      console.log(`Fetching settings for doctor ID: ${doctorId} from user profile`);

      // Use the existing /api/auth/me/ endpoint to get user profile
      const response = await api.get(`/api/auth/me/`);
      console.log('User profile fetched from API:', response.data);

      // Extract settings from user profile or use defaults
      const userProfile = response.data;
      const settings: UserSettings = {
        doctorId: doctorId,
        theme: userProfile.settings?.theme || 'light',
        emailNotifications: userProfile.settings?.emailNotifications ?? true,
        smsNotifications: userProfile.settings?.smsNotifications ?? true,
        appointmentReminders: userProfile.settings?.appointmentReminders ?? true,
        marketingEmails: userProfile.settings?.marketingEmails ?? false,
        language: userProfile.settings?.language || 'en',
        timeFormat: userProfile.settings?.timeFormat || '12h',
        dateFormat: userProfile.settings?.dateFormat || 'MM/DD/YYYY',
        calendarView: userProfile.settings?.calendarView || 'month',
        calendarStartHour: userProfile.settings?.calendarStartHour || 8,
        calendarEndHour: userProfile.settings?.calendarEndHour || 18,
        workingHoursSettings: {
          morningStart: userProfile.settings?.workingHoursSettings?.morningStart || '09:00',
          morningEnd: userProfile.settings?.workingHoursSettings?.morningEnd || '12:00',
          afternoonStart: userProfile.settings?.workingHoursSettings?.afternoonStart || '13:00',
          afternoonEnd: userProfile.settings?.workingHoursSettings?.afternoonEnd || '17:00',
          breakEnabled: userProfile.settings?.workingHoursSettings?.breakEnabled ?? true,
          breakDuration: userProfile.settings?.workingHoursSettings?.breakDuration || 60,
        },
        workDaysSettings: {
          monday: userProfile.settings?.workDaysSettings?.monday ?? true,
          tuesday: userProfile.settings?.workDaysSettings?.tuesday ?? true,
          wednesday: userProfile.settings?.workDaysSettings?.wednesday ?? true,
          thursday: userProfile.settings?.workDaysSettings?.thursday ?? true,
          friday: userProfile.settings?.workDaysSettings?.friday ?? true,
          saturdayFirstHalf: userProfile.settings?.workDaysSettings?.saturdayFirstHalf ?? true,
          vacationDays: userProfile.settings?.workDaysSettings?.vacationDays || 20,
          holidays: userProfile.settings?.workDaysSettings?.holidays || 10,
          emergencyDays: userProfile.settings?.workDaysSettings?.emergencyDays || 5,
          vacationPeriods: userProfile.settings?.workDaysSettings?.vacationPeriods || [],
          holidayDates: userProfile.settings?.workDaysSettings?.holidayDates || [],
          emergencyDates: userProfile.settings?.workDaysSettings?.emergencyDates || [],
        },
        privacySettings: {
          showProfileToOtherDoctors: userProfile.settings?.privacySettings?.showProfileToOtherDoctors ?? true,
          shareAnonymizedDataForResearch: userProfile.settings?.privacySettings?.shareAnonymizedDataForResearch ?? true,
          allowPatientFeedback: userProfile.settings?.privacySettings?.allowPatientFeedback ?? true,
        },
        accessibilitySettings: {
          highContrast: userProfile.settings?.accessibilitySettings?.highContrast ?? false,
          largeText: userProfile.settings?.accessibilitySettings?.largeText ?? false,
          screenReader: userProfile.settings?.accessibilitySettings?.screenReader ?? false,
          fontSize: userProfile.settings?.accessibilitySettings?.fontSize || 16,
        },
      };

      // Update global font size and Mantine variables
      if (settings.accessibilitySettings?.fontSize) {
        applyFontSizeSettings(settings.accessibilitySettings.fontSize);
      }

      // Update language settings
      if (settings.language && supportedLocales.includes(settings.language as Locales)) {
        try {
          await applyLanguageSettings(settings.language as Locales);
        } catch (error) {
          console.error('Error applying language settings:', error);
        }
      }

      return settings;
    } catch (error) {
      console.error('Error fetching user profile from API:', error);

      // Return default settings if API fails
      try {
        const doctorId = await getCurrentDoctorId();
        const settings = { ...defaultSettings, doctorId };
        return settings;
      } catch {
        const settings = { ...defaultSettings, doctorId: 'default' };
        return settings;
      }
    }
  },

  // Save user settings (temporary solution - log only)
  async saveUserSettings(settings: UserSettings): Promise<boolean> {
    try {
      const doctorId = await getCurrentDoctorId();
      settings.doctorId = doctorId;

      console.log(`Saving settings for doctor ID: ${doctorId} (temporary - logged only)`);
      console.log('Settings to save:', settings);

      // TODO: Implement actual API call when backend endpoint is available
      // For now, we'll just log the settings and return success
      // const response = await api.put(`/api/auth/settings/`, settings);

      // Update global font size and Mantine variables
      if (settings.accessibilitySettings?.fontSize) {
        applyFontSizeSettings(settings.accessibilitySettings.fontSize);
      }

      // Update language settings
      if (settings.language && supportedLocales.includes(settings.language as Locales)) {
        try {
          await applyLanguageSettings(settings.language as Locales);
        } catch (error) {
          console.error('Error applying language settings:', error);
        }
      }

      // Show success notification
      notifications.show({
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully (temporary - not persisted)',
        color: 'green',
      });

      return true;
    } catch (error) {
      console.error('Error saving settings:', error);

      // Show error notification
      notifications.show({
        title: 'Error',
        message: 'Failed to save settings. Please try again.',
        color: 'red',
      });

      return false;
    }
  },

  // Update appointment system with new settings (temporary solution)
  async updateAppointmentSystem(settings: UserSettings, showNotification: boolean = false): Promise<boolean> {
    try {
      const doctorId = await getCurrentDoctorId();
      settings.doctorId = doctorId;

      console.log(`Updating appointment system for doctor ID: ${doctorId} (temporary - logged only)`);
      console.log('Appointment system settings:', settings);

      // TODO: Implement actual API call when backend endpoint is available
      // For now, we'll just log the settings and return success
      // const response = await api.put(`/api/auth/settings/appointment-system/`, settings);

      // Update global font size and Mantine variables
      if (settings.accessibilitySettings?.fontSize) {
        applyFontSizeSettings(settings.accessibilitySettings.fontSize);
      }

      // Update language settings
      if (settings.language && supportedLocales.includes(settings.language as Locales)) {
        try {
          await applyLanguageSettings(settings.language as Locales);
        } catch (error) {
          console.error('Error applying language settings:', error);
        }
      }

      // Show success notification if requested
      if (showNotification) {
        notifications.show({
          title: 'Success',
          message: 'Appointment system updated with new settings (temporary)',
          color: 'green',
        });
      }

      return true;
    } catch (error) {
      console.error('Error updating appointment system:', error);

      // Show error notification if requested
      if (showNotification) {
        notifications.show({
          title: 'Error',
          message: 'Unable to update appointment system',
          color: 'red',
        });
      }

      return false;
    }
  }
};

export default settingsService;
