'use client';
import Descriptif from './Descriptif'
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  Text,
  NumberInput,
  Tabs,
  Radio,
  Checkbox,
  ActionIcon,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconFileText,
  IconList,
  IconSearch,
  IconPlus,
  IconDeviceFloppy,
  IconPhoto,
  IconPaperclip,
  IconHistory,
  IconChartBar,
} from '@tabler/icons-react';

// TypeScript interfaces
interface ArticleData {
  code: string;
  medicament: string;
  laboratoire: string;
  familleArticle: string;
  classeTherapeutique: string;
  ppm: string;
  prixVenteMin: number;
  prixAchat: number;
  uniteVente: string;
  uniteAchat: string;
  stockMinQte: number;
  depositionMinQte: number;
  valorisationStock: 'FIFO' | 'LIFO' | 'FEFO' | 'PMP';
  interditVente: boolean;
  interditAchat: boolean;
  autoriseStockNegatif: boolean;
  numeroDeLot: boolean;
}

const Fiche_Article = () => {
  const [activeTab, setActiveTab] = useState<string>('fiche-article');
  const [articleData, setArticleData] = useState<ArticleData>({
    code: '',
    medicament: 'Médicament',
    laboratoire: '',
    familleArticle: '',
    classeTherapeutique: '',
    ppm: 'PPM',
    prixVenteMin: 1.00,
    prixAchat: 1.00,
    uniteVente: '',
    uniteAchat: '',
    stockMinQte: 0,
    depositionMinQte: 0,
    valorisationStock: 'LIFO',
    interditVente: false,
    interditAchat: false,
    autoriseStockNegatif: false,
    numeroDeLot: false,
  });

  const form = useForm({
    initialValues: articleData,
  });

  const familleArticleOptions = [
    'Famille 1',
    'Famille 2',
    'Famille 3',
  ];

  const classeTherapeutiqueOptions = [
    'Classe 1',
    'Classe 2',
    'Classe 3',
  ];

  const uniteOptions = [
    'Unité',
    'Boîte',
    'Flacon',
    'Comprimé',
  ];

  const handleSave = () => {
    notifications.show({
      title: 'Article enregistré',
      message: 'La fiche article a été enregistrée avec succès',
      color: 'green',
    });
  };

  const handleCancel = () => {
    notifications.show({
      title: 'Annulé',
      message: 'Les modifications ont été annulées',
      color: 'red',
    });
  };

  const handleRegisterAndQuit = () => {
    notifications.show({
      title: 'Enregistré et quitté',
      message: 'La fiche article a été enregistrée et fermée',
      color: 'green',
    });
  };

  const handleRegister = () => {
    notifications.show({
      title: 'Article enregistré',
      message: 'La fiche article a été enregistrée',
      color: 'green',
    });
  };

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconFileText size={24} className="text-slate-600" />
            <Title order={3} className="text-slate-600">
              Fiche article
            </Title>
          </Group>
          <Button
            variant="filled"
            className="bg-blue-500 hover:bg-blue-600"
            leftSection={<IconList size={16} />}
          >
            Liste des articles
          </Button>
        </Group>
      </Paper>

      {/* Tabs Section */}
      <Paper p="md" mb="md" withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'fiche-article')}>
          <Tabs.List mb="md">
            <Tabs.Tab value="fiche-article" leftSection={<IconPhoto size={16} />}>
              Fiche article
            </Tabs.Tab>
            <Tabs.Tab value="descriptif" leftSection={<IconFileText size={16} />}>
              Descriptif
            </Tabs.Tab>
            <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
              Pièces jointes
            </Tabs.Tab>
            <Tabs.Tab value="historique-achat" leftSection={<IconHistory size={16} />}>
              Historique Achat
            </Tabs.Tab>
            <Tabs.Tab value="historique-sortie" leftSection={<IconHistory size={16} />}>
              Historique Sortie
            </Tabs.Tab>
            <Tabs.Tab value="statistique" leftSection={<IconChartBar size={16} />}>
              Statistique
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="fiche-article">
            {/* Main Form */}
            <form onSubmit={form.onSubmit(() => {})}>
              {/* Code Section */}
              <Grid mb="md">
                <Grid.Col span={12}>
                  <TextInput
                    label="Code *"
                    placeholder=""
                    {...form.getInputProps('code')}
                    required
                    styles={{
                      label: { color: '#3b82f6' }
                    }}
                  />
                </Grid.Col>
              </Grid>

              {/* Medicament Section */}
              <Grid mb="md">
                <Grid.Col span={12}>
                  <Text size="lg" fw={500} mb="md" className="text-center">
                    Médicament
                  </Text>
                </Grid.Col>
              </Grid>

              {/* Laboratory and Family Section */}
              <Grid mb="md">
                <Grid.Col span={4}>
                  <TextInput
                    label="Laboratoire"
                    placeholder=""
                    {...form.getInputProps('laboratoire')}
                    rightSection={<ActionIcon size="sm" variant="subtle"><IconSearch size={16} /></ActionIcon>}
                  />
                </Grid.Col>
                <Grid.Col span={1}>
                  <ActionIcon size="lg" variant="filled" color="blue" mt="xl">
                    <IconPlus size={16} />
                  </ActionIcon>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    label="Famille d'article"
                    placeholder=""
                    data={familleArticleOptions}
                    {...form.getInputProps('familleArticle')}
                  />
                </Grid.Col>
                <Grid.Col span={1}>
                  <ActionIcon size="lg" variant="filled" color="blue" mt="xl">
                    <IconPlus size={16} />
                  </ActionIcon>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    label="Classe thérapeutique"
                    placeholder=""
                    data={classeTherapeutiqueOptions}
                    {...form.getInputProps('classeTherapeutique')}
                  />
                </Grid.Col>
              </Grid>

              {/* Pricing Section */}
              <Grid mb="md">
                <Grid.Col span={3}>
                  <Text size="sm" fw={500} mb="xs">PPM</Text>
                  <Text size="sm" className="border border-gray-300 p-2 rounded">
                    {form.values.ppm}
                  </Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label="Prix de vente Min"
                    placeholder="1,00"
                    {...form.getInputProps('prixVenteMin')}
                    decimalScale={2}
                    fixedDecimalScale
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label="Prix d'achat"
                    placeholder="1,00"
                    {...form.getInputProps('prixAchat')}
                    decimalScale={2}
                    fixedDecimalScale
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    placeholder="1,00"
                    decimalScale={2}
                    fixedDecimalScale
                    mt="xl"
                  />
                </Grid.Col>
              </Grid>

              {/* Units Section */}
              <Grid mb="md">
                <Grid.Col span={3}>
                  <Select
                    label="Unité de vente"
                    placeholder=""
                    data={uniteOptions}
                    {...form.getInputProps('uniteVente')}
                  />
                </Grid.Col>
                <Grid.Col span={1}>
                  <ActionIcon size="lg" variant="filled" color="blue" mt="xl">
                    <IconPlus size={16} />
                  </ActionIcon>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    label="Unité d'achat"
                    placeholder=""
                    data={uniteOptions}
                    {...form.getInputProps('uniteAchat')}
                  />
                </Grid.Col>
                <Grid.Col span={1}>
                  <ActionIcon size="lg" variant="filled" color="blue" mt="xl">
                    <IconPlus size={16} />
                  </ActionIcon>
                </Grid.Col>
                <Grid.Col span={4}>
                  {/* Empty space */}
                </Grid.Col>
              </Grid>

              {/* Stock Section */}
              <Grid mb="md">
                <Grid.Col span={3}>
                  <NumberInput
                    label="Stock.Min Qté"
                    placeholder="0"
                    {...form.getInputProps('stockMinQte')}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label="Déposition.Min Qté"
                    placeholder="0"
                    {...form.getInputProps('depositionMinQte')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  {/* Empty space */}
                </Grid.Col>
              </Grid>

              {/* Stock Valuation Section */}
              <Grid mb="md">
                <Grid.Col span={12}>
                  <Text size="sm" fw={500} mb="xs">Valorisation du stock:</Text>
                  <Radio.Group
                    value={form.values.valorisationStock}
                    onChange={(value) => form.setFieldValue('valorisationStock', value as 'FIFO' | 'LIFO' | 'FEFO' | 'PMP')}
                  >
                    <Group>
                      <Radio value="FIFO" label="FIFO" />
                      <Radio value="LIFO" label="LIFO" />
                      <Radio value="FEFO" label="FEFO" />
                      <Radio value="PMP" label="PMP" />
                    </Group>
                  </Radio.Group>
                </Grid.Col>
              </Grid>

              {/* Checkboxes Section */}
              <Grid mb="md">
                <Grid.Col span={3}>
                  <Checkbox
                    label="Interdit à la vente"
                    {...form.getInputProps('interditVente', { type: 'checkbox' })}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Checkbox
                    label="Interdit à l'achat"
                    {...form.getInputProps('interditAchat', { type: 'checkbox' })}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Checkbox
                    label="Autorisé stock négatif"
                    {...form.getInputProps('autoriseStockNegatif', { type: 'checkbox' })}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Checkbox
                    label="Numéro de lot"
                    {...form.getInputProps('numeroDeLot', { type: 'checkbox' })}
                  />
                </Grid.Col>
              </Grid>
            </form>
          </Tabs.Panel>

          <Tabs.Panel value="descriptif">
            <Descriptif />
          </Tabs.Panel>

          <Tabs.Panel value="pieces-jointes">
            <Text>Pièces jointes content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="historique-achat">
            <Text>Historique Achat content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="historique-sortie">
            <Text>Historique Sortie content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="statistique">
            <Text>Statistique content</Text>
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Action Buttons */}
      <Paper p="md" withBorder>
        <Group justify="flex-end">
          <Button
            variant="filled"
            color="red"
            onClick={handleCancel}
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="gray"
            onClick={handleRegisterAndQuit}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer et quitter
          </Button>
          <Button
            variant="filled"
            color="gray"
            onClick={handleRegister}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer
          </Button>
        </Group>
      </Paper>
    </div>
  );
};

export default Fiche_Article;
