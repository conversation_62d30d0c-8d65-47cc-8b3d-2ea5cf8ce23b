'use client';
import React, { useState, useRef, useEffect } from 'react';
import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  Loader,
  Card,
  Modal,
  Button,
  Badge,
  Alert
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconFolder,
  //IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconFile,
  IconTrash,
  IconDownload,
  IconCheck,
  IconAlertCircle
} from '@tabler/icons-react';
import AudioRecorder from './AudioRecorder';
import patientService, { PatientAttachment as PatientServiceAttachment } from '@/services/patientService';
import { patientFormService, PatientAttachment as PatientFormAttachment } from '@/services/patientFormService';

// Use PatientServiceAttachment as the main type for the component
type RealAttachment = PatientServiceAttachment;

interface AttachmentManagerProps {
  patientId: string;
  fullName?: string;
  onSave?: (blob: Blob, fileName: string) => void;
  onCancel?: () => void;
  onAttachmentsChange?: () => void; // Callback when attachments are added/removed
}

export default function AttachmentManager({
  patientId,
  fullName,
  onSave,
  onAttachmentsChange,
  //onCancel,
}: AttachmentManagerProps) {
  // Real attachment data from Django
  const [attachments, setAttachments] = useState<RealAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [uploading, setUploading] = useState(false);

  // UI state
  const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false);
  const [isCameraVisible, setIsCameraVisible] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);

  // Load attachments from Django backend
  useEffect(() => {
    const loadAttachments = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          // Load real attachments from Django using patientFormService
          console.log(`🔄 Loading attachments for patient: ${patientId}`);
          const formServiceAttachments = await patientFormService.getPatientAttachments(patientId);

          // Convert PatientFormAttachment to PatientServiceAttachment format
          const convertedAttachments: PatientServiceAttachment[] = formServiceAttachments.map(attachment => ({
            id: attachment.id,
            patient: attachment.patient,
            original_filename: attachment.file_name,
            file_size: attachment.file_size,
            mime_type: attachment.file_type,
            attachment_type: 'document' as const,
            category: attachment.category === 'medical' ? 'medical_record' as const : 'other' as const,
            title: attachment.file_name,
            description: attachment.description || '',
            is_private: false,
            is_sensitive: false,
            download_url: attachment.file_url,
            created_at: attachment.created_at,
          }));

          setAttachments(convertedAttachments);
          console.log('✅ Loaded attachments:', convertedAttachments.length);

          // Notify parent component about attachment changes
          if (onAttachmentsChange) {
            onAttachmentsChange();
          }
        } else {
          setError('Django backend is not connected');
        }
      } catch (err) {
        console.error('Error loading attachments:', err);
        setError('Failed to load attachments from Django backend');
        setDjangoStatus('disconnected');
      } finally {
        setLoading(false);
      }
    };

    loadAttachments();
  }, [patientId]);

  // Initialize webcam
  useEffect(() => {
    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      } catch (err) {
        console.error('Camera access error:', err);
      }
    };
    startCamera();

    return () => {
      // Clean up on unmount - capture current ref value
      const currentVideo = videoRef.current;
      const stream = currentVideo?.srcObject as MediaStream | null;
      stream?.getTracks().forEach((track) => track.stop());
    };
  }, []);

  const handleUpload = async (uploaded: File[]) => {
    if (djangoStatus !== 'connected') {
      setError('Django backend is not connected');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      for (const file of uploaded) {
        console.log(`Uploading file for patient: ${patientId}`, file.name);
        
        console.log('🔄 Uploading file via patientFormService...');
        const result = await patientFormService.uploadPatientAttachment(
          patientId,
          file,
          `${file.name} - ${getAttachmentType(file.type)}`,
          'other'
        );

        if (result) {
          // Convert patientFormService result to PatientServiceAttachment format
          const convertedAttachment: PatientServiceAttachment = {
            id: result.id,
            patient: patientId,
            original_filename: file.name,
            mime_type: file.type,
            file_size: file.size,
            attachment_type: getAttachmentType(file.type),
            category: 'other',
            title: file.name,
            description: result.description || '',
            is_private: false,
            is_sensitive: false,
            created_at: new Date().toISOString(),
            download_url: result.file_url || '',
          };

          setAttachments(prev => [...prev, convertedAttachment]);
          notifications.show({
            title: 'File Uploaded',
            message: `${file.name} has been uploaded successfully`,
            color: 'green',
            icon: <IconCheck size={16} />
          });

          // Notify parent component about attachment changes
          if (onAttachmentsChange) {
            onAttachmentsChange();
          }
        } else {
          notifications.show({
            title: 'Upload Failed',
            message: `Failed to upload ${file.name}`,
            color: 'red',
            icon: <IconAlertCircle size={16} />
          });
        }
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      setError('Error uploading files. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Helper function to determine attachment type from MIME type
  const getAttachmentType = (mimeType: string): RealAttachment['attachment_type'] => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.includes('dicom')) return 'dicom';
    return 'document';
  };

  const handleAudioSave = async (audioBlob: Blob, fileName: string) => {
    console.log('Audio saved:', fileName, audioBlob);

    if (djangoStatus !== 'connected') {
      setError('Django backend is not connected');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      // Create a File object from the Blob
      const audioFile = new File([audioBlob], fileName, {
        type: 'audio/webm',
        lastModified: Date.now()
      });

      // Upload to Django using patientFormService
      console.log('🔄 Uploading audio file via patientFormService...');
      const result = await patientFormService.uploadPatientAttachment(
        patientId,
        audioFile,
        `Audio recording - ${fileName}`,
        'other'
      );

      if (result) {
        // Convert patientFormService result to PatientServiceAttachment format
        const convertedAttachment: PatientServiceAttachment = {
          id: result.id,
          patient: patientId,
          original_filename: fileName,
          mime_type: 'audio/webm',
          file_size: audioBlob.size,
          attachment_type: 'audio',
          category: 'other',
          title: fileName,
          description: result.description || '',
          is_private: false,
          is_sensitive: false,
          created_at: new Date().toISOString(),
          download_url: result.file_url || '',
        };

        setAttachments(prev => [...prev, convertedAttachment]);
        notifications.show({
          title: 'Audio Recorded',
          message: `${fileName} has been saved successfully`,
          color: 'green',
          icon: <IconCheck size={16} />
        });

        // Notify parent component about attachment changes
        if (onAttachmentsChange) {
          onAttachmentsChange();
        }

        // Call original onSave if provided
        if (onSave) {
          onSave(audioBlob, fileName);
        }
      } else {
        notifications.show({
          title: 'Save Failed',
          message: `Failed to save ${fileName}`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        });
      }
    } catch (error) {
      console.error('Error saving audio:', error);
      setError('Error saving audio. Please try again.');
    } finally {
      setUploading(false);
    }

    // Close microphone modal
    setIsMicrophoneVisible(false);
  };

  // Handle file deletion
  const handleDeleteAttachment = async (attachment: RealAttachment) => {
    if (djangoStatus !== 'connected') {
      setError('Django backend is not connected');
      return;
    }

    try {
      const success = await patientService.deletePatientAttachment(patientId, attachment.id!);
      
      if (success) {
        setAttachments(prev => prev.filter(a => a.id !== attachment.id));
        notifications.show({
          title: 'File Deleted',
          message: `${attachment.original_filename} has been deleted`,
          color: 'green',
          icon: <IconCheck size={16} />
        });

        // Notify parent component about attachment changes
        if (onAttachmentsChange) {
          onAttachmentsChange();
        }
      } else {
        notifications.show({
          title: 'Delete Failed',
          message: `Failed to delete ${attachment.original_filename}`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        });
      }
    } catch (error) {
      console.error('Error deleting attachment:', error);
      setError('Error deleting file. Please try again.');
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file icon based on type
  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <IconCamera size={20} />;
    if (mimeType.startsWith('audio/')) return <IconMicrophone size={20} />;
    if (mimeType.startsWith('video/')) return <IconCamera size={20} />;
    return <IconFile size={20} />;
  };

  return (
    <Stack p="10px">
      {/* Header */}
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Patient Attachments</Text>
          {fullName && <Text c="dimmed">- {fullName}</Text>}
        </Group>
        
        {/* Django Status Badge */}
        <Badge 
          color={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'yellow'}
          variant="light"
          size="sm"
        >
          Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
        </Badge>
      </Group>

      {/* Error Alert */}
      {error && (
        <Alert 
          icon={<IconAlertCircle size={16} />} 
          title="Error" 
          color="red" 
          onClose={() => setError(null)}
          withCloseButton
        >
          {error}
        </Alert>
      )}

      {/* Upload Controls */}
      <Group gap="xs">
        <Tooltip label="Upload files">
          <FileInput
            placeholder="Select files to upload"
            multiple
            onChange={(files) => {
              if (files && files.length > 0) {
                handleUpload(files);
              }
            }}
            disabled={djangoStatus !== 'connected' || uploading}
            style={{ flex: 1 }}
          />
        </Tooltip>
        
        <Tooltip label="Record audio">
          <ActionIcon
            variant="light"
            onClick={() => setIsMicrophoneVisible(true)}
            disabled={djangoStatus !== 'connected' || uploading}
          >
            <IconMicrophone size={18} />
          </ActionIcon>
        </Tooltip>
        
        <Tooltip label="Take photo">
          <ActionIcon
            variant="light"
            onClick={() => setIsCameraVisible(true)}
            disabled={djangoStatus !== 'connected' || uploading}
          >
            <IconCamera size={18} />
          </ActionIcon>
        </Tooltip>
      </Group>

      {/* Loading State */}
      {(loading || uploading) && (
        <Group justify="center" p="xl">
          <Loader size="lg" />
          <Text>{loading ? 'Loading attachments...' : 'Uploading files...'}</Text>
        </Group>
      )}

      {/* Attachments List */}
      {!loading && (
        <ScrollArea h={400}>
          <Stack gap="sm">
            {attachments.length === 0 ? (
              <Text c="dimmed" ta="center" py="xl">
                No attachments found for this patient
              </Text>
            ) : (
              attachments.map((attachment, index) => (
                <Card key={attachment.id || `attachment-${index}`} withBorder p="sm">
                  <Group justify="space-between">
                    <Group>
                      {getFileIcon(attachment.mime_type)}
                      <Stack gap={0}>
                        <Text fw={500}>{attachment.original_filename}</Text>
                        <Group gap="xs">
                          <Text size="xs" c="dimmed">
                            {formatFileSize(attachment.file_size)}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {attachment.attachment_type}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {new Date(attachment.created_at!).toLocaleDateString()}
                          </Text>
                        </Group>
                        {attachment.description && (
                          <Text size="sm" c="dimmed" lineClamp={1}>
                            {attachment.description}
                          </Text>
                        )}
                      </Stack>
                    </Group>
                    
                    <Group gap="xs">
                      {attachment.download_url && (
                        <Tooltip label="Download">
                          <ActionIcon
                            variant="light"
                            onClick={() => window.open(attachment.download_url, '_blank')}
                          >
                            <IconDownload size={16} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                      
                      <Tooltip label="Delete">
                        <ActionIcon
                          variant="light"
                          color="red"
                          onClick={() => handleDeleteAttachment(attachment)}
                          disabled={uploading}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Group>
                </Card>
              ))
            )}
          </Stack>
        </ScrollArea>
      )}

      {/* Audio Recording Modal */}
      <Modal
        opened={isMicrophoneVisible}
        onClose={() => setIsMicrophoneVisible(false)}
        title="Record Audio"
        centered
      >
        <AudioRecorder
          patientId={patientId}
          onSave={handleAudioSave}
          onCancel={() => setIsMicrophoneVisible(false)}
        />
      </Modal>

      {/* Camera Modal */}
      <Modal
        opened={isCameraVisible}
        onClose={() => setIsCameraVisible(false)}
        title="Take Photo"
        centered
        size="lg"
      >
        <Stack align="center">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            style={{ width: '100%', maxWidth: 400, borderRadius: 8 }}
          />
          <Group>
            <Button onClick={() => setIsCameraVisible(false)} variant="outline">
              Cancel
            </Button>
            <Button onClick={() => {
              // Capture photo logic would go here
              setIsCameraVisible(false);
            }}>
              Capture
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
