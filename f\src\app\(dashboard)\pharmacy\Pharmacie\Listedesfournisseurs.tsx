'use client';
import React, { useState, useMemo, useEffect } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  ActionIcon,
  Text,
  Select,
  Pagination,
  Checkbox,
  Modal,
  Stack,
  Grid,
  Textarea,
  Loader,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconUsers,
} from '@tabler/icons-react';
import { supplierService, type Supplier } from '~/services/pharmacyService';

// Interface pour les fournisseurs (utilise maintenant le type Supplier du service)
// Gardons l'interface locale pour la compatibilité avec le code existant
interface Fournisseur extends Supplier {}

const Listedesfournisseurs = () => {
  // États pour la gestion de l'interface
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);
  const [opened, { open, close }] = useDisclosure(false);
  const [editingFournisseur, setEditingFournisseur] = useState<Fournisseur | null>(null);
  const [loading, setLoading] = useState(false);

  // Données des fournisseurs depuis l'API
  const [fournisseurs, setFournisseurs] = useState<Fournisseur[]>([]);

  // Charger les fournisseurs depuis l'API
  useEffect(() => {
    const loadFournisseurs = async () => {
      try {
        setLoading(true);
        const data = await supplierService.getAll();
        setFournisseurs(data);
      } catch (error) {
        console.error('Erreur lors du chargement des fournisseurs:', error);
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de charger les fournisseurs',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    loadFournisseurs();
  }, []);

  // Formulaire pour ajouter/modifier un fournisseur
  const form = useForm({
    initialValues: {
      raison_sociale: '',
      email: '',
      ville: '',
      tel_gestionnaire: '',
      tel_fixe: '',
      fax: '',
      adresse: '',
      commentaire: '',
      mode_paiement: '',
      condition_paiement: '',
      ice: '',
      rc: '',
      directeur_commercial: '',
      gestionnaire_vente: '',
    },
    validate: {
      raison_sociale: (value) => (!value ? 'La raison sociale est requise' : null),
      email: (value) => (!value ? 'L\'email est requis' : !/^\S+@\S+\.\S+$/.test(value) ? 'Email invalide' : null),
      tel_gestionnaire: (value) => (!value ? 'Le téléphone gestionnaire est requis' : null),
    },
  });

  // Filtrage des fournisseurs
  const filteredFournisseurs = useMemo(() => {
    return fournisseurs.filter(fournisseur =>
      fournisseur.raison_sociale.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fournisseur.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fournisseur.tel_gestionnaire.includes(searchQuery) ||
      fournisseur.tel_fixe.includes(searchQuery) ||
      fournisseur.fax.includes(searchQuery)
    );
  }, [fournisseurs, searchQuery]);

  // Pagination
  const totalPages = Math.ceil(filteredFournisseurs.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentFournisseurs = filteredFournisseurs.slice(startIndex, endIndex);

  // Gestion de la sélection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFournisseurs(currentFournisseurs.map(f => f.id!).filter(Boolean));
    } else {
      setSelectedFournisseurs([]);
    }
  };

  const handleSelectFournisseur = (id: string | undefined, checked: boolean) => {
    if (!id) return;

    if (checked) {
      setSelectedFournisseurs(prev => [...prev, id]);
    } else {
      setSelectedFournisseurs(prev => prev.filter(fId => fId !== id));
    }
  };

  // Gestion du formulaire
  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true);

      const supplierData = {
        raison_sociale: values.raison_sociale,
        email: values.email,
        ville: values.ville,
        tel_gestionnaire: values.tel_gestionnaire,
        tel_fixe: values.tel_fixe,
        fax: values.fax,
        adresse: values.adresse,
        commentaire: values.commentaire,
        mode_paiement: values.mode_paiement,
        condition_paiement: values.condition_paiement,
        ice: values.ice,
        rc: values.rc,
        directeur_commercial: values.directeur_commercial,
        gestionnaire_vente: values.gestionnaire_vente,
      };

      let updatedFournisseur: Fournisseur;

      if (editingFournisseur) {
        updatedFournisseur = await supplierService.update(editingFournisseur.id!, supplierData);
        setFournisseurs(prev => prev.map(f => f.id === editingFournisseur.id ? updatedFournisseur : f));
        notifications.show({
          title: 'Succès',
          message: 'Fournisseur modifié avec succès',
          color: 'green',
        });
      } else {
        updatedFournisseur = await supplierService.create(supplierData);
        setFournisseurs(prev => [...prev, updatedFournisseur]);
        notifications.show({
          title: 'Succès',
          message: 'Nouveau fournisseur ajouté avec succès',
          color: 'green',
        });
      }

      form.reset();
      setEditingFournisseur(null);
      close();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du fournisseur:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de sauvegarder le fournisseur',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Gestion de l'édition
  const handleEdit = (fournisseur: Fournisseur) => {
    setEditingFournisseur(fournisseur);
    form.setValues({
      raisonSociale: fournisseur.raisonSociale,
      email: fournisseur.email,
      ville: fournisseur.ville,
      telGestionnaire: fournisseur.telGestionnaire,
      telFixe: fournisseur.telFixe,
      fax: fournisseur.fax,
      adresse: fournisseur.adresse,
      commentaire: fournisseur.commentaire,
      modePaiement: fournisseur.modePaiement,
      conditionPaiement: fournisseur.conditionPaiement,
      ice: fournisseur.ice,
      rc: fournisseur.rc,
      directeurCommercial: fournisseur.directeurCommercial,
      gestionnairevente: fournisseur.gestionnairevente,
    });
    open();
  };

  // Gestion de la suppression
  const handleDelete = (id: string) => {
    setFournisseurs(prev => prev.filter(f => f.id !== id));
    setSelectedFournisseurs(prev => prev.filter(fId => fId !== id));
    notifications.show({
      title: 'Succès',
      message: 'Fournisseur supprimé avec succès',
      color: 'red',
    });
  };

  // Ouvrir le modal pour ajouter un nouveau fournisseur
  const handleAdd = () => {
    setEditingFournisseur(null);
    form.reset();
    open();
  };

  return (
    <div className="w-full">
      <Paper p="md" withBorder>
        {/* Header */}
        <Group justify="space-between" mb="lg">
          <Group align="center">
            <IconUsers size={24} color="blue" />
            <Title order={3} c="blue">
              Liste des fournisseurs
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleAdd}
            color="blue"
          >
            Fournisseur
          </Button>
        </Group>

        {/* Search Bar */}
        <Group mb="md">
          <TextInput
            placeholder="Rechercher"
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.currentTarget.value)}
            style={{ flex: 1 }}
          />
        </Group>

        {/* Table */}
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  checked={selectedFournisseurs.length === currentFournisseurs.length && currentFournisseurs.length > 0}
                  indeterminate={selectedFournisseurs.length > 0 && selectedFournisseurs.length < currentFournisseurs.length}
                  onChange={(event) => handleSelectAll(event.currentTarget.checked)}
                />
              </Table.Th>
              <Table.Th>Raison sociale</Table.Th>
              <Table.Th>Email</Table.Th>
              <Table.Th>Numéro de téléphone</Table.Th>
              <Table.Th>Fax</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentFournisseurs.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} style={{ textAlign: 'center', padding: '2rem' }}>
                  <Text c="dimmed">Aucun fournisseur trouvé</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentFournisseurs.map((fournisseur) => (
                <Table.Tr key={fournisseur.id}>
                  <Table.Td>
                    <Checkbox
                      checked={selectedFournisseurs.includes(fournisseur.id)}
                      onChange={(event) => handleSelectFournisseur(fournisseur.id, event.currentTarget.checked)}
                    />
                  </Table.Td>
                  <Table.Td>{fournisseur.raisonSociale}</Table.Td>
                  <Table.Td>{fournisseur.email}</Table.Td>
                  <Table.Td>{fournisseur.telGestionnaire}</Table.Td>
                  <Table.Td>{fournisseur.fax}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        color="blue"
                        variant="subtle"
                        onClick={() => handleEdit(fournisseur)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon
                        color="red"
                        variant="subtle"
                        onClick={() => handleDelete(fournisseur.id)}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>

        {/* Pagination */}
        <Group justify="space-between" mt="md">
          <Group>
            <Text size="sm">Page</Text>
            <Select
              size="sm"
              w={60}
              data={Array.from({ length: totalPages }, (_, i) => (i + 1).toString())}
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(parseInt(value || '1'))}
            />
            <Text size="sm">Lignes par Page</Text>
            <Select
              size="sm"
              w={60}
              data={['15', '25', '50', '100']}
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(parseInt(value || '15'))}
            />
            <Text size="sm">
              {startIndex + 1} - {Math.min(endIndex, filteredFournisseurs.length)} de {filteredFournisseurs.length}
            </Text>
          </Group>
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Paper>

      {/* Modal pour ajouter/modifier un fournisseur */}
      <Modal
        opened={opened}
        onClose={close}
        title={editingFournisseur ? 'Modifier le fournisseur' : 'Nouveau Fournisseur'}
        size="xl"
        styles={{
          title: {
            color: '#1976d2',
            fontWeight: 600,
          },
        }}
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            {/* Première ligne */}
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Raison sociale"
                  placeholder=""
                  {...form.getInputProps('raisonSociale')}
                  required
                  styles={{
                    label: { color: '#1976d2', fontWeight: 500 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Email"
                  placeholder=""
                  type="email"
                  {...form.getInputProps('email')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Deuxième ligne */}
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Ville"
                  placeholder=""
                  data={['Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Tanger']}
                  {...form.getInputProps('ville')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <TextInput
                  label="Tél.Gestionnaire"
                  placeholder=""
                  {...form.getInputProps('telGestionnaire')}
                  required
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <TextInput
                  label="Tél.Fixe"
                  placeholder=""
                  {...form.getInputProps('telFixe')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Troisième ligne */}
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Adresse"
                  placeholder=""
                  {...form.getInputProps('adresse')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <TextInput
                  label="Fax"
                  placeholder=""
                  {...form.getInputProps('fax')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <Textarea
                  label="Commentaire"
                  placeholder=""
                  rows={1}
                  {...form.getInputProps('commentaire')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Ligne de séparation */}
            <div style={{ borderTop: '1px dotted #ccc', margin: '16px 0' }} />

            {/* Quatrième ligne */}
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Mode de paiement"
                  placeholder=""
                  data={['Espèces', 'Chèque', 'Virement', 'Carte bancaire']}
                  {...form.getInputProps('modePaiement')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Condition de paiement"
                  placeholder=""
                  data={['Comptant', '30 jours', '60 jours', '90 jours']}
                  {...form.getInputProps('conditionPaiement')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Cinquième ligne */}
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="ICE"
                  placeholder=""
                  {...form.getInputProps('ice')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="R.C"
                  placeholder=""
                  {...form.getInputProps('rc')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Sixième ligne */}
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Directeur Commercial"
                  placeholder=""
                  {...form.getInputProps('directeurCommercial')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Gestionnaire de vente"
                  placeholder=""
                  {...form.getInputProps('gestionnairevente')}
                  styles={{
                    label: { color: '#666', fontWeight: 400 },
                  }}
                />
              </Grid.Col>
            </Grid>

            {/* Boutons d'action */}
            <Group justify="flex-end" mt="xl">
              <Button
                type="submit"
                variant="outline"
                color="gray"
                styles={{
                  root: { backgroundColor: '#f5f5f5', color: '#666', borderColor: '#ccc' },
                }}
              >
                Enregistrer
              </Button>
              <Button
                variant="filled"
                color="red"
                onClick={close}
                styles={{
                  root: { backgroundColor: '#d32f2f' },
                }}
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default Listedesfournisseurs;
