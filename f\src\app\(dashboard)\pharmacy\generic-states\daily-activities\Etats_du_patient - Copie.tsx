<div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon ng-class="vm.currentState.icon || 'mdi-arrow-right-drop-circle'" md-font-set="mdi" class="mdi mdi-account-details" role="img" aria-hidden="true"></md-icon>
        </div>
        <h2 translate-once="states_patient_status">États du patient</h2>
    </div>
</md-toolbar>

<md-content class="mn-module-body layout-fill flex layout-column md-padding ng-scope _md">
    <div class="layout-row flex-nogrow">

        <mn-input-search-modal ng-model="vm.query.patient" flex="nogrow" ng-change="vm.patientChanged()" class="ng-pristine ng-untouched ng-valid layout-row ng-isolate-scope flex-nogrow ng-empty" aria-invalid="false">
            <md-input-container ng-click="vm.showModal($event)" class="md-auto-horizontal-margin" role="button" tabindex="0">
                <input ng-model="vm.currentValue.full_name" aria-label="patient fullname" readonly="" class="ng-pristine ng-untouched ng-valid md-input ng-empty" autocomplete="off" id="input_1066" aria-invalid="false"><div class="md-errors-spacer"></div>
                <label translate-once="payment_choose_patient" for="input_1066">Choisir un patient</label>
                <div class="mn-option-buttons flex-nogrow layout-row">
                    <!-- ngIf: !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled" ng-click="vm.showModal($event)" aria-label="search">
                        <md-icon md-font-icon="mdi-magnify" md-font-set="mdi" class="ng-scope md-font mdi mdi-magnify" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: !vm.disabled -->
                    <!-- ngIf: vm.choose && !vm.disabled -->
                </div>
            </md-input-container>
        </mn-input-search-modal>

        <hr class="mn-sep vertical-sep dashed flex-nogrow">

        <md-input-container class="w-option-buttons mn-datepicker-container md-auto-horizontal-margin _md-datepicker-floating-label _md-datepicker-has-calendar-icon">
            <label translate-once="from_date">Du</label>
            <md-datepicker mn-date="" ng-model="vm.query.start" ng-change="vm.queryChanged()" md-max-date="vm.maxDate" class="ng-pristine ng-untouched ng-valid _md-datepicker-has-triangle-icon ng-isolate-scope ng-empty ng-valid-mindate ng-valid-maxdate ng-valid-filtered ng-valid-valid" tabindex="-1" aria-owns="md-date-pane-1067" type="date" aria-invalid="false"><button class="md-datepicker-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" tabindex="-1" aria-hidden="true" ng-click="ctrl.openCalendarPane($event)"><md-icon class="md-datepicker-calendar-icon ng-scope" aria-label="md-calendar" md-svg-src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjExIDAtMS45OS45LTEuOTkgMkwzIDE5YzAgMS4xLjg5IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMTZINVY4aDE0djExek03IDEwaDV2NUg3eiIvPjwvc3ZnPg==" role="img"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"></path></svg></md-icon></button><div class="md-datepicker-input-container" ng-class="{'md-datepicker-focused': ctrl.isFocused}"><input class="md-datepicker-input md-input" aria-haspopup="dialog" ng-focus="ctrl.setFocused(true)" ng-blur="ctrl.setFocused(false)" autocomplete="off" id="input_1068" size="3"> <button class="md-datepicker-triangle-button md-icon-button md-button" type="button" ng-transclude="" md-no-ink="" ng-click="ctrl.openCalendarPane($event)" aria-label="Open calendar" tabindex="-1"><div class="md-datepicker-expand-triangle ng-scope"></div></button></div><div class="md-datepicker-calendar-pane md-whiteframe-z1" id="md-date-pane-1067"><div class="md-datepicker-input-mask"><div class="md-datepicker-input-mask-opaque"></div></div><div class="md-datepicker-calendar"><!-- ngIf: ctrl.isCalendarOpen --></div></div></md-datepicker><div><div class="md-errors-spacer"></div></div>
            <div class="mn-option-buttons flex-nogrow layout-row">
                <!-- ngIf: vm.query.start -->
            </div>
        </md-input-container>

        <md-input-container class="w-option-buttons mn-datepicker-container md-auto-horizontal-margin _md-datepicker-floating-label _md-datepicker-has-calendar-icon">
            <label translate-once="to_date">Au</label>
            <md-datepicker mn-date="" md-max-date="vm.maxDate" ng-model="vm.query.end" ng-change="vm.queryChanged()" class="ng-pristine ng-untouched ng-valid _md-datepicker-has-triangle-icon ng-isolate-scope ng-empty ng-valid-mindate ng-valid-maxdate ng-valid-filtered ng-valid-valid" tabindex="-1" aria-owns="md-date-pane-1069" type="date" aria-invalid="false"><button class="md-datepicker-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" tabindex="-1" aria-hidden="true" ng-click="ctrl.openCalendarPane($event)"><md-icon class="md-datepicker-calendar-icon ng-scope" aria-label="md-calendar" md-svg-src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjExIDAtMS45OS45LTEuOTkgMkwzIDE5YzAgMS4xLjg5IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMTZINVY4aDE0djExek03IDEwaDV2NUg3eiIvPjwvc3ZnPg==" role="img"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"></path></svg></md-icon></button><div class="md-datepicker-input-container" ng-class="{'md-datepicker-focused': ctrl.isFocused}"><input class="md-datepicker-input md-input" aria-haspopup="dialog" ng-focus="ctrl.setFocused(true)" ng-blur="ctrl.setFocused(false)" autocomplete="off" id="input_1070" size="3"> <button class="md-datepicker-triangle-button md-icon-button md-button" type="button" ng-transclude="" md-no-ink="" ng-click="ctrl.openCalendarPane($event)" aria-label="Open calendar" tabindex="-1"><div class="md-datepicker-expand-triangle ng-scope"></div></button></div><div class="md-datepicker-calendar-pane md-whiteframe-z1" id="md-date-pane-1069"><div class="md-datepicker-input-mask"><div class="md-datepicker-input-mask-opaque"></div></div><div class="md-datepicker-calendar"><!-- ngIf: ctrl.isCalendarOpen --></div></div></md-datepicker><div><div class="md-errors-spacer"></div></div>
            <div class="mn-option-buttons flex-nogrow layout-row">
                <!-- ngIf: vm.query.end -->
            </div>
        </md-input-container>

        <div flex="nogrow" layout="column" class="mn-radio-group-container layout-column flex-nogrow" style="margin-left: 12px">
            <label translate-once="states_data_source">Source de données</label>
            <md-radio-group ng-model="vm.state" layout="row" ng-change="vm.handleStatesChange()" class="ng-pristine ng-untouched ng-valid _md layout-row ng-not-empty" role="radiogroup" tabindex="0" aria-invalid="false" aria-activedescendant="radio_1073">
                <!-- ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin md-checked" id="radio_1073" role="radio" aria-checked="true" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">Etat du Compte général</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin" id="radio_1074" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">Les actes</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin" id="radio_1075" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">Les actes dentaires</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin" id="radio_1076" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">les actes de soins</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin" id="radio_1077" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">Encaissements</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="state base" ng-class="{'mn-warn': state.deactivated}" class="ng-scope md-auto-horizontal-margin" id="radio_1078" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <span ng-bind="state.label|translate" class="ng-binding ng-scope">Paiements</span>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name -->
            </md-radio-group>
        </div>

        <span class="flex"></span>

        <!--        <md-input-container class="flex-none" ng-if="vm.state.type == 'procedure'">-->
        <!--            <label translate-once="states_procedure_activity_label"></label>-->
        <!--            <md-select name="" ng-model="vm.procedureActivityType" ng-change="vm.getData()">-->
        <!--                <md-option ng-repeat="type in vm.procedureActivityTypes" ng-value="type.value">-->
        <!--                    <span translate-once="{{type.label}}"></span>-->
        <!--                </md-option>-->
        <!--            </md-select>-->
        <!--        </md-input-container>-->
    </div>

    <hr class="mn-sep flex-nogrow">

    <!-- ngIf: !vm.hideUI -->

    <!-- ngIf: vm.hideUI --><div class="empty-content layout-row layout-align-start-center ng-scope" ng-if="vm.hideUI">
        <md-icon md-font-icon="mdi-alert-circle-outline" md-font-set="mdi" aria-label="warning" class="md-font mdi mdi-alert-circle-outline" role="img"></md-icon>
        <span class="flex" translate-once="no_element_selected">Aucun élément sélectionné.</span>
    </div><!-- end ngIf: vm.hideUI -->
</md-content></div>