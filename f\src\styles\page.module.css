.card {
  width: rem(420px);
  margin-top: rem(15px);
  padding: var(--mantine-spacing-lg);
  border-radius: var(--mantine-radius-default);
  box-shadow: var(--mantine-shadow-xl);
  border: rem(1px) solid var(--mantine-color-gray-3);
  background-color: var(--bg-body);
  /* @mixin light {
    background-color: var(--mantine-color-gray-0);
    color: var(--mantine-color-black);
  }

  @mixin dark {
    background-color: var(--mantine-color-dark-7);
    color: var(--mantine-color-white);
  }

  @media (max-width: $mantine-breakpoint-sm) {
    width: rem(360);
  } */
}

.link {
  padding: rem(6px) rem(10px);
  border-radius: var(--mantine-radius-default);
  color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
  font-weight: 500;

  @mixin light {
    color: var(--mantine-color-black);
  }

  @mixin dark {
    color: var(--mantine-color-white);
  }

  @mixin hover {
    background-color: light-dark(
      var(--mantine-color-dark-5),
      var(--mantine-color-gray-2)
    );
    text-decoration: none;
    transition: all ease 150ms;

    @mixin light {
      background-color: var(--mantine-color-gray-2);
    }

    @mixin dark {
      background-color: var(--mantine-color-dark-4);
    }
  }
}

.label {
  @mixin light {
    color: var(--mantine-color-black);
  }

  @mixin dark {
    color: var(--mantine-color-white);
  }
}
