// components/PatientAlertDialog.tsx

import {
  Modal,
  Table,
  Text,
  Loader,
  ScrollArea,
  Group,
  ActionIcon,
  Title,
  Box,
 
} from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiClose, mdiPlus, mdiHistory } from '@mdi/js';

interface AlertItem {
  id: string;
  trigger: string;
  level: string;
  isPublic: boolean;
  isPermanent: boolean;
  description: string;
}

interface PatientAlertDialogProps {
  opened: boolean;
  onClose: () => void;
  onAddAlert: () => void;
  alerts: AlertItem[];
  isLoading: boolean;
  patientName: string;
}

export default function PatientAlertDialog({
  opened,
  onClose,
  onAddAlert,
  alerts,
  isLoading,
  patientName,
}: PatientAlertDialogProps) {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="sm" align="center">
          <Icon path={mdiHistory} size={1} />
          <Title order={4}>Alerts – {patientName}</Title>
          <Box style={{ flex: 1 }} />
          <ActionIcon
            variant="light"
            color="blue"
            aria-label="Add alert"
            onClick={onAddAlert}
          >
            <Icon path={mdiPlus} size={1} />
          </ActionIcon>
          <ActionIcon variant="light" color="red" onClick={onClose} aria-label="Fermer">
            <Icon path={mdiClose} size={1} />
          </ActionIcon>
        </Group>
      }
      size="lg"
      padding="md"
      centered
      withCloseButton={false}
    >
      <ScrollArea h={400}>
        <Table striped highlightOnHover withTableBorder withColumnBorders>
          <thead>
            <tr>
              <th>Déclencheur</th>
              <th>Niveau</th>
              <th>Publique</th>
              <th>Permanente</th>
              <th>Description</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={6}>
                  <Group justify="center" py="md">
                    <Loader size="sm" />
                  </Group>
                </td>
              </tr>
            ) : alerts.length === 0 ? (
              <tr>
                <td colSpan={6}>
                  <Text ta="center" c="dimmed" py="md">
                    Aucun élément trouvé.
                  </Text>
                </td>
              </tr>
            ) : (
              alerts.map((item) => (
                <tr key={item.id}>
                  <td>{item.trigger}</td>
                  <td>{item.level}</td>
                  <td>{item.isPublic ? 'Oui' : 'Non'}</td>
                  <td>{item.isPermanent ? 'Oui' : 'Non'}</td>
                  <td>{item.description}</td>
                  <td>{/* boutons d'actions optionnels ici */}</td>
                </tr>
              ))
            )}
          </tbody>
        </Table>
      </ScrollArea>
    </Modal>
  );
}
