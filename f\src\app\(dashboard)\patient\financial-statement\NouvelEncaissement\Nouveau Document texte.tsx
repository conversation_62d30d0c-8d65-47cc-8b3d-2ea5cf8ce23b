import React, { useState, useRef, useEffect } from 'react';
import { Mic, X } from 'lucide-react';

const VoiceRecognitionDialog = ({ isOpen, onClose, onSubmit }) => {
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [validSpeech, setValidSpeech] = useState('');
  const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
  const [isListening, setIsListening] = useState(false);
  
  const recognitionRef = useRef(null);
  const validSpeechRef = useRef(null);

  useEffect(() => {
    // Initialize speech recognition if available
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      
      if (recognitionRef.current) {
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.lang = 'fr-FR';

        recognitionRef.current.onstart = () => {
          setIsListening(true);
          setInvalidSpeech('Parlez maintenant...');
        };

        recognitionRef.current.onresult = (event) => {
          let interimTranscript = '';
          let finalTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }

          setValidSpeech(finalTranscript);
          setInvalidSpeech(interimTranscript || 'Parlez maintenant...');
        };

        recognitionRef.current.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
          setIsRecognizing(false);
          setInvalidSpeech('Erreur de reconnaissance vocale.');
        };

        recognitionRef.current.onend = () => {
          setIsListening(false);
          if (isRecognizing) {
            // Restart if still in recognition mode
            recognitionRef.current?.start();
          }
        };
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [isRecognizing]);

  const toggleRecognition = () => {
    if (!recognitionRef.current) {
      alert('La reconnaissance vocale n\'est pas supportée par ce navigateur.');
      return;
    }

    if (isRecognizing) {
      recognitionRef.current.stop();
      setIsRecognizing(false);
      setIsListening(false);
    } else {
      recognitionRef.current.start();
      setIsRecognizing(true);
    }
  };

  const emptyContent = () => {
    setValidSpeech('');
    setInvalidSpeech('Parlez maintenant.');
    if (validSpeechRef.current) {
      validSpeechRef.current.textContent = '';
    }
  };

  const handleSubmit = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsRecognizing(false);
    onSubmit?.(validSpeech);
  };

  const handleCancel = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsRecognizing(false);
    setIsListening(false);
    onClose?.();
  };

  const handleValidSpeechEdit = (e) => {
    setValidSpeech(e.target.textContent || '');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4" role="dialog" aria-label="Voice Recognition">
        {/* Dialog Content */}
        <div className="p-6">
          <div className="flex items-start gap-4">
            <div className="flex-1">
              <p className="text-sm text-gray-600 mb-2">Reconnaissance vocale:</p>
              <div className="min-h-[60px] p-3 border rounded-md bg-gray-50">
                <span
                  ref={validSpeechRef}
                  contentEditable={true}
                  suppressContentEditableWarning={true}
                  className="block text-black font-medium outline-none"
                  onInput={handleValidSpeechEdit}
                  style={{ minHeight: '1.2em' }}
                >
                  {validSpeech}
                </span>
                <span className="text-gray-500 italic">
                  {!validSpeech && invalidSpeech}
                </span>
              </div>
            </div>
            
            {/* Control Buttons */}
            <div className="flex flex-col gap-2">
              <button
                onClick={toggleRecognition}
                className={`p-2 rounded-full transition-colors ${
                  isRecognizing 
                    ? 'bg-red-500 hover:bg-red-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                } ${isListening ? 'animate-pulse' : ''}`}
                aria-label="Toggle microphone"
                type="button"
              >
                <Mic size={20} />
              </button>
              
              <button
                onClick={emptyContent}
                className="p-2 rounded-full bg-red-100 hover:bg-red-200 text-red-600 transition-colors"
                aria-label="Clear content"
                type="button"
              >
                <X size={20} />
              </button>
            </div>
          </div>
        </div>
        
        {/* Dialog Actions */}
        <div className="flex justify-end gap-2 p-6 pt-0 border-t">
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            type="submit"
            aria-label="Submit"
          >
            Valider
          </button>
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md transition-colors"
            type="button"
            aria-label="Cancel"
          >
            Annuler
          </button>
        </div>
      </div>
    </div>
  );
};

// Example usage component
const App = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleSubmit = (speechText) => {
    console.log('Speech text:', speechText);
    setIsDialogOpen(false);
  };

  const handleClose = () => {
    setIsDialogOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Voice Recognition Dialog</h1>
        <button
          onClick={() => setIsDialogOpen(true)}
          className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          Open Voice Recognition
        </button>
        
        <VoiceRecognitionDialog
          isOpen={isDialogOpen}
          onClose={handleClose}
          onSubmit={handleSubmit}
        />
      </div>
    </div>
  );
};

export default App;