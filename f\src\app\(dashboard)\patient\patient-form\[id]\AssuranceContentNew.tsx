'use client';
import { 
  Card, Text, Group, Badge, ActionIcon, Stack, 
  Paper, Title, Alert,  Grid, Divider
} from '@mantine/core';
import Icon from '@mdi/react';
import { 
  mdiPencil, mdiDelete, mdiShieldAccount, mdiCalendar, 
  mdiPhone, mdiWeb, mdiCurrencyUsd, mdiCheckCircle 
} from '@mdi/js';
import { Patient } from '@/types/typesCalendarPatient';
import { PatientInsurance } from '@/services/patientService';
import { IconAlertCircle,  } from '@tabler/icons-react';

interface AssuranceContentProps {
  patient: Patient;
  insurances: PatientInsurance[];
  onEditInsurance: (insurance: PatientInsurance) => void;
  onDeleteInsurance: (insuranceId: string) => void;
  djangoConnected: boolean;
}

export const AssuranceContent = ({
  patient,
  insurances,
  onEditInsurance,
  onDeleteInsurance,
  djangoConnected,
}: AssuranceContentProps) => {

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Not specified';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Invalid date';
    }
  };

  // Check if insurance is active
  const isInsuranceActive = (insurance: PatientInsurance) => {
    if (!insurance.expiry_date) return true;
    return new Date(insurance.expiry_date) > new Date();
  };

  // Check if insurance is expiring soon (within 30 days)
  const isExpiringSoon = (insurance: PatientInsurance) => {
    if (!insurance.expiry_date) return false;
    const expiryDate = new Date(insurance.expiry_date);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return expiryDate <= thirtyDaysFromNow && expiryDate > new Date();
  };

  if (!djangoConnected) {
    return (
      <Alert 
        icon={<IconAlertCircle size={16} />} 
        title="Django Connection Required" 
        color="red"
      >
        Please ensure Django backend is running to manage patient insurance information.
      </Alert>
    );
  }

  if (insurances.length === 0) {
    return (
      <Paper p="xl" withBorder style={{ textAlign: 'center' }}>
        <Stack align="center" gap="md">
          <Icon path={mdiShieldAccount} size={3} color="#e0e0e0" />
          <div>
            <Title order={3} c="dimmed">No Insurance Information</Title>
            <Text c="dimmed" size="sm">
              No insurance policies found for {patient?.first_name} {patient?.last_name}.
              Click &quot;Add Insurance&quot; to add a new policy.
            </Text>
          </div>
        </Stack>
      </Paper>
    );
  }

  return (
    <Stack gap="md">
      <Title order={3}>
        <Group gap="sm">
          <Icon path={mdiShieldAccount} size={1} />
          Insurance Policies ({insurances.length})
        </Group>
      </Title>

      <Grid>
        {insurances.map((insurance, index) => (
          <Grid.Col span={12} key={insurance.id || `insurance-${index}`}>
            <Card withBorder shadow="sm" p="md">
              <Group justify="space-between" mb="sm">
                <Group gap="sm">
                  <Icon path={mdiShieldAccount} size={1} color="#228be6" />
                  <div>
                    <Text fw={600} size="lg">{insurance.provider_name}</Text>
                    <Text size="sm" c="dimmed">Policy: {insurance.policy_number}</Text>
                  </div>
                </Group>
                
                <Group gap="xs">
                  {/* Status Badge */}
                  <Badge 
                    color={
                      !isInsuranceActive(insurance) ? 'red' :
                      isExpiringSoon(insurance) ? 'yellow' : 'green'
                    }
                    variant="light"
                  >
                    {!isInsuranceActive(insurance) ? 'Expired' :
                     isExpiringSoon(insurance) ? 'Expiring Soon' : 'Active'}
                  </Badge>
                  
                  {/* Action Buttons */}
                  <ActionIcon 
                    variant="light" 
                    color="blue"
                    onClick={() => onEditInsurance(insurance)}
                  >
                    <Icon path={mdiPencil} size={0.8} />
                  </ActionIcon>
                  <ActionIcon 
                    variant="light" 
                    color="red"
                    onClick={() => insurance.id && onDeleteInsurance(insurance.id)}
                  >
                    <Icon path={mdiDelete} size={0.8} />
                  </ActionIcon>
                </Group>
              </Group>

              <Divider mb="sm" />

              {/* Insurance Details */}
              <Grid>
                <Grid.Col span={6}>
                  <Stack gap="xs">
                    <Group gap="xs">
                      <Icon path={mdiCalendar} size={0.7} />
                      <Text size="sm" fw={500}>Coverage Period</Text>
                    </Group>
                    <Text size="sm" c="dimmed" pl="md">
                      {formatDate(insurance.effective_date)} - {formatDate(insurance.expiry_date)}
                    </Text>
                  </Stack>
                </Grid.Col>

                <Grid.Col span={6}>
                  <Stack gap="xs">
                    <Group gap="xs">
                      <Icon path={mdiCheckCircle} size={0.7} />
                      <Text size="sm" fw={500}>Coverage Type</Text>
                    </Group>
                    <Text size="sm" c="dimmed" pl="md">
                      {insurance.coverage_type || 'Not specified'}
                    </Text>
                  </Stack>
                </Grid.Col>

                {insurance.group_number && (
                  <Grid.Col span={6}>
                    <Stack gap="xs">
                      <Text size="sm" fw={500}>Group Number</Text>
                      <Text size="sm" c="dimmed">{insurance.group_number}</Text>
                    </Stack>
                  </Grid.Col>
                )}

                <Grid.Col span={6}>
                  <Stack gap="xs">
                    <Text size="sm" fw={500}>Coverage</Text>
                    <Group gap="xs">
                      {insurance.medical_coverage && (
                        <Badge size="sm" color="blue" variant="light">Medical</Badge>
                      )}
                      {insurance.dental_coverage && (
                        <Badge size="sm" color="green" variant="light">Dental</Badge>
                      )}
                    </Group>
                  </Stack>
                </Grid.Col>

                {(insurance.copay_amount || insurance.deductible_amount) && (
                  <Grid.Col span={12}>
                    <Group gap="md">
                      {insurance.copay_amount && (
                        <Group gap="xs">
                          <Icon path={mdiCurrencyUsd} size={0.7} />
                          <Text size="sm">
                            <Text span fw={500}>Copay:</Text> ${insurance.copay_amount}
                          </Text>
                        </Group>
                      )}
                      {insurance.deductible_amount && (
                        <Group gap="xs">
                          <Icon path={mdiCurrencyUsd} size={0.7} />
                          <Text size="sm">
                            <Text span fw={500}>Deductible:</Text> ${insurance.deductible_amount}
                          </Text>
                        </Group>
                      )}
                    </Group>
                  </Grid.Col>
                )}

                {(insurance.provider_phone || insurance.provider_website) && (
                  <Grid.Col span={12}>
                    <Divider my="xs" />
                    <Group gap="md">
                      {insurance.provider_phone && (
                        <Group gap="xs">
                          <Icon path={mdiPhone} size={0.7} />
                          <Text size="sm">{insurance.provider_phone}</Text>
                        </Group>
                      )}
                      {insurance.provider_website && (
                        <Group gap="xs">
                          <Icon path={mdiWeb} size={0.7} />
                          <Text 
                            size="sm" 
                            c="blue" 
                            style={{ cursor: 'pointer' }}
                            onClick={() => window.open(insurance.provider_website, '_blank')}
                          >
                            {insurance.provider_website}
                          </Text>
                        </Group>
                      )}
                    </Group>
                  </Grid.Col>
                )}

                {insurance.notes && (
                  <Grid.Col span={12}>
                    <Divider my="xs" />
                    <Stack gap="xs">
                      <Text size="sm" fw={500}>Notes</Text>
                      <Text size="sm" c="dimmed">{insurance.notes}</Text>
                    </Stack>
                  </Grid.Col>
                )}
              </Grid>
            </Card>
          </Grid.Col>
        ))}
      </Grid>

      {/* Summary Card */}
      <Card withBorder p="md" bg="gray.0">
        <Group justify="space-between">
          <div>
            <Text fw={600}>Insurance Summary</Text>
            <Text size="sm" c="dimmed">
              {insurances.filter(ins => isInsuranceActive(ins)).length} active, {' '}
              {insurances.filter(ins => isExpiringSoon(ins)).length} expiring soon, {' '}
              {insurances.filter(ins => !isInsuranceActive(ins)).length} expired
            </Text>
          </div>
          <Badge 
            size="lg" 
            color={insurances.some(ins => isInsuranceActive(ins)) ? 'green' : 'red'}
            variant="light"
          >
            {insurances.some(ins => isInsuranceActive(ins)) ? 'Insured' : 'No Active Coverage'}
          </Badge>
        </Group>
      </Card>
    </Stack>
  );
};
