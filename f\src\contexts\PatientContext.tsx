'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconX } from '@tabler/icons-react';
import { 
  globalPatientService, 
  GlobalPatient, 
  PatientSearchResult, 
  PatientSummary,
  PatientStats 
} from '../services/globalPatientService';

// Context Types
interface PatientContextType {
  // State
  patients: GlobalPatient[];
  currentPatient: GlobalPatient | null;
  patientSummary: PatientSummary | null;
  patientStats: PatientStats | null;
  searchResults: PatientSearchResult[];
  loading: boolean;
  error: string | null;
  
  // Actions
  loadPatients: (params?: any) => Promise<void>;
  loadPatient: (id: string) => Promise<void>;
  loadPatientSummary: (id: string) => Promise<void>;
  loadPatientStats: () => Promise<void>;
  searchPatients: (query: string) => Promise<void>;
  createPatient: (data: Partial<GlobalPatient>) => Promise<GlobalPatient | null>;
  updatePatient: (id: string, data: Partial<GlobalPatient>) => Promise<GlobalPatient | null>;
  deletePatient: (id: string) => Promise<boolean>;
  setCurrentPatient: (patient: GlobalPatient | null) => void;
  clearError: () => void;
  refreshCurrentPatient: () => Promise<void>;
}

// Create Context
const PatientContext = createContext<PatientContextType | undefined>(undefined);

// Provider Props
interface PatientProviderProps {
  children: ReactNode;
}

// Patient Provider Component
export const PatientProvider: React.FC<PatientProviderProps> = ({ children }) => {
  // State
  const [patients, setPatients] = useState<GlobalPatient[]>([]);
  const [currentPatient, setCurrentPatientState] = useState<GlobalPatient | null>(null);
  const [patientSummary, setPatientSummary] = useState<PatientSummary | null>(null);
  const [patientStats, setPatientStats] = useState<PatientStats | null>(null);
  const [searchResults, setSearchResults] = useState<PatientSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Actions
  const loadPatients = async (params?: any) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await globalPatientService.getAllPatients(params);
      setPatients(response.results);
      
      console.log('✅ Patients loaded successfully:', response.results.length);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load patients';
      setError(errorMessage);
      console.error('❌ Error loading patients:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger la liste des patients',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  const loadPatient = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const patient = await globalPatientService.getPatient(id);
      setCurrentPatientState(patient);
      
      console.log('✅ Patient loaded successfully:', patient);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load patient';
      setError(errorMessage);
      console.error('❌ Error loading patient:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger les données du patient',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  const loadPatientSummary = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const summary = await globalPatientService.getPatientSummary(id);
      setPatientSummary(summary);
      
      console.log('✅ Patient summary loaded successfully:', summary);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load patient summary';
      setError(errorMessage);
      console.error('❌ Error loading patient summary:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPatientStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const stats = await globalPatientService.getPatientStats();
      setPatientStats(stats);
      
      console.log('✅ Patient stats loaded successfully:', stats);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load patient stats';
      setError(errorMessage);
      console.error('❌ Error loading patient stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const searchPatients = async (query: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const results = await globalPatientService.searchPatients(query);
      setSearchResults(results);
      
      console.log('✅ Patient search completed:', results.length, 'results');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search patients';
      setError(errorMessage);
      console.error('❌ Error searching patients:', err);
      
      notifications.show({
        title: 'Erreur de recherche',
        message: 'Impossible de rechercher les patients',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  const createPatient = async (data: Partial<GlobalPatient>): Promise<GlobalPatient | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const newPatient = await globalPatientService.createPatient(data);
      
      // Add to patients list
      setPatients(prev => [newPatient, ...prev]);
      
      notifications.show({
        title: 'Succès',
        message: 'Patient créé avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      });
      
      console.log('✅ Patient created successfully:', newPatient);
      return newPatient;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create patient';
      setError(errorMessage);
      console.error('❌ Error creating patient:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de créer le patient',
        color: 'red',
        icon: <IconX size={16} />
      });
      
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updatePatient = async (id: string, data: Partial<GlobalPatient>): Promise<GlobalPatient | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const updatedPatient = await globalPatientService.updatePatient(id, data);
      
      // Update in patients list
      setPatients(prev => prev.map(p => p.id === id ? updatedPatient : p));
      
      // Update current patient if it's the same
      if (currentPatient?.id === id) {
        setCurrentPatientState(updatedPatient);
      }
      
      notifications.show({
        title: 'Succès',
        message: 'Patient mis à jour avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      });
      
      console.log('✅ Patient updated successfully:', updatedPatient);
      return updatedPatient;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update patient';
      setError(errorMessage);
      console.error('❌ Error updating patient:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de mettre à jour le patient',
        color: 'red',
        icon: <IconX size={16} />
      });
      
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deletePatient = async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      await globalPatientService.deletePatient(id);
      
      // Remove from patients list
      setPatients(prev => prev.filter(p => p.id !== id));
      
      // Clear current patient if it's the same
      if (currentPatient?.id === id) {
        setCurrentPatientState(null);
        setPatientSummary(null);
      }
      
      notifications.show({
        title: 'Succès',
        message: 'Patient supprimé avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      });
      
      console.log('✅ Patient deleted successfully:', id);
      return true;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete patient';
      setError(errorMessage);
      console.error('❌ Error deleting patient:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de supprimer le patient',
        color: 'red',
        icon: <IconX size={16} />
      });
      
      return false;
    } finally {
      setLoading(false);
    }
  };

  const setCurrentPatient = (patient: GlobalPatient | null) => {
    setCurrentPatientState(patient);
    if (!patient) {
      setPatientSummary(null);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const refreshCurrentPatient = async () => {
    if (currentPatient?.id) {
      await loadPatient(currentPatient.id);
    }
  };

  // Context value
  const value: PatientContextType = {
    // State
    patients,
    currentPatient,
    patientSummary,
    patientStats,
    searchResults,
    loading,
    error,
    
    // Actions
    loadPatients,
    loadPatient,
    loadPatientSummary,
    loadPatientStats,
    searchPatients,
    createPatient,
    updatePatient,
    deletePatient,
    setCurrentPatient,
    clearError,
    refreshCurrentPatient
  };

  return (
    <PatientContext.Provider value={value}>
      {children}
    </PatientContext.Provider>
  );
};

// Custom hook to use Patient Context
export const usePatient = (): PatientContextType => {
  const context = useContext(PatientContext);
  if (context === undefined) {
    throw new Error('usePatient must be used within a PatientProvider');
  }
  return context;
};

export default PatientContext;
