'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Avatar,
  TextInput,
  Textarea,
  Stack,
  Divider,
  Badge,
  Loader,
  Alert,
  Paper,
  ActionIcon,
  Grid,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconAlertCircle,
  IconCheck,
  IconSend,
  IconSearch,
  IconTrash,
  IconMessageCircle,
} from '@tabler/icons-react';
import authService, { UserProfile } from '@/services/authService';

interface Message {
  id: string;
  sender: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  recipient: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  subject: string;
  content: string;
  timestamp: string;
  read: boolean;
}

export default function MessagesPage() {
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch user profile
        try {
          const profile = await authService.getProfile();
          setUserProfile(profile);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Set default user
          setUserProfile({
            id: 'doctor-1',
            first_name: 'Doctor',
            last_name: 'User',
            email: '<EMAIL>',
            user_type: 'doctor'
          });
        }

        // In a real app, you would fetch messages from an API
        // For now, we'll use mock data
        const mockMessages: Message[] = [
          {
            id: '1',
            sender: {
              id: 'patient-1',
              name: 'John Doe',
              role: 'patient',
            },
            recipient: {
              id: 'doctor-1',
              name: 'Dr. User',
              role: 'doctor',
            },
            subject: 'Question about medication',
            content: 'Hello Doctor, I have a question about the medication you prescribed. I\'ve been experiencing some side effects like dizziness and nausea. Should I continue taking it or should we consider an alternative?',
            timestamp: '2023-07-15T10:30:00Z',
            read: false,
          },
          {
            id: '2',
            sender: {
              id: 'patient-2',
              name: 'Jane Smith',
              role: 'patient',
            },
            recipient: {
              id: 'doctor-1',
              name: 'Dr. User',
              role: 'doctor',
            },
            subject: 'Appointment rescheduling',
            content: 'Dear Doctor, I need to reschedule my appointment on July 20th due to an unexpected work commitment. Would it be possible to move it to the following week? Thank you for your understanding.',
            timestamp: '2023-07-14T15:45:00Z',
            read: true,
          },
          {
            id: '3',
            sender: {
              id: 'assistant-1',
              name: 'Sarah Johnson',
              role: 'assistant',
            },
            recipient: {
              id: 'doctor-1',
              name: 'Dr. User',
              role: 'doctor',
            },
            subject: 'Lab results for patient #12345',
            content: 'Dr. User, the lab results for patient John Doe (ID: 12345) have arrived. The blood work shows elevated levels of cholesterol. I\'ve attached the full report to the patient\'s medical record for your review.',
            timestamp: '2023-07-13T09:15:00Z',
            read: true,
          },
          {
            id: '4',
            sender: {
              id: 'pharmacy-1',
              name: 'Central Pharmacy',
              role: 'pharmacy',
            },
            recipient: {
              id: 'doctor-1',
              name: 'Dr. User',
              role: 'doctor',
            },
            subject: 'Prescription clarification',
            content: 'Hello Dr. User, we received a prescription for patient Emily Davis, but the dosage information seems unclear. Could you please clarify if it should be 10mg twice daily or 20mg once daily? Thank you.',
            timestamp: '2023-07-12T14:20:00Z',
            read: false,
          },
          {
            id: '5',
            sender: {
              id: 'patient-3',
              name: 'Robert Johnson',
              role: 'patient',
            },
            recipient: {
              id: 'doctor-1',
              name: 'Dr. User',
              role: 'doctor',
            },
            subject: 'Follow-up question',
            content: 'Hi Doctor, I\'ve been following the treatment plan for my back pain, but I haven\'t seen much improvement yet. How long should I continue before we consider other options? Also, is it normal to feel a tingling sensation in my legs?',
            timestamp: '2023-07-11T11:05:00Z',
            read: true,
          },
        ];

        setMessages(mockMessages);
      } catch (error) {
        console.error('Error fetching data:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load messages. Please try again later.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  const handleSelectMessage = (message: Message) => {
    // Mark the message as read if it wasn't already
    if (!message.read) {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === message.id ? { ...msg, read: true } : msg
        )
      );
    }
    setSelectedMessage(message);
  };

  const handleSendReply = () => {
    if (!selectedMessage || !replyContent.trim()) return;

    try {
      // In a real app, you would send the reply to an API
      // For now, we'll just simulate a successful reply

      // Create a new message (the reply)
      const newMessage: Message = {
        id: `reply-${Date.now()}`,
        sender: {
          id: userProfile?.id || 'doctor-1',
          name: `Dr. ${userProfile?.first_name || 'User'} ${userProfile?.last_name || ''}`,
          role: 'doctor',
        },
        recipient: selectedMessage.sender,
        subject: `Re: ${selectedMessage.subject}`,
        content: replyContent,
        timestamp: new Date().toISOString(),
        read: true,
      };

      // Add the new message to the list
      setMessages(prevMessages => [newMessage, ...prevMessages]);

      // Clear the reply content
      setReplyContent('');

      notifications.show({
        title: 'Reply Sent',
        message: 'Your reply has been sent successfully.',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      console.error('Error sending reply:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to send reply. Please try again later.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleDeleteMessage = (messageId: string) => {
    // Remove the message from the list
    setMessages(prevMessages => prevMessages.filter(msg => msg.id !== messageId));

    // If the deleted message was selected, clear the selection
    if (selectedMessage && selectedMessage.id === messageId) {
      setSelectedMessage(null);
    }

    notifications.show({
      title: 'Message Deleted',
      message: 'The message has been deleted successfully.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  const filteredMessages = messages.filter(message => {
    const searchLower = searchQuery.toLowerCase();
    return (
      message.subject.toLowerCase().includes(searchLower) ||
      message.content.toLowerCase().includes(searchLower) ||
      message.sender.name.toLowerCase().includes(searchLower)
    );
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" gap="md">
          <Loader size="lg" />
          <Text>Loading messages...</Text>
        </Stack>
      </Container>
    );
  }

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      <Title order={1} mb="lg">Messages2</Title>

      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder p="md" radius="md">
            <Group justify="space-between" mb="md">
              <Title order={3}>Inbox</Title>
              <Badge size="lg">{messages.filter(msg => !msg.read).length} Unread</Badge>
            </Group>

            <TextInput
              placeholder="Search messages..."
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.currentTarget.value)}
              mb="md"
            />

            {filteredMessages.length === 0 ? (
              <Alert color="blue" title="No Messages">
                {searchQuery ? 'No messages match your search.' : 'Your inbox is empty.'}
              </Alert>
            ) : (
              <Stack gap="md">
                {filteredMessages.map((message) => (
                  <Paper
                    key={message.id}
                    withBorder
                    p="md"
                    radius="md"
                    onClick={() => handleSelectMessage(message)}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedMessage?.id === message.id ? '#f0f9ff' : undefined,
                      borderLeft: message.read ? undefined : '4px solid #228be6',
                    }}
                  >
                    <Group justify="space-between" mb="xs">
                      <Group>
                        <Avatar color="blue" radius="xl">
                          {message.sender.name.charAt(0)}
                        </Avatar>
                        <div>
                          <Text fw={500}>{message.sender.name}</Text>
                          <Text size="xs" c="dimmed">
                            {formatDate(message.timestamp)}
                          </Text>
                        </div>
                      </Group>
                      <ActionIcon
                        color="red"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteMessage(message.id);
                        }}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                    <Text fw={message.read ? 400 : 700}>{message.subject}</Text>
                    <Text lineClamp={2} size="sm" c="dimmed">
                      {message.content}
                    </Text>
                  </Paper>
                ))}
              </Stack>
            )}
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 8 }}>
          {selectedMessage ? (
            <Card withBorder p="md" radius="md">
              <Title order={3} mb="md">{selectedMessage.subject}</Title>

              <Group mb="md">
                <Avatar color="blue" radius="xl" size="lg">
                  {selectedMessage.sender.name.charAt(0)}
                </Avatar>
                <div>
                  <Text fw={500}>{selectedMessage.sender.name}</Text>
                  <Text size="xs" c="dimmed">
                    {formatDate(selectedMessage.timestamp)}
                  </Text>
                </div>
              </Group>

              <Text mb="xl">{selectedMessage.content}</Text>

              <Divider my="md" label="Reply" labelPosition="center" />

              <Textarea
                placeholder="Type your reply here..."
                minRows={4}
                value={replyContent}
                onChange={(e) => setReplyContent(e.currentTarget.value)}
                mb="md"
              />

              <Group justify="flex-end">
                <Button
                  leftSection={<IconSend size={16} />}
                  onClick={handleSendReply}
                  disabled={!replyContent.trim()}
                >
                  Send Reply
                </Button>
              </Group>
            </Card>
          ) : (
            <Card withBorder p="xl" radius="md">
              <Stack align="center" gap="md">
                <IconMessageCircle size={64} color="#228be6" />
                <Title order={3}>Select a message to view</Title>
                <Text c="dimmed" ta="center">
                  Click on a message from the inbox to view its contents and reply.
                </Text>
              </Stack>
            </Card>
          )}
        </Grid.Col>
      </Grid>
    </Paper>
  );
}
