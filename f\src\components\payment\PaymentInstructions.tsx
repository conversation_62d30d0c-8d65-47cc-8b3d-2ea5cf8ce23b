'use client';

import { useState, useEffect } from 'react';
import {
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Loader,
  Alert,
  CopyButton,
  ActionIcon,
  Tooltip,
  Badge,
  Divider,
  Box,
  Card
} from '@mantine/core';
import { IconAlertCircle, IconCopy, IconCheck } from '@tabler/icons-react';
import paymentConfigService, { PaymentConfiguration } from '@/services/paymentConfigService';

interface PaymentInstructionsProps {
  packageName?: string;
  packagePrice?: string;
  billingCycle?: string;
  email?: string;
}

export default function PaymentInstructions({
  packageName = 'Standard',
  packagePrice = '399',
  billingCycle = 'annual',
  email = ''
}: PaymentInstructionsProps) {
  const [config, setConfig] = useState<PaymentConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const configData = await paymentConfigService.getActiveConfig();
        setConfig(configData);
        setError(null);
      } catch (err) {
        console.error('Error fetching payment configuration:', err);
        setError('Failed to load payment information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  if (loading) {
    return (
      <Paper p="md" withBorder>
        <Group justify="center">
          <Loader size="sm" />
          <Text>Loading payment information...</Text>
        </Group>
      </Paper>
    );
  }

  if (error || !config) {
    return (
      <Paper p="md" withBorder>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error || 'Failed to load payment information'}
        </Alert>
      </Paper>
    );
  }

  // Determine the correct price based on package name and billing cycle
  let actualPrice = packagePrice;
  if (packageName && billingCycle) {
    if (packageName.toLowerCase().includes('basic')) {
      actualPrice = billingCycle === 'annual' 
        ? config.basic_price_annual.toString()
        : config.basic_price_6months.toString();
    } else if (packageName.toLowerCase().includes('standard')) {
      actualPrice = billingCycle === 'annual'
        ? config.standard_price_annual.toString()
        : config.standard_price_6months.toString();
    } else if (packageName.toLowerCase().includes('premium')) {
      actualPrice = billingCycle === 'annual'
        ? config.premium_price_annual.toString()
        : config.premium_price_6months.toString();
    }
  }

  return (
    <Paper p="md" withBorder>
      <Title order={4} mb="md">Payment Instructions</Title>
      
      <Card withBorder mb="md">
        <Group justify="apart" mb="xs">
          <Text fw={500}>Package:</Text>
          <Badge size="lg" color="blue">{packageName}</Badge>
        </Group>
        
        <Group justify="apart" mb="xs">
          <Text fw={500}>Price:</Text>
          <Text fw={700} size="lg">${actualPrice}</Text>
        </Group>
        
        <Group justify="apart">
          <Text fw={500}>Billing Cycle:</Text>
          <Text>{billingCycle === 'annual' ? 'Annual' : '6 Months'}</Text>
        </Group>
      </Card>
      
      <Divider label="Bank Transfer Details" labelPosition="center" my="md" />
      
      <Stack gap="xs">
        <Group justify="apart">
          <Text fw={500}>Bank Name:</Text>
          <Group gap="xs">
            <Text>{config.bank_name}</Text>
            <CopyButton value={config.bank_name} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>
        
        <Group justify="apart">
          <Text fw={500}>Account Number:</Text>
          <Group gap="xs">
            <Text>{config.account_number}</Text>
            <CopyButton value={config.account_number} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>
        
        <Group justify="apart">
          <Text fw={500}>Account Holder:</Text>
          <Group gap="xs">
            <Text>{config.account_holder}</Text>
            <CopyButton value={config.account_holder} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>
        
        <Group justify="apart">
          <Text fw={500}>Amount:</Text>
          <Group gap="xs">
            <Text fw={700}>${actualPrice}</Text>
            <CopyButton value={actualPrice} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>
        
        <Group justify="apart">
          <Text fw={500}>Reference:</Text>
          <Group gap="xs">
            <Text>{email || 'Your email address'}</Text>
            <CopyButton value={email || 'Your email address'} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                  <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>
      </Stack>
      
      <Alert icon={<IconAlertCircle size={16} />} color="blue" mt="md">
        <Text size="sm">
          After making the payment, please enter the transaction ID in the registration form.
          Your account will be activated once the payment is verified.
        </Text>
      </Alert>
      
      <Box mt="md">
        <Text size="sm" c="dimmed">
          For any payment issues, please contact: <Text component="span" fw={500}>{config.support_email}</Text>
        </Text>
      </Box>
    </Paper>
  );
}
