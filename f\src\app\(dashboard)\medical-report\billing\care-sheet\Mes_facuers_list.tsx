'use client';
import React, { useState } from 'react';
import {
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  TextInput,
  
  Checkbox,
  Button,
  Select,
  Pagination,
  Modal,
} from '@mantine/core';
import {
  IconSearch,
  IconPrinter,
  IconEdit,
  IconTrash,
  IconCurrencyDollar,
  
  IconEye,
  IconFileText,
} from '@tabler/icons-react';

// Import du composant MesFactures
import MesFactures from './Mes_facuers';

// Interface pour les données de facture
interface FactureData {
  id: number;
  numeroFacture: string;
  date: string;
  beneficiaire: string;
  montant: number;
  assurance: string;
  modePaiement: string;
}

const MesFacturesList = () => {
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFactures, setSelectedFactures] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Données d'exemple correspondant à l'image
  const facturesData: FactureData[] = [
    {
      id: 6,
      numeroFacture: '6',
      date: '24/08/2022',
      beneficiaire: 'EL KANBI HAMZA',
      montant: 700.00,
      assurance: 'CNSS',
      modePaiement: 'Espèce'
    },
    {
      id: 5,
      numeroFacture: '5',
      date: '13/07/2022',
      beneficiaire: 'EL KANBI HAMZA',
      montant: 17600.00,
      assurance: 'CNSS',
      modePaiement: 'Espèce'
    },
    {
      id: 4,
      numeroFacture: '4',
      date: '12/07/2022',
      beneficiaire: 'AKONGA MBARGA JOSEPH',
      montant: 0.00,
      assurance: 'AXA',
      modePaiement: 'Espèce'
    },
    {
      id: 3,
      numeroFacture: '3',
      date: '12/07/2022',
      beneficiaire: 'AKONGA MBARGA JOSEPH',
      montant: 500.00,
      assurance: 'AXA',
      modePaiement: 'Espèce'
    },
    {
      id: 2,
      numeroFacture: '2',
      date: '17/09/2021',
      beneficiaire: 'NISSR TEST',
      montant: 1700.00,
      assurance: 'BANK AL MAGHREB',
      modePaiement: 'Espèce'
    },
    {
      id: 1,
      numeroFacture: '1',
      date: '01/03/2021',
      beneficiaire: 'Mr OUARAOU ANIS',
      montant: 2000.00,
      assurance: 'CMIM',
      modePaiement: 'Espèce'
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredFactures = facturesData.filter(facture =>
    facture.beneficiaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
    facture.numeroFacture.includes(searchTerm) ||
    facture.assurance.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gérer la sélection des factures
  const handleSelectFacture = (id: number) => {
    setSelectedFactures(prev =>
      prev.includes(id)
        ? prev.filter(factureId => factureId !== id)
        : [...prev, id]
    );
  };

  // Sélectionner/désélectionner toutes les factures
  const handleSelectAll = () => {
    if (selectedFactures.length === filteredFactures.length) {
      setSelectedFactures([]);
    } else {
      setSelectedFactures(filteredFactures.map(f => f.id));
    }
  };

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec onglets */}
     
      {/* Barre de recherche et boutons d'action */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          {/* Barre de recherche */}
          <Group align="center" gap="sm">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />
          </Group>

          {/* Boutons d'action à droite */}
          <Group gap="xs" align="center">
            <Text size="sm" className="text-gray-600">
              Model1
            </Text>
            <Text size="sm" className="text-gray-600">
              Tous
            </Text>
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconFileText size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsModalOpen(true)}
            >
              Facture
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedFactures.length === filteredFactures.length && filteredFactures.length > 0}
                  indeterminate={selectedFactures.length > 0 && selectedFactures.length < filteredFactures.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N°. Facture
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Bénéficiaire
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mon...
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Assurance
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mode de paiement
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-32">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {/* Ligne de recherche */}
            <Table.Tr className="bg-gray-50">
              <Table.Td className="border-r border-gray-300">
                {/* Cellule vide pour la checkbox */}
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                <TextInput
                  placeholder="Rechercher..."
                  size="xs"
                  className="w-full"
                />
              </Table.Td>
              <Table.Td className="border-r border-gray-300">
                {/* Cellule vide pour les actions */}
              </Table.Td>
            </Table.Tr>

            {/* Données des factures */}
            {filteredFactures.map((facture) => (
              <Table.Tr key={facture.id} className="hover:bg-gray-50">
                <Table.Td className="border-r border-gray-300">
                  <Checkbox
                    checked={selectedFactures.includes(facture.id)}
                    onChange={() => handleSelectFacture(facture.id)}
                    size="sm"
                  />
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.numeroFacture}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.date}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.beneficiaire}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right">
                  <Text size="sm" className="text-gray-800">
                    {facture.montant.toFixed(2)}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.assurance}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {facture.modePaiement}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300">
                  <Group gap="xs" justify="center">
                    <Tooltip label="Modifier">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="blue"
                        className="hover:bg-blue-100"
                      >
                        <IconEdit size={14} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Imprimer">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="gray"
                        className="hover:bg-gray-100"
                      >
                        <IconPrinter size={14} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Voir">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="green"
                        className="hover:bg-green-100"
                      >
                        <IconEye size={14} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Supprimer">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="red"
                        className="hover:bg-red-100"
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Paiement">
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        color="yellow"
                        className="hover:bg-yellow-100"
                      >
                        <IconCurrencyDollar size={14} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={['1', '2', '3', '4', '5']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 5)}
              data={['5', '10', '20', '50']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              1 - 6 de 6
            </Text>
          </Group>

          <Pagination
            total={Math.ceil(filteredFactures.length / itemsPerPage)}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création de facture */}
      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nouvelle Facture"
        size="95%"
        centered
        className="modal-facture"
      >
        <MesFactures />
      </Modal>
    </Box>
  );
};

export default MesFacturesList;
