'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { Prestations_par_Medecin } from './Prestations_par_Medecin';

export default function PrestationsParMedecinDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    const activityLabel = query.activityNature === 'RENTING' ? 'Location' : 'Vente';
    alert(`Période sélectionnée: Du ${query.start.toLocaleDateString()} au ${query.end.toLocaleDateString()}\nNature: ${activityLabel}`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    const activeFilters = Object.entries(filter)
      .filter(([_, value]) => value && value !== '')
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
    
    if (activeFilters) {
      alert(`Filtres actifs: ${activeFilters}`);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des prestations par médecin en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des prestations par médecin en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'month_count': 'Nombre de mois',
      'total_amount': 'Montant Total'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function PrestationsParMedecinLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={true}
          onQueryChange={(query) => console.log('Query:', query)}
          onFilterChange={(filter) => console.log('Filter:', filter)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
          onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function PrestationsParMedecinWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    const activityLabel = query.activityNature === 'RENTING' ? 'Location' : 'Vente';
    
    // Simuler des données selon la nature d'activité
    const mockData = {
      'RENTING': {
        prestations: 28,
        montantTotal: 45780.50,
        nombreMois: 3
      },
      'SALES': {
        prestations: 15,
        montantTotal: 23450.75,
        nombreMois: 2
      }
    };
    
    const data = mockData[query.activityNature];
    alert(`Prestations ${activityLabel} du ${startDate} au ${endDate}:\n- ${data.prestations} prestations\n- ${data.montantTotal.toFixed(2)} DH au total\n- ${data.nombreMois} mois d'activité`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    const filterDescriptions: { [key: string]: string } = {
      'code': 'Code de prestation',
      'referredBy': 'Médecin référent',
      'date': 'Date de prestation',
      'description': 'Description de l\'acte'
    };
    
    const activeFilters = Object.entries(filter)
      .filter(([_, value]) => value && value !== '')
      .map(([key, value]) => `${filterDescriptions[key]}: ${value}`)
      .join('\n- ');
    
    if (activeFilters) {
      alert(`Filtres appliqués:\n- ${activeFilters}`);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des prestations par médecin avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des prestations par médecin avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'month_count': 'Tri par nombre de mois d\'activité',
      'total_amount': 'Tri par montant total des prestations'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode location
export function PrestationsParMedecinLocationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Mode location:', query);
    if (query.activityNature === 'RENTING') {
      alert('Mode Location activé:\n- Équipements médicaux\n- Matériel de diagnostic\n- Appareils thérapeutiques\n- Facturation mensuelle');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre location:', filter);
    if (filter.code) {
      alert(`Recherche par code de location: ${filter.code}\n- Équipements disponibles\n- Tarifs de location\n- Durée de contrat`);
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données de location`)}
          onPrint={() => alert('Impression des données de location')}
          onSort={(columnId, direction) => console.log('Sort location:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode vente
export function PrestationsParMedecinVenteDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Mode vente:', query);
    if (query.activityNature === 'SALES') {
      alert('Mode Vente activé:\n- Matériel médical\n- Consommables\n- Équipements neufs\n- Facturation à l\'acte');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre vente:', filter);
    if (filter.description) {
      alert(`Recherche par description: ${filter.description}\n- Produits disponibles\n- Prix de vente\n- Garanties`);
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données de vente`)}
          onPrint={() => alert('Impression des données de vente')}
          onSort={(columnId, direction) => console.log('Sort vente:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function PrestationsParMedecinErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    const diffDays = Math.abs(query.end - query.start) / (1000 * 60 * 60 * 24);
    if (diffDays > 365) {
      alert('Attention: Période trop longue (> 1 an). Les performances peuvent être affectées.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
          onExport={(format) => {
            console.log(`Export ${format} avec validation`);
            if (confirm(`Êtes-vous sûr de vouloir exporter les prestations par médecin en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onPrint={() => {
            console.log('Impression avec validation');
            if (confirm('Êtes-vous sûr de vouloir imprimer les prestations par médecin ?')) {
              alert('Impression en cours...');
            }
          }}
          onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données financières simulées
export function PrestationsParMedecinFinancialDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête financière:', query);
    
    // Simuler des données financières selon la nature d'activité
    const mockFinancialData = {
      'RENTING': {
        totalAmount: 125780.50,
        monthCount: 6,
        avgMonthly: 20963.42,
        doctors: 12
      },
      'SALES': {
        totalAmount: 89450.75,
        monthCount: 4,
        avgMonthly: 22362.69,
        doctors: 8
      }
    };
    
    const data = mockFinancialData[query.activityNature];
    const activityLabel = query.activityNature === 'RENTING' ? 'Location' : 'Vente';
    
    alert(`Données financières ${activityLabel}:\n- Montant total: ${data.totalAmount.toFixed(2)} DH\n- Nombre de mois: ${data.monthCount}\n- Moyenne mensuelle: ${data.avgMonthly.toFixed(2)} DH\n- Médecins impliqués: ${data.doctors}`);
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri financier:', columnId, direction);
    
    // Simuler le tri des données financières
    const sortData: { [key: string]: string } = {
      'month_count': 'Tri par nombre de mois d\'activité (périodes de facturation)',
      'total_amount': 'Tri par montant total des prestations (chiffre d\'affaires)'
    };
    
    const message = sortData[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Prestations_par_Medecin
          loading={false}
          onQueryChange={handleQueryChange}
          onFilterChange={(filter) => console.log('Filter financier:', filter)}
          onExport={(format) => alert(`Export ${format} des données financières des prestations`)}
          onPrint={() => alert('Impression des données financières des prestations')}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}
