# Comprehensive Patient Fields Reference - UPDATED

This document outlines all the patient-specific fields available in the User model for comprehensive patient management.

## 🆕 NEWLY ADDED FIELDS (Latest Update)

### Extended Medical Information
- `chronic_conditions` - Chronic medical conditions
- `previous_surgeries` - Previous surgeries and dates
- `family_medical_history` - Family medical history
- `smoking_status` - Smoking status (never, former, current)
- `alcohol_consumption` - Alcohol consumption frequency
- `exercise_frequency` - Exercise frequency

### Insurance and Billing
- `insurance_provider` - Primary insurance provider
- `insurance_policy_number` - Insurance policy number
- `insurance_group_number` - Insurance group number
- `secondary_insurance` - Secondary insurance provider
- `payment_method_preference` - Preferred payment method
- `outstanding_balance` - Outstanding balance
- `credit_limit` - Credit limit for patient

### Patient Preferences and Communication
- `preferred_language` - Preferred language for communication
- `communication_preference` - Preferred communication method
- `appointment_reminder_preference` - Appointment reminder preference
- `do_not_call` - Do not call this patient
- `do_not_email` - Do not email this patient
- `do_not_sms` - Do not send SMS to this patient
- `best_time_to_call` - Best time to call patient

### Patient Status and Tracking
- `patient_status` - Patient status (active, inactive, transferred)
- `referral_source` - How patient was referred
- `primary_care_physician` - Primary care physician name
- `patient_priority` - Patient priority level (low, normal, high, urgent)
- `follow_up_required` - Follow-up appointment required
- `follow_up_date` - Recommended follow-up date
- `follow_up_notes` - Follow-up instructions

### Visit Tracking
- `first_visit_date` - Date of first visit
- `last_visit_date` - Date of last visit
- `total_visits` - Total number of visits
- `missed_appointments` - Number of missed appointments

### Demographics and Social Information
- `marital_status` - Marital status
- `occupation` - Patient occupation
- `employer` - Employer name
- `education_level` - Education level

### Special Needs and Accessibility
- `mobility_assistance_needed` - Requires mobility assistance
- `interpreter_needed` - Requires interpreter services
- `special_accommodations` - Special accommodations needed

### Legal and Consent
- `consent_for_treatment` - Consent for treatment given
- `consent_for_communication` - Consent for communication given
- `hipaa_authorization` - HIPAA authorization signed

### Medical Record Management
- `medical_record_number` - Medical record number
- `chart_location` - Physical chart location
- `digital_chart_id` - Digital chart identifier

### Quality Metrics
- `patient_satisfaction_score` - Patient satisfaction score (1-10)
- `care_quality_rating` - Care quality rating

### Administrative Fields
- `registration_date` - Date patient was first registered
- `last_updated_by` - Last person to update patient record
- `data_source` - Source of patient data (manual, import, api)

## Core Patient Information (Existing)
- `first_name` - Patient's first name
- `last_name` - Patient's last name  
- `email` - Patient's email address
- `phone_number` - Mobile phone number
- `landline_number` - Landline phone number (optional)
- `address` - Patient's address
- `date_of_birth` - Date of birth
- `age` - Patient age (calculated from birth date)
- `gender` - Patient gender
- `social_security` - Social security number
- `cin` - National ID number
- `etat_civil` - Civil status

## Medical Information

### Basic Medical Data (Existing)
- `medical_history` - Patient's medical history
- `allergies` - Patient allergies
- `medications` - Current medications
- `blood_type` - Blood type (A+, B-, etc.)
- `height` - Patient height
- `weight` - Patient weight

### Extended Medical Information (NEW)
- `chronic_conditions` - Chronic medical conditions
- `previous_surgeries` - Previous surgeries and dates
- `family_medical_history` - Family medical history
- `smoking_status` - Smoking status (never, former, current)
- `alcohol_consumption` - Alcohol consumption frequency
- `exercise_frequency` - Exercise frequency

## Insurance and Billing (NEW)
- `insurance_provider` - Primary insurance provider
- `insurance_policy_number` - Insurance policy number
- `insurance_group_number` - Insurance group number
- `secondary_insurance` - Secondary insurance provider
- `payment_method_preference` - Preferred payment method
- `outstanding_balance` - Outstanding balance
- `credit_limit` - Credit limit for patient

## Patient Preferences and Communication (NEW)
- `preferred_language` - Preferred language for communication
- `communication_preference` - Preferred communication method (email, phone, sms)
- `appointment_reminder_preference` - Appointment reminder preference
- `do_not_call` - Do not call this patient
- `do_not_email` - Do not email this patient
- `do_not_sms` - Do not send SMS to this patient
- `best_time_to_call` - Best time to call patient

## Patient Status and Tracking (NEW)
- `patient_status` - Patient status (active, inactive, transferred)
- `referral_source` - How patient was referred
- `primary_care_physician` - Primary care physician name
- `patient_priority` - Patient priority level (low, normal, high, urgent)

## Visit Tracking (NEW)
- `first_visit_date` - Date of first visit
- `last_visit_date` - Date of last visit
- `total_visits` - Total number of visits
- `missed_appointments` - Number of missed appointments
- `follow_up_required` - Follow-up appointment required
- `follow_up_date` - Recommended follow-up date
- `follow_up_notes` - Follow-up instructions

## Demographics and Social Information (NEW)
- `marital_status` - Marital status
- `occupation` - Patient occupation
- `employer` - Employer name
- `education_level` - Education level
- `nationality` - Patient nationality
- `spoken_languages` - Languages spoken by patient
- `profession` - Patient profession

## Emergency Contact (Existing)
- `emergency_contact_name` - Emergency contact name
- `emergency_contact_phone` - Emergency contact phone
- `emergency_contact_relationship` - Emergency contact relationship

## Special Needs and Accessibility (NEW)
- `mobility_assistance_needed` - Requires mobility assistance
- `interpreter_needed` - Requires interpreter services
- `special_accommodations` - Special accommodations needed

## Legal and Consent (NEW)
- `consent_for_treatment` - Consent for treatment given
- `consent_for_communication` - Consent for communication given
- `hipaa_authorization` - HIPAA authorization signed

## Medical Record Management (NEW)
- `medical_record_number` - Medical record number
- `chart_location` - Physical chart location
- `digital_chart_id` - Digital chart identifier

## Quality Metrics (NEW)
- `patient_satisfaction_score` - Patient satisfaction score (1-10)
- `care_quality_rating` - Care quality rating

## Administrative Fields (NEW)
- `registration_date` - Date patient was first registered
- `last_updated_by` - Last person to update patient record
- `data_source` - Source of patient data (manual, import, api)

## Patient File Management (Existing)
- `death` - Is patient deceased
- `death_reason` - Reason for death
- `category` - Patient category
- `description` - Patient description
- `file_number` - Patient file number
- `insured` - Is patient insured
- `is_bookmarked` - Is patient bookmarked
- `is_complete` - Is patient file complete
- `pricing` - Patient pricing
- `reason` - Reason for visit
- `titre` - Patient title/designation

## Appointment Related (Existing)
- `appointment_date` - Next appointment date
- `appointment_time` - Next appointment start time
- `appointment_end_time` - Next appointment end time
- `consultation_duration` - Consultation duration in minutes
- `etat_agenda` - Agenda status
- `event_title` - Event title
- `event_type` - Event type
- `resource_id` - Resource ID
- `type_consultation` - Consultation type
- `commentaire_liste_attente` - Waiting list comment
- `notes` - Patient notes
- `visitor_count` - Visitor count

## Usage Notes

### For Frontend Integration
- All new fields are optional (blank=True, null=True)
- Boolean fields have appropriate defaults
- Numeric fields have sensible defaults where applicable

### For API Integration
- Fields can be included in patient registration/update endpoints
- Consider creating field groups for different forms (medical, insurance, preferences)
- Implement field validation based on business rules

### For Database Performance
- Consider indexing frequently queried fields
- Use appropriate field types for optimal storage
- Implement data archival strategies for inactive patients

### Migration Instructions
1. Run: `python manage.py makemigrations users`
2. Run: `python manage.py migrate`
3. Update admin interface to include new fields
4. Update API serializers to include relevant fields
5. Update frontend forms to capture new data
