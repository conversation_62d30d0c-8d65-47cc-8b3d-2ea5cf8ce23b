"use client";
import React from 'react';
import {useState, useCallback } from "react";
import SelectMois from "./SelectMois";

import { Group, Button,Text ,ThemeIcon, Avatar,Divider,Tooltip, } from '@mantine/core';
import Icon from '@mdi/react';


import { mdiChevronLeft, mdiChevronRight, mdiFileTree,mdiAccountNetwork,mdiBookmarkOutline,mdiCalendarRange,mdiCalendarPlus,mdiMagnify,mdiCalendarMonth} from '@mdi/js';

import {  IconTextPlus,IconCalendarClock,IconChevronDown, IconRefresh } from '@tabler/icons-react';
import "./MonthView.css";
// Types
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
}

// Utility functions
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const getMonthDays = (date: Date): Date[] => {
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDay = new Date(year, month, 1);
  // const lastDay = new Date(year, month + 1, 0);
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - firstDay.getDay());
  
  const days = [];
  const totalDays = 42; // 6 weeks * 7 days
  for (let i = 0; i < totalDays; i++) {
    const day = new Date(startDate);
    day.setDate(startDate.getDate() + i);
    days.push(day);
  }
  return days;
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const getEventsForDay = (events: CalendarEvent[], day: Date): CalendarEvent[] => {
  return events.filter(event => isSameDay(event.start, day));
};

// Event Component
const EventComponent = ({ 
  event, 
  onDragStart,
  onEdit 
}: { 
  event: CalendarEvent;
  onDragStart?: (event: CalendarEvent) => void;
  onEdit?: (event: CalendarEvent) => void;
}) => (
  <div
    draggable={!!onDragStart}
    onDragStart={() => onDragStart?.(event)}
    onClick={() => onEdit?.(event)}
    className="p-1 mb-1 text-xs rounded cursor-pointer hover:opacity-80 transition-opacity"
    style={{ backgroundColor: event.color || '#3b82f6', color: 'white' }}
  >
    <div className="font-medium truncate">{event.title}</div>
    <div className="text-xs opacity-90">
      {formatTime(event.start)} - {formatTime(event.end)}
    </div>
  </div>
);

// Month View Component
const MonthView = ({ 
  currentDate, 
  events, 
  onDateClick,
  onEventDrag ,
   onDateChange,
  onNavigate
}: {
  currentDate: Date;
  events: CalendarEvent[];
  onDateClick: (date: Date) => void;
  onEventDrag: (event: CalendarEvent) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
}) => {
  const days = getMonthDays(currentDate);
  const today = new Date();
  const currentMonth = currentDate.getMonth();
  
  const handleDrop = (e: React.DragEvent, date: Date) => {
    e.preventDefault();
    const eventData = e.dataTransfer.getData('event');
    if (eventData) {
      const event = JSON.parse(eventData);
      onEventDrag({ ...event, start: date, end: new Date(date.getTime() + (event.end - event.start)) });
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragStart = (event: CalendarEvent) => {
    // Store event data for drag and drop
    if (window.event && 'dataTransfer' in window.event) {
      (window.event as DragEvent).dataTransfer?.setData('event', JSON.stringify(event));
    }
  };
  // Handle date change for SelectJournee
    const handleDateChange = useCallback((newDate: Date) => {
      onDateChange?.(newDate);
    }, [onDateChange]);
  
    // Handle navigation
    const handleNavigate = useCallback((direction: 'prev' | 'next' | 'today') => {
      onNavigate?.(direction);
    }, [onNavigate]);
  
    // Format date title in French format: "12 août 2025"
    const getCurrentDateTitle = () => {
      return currentDate.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })
    };
  
    // Label for SelectJournee
    const label = getCurrentDateTitle();
    const messages = {
      today: "Aujourd’hui",
      previous: "Précédent",
      next: "Suivant",
      month: "Mois",
      week: "Semaine",
      day: "Jour",
      agenda: "Agenda",
      noEventsInRange: "Aucun événement prévu",
    };
     const [selectedDateM, setSelectedDateM] = useState(new Date());
  return (
    <>
         <div className="h-[40px] bg-[#E6E9EC] flex my-2 p-1">
            <div className="w-[50%]">
               <Group>
                 <div className="flex justify-start">
                                               {/* liste d'attente */}
                            <Icon path={mdiFileTree} size={1}    className="bg-[#E6E9EC] text-[#3799CE] cursor-pointer"/> 
                                                  <Divider orientation="vertical" color="#3799CE" h={20} mt={4}/>
                            <Avatar size={26}  radius={30} variant="filled" color="cyan" className="border-2 rounded-full border-[#fff] mr-[2px] ">
                              <Text fz="sm" fw={500} c={"white"}>
                               0
                              </Text>
                              </Avatar>
                            <ThemeIcon className="cursor-pointer">
                            <IconTextPlus stroke={2} size={30} className="bg-[#E6E9EC] text-[#3799CE] "
                                              />
                                              </ThemeIcon>
                                              </div>
                                            <Group gap={0}>  
                   <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize text-[#797C7F]">
                             <IconCalendarClock stroke={1.5} className="mr-1 h-3.5 w-3.5 " />
                             {getCurrentDateTitle()}  
                           </h3>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                        </Group>
                  <Button
                        size="xs"
                        className="HoverButton"
                        rightSection={<IconChevronDown stroke={2} />}
                        // onClick={open}
                      >
                        La durée du déjeuner
                      </Button>
                </Group>
            </div>
            <div className="w-[50%]">
               <Group justify="flex-end">
                     <Button
                      size="xs"
                      className="HoverButton"
                      rightSection={<IconChevronDown stroke={2} />}
                      // onClick={openedStartofwork}
                    >
                      Début des travaux
                    </Button>
                    <Group>
                      <Tooltip
                          label="Changer ressource"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                          
                            {/* <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                            <IconFileTypography stroke={1.75} className="h-4 w-4  hover:text-[#3799CE]" />
                            </h2> */}
                            <Icon path={mdiAccountNetwork} size={1} color={"#797C7F"}/>
                        </Tooltip>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                           <Tooltip
                          label="Filtrer par motifs"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                            <Icon path={mdiBookmarkOutline} size={1} color={"#797C7F"}/>
                        </Tooltip>
                        <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                          <Tooltip
                          label="Nouvel événement"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
                          
                       
                           <Icon path={mdiCalendarRange} size={1} color={"#797C7F"} />
                    
                        </Tooltip>
                         <Tooltip
                          label="List des événements"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiCalendarMonth} size={1} color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                         <Tooltip
                          label="Planifier des rendez-vous"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiCalendarPlus} size={1}  color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                           <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                         <Tooltip
                          label="Chercher des rendez-vous"
                          position="bottom"
                          withArrow
                          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                           style={{color:"var(--mantine-color-text)"}}
                        >
            <Icon path={mdiMagnify} size={1}  color={"#797C7F"}/>
                       
                    
                        </Tooltip>
                     
                      </Group>
                    {/* Refresh button for appointments */}
                    <Button
                      variant="light"
                      size="xs"
                      // onClick={fetchTodayAppointments}
                      // loading={loadingAppointments}
                      leftSection={<IconRefresh size={14} />}
                      color="#15aabf"
                    >
                      Rafraîchir
                    </Button>
                </Group>
            </div>
            </div>
     {/* Navigation Header */}
           <Group justify="space-between" p={4} className="border-2 border-[#3799CE] rounded-t-md bg-[#3799CE]">
          <Button variant="filled" size="sm" onClick={() => handleNavigate("today")} className=" rounded-md  text-[var(--text-tab)] ButtonHover" fz="md">
                     {messages.today}
                  </Button>
         <div className="flex items-center">
                   <button className="btn-sm  mr-1" onClick={() => handleNavigate('prev')}> <Icon path={mdiChevronLeft} size={1} color={"white"}/></button>
                   <Text fw={550} c={"var(--text-daisy-white)"}>
              
          {getCurrentDateTitle()}
        
                   </Text>
                   <button className="btn-sm  ml-1" onClick={() => handleNavigate('next')}><Icon path={mdiChevronRight} size={1} color={"white"}/></button>
                 </div>
         <SelectMois
              date={selectedDateM}
              setter={handleDateChange}
              label={label} // Pass the current label
            />
    </Group>
    <div className="grid grid-cols-7 gap-1 h-full">
      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
        <div key={day} className="p-2 text-center font-semibold bg-gray-100 border">
          {day}
        </div>
      ))}
      {days.map((day, index) => (
        <div
          key={index}
          className={`min-h-24 p-2 border cursor-pointer hover:bg-gray-50 ${
            day.getMonth() !== currentMonth ? 'bg-gray-50 text-gray-400' : ''
          } ${
            isSameDay(day, today) ? 'bg-blue-50 border-blue-300' : ''
          }`}
          onClick={() => onDateClick(day)}
          onDrop={(e) => handleDrop(e, day)}
          onDragOver={handleDragOver}
        >
          <div className="font-medium">{day.getDate()}</div>
          <div className="mt-1">
            {getEventsForDay(events, day).map(event => (
              <EventComponent 
                key={event.id} 
                event={event} 
                onDragStart={handleDragStart}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
    </>
  );
};

export default MonthView;
