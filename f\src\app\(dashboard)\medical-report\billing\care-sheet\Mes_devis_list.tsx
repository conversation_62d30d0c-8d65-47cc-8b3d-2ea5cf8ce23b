'use client';
import React, { useState } from 'react';
import {
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  TextInput,
  Checkbox,
  Button,
  Select,
  Pagination,
  Modal,
} from '@mantine/core';
import {
  IconSearch,
  IconPrinter,
  IconEdit,
  IconTrash,
  IconEye,
  IconPlus,
} from '@tabler/icons-react';

// Import du composant Mes_devis
import Mes_devis from './Mes_devis';

// Interface pour les données de devis
interface DevisData {
  id: number;
  numeroDevis: string;
  date: string;
  beneficiaire: string;
  validite: string;
  montant: number;
}

const Mes_devis_list = () => {
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDevis, setSelectedDevis] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Données d'exemple pour les devis
  const devisData: DevisData[] = [
    {
      id: 1,
      numeroDevis: '1',
      date: '18/09/2022',
      beneficiaire: 'Rechercher',
      validite: 'Rechercher',
      montant: 0.00
    },
    {
      id: 2,
      numeroDevis: '2',
      date: '19/09/2022',
      beneficiaire: 'Patient Test',
      validite: '30 jours',
      montant: 1500.00
    },
    {
      id: 3,
      numeroDevis: '3',
      date: '20/09/2022',
      beneficiaire: 'Organisme Test',
      validite: '15 jours',
      montant: 2500.00
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredDevis = devisData.filter(devis =>
    devis.beneficiaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
    devis.numeroDevis.includes(searchTerm) ||
    devis.validite.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gestion de la sélection
  const handleSelectDevis = (id: number) => {
    setSelectedDevis(prev =>
      prev.includes(id)
        ? prev.filter(devisId => devisId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedDevis.length === filteredDevis.length) {
      setSelectedDevis([]);
    } else {
      setSelectedDevis(filteredDevis.map(devis => devis.id));
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredDevis.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentDevis = filteredDevis.slice(startIndex, endIndex);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Barre de recherche et boutons d'action */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          {/* Barre de recherche */}
          <Group align="center" gap="sm">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />
          </Group>

          {/* Boutons d'action à droite */}
          <Group gap="xs" align="center">
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsModalOpen(true)}
            >
              Devis
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedDevis.length === filteredDevis.length && filteredDevis.length > 0}
                  indeterminate={selectedDevis.length > 0 && selectedDevis.length < filteredDevis.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N°. Devis
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Bénéficiaire
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Validité
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Montant
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-32">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentDevis.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={7} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentDevis.map((devis) => (
                <Table.Tr key={devis.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      checked={selectedDevis.includes(devis.id)}
                      onChange={() => handleSelectDevis(devis.id)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {devis.numeroDevis}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {devis.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {devis.beneficiaire}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {devis.validite}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {devis.montant.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs" justify="center">
                      <Tooltip label="Voir">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="sm"
                        >
                          <IconEye size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="orange"
                          size="sm"
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="sm"
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Imprimer">
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                        >
                          <IconPrinter size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 15)}
              data={['15', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {startIndex + 1} - {Math.min(endIndex, filteredDevis.length)} de {filteredDevis.length}
            </Text>
          </Group>

          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création de devis */}
      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nouveau Devis"
        size="95%"
        centered
        className="modal-devis"
      >
        <Mes_devis />
      </Modal>
    </Box>
  );
};

export default Mes_devis_list;

