import React, { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Button,
  Table,
  Group,
  ActionIcon,
  Paper,
  Tooltip,
  Modal,
  Text,
  TextInput,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconUsers,
} from '@tabler/icons-react';

// Types
interface Technicien {
  id: string;
  nom: string;
  prenom: string;
  specialite?: string;
  telephone?: string;
  email?: string;
}

// Mock data for techniciens (empty initially to show "Aucun élément trouvé")
const mockTechniciens: Technicien[] = [];

const List_des_techniciens = () => {
  const [techniciens, setTechniciens] = useState<Technicien[]>([]);
  const [modalOpened, setModalOpened] = useState(false);
  const [newTechnicien, setNewTechnicien] = useState({
    nom: '',
    prenom: '',
    specialite: '',
    telephone: '',
    email: '',
  });

  useEffect(() => {
    setTechniciens(mockTechniciens);
  }, []);

  // Reset form
  const resetForm = () => {
    setNewTechnicien({
      nom: '',
      prenom: '',
      specialite: '',
      telephone: '',
      email: '',
    });
  };

  return (
    <Stack gap={0} className="w-full">
      {/* Header with blue background */}
      <Paper
        className="bg-blue-500 text-white"
        p="md"
        style={{
          borderRadius: '8px 8px 0 0',
          backgroundColor: '#3b82f6'
        }}
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUsers size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Liste des techniciens
            </Title>
          </Group>
          <Button
            variant="subtle"
            color="white"
            size="sm"
            onClick={() => {
              resetForm();
              setModalOpened(true);
            }}
            styles={{
              root: {
                color: 'white',
                backgroundColor: 'transparent',
                border: '1px solid white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              },
            }}
          >
            <IconPlus size={16} />
          </Button>
        </Group>
      </Paper>

      {/* Table */}
      <Paper
        withBorder
        style={{
          borderRadius: '0 0 8px 8px',
          borderTop: 'none'
        }}
      >
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr className="bg-gray-50">
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200 py-3 px-4">
                Nom
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200 py-3 px-4">
                Prénom
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center py-3 px-4">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {techniciens.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={3} className="text-center py-8 text-gray-500">
                  Aucun élément trouvé
                </Table.Td>
              </Table.Tr>
            ) : (
              techniciens.map((technicien) => (
                <Table.Tr key={technicien.id} className="hover:bg-gray-50">
                  <Table.Td className="font-medium border-r border-gray-200 py-3 px-4">
                    {technicien.nom}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 py-3 px-4">
                    {technicien.prenom}
                  </Table.Td>
                  <Table.Td className="text-center py-3 px-4">
                    <Group gap="xs" justify="center">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="sm"
                          onClick={() => {
                            // Handle edit action
                            console.log('Edit technicien:', technicien.id);
                          }}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="sm"
                          onClick={() => {
                            // Handle delete action
                            console.log('Delete technicien:', technicien.id);
                          }}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Modal for adding new technicien */}
      <Modal
        opened={modalOpened}
        onClose={() => {
          setModalOpened(false);
          resetForm();
        }}
        title="Nouveau technicien"
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Nom"
            placeholder="Entrez le nom"
            value={newTechnicien.nom}
            onChange={(event) =>
              setNewTechnicien({ ...newTechnicien, nom: event.currentTarget.value })
            }
            required
          />

          <TextInput
            label="Prénom"
            placeholder="Entrez le prénom"
            value={newTechnicien.prenom}
            onChange={(event) =>
              setNewTechnicien({ ...newTechnicien, prenom: event.currentTarget.value })
            }
            required
          />

          <TextInput
            label="Spécialité"
            placeholder="Spécialité technique"
            value={newTechnicien.specialite}
            onChange={(event) =>
              setNewTechnicien({ ...newTechnicien, specialite: event.currentTarget.value })
            }
          />

          <TextInput
            label="Téléphone"
            placeholder="Numéro de téléphone"
            value={newTechnicien.telephone}
            onChange={(event) =>
              setNewTechnicien({ ...newTechnicien, telephone: event.currentTarget.value })
            }
          />

          <TextInput
            label="Email"
            placeholder="Adresse email"
            type="email"
            value={newTechnicien.email}
            onChange={(event) =>
              setNewTechnicien({ ...newTechnicien, email: event.currentTarget.value })
            }
          />

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                setModalOpened(false);
                resetForm();
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                // Add new technicien to the list
                const newId = (techniciens.length + 1).toString();
                const technicien: Technicien = {
                  id: newId,
                  nom: newTechnicien.nom,
                  prenom: newTechnicien.prenom,
                  specialite: newTechnicien.specialite,
                  telephone: newTechnicien.telephone,
                  email: newTechnicien.email,
                };
                setTechniciens([...techniciens, technicien]);

                // Reset form and close modal
                resetForm();
                setModalOpened(false);
              }}
              disabled={!newTechnicien.nom || !newTechnicien.prenom}
            >
              Ajouter
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default List_des_techniciens;
