'use client';
import React, { useState, useEffect } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  Textarea,
  Table,
  Modal,
  Tabs,
  Radio,
  Pagination,
  Loader,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  supplierService,
  depotService,
  productService,
  purchaseRequestService,
  type Supplier,
  type Depot,
  type Product,
  type PurchaseRequest,
  type PurchaseRequestItem
} from '~/services/pharmacyService';
import {
  IconSearch,
  IconList,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  IconX,
  IconFileInvoice,
} from '@tabler/icons-react';

// Interface pour les articles de la demande d'achat (compatible avec le backend)
interface ArticleDemande {
  id?: string;
  product: string;
  product_code?: string;
  product_designation?: string;
  depot: string;
  depot_name?: string;
  quantity: number;
  unit_price?: number;
  total_price?: number;
}

// Interface pour la demande d'achat (compatible avec le backend)
interface DemandeAchat {
  numero: string;
  date: Date | null;
  date_echeance: Date | null;
  urgent: boolean;
  supplier: string;
  commentaire: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed';
  items: ArticleDemande[];
}

const Demandes_dachats = () => {
  // États pour la gestion du formulaire et de l'interface
  const [activeTab, setActiveTab] = useState<string | null>('details');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [articles, setArticles] = useState<ArticleDemande[]>([]);
  const [loading, setLoading] = useState(false);

  // Données de référence depuis l'API
  const [suppliers, setSuppliers] = useState<{ value: string; label: string }[]>([]);
  const [depots, setDepots] = useState<{ value: string; label: string }[]>([]);
  const [products, setProducts] = useState<{ value: string; label: string; code: string; designation: string }[]>([]);

  // Charger les données de référence
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        setLoading(true);

        // Charger les fournisseurs
        const suppliersData = await supplierService.getSimpleList();
        setSuppliers(suppliersData.map(s => ({ value: s.id, label: s.raison_sociale })));

        // Charger les dépôts
        const depotsData = await depotService.getSimpleList();
        setDepots(depotsData.map(d => ({ value: d.id, label: d.name })));

        // Charger les produits
        const productsData = await productService.getSimpleList();
        setProducts(productsData.map(p => ({
          value: p.id,
          label: `${p.code} - ${p.designation}`,
          code: p.code,
          designation: p.designation
        })));

      } catch (error) {
        console.error('Erreur lors du chargement des données de référence:', error);
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de charger les données de référence',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    loadReferenceData();
  }, []);

  // Formulaire principal
  const form = useForm<DemandeAchat>({
    initialValues: {
      numero: '1',
      date: new Date(),
      date_echeance: null,
      urgent: false,
      supplier: '',
      commentaire: '',
      status: 'draft' as const,
      items: [],
    },
  });

  // Formulaire pour ajouter un article
  const itemForm = useForm({
    initialValues: {
      product: '',
      quantity: 1,
      depot: '',
      unit_price: 0,
    },
    validate: {
      product: (value) => (!value ? 'Le produit est requis' : null),
      depot: (value) => (!value ? 'Le dépôt est requis' : null),
      quantity: (value) => (value < 1 ? 'La quantité doit être supérieure à 0' : null),
    },
  });

  // Fonction pour ajouter un article
  const addItem = (values: { product: string; quantity: number; depot: string; unit_price: number }) => {
    const selectedProduct = products.find(p => p.value === values.product);
    const selectedDepot = depots.find(d => d.value === values.depot);

    if (!selectedProduct || !selectedDepot) {
      notifications.show({
        title: 'Erreur',
        message: 'Produit ou dépôt non trouvé',
        color: 'red',
      });
      return;
    }

    const newArticle: ArticleDemande = {
      id: Date.now().toString(),
      product: values.product,
      product_code: selectedProduct.code,
      product_designation: selectedProduct.designation,
      depot: values.depot,
      depot_name: selectedDepot.label,
      quantity: values.quantity,
      unit_price: values.unit_price,
      total_price: values.quantity * values.unit_price,
    };

    setArticles(prev => [...prev, newArticle]);
    itemForm.reset();
    close();

    notifications.show({
      title: 'Article ajouté',
      message: 'L\'article a été ajouté à la demande d\'achat.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  // Fonction pour supprimer un article
  const removeItem = (id: string) => {
    setArticles(prev => prev.filter(item => item.id !== id));
    notifications.show({
      title: 'Article supprimé',
      message: 'L\'article a été supprimé de la demande d\'achat.',
      color: 'red',
    });
  };

  // Fonctions de gestion des boutons
  const handleSave = async () => {
    try {
      setLoading(true);

      // Préparer les données pour l'API
      const purchaseRequestData = {
        numero: form.values.numero,
        date: form.values.date?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        date_echeance: form.values.date_echeance?.toISOString().split('T')[0],
        urgent: form.values.urgent,
        supplier: form.values.supplier,
        commentaire: form.values.commentaire,
        status: 'draft' as const,
        items: articles.map(article => ({
          product: article.product,
          depot: article.depot,
          quantity: article.quantity,
          unit_price: article.unit_price,
        })),
      };

      await purchaseRequestService.create(purchaseRequestData);

      notifications.show({
        title: 'Demande sauvegardée',
        message: 'La demande d\'achat a été sauvegardée.',
        color: 'green',
        icon: <IconDeviceFloppy size={16} />,
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de sauvegarder la demande d\'achat.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleValidate = async () => {
    try {
      setLoading(true);

      // D'abord sauvegarder, puis valider
      await handleSave();

      notifications.show({
        title: 'Demande validée',
        message: 'La demande d\'achat a été validée.',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de valider la demande d\'achat.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAndExit = async () => {
    await handleSave();
    // Ici on pourrait rediriger vers la liste des demandes
  };

  const handleCancel = () => {
    form.reset();
    setArticles([]);
    notifications.show({
      title: 'Demande annulée',
      message: 'Les modifications ont été annulées.',
      color: 'red',
      icon: <IconX size={16} />,
    });
  };

  if (loading) {
    return (
      <Paper p="xl" radius="md" withBorder w="100%">
        <Group justify="center" p="xl">
          <Loader size="lg" />
          <Text>Chargement des données...</Text>
        </Group>
      </Paper>
    );
  }

  return (
    <Paper p="xl" radius="md" withBorder w="100%">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group align="center">
          <IconFileInvoice size={24} color="blue" />
          <Title order={3} c="blue">
            Demande d'achat N°: {form.values.numero}
          </Title>
        </Group>
        <Group>
          <Button leftSection={<IconList size={16} />} variant="outline">
            Liste
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Demande"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
                disabled
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="16/09/2022"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date d'échéance"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date_echeance')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Text size="sm" fw={500} mb="xs">Urgent</Text>
              <Radio.Group
                value={form.values.urgent ? 'oui' : 'non'}
                onChange={(value) => form.setFieldValue('urgent', value === 'oui')}
              >
                <Group>
                  <Radio value="oui" label="Oui" />
                  <Radio value="non" label="Non" />
                </Group>
              </Radio.Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={12}>
              <Select
                label="Fournisseur"
                placeholder="Choisir un fournisseur"
                data={suppliers}
                searchable
                rightSection={<IconSearch size={16} />}
                {...form.getInputProps('supplier')}
                required
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              <Group justify="space-between" mb="md">
                <Group>
                  <Button leftSection={<IconBarcode size={16} />} color="blue">
                    Code à barres
                  </Button>
                  <Button
                    leftSection={<IconShoppingCart size={16} />}
                    color="blue"
                    onClick={open}
                  >
                    Ajouter Article
                  </Button>
                  <Button leftSection={<IconMessageCircle size={16} />} color="blue">
                    Commentaire
                  </Button>
                </Group>
              </Group>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix unitaire</Table.Th>
                    <Table.Th>Prix total</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {articles.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={7} style={{ textAlign: 'center', padding: '2rem' }}>
                        <Text c="dimmed">Aucun élément trouvé</Text>
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    articles.map((article) => (
                      <Table.Tr key={article.id}>
                        <Table.Td>{article.product_code}</Table.Td>
                        <Table.Td>{article.product_designation}</Table.Td>
                        <Table.Td>{article.quantity}</Table.Td>
                        <Table.Td>{article.unit_price?.toFixed(2)} €</Table.Td>
                        <Table.Td>{article.total_price?.toFixed(2)} €</Table.Td>
                        <Table.Td>{article.depot_name}</Table.Td>
                        <Table.Td>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeItem(article.id!)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>

              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm">Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['1', '2', '3']}
                    value={currentPage.toString()}
                    onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  />
                  <Text size="sm">Lignes par Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['10', '25', '50']}
                    value={itemsPerPage.toString()}
                    onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
                  />
                  <Text size="sm">0 - 0 de 0</Text>
                </Group>
                <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
              </Group>
            </Tabs.Panel>

            <Tabs.Panel value="pieces" pt="md">
              <Text c="dimmed" mb="md">Aucune pièce jointe</Text>
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Comment Section */}
          <Grid>
            <Grid.Col span={12}>
              <Textarea
                label="Commentaire"
                placeholder="Commentaire général..."
                rows={3}
                {...form.getInputProps('commentaire')}
              />
            </Grid.Col>
          </Grid>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red" onClick={handleCancel}>
              Annuler
            </Button>
            <Button color="gray" onClick={handleValidate}>
              Valider
            </Button>
            <Button color="blue" onClick={handleSaveAndExit}>
              Enregistrer et quitter
            </Button>
            <Button onClick={handleSave}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="lg">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <Select
              label="Produit"
              placeholder="Sélectionner un produit"
              data={products}
              searchable
              {...itemForm.getInputProps('product')}
              required
            />
            <NumberInput
              label="Quantité"
              placeholder="1"
              {...itemForm.getInputProps('quantity')}
              min={1}
              required
            />
            <NumberInput
              label="Prix unitaire"
              placeholder="0.00"
              {...itemForm.getInputProps('unit_price')}
              min={0}
              decimalScale={2}
              fixedDecimalScale
              suffix=" €"
            />
            <Select
              label="Dépôt"
              placeholder="Sélectionner un dépôt"
              data={depots}
              {...itemForm.getInputProps('depot')}
              required
            />
            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
};

export default Demandes_dachats;
