{"version": "0.2.0", "configurations": [{"name": "Django Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "127.0.0.1:8000"], "django": true, "justMyCode": true, "python": "${workspaceFolder}/venv/Scripts/python.exe", "env": {"DJANGO_SETTINGS_MODULE": "config.settings.development"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}"}, {"name": "Django Shell", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["shell"], "django": true, "justMyCode": true, "python": "${workspaceFolder}/venv/Scripts/python.exe", "env": {"DJANGO_SETTINGS_MODULE": "config.settings.development"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}