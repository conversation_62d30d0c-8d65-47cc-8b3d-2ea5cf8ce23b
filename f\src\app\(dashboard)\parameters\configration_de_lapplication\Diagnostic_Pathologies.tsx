'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Switch,
  Table,
  ActionIcon,
  Modal,
  Text,
  Tabs,
  Card,
  Tooltip,
  Container,
  Stack,
  ScrollArea,
  Pagination,
  Textarea,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconTrash,
  IconMedicalCross,
  IconSettings,
  IconList,
  IconMenu2,
} from '@tabler/icons-react';

// Types pour les données
interface Pathology {
  id: number;
  title: string;
  description?: string;
}

interface FieldSettings {
  pathologies: boolean;
  cim10: boolean;
  osiics: boolean;
}

interface MedicalFileSettings {
  showPathologyField: boolean;
}

const Diagnostic_Pathologies = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string | null>('parameters');

  // États pour les modals
  const [pathologyModalOpened, { open: openPathologyModal, close: closePathologyModal }] = useDisclosure(false);

  // États pour l'édition
  const [editingPathology, setEditingPathology] = useState<Pathology | null>(null);

  // États pour la recherche et pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;

  // États pour les paramètres
  const [fieldSettings, setFieldSettings] = useState<FieldSettings>({
    pathologies: true,
    cim10: true,
    osiics: true,
  });

  const [medicalFileSettings, setMedicalFileSettings] = useState<MedicalFileSettings>({
    showPathologyField: true,
  });

  // Données mockées pour les pathologies
  const [pathologies, setPathologies] = useState<Pathology[]>([
    { id: 1, title: 'Aboulie', description: '' },
    { id: 2, title: 'Absence de menstruation (Amenorrhee)', description: '' },
    { id: 3, title: 'Acanthosis nigricans', description: '' },
    { id: 4, title: 'Accident vasculaire cérébral', description: '' },
    { id: 5, title: 'Achondroplasie', description: '' },
    { id: 6, title: 'Acné', description: '' },
    { id: 7, title: 'Acné rosacée', description: '' },
    { id: 8, title: 'Acouphènes', description: '' },
    { id: 9, title: 'Acrocyanose', description: '' },
    { id: 10, title: 'Acromégalie', description: '' },
    { id: 11, title: 'Acrophobie', description: '' },
    { id: 12, title: 'Adénolymphite mésentérique', description: '' },
    { id: 13, title: 'Adénomyose', description: '' },
    { id: 14, title: 'Adénopathie', description: '' },
    { id: 15, title: 'Adipomastie', description: '' },
  ]);

  // Filtrage et pagination
  const filteredPathologies = pathologies.filter((pathology) =>
    pathology.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredPathologies.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedPathologies = filteredPathologies.slice(startIndex, startIndex + itemsPerPage);

  return (
    <Container size="xl" className="py-6">
      <Paper shadow="sm" radius="md" p="xl" className="bg-white">
        {/* En-tête */}
        <Group justify="space-between" mb="xl">
          <Group>
            <IconMedicalCross size={24} className="text-blue-600" />
            <Title order={2} className="text-gray-800">Diagnostic et Pathologies</Title>
          </Group>
        </Group>

        {/* Onglets principaux */}
        <Tabs value={activeTab} onChange={setActiveTab} className="w-full">
          <Tabs.List className="mb-6">
            <Tabs.Tab value="parameters" leftSection={<IconSettings size={16} />}>
              Paramètres généraux
            </Tabs.Tab>
            <Tabs.Tab value="pathologies" leftSection={<IconList size={16} />}>
              Pathologies
            </Tabs.Tab>
          </Tabs.List>

          {/* Onglet Paramètres généraux */}
          <Tabs.Panel value="parameters">
            <div className="space-y-6">
              {/* Section Order des champs */}
              <Card shadow="sm" padding="lg" radius="md" className="bg-white">
                <Group justify="space-between" align="center" mb="md">
                  <Text fw={500} size="lg">Order des champs</Text>
                </Group>

                <div className="space-y-4">
                  <Group justify="space-between" align="center" className="p-3 border border-gray-200 rounded-md">
                    <Group>
                      <IconMenu2 size={16} className="text-gray-400" />
                      <Text>Pathologies</Text>
                    </Group>
                    <Group>
                      <Switch
                        checked={fieldSettings.pathologies}
                        onChange={(event) => setFieldSettings(prev => ({ ...prev, pathologies: event.currentTarget.checked }))}
                        color="blue"
                      />
                      <ActionIcon variant="subtle" color="blue" size="sm">
                        <IconMenu2 size={14} />
                      </ActionIcon>
                      <Button variant="subtle" color="blue" size="xs" leftSection={<IconPlus size={12} />}>

                      </Button>
                    </Group>
                  </Group>

                  <Group justify="space-between" align="center" className="p-3 border border-gray-200 rounded-md">
                    <Group>
                      <IconMenu2 size={16} className="text-gray-400" />
                      <Text>Classification CIM10</Text>
                    </Group>
                    <Group>
                      <Switch
                        checked={fieldSettings.cim10}
                        onChange={(event) => setFieldSettings(prev => ({ ...prev, cim10: event.currentTarget.checked }))}
                        color="blue"
                      />
                      <ActionIcon variant="subtle" color="blue" size="sm">
                        <IconMenu2 size={14} />
                      </ActionIcon>
                      <Button variant="subtle" color="blue" size="xs" leftSection={<IconPlus size={12} />}>

                      </Button>
                    </Group>
                  </Group>

                  <Group justify="space-between" align="center" className="p-3 border border-gray-200 rounded-md">
                    <Group>
                      <IconMenu2 size={16} className="text-gray-400" />
                      <Text>Classification OSIICS(Orchard)</Text>
                    </Group>
                    <Group>
                      <Switch
                        checked={fieldSettings.osiics}
                        onChange={(event) => setFieldSettings(prev => ({ ...prev, osiics: event.currentTarget.checked }))}
                        color="blue"
                      />
                      <ActionIcon variant="subtle" color="blue" size="sm">
                        <IconMenu2 size={14} />
                      </ActionIcon>
                      <Button variant="subtle" color="blue" size="xs" leftSection={<IconPlus size={12} />}>

                      </Button>
                    </Group>
                  </Group>
                </div>
              </Card>

              {/* Section Fiche médicale */}
              <Card shadow="sm" padding="lg" radius="md" className="bg-white">
                <Group justify="space-between" align="center" mb="md">
                  <Text fw={500} size="lg">Fiche médicale</Text>
                </Group>

                <Group justify="space-between" align="center" className="p-3">
                  <Group>
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <Text>Afficher le champs des pathologie</Text>
                  </Group>
                  <Group>
                    <Text size="sm" c="dimmed">Order des champs</Text>
                    <Switch
                      checked={medicalFileSettings.showPathologyField}
                      onChange={(event) => setMedicalFileSettings(prev => ({ ...prev, showPathologyField: event.currentTarget.checked }))}
                      color="blue"
                    />
                    <ActionIcon variant="subtle" color="blue" size="sm">
                      <IconMenu2 size={14} />
                    </ActionIcon>
                    <Button variant="subtle" color="blue" size="xs" leftSection={<IconPlus size={12} />}>

                    </Button>
                  </Group>
                </Group>
              </Card>
            </div>
          </Tabs.Panel>

          {/* Onglet Pathologies */}
          <Tabs.Panel value="pathologies">
            <Card shadow="sm" padding="lg" radius="md" className="bg-white">
              {/* Barre d'outils */}
              <Group justify="space-between" mb="md">
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={openPathologyModal}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Ajouter une pathologie
                </Button>
                <TextInput
                  placeholder="Rechercher"
                  leftSection={<IconSearch size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.currentTarget.value)}
                  className="w-64"
                />
              </Group>

              {/* Tableau des pathologies */}
              <ScrollArea>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Titre</Table.Th>
                      <Table.Th style={{ width: 100 }}>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {paginatedPathologies.map((pathology) => (
                      <Table.Tr key={pathology.id}>
                        <Table.Td>{pathology.title}</Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <Tooltip label="Modifier">
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                size="sm"
                                onClick={() => {
                                  setEditingPathology(pathology);
                                  openPathologyModal();
                                }}
                              >
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Supprimer">
                              <ActionIcon
                                variant="subtle"
                                color="red"
                                size="sm"
                                onClick={() => {
                                  setPathologies(pathologies.filter(p => p.id !== pathology.id));
                                  notifications.show({
                                    title: 'Pathologie supprimée',
                                    message: 'La pathologie a été supprimée avec succès',
                                    color: 'green',
                                  });
                                }}
                              >
                                <IconTrash size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm" c="dimmed">
                    Page {currentPage} de {totalPages}
                  </Text>
                  <Text size="sm" c="dimmed">
                    Lignes par Page: {itemsPerPage}
                  </Text>
                  <Text size="sm" c="dimmed">
                    {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredPathologies.length)} de {filteredPathologies.length}
                  </Text>
                </Group>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Modal pour les pathologies */}
        <Modal
          opened={pathologyModalOpened}
          onClose={() => {
            closePathologyModal();
            setEditingPathology(null);
          }}
          title={
            <Group>
              <IconMedicalCross size={20} />
              <Text fw={600} c="white">
                Ajouter une pathologie
              </Text>
            </Group>
          }
          size="md"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <PathologyForm
            pathology={editingPathology}
            onSubmit={(data) => {
              if (editingPathology) {
                setPathologies(pathologies.map(p => p.id === editingPathology.id ? { ...editingPathology, ...data } : p));
                notifications.show({
                  title: 'Pathologie modifiée',
                  message: 'La pathologie a été modifiée avec succès',
                  color: 'green',
                });
              } else {
                const newPathology: Pathology = {
                  id: Math.max(...pathologies.map(p => p.id)) + 1,
                  title: data.title || '',
                  description: data.description || '',
                };
                setPathologies([...pathologies, newPathology]);
                notifications.show({
                  title: 'Pathologie créée',
                  message: 'La pathologie a été créée avec succès',
                  color: 'green',
                });
              }
              closePathologyModal();
              setEditingPathology(null);
            }}
            onCancel={() => {
              closePathologyModal();
              setEditingPathology(null);
            }}
          />
        </Modal>
      </Paper>
    </Container>
  );
};

// Composant de formulaire pour les pathologies
interface PathologyFormProps {
  pathology: Pathology | null;
  onSubmit: (data: Partial<Pathology>) => void;
  onCancel: () => void;
}

const PathologyForm: React.FC<PathologyFormProps> = ({ pathology, onSubmit, onCancel }) => {
  const [title, setTitle] = useState(pathology?.title || '');
  const [description, setDescription] = useState(pathology?.description || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    onSubmit({
      title: title.trim(),
      description: description.trim(),
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        <div>
          <Text size="sm" fw={500} mb="xs">
            <span style={{ color: 'red' }}>Valeur *</span>
          </Text>
          <TextInput
            placeholder={pathology ? pathology.title : ""}
            value={title}
            onChange={(e) => setTitle(e.currentTarget.value)}
            required
            styles={{
              input: {
                borderBottom: '2px solid red',
                borderTop: 'none',
                borderLeft: 'none',
                borderRight: 'none',
                borderRadius: 0,
                backgroundColor: 'transparent',
              },
            }}
          />
        </div>

        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Description
          </Text>
          <Textarea
            placeholder=""
            value={description}
            onChange={(e) => setDescription(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        <Group justify="flex-end" mt="md">
          <Button
            variant="filled"
            color="gray"
            onClick={() => {
              if (pathology) {
                onSubmit({ title, description });
              } else {
                onSubmit({ title, description });
              }
            }}
          >
            {pathology ? 'Enregistrer' : 'Enregistrer'}
          </Button>
          <Button
            variant="filled"
            color="red"
            onClick={onCancel}
          >
            Annuler
          </Button>
        </Group>
      </Stack>
    </form>
  );
};

export default Diagnostic_Pathologies;
