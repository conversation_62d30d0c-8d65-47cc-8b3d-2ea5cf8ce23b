import React, { useState } from 'react';
import { ScrollArea } from '@mantine/core';
import {
  Box,
  Checkbox,
  Divider,
  ActionIcon,
  Text,
  Group,
  List,
  ListItem
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiMagnifyPlus,
  mdiFilterRemoveOutline,
  mdiFilterOutline,
  mdiFindReplace
} from '@mdi/js';

const filterItems = [
  'Date de création',
  'Nom',
  'Prénom',
  'Date de naissance',
  'Age',
  'CNIE',
  'Dernière visite',
  'Téléphone',
  'Ville',
  'Assurance',
  'N° de dossier papier',
  'Etat du Compte général'
];

export default function FilterList() {
  const [checkedFilters, setCheckedFilters] = useState<Record<string, boolean>>({});

  const handleFilterChange = (label: string) => {
    setCheckedFilters((prev) => ({ ...prev, [label]: !prev[label] }));
  };

  return (
    <Box p={10}>
      <Text size="sm" fw={500} mb="sm">
       <Group  justify="flex-end" my="md" >
        <ActionIcon variant="light" disabled>
          <Icon path={mdiFindReplace} size={1} />
        </ActionIcon>
        <ActionIcon variant="light" disabled>
          <Icon path={mdiMagnifyPlus} size={1} />
        </ActionIcon>
        <ActionIcon variant="light" color="red" disabled>
          <Icon path={mdiFilterRemoveOutline} size={1} />
        </ActionIcon>
        <ActionIcon variant="light" disabled>
          <Icon path={mdiFilterOutline} size={1} />
        </ActionIcon>
      </Group>
      </Text>
       <ScrollArea h={550}>
      <List spacing="xs">
        {filterItems.map((label, index) => (
          <Box key={label}>
            <ListItem>
              <Checkbox
                label={label}
                checked={!!checkedFilters[label]}
                onChange={() => handleFilterChange(label)}
              />
            </ListItem>
            {index !== filterItems.length - 1 && <Divider my="xs" />}
          </Box>
        ))}
      </List>
      </ScrollArea>
    </Box>
  );
}
