"use client";
import React from 'react'
import {  useState } from 'react';
import {  Group,Card,Radio, TextInput,Text ,Select, } from '@mantine/core';

export const LeftPart = () => {
     const [beneficiaire, setBeneficiaire] = useState('Patient');
      const [tiersPayant, setTiersPayant] = useState('');
      const [choisirPatient, setChoisirPatient] = useState('ABADI SOUAD');
      const [payeur, setPayeur] = useState('Patient');
     const [tiersPayantPayeur, setTiersPayantPayeur] = useState('');
     const [autre, setAutre] = useState('');
  return (
     <Card
                      shadow="none"
                      padding="sm"
                      radius={0}
                      className=" bg-white border-r border-gray-200"
                    >
                      <div className="space-y-3">
                        {/* Bénéficiaire */}
                        <div>
                          <Text size="xs" fw={500} className="text-gray-700 mb-2">
                            Bénéficiaire
                          </Text>
                          <Radio.Group
                            value={beneficiaire}
                            onChange={setBeneficiaire}
                            size="xs"
                          >
                             <div className="flex items-center gap-2 ">
                              <Radio value="Patient" label="Patient" />
                                <Radio value="Tiers payant" label="Tiers payant" disabled/>
                                <TextInput
                                  value={tiersPayant}
                                  onChange={(e) => setTiersPayant(e.target.value)}
                                  size="xs"
                                  className="flex-1"
                                  disabled={beneficiaire !== 'Tiers payant'}
                                  w={"120px"}
                                />
                                <div className="flex-col -mt-4">
                                 <Text size="xs" fw={500} className="text-gray-700 mb-1">
                            {beneficiaire === 'Tiers payant' ? 'Choisir un tiers payant' : 'Choisir un patient'}
                          </Text>
                            <Select
                            value={choisirPatient}
                            onChange={(value) => setChoisirPatient(value || '')}
                            size="xs"
                            className="w-[250px]"
                            placeholder={beneficiaire === 'Tiers payant' ? 'Nom du tiers payant' : 'ABADI SOUAD'}
                            data={beneficiaire === 'Tiers payant' ? [
                              'ATLANTA',
                              'ATLANTA/SANAD',
                              'AXA',
                              'AXA ASSURANCE MAROC',
                              'Allianz',
                              'Aucune',
                              'Autre',
                              'BANK AL MAGHREB',
                              'BP',
                              'CMIM',
                              'CNOPS',
                              'CNSS',
                              'CNCF',
                              'CNIA SAADA',
                              'Es Saada',
                              'FAR',
                              'LYDEC',
                              'MAMDA',
                              'MAROCAINE VIE',
                              'MAMT',
                              'MCMA',
                              'MNE',
                              'MUFRAS',
                              'MUTUELLE',
                              'NM',
                              'OCP',
                              'ONE',
                              'RAM',
                              'RMA WATANYA',
                              'SAHAM ASSURANCE',
                              'SANAD',
                              'WAFA ASSURANCE',
                              'ZURICH'
                            ] : [
                              'ABADI SOUAD',
                              'BENALI MOHAMED',
                              'CHAKIR FATIMA',
                              'DOUIRI HASSAN',
                              'EL AMRANI AICHA',
                              'FASSI YOUSSEF',
                              'GHARBI NADIA',
                              'HAMIDI OMAR',
                              'IDRISSI KHADIJA',
                              'JAMAL RACHID'
                            ]}
                            searchable
                            clearable
                          />
                          </div>
                              </div>
                           
                          </Radio.Group>
                          
                        </div>
          
                        {/* Payeur */}
                        <div>
                          <Text size="xs" fw={500} className="text-gray-700 mb-2">
                            Payeur
                          </Text>
                          <Radio.Group
                            value={payeur}
                            onChange={setPayeur}
                            size="xs"
                          >
                            <div className="space-y-1 ">
                              <Group>
                              <Radio value="Patient" label="Patient" />
                                <Radio value="Tiers payant" label="Tiers payant" disabled/>
                                <Radio value="Autre" label="Autre" />
                                <TextInput
                                  value={tiersPayantPayeur}
                                  onChange={(e) => setTiersPayantPayeur(e.target.value)}
                                  size="xs"
                                  className="flex-1"
                                  disabled={payeur !== 'Autre'}
                                  placeholder="Pièce d'identité"
                                />
                                <TextInput
                                  value={autre}
                                  placeholder="Nom complet"
                                  onChange={(e) => setAutre(e.target.value)}
                                  size="xs"
                                  className="flex-1"
                                  disabled={payeur !== 'Autre'}
                                />
                              </Group>
                            </div>
                          </Radio.Group>
                        </div>
                      </div>
                    </Card>
  )
}
