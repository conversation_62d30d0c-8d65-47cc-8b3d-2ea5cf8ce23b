'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  ActionIcon,
  Modal,
  Text,
  Container,
  Stack,
  ScrollArea,
  Textarea,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconEdit,
  IconCertificate,
} from '@tabler/icons-react';

// Types pour les données
interface CertificateFamily {
  id: number;
  name: string;
  description: string;
  is_system?: boolean;
}

const FamillesDesCertificats = () => {
  // États pour les modals
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);

  // États pour l'édition
  const [editingFamily, setEditingFamily] = useState<CertificateFamily | null>(null);

  // Données mockées pour les familles de certificats
  const [families, setFamilies] = useState<CertificateFamily[]>([
    {
      id: 1,
      name: 'Aucune Famille',
      description: '',
      is_system: true
    },
    {
      id: 2,
      name: 'Certificats',
      description: 'Certificats, et modèles qui n\'appartient a aucune famille',
      is_system: false
    },
  ]);

  const handleNew = () => {
    setEditingFamily(null);
    openModal();
  };

  const handleEdit = (family: CertificateFamily) => {
    setEditingFamily(family);
    openModal();
  };

  const handleSubmit = (data: { name: string; description: string }) => {
    if (editingFamily) {
      // Modifier une famille existante
      setFamilies(families.map(f =>
        f.id === editingFamily.id
          ? { ...editingFamily, name: data.name, description: data.description }
          : f
      ));
      notifications.show({
        title: 'Famille modifiée',
        message: 'La famille de certificats a été modifiée avec succès',
        color: 'green',
      });
    } else {
      // Créer une nouvelle famille
      const newFamily: CertificateFamily = {
        id: Math.max(...families.map(f => f.id)) + 1,
        name: data.name,
        description: data.description,
        is_system: false,
      };
      setFamilies([...families, newFamily]);
      notifications.show({
        title: 'Famille créée',
        message: 'La famille de certificats a été créée avec succès',
        color: 'green',
      });
    }
    closeModal();
    setEditingFamily(null);
  };

  const handleCancel = () => {
    closeModal();
    setEditingFamily(null);
  };

  return (
    <Container size="xl" className="py-6">
      <Paper shadow="sm" radius="md" p="xl" className="bg-white">
        {/* En-tête */}
        <Group justify="space-between" mb="xl">
          <Group>
            <IconCertificate size={24} className="text-blue-600" />
            <Title order={2} className="text-gray-800">Familles des certificats</Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleNew}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Nouveau
          </Button>
        </Group>

        {/* Table des familles */}
        <div className="table-container">
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Titre de la famille</Table.Th>
                  <Table.Th width={100}></Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {families.map((family) => (
                  <Table.Tr key={family.id}>
                    <Table.Td>
                      <Text>{family.name}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs" justify="center">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handleEdit(family)}
                          aria-label="edit category"
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        </div>

        {/* Modal pour ajouter/modifier une famille */}
        <Modal
          opened={modalOpened}
          onClose={handleCancel}
          title={
            <Group>
              <IconCertificate size={20} />
              <Text fw={600} c="white">
                Familles des certificats
              </Text>
            </Group>
          }
          size="md"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <FamilyForm
            family={editingFamily}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </Modal>
      </Paper>
    </Container>
  );
};

// Composant de formulaire pour les familles
interface FamilyFormProps {
  family: CertificateFamily | null;
  onSubmit: (data: { name: string; description: string }) => void;
  onCancel: () => void;
}

const FamilyForm: React.FC<FamilyFormProps> = ({ family, onSubmit, onCancel }) => {
  const [name, setName] = useState(family?.name || '');
  const [description, setDescription] = useState(family?.description || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    onSubmit({
      name: name.trim(),
      description: description.trim(),
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Titre de la famille <span style={{ color: 'red' }}>*</span>
          </Text>
          <TextInput
            placeholder={family ? family.name : ""}
            value={name}
            onChange={(e) => setName(e.currentTarget.value)}
            required
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Description
          </Text>
          <Textarea
            placeholder={family ? family.description : ""}
            value={description}
            onChange={(e) => setDescription(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        <Group justify="flex-end" mt="md">
          <Button
            variant="filled"
            color="blue"
            type="submit"
          >
            Enregistrer
          </Button>
          <Button
            variant="filled"
            color="red"
            onClick={onCancel}
          >
            Annuler
          </Button>
        </Group>
      </Stack>
    </form>
  );
};

export default FamillesDesCertificats;
