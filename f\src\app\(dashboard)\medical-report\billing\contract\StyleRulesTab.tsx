
import {
  Box,
  List,
  Text,
  ThemeIcon,
  Center,
} from '@mantine/core';
import Icon from '@mdi/react';
import { mdiAlertCircleOutline,  mdiPlusCircleOutline} from '@mdi/js';
import { ScrollArea } from '@mantine/core';
import { ActionIcon } from '@mantine/core';
export interface Column {
  id: string;
  label: string;
  field: string;
  isFilter?: boolean;
  // ajoute d'autres propriétés selon ton modèle AngularJS
}

export interface Model {
  // Définis la structure de ton modèle
  [key: string]: unknown;
}

export interface StyleRule {
  uid: string;
  // Définis d'autres propriétés des règles ici
}

export interface DraftRule {
  // Structure du draft de règle
  [key: string]: unknown;
}
export interface StyleRulesTabProps {
  styleRules: StyleRule[];
  isCreate: boolean;
  onStartCreate: () => void;
  columns: Column[];       // ✅ manquait ici
  model: Model;
  draftRule: DraftRule;
}
export default function StyleRulesTab(props: StyleRulesTabProps) {
  const {
    styleRules,
    isCreate,
    onStartCreate,
   
  } = props;

  const hasNoRules = styleRules.length === 0;

  return (
    <Box p={10}>
        <ScrollArea h={550}>
      {!isCreate && (
        <Box>
          {hasNoRules && (
            <Center className="gap-2" mt="md">
              <ThemeIcon color="yellow" size="lg" variant="light">
                <Icon  path={mdiAlertCircleOutline} size={1} />
              </ThemeIcon>
              <Text>Aucun élément trouvé.</Text>
            </Center>
          )}

          {!hasNoRules && (
            <List spacing="xs">
              {styleRules.map((item) => (
                <List.Item key={item.uid}>{/* Render rule info here */}</List.Item>
              ))}
            </List>
          )}
             <ActionIcon variant="filled" aria-label="Settings" onClick={(e) => {
              e.stopPropagation();
              onStartCreate();
            }} mt="md"
            >
      <Icon path={mdiPlusCircleOutline} size={1} />
    </ActionIcon>
        </Box>
      )}
      </ScrollArea>
    </Box>
  );
}

