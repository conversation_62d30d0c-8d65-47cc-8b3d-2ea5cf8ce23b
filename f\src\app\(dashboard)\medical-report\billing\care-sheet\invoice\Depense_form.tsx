'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  Tabs,
  Textarea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconFileText,
  IconSearch,
  IconPlus,
  IconMinus,
  IconList,
} from '@tabler/icons-react';

const Depense_form = () => {
  // États pour les données de la dépense
  const [date, setDate] = useState<Date | null>(new Date('2022-09-18'));
  const [marchand, setMarchand] = useState('');
  const [modePrix, setModePrix] = useState('HT');
  const [patient, setPatient] = useState('MEDECIN YOUSSEF');
  const [commentaire, setCommentaire] = useState('');
  const [activeTab, setActiveTab] = useState('details');

  // Données d'exemple pour les éléments de dépense
  const [elementsDepense, setElementsDepense] = useState([
    { id: 1, categorie: 'Aucun élément trouvé', description: '', tva: '', montant: 0, mode: '' }
  ]);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Calcul du total
  const total = elementsDepense.reduce((sum, element) => sum + element.montant, 0);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconFileText size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Mes dépenses
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Liste des dépenses">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconList size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Sidebar gauche avec les informations de la dépense */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* Date */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Date
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  value={date}
                  onChange={setDate}
                  size="xs"
                  className="w-full"
                  placeholder="Sélectionner une date"
                />
              </div>
            </div>

            {/* Marchand */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Marchand
                </Text>
              </div>
              <div className="p-2">
                <TextInput
                  value={marchand}
                  onChange={(e) => setMarchand(e.target.value)}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>

            {/* Mode de prix */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Mode de prix
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={modePrix}
                  onChange={setModePrix}
                  size="xs"
                >
                  <Group gap="md">
                    <Radio value="HT" label="HT" />
                    <Radio value="TTC" label="TTC" />
                  </Group>
                </Radio.Group>
              </div>
            </div>

            {/* Patient */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Patient
                </Text>
              </div>
              <div className="p-2">
                <Group gap="xs">
                  <TextInput
                    value={patient}
                    onChange={(e) => setPatient(e.target.value)}
                    size="xs"
                    className="flex-1"
                    placeholder="Docteur"
                  />
                  <ActionIcon
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    <IconSearch size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du contenu */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Section onglets */}
          <div className="flex-1 flex flex-col">
            <Tabs
              value={activeTab}
              onChange={(value) => setActiveTab(value || 'details')}
              className="flex-1 flex flex-col"
            >
              <Tabs.List className="bg-gray-100 border-b border-gray-200">
                <Tabs.Tab
                  value="details"
                  className="text-sm font-medium"
                >
                  Détails
                </Tabs.Tab>
                <Tabs.Tab
                  value="pieces-jointes"
                  className="text-sm font-medium"
                >
                  Pièces jointes
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="details" className="flex-1 flex flex-col">
                <div className="flex-1 overflow-auto">
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                    className="h-full"
                  >
                    <Table.Thead className="bg-gray-50 sticky top-0">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Catégorie
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Description
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-20">
                          Tva
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Montant
                        </Table.Th>
                        <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Mode
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {elementsDepense.map((element, index) => (
                        <Table.Tr key={index} className="hover:bg-gray-50">
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {element.categorie}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {element.description}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-center">
                            {element.tva}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-right">
                            {element.montant.toFixed(2)}
                          </Table.Td>
                          <Table.Td className="text-sm text-center">
                            {element.mode}
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Section pagination et total */}
                <div className="border-t border-gray-300 bg-gray-50 p-3">
                  <Group justify="space-between" align="center">
                    <Group gap="sm" align="center">
                      <Text size="sm" className="text-gray-600">Page</Text>
                      <Select
                        value={currentPage.toString()}
                        onChange={(value) => setCurrentPage(Number(value) || 1)}
                        data={['1', '2', '3', '4', '5']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                      <Select
                        value={itemsPerPage.toString()}
                        onChange={(value) => setItemsPerPage(Number(value) || 5)}
                        data={['5', '10', '20', '50']}
                        size="xs"
                        className="w-16"
                      />
                      <Text size="sm" className="text-gray-600">0 - de</Text>
                      <Text size="sm" className="text-gray-600">K</Text>
                      <Group gap="xs">
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          className="text-gray-500"
                        >
                          <IconMinus size={14} />
                        </ActionIcon>
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          className="text-gray-500"
                        >
                          <IconPlus size={14} />
                        </ActionIcon>
                      </Group>
                    </Group>

                    <Text size="sm" fw={600} className="text-gray-800">
                      Total : {total.toFixed(2)}
                    </Text>
                  </Group>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="pieces-jointes" className="flex-1 flex flex-col">
                <div className="flex-1 flex items-center justify-center">
                  <Text size="sm" className="text-gray-500">
                    Contenu des pièces jointes
                  </Text>
                </div>
              </Tabs.Panel>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Section commentaire */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <div className="border border-gray-300 rounded">
          <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
            <Text size="sm" fw={500} className="text-gray-700">
              Commentaire
            </Text>
          </div>
          <div className="p-2">
            <Textarea
              value={commentaire}
              onChange={(e) => setCommentaire(e.target.value)}
              size="xs"
              className="w-full"
              rows={3}
            />
          </div>
        </div>
      </Card>

      {/* Boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-gray-50 border-t border-gray-200"
      >
        <Group justify="flex-end" gap="sm">
          <Button
            variant="outline"
            color="red"
            size="sm"
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="blue"
            size="sm"
          >
            Enregistrer et quitter
          </Button>
          <Button
            color="blue"
            size="sm"
          >
            Enregistrer
          </Button>
        </Group>
      </Card>
    </Box>
  );
};

export default Depense_form;
