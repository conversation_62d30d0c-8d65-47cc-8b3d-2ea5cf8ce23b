'use client';
import React, { useState } from 'react';
import {
  Group,
  Table,
  Text,
  Card,
  Box,
  TextInput,
  
  Checkbox,
  Button,
  Select,
  Pagination,
  Modal,
} from '@mantine/core';
import {
  IconSearch,
  
  IconPlus,
} from '@tabler/icons-react';

// Import du composant Depense_form
import Depense_form from './Depense_form';

// Interface pour les données de dépense
interface DepenseData {
  id: number;
  date: string;
  marchand: string;
  nom: string;
  prenom: string;
  numeroRef: string;
  montant: number;
}

const Mes_depenses = () => {
  // États pour les filtres et données
  const [searchTerm, setSearchTerm] = useState('');

  const [selectedDepenses, setSelectedDepenses] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Données d'exemple pour les dépenses
  const depensesData: DepenseData[] = [
    {
      id: 1,
      date: 'Rechercher',
      marchand: 'Rechercher',
      nom: 'Rechercher',
      prenom: 'Rechercher',
      numeroRef: 'Rechercher',
      montant: 0.00
    }
  ];

  // Filtrer les données selon le terme de recherche
  const filteredDepenses = depensesData.filter(depense =>
    depense.marchand.toLowerCase().includes(searchTerm.toLowerCase()) ||
    depense.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    depense.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    depense.numeroRef.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Gestion de la sélection
  const handleSelectDepense = (id: number) => {
    setSelectedDepenses(prev =>
      prev.includes(id)
        ? prev.filter(depenseId => depenseId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedDepenses.length === filteredDepenses.length) {
      setSelectedDepenses([]);
    } else {
      setSelectedDepenses(filteredDepenses.map(depense => depense.id));
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredDepenses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentDepenses = filteredDepenses.slice(startIndex, endIndex);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec onglets */}
   

      {/* Barre de recherche et boutons d'action */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          {/* Barre de recherche */}
          <Group align="center" gap="sm">
            <TextInput
              placeholder="Rechercher"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              size="sm"
              className="w-64"
            />
          </Group>

          {/* Boutons d'action à droite */}
          <Group gap="xs" align="center">
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsModalOpen(true)}
            >
              Dépense
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal avec tableau */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-12">
                <Checkbox
                  checked={selectedDepenses.length === filteredDepenses.length && filteredDepenses.length > 0}
                  indeterminate={selectedDepenses.length > 0 && selectedDepenses.length < filteredDepenses.length}
                  onChange={handleSelectAll}
                  size="sm"
                />
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Date
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Marchand
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Prénom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N° ref
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm">
                Mo...
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentDepenses.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={7} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentDepenses.map((depense) => (
                <Table.Tr key={depense.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Checkbox
                      checked={selectedDepenses.includes(depense.id)}
                      onChange={() => handleSelectDepense(depense.id)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {depense.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {depense.marchand}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {depense.nom}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {depense.prenom}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {depense.numeroRef}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-right">
                    <Text size="sm" className="text-gray-800">
                      {depense.montant.toFixed(2)}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">
              Page
            </Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              Lignes par Page
            </Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 15)}
              data={['15', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {filteredDepenses.length > 0
                ? `${startIndex + 1} - ${Math.min(endIndex, filteredDepenses.length)} de ${filteredDepenses.length}`
                : '0 - 0 de 0'
              }
            </Text>
          </Group>

          <Pagination
            total={totalPages || 1}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Card>

      {/* Modale pour la création de dépense */}
      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nouvelle Dépense"
        size="95%"
        centered
        className="modal-depense"
      >
        <Depense_form />
      </Modal>
    </Box>
  );
};

export default Mes_depenses;
