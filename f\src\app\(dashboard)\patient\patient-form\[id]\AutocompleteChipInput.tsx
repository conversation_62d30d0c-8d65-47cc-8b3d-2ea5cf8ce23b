'use client';

import React, { useState, useEffect } from 'react';
import {
  Autocomplete,
  Chip,
  Group,
  Text,
  ActionIcon,
  Stack,
  Badge,
  Loader,
  Alert,
} from '@mantine/core';
import { IconTrash,  IconAlertCircle, IconCheck } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import patientService, { MedicalDataItem, PatientMedicalData } from '@/services/patientService';

interface AutocompleteChipInputProps {
  label: string;
  patientId: string; // Required for Django integration
  categoryType: 'allergy' | 'medication' | 'condition' | 'treatment' | 'pathology' | 'symptom' | 'procedure' | 'other';
  value: string[]; // Array of medical item names for display
  onChange: (value: string[]) => void;
  readOnly?: boolean;
  required?: boolean;
  placeholder?: string;
  autoSaveToDjango?: boolean; // Whether to automatically save to Django
  onDataChange?: (data: PatientMedicalData[]) => void; // Callback when Django data changes
}

export function AutocompleteChipInput({
  label,
  patientId,
  categoryType,
  value,
  onChange,
  readOnly = false,
  required = false,
  placeholder = 'Type to search medical items...',
  autoSaveToDjango = true,
  onDataChange,
}: AutocompleteChipInputProps) {
  // State management
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  // Medical data from Django
  const [availableItems, setAvailableItems] = useState<MedicalDataItem[]>([]);
  const [patientMedicalData, setPatientMedicalData] = useState<PatientMedicalData[]>([]);
  const [saving, setSaving] = useState(false);

  // Check Django connection and load data on mount
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check Django connection
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');

        if (status.status === 'active') {
          // Load available medical items for this category
          await loadAvailableItems();

          // Load patient's existing medical data
          await loadPatientMedicalData();
        } else {
          setError('Django backend is not connected');
        }
      } catch (err) {
        console.error('Error initializing AutocompleteChipInput:', err);
        setError('Failed to connect to Django backend');
        setDjangoStatus('disconnected');
      } finally {
        setLoading(false);
      }
    };

    initializeComponent();
  }, [patientId, categoryType]);

  // Load available medical items for autocomplete
  const loadAvailableItems = async (searchTerm?: string) => {
    try {
      const items = await patientService.getMedicalDataItems({
        categoryType,
        search: searchTerm,
        commonOnly: !searchTerm // Show common items when no search term
      });
      setAvailableItems(items);
    } catch (error) {
      console.error('Error loading medical items:', error);
    }
  };

  // Load patient's existing medical data
  const loadPatientMedicalData = async () => {
    try {
      const data = await patientService.getPatientMedicalData(patientId, categoryType);
      setPatientMedicalData(data);

      // Update display value with current patient data
      const currentItems = data.filter(d => d.is_active).map(d => d.medical_item.name);
      onChange(currentItems);

      // Notify parent component
      if (onDataChange) {
        onDataChange(data);
      }
    } catch (error) {
      console.error('Error loading patient medical data:', error);
    }
  };

  // Handle adding new item
  const handleAdd = async (itemName: string) => {
    if (!itemName || value.includes(itemName)) return;

    // Find existing medical item or create new one
    const existingItem = availableItems.find(item =>
      item.name.toLowerCase() === itemName.toLowerCase()
    );

    if (autoSaveToDjango && djangoStatus === 'connected') {
      setSaving(true);
      setError(null);

      try {
        const result = await patientService.addPatientMedicalData(patientId, {
          medical_item_id: existingItem?.id,
          medical_item_name: existingItem ? undefined : itemName,
          category_type: categoryType,
          is_active: true
        });

        if (result) {
          // Reload patient data to get updated list
          await loadPatientMedicalData();

          notifications.show({
            title: 'Item Added',
            message: `${itemName} has been added to ${label.toLowerCase()}`,
            color: 'green',
            icon: <IconCheck size={16} />
          });
        } else {
          throw new Error('Failed to save to Django backend');
        }
      } catch (error) {
        console.error('Error adding medical data:', error);
        setError('Error saving to patient records. Please try again.');
        notifications.show({
          title: 'Save Failed',
          message: `Failed to add ${itemName} to patient records`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        });
      } finally {
        setSaving(false);
      }
    } else {
      // Just update local state if not saving to Django
      onChange([...value, itemName]);
    }

    setInputValue('');
  };

  // Handle removing item
  const handleRemove = async (itemName: string) => {
    if (autoSaveToDjango && djangoStatus === 'connected') {
      setSaving(true);
      setError(null);

      try {
        // Find the patient medical data record to remove
        const dataToRemove = patientMedicalData.find(
          data => data.medical_item.name === itemName && data.is_active
        );

        if (dataToRemove) {
          const success = await patientService.removePatientMedicalData(patientId, dataToRemove.id);

          if (success) {
            // Reload patient data to get updated list
            await loadPatientMedicalData();

            notifications.show({
              title: 'Item Removed',
              message: `${itemName} has been removed from ${label.toLowerCase()}`,
              color: 'green',
              icon: <IconCheck size={16} />
            });
          } else {
            throw new Error('Failed to remove from Django backend');
          }
        }
      } catch (error) {
        console.error('Error removing medical data:', error);
        setError('Error removing from patient records. Please try again.');
        notifications.show({
          title: 'Remove Failed',
          message: `Failed to remove ${itemName} from patient records`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        });
      } finally {
        setSaving(false);
      }
    } else {
      // Just update local state if not saving to Django
      onChange(value.filter((v) => v !== itemName));
    }
  };

  // Handle search input change
  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);

    // Load items based on search term
    if (newValue.length > 2) {
      loadAvailableItems(newValue);
    } else {
      loadAvailableItems(); // Load common items
    }
  };

  // Prepare autocomplete data
  const autocompleteData = availableItems.map(item => item.name);

  return (
    <Stack gap="xs">
      {/* Header with Django Status */}
      <Group justify="space-between">
        <Text size="sm" fw={500}>
          {label} {required && <Text component="span" c="red">*</Text>}
        </Text>

        <Badge
          color={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'yellow'}
          variant="light"
          size="xs"
        >
          Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
        </Badge>
      </Group>

      {/* Error Alert */}
      {error && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Error"
          color="red"
         // size="sm"
          onClose={() => setError(null)}
          withCloseButton
        >
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <Group justify="center" p="sm">
          <Loader size="sm" />
          <Text size="sm">Loading medical data...</Text>
        </Group>
      )}

      {/* Autocomplete Input */}
      {!readOnly && !loading && (
        <Autocomplete
          placeholder={placeholder}
          data={autocompleteData}
          value={inputValue}
          onChange={handleInputChange}
          onOptionSubmit={handleAdd}
          disabled={djangoStatus !== 'connected' || saving}
          rightSection={saving ? <Loader size="xs" /> : undefined}
        />
      )}

      {/* Selected Items */}
      <Group gap="xs">
        {value.map((item) => (
          <Chip
            key={item}
            checked={false}
            onChange={() => {}}
            variant="filled"
          >
            <Group gap={4} wrap="nowrap">
              <Text size="sm">{item}</Text>
              {!readOnly && (
                <ActionIcon
                  size="xs"
                  variant="transparent"
                  onClick={() => handleRemove(item)}
                  disabled={saving}
                >
                  <IconTrash size={12} />
                </ActionIcon>
              )}
            </Group>
          </Chip>
        ))}
      </Group>

      {/* Empty State */}
      {!loading && value.length === 0 && (
        <Text size="sm" c="dimmed" ta="center" py="sm">
          No {label.toLowerCase()} added yet
        </Text>
      )}
    </Stack>
  );
}
