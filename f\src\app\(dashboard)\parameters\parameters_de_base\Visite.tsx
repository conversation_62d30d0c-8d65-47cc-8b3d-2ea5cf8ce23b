import React, { useState } from 'react';
import {
  Table,
  Switch,
  ActionIcon,
  Group,
  Text,
  Menu,
} from '@mantine/core';
import {
  IconStar,
  IconStarFilled,
  IconDots,
  IconEdit,
  IconTrash,
  IconSettings
} from '@tabler/icons-react';

interface VisiteItem {
  id: string;
  name: string;
  isFavorite: boolean;
  isDefault: boolean;
  isDisabled: boolean;
}

const Visite = () => {
  // State for visit items
  const [visiteItems, setVisiteItems] = useState<VisiteItem[]>([
    { id: '1', name: 'Consultation', isFavorite: false, isDefault: true, isDisabled: false },
    { id: '2', name: '<PERSON><PERSON>', isFavorite: true, isDefault: false, isDisabled: false },
    { id: '3', name: '<PERSON><PERSON><PERSON><PERSON>', isFavorite: false, isDefault: false, isDisabled: false },
    { id: '4', name: 'Prescription', isFavorite: false, isDefault: false, isDisabled: false },
    { id: '5', name: 'Compt<PERSON> rendus', isFavorite: false, isDefault: true, isDisabled: false },
    { id: '6', name: 'Certificats et courriers', isFavorite: false, isDefault: false, isDisabled: false },
    { id: '7', name: 'État financier', isFavorite: false, isDefault: false, isDisabled: false },
    { id: '8', name: 'Plan de soins', isFavorite: false, isDefault: true, isDisabled: false },
    { id: '9', name: 'Formulaires', isFavorite: false, isDefault: true, isDisabled: false },
  ]);

  // Toggle favorite status
  const toggleFavorite = (id: string) => {
    setVisiteItems(items =>
      items.map(item =>
        item.id === id ? { ...item, isFavorite: !item.isFavorite } : item
      )
    );
  };

  // Toggle default status
  const toggleDefault = (id: string) => {
    setVisiteItems(items =>
      items.map(item =>
        item.id === id ? { ...item, isDefault: !item.isDefault } : item
      )
    );
  };

  return (
    <div className="w-full">
      <Table
        striped
        highlightOnHover
        withTableBorder
        withColumnBorders
        className="w-full"
      >
        <Table.Thead>
          <Table.Tr>
            <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-1/3"style={{ paddingLeft:'20px',textAlign: 'left',width: '90%' }}>
              Lien
            </Table.Th>
            <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm text-center w-1/3"style={{ textAlign: 'center' }}>
              Par défaut
            </Table.Th>
            <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm text-center w-1/3">
              Désa...
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {visiteItems.map((item) => (
            <Table.Tr key={item.id} className="hover:bg-gray-50">
              {/* Lien column with star and name */}
              <Table.Td className="border-r border-gray-300">
                <Group gap="sm" align="center">
                  <ActionIcon
                    variant="subtle"
                    size="sm"
                    onClick={() => toggleFavorite(item.id)}
                    className="text-gray-400 hover:text-yellow-500"
                  >
                    {item.isFavorite ? (
                      <IconStarFilled size={16} className="text-green-500" />
                    ) : (
                      <IconStar size={16} className="text-gray-400" />
                    )}
                  </ActionIcon>
                  <Text size="sm" className="text-gray-700">
                    {item.name}
                  </Text>
                </Group>
              </Table.Td>

              {/* Par défaut column with switch */}
              <Table.Td className="border-r border-gray-300 text-center">
                <div className="flex justify-center">
                  <Switch
                    checked={item.isDefault}
                    onChange={() => toggleDefault(item.id)}
                    color="blue"
                    size="sm"
                  />
                </div>
              </Table.Td>

              {/* Désa... column with menu */}
              <Table.Td className="text-center">
                <div className="flex justify-center">
                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <ActionIcon variant="subtle" color="gray" size="sm">
                        <IconDots size={16} />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item leftSection={<IconEdit size={14} />}>
                        Modifier
                      </Menu.Item>
                      <Menu.Item leftSection={<IconSettings size={14} />}>
                        Paramètres
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<IconTrash size={14} />}
                        color="red"
                      >
                        Supprimer
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </div>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </div>
  );
};

export default Visite;
