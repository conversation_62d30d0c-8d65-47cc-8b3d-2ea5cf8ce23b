'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconCash,
  IconCalendar,
} from '@tabler/icons-react';

const EcheanceDesChequesEspece = () => {
  // États pour les filtres
  const [dateDebut, setDateDebut] = useState<Date | null>(new Date('2022-09-01'));
  const [dateFin, setDateFin] = useState<Date | null>(new Date('2022-09-16'));
  const [mode, setMode] = useState('espece');

  // Interface pour les données d'échéance
  interface EcheanceData {
    dateEcheance: string;
    payerName: string;
    bank: string;
    numeroReglement: string;
    nombreCheques: number;
    montantEncaisse: number;
  }

  // Données d'exemple correspondant à l'image
  const donneesEcheances: EcheanceData[] = [
    // Pas de données dans l'exemple de l'image, juste les en-têtes
  ];

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconCash size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Échéance des chèques
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-64 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* Filtre Date Du */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Du
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  placeholder="01/09/2022"
                  value={dateDebut}
                  onChange={setDateDebut}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Filtre Date Au */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Au
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  placeholder="16/09/2022"
                  value={dateFin}
                  onChange={setDateFin}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Filtre Mode */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Mode
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={mode}
                  onChange={setMode}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="cheque" label="Chèque" />
                    <Radio value="espece" label="Espèce" />
                  </Stack>
                </Radio.Group>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du tableau */}
        <div className="flex-1 bg-white">
          <Table
            striped={false}
            highlightOnHover={false}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Date d&apos;échéance
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  payer_name
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  bank
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  N° Règlement
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nombre des chèques
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Montant encaissé
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {/* Données des échéances */}
              {donneesEcheances.map((item, index) => (
                <Table.Tr key={index} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.dateEcheance}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.payerName}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.bank}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.numeroReglement}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-center">
                    <Text size="sm" className="text-gray-800">
                      {item.nombreCheques}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-center">
                    <Text size="sm" className="text-gray-800">
                      {item.montantEncaisse}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))}

              {/* Ligne Total avec fond vert */}
              <Table.Tr className="bg-green-100">
                <Table.Td className="border-r border-gray-300 bg-green-100">
                  <Text size="sm" fw={500} className="text-gray-800">
                    Total
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-100" />
                <Table.Td className="border-r border-gray-300 bg-green-100" />
                <Table.Td className="border-r border-gray-300 bg-green-100" />
                <Table.Td className="border-r border-gray-300 bg-green-200 text-center">
                  <div className="inline-block px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      Nombre des chèques
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200 text-center">
                  <div className="inline-block px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      Montant encaissé
                    </Text>
                  </div>
                </Table.Td>
              </Table.Tr>

              {/* Lignes vides pour remplir l'espace */}
              {Array.from({ length: 15 }, (_, index) => (
                <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                  {Array.from({ length: 6 }, (_, cellIndex) => (
                    <Table.Td
                      key={cellIndex}
                      className="border-r border-gray-300 h-8"
                    />
                  ))}
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>
    </Box>
  );
};

export default EcheanceDesChequesEspece;
