#!/usr/bin/env python3

"""
Test script to verify duplicate email prevention is working properly.
This script tests the backend email validation.
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.core.exceptions import ValidationError
from users.models import User

def test_duplicate_email_prevention():
    """Test that duplicate emails are properly prevented."""
    
    print("🧪 TESTING DUPLICATE EMAIL PREVENTION")
    print("=" * 50)
    
    test_email = "<EMAIL>"
    
    # Clean up any existing test users
    User.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing users with email: {test_email}")
    
    # Test 1: Create first user
    print(f"\n1️⃣ Creating first user with email: {test_email}")
    try:
        user1 = User.objects.create_user(
            email=test_email,
            password="testpass123",
            first_name="Test",
            last_name="User1",
            user_type="patient"
        )
        print(f"✅ Successfully created first user: {user1.first_name} {user1.last_name}")
    except Exception as e:
        print(f"❌ Failed to create first user: {e}")
        return False
    
    # Test 2: Try to create duplicate user
    print(f"\n2️⃣ Attempting to create duplicate user with same email: {test_email}")
    try:
        user2 = User.objects.create_user(
            email=test_email,
            password="testpass456",
            first_name="Test",
            last_name="User2",
            user_type="patient"
        )
        print(f"❌ CRITICAL ERROR: Duplicate user was created! {user2.first_name} {user2.last_name}")
        return False
    except ValidationError as e:
        print(f"✅ Duplicate email properly blocked by ValidationError: {e}")
    except Exception as e:
        print(f"✅ Duplicate email blocked by other validation: {e}")
    
    # Test 3: Try case-insensitive duplicate
    print(f"\n3️⃣ Testing case-insensitive duplicate: {test_email.upper()}")
    try:
        user3 = User.objects.create_user(
            email=test_email.upper(),
            password="testpass789",
            first_name="Test",
            last_name="User3",
            user_type="patient"
        )
        print(f"❌ CRITICAL ERROR: Case-insensitive duplicate was created! {user3.first_name} {user3.last_name}")
        return False
    except ValidationError as e:
        print(f"✅ Case-insensitive duplicate properly blocked: {e}")
    except Exception as e:
        print(f"✅ Case-insensitive duplicate blocked: {e}")
    
    # Test 4: Verify only one user exists
    print(f"\n4️⃣ Verifying user count for email: {test_email}")
    user_count = User.objects.filter(email__iexact=test_email).count()
    if user_count == 1:
        print(f"✅ Correct: Only 1 user exists with email {test_email}")
    else:
        print(f"❌ CRITICAL ERROR: {user_count} users exist with email {test_email}")
        return False
    
    # Clean up
    User.objects.filter(email=test_email).delete()
    print(f"\n🧹 Cleaned up test users")
    
    print(f"\n🎉 ALL DUPLICATE EMAIL TESTS PASSED!")
    return True

if __name__ == '__main__':
    try:
        success = test_duplicate_email_prevention()
        if success:
            print("\n✅ DUPLICATE EMAIL PREVENTION IS WORKING CORRECTLY")
        else:
            print("\n❌ DUPLICATE EMAIL PREVENTION HAS ISSUES")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
