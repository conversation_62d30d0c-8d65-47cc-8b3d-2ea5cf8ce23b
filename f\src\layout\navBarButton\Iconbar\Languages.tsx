
import React, { useState } from 'react';

import { useTranslation } from '~/i18n/client';
import { useLanguage } from '~/contexts/LanguageContext';
import { Locales, supportedLocales } from '~/i18n/settings';
import Image from 'next/legacy/image'
import { Tooltip, Group, rem, Menu, ActionIcon, Loader } from "@mantine/core";
import { IconChevronDown, IconLanguage } from "@tabler/icons-react";
import { notifications } from '@mantine/notifications';

const Languages = () => {
  const { t } = useTranslation('menu');
  const { language, saveLanguage, isLoading } = useLanguage();
  const [saving, setSaving] = useState(false);

  const handleLocaleChange = async (locale: string) => {
    if (!supportedLocales.includes(locale as Locales) || saving || isLoading) {
      return;
    }

    const newLanguage = locale as Locales;

    if (newLanguage === language) {
      return; // No change needed
    }

    try {
      setSaving(true);
      await saveLanguage(newLanguage);

      notifications.show({
        title: t('language-updated') || 'Language Updated',
        message: t('language-changed-to') || `Language changed to ${locale}`,
        color: 'green',
      });
    } catch (error) {
      console.error('Error saving language:', error);
      notifications.show({
        title: t('error') || 'Error',
        message: t('language-save-error') || 'Failed to save language preference. Please try again.',
        color: 'red',
      });
    } finally {
      setSaving(false);
    }
  };
  return (
    <>
      <Menu
        width={200}
        position="bottom-end"
        transitionProps={{ transition: "pop-top-right" }}
        withinPortal
        shadow="lg"
        zIndex={1000010}
     
      >
        <Menu.Target>
        
            <Group gap={7} pl={"1px"} >
            <Tooltip
            label={t('top-title-Langue')}
            withArrow
            style={{color:"var(--mantine-color-text)"}}
          >
              <ActionIcon
                //variant="light"
                // className="h-10 bg-[var(--bg-SwitchColor)] text-[var(--mantine-color-dark-0)] focus:outline-none hover:bg-[var(---mantinelight-hover)]"
                // className="h-10 w-10 bg-[var(--bg-SwitchColor)]  focus:outline-none navBarButtonicon"
                //style={{color:" light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-0))"}}
                className="h-10 w-10 rounded  navBarButtonicon"
                
              >
                <IconLanguage radius="xl" size={20} className=" navBarButtonicon "/>
                {/* <IconLanguage radius="xl" size={20} /> */}
                 <IconChevronDown
                  style={{ width: rem(12), height: rem(12) }}
                  stroke={1.5}
                  
                />
              </ActionIcon>
              </Tooltip>
            </Group>
         
        </Menu.Target>
        <Menu.Dropdown
       
        >
        {language === 'fr' ? null : (
          <>
          <div>
          <Menu.Item
            leftSection={
              <Image
                className="mr-2 rounded-full"
                src={"/flags/fr.svg"}
                alt="Image description"
                width={16}
                height={16}
              />
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('fr');
            }}
            disabled={saving || isLoading}
          >
             {t('menu-Fransh')}
             {saving ? <Loader size={12} ml="xs" /> : null}
          </Menu.Item>
          </div>
          <Menu.Divider />
          </>
        )}
        {language === 'en' ? null : (
          <>
          <Menu.Item
            leftSection={
              <>
                <Image
                   className="h-4 w-4 rounded-full ring-3  ring-white "
                  src={"/flags/us.svg"}
                  alt="Image description"
                  width={16}
                  height={16}
                />
              </>
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('en');
            }}
            disabled={saving || isLoading}
          >
             {t('menu-English')}
             {saving ? <Loader size={12} ml="xs" /> : null}
          </Menu.Item>
          <Menu.Divider />
          </>
        )}
        {language === 'ar' ? null : (
          <>
          <Menu.Item
            leftSection={
              <>
                <Image
                  className="mr-2 rounded-full"
                  src={"/flags/ma.svg"}
                  alt="Image description"
                  width={16}
                  height={16}
                />
              </>
            }
            onClick={(e) => {
              e.preventDefault();
              handleLocaleChange('ar');
            }}
            disabled={saving || isLoading}
          >
            {t('menu-Arabic')}
            {saving ? <Loader size={12} ml="xs" /> : null}
          </Menu.Item>
          </>
        )}
        </Menu.Dropdown>
      </Menu>
      {/* <span className="-mx-3.5 text-[var(--bg-base-200)]">|</span> */}
    </>
  );
};

export default Languages;
