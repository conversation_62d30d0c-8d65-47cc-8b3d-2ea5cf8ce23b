
import React, { useState, useEffect } from 'react';
import { Modal,Text,Table,Input,} from '@mantine/core';
import Icon from '@mdi/react';
import { mdiFolderAccount ,mdiMagnify,mdiArrowUp,mdiChevronRight,mdiChevronLeft,mdiChevronDoubleRight,mdiChevronDoubleLeft,} from '@mdi/js';
import SimpleBar from "simplebar-react";
// import { 
  
//   ArrowUp,
//   ChevronRight,
//   ChevronLeft,
//   ChevronsRight,
//   ChevronsLeft,
  
// } from 'lucide-react';

interface Visit {
  id: string | number;
  visit_date: string;
  last_name: string;
  first_name: string;
  payment_amount: string;
  payment_received_amount: string;
  payment_general_discount: string;
  payment_status: string;
  payment_unpaid_amount: string;
}

interface PatientVisitsDialogProps {
  opened: boolean;
  onClose: () => void;
  beneficiaryType?: 'ORGANIZATION' | 'INDIVIDUAL';
}
const ListeDesVisitesDentaire: React.FC<PatientVisitsDialogProps> = ({ 

  opened, 
  onClose, 
  beneficiaryType = 'ORGANIZATION' 
}) => {
  const [visits, setVisits] = useState<Visit[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selected, setSelected] = useState<(string | number)[]>([]);
  const [searchFilters, setSearchFilters] = useState({
    lastName: '',
    firstName: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });
  const [sortBy, setSortBy] = useState({
    field: 'visit_date',
    direction: 'desc'
  });

  // Mock data for demonstration
  useEffect(() => {
    if (opened) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setVisits([]);
        setPagination(prev => ({ ...prev, total: 0 }));
        setLoading(false);
      }, 1000);
    }
  }, [opened, searchFilters, pagination.page, pagination.limit, sortBy]);

  const handleSort = (field: string) => {
    setSortBy(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSearchChange = (field: string, value: string) => {
    setSearchFilters(prev => ({ ...prev, [field]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelected(visits.map(visit => visit.id));
    } else {
      setSelected([]);
    }
  };

  const handleSelectRow = (visitId: string | number, checked: boolean) => {
    if (checked) {
      setSelected(prev => [...prev, visitId]);
    } else {
      setSelected(prev => prev.filter(id => id !== visitId));
    }
  };

  const handleAddSelection = () => {
    console.log('Selected visits:', selected);
    onClose();
  };

  const getSortIcon = (field: string) => {
    if (sortBy.field !== field) return null;
    return (
<Icon path={mdiArrowUp} size={1} className={`ml-1 transition-transform ${
          sortBy.direction === 'desc' ? 'rotate-180' : ''
        }`} />
      // <ArrowUp
      //   size={16}
      //   className={`ml-1 transition-transform ${
      //     sortBy.direction === 'desc' ? 'rotate-180' : ''
      //   }`}
      ///>
    );
  };

  const columns = [
    {
      key: 'visit_date',
      label: 'Date',
      sortable: true,
      className: 'w-32'
    },
    {
      key: 'last_name',
      label: 'Nom',
      sortable: true,
      searchable: beneficiaryType === 'ORGANIZATION'
    },
    {
      key: 'first_name',
      label: 'Prénom',
      sortable: true,
      searchable: beneficiaryType === 'ORGANIZATION'
    },
    {
      key: 'payment_amount',
      label: 'Montant dû',
      numeric: true,
      className: 'text-right'
    },
    // {
    //   key: 'payment_received_amount',
    //   label: 'Montant encaissé',
    //   numeric: true,
    //   className: 'text-right'
    // },
    // {
    //   key: 'payment_general_discount',
    //   label: 'Remise',
    //   numeric: true,
    //   className: 'text-right'
    // },
    // {
    //   key: 'payment_status',
    //   label: 'État',
    //   className: 'w-24'
    // },
    // {
    //   key: 'payment_unpaid_amount',
    //   label: 'Reste à régler',
    //   numeric: true,
    //   className: 'text-right'
    // }
  ];

  const totalPages = Math.ceil(pagination.total / pagination.limit);
  const startItem = pagination.total === 0 ? 0 : (pagination.page - 1) * pagination.limit + 1;
  const endItem = Math.min(pagination.page * pagination.limit, pagination.total);

  if (!opened) return null;

  return (
    <Modal.Content className="overflow-y-hidden">
       <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                  <Modal.Title>
                    <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                      <Icon path={mdiFolderAccount} size={1} />
                      Liste des visites dentaire
                    </Text>
                    
                  </Modal.Title>
                  <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                </Modal.Header>
                  <Modal.Body style={{ padding: '0px' }}>

      
     <div className="py-2 pl-4 max-h-[600px]">
        <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                      <div className="pr-4">

        {/* Content */}
    
    
            {/* Loading overlay */}
            {loading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-b-[#3799CE]"></div>
              </div>
            )}

           <Table striped highlightOnHover withTableBorder withColumnBorders>
              <Table.Thead className="bg-gray-50 sticky top-0">
                <Table.Tr>
                  <Table.Th className="w-12 px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={visits.length > 0 && selected.length === visits.length}
                      ref={input => {
                        if (input) {
                          input.indeterminate = selected.length > 0 && selected.length < visits.length;
                        }
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      disabled={visits.length === 0}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      aria-label="Sélectionner tout"
                    />
                  </Table.Th>
                  {columns.map(column => (
                    <Table.Th 
                      key={column.key}
                         className={`px-4 py-3 text-left text-sm font-medium text-gray-700 border-b-2 border-b-[#3799CE] ${column.className || ''} ${
                        column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                      }`}
                      onClick={column.sortable ? () => handleSort(column.key) : undefined}
                    >
                      <div className="flex items-center justify-between">
                        <span className="ml-2">{column.label}</span>
                        {column.sortable && getSortIcon(column.key)}
                      </div>
                    </Table.Th>
                  ))}
                  {/* <Table.Th className="w-12 px-4 py-3"></Table.Th> */}
                </Table.Tr>
                {beneficiaryType === 'ORGANIZATION' && (
                  <Table.Tr className="bg-gray-100">
                    <Table.Td className="px-4 py-2"></Table.Td>
                    <Table.Td className="px-4 py-2"></Table.Td>
                    <Table.Td className=" py-2" pl={0}>
                     <Input
                        placeholder="Rechercher"
                        value={searchFilters.lastName}
                        onChange={(e) => handleSearchChange('lastName', e.target.value)}
                      leftSection={<Icon path={mdiMagnify} size={0.75} />}
                        styles={{
                          input: {
                            borderTopWidth: "0",
                            borderRightWidth: "0",
                            borderLeftWidth: "0",
                            fontSize:"13px",
                            fontWeight:'500',
                            lineHeight:'20.15px'
                          },
                      }}
                      />
                    </Table.Td>
                    <Table.Td className=" py-2"pl={0}>
                       <Input
                        placeholder="Rechercher"
                        value={searchFilters.firstName}
                        onChange={(e) => handleSearchChange('firstName', e.target.value)}
                          leftSection={<Icon path={mdiMagnify} size={0.75} />}
                          styles={{
                          input: {
                              borderTopWidth: "0",
                            borderRightWidth: "0",
                            borderLeftWidth: "0",
                            fontSize:"13px",
                            fontWeight:'500',
                            lineHeight:'20.15px'
                          },
                      }}
                      />
                    </Table.Td>
                    <Table.Td className="px-4 py-2"></Table.Td>
                    {/* <Table.Td className="px-4 py-2"></Table.Td> */}
                    {/* <Table.Td className="px-4 py-2"></Table.Td>
                    <Table.Td className="px-4 py-2"></Table.Td>
                    <Table.Td className="px-4 py-2"></Table.Td>*/}
                    {/* <Table.Td className="px-4 py-2"></Table.Td>  */}
                  </Table.Tr>
                )}
              </Table.Thead>
              <Table.Tbody className="bg-white divide-y divide-gray-200">
                {visits.length === 0 ? (
                  <Table.Tr>
                    <Table.Td></Table.Td>
                    <Table.Td colSpan={9} className="px-4 py-8 text-center text-gray-500">
                      Aucun élément trouvé.
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  visits.map((visit) => (
                    <Table.Tr key={visit.id} className="hover:bg-gray-50">
                      <Table.Td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selected.includes(visit.id)}
                          onChange={(e) => handleSelectRow(visit.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-sm text-gray-900">{visit.visit_date}</Table.Td>
                      <Table.Td className="px-4 py-3 text-sm text-gray-900">{visit.last_name}</Table.Td>
                      <Table.Td className="px-4 py-3 text-sm text-gray-900">{visit.first_name}</Table.Td>
                      <Table.Td className="px-4 py-3 text-sm text-gray-900 text-right">{visit.payment_amount}</Table.Td>
                      {/* <Table.Td className="px-4 py-3 text-sm text-gray-900 text-right">{visit.payment_received_amount}</Table.Td> */}
                      {/* <Table.Td className="px-4 py-3 text-sm text-gray-900 text-right">{visit.payment_general_discount}</Table.Td> */}
                      {/* <Table.Td className="px-4 py-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          visit?.payment_status === 'PAID' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {visit?.payment_status}
                        </span>
                      </Table.Td> */}
                      {/* <Table.Td className="px-4 py-3 text-sm text-gray-900 text-right">{visit.payment_unpaid_amount}</Table.Td> */}
                       {/* <Table.Td className="px-4 py-3"></Table.Td>  */}
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>
         

          {/* Pagination */}
          <div className=" bg-gray-50 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Page</span>
                  <select
                    value={pagination.page}
                    onChange={(e) => setPagination(prev => ({ ...prev, page: parseInt(e.target.value) }))}
                    className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={totalPages === 0}
                  >
                    {Array.from({ length: totalPages || 1 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>{i + 1}</option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Lignes par Page</span>
                  <select
                    value={pagination.limit}
                    onChange={(e) => setPagination(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
                    className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="100">100</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-700">
                  {startItem} - {endItem} de {pagination.total}
                </span>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
                    disabled={pagination.page === 1 || totalPages === 0}
                    className="p-1 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Première page"
                  >
                    {/* <ChevronsLeft size={16} /> */}<Icon path={mdiChevronDoubleLeft} size={1} />
                  </button>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1 || totalPages === 0}
                    className="p-1 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Page précédente"
                  >
                    {/* <ChevronLeft size={16} /> */}<Icon path={mdiChevronLeft} size={1} />
                  </button>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page >= totalPages || totalPages === 0}
                    className="p-1 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Page suivante"
                  >
                    {/* <ChevronRight size={16} /> */}
                    <Icon path={mdiChevronRight} size={1} />
                  </button>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: totalPages }))}
                    disabled={pagination.page >= totalPages || totalPages === 0}
                    className="p-1 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Dernière page"
                  >
                    {/* <ChevronsRight size={16} /> */}<Icon path={mdiChevronDoubleRight} size={1} />
                  </button>
                </div>
              </div>
            </div>
          </div>
      

        {/* Footer */}
                     <div className="flex justify-end space-x-3 p-4 border-t border-t-[#3799CE] bg-gray-50 rounded-b-lg">
          <button
            onClick={handleAddSelection}
            className="px-4 py-2 bg-[#3799CE] text-white rounded-md hover:bg-[teal] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Ok
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-[#FA5252] text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          >
            Annuler
          </button>
        </div>
     </div>
        </SimpleBar>
   </div>
    </Modal.Body>
    </Modal.Content>
  );
};
export default ListeDesVisitesDentaire;