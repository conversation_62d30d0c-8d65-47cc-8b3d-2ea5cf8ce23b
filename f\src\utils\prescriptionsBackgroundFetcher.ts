/**
 * Prescriptions Background Data Fetcher
 * Handles background fetching and caching of prescriptions data
 * Provides real-time updates without blocking the UI
 */

import { prescriptionsService, PrescriptionsSummary } from '@/services/prescriptionsService';

interface CacheEntry {
  data: PrescriptionsSummary;
  timestamp: number;
  expiresAt: number;
}

interface FetchOptions {
  forceRefresh?: boolean;
  cacheTimeout?: number; // in milliseconds
  priority?: 'low' | 'normal' | 'high';
  patientId?: string;
  dateRange?: { start: string; end: string };
}

interface BackgroundFetcherConfig {
  defaultCacheTimeout: number;
  maxConcurrentFetches: number;
  retryAttempts: number;
  retryDelay: number;
}

class PrescriptionsBackgroundFetcher {
  private cache = new Map<string, CacheEntry>();
  private activeFetches = new Map<string, Promise<PrescriptionsSummary>>();
  private fetchQueue: Array<{ 
    cacheKey: string; 
    options: FetchOptions; 
    resolve: Function; 
    reject: Function 
  }> = [];
  private isProcessingQueue = false;
  
  private config: BackgroundFetcherConfig = {
    defaultCacheTimeout: 3 * 60 * 1000, // 3 minutes (prescriptions change frequently)
    maxConcurrentFetches: 3,
    retryAttempts: 3,
    retryDelay: 1500, // 1.5 seconds
  };

  private listeners = new Map<string, Set<(data: PrescriptionsSummary) => void>>();

  /**
   * Get prescriptions data with background fetching
   */
  async getPrescriptionsData(options: FetchOptions = {}): Promise<PrescriptionsSummary> {
    const {
      forceRefresh = false,
      cacheTimeout = this.config.defaultCacheTimeout,
      priority = 'normal',
      patientId,
      dateRange
    } = options;

    const cacheKey = this.generateCacheKey(patientId, dateRange);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        // Start background refresh if data is getting old
        const age = Date.now() - cached.timestamp;
        if (age > cacheTimeout * 0.7) { // Refresh when 70% of cache time has passed
          this.queueBackgroundFetch(cacheKey, { ...options, priority: 'low' });
        }
        return cached.data;
      }
    }

    // Check if already fetching
    const activeFetch = this.activeFetches.get(cacheKey);
    if (activeFetch) {
      return activeFetch;
    }

    // Queue the fetch
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options: { ...options, cacheTimeout }, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Subscribe to real-time updates for prescriptions data
   */
  subscribe(callback: (data: PrescriptionsSummary) => void, patientId?: string, dateRange?: { start: string; end: string }): () => void {
    const cacheKey = this.generateCacheKey(patientId, dateRange);
    
    if (!this.listeners.has(cacheKey)) {
      this.listeners.set(cacheKey, new Set());
    }
    
    this.listeners.get(cacheKey)!.add(callback);

    // Start background fetching for this patient/date range
    this.queueBackgroundFetch(cacheKey, { priority: 'normal', patientId, dateRange });

    // Return unsubscribe function
    return () => {
      const keyListeners = this.listeners.get(cacheKey);
      if (keyListeners) {
        keyListeners.delete(callback);
        if (keyListeners.size === 0) {
          this.listeners.delete(cacheKey);
        }
      }
    };
  }

  /**
   * Preload prescriptions data for multiple patients
   */
  async preloadPatients(patientIds: string[], options: FetchOptions = {}): Promise<void> {
    const promises = patientIds.map(patientId => 
      this.queueBackgroundFetch(
        this.generateCacheKey(patientId, options.dateRange), 
        { ...options, patientId, priority: 'low' }
      )
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Clear cache for a specific patient or all data
   */
  clearCache(patientId?: string, dateRange?: { start: string; end: string }): void {
    if (patientId || dateRange) {
      const cacheKey = this.generateCacheKey(patientId, dateRange);
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    averageAge: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const now = Date.now();
    let expiredCount = 0;
    let totalAge = 0;
    let oldestAge = 0;
    let newestAge = Infinity;
    
    for (const [, entry] of this.cache) {
      const age = now - entry.timestamp;
      if (entry.expiresAt < now) {
        expiredCount++;
      }
      totalAge += age;
      oldestAge = Math.max(oldestAge, age);
      newestAge = Math.min(newestAge, age);
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would need to track hits/misses
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
      oldestEntry: oldestAge,
      newestEntry: newestAge === Infinity ? 0 : newestAge,
    };
  }

  /**
   * Force refresh data and notify all subscribers
   */
  async refreshData(patientId?: string, dateRange?: { start: string; end: string }): Promise<PrescriptionsSummary> {
    const cacheKey = this.generateCacheKey(patientId, dateRange);
    this.clearCache(patientId, dateRange);
    const data = await this.getPrescriptionsData({ 
      forceRefresh: true, 
      priority: 'high',
      patientId,
      dateRange 
    });
    this.notifyListeners(cacheKey, data);
    return data;
  }

  /**
   * Get patient prescription summary
   */
  async getPatientPrescriptionData(patientId: string, dateRange?: { start: string; end: string }) {
    const data = await this.getPrescriptionsData({ patientId, dateRange });
    
    const patientPrescriptions = data.prescriptions.filter(p => p.patient_id === patientId);
    const activePrescriptions = patientPrescriptions.filter(p => p.status === 'active');
    const expiredPrescriptions = patientPrescriptions.filter(p => p.status === 'expired');
    const patientHistory = data.medicationHistory.filter(h => h.patient_id === patientId);
    const patientRefills = data.prescriptionRefills.filter(r => 
      patientPrescriptions.some(p => p.id === r.prescription_id)
    );

    return {
      totalPrescriptions: patientPrescriptions.length,
      activePrescriptions: activePrescriptions.length,
      expiredPrescriptions: expiredPrescriptions.length,
      medicationHistory: patientHistory.length,
      pendingRefills: patientRefills.filter(r => r.status === 'requested' || r.status === 'approved').length,
      controlledSubstances: patientPrescriptions.filter(p => p.is_controlled_substance).length,
      drugInteractions: data.drugInteractions.length,
      lastPrescriptionDate: patientPrescriptions.length > 0 ? 
        patientPrescriptions.sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())[0].date_prescribed : null,
      mostCommonMedication: this.getMostCommonMedication(patientPrescriptions),
      complianceRate: this.calculateComplianceRate(patientPrescriptions),
    };
  }

  /**
   * Get prescription alerts
   */
  async getPrescriptionAlerts(patientId?: string, dateRange?: { start: string; end: string }) {
    const data = await this.getPrescriptionsData({ patientId, dateRange });
    
    const today = new Date();
    const nearExpiryDate = new Date();
    nearExpiryDate.setDate(today.getDate() + 30); // 30 days from now

    const expiredPrescriptions = data.prescriptions.filter(p => new Date(p.date_expires) < today);
    const nearExpiryPrescriptions = data.prescriptions.filter(p => {
      const expiryDate = new Date(p.date_expires);
      return expiryDate > today && expiryDate <= nearExpiryDate;
    });
    const overdueRefills = data.prescriptionRefills.filter(r => r.status === 'requested' && 
      new Date(r.refill_date).getTime() < today.getTime() - (7 * 24 * 60 * 60 * 1000) // 7 days overdue
    );
    const drugInteractions = data.drugInteractions.filter(i => i.interaction_type === 'major' || i.interaction_type === 'moderate');

    return {
      expiredPrescriptions,
      nearExpiryPrescriptions,
      overdueRefills,
      drugInteractions,
      totalAlerts: expiredPrescriptions.length + nearExpiryPrescriptions.length + overdueRefills.length + drugInteractions.length,
    };
  }

  // Private methods

  private generateCacheKey(patientId?: string, dateRange?: { start: string; end: string }): string {
    let key = patientId || 'all';
    if (dateRange) {
      key += `_${dateRange.start}_${dateRange.end}`;
    }
    return key;
  }

  private getCachedData(cacheKey: string): CacheEntry | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    const now = Date.now();
    if (entry.expiresAt < now) {
      this.cache.delete(cacheKey);
      return null;
    }

    return entry;
  }

  private async queueBackgroundFetch(cacheKey: string, options: FetchOptions): Promise<PrescriptionsSummary> {
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.fetchQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.fetchQueue.length > 0 && this.activeFetches.size < this.config.maxConcurrentFetches) {
      // Sort queue by priority
      this.fetchQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.options.priority || 'normal'] - priorityOrder[a.options.priority || 'normal'];
      });

      const item = this.fetchQueue.shift();
      if (!item) break;

      const { cacheKey, options, resolve, reject } = item;

      // Skip if already fetching this cache key
      if (this.activeFetches.has(cacheKey)) {
        const existingFetch = this.activeFetches.get(cacheKey)!;
        existingFetch.then(resolve).catch(reject);
        continue;
      }

      // Start the fetch
      const fetchPromise = this.performFetch(cacheKey, options);
      this.activeFetches.set(cacheKey, fetchPromise);

      fetchPromise
        .then((data) => {
          resolve(data);
          this.notifyListeners(cacheKey, data);
        })
        .catch(reject)
        .finally(() => {
          this.activeFetches.delete(cacheKey);
          // Continue processing queue
          setTimeout(() => this.processQueue(), 0);
        });
    }

    this.isProcessingQueue = false;
  }

  private async performFetch(cacheKey: string, options: FetchOptions): Promise<PrescriptionsSummary> {
    const { cacheTimeout = this.config.defaultCacheTimeout, patientId, dateRange } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const data = await prescriptionsService.getPrescriptionsSummary(patientId, dateRange);
        
        // Cache the result
        const now = Date.now();
        this.cache.set(cacheKey, {
          data,
          timestamp: now,
          expiresAt: now + cacheTimeout,
        });

        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError || new Error('Failed to fetch prescriptions data');
  }

  private notifyListeners(cacheKey: string, data: PrescriptionsSummary): void {
    const listeners = this.listeners.get(cacheKey);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in prescriptions listener:', error);
        }
      });
    }
  }

  private getMostCommonMedication(prescriptions: any[]): string {
    if (prescriptions.length === 0) return 'N/A';
    
    const medicationCounts = prescriptions.reduce((acc, p) => {
      acc[p.medication_name] = (acc[p.medication_name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(medicationCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
  }

  private calculateComplianceRate(prescriptions: any[]): number {
    if (prescriptions.length === 0) return 0;
    
    const completedPrescriptions = prescriptions.filter(p => p.status === 'completed').length;
    return (completedPrescriptions / prescriptions.length) * 100;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [cacheKey, entry] of this.cache) {
      if (entry.expiresAt < now) {
        this.cache.delete(cacheKey);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startPeriodicCleanup(interval: number = 120000): () => void { // 2 minutes
    const intervalId = setInterval(() => {
      this.cleanupCache();
    }, interval);

    return () => clearInterval(intervalId);
  }
}

// Create singleton instance
export const prescriptionsBackgroundFetcher = new PrescriptionsBackgroundFetcher();

// Auto-start periodic cleanup
prescriptionsBackgroundFetcher.startPeriodicCleanup();

export default prescriptionsBackgroundFetcher;
