#!/usr/bin/env node

/**
 * Simple test script to verify API connection and data format
 */

const https = require('https');
const http = require('http');

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://127.0.0.1:8000';

console.log('🧪 Simple API Test');
console.log('==================');

/**
 * Make HTTP request to API
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = API_BASE_URL.startsWith('https') ? https : http;
    const req = protocol.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      console.log('Sending data:', JSON.stringify(data));
      const jsonData = JSON.stringify(data);
      req.write(jsonData);
    }
    
    req.end();
  });
}

/**
 * Test patient data
 */
const TEST_PATIENT = {
  email: '<EMAIL>',
  password: 'TestPass123!',
  password2: 'TestPass123!',
  first_name: 'Simple',
  last_name: 'Test',
  phone_number: '**********'
};

async function runTest() {
  console.log('Testing patient creation...\n');
  
  const options = {
    hostname: new URL(API_BASE_URL).hostname,
    port: new URL(API_BASE_URL).port,
    path: '/api/users/patients/create-from-frontend/',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': JSON.stringify(TEST_PATIENT).length
    }
  };
  
  try {
    console.log('Sending request to:', `${API_BASE_URL}/api/users/patients/create-from-frontend/`);
    const response = await makeRequest(options, TEST_PATIENT);
    console.log(`Status: ${response.statusCode}`);
    console.log('Response:', response.data);
  } catch (error) {
    console.log('Error:', error.message);
  }
}

// Run the test
runTest().catch(error => {
  console.error('Test failed with error:', error);
});