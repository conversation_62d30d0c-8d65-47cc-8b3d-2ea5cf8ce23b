
// Core types
export interface ConsultationType {
  value: string;
  label: string;
  duration?: string;
  color?: string;
}

export interface TitleOption {
  value: string;
  label: string;
}

export interface AgendaType {
  value: string;
  label: string;
}

export interface Patient {
    id: string;
    title: string;
    name: string;
    prenom: string;
    first_name: string;
    last_name: string;
    birth_date: string;
    appointmentDate: string;
    appointmentTime: string;
    appointmentEndTime: string;
    consultationDuration: number;
    cin?: string;
    email: string;
    age: number;
    phone_numbers: string;
    socialSecurity: string;
    duration: string;
    agenda: string;
    comment: string;
    address: string;
    etatCivil: string;
    etatAganda: string;
    patientTitle: string;
    notes: string;
    date: string;
    docteur: string;
    event_Title: string;
    gender: string;
    sociale: string;
    typeConsultation: string;
    commentairelistedattente: string;
    resourceId: number,
    lastVisit?: {
      date: Date;
      notes: string;
    };
    color?: string;
    style?: { backgroundColor: string };
    disabled?: string;
    lunchTime?: boolean;
    clientName?: string;
    type: EventType;
    patientId?: string;
    eventType: EventType;
    start?: Date;
    end?: Date;
    visitorCount?: number;
    checkedListedattente?: boolean;
  }
  export interface Appointment {
    id: string;
  title: string;
  start: Date;
  end: Date;
  patientId: string;
  isActive: boolean;
  resourceId: number;
  name: string;
  prenom: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  appointmentDate: string;
  appointmentTime: string;
  appointmentEndTime: string;
  consultationDuration: number;
  cin?: string;
  email: string;
  age: number;
  phone_numbers: string;
  socialSecurity: string;
  duration: string;
  agenda: string;
  comment: string;
  address: string;
  etatCivil: string;
  etatAganda: string;
  patientTitle: string;
  notes: string;
  date: string;
  docteur: string;
  event_Title: string;
  gender: string;
  sociale: string;
  typeConsultation: string;
  commentairelistedattente: string;
  color?: string;
  style?: { backgroundColor: string };
  disabled?: string;
  lunchTime?: boolean;
  type: EventType;
  clientName?: string;
  checkedListedattente?: boolean;
  eventType: EventType;
  visitorCount: number;
  currentEventColor?: string;
  interventionDuration?: number;
  interventionStartTime?: string;
  interventionEndTime?: string;
  }
  export type EventType = "visit" | "visitor-counter" | "completed" | "diagnosis" | "Consultation"| "Contrôle"| "Urgence"|"Re-diagnose" |"Autre" ;

  export interface AppointmentEvent {
    id: string;
      title: string;
      start: Date;
      end: Date;
      patient?: Patient;
      first_name: string;
      last_name: string;
      name: string;
      prenom: string;
      birth_date: string;
      appointmentDate: string;
      appointmentTime: string;
      appointmentEndTime: string;
      consultationDuration: number;
      cin?: string;
      email: string;
      age: number;
      phone_numbers: string;
      socialSecurity: string;
      duration: string;
    agenda: string;
      comment: string;
      address: string;
      etatCivil: string;
      etatAganda: string;
      patientTitle: string;
      notes: string;
      date: string;
      docteur: string;
      event_Title: string;
      gender: string;
      sociale: string;
      typeConsultation: string;
      commentairelistedattente: string;
      resourceId: number;
      color?: string;
      style?: { backgroundColor: string };
      disabled?: string;
      lunchTime?: boolean;
      clientName?: string;
      type: EventType;
      isActive: boolean;  // Add this
      eventType: EventType;
      patientId: string;
      visitorCount?: number;
     checkedListedattente?: boolean;
  }
  export interface Event {
    id: number;
  title: string;
  name: string;
  prenom: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  age: number;
  etatCivil: string;
  etatAganda: string;
  cin: string;
  adresse: string;
  phone_numbers: string;
  email: string;
  docteur: string;
  typeConsultation: string;
  start: Date;
  end: Date;
  type: EventType;
  clientName?: string;
  description?: string;
  color?: string;
  style?: { backgroundColor: string };
  disabled?: string;
  isDraggable?: boolean;
  isResizable?: boolean;
  tooltip: string;
  lunchTime?: boolean;
  profession?: string; // ✅ Ajout de profession
  entreprise?: string;
  ville?: string;
  codePostal?: string;
  isExpanded?: boolean;  // Add this property
  resourceId: number;
  Commentaire: string;
  Commentairelistedattente: string;
  checkedListedattente?: boolean;
  checkedAppelvideo?: boolean;
  checkedRappelSms?: boolean;
  checkedRappelEmail?: boolean;
  sociale: string;

  }
  