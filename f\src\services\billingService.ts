/**
 * Billing Service
 * Handles all billing-related operations including invoices, quotes, payments, expenses, and contracts
 */

// Types for billing entities
export interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  items: InvoiceItem[];
  subtotal: number;
  tax: number;
  total: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Quote {
  id: string;
  number: string;
  date: string;
  validUntil: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  items: QuoteItem[];
  subtotal: number;
  tax: number;
  total: number;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Payment {
  id: string;
  invoiceId: string;
  amount: number;
  method: 'cash' | 'credit_card' | 'bank_transfer' | 'check';
  date: string;
  reference?: string;
  notes?: string;
  createdAt: string;
}

export interface Expense {
  id: string;
  date: string;
  category: string;
  description: string;
  amount: number;
  vendor?: string;
  receipt?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface Contract {
  id: string;
  title: string;
  clientId: string;
  clientName: string;
  startDate: string;
  endDate: string;
  value: number;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  terms: string;
  createdAt: string;
  updatedAt: string;
}

export interface BillingStats {
  totalInvoices: number;
  totalRevenue: number;
  pendingPayments: number;
  overdueInvoices: number;
  monthlyRevenue: number;
  yearlyRevenue: number;
}

class BillingService {
  private baseURL = 'http://127.0.0.1:8000/api/billing';

  // Invoice operations
  async getInvoices(filters?: { status?: string; patientId?: string }): Promise<Invoice[]> {
    try {
      let url = `${this.baseURL}/invoices`;
      if (filters) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value);
        });
        if (params.toString()) url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Invoices API not available (${response.status}), using mock data`);
        return this.getMockInvoices();
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Invoices API error, using mock data:', error);
      return this.getMockInvoices();
    }
  }

  async createInvoice(invoiceData: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>): Promise<Invoice> {
    try {
      const response = await fetch(`${this.baseURL}/invoices`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create invoice: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating invoice:', error);
      // Return mock created invoice
      return {
        id: Date.now().toString(),
        ...invoiceData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }
  }

  async updateInvoice(id: string, invoiceData: Partial<Invoice>): Promise<Invoice> {
    try {
      const response = await fetch(`${this.baseURL}/invoices/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update invoice: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error updating invoice:', error);
      return { id, ...invoiceData, updatedAt: new Date().toISOString() } as Invoice;
    }
  }

  async deleteInvoice(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/invoices/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete invoice: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting invoice:', error);
      console.log(`Mock deletion of invoice ${id}`);
    }
  }

  // Quote operations
  async getQuotes(filters?: { status?: string; patientId?: string }): Promise<Quote[]> {
    try {
      let url = `${this.baseURL}/quotes`;
      if (filters) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value);
        });
        if (params.toString()) url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Quotes API not available (${response.status}), using mock data`);
        return this.getMockQuotes();
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Quotes API error, using mock data:', error);
      return this.getMockQuotes();
    }
  }

  async createQuote(quoteData: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>): Promise<Quote> {
    try {
      const response = await fetch(`${this.baseURL}/quotes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(quoteData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create quote: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating quote:', error);
      return {
        id: Date.now().toString(),
        ...quoteData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }
  }

  // Payment operations
  async getPayments(invoiceId?: string): Promise<Payment[]> {
    try {
      let url = `${this.baseURL}/payments`;
      if (invoiceId) url += `?invoiceId=${invoiceId}`;

      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Payments API not available (${response.status}), using mock data`);
        return this.getMockPayments();
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Payments API error, using mock data:', error);
      return this.getMockPayments();
    }
  }

  async createPayment(paymentData: Omit<Payment, 'id' | 'createdAt'>): Promise<Payment> {
    try {
      const response = await fetch(`${this.baseURL}/payments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create payment: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating payment:', error);
      return {
        id: Date.now().toString(),
        ...paymentData,
        createdAt: new Date().toISOString(),
      };
    }
  }

  // Expense operations
  async getExpenses(filters?: { category?: string; status?: string }): Promise<Expense[]> {
    try {
      let url = `${this.baseURL}/expenses`;
      if (filters) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value);
        });
        if (params.toString()) url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Expenses API not available (${response.status}), using mock data`);
        return this.getMockExpenses();
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Expenses API error, using mock data:', error);
      return this.getMockExpenses();
    }
  }

  async createExpense(expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> {
    try {
      const response = await fetch(`${this.baseURL}/expenses`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(expenseData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create expense: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating expense:', error);
      return {
        id: Date.now().toString(),
        ...expenseData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }
  }

  // Contract operations
  async getContracts(filters?: { status?: string; clientId?: string }): Promise<Contract[]> {
    try {
      let url = `${this.baseURL}/contracts`;
      if (filters) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value);
        });
        if (params.toString()) url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Contracts API not available (${response.status}), using mock data`);
        return this.getMockContracts();
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Contracts API error, using mock data:', error);
      return this.getMockContracts();
    }
  }

  async createContract(contractData: Omit<Contract, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contract> {
    try {
      const response = await fetch(`${this.baseURL}/contracts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(contractData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create contract: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating contract:', error);
      return {
        id: Date.now().toString(),
        ...contractData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }
  }

  // Mock data methods (to be implemented)
  private getMockInvoices(): Invoice[] {
    return [
      {
        id: '1',
        number: 'INV-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        patientId: '1',
        patientName: 'Jean Dupont',
        doctorId: '1',
        doctorName: 'Dr. Martin',
        items: [
          { id: '1', description: 'Consultation', quantity: 1, unitPrice: 100, total: 100 }
        ],
        subtotal: 100,
        tax: 20,
        total: 120,
        status: 'sent',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      }
    ];
  }

  private getMockQuotes(): Quote[] {
    return [
      {
        id: '1',
        number: 'QUO-001',
        date: '2024-01-10',
        validUntil: '2024-02-10',
        patientId: '1',
        patientName: 'Jean Dupont',
        doctorId: '1',
        doctorName: 'Dr. Martin',
        items: [
          { id: '1', description: 'Treatment Plan', quantity: 1, unitPrice: 500, total: 500 }
        ],
        subtotal: 500,
        tax: 100,
        total: 600,
        status: 'sent',
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-10T10:00:00Z',
      }
    ];
  }

  private getMockPayments(): Payment[] {
    return [
      {
        id: '1',
        invoiceId: '1',
        amount: 120,
        method: 'credit_card',
        date: '2024-01-20',
        reference: 'PAY-001',
        createdAt: '2024-01-20T10:00:00Z',
      }
    ];
  }

  private getMockExpenses(): Expense[] {
    return [
      {
        id: '1',
        date: '2024-01-15',
        category: 'Office Supplies',
        description: 'Medical supplies',
        amount: 250,
        vendor: 'Medical Supply Co.',
        status: 'approved',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      }
    ];
  }

  private getMockContracts(): Contract[] {
    return [
      {
        id: '1',
        title: 'Annual Health Plan',
        clientId: '1',
        clientName: 'Jean Dupont',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        value: 5000,
        status: 'active',
        terms: 'Annual health coverage plan',
        createdAt: '2024-01-01T10:00:00Z',
        updatedAt: '2024-01-01T10:00:00Z',
      }
    ];
  }
}

export const billingService = new BillingService();
export default billingService;
