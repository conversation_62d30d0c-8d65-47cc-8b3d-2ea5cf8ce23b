import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle,  } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import Den<PERSON> from './Dentaire'
import CalendrierDeGrossesse from "./CalendrierDeGrossesse"
import PramètrageDhospitalisation from './PramètrageDhospitalisation'
import Oxymetrie from './Oxymetrie'
import PlanDeSoins from './PlanDeSoins'
import DonneesSportives from './DonneesSportives'
import FicheTechniqueSupplementaire from './FicheTechniqueSupplementaire'
// Mapping des sous-onglets pour Configuration de l'application
const subtabMapping: { [key: string]: string } = {
  'Dentaire': 'Dentaire',
  'CalendrierDeGrossesse': 'CalendrierDeGrossesse',
  'PramètrageDhospitalisation':'PramètrageDhospitalisation',
  'Oxymetrie':'Oxymetrie',
  'PlanDeSoins':'PlanDeSoins',
  'DonneesSportives':'DonneesSportives',
  'FicheTechniqueSupplementaire':'FicheTechniqueSupplementaire',


};

const Parametrage_du_module_des_specialites = () => {
  const [activeTab, setActiveTab] = useState('Dentaire');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'Dentaire')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="Dentaire" leftSection={<IconPhoto size={12} />}>
             Dentaire
           </Tabs.Tab>
           <Tabs.Tab value="CalendrierDeGrossesse" leftSection={<IconMessageCircle size={12} />}>
          CalendrierDeGrossesse
           </Tabs.Tab>
          <Tabs.Tab value="PramètrageDhospitalisation" leftSection={<IconMessageCircle size={12} />}>
          PramètrageDhospitalisation
           </Tabs.Tab>
           <Tabs.Tab value="Oxymetrie" leftSection={<IconMessageCircle size={12} />}>
          Oxymetrie
           </Tabs.Tab>
 <Tabs.Tab value="PlanDeSoins" leftSection={<IconMessageCircle size={12} />}>
         PlanDeSoins
           </Tabs.Tab>
 <Tabs.Tab value="DonneesSportives" leftSection={<IconMessageCircle size={12} />}>
         DonneesSportives
           </Tabs.Tab>
           <Tabs.Tab value="FicheTechniqueSupplementaire" leftSection={<IconMessageCircle size={12} />}>
         FicheTechniqueSupplementaire
           </Tabs.Tab>
         </Tabs.List>
         <Tabs.Panel value="Dentaire" ml={20}>
           <Dentaire/>
         </Tabs.Panel>

         <Tabs.Panel value="CalendrierDeGrossesse" ml={20}>
           <CalendrierDeGrossesse/>
         </Tabs.Panel>

         <Tabs.Panel value="PramètrageDhospitalisation" ml={20}>
           <PramètrageDhospitalisation/>
         </Tabs.Panel>
       
        <Tabs.Panel value="Oxymetrie" ml={20}>
           <Oxymetrie/>
         </Tabs.Panel>

           <Tabs.Panel value="PlanDeSoins" ml={20}>
           <PlanDeSoins/>
         </Tabs.Panel>
         <Tabs.Panel value="DonneesSportives" ml={20}>
           <DonneesSportives/>
         </Tabs.Panel>

          <Tabs.Panel value="FicheTechniqueSupplementaire" ml={20}>
           <FicheTechniqueSupplementaire/>
         </Tabs.Panel>

       </Tabs>
  )
};







export default Parametrage_du_module_des_specialites
