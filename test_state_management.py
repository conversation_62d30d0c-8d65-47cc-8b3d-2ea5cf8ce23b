#!/usr/bin/env python3

import requests
import json

def test_appointment_state_management():
    """Test appointment state management API endpoints"""
    
    # Test appointment ID
    appointment_id = '604eb10f-1feb-4605-af73-0fd0c3092744'
    base_url = 'http://127.0.0.1:8000/api'
    
    print('🧪 Testing Appointment State Management')
    print('=' * 50)
    
    # Test 1: Add to waiting list
    print('\n🔄 Test 1: Adding to waiting list...')
    response = requests.patch(f'{base_url}/appointments/{appointment_id}/', 
        json={'is_waiting_list': True, 'status': 'waiting_list'})
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success: is_waiting_list = {data.get("is_waiting_list", "N/A")}')
    else:
        print(f'❌ Failed: {response.text}')
    
    # Test 2: Add to presentation room
    print('\n🏥 Test 2: Adding to presentation room...')
    response = requests.patch(f'{base_url}/appointments/{appointment_id}/', 
        json={'is_in_presentation_room': True, 'status': 'in_progress'})
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success: is_in_presentation_room = {data.get("is_in_presentation_room", "N/A")}')
    else:
        print(f'❌ Failed: {response.text}')
    
    # Test 3: Add to active visits
    print('\n🏃 Test 3: Adding to active visits...')
    response = requests.patch(f'{base_url}/appointments/{appointment_id}/', 
        json={'is_active': True, 'status': 'in_progress'})
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success: is_active = {data.get("is_active", "N/A")}')
    else:
        print(f'❌ Failed: {response.text}')
    
    # Test 4: Complete and move to history journal
    print('\n📚 Test 4: Completing appointment...')
    response = requests.patch(f'{base_url}/appointments/{appointment_id}/', 
        json={
            'is_in_history_journal': True, 
            'is_active': False, 
            'is_in_presentation_room': False, 
            'status': 'completed'
        })
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success: is_in_history_journal = {data.get("is_in_history_journal", "N/A")}')
    else:
        print(f'❌ Failed: {response.text}')
    
    # Test 5: Get appointment details to verify all fields
    print('\n🔍 Test 5: Verifying final state...')
    response = requests.get(f'{base_url}/appointments/{appointment_id}/')
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print('✅ Final appointment state:')
        print(f'  - is_waiting_list: {data.get("is_waiting_list", "N/A")}')
        print(f'  - is_in_presentation_room: {data.get("is_in_presentation_room", "N/A")}')
        print(f'  - is_active: {data.get("is_active", "N/A")}')
        print(f'  - is_in_history_journal: {data.get("is_in_history_journal", "N/A")}')
        print(f'  - status: {data.get("status", "N/A")}')
    else:
        print(f'❌ Failed to get appointment details: {response.text}')
    
    print('\n' + '=' * 50)
    print('🎉 All state management tests completed!')

if __name__ == '__main__':
    test_appointment_state_management()
