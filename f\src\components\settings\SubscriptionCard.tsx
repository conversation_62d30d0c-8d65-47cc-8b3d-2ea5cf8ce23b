'use client';
import React from 'react';
import Link from 'next/link';
import {
  Title, Text, Paper, Button, Group, ThemeIcon, Badge, 
  SimpleGrid, List, Progress, Tooltip
} from '@mantine/core';
import { IconCrown } from '@tabler/icons-react';
import { SubscriptionPackage } from '@/services/userManagementService';

interface SubscriptionCardProps {
  subscription: SubscriptionPackage | null;
  userCounts: {
    assistants: number;
    patients: number;
  };
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({ 
  subscription, 
  userCounts 
}) => {
  if (!subscription) {
    return (
      <Paper withBorder p="xl" radius="md" mb="xl">
        <Group justify="space-between">
          <div>
            <Group mb="xs">
              <ThemeIcon size={32} radius="md" color="violet">
                <IconCrown size={20} />
              </ThemeIcon>
              <Title order={3}>Subscription</Title>
            </Group>
            <Text c="dimmed">Your current subscription plan and usage</Text>
          </div>
          <Button
            component={Link}
            href="/subscription"
            variant="light"
            color="violet"
          >
            Upgrade Plan
          </Button>
        </Group>
        <Text mt="md">Loading subscription information...</Text>
      </Paper>
    );
  }

  return (
    <Paper withBorder p="xl" radius="md" mb="xl">
      <Group justify="space-between">
        <div>
          <Group mb="xs">
            <ThemeIcon size={32} radius="md" color="violet">
              <IconCrown size={20} />
            </ThemeIcon>
            <Title order={3}>Subscription</Title>
          </Group>
          <Text c="dimmed">Your current subscription plan and usage</Text>
        </div>
        <Button
          component={Link}
          href="/subscription"
          variant="light"
          color="violet"
        >
          Upgrade Plan
        </Button>
      </Group>

      <Group mt="md">
        <Badge size="lg" color="violet" variant="filled">{subscription.name} Plan</Badge>
        <Text>${subscription.price_monthly}/month</Text>
      </Group>

      <SimpleGrid cols={2} mt="lg" spacing="xl">
        <div>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Assistant Accounts</Text>
            <Text size="sm" fw={700}>
              {userCounts.assistants} / {subscription.max_assistants}
            </Text>
          </Group>
          <Tooltip
            label={`${userCounts.assistants} of ${subscription.max_assistants} assistant accounts used`}
            position="bottom"
            withArrow
          >
            <Progress
              value={(userCounts.assistants / subscription.max_assistants) * 100}
              color={userCounts.assistants >= subscription.max_assistants ? "red" : "blue"}
              size="lg"
              radius="xl"
            />
          </Tooltip>
        </div>

        <div>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Patient Accounts</Text>
            <Text size="sm" fw={700}>
              {userCounts.patients} / {subscription.max_patients}
            </Text>
          </Group>
          <Tooltip
            label={`${userCounts.patients} of ${subscription.max_patients} patient accounts used`}
            position="bottom"
            withArrow
          >
            <Progress
              value={(userCounts.patients / subscription.max_patients) * 100}
              color={userCounts.patients >= subscription.max_patients ? "red" : "green"}
              size="lg"
              radius="xl"
            />
          </Tooltip>
        </div>
      </SimpleGrid>

      <Title order={4} mt="xl">Plan Features</Title>
      <List mt="md" spacing="sm">
        {subscription.features.map((feature, index) => (
          <List.Item key={index}>{feature}</List.Item>
        ))}
      </List>
    </Paper>
  );
};

export default SubscriptionCard;
