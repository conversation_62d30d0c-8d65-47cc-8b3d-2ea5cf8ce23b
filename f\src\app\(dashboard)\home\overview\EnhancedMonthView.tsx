'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { CalendarEvent } from '../CalendarPatient';

// Configure moment localizer
const localizer = momentLocalizer(moment);

// Configure French locale
moment.locale('fr', {
  months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),
  monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),
  weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),
  weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),
  weekdaysMin: 'Di_Lu_Ma_Me_Je_Ve_Sa'.split('_'),
});

interface EnhancedMonthViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick: (date: Date, hour: number, minute?: number, roomId?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
}

// Custom event component for month view
const CustomMonthEvent = ({ event }: { event: CalendarEvent }) => {
  return (
    <div className="rbc-event-content">
      <div className="font-medium text-xs truncate">{event.title}</div>
      <div className="text-xs opacity-75">
        {moment(event.start).format('HH:mm')}
      </div>
    </div>
  );
};

// Custom date cell component with show more functionality
const CustomDateCell = ({ value, children }: { value: Date; children: React.ReactNode }) => {
  return (
    <div className="rbc-date-cell">
      {children}
    </div>
  );
};

// Custom show more component
const CustomShowMore = ({
  events,
  date,
  onShowMore
}: {
  events: CalendarEvent[];
  date: Date;
  onShowMore: (events: CalendarEvent[], date: Date) => void;
}) => {
  const handleClick = () => {
    onShowMore(events, date);
  };

  return (
    <button
      onClick={handleClick}
      className="text-xs text-blue-600 hover:text-blue-800 font-medium bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded transition-colors w-full text-left"
    >
      +{events.length} de plus
    </button>
  );
};

const EnhancedMonthView: React.FC<EnhancedMonthViewProps> = ({
  currentDate,
  events,
  onTimeSlotClick,
  onEventClick,
  onDateChange,
  onNavigate
}) => {
  const [selectedRoom, setSelectedRoom] = useState<string>('all');
  const [showStats, setShowStats] = useState(true);
  const [showMoreModal, setShowMoreModal] = useState<{
    isOpen: boolean;
    events: CalendarEvent[];
    date: Date;
  }>({
    isOpen: false,
    events: [],
    date: new Date()
  });

  // Room configuration
  const rooms = useMemo(() => [
    { id: 'room-a', name: 'Salle A', color: '#3b82f6' },
    { id: 'room-b', name: 'Salle B', color: '#10b981' },
  ], []);

  // Filter events by selected room and current month
  const filteredEvents = useMemo(() => {
    const monthStart = moment(currentDate).startOf('month');
    const monthEnd = moment(currentDate).endOf('month');

    let filtered = events.filter(event => {
      const eventMoment = moment(event.start);
      return eventMoment.isBetween(monthStart, monthEnd, 'day', '[]');
    });

    // Filter by room if not 'all'
    if (selectedRoom !== 'all') {
      filtered = filtered.filter(event => event.roomId === selectedRoom);
    }

    return filtered;
  }, [events, currentDate, selectedRoom]);

  // Convert events to react-big-calendar format
  const calendarEvents = useMemo(() => {
    return filteredEvents.map(event => ({
      ...event,
      start: event.start,
      end: event.end || new Date(event.start.getTime() + (event.duration || 30) * 60000),
      resource: event.roomId,
    }));
  }, [filteredEvents]);

  // Event style getter
  const eventStyleGetter = useCallback((event: CalendarEvent) => {
    const room = rooms.find(r => r.id === event.roomId);
    return {
      style: {
        backgroundColor: event.color || room?.color || '#3b82f6',
        borderRadius: '3px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '10px',
        padding: '1px 3px',
        margin: '1px 0',
      }
    };
  }, [rooms]);

  // Handle slot selection (day click)
  const handleSelectSlot = useCallback((slotInfo: { start: Date; end: Date; slots: Date[] }) => {
    const { start } = slotInfo;
    // For month view, default to 9 AM when clicking on a day
    const hour = 9;
    const minute = 0;
    const roomId = selectedRoom !== 'all' ? selectedRoom : 'room-a';
    
    onTimeSlotClick(start, hour, minute, roomId);
  }, [onTimeSlotClick, selectedRoom]);

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    onEventClick(event);
  }, [onEventClick]);

  // Handle navigation
  const handleNavigate = useCallback((newDate: Date) => {
    if (onDateChange) {
      onDateChange(newDate);
    }
  }, [onDateChange]);

  // Handle show more
  const handleShowMore = useCallback((events: CalendarEvent[], date: Date) => {
    setShowMoreModal({
      isOpen: true,
      events,
      date
    });
  }, []);

  // Close show more modal
  const closeShowMoreModal = useCallback(() => {
    setShowMoreModal({
      isOpen: false,
      events: [],
      date: new Date()
    });
  }, []);

  // Custom formats
  const formats = {
    monthHeaderFormat: 'MMMM YYYY',
    dayHeaderFormat: 'dddd',
    dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('MMMM YYYY')}`;
    },
    eventTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('HH:mm')}`;
    },
  };

  // Calculate month statistics
  const monthStats = useMemo(() => {
    const monthStart = moment(currentDate).startOf('month');
    const monthEnd = moment(currentDate).endOf('month');
    
    const monthEvents = events.filter(event => {
      const eventMoment = moment(event.start);
      return eventMoment.isBetween(monthStart, monthEnd, 'day', '[]');
    });

    // Group by week
    const weeklyStats = [];
    let currentWeek = moment(monthStart).startOf('week');
    
    while (currentWeek.isBefore(monthEnd)) {
      const weekEnd = moment(currentWeek).endOf('week');
      const weekEvents = monthEvents.filter(event => {
        const eventMoment = moment(event.start);
        return eventMoment.isBetween(currentWeek, weekEnd, 'day', '[]');
      });
      
      weeklyStats.push({
        week: `${currentWeek.format('DD/MM')} - ${weekEnd.format('DD/MM')}`,
        count: weekEvents.length
      });
      
      currentWeek.add(1, 'week');
    }

    // Group by room
    const roomStats = rooms.map(room => ({
      ...room,
      count: monthEvents.filter(event => event.roomId === room.id).length
    }));

    return {
      total: monthEvents.length,
      filtered: filteredEvents.length,
      weekly: weeklyStats,
      rooms: roomStats,
      averagePerDay: Math.round(monthEvents.length / monthStart.daysInMonth() * 10) / 10
    };
  }, [events, currentDate, filteredEvents.length, rooms]);

  return (
    <div className="enhanced-month-view h-full flex flex-col">
      {/* Header Controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-800">
            {moment(currentDate).format('MMMM YYYY')}
          </h3>
          
          {/* Room Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Salle:</span>
            <div className="flex gap-1">
              <button
                onClick={() => setSelectedRoom('all')}
                className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                  selectedRoom === 'all'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Toutes
              </button>
              {rooms.map(room => (
                <button
                  key={room.id}
                  onClick={() => setSelectedRoom(room.id)}
                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                    selectedRoom === room.id
                      ? 'text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  style={{
                    backgroundColor: selectedRoom === room.id ? room.color : undefined
                  }}
                >
                  {room.name}
                </button>
              ))}
            </div>
          </div>

          {/* Stats Toggle */}
          <button
            onClick={() => setShowStats(!showStats)}
            className="px-2 py-1 rounded text-xs bg-gray-200 text-gray-700 hover:bg-gray-300"
          >
            {showStats ? 'Masquer stats' : 'Afficher stats'}
          </button>
        </div>

        {/* Month Stats */}
        <div className="flex items-center gap-4 text-sm">
          <div className="text-gray-600">
            <span className="font-medium">{filteredEvents.length}</span> rendez-vous ce mois
          </div>
          <div className="text-gray-500">
            Moyenne: {monthStats.averagePerDay}/jour
          </div>
        </div>
      </div>

      {/* Statistics Panel */}
      {showStats && (
        <div className="p-3 bg-blue-50 border-b">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            {/* Room Statistics */}
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Par salle</h4>
              <div className="space-y-1">
                {monthStats.rooms.map(room => (
                  <div key={room.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: room.color }}
                      ></div>
                      <span className="text-blue-800">{room.name}</span>
                    </div>
                    <span className="font-medium text-blue-900">{room.count}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Weekly Statistics */}
            <div className="md:col-span-2">
              <h4 className="font-medium text-blue-900 mb-2">Par semaine</h4>
              <div className="grid grid-cols-2 gap-2">
                {monthStats.weekly.map((week, index) => (
                  <div key={index} className="flex justify-between text-blue-800">
                    <span>{week.week}</span>
                    <span className="font-medium">{week.count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar */}
      <div className="flex-1 p-4">
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          defaultView="month"
          view="month"
          views={['month']}
          date={currentDate}
          onNavigate={handleNavigate}
          formats={formats}
          eventPropGetter={eventStyleGetter}
          onSelectSlot={handleSelectSlot}
          onSelectEvent={handleSelectEvent}
          selectable
          popup
          popupOffset={30}
          components={{
            event: CustomMonthEvent,
            dateCellWrapper: CustomDateCell,
            month: {
              showMore: (props: any) => (
                <CustomShowMore
                  events={props.events}
                  date={props.date}
                  onShowMore={handleShowMore}
                />
              )
            }
          }}
          messages={{
            allDay: 'Toute la journée',
            previous: 'Précédent',
            next: 'Suivant',
            today: "Aujourd'hui",
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour',
            agenda: 'Agenda',
            date: 'Date',
            time: 'Heure',
            event: 'Événement',
            noEventsInRange: 'Aucun rendez-vous ce mois.',
            showMore: (total: number) => `+${total} de plus`,
          }}
          className="rbc-calendar-enhanced"
        />
      </div>

      {/* Debug Info */}
      <div className="p-2 bg-blue-50 border-t text-xs text-blue-700">
        <strong>🔍 Enhanced Month View:</strong><br/>
        Mois: {moment(currentDate).format('MMMM YYYY')} |
        Total events: {events.length} | Filtered: {filteredEvents.length} | Room: {selectedRoom}
      </div>

      {/* Show More Modal */}
      {showMoreModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-96 overflow-hidden">
            <div className="p-4 border-b bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">
                  Rendez-vous du {moment(showMoreModal.date).format('DD MMMM YYYY')}
                </h3>
                <button
                  onClick={closeShowMoreModal}
                  className="text-gray-400 hover:text-gray-600 text-xl font-bold"
                >
                  ×
                </button>
              </div>
            </div>
            <div className="p-4 overflow-y-auto max-h-80">
              <div className="space-y-2">
                {showMoreModal.events.map((event, index) => (
                  <div
                    key={`${event.id}-${index}`}
                    onClick={() => {
                      onEventClick(event);
                      closeShowMoreModal();
                    }}
                    className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: event.color || '#3b82f6' }}
                      ></div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm text-gray-900 truncate">
                          {event.title}
                        </div>
                        <div className="text-xs text-gray-500">
                          {moment(event.start).format('HH:mm')} • {event.duration}min
                        </div>
                        {event.desc && (
                          <div className="text-xs text-gray-400 truncate mt-1">
                            {event.desc}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="p-3 border-t bg-gray-50 text-center">
              <div className="text-sm text-gray-600">
                {showMoreModal.events.length} rendez-vous au total
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedMonthView;
