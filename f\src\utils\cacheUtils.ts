/**
 * Cache utilities for managing doctor-specific data
 */

import { STORAGE_KEYS } from '@/utils/mockDataStorage';

// Define types for user data
interface UserData {
  email: string;
  id?: string;
  [key: string]: unknown;
}

/**
 * Clear all cache data for a specific doctor
 */
export function clearDoctorCache(doctorId: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear doctor-specific cache keys
    const doctorSpecificKeys = [
      `${STORAGE_KEYS.REAL_USER_ACCOUNTS}_${doctorId}`,
      `${STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS}_${doctorId}`,
      `doctor_settings_${doctorId}`,
      `fontSizePreference_${doctorId}`,
    ];

    doctorSpecificKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    console.log('Cleared cache for doctor:', doctorId);
  } catch (error) {
    console.warn('Error clearing doctor cache:', error);
  }
}

/**
 * Clear all cached data (useful when switching doctors)
 */
export function clearAllCache(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear all cache-related keys
    const cacheKeys = [
      STORAGE_KEYS.REAL_USER_ACCOUNTS,
      STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS,
      STORAGE_KEYS.ASSISTANT_ACCOUNTS,
      'mock_subscription_packages',
      'fontSizePreference',
    ];

    cacheKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    // Clear all doctor-specific keys (pattern-based)
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('doctor_settings_') ||
        key.includes('_user_accounts_') ||
        key.includes('_cache_timestamp_')
      )) {
        localStorage.removeItem(key);
      }
    }

    console.log('Cleared all cache data');
  } catch (error) {
    console.warn('Error clearing all cache:', error);
  }
}

/**
 * Clear cache when user logs out or switches accounts
 */
export function clearUserCache(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear user-specific data
    const userKeys = [
      'userId',
      'userType',
      'token',
      'refreshToken',
    ];

    userKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    // Also clear all cache data
    clearAllCache();

    console.log('Cleared user cache and all cached data');
  } catch (error) {
    console.warn('Error clearing user cache:', error);
  }
}

/**
 * Remove specific user from stored mock data (useful for cleaning up test data)
 */
export function removeUserFromMockData(email: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Get current mock data
    const storedData = localStorage.getItem(STORAGE_KEYS.ASSISTANT_ACCOUNTS);
    if (storedData) {
      const accounts: UserData[] = JSON.parse(storedData) as UserData[];

      // Filter out the user with the specified email
      const filteredAccounts = accounts.filter((user: UserData) => user.email !== email);

      // Save the filtered data back
      localStorage.setItem(STORAGE_KEYS.ASSISTANT_ACCOUNTS, JSON.stringify(filteredAccounts));

      console.log(`Removed user with email ${email} from mock data`);
    }
  } catch (error) {
    console.warn('Error removing user from mock data:', error);
  }
}

/**
 * Get cache key specific to current doctor
 */
export function getDoctorSpecificCacheKey(baseKey: string): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const doctorId = localStorage.getItem('userId');
  if (!doctorId) {
    return null;
  }

  return `${baseKey}_${doctorId}`;
}

/**
 * Check if current user is a doctor
 */
export function isCurrentUserDoctor(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  const userType = localStorage.getItem('userType');
  return userType === 'doctor';
}

/**
 * Get current doctor ID
 */
export function getCurrentDoctorId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const userType = localStorage.getItem('userType');
  if (userType !== 'doctor') {
    return null;
  }

  return localStorage.getItem('userId');
}
