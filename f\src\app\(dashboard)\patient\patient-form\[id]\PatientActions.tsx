import { Button, Group, Tooltip, ActionIcon,  } from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiTooth,
  mdiCalendarPlus,
  mdiContentSave,
  mdiCloudSync,
} from '@mdi/js';
import { useState } from 'react';
import { notifications } from '@mantine/notifications';
import patientService from '@/services/patientService';

type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
  // Django integration props
  enableDjangoSync?: boolean;
  patientData?: Record<string, unknown>;
  onSyncWithDjango?: () => Promise<void>;
  syncLoading?: boolean;
};

export const PatientActions = ({
  patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  enableDjangoSync = false,
  patientData,
  onSyncWithDjango,
  syncLoading = false,
}: PatientActionsProps) => {
  const [loading, setLoading] = useState(false);
  const disabled = isFormInvalid || isDraft || loading || syncLoading;

  const handlePrint = async () => {
    if (onPrint) {
      onPrint();
    } else if (enableDjangoSync && patientId) {
      // Generate and print patient barcode/QR code
      try {
        setLoading(true);
        // TODO: Implement barcode generation from Django patient data
        notifications.show({
          title: 'Print',
          message: 'Barcode printing functionality will be implemented soon',
          color: 'blue',
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to generate barcode',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSyncWithDjango = async () => {
    if (onSyncWithDjango) {
      try {
        await onSyncWithDjango();
        notifications.show({
          title: 'Success',
          message: 'Patient data synchronized with Django',
          color: 'green',
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to sync with Django',
          color: 'red',
        });
      }
    }
  };

  const handleSaveAndSubmit = async () => {
    if (enableDjangoSync && patientId && patientData) {
      try {
        setLoading(true);

        // Save to Django before submitting
        const result = await patientService.updatePatientDetail(patientId, patientData);
        if (result) {
          notifications.show({
            title: 'Success',
            message: 'Patient data saved to Django',
            color: 'green',
          });

          // Call original submit handler
          if (onSubmit) {
            onSubmit();
          }
        }
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to save patient data',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    } else if (onSubmit) {
      onSubmit();
    }
  };

  return (
    <Group justify="space-between" wrap="wrap" mt="md">
      <Group gap="xs">
        {patientId && (
          <>
            <Tooltip label="Print barcode">
                <ActionIcon
                  variant="filled"
                  aria-label="Print barcode"
                  radius="4px"
                  onClick={handlePrint}
                  loading={loading}
                  disabled={disabled}
                >
                  <Icon path={mdiBarcode} size={0.75} style={{ width: '70%', height: '70%' }} />
                </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
            {/* <Button variant="light" size="compact-md" onClick={onPrevious}>
              <Icon path={mdiSkipPrevious} size={1} />
            </Button> */}
  <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
            {/* <Button variant="light" size="compact-md" onClick={onNext}>
              <Icon path={mdiSkipPrevious} size={1} />
            </Button> */}
          </>
        )}
      </Group>

      <Group gap="xs">
        <Tooltip label="Commencer la visite">
          <ActionIcon variant="filled" aria-label="Settings" radius="4px"
          onClick={onStartVisit}
            disabled={disabled}>
          <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
          <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
          Annuler
        </Button>

        {patientId && (
          <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
          >
            Enregistrer & Nouvelle fiche
          </Button>
        )}

        <Button
          variant="filled"
          color="blue"
          onClick={onSaveQuit}
          disabled={isFormInvalid}
        >
          Enregistrer et quitter
        </Button>

        {enableDjangoSync && onSyncWithDjango && (
          <Button
            variant="light"
            color="green"
            onClick={handleSyncWithDjango}
            disabled={disabled}
            loading={syncLoading}
            leftSection={<Icon path={mdiCloudSync} size={0.8} />}
          >
            Sync with Django
          </Button>
        )}

        <Button
          variant="filled"
          color="blue"
          type="submit"
          onClick={handleSaveAndSubmit}
          disabled={disabled}
          loading={loading}
          leftSection={enableDjangoSync ? <Icon path={mdiContentSave} size={0.8} /> : undefined}
        >
          {enableDjangoSync ? 'Save to Django & Submit' : 'Enregistrer la fiche'}
        </Button>
      </Group>
    </Group>
  );
};
