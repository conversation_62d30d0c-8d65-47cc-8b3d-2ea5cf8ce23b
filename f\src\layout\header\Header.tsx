"use client";
//useEffect
import { useState,  } from 'react';
// import { useRouter } from 'next/navigation';
// import { notifications } from '@mantine/notifications';
import Link from 'next/link';
import Image from 'next/image';
import {
  Avatar,
  Group,
  rem,
  Text,
  UnstyledButton,
  Menu,

} from "@mantine/core";
import {
  IconLogout,
  IconMessage,
  IconSettings,
  IconTrash,
  IconChevronDown,
  IconUser,
  IconSearch,
} from "@tabler/icons-react";
//import authService from '~/services/authService';
import { useDisclosure } from '@mantine/hooks';
import classes from '~/styles/layout.module.css';
import {   Burger, } from '@mantine/core';

interface HeaderProps {
    toggleSidebar: () => void;
  }
  const headerLinks = [
    { link: '/about', label: 'Features' },
    { link: '/pricing', label: 'Pricing' },
    { link: '/learn', label: 'Learn' },
    { link: '/community', label: 'Community' },
  ];
  const items = headerLinks.map((link) => (
    <a
      key={link.label}
      href={link.link}
      className={classes.link_H}
    >
      {link.label}
    </a>
  ));
const Header = ({ toggleSidebar }: HeaderProps) => {
  //setIsAuthenticated
  const [isAuthenticated, ] = useState(false);
  const [userProfile, setUserProfile] = useState<{
    first_name?: string;
    firstName?: string;
    last_name?: string;
    lastName?: string;
    profile_image?: string;
    email?: string;
    user_type?: string;
  } | null>(null);
  // const router = useRouter();
  // useEffect(() => {
  //   const fetchDashboardData = async () => {
  //     try {
  //       // Check if user is authenticated using the validateAuthentication method
  //       const isAuth = await authService.validateAuthentication();
  //       setIsAuthenticated(isAuth);

  //       if (!isAuth) {
  //         router.push('/login');
  //         return;
  //       }

  //       // Fetch user profile
  //       try {
  //         const userProfileData = await authService.getProfile();
  //         setUserProfile(userProfileData);
  //       } catch (profileError) {
  //         console.error('Error fetching user profile:', profileError);
  //         // Set default user
  //         const defaultUser = {
  //           first_name: 'Doctor',
  //           last_name: '',
  //           email: '<EMAIL>',
  //           user_type: 'doctor'
  //         };
  //         setUserProfile(defaultUser);
  //       }

  //       // Fetch today's appointments
  //       const today = new Date();
  //       // Fetch upcoming appointments (excluding today)
  //       const tomorrow = new Date(today);
  //       tomorrow.setDate(tomorrow.getDate() + 1);
  //     } catch (error) {
  //       console.error('Error fetching dashboard data:', error);
  //       notifications.show({
  //         title: 'Error',
  //         message: 'Failed to load dashboard data',
  //         color: 'red',
  //       });
  //     } finally {
  //       // Loading finished
  //     }
  //   };

  //   fetchDashboardData();
  // }, [router]);
  // const handleLogout = async () => {
  //   try {
  //     await authService.logout();
  //     setIsAuthenticated(false);
  //     setUserProfile(null);
  //     // Redirect to the login page instead of the home page
  //     router.push('/login');
  //   } catch (error) {
  //     console.error('Error logging out:', error);
  //     // Even if there's an error, still try to redirect to login
  //     router.push('/login');
  //   }
  // };
  const getUserInitials = () => {
    if (!userProfile) return '';

    const firstName = userProfile.first_name || userProfile.firstName || '';
    const lastName = userProfile.last_name || userProfile.lastName || '';

    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };

  // Get user display name
  const getUserDisplayName = () => {
    if (!userProfile) return '';

    const firstName = userProfile.first_name || userProfile.firstName || '';
    const lastName = userProfile.last_name || userProfile.lastName || '';

    return `${firstName} ${lastName}`;
  };
  // Handle image loading errors
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('Error loading profile image:', e);
    // Set the src to null to trigger the fallback to initials
    e.currentTarget.src = '';
    // Force a re-render to show initials instead
    if (userProfile) {
      const updatedProfile = { ...userProfile };
      updatedProfile.profile_image = '';
      setUserProfile(updatedProfile);
    }
  };

    const [sidebarVisible] = useState(false);
    const [opened, { toggle }] = useDisclosure(false);
  return (
    <header className={`${classes.header} ${sidebarVisible ? classes.headerWithSidebar : classes.headerWithoutSidebar}`}
    style={{marginLeft: "auto"}}>
    <div className={`${classes.inner} `}>

<Group justify="space-between" w={"100%"}>
        <Burger
      opened={opened}
      onClick={() => {
        toggle();
        toggleSidebar();
      }}
      size="sm"
      hiddenFrom="sm"
    />
      <div className={classes.logo}>
        <Link href="/">
          <Image src="/logo.png" alt="Logo" width={120} height={40} priority />
        </Link>
      </div>
     {isAuthenticated ? (
    <Menu
      width={260}
      position="bottom-end"
      transitionProps={{ transition: "pop-top-right" }}
      withinPortal
      shadow="lg"
      zIndex={1000010}
      withArrow
    >
      <Menu.Target>
        <UnstyledButton
        // className={cx(classes.user, { [classes.userActive]: userMenuOpened })}
        >
          <Group gap={7} pl={"3px"}>
          {userProfile?.profile_image ? (

                        <Avatar
                          src={userProfile.profile_image}
                          radius="xl"
                          onError={handleImageError}
                          size={20}
                        />
                      ) : (
                        <Avatar color="blue" radius="xl">{getUserInitials()}</Avatar>
                      )}
                       <Text size="sm" fw={500}>
                           {getUserDisplayName().slice(0, 12)}

                          </Text>
            <IconChevronDown
              style={{ width: rem(12), height: rem(12) }}
              stroke={1.5}
            />
          </Group>
        </UnstyledButton>
      </Menu.Target>
      <Menu.Dropdown style={{top: "46.6406px"}}>

      <Menu.Item
                      leftSection={<IconUser style={{ width: rem(16), height: rem(16) }} />}
                      component={Link}
                      href="/profile"
                    >
                      My Profile
                    </Menu.Item>

                    <Menu.Item
                      leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
                      component={Link}
                      href="/settings"
                    >
                      Settings
                    </Menu.Item>

                    <Menu.Item
                      leftSection={<IconMessage style={{ width: rem(16), height: rem(16) }} />}
                      component={Link}
                      href="/messages"
                    >
                      Messages
                    </Menu.Item>

                    <Menu.Divider />
                    <Menu.Item
                      leftSection={<IconSearch size={14} />}
                      rightSection={
                        <Text size="xs" c="dimmed">
                          ⌘K
                        </Text>
                      }
                    >
                      Search
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<IconTrash style={{ width: rem(16), height: rem(16) }} color="red" />}
                      color="red"
                      component={Link}
                      href="/delete-account"
                    >
                      Delete Account
                    </Menu.Item>

                    <Menu.Item
                      leftSection={<IconLogout style={{ width: rem(16), height: rem(16) }} />}
                      //onClick={handleLogout}
                    >
                      Logout
                    </Menu.Item>


        <Menu.Divider />


      </Menu.Dropdown>
    </Menu>
  ) : ( null)}
    </Group>
      <Group>
      <Group
        ml={50}
        gap={5}
        className={classes.links}
        visibleFrom="sm"
      >
        {items}
      </Group>
      </Group>
    </div>
    {/* Mobile menu */}
    {opened && (
      <div className={classes.mobileMenu}>
        <div className={classes.mobileLinks}>
          {items}
          {isAuthenticated && (
            <Link href="/profile" className={classes.link_H}>Profile</Link>
          )}
          {isAuthenticated && (
            <a href="#" className={classes.link_H} onClick={(e) => {
              e.preventDefault();
            //  handleLogout();
            }}>Logout</a>
          )}
        </div>
      </div>
    )}
  </header>
  )
}

export default Header;