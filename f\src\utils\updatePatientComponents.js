// Utility script to update patient components with backend integration
// This shows the pattern that needs to be applied to each component

const componentUpdates = {
  'Archives.tsx': {
    status: 'archived',
    loadingMessage: 'Chargement des patients archivés...',
    emptyMessage: 'Aucun patient archivé trouvé'
  },
  'Incomplets.tsx': {
    status: 'incomplete', 
    loadingMessage: 'Chargement des patients incomplets...',
    emptyMessage: 'Aucun patient incomplet trouvé'
  },
  'SansVisite.tsx': {
    status: 'no_visit',
    loadingMessage: 'Chargement des patients sans visite...',
    emptyMessage: 'Aucun patient sans visite trouvé'
  }
};

// Pattern for imports to add:
const importsToAdd = `
import { Loader, Center } from '@mantine/core';
import { usePatients } from '@/hooks/usePatients';
`;

// Pattern for hook usage:
const hookPattern = (status) => `
const {
  patients,
  loading,
  error,
  totalCount,
  currentPage,
  itemsPerPage,
  totalPages,
  fetchPatientsByStatus,
  searchPatients,
  setPage,
  setItemsPerPage,
  refreshPatients
} = usePatients({ status: '${status}' });
`;

// Pattern for data conversion:
const dataConversionPattern = (status) => `
// Convert Patient data to VisiteData format for compatibility
const visitesData: VisiteData[] = loading ? [] : patients.map((patient, index) => ({
  id: parseInt(patient.id) || index + 1,
  date: patient.created_at ? new Date(patient.created_at).toLocaleDateString('fr-FR') : '',
  nom: patient.last_name || '',
  prenom: patient.first_name || '',
  dateNaissance: patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString('fr-FR') : '',
  age: patient.age?.toString() || '',
  cin: patient.national_id_number || '',
  telephone: patient.phone_number || '',
  ville: patient.address || '',
  assurance: patient.insurance_company || '',
}));

// Handle search with backend
const handleSearch = (term: string) => {
  setSearchTerm(term);
  if (term.trim()) {
    searchPatients({ search: term, status: '${status}' });
  } else {
    fetchPatientsByStatus('${status}');
  }
};
`;

// Pattern for loading states:
const loadingStatePattern = (loadingMessage, emptyMessage) => `
{loading ? (
  <Table.Tr>
    <Table.Td colSpan={10} className="text-center py-8">
      <Center>
        <Loader size="md" />
        <Text ml="md">${loadingMessage}</Text>
      </Center>
    </Table.Td>
  </Table.Tr>
) : error ? (
  <Table.Tr>
    <Table.Td colSpan={10} className="text-center py-8">
      <Text c="red">Erreur: {error}</Text>
    </Table.Td>
  </Table.Tr>
) : currentVisites.length === 0 ? (
  <Table.Tr>
    <Table.Td colSpan={10} className="text-center py-8">
      <Text c="dimmed">${emptyMessage}</Text>
    </Table.Td>
  </Table.Tr>
) : currentVisites.map((visite) => (
`;

// Pattern for pagination fixes:
const paginationFixes = `
// Replace setCurrentPage with setPage
onChange={(value) => setPage(Number(value))}

// Replace endIndex calculation with totalCount
{((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalCount)} de {totalCount}

// Replace onChange={setCurrentPage} with onChange={setPage}
onChange={setPage}

// Replace manual pagination with hook pagination
const currentVisites = sortedVisites;
`;

console.log('Patient Component Update Patterns Generated');
console.log('Apply these patterns to each component:', Object.keys(componentUpdates));
