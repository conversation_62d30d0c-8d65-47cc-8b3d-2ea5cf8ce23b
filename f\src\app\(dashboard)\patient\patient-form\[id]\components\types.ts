// Types partagés pour les composants de dictionnaire

export interface TreeNode {
  uid: string;
  value: string;
  nodes?: TreeNode[];
}

export interface TreeItemChoixMultipleProps {
  node: TreeNode;
  collapsedNodes: Record<string, boolean>;
  toggleNodeCollapse: (nodeId: string) => void;
  selectedNodes: Set<string>;
  toggleNodeSelection: (nodeId: string) => void;
}

export interface SavedModel {
  id: string;
  title: string;
  selections: string[];
  selected?: boolean;
}
