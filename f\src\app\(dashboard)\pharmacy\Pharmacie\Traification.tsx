'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  Table,
  Text,
  NumberInput,
  ScrollArea,
  Tabs,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import Icon from '@mdi/react';
import { mdiCurrencyUsd } from '@mdi/js';
import {
  
  IconPlus,
  IconDeviceFloppy,
  IconTag,
} from '@tabler/icons-react';

// TypeScript interfaces
interface TarifType {
  id: string;
  typeTarif: string;
  regle: string;
}

interface ArticleProcedure {
  id: string;
  article: string;
  procedure: string;
}

interface TarificationData {
  nom: string;
  typeTarifAAppliquer: string;
  valeur: number;
  tarifTypes: TarifType[];
  articlesProcedures: ArticleProcedure[];
}

const Traification = () => {
  const [activeTab, setActiveTab] = useState<string>('tarifs');
  const [currentTarification, ] = useState<TarificationData>({
    nom: '',
    typeTarifAAppliquer: 'Montant',
    valeur: 0.00,
    tarifTypes: [],
    articlesProcedures: [],
  });

  const form = useForm({
    initialValues: currentTarification,
  });

  const typeTarifOptions = [
    'Montant',
    'Pourcentage',
    'Fixe',
    'Variable',
  ];

  const handleSave = () => {
    notifications.show({
      title: 'Tarification enregistrée',
      message: 'La configuration de tarification a été enregistrée avec succès',
      color: 'green',
    });
  };

  const handleAddTarif = () => {
    notifications.show({
      title: 'Tarif ajouté',
      message: 'Un nouveau tarif a été ajouté',
      color: 'blue',
    });
  };

  const handleAddArticle = () => {
    notifications.show({
      title: 'Article ajouté',
      message: 'Un nouvel article a été ajouté',
      color: 'blue',
    });
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Header */}
      <div className="w-full">
        <Paper p="md" mb="md" withBorder className="bg-slate-600">
          <Group justify="space-between" align="center">
            <Group align="center">
              <Icon path={mdiCurrencyUsd} size={1} className="text-slate-600" />
              <Title order={3} className="text-slate-600">
                Tarification
              </Title>
            </Group>
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              leftSection={<IconPlus size={16} />}
              onClick={handleAddTarif}
            >
              Ajouter
            </Button>
          </Group>
        </Paper>

        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'tarifs')} orientation="vertical">
          <div className="flex">
            {/* Left Sidebar */}
            <div className="w-48 bg-white border-r border-gray-200">
              <Tabs.List>
                <Tabs.Tab value="tarifs" leftSection={<IconTag size={16} />} className="w-full justify-start">
                  Tarifs
                </Tabs.Tab>
              </Tabs.List>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 p-6">
              <Tabs.Panel value="tarifs">
              {/* Form Section */}
              <Paper p="md" mb="md" withBorder>
                <form onSubmit={form.onSubmit(() => {})}>
                  <Grid mb="md">
                    <Grid.Col span={12}>
                      <TextInput
                        label="Nom *"
                        placeholder=""
                        {...form.getInputProps('nom')}
                        required
                        styles={{
                          label: { color: '#ff6b6b' }
                        }}
                      />
                    </Grid.Col>
                  </Grid>

                  <Grid mb="md">
                    <Grid.Col span={6}>
                      <Select
                        label="Type de tarif à appliquer"
                        placeholder="Montant"
                        data={typeTarifOptions}
                        {...form.getInputProps('typeTarifAAppliquer')}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Valeur"
                        placeholder="0,00"
                        {...form.getInputProps('valeur')}
                        decimalScale={2}
                        fixedDecimalScale
                      />
                    </Grid.Col>
                  </Grid>

                  <Group justify="flex-end" mb="md">
                    <Button
                      variant="filled"
                      className="bg-blue-500 hover:bg-blue-600"
                      leftSection={<IconPlus size={16} />}
                      onClick={handleAddTarif}
                    >
                      Tarif
                    </Button>
                  </Group>
                </form>
              </Paper>

              {/* Type de tarif Table */}
              <Paper p="md" mb="md" withBorder>
                <Group justify="space-between" align="center" mb="md">
                  <Text size="lg" fw={500} className="text-blue-600">
                    Type de tarif
                  </Text>
                  <Text size="lg" fw={500} className="text-blue-600">
                    Règle
                  </Text>
                </Group>

                <ScrollArea>
                  <Table striped highlightOnHover>
                    <Table.Tbody>
                      {currentTarification.tarifTypes.length === 0 ? (
                        <Table.Tr>
                          <Table.Td colSpan={2} className="text-center text-gray-500 py-8">
                            Aucun élément trouvé
                          </Table.Td>
                        </Table.Tr>
                      ) : (
                        currentTarification.tarifTypes.map((tarif) => (
                          <Table.Tr key={tarif.id}>
                            <Table.Td>{tarif.typeTarif}</Table.Td>
                            <Table.Td>{tarif.regle}</Table.Td>
                          </Table.Tr>
                        ))
                      )}
                    </Table.Tbody>
                  </Table>
                </ScrollArea>
              </Paper>

              {/* Article/Procédure Section */}
              <Paper p="md" mb="md" withBorder>
                <Group justify="space-between" align="center" mb="md">
                  <Text size="lg" fw={500} className="text-blue-600">
                    Article/Procédure
                  </Text>
                  <Button
                    variant="filled"
                    className="bg-blue-500 hover:bg-blue-600"
                    leftSection={<IconPlus size={16} />}
                    onClick={handleAddArticle}
                  >
                    Article
                  </Button>
                </Group>

                <ScrollArea>
                  <Table striped highlightOnHover>
                    <Table.Tbody>
                      {currentTarification.articlesProcedures.length === 0 ? (
                        <Table.Tr>
                          <Table.Td colSpan={2} className="text-center text-gray-500 py-8">
                            Aucun élément trouvé
                          </Table.Td>
                        </Table.Tr>
                      ) : (
                        currentTarification.articlesProcedures.map((item) => (
                          <Table.Tr key={item.id}>
                            <Table.Td>{item.article}</Table.Td>
                            <Table.Td>{item.procedure}</Table.Td>
                          </Table.Tr>
                        ))
                      )}
                    </Table.Tbody>
                  </Table>
                </ScrollArea>

                {/* Pagination */}
                <Group justify="space-between" mt="md">
                  <Group>
                    <Text size="sm">Page</Text>
                    <Select
                      size="sm"
                      w={60}
                      data={['1']}
                      value="1"
                    />
                    <Text size="sm">Lignes par Page</Text>
                    <Select
                      size="sm"
                      w={80}
                      data={['10', '25', '50', '100']}
                      value="10"
                    />
                    <Text size="sm">0 - 0 de 0</Text>
                  </Group>
                  <Group>
                    <Button size="xs" variant="outline">K</Button>
                    <Button size="xs" variant="outline">‹</Button>
                    <Button size="xs" variant="outline">›</Button>
                    <Button size="xs" variant="outline">⟩</Button>
                  </Group>
                </Group>
              </Paper>

              {/* Save Button */}
              <Group justify="flex-end" mt="md">
                <Button
                  variant="filled"
                  color="gray"
                  onClick={handleSave}
                  leftSection={<IconDeviceFloppy size={16} />}
                >
                  Enregistrer
                </Button>
              </Group>
              </Tabs.Panel>
            </div>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default Traification;
