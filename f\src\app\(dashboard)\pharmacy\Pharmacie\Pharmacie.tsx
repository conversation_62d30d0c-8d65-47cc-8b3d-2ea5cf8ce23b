import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import ListeeDesFamilles from "./ListeeDesFamilles"
import Fiche_Article from "./Fiche_Article"
import Listedesfournisseurs from "./Listedesfournisseurs"
import Traification from "./Traification"
import Listdesaffaires from "./Listdesaffaires"
import Mouvement from "./Mouvement"
import TransformationN from "./TransformationN"
import ListEchangeInterDepots from "./List-Echange-inter-depots"
import ListeDepots from "./ListeDepots"

// Mapping des sous-onglets pour Pharmacie
const subtabMapping: { [key: string]: string } = {
  'liste-familles': 'ListeeDesFamilles',
  'liste-articles': 'Fiche_Article',
  'liste-fournisseurs': 'Listedesfournisseurs',
  'tarification': 'Traification',
  'liste-affaires': 'Listdesaffaires',
  'mouvements-stock': 'Mouvement',
  'transformations': 'TransformationN',
  'echange-inter-depots': 'Echangeinter-depots',
  'liste-depots': 'ListeDepots'
};

const Pharmacie = () => {
  const [activeTab, setActiveTab] = useState('ListeeDesFamilles');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
     <Tabs
       variant="outline"
       radius="md"
       orientation="vertical"
       value={activeTab}
       onChange={(value) => setActiveTab(value || 'ListeeDesFamilles')}
       w={"100%"}
       mt={10}
     >
          <Tabs.List>
            <Tabs.Tab value="ListeeDesFamilles" leftSection={<IconPhoto size={12} />}>
              Listee des familles
            </Tabs.Tab>
            <Tabs.Tab value="Fiche_Article" leftSection={<IconMessageCircle size={12} />}>
               Listee des articles
            </Tabs.Tab>
            <Tabs.Tab value="Listedesfournisseurs" leftSection={<IconSettings size={12} />}>
              List des fournisseurs
            </Tabs.Tab>
             <Tabs.Tab value="Traification" leftSection={<IconPhoto size={12} />}>
              Trafication
            </Tabs.Tab>
            <Tabs.Tab value="Listdesaffaires" leftSection={<IconMessageCircle size={12} />}>
              List des affaires
            </Tabs.Tab>
            <Tabs.Tab value="Mouvement" leftSection={<IconSettings size={12} />}>
              Mouvement du stock
            </Tabs.Tab>
             <Tabs.Tab value="TransformationN" leftSection={<IconPhoto size={12} />}>
              Transformayions
            </Tabs.Tab>
            <Tabs.Tab value="Echangeinter-depots" leftSection={<IconMessageCircle size={12} />}>
             Echange inter-depots
            </Tabs.Tab>
            <Tabs.Tab value="ListeDepots" leftSection={<IconSettings size={12} />}>
              Liste des depots
            </Tabs.Tab>
          </Tabs.List>
    
          <Tabs.Panel value="ListeeDesFamilles" ml={20}>
            <ListeeDesFamilles/>
          </Tabs.Panel>
    
          <Tabs.Panel value="Fiche_Article" ml={20}>
            <Fiche_Article/>
          </Tabs.Panel>
    
          <Tabs.Panel value="Listedesfournisseurs" ml={20}>
            <Listedesfournisseurs/>
          </Tabs.Panel>
           <Tabs.Panel value="Traification" ml={20}>
            <Traification/>
          </Tabs.Panel>
    
          <Tabs.Panel value="Listdesaffaires" ml={20}>
            <Listdesaffaires/>
          </Tabs.Panel>
    
          <Tabs.Panel value="Mouvement" ml={20}>
            <Mouvement/>
          </Tabs.Panel>
           <Tabs.Panel value="TransformationN" ml={20}>
            <TransformationN/>
          </Tabs.Panel>
    
          <Tabs.Panel value="Echangeinter-depots" ml={20}>
           <ListEchangeInterDepots/>
          </Tabs.Panel>
    
          <Tabs.Panel value="ListeDepots" ml={20}>
            <ListeDepots/>
          </Tabs.Panel>
        </Tabs>
  )
}

export default Pharmacie
