'use client';

import { useState, useEffect } from 'react';
import {  Title, TextInput, Button, Table, Group, ActionIcon, Text,  Card, Loader, Center } from '@mantine/core';
import { IconSearch, IconEdit, IconTrash, IconEye, IconPlus } from '@tabler/icons-react';
import Link from 'next/link';
import patientService, { Patient } from '@/services/patientService';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import { Modal, } from '@mantine/core';
import SimpleBar from "simplebar-react";
import AddPatientPage from './newpatient'
import Icon from '@mdi/react';
import { mdiAccountBoxEditOutline } from '@mdi/js';
export default function PatientsPage() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);
 const [newpatientopened, { open:opennewpatient, close:closenewpatient }] = useDisclosure(false);
  // Fetch patients on component mount
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        const data = await patientService.getPatients();
        setPatients(data);
      } catch (error: unknown) {
        console.error('Error fetching patients:', error);
        let errorMessage = 'Failed to load patients. Please try again.';

        if (error instanceof Error) {
          errorMessage = error.message;
        }

        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.first_name} ${patient.last_name}`.toLowerCase();
    const searchLower = search.toLowerCase();

    return (
      fullName.includes(searchLower) ||
      patient.email.toLowerCase().includes(searchLower) ||
      (patient.phone_number && patient.phone_number.includes(search))
    );
  });

  const handleDeletePatient = async (id: string) => {
    try {
      // Update the patient status to inactive instead of hard deleting
      const updatedPatient = await patientService.updatePatient(id, {
        is_active: false,
        status: 'inactive'
      });

      if (updatedPatient) {
        // Remove the patient from the local state
        setPatients(patients.filter(patient => patient.id !== id));

        notifications.show({
          title: 'Success',
          message: 'Patient removed successfully',
          color: 'green',
        });
      } else {
        throw new Error('Failed to update patient status');
      }
    } catch (error: unknown) {
      console.error('Error removing patient:', error);
      let errorMessage = 'Failed to remove patient. Please try again.';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    }
  };

  return (
    <>
      <Group justify="space-between" mb="lg" w={"100%"}>
        <Title order={1} className="text-primary-700 ml-10">Liste des patients</Title>
        <Button component={Link} href="/patient/add" leftSection={<IconPlus size={16} />}>
         Ajouter un patient
        </Button>
         <Button variant="default" onClick={opennewpatient}>
      Modification
      </Button>
      </Group>

      <Card withBorder mb="lg" w={"100%"}>
        <Group>
          <TextInput
            placeholder="Search patients..."
            value={search}
            onChange={(event) => setSearch(event.currentTarget.value)}
            className="flex-1"
            leftSection={<IconSearch size={16} />}
          />
        </Group>
      </Card>

      {loading ? (
        <Center py="xl">
          <Loader size="lg" />
        </Center>
      ) : (
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Name</Table.Th>
              <Table.Th>Email</Table.Th>
              <Table.Th>Phone</Table.Th>
              <Table.Th>Date of Birth</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredPatients.length > 0 ? (
              filteredPatients.map((patient) => (
                <Table.Tr key={patient.id}>
                  <Table.Td>{patient.first_name} {patient.last_name}</Table.Td>
                  <Table.Td>{patient.email}</Table.Td>
                  <Table.Td>{patient.phone_number || 'N/A'}</Table.Td>
                  <Table.Td>{patient.date_of_birth || 'N/A'}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon variant="subtle" color="blue" component={Link} href={`/patients/${patient.id}`}>
                        <IconEye size={16} />
                      </ActionIcon>
                      <ActionIcon variant="subtle" color="green" component={Link} href={`/patients/${patient.id}/edit`}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon variant="subtle" color="red" onClick={() => handleDeletePatient(patient.id)}>
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            ) : (
              <Table.Tr>
                <Table.Td colSpan={5}>
                  <Text ta="center" c="dimmed">No patients found</Text>
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>
      )}
      <Modal.Root opened={newpatientopened} onClose={closenewpatient} 
      centered size="70%"
      transitionProps={{ transition: 'fade', duration: 200 }}
      >
    <Modal.Content  className="overflow-y-hidden ">
       <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px",  }}>
            <Modal.Title>
              <Group justify="space-between" gap="sm">
              <Text fw={700} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
               <Icon path={mdiAccountBoxEditOutline} size={1}></Icon>
                Edite patient
              </Text>
              {/* <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
               Add New Patient
              </p> */}
              </Group>
            </Modal.Title>
            <Modal.CloseButton className="mantine-focus-always  hover:text-[#868e96]" style={{color:'white'}}/>
          </Modal.Header>
             <Modal.Body style={{ padding: '0px',}}>
          <div className="py-2 pl-4 h-[600px]">
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
            <div className=" pr-4">
      <AddPatientPage/>
     </div>
       </SimpleBar>
       </div>
       </Modal.Body>
       </Modal.Content>
       
      </Modal.Root>
    </>
  );
}
