'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,

  Textarea,
  Table,

  Modal,
  FileInput,

  Tabs,
  Radio,
  Pagination,
  
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';

import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconList,
  IconFile,
  IconUpload,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  IconCurrencyEuro,
  IconFileInvoice,
  IconReceipt,
  IconX,
} from '@tabler/icons-react';

interface FactureItem {
  id: string;
  code: string;
  designation: string;
  quantite: number;
  prix: number;
  tva: number;
  depot: string;
  montant: number;
}

interface Facture {
  id: string;
  numero: string;
  date: Date | null;
  dateEcheance: Date | null;
  depot: string;
  fournisseur: string;
  modePaiement: string;
  conditionPaiement: string;
  numeroDocument: string;
  affaire: string;
  modePrix: 'HT' | 'TTC';
  type: 'Achat' | 'Charge';
  items: FactureItem[];
  commentaire: string;
  montantHT: number;
  montantTVA: number;
  totaleDesCharges: number;
  montantTTC: number;
  status: 'draft' | 'validated' | 'processed';
  attachments: string[];
}

export default function FacturePage() {
  
  const [currentFacture, setCurrentFacture] = useState<Facture>({
    id: '1',
    numero: '2',
    date: new Date('2022-09-16'),
    dateEcheance: null,
    depot: 'Dépôt 1',
    fournisseur: '',
    modePaiement: '',
    conditionPaiement: '',
    numeroDocument: '',
    affaire: '',
    modePrix: 'HT',
    type: 'Achat',
    items: [],
    commentaire: '',
    montantHT: 0,
    montantTVA: 0,
    totaleDesCharges: 0,
    montantTTC: 0,
    status: 'draft',
    attachments: [],
  });

 
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [viewMode, setViewMode] = useState<'form' | 'list'>('form');

  const form = useForm({
    initialValues: {
      numero: currentFacture.numero,
      date: currentFacture.date,
      dateEcheance: currentFacture.dateEcheance,
      depot: currentFacture.depot,
      fournisseur: currentFacture.fournisseur,
      modePaiement: currentFacture.modePaiement,
      conditionPaiement: currentFacture.conditionPaiement,
      numeroDocument: currentFacture.numeroDocument,
      affaire: currentFacture.affaire,
      modePrix: currentFacture.modePrix,
      type: currentFacture.type,
      commentaire: currentFacture.commentaire,
    },
  });

  const itemForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      quantite: 1,
      prix: 0,
      tva: 20,
      depot: '',
    },
  });

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
    'Dépôt Principal',
    'Dépôt Secondaire',
  ];

  const fournisseurs = [
    'Fournisseur A',
    'Fournisseur B',
    'Fournisseur C',
    'Fournisseur D',
  ];

  const modesPaiement = [
    'Espèces',
    'Chèque',
    'Virement',
    'Carte bancaire',
    'Prélèvement',
  ];

  const conditionsPaiement = [
    'Comptant',
    '30 jours',
    '60 jours',
    '90 jours',
    'Fin de mois',
  ];

  const affaires = [
    'Affaire A',
    'Affaire B',
    'Affaire C',
    'Affaire D',
  ];

  const calculateMontant = (item: Partial<FactureItem>) => {
    const { quantite = 0, prix = 0 } = item;
    return quantite * prix;
  };

  const calculateTotals = () => {
    const montantHT = currentFacture.items.reduce((sum, item) => sum + item.montant, 0);
    const montantTVA = currentFacture.items.reduce((sum, item) => {
      const tvaAmount = item.montant * (item.tva / 100);
      return sum + tvaAmount;
    }, 0);
    const totaleDesCharges = 0; // À calculer selon la logique métier
    const montantTTC = montantHT + montantTVA + totaleDesCharges;

    setCurrentFacture(prev => ({
      ...prev,
      montantHT,
      montantTVA,
      totaleDesCharges,
      montantTTC,
    }));
  };

  const addItem = (values: typeof itemForm.values) => {
    const newItem: FactureItem = {
      id: Date.now().toString(),
      code: values.code,
      designation: values.designation,
      quantite: values.quantite,
      prix: values.prix,
      tva: values.tva,
      depot: values.depot,
      montant: calculateMontant(values),
    };

    setCurrentFacture(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));

    itemForm.reset();
    close();
  };

  const removeItem = (id: string) => {
    setCurrentFacture(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  // const handleSave = (action: 'save' | 'validate' | 'process') => {
  //   const updatedFacture = {
  //     ...currentFacture,
  //     ...form.values,
  //     status: action === 'save' ? 'draft' : action === 'validate' ? 'validated' : 'processed',
  //   };

  //   setCurrentFacture(updatedFacture);
    
  //   const actionText = action === 'save' ? 'enregistrée' : action === 'validate' ? 'validée' : 'traitée';
  //   notifications.show({
  //     title: 'Succès',
  //     message: `Facture ${actionText} avec succès`,
  //     color: 'green',
  //   });
  // };

  React.useEffect(() => {
    calculateTotals();
  }, [currentFacture.items]);

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconFileInvoice size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Facture N°: {currentFacture.numero}
          </Title>
        </Group>
        <Group>
          <Button
            variant={viewMode === 'form' ? 'filled' : 'outline'}
            leftSection={<IconFile size={16} />}
            onClick={() => setViewMode('form')}
          >
            Nouveau
          </Button>
          <Button
            variant={viewMode === 'list' ? 'filled' : 'outline'}
            leftSection={<IconList size={16} />}
            onClick={() => setViewMode('list')}
          >
            Liste des factures
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Facture"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date d'échéanc..."
                placeholder="Date d'échéance"
                {...form.getInputProps('dateEcheance')}
                styles={{
                  input: {
                    borderColor: '#ff6b6b',
                    color: '#ff6b6b',
                  },
                  label: {
                    color: '#ff6b6b',
                  }
                }}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Group>
                <Text size="sm" fw={500} mb="xs">Mode de prix</Text>
                <Radio.Group
                  value={form.values.modePrix}
                  onChange={(value) => form.setFieldValue('modePrix', value as 'HT' | 'TTC')}
                >
                  <Group>
                    <Radio value="HT" label="HT" />
                    <Radio value="TTC" label="TTC" />
                  </Group>
                </Radio.Group>
              </Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Dépôt"
                placeholder="Sélectionner un dépôt"
                data={depots}
                rightSection={<IconX size={16} />}
                {...form.getInputProps('depot')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Fournisseur"
                placeholder="Sélectionner un fournisseur"
                data={fournisseurs}
                rightSection={
                  <Group gap={4}>
                    <ActionIcon variant="subtle" size="sm">
                      <IconSearch size={14} />
                    </ActionIcon>
                    <ActionIcon variant="subtle" size="sm">
                      <IconPlus size={14} />
                    </ActionIcon>
                  </Group>
                }
                {...form.getInputProps('fournisseur')}
                searchable
                required
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Group>
                <Text size="sm" fw={500} mb="xs">Type</Text>
                <Radio.Group
                  value={form.values.type}
                  onChange={(value) => form.setFieldValue('type', value as 'Achat' | 'Charge')}
                >
                  <Group>
                    <Radio value="Achat" label="Achat" />
                    <Radio value="Charge" label="Charge" />
                  </Group>
                </Radio.Group>
              </Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Mode de paiement"
                placeholder="Sélectionner un mode de paiement"
                data={modesPaiement}
                rightSection={<IconPlus size={16} />}
                {...form.getInputProps('modePaiement')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Condition de paiement"
                placeholder="Sélectionner une condition"
                data={conditionsPaiement}
                {...form.getInputProps('conditionPaiement')}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="N° Document"
                placeholder="Numéro de document"
                {...form.getInputProps('numeroDocument')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Affaire"
                placeholder="Sélectionner une affaire"
                data={affaires}
                rightSection={<IconPlus size={16} />}
                {...form.getInputProps('affaire')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs defaultValue="details">
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              <Group justify="space-between" mb="md">
                <Group>
                  <Button leftSection={<IconReceipt size={16} />} color="blue">
                    Bons de réception
                  </Button>
                  <Button leftSection={<IconBarcode size={16} />} color="blue">
                    Code à barres
                  </Button>
                  <Button leftSection={<IconShoppingCart size={16} />} color="blue">
                    Article
                  </Button>
                  <Button leftSection={<IconCurrencyEuro size={16} />} color="blue">
                    Charge
                  </Button>
                  <Button leftSection={<IconMessageCircle size={16} />} color="blue">
                    Commentaire
                  </Button>
                </Group>
              </Group>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix</Table.Th>
                    <Table.Th>Tva</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Montant</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {currentFacture.items.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={8} className="text-center text-gray-500">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    currentFacture.items.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.code}</Table.Td>
                        <Table.Td>{item.designation}</Table.Td>
                        <Table.Td>{item.quantite}</Table.Td>
                        <Table.Td>{item.prix.toFixed(2)} €</Table.Td>
                        <Table.Td>{item.tva}%</Table.Td>
                        <Table.Td>{item.depot}</Table.Td>
                        <Table.Td>{item.montant.toFixed(2)} €</Table.Td>
                        <Table.Td>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeItem(item.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>

              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm">Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['1', '2', '3']}
                    value={currentPage.toString()}
                    onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  />
                  <Text size="sm">Lignes par Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['10', '25', '50']}
                    value={itemsPerPage.toString()}
                    onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
                  />
                  <Text size="sm">0 - 0 de 0</Text>
                </Group>
                <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
              </Group>

              <Button
                leftSection={<IconPlus size={16} />}
                onClick={open}
                mt="md"
              >
                Ajouter un article
              </Button>
            </Tabs.Panel>

            <Tabs.Panel value="pieces" pt="md">
              <FileInput
                label="Ajouter des pièces jointes"
                placeholder="Sélectionner des fichiers"
                leftSection={<IconUpload size={16} />}
                multiple
              />
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Totals */}
          <Grid>
            <Grid.Col span={8}>
              <Textarea
                label="Commentaire"
                placeholder="Commentaire général..."
                rows={3}
                {...form.getInputProps('commentaire')}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Stack>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT HT :</Text>
                  <Text>{currentFacture.montantHT.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TVA :</Text>
                  <Text>{currentFacture.montantTVA.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>TOTALE DES CHARGES :</Text>
                  <Text>{currentFacture.totaleDesCharges.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TTC :</Text>
                  <Text>{currentFacture.montantTTC.toFixed(2)}</Text>
                </Group>
              </Stack>
            </Grid.Col>
          </Grid>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red">
              Annuler
            </Button>
            <Button variant="outline" leftSection={<IconCheck size={16} />}>
              Valider
            </Button>
            <Button color="cyan" leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer et quitter
            </Button>
            <Button leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="lg">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <TextInput
              label="Code"
              placeholder="Code article"
              {...itemForm.getInputProps('code')}
              required
            />
            <TextInput
              label="Désignation"
              placeholder="Désignation"
              {...itemForm.getInputProps('designation')}
              required
            />
            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité"
                  placeholder="1"
                  {...itemForm.getInputProps('quantite')}
                  min={1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Prix"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('prix')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="TVA (%)"
                  placeholder="20"
                  {...itemForm.getInputProps('tva')}
                  min={0}
                  max={100}
                />
              </Grid.Col>
            </Grid>
            <Select
              label="Dépôt"
              placeholder="Sélectionner un dépôt"
              data={depots}
              {...itemForm.getInputProps('depot')}
            />
            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
}
