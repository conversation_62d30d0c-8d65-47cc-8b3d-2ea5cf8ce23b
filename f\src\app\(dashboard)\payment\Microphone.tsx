import React, { useState } from 'react';
import { 
  Modal, 
  TextInput, 
  Button, 
  Group, 
  ActionIcon,
  Stack,
  Box
} from '@mantine/core';
import { 
  IconMicrophone, 
  IconX 
} from '@tabler/icons-react';

interface VoiceInputModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: (text: string) => void;
  onCancel: () => void;
  placeholder?: string;
  confirmText?: string;
  cancelText?: string;
}

export default function VoiceInputModal({
  opened,
  onClose,
  onConfirm,
  onCancel,
  placeholder = "Parlez maintenant.",
  confirmText = "Valider",
  cancelText = "Annuler"
}: VoiceInputModalProps) {
  const [inputValue, setInputValue] = useState('');
  const [isRecording, setIsRecording] = useState(false);

  const handleConfirm = () => {
    onConfirm(inputValue);
    setInputValue('');
    onClose();
  };

  const handleCancel = () => {
    onCancel();
    setInputValue('');
    onClose();
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    // Here you would implement actual voice recording logic
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      withCloseButton={false}
      centered
      size="md"
      padding="lg"
      radius="md"
      styles={{
        content: {
          backgroundColor: '#ffffff',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Stack spacing="md">
        {/* Close button */}
        <Group position="right">
          <ActionIcon 
            variant="subtle" 
            color="gray" 
            onClick={onClose}
            size="sm"
          >
            <IconX size={16} />
          </ActionIcon>
        </Group>

        {/* Input field with microphone */}
        <Box style={{ position: 'relative' }}>
          <TextInput
            placeholder={placeholder}
            value={inputValue}
            onChange={(event) => setInputValue(event.currentTarget.value)}
            size="lg"
            styles={{
              input: {
                paddingRight: '50px',
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '16px',
                color: '#9e9e9e',
                minHeight: '120px',
                padding: '16px',
              },
            }}
            multiline
            minRows={4}
            rightSection={
              <ActionIcon
                variant={isRecording ? "filled" : "subtle"}
                color={isRecording ? "red" : "orange"}
                onClick={toggleRecording}
                style={{
                  position: 'absolute',
                  right: '12px',
                  top: '12px',
                }}
              >
                <IconMicrophone size={20} />
              </ActionIcon>
            }
          />
        </Box>

        {/* Action buttons */}
        <Group position="right" spacing="sm" style={{ marginTop: '16px' }}>
          <Button
            variant="subtle"
            color="red"
            onClick={handleCancel}
            style={{
              color: '#f44336',
              fontWeight: 500,
            }}
          >
            {cancelText}
          </Button>
          <Button
            variant="subtle"
            color="blue"
            onClick={handleConfirm}
            style={{
              color: '#2196f3',
              fontWeight: 500,
            }}
          >
            {confirmText}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}

// Example usage component
export function VoiceInputExample() {
  const [modalOpened, setModalOpened] = useState(false);

  const handleConfirm = (text: string) => {
    console.log('Confirmed text:', text);
  };

  const handleCancel = () => {
    console.log('Cancelled');
  };

  return (
    <>
      <Button onClick={() => setModalOpened(true)}>
        Open Voice Input Modal
      </Button>
      
      <VoiceInputModal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </>
  );
}