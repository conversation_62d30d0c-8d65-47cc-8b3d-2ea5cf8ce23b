
"use client";
import { useState, useEffect } from "react";
import React from "react";
import { Tooltip,} from '@mantine/core';
import { useSearchParams } from "next/navigation";
import Icon from '@mdi/react';
import {  mdiCog,mdiAccountWrench,mdiTune,mdiAccountSettings,mdiFileCog,mdiCogTransfer,mdiCloudCog,mdiTuneVariant,mdiCalendarEdit} from '@mdi/js';
import MetaSeo from"./MetaSeo"
import Parameters_de_base from './parameters_de_base/Parameters_de_base'
import Gestion_des_acteurs from './gestion_des_acteurs/Gestion_des_acteurs'
import PatientPages from './module_patient/PatientPages'
import Configration_de_lapplication from "./configration_de_lapplication/Configration_de_lapplication"
import Facturation_Stock from './facturationStock/Facturation_Stock'
import Maintenance_des_donnees from './maintenance_des_donnees/Maintenance_des_donnees'
import Configration_des_platformes_cloud from './configration_des_platformes_cloud/Configration_des_platformes_cloud'
import Parametrage_du_module_des_specialites from'./parametrage_du_module_des_specialites/Parametrage_du_module_des_specialites'
import Conf_Calendrier from'./Calendrier'
import "~/styles/tab.css";

// Mapping des paramètres URL vers les numéros d'onglets
const tabMapping: { [key: string]: number } = {
  //'general': 1,
  'Parameters_de_base': 1,
  'Gestion_des_acteurs': 2,
  'PatientPages': 3,
  'Configration_de_lapplication': 4,
  'Facturation_Stock': 5,
  'Maintenance_des_donnees': 6,
   'Parametrage_du_module_des_specialites': 7,
  'Configration_des_platformes_cloud': 8,
  'calendrier': 9,
 
};

function  SettingsPage() {
  
  const [toggleState, setToggleState] = useState(1);
  const searchParams = useSearchParams();

  // Effet pour lire les paramètres d'URL et définir l'onglet actif
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);
 

const icons = [
  //  { icon: <CalendarDays style={iconStyle} key="Settings" />, label: "General Settings" },
  {
    icon: (
      <Tooltip label="Parameters de base"  position="bottom">
        <Icon path={mdiAccountWrench} size={1} />
      </Tooltip>
    ),
    label: "Parameters de base",
    key: "Parameters_de_base",
  },
   {
    icon: (
      <Tooltip label="Gestion des acteurs">
        <Icon path={mdiTune} size={1} />
      </Tooltip>
    ),
    label: "Gestion des acteurs",
    key: "Gestion_des_acteurs",
  },
    {
    icon: (
      <Tooltip label="Module patient">
        <Icon path={mdiAccountSettings} size={1} />
      </Tooltip>
    ),
    label: "Module patient",
    key: "PatientPages",
  },
    
     {
    icon: (
      <Tooltip label="Configration de lapplication">
        <Icon path={mdiCog} size={1} />
      </Tooltip>
    ),
    label: "Configration",
    key: "Configration_de_lapplication",
  },
  
    {
    icon: (
      <Tooltip label="Facturation stock">
        <Icon path={mdiFileCog} size={1} />
      </Tooltip>
    ),
    label: "Facturation stock",
    key: "Facturation_Stock",
  },
    
     {
    icon: (
      <Tooltip label="Maintenance des donnees">
        <Icon path={mdiCogTransfer} size={1} />
      </Tooltip>
    ),
    label: "Maintenance",
    key: "Maintenance_des_donnees",
  },
    
    {
    icon: (
      <Tooltip label="Parametrage du module des specialites">
        <Icon path={mdiTuneVariant} size={1} />
      </Tooltip>
    ),
    label: "Parametrage",
    key: "Parametrage_du_module_des_specialites",
  },
    
  {
    icon: (
      <Tooltip label="Parametrage du module des specialites">
        <Icon path={mdiCloudCog} size={1} />
      </Tooltip>
    ),
    label: "Cloud",
    key: "Configration_des_platformes_cloud",
  },
 {
    icon: (
      <Tooltip label="Calendrier">
        <Icon path={mdiCalendarEdit} size={1} />
      </Tooltip>
    ),
    label: "Tableau de bord",
    key: "calendrier",
  },

];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
     case 1:
          //return (<SettingsPage/> )
           return (<Parameters_de_base/> )
        case 2:
          return (<Gestion_des_acteurs/> ) // Profile Settings
        case 3:
          return (<PatientPages/> ) // Module patient
        case 4:
          return (<Configration_de_lapplication/> ) // Security
        case 5:
          return (<Facturation_Stock/> ) // Notifications
        case 6:
          return (<Maintenance_des_donnees/> ) // Privacy
          case 7:
          return (<Parametrage_du_module_des_specialites/>) // Logout
        case 8:
          return (<div className="w-full"><Configration_des_platformes_cloud/></div> ) // Accessibility
         case 9:
          return (<Conf_Calendrier/> ) // Calendrier

        default:
          return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box w-full p-4 [border-width:var(--tab-border)]">
          <div className="w-full">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
    </>
  );
}

export default SettingsPage;

 

 