/**
 * Prescriptions Dashboard Widgets
 * Displays key prescription and medication metrics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconPrescription,
  IconHistory,
  IconRefresh,
  IconAlertTriangle,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconAlertCircle,
  IconUsers,
  IconChartPie,
  IconCalendarDollar,
  IconShieldCheck,
} from '@tabler/icons-react';
import { usePrescriptions } from '@/hooks/usePrescriptions';

interface PrescriptionsWidgetsProps {
  patientId?: string;
  dateRange?: { start: string; end: string };
  compact?: boolean;
  showDrugInteractions?: boolean;
}

const PrescriptionsWidgets: React.FC<PrescriptionsWidgetsProps> = ({
  patientId,
  dateRange,
  compact = false,
  showDrugInteractions = true,
}) => {
  const {
    prescriptions,
    medicationHistory,
    prescriptionRefills,
    drugInteractions,
    analytics,
    loading,
    error,
    getActivePrescriptions,
    getExpiredPrescriptions,
    getPendingRefills,
    getPatientPrescriptionStats,
    getPrescriptionTrends,
  } = usePrescriptions({ 
    patientId, 
    dateRange, 
    autoFetch: true,
    dataTypes: ['prescriptions', 'history', 'refills', 'analytics']
  });

  const patientStats = patientId ? getPatientPrescriptionStats(patientId) : null;
  const prescriptionTrends = getPrescriptionTrends();
  const activePrescriptions = getActivePrescriptions();
  const expiredPrescriptions = getExpiredPrescriptions();
  const pendingRefills = getPendingRefills();

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  const complianceRate = analytics?.patient_compliance.reduce((sum, p) => sum + p.compliance_rate, 0) / 
    (analytics?.patient_compliance.length || 1) || 0;

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Total Prescriptions Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconPrescription size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Prescriptions</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="blue">
            {prescriptions.length}
          </Text>
          <Text size="xs" c="dimmed">
            {activePrescriptions.length} actives
          </Text>
        </Card>

        {/* Active Prescriptions Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="green" size="sm">
                <IconShieldCheck size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Actives</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="green">
            {activePrescriptions.length}
          </Text>
          <Progress 
            value={prescriptions.length > 0 ? (activePrescriptions.length / prescriptions.length) * 100 : 0} 
            size="xs" 
            mt="xs" 
          />
          <Text size="xs" c="dimmed">
            {((activePrescriptions.length / prescriptions.length) * 100).toFixed(1)}% du total
          </Text>
        </Card>

        {/* Pending Refills Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconRefresh size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Renouvellements</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="orange">
            {pendingRefills.length}
          </Text>
          <Text size="xs" c="dimmed">
            {prescriptionRefills.length} total
          </Text>
        </Card>

        {/* Compliance Rate Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconChartPie size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Observance</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ value: complianceRate, color: 'purple' }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {complianceRate.toFixed(1)}%
                </Text>
              }
            />
          </Group>
        </Card>
      </SimpleGrid>

      {!compact && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={3} spacing="md">
            {/* Recent Prescriptions */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconHistory size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Prescriptions Récentes</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {prescriptions.slice(0, 3).map((prescription) => (
                  <Group key={prescription.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{prescription.medication_name}</Text>
                      <Text size="xs" c="dimmed">
                        {prescription.patient_name} - {prescription.dosage}
                      </Text>
                      <Text size="xs" c="dimmed">
                        {new Date(prescription.date_prescribed).toLocaleDateString()}
                      </Text>
                    </div>
                    <Group gap="xs">
                      <Badge 
                        size="xs" 
                        color={
                          prescription.status === 'active' ? 'green' : 
                          prescription.status === 'expired' ? 'red' : 
                          prescription.status === 'completed' ? 'blue' : 'gray'
                        }
                      >
                        {prescription.status}
                      </Badge>
                      {prescription.is_controlled_substance && (
                        <Badge size="xs" color="orange">Contrôlé</Badge>
                      )}
                    </Group>
                  </Group>
                ))}
                {prescriptions.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune prescription récente
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Most Prescribed Medications */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="indigo" size="sm">
                    <IconTrendingUp size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Médicaments Fréquents</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {analytics?.most_prescribed_medications.slice(0, 3).map((medication, index) => (
                  <Group key={medication.medication_name} justify="space-between">
                    <Group gap="xs">
                      <Badge size="xs" color="indigo" variant="light">
                        {index + 1}
                      </Badge>
                      <Text size="xs">{medication.medication_name}</Text>
                    </Group>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{medication.prescription_count}</Text>
                      <Text size="xs" c="dimmed">{medication.patient_count} patients</Text>
                    </Group>
                  </Group>
                )) || (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée disponible
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Refill Statistics */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="yellow" size="sm">
                    <IconCalendarDollar size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Statistiques Renouvellements</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text size="xs">Total renouvellements</Text>
                  <Text size="xs" fw={500}>{analytics?.refill_statistics.total_refills || 0}</Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">En attente</Text>
                  <Text size="xs" fw={500}>{analytics?.refill_statistics.pending_refills || 0}</Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">En retard</Text>
                  <Text size="xs" fw={500} c="orange">{analytics?.refill_statistics.overdue_refills || 0}</Text>
                </Group>
                <Divider size="xs" />
                <Group justify="space-between">
                  <Text size="xs">Temps moyen</Text>
                  <Text size="xs" fw={500}>{analytics?.refill_statistics.average_refill_time.toFixed(1) || 0}j</Text>
                </Group>
              </Stack>
            </Card>
          </SimpleGrid>

          {/* Patient-Specific Stats */}
          {patientId && patientStats && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>Statistiques Patient</Text>
                <Badge size="sm" color="blue">
                  {patientStats.totalPrescriptions} prescriptions
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Total</Text>
                    <Text size="lg" fw={700} c="blue">{patientStats.totalPrescriptions}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Actives</Text>
                    <Text size="lg" fw={700} c="green">{patientStats.activePrescriptions}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Observance</Text>
                    <Text size="lg" fw={700} c="purple">{patientStats.complianceRate.toFixed(1)}%</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Renouvellements</Text>
                    <Text size="lg" fw={700} c="orange">{patientStats.pendingRefills}</Text>
                  </Stack>
                </Grid.Col>
              </Grid>
              <Divider my="md" />
              <Group justify="space-between">
                <div>
                  <Text size="sm" c="dimmed">Médicament principal</Text>
                  <Text size="sm" fw={600}>{patientStats.mostCommonMedication}</Text>
                </div>
                {patientStats.lastPrescriptionDate && (
                  <div style={{ textAlign: 'right' }}>
                    <Text size="sm" c="dimmed">Dernière prescription</Text>
                    <Text size="sm" fw={600}>
                      {new Date(patientStats.lastPrescriptionDate).toLocaleDateString()}
                    </Text>
                  </div>
                )}
              </Group>
            </Card>
          )}

          {/* Drug Interactions Alert */}
          {showDrugInteractions && drugInteractions.length > 0 && (
            <Alert icon={<IconAlertTriangle size={16} />} color="orange" variant="light">
              <Group justify="space-between">
                <div>
                  <Text size="sm" fw={600}>Interactions Médicamenteuses Détectées</Text>
                  <Text size="xs">
                    {drugInteractions.length} interaction(s) trouvée(s)
                  </Text>
                  <Stack gap="xs" mt="xs">
                    {drugInteractions.slice(0, 2).map((interaction) => (
                      <div key={interaction.id}>
                        <Text size="xs" fw={500}>
                          {interaction.medication_1} + {interaction.medication_2}
                        </Text>
                        <Text size="xs" c="dimmed">{interaction.description}</Text>
                      </div>
                    ))}
                  </Stack>
                </div>
                <Badge 
                  size="lg" 
                  color={
                    drugInteractions.some(i => i.interaction_type === 'major') ? 'red' :
                    drugInteractions.some(i => i.interaction_type === 'moderate') ? 'orange' : 'yellow'
                  }
                >
                  {drugInteractions.filter(i => i.interaction_type === 'major').length > 0 ? 'Majeure' :
                   drugInteractions.filter(i => i.interaction_type === 'moderate').length > 0 ? 'Modérée' : 'Mineure'}
                </Badge>
              </Group>
            </Alert>
          )}

          {/* Expired Prescriptions Alert */}
          {expiredPrescriptions.length > 0 && (
            <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
              <Group justify="space-between">
                <div>
                  <Text size="sm" fw={600}>Prescriptions Expirées</Text>
                  <Text size="xs">
                    {expiredPrescriptions.length} prescription(s) ont expiré
                  </Text>
                </div>
                <Text size="sm" fw={600} c="red">
                  {expiredPrescriptions.length}
                </Text>
              </Group>
            </Alert>
          )}

          {/* Controlled Substances Alert */}
          {prescriptionTrends.controlledSubstances > 0 && (
            <Alert icon={<IconShieldCheck size={16} />} color="blue" variant="light">
              <Group justify="space-between">
                <div>
                  <Text size="sm" fw={600}>Substances Contrôlées</Text>
                  <Text size="xs">
                    {prescriptionTrends.controlledSubstances} prescription(s) de substances contrôlées
                  </Text>
                </div>
                <Badge size="sm" color="blue">
                  {prescriptionTrends.controlledSubstances}
                </Badge>
              </Group>
            </Alert>
          )}
        </>
      )}
    </Stack>
  );
};

export default PrescriptionsWidgets;
