
/** This file is autogenerated by the script. Do not edit it manually. */


@theme {
  

    /* colors - all */
    
    --color-brand-50: rgb(from var(--mantine-color-brand-0) r g b / <alpha-value>);
    --color-brand-100: rgb(from var(--mantine-color-brand-1) r g b / <alpha-value>);
    --color-brand-200: rgb(from var(--mantine-color-brand-2) r g b / <alpha-value>);
    --color-brand-300: rgb(from var(--mantine-color-brand-3) r g b / <alpha-value>);
    --color-brand-400: rgb(from var(--mantine-color-brand-4) r g b / <alpha-value>);
    --color-brand-500: rgb(from var(--mantine-color-brand-5) r g b / <alpha-value>);
    --color-brand-600: rgb(from var(--mantine-color-brand-6) r g b / <alpha-value>);
    --color-brand-700: rgb(from var(--mantine-color-brand-7) r g b / <alpha-value>);
    --color-brand-800: rgb(from var(--mantine-color-brand-8) r g b / <alpha-value>);
    --color-brand-900: rgb(from var(--mantine-color-brand-9) r g b / <alpha-value>);
    --color-brand: rgb(from var(--mantine-color-brand-filled) r g b / <alpha-value>);
  
    /* colors - variant specific */
    
    --color-brand-filled: rgb(from var(--mantine-color-brand-filled) r g b / <alpha-value>);
    --color-brand-filled-hover: var(--mantine-color-brand-filled-hover);
    --color-brand-light: var(--mantine-color-brand-light);
    --color-brand-light-hover: var(--mantine-color-brand-light-hover);
    --color-brand-light-color: rgb(from var(--mantine-color-brand-light-color) r g b / <alpha-value>);
    --color-brand-outline: rgb(from var(--mantine-color-brand-outline) r g b / <alpha-value>);
    --color-brand-outline-hover: var(--mantine-color-brand-outline-hover);
  
    /* breakpoints */
    
    --breakpoint-xs: 36em;
  
    --breakpoint-sm: 48em;
  
    --breakpoint-md: 62em;
  
    --breakpoint-lg: 75em;
  
    --breakpoint-xl: 88em;
  }