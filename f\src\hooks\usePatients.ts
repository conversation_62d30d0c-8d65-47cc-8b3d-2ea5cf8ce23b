import { useState, useEffect, useCallback } from 'react';
import patientService, { Patient } from '@/services/patientService';
import { notifications } from '@mantine/notifications';

export interface PatientFilters {
  search?: string;
  status?: 'complete' | 'validated' | 'favorites' | 'archived' | 'incomplete' | 'no_visit';
  city?: string;
  insurance?: string;
  age_min?: number;
  age_max?: number;
  date_from?: string;
  date_to?: string;
}

export interface UsePatients {
  patients: Patient[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  itemsPerPage: number;
  totalPages: number;
  
  // Actions
  fetchPatients: (filters?: PatientFilters) => Promise<void>;
  fetchPatientsByStatus: (status: PatientFilters['status']) => Promise<void>;
  searchPatients: (filters: PatientFilters) => Promise<void>;
  setPage: (page: number) => void;
  setItemsPerPage: (items: number) => void;
  refreshPatients: () => Promise<void>;
}

export const usePatients = (initialFilters?: PatientFilters): UsePatients => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [filters, setFilters] = useState<PatientFilters>(initialFilters || {});

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const fetchPatients = useCallback(async (newFilters?: PatientFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = newFilters || filters;
      setFilters(filtersToUse);
      
      let data: Patient[];
      
      if (filtersToUse.status) {
        data = await patientService.getPatientsByStatus(filtersToUse.status);
      } else if (Object.keys(filtersToUse).length > 0) {
        data = await patientService.searchPatients(filtersToUse);
      } else {
        data = await patientService.getPatients();
      }
      
      setPatients(data);
      setTotalCount(data.length);
      
      console.log(`✅ Loaded ${data.length} patients`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load patients';
      setError(errorMessage);
      console.error('❌ Error fetching patients:', err);
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger les patients',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const fetchPatientsByStatus = useCallback(async (status: PatientFilters['status']) => {
    await fetchPatients({ ...filters, status });
  }, [fetchPatients, filters]);

  const searchPatients = useCallback(async (searchFilters: PatientFilters) => {
    await fetchPatients(searchFilters);
  }, [fetchPatients]);

  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const setItemsPerPageCallback = useCallback((items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1); // Reset to first page when changing items per page
  }, []);

  const refreshPatients = useCallback(async () => {
    await fetchPatients(filters);
  }, [fetchPatients, filters]);

  // Initial load
  useEffect(() => {
    fetchPatients();
  }, []);

  // Paginate patients
  const paginatedPatients = patients.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return {
    patients: paginatedPatients,
    loading,
    error,
    totalCount,
    currentPage,
    itemsPerPage,
    totalPages,
    fetchPatients,
    fetchPatientsByStatus,
    searchPatients,
    setPage,
    setItemsPerPage: setItemsPerPageCallback,
    refreshPatients,
  };
};

export default usePatients;
