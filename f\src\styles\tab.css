:root {
  color-scheme: light;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;
}

[data-theme="light"] {
  color-scheme: light;

  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #d9dcde;
  --b1: 100% 0 0;
  --b2: 96.1151% 0 0;
}
[data-theme="dark"] {
  color-scheme: dark;
  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;
  --animation-btn: 0.25s;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #25292d;
  --b1: 25.3267% 0.015896 252.417568;
  --b2: 23.2607% 0.013807 253.100675;
}
@media (hover: hover) {
  .tab[disabled],
  .tab[disabled]:hover {
    cursor: not-allowed;
    color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
    --tw-text-opacity: 0.2;
  }
}
.tabs {
  display: grid;
  align-items: flex-end;
}

.tabs-lifted:has(.tab-content[class^="rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])),
.tabs-lifted:has(.tab-content[class*=" rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])) {
  border-bottom-color: transparent;
}

.tab {
  position: relative;
  grid-row-start: 1;
  display: inline-flex;
  height: 39.6px;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
  --tw-text-opacity: 0.5;
  --tab-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --tab-bg: var(--content-background);
  --tab-border-color: var(--border-color);
  color: var(--text-daisy);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
}

.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  /* border-color: transparent; */
  border-width: var(--tab-border, 0);
}

:checked + .tab-content:nth-child(2),
:is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(2) {
  border-start-start-radius: 0;
}

.tabs-lifted > .tab:focus-visible {
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}

.tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ) {
  border-color: var(--fallback-bc, oklch(var(--bc) / var(--tw-border-opacity)));
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
}

.tab:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.tab:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: -5px;
}

.tab-disabled,
.tab[disabled] {
  cursor: not-allowed;
  color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
  --tw-text-opacity: 0.2;
}

.tabs-lifted > .tab {
  border: var(--tab-border, 1px) solid transparent;
  border-width: 0 0 var(--tab-border, 1px);
  border-start-start-radius: var(--tab-radius, 0.5rem);
  border-start-end-radius: var(--tab-radius, 0.5rem);
  border-bottom-color: var(--tab-border-color);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
  padding-top: var(--tab-border, 1px);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ),
.tabs-lifted > .tab:is(input:checked) {
  background-color: var(--mantine-color-body);
  border-width: var(--tab-border, 1px) var(--tab-border, 1px) 0;
  border-inline-start-color: var(--tab-border-color);
  border-inline-end-color: var(--tab-border-color);
  border-top-color: var(--tab-border-color);
  padding-inline-start: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-inline-end: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-bottom: var(--tab-border, 1px);
  padding-top: 0;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted > .tab:is(input:checked):before {
  z-index: 1;
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + var(--tab-radius, 0.5rem) * 2);
  height: var(--tab-radius, 0.5rem);
  bottom: 0;
  background-size: var(--tab-radius, 0.5rem);
  background-position:
    top left,
    top right;
  background-repeat: no-repeat;
  --tab-grad: calc(69% - var(--tab-border, 1px));
  --radius-start: radial-gradient(
    circle at top left,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  --radius-end: radial-gradient(
    circle at top right,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  background-image: var(--radius-start), var(--radius-end);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
.tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
.tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-lifted
  > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled])
  + .tabs-lifted
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted
  > .tab:is(input:checked)
  + .tabs-lifted
  .tab:is(input:checked):before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-boxed {
  border-radius: var(--rounded-btn, 0.5rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2, oklch(var(--b2) / var(--tw-bg-opacity)));
  padding: 0.25rem;
}

.tabs-boxed .tab {
  border-radius: var(--rounded-btn, 0.5rem);
}

.tabs-boxed
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]),
.tabs-boxed :is(input:checked) {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p, oklch(var(--p) / var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc, oklch(var(--pc) / var(--tw-text-opacity)));
}

.tab-border-none {
  --tab-border: 0px;
}

.tab-border {
  --tab-border: 1px;
}

.tabs-md :where(.tab) {
  height: 2rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
}

.tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}

.tabs-sm :where(.tab) {
  height: 1.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  --tab-padding: 0.75rem;
}

.tabs-xs :where(.tab) {
  height: 1.25rem;
  font-size: 0.75rem;
  line-height: 0.75rem;
  --tab-padding: 0.5rem;
}

.-mb-\[var\(--tab-border\)\] {
  margin-bottom: calc(var(--tab-border) * -1);
}

.\[--tab-bg\: oklch\(var\(--n\)\)\] {
  --tab-bg: oklch(var(--n));
}

.\[--tab-bg\: var\(--fallback-b1\,oklch\(var\(--b1\)\)\)\] {
  --tab-bg: var(--fallback-b1, oklch(var(--b1)));
}

.\[--tab-bg\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-bg: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-bg\: yellow\] {
  --tab-bg: yellow;
}

.\[--tab-border-color\: oklch\(var\(--n\)\)\] {
  --tab-border-color: oklch(var(--n));
}

.\[--tab-border-color\: orange\] {
  --tab-border-color: orange;
}

.\[--tab-border-color\: transparent\] {
  --tab-border-color: transparent;
}

.\[--tab-border-color\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-border-color: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-color\: var\(--fallback-nc\,oklch\(var\(--nc\)\)\)\] {
  --tab-color: var(--fallback-nc, oklch(var(--nc)));
}

.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
}

.xl\:tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}
.tab-active {
  color: #3895c8;
  background-color: var(--content-background);
}
/* .border-base-300 {
  background-color: var(--mantine-color-body);
  border: var(--mantine-color-gray-3);
} */

.rounded-box {
  border-radius: var(--rounded-box, 1rem);
}
.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  border-color: transparent;
  border-width: var(--tab-border, 0);
}
.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
  border-color: var(--border-color);
}
.rounded-se-box {
  border-start-end-radius: var(--rounded-box, 1rem);
}
.rounded-b-box {
  border-bottom-right-radius: var(--rounded-box, 1rem);
  border-bottom-left-radius: var(--rounded-box, 1rem);
}
.overflow-x-auto {
  overflow-x: auto;
}
*:hover {
  scrollbar-color: color-mix(in oklch, currentColor 60%, transparent)
    transparent;
}
.preview {
  background-image: repeating-linear-gradient(
    45deg,
    var(--fallback-b1, oklch(var(--b1))),
    var(--fallback-b1, oklch(var(--b1))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 14px
  );
  background-size: 40px 40px;
}
.border-base-300 {
  --tw-border-opacity: 1;
  background-color: var(--mantine-color-body);
}
.bg-base-100 {
  --tw-bg-opacity: 1;
  background-color: var(oklch(var(--b1) / var(--tw-bg-opacity)));
}
