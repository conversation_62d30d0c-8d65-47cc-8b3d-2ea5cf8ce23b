"use client";

import React, { useState, useMemo } from 'react';
import {
  <PERSON>ton,
  Tabs,
  TextInput,
  Group,
  ActionIcon,
  Modal,
  Stack,
  Text,
  ColorPicker,
  Badge,
  Avatar,
  ScrollArea,
  Card,
  Textarea,
  Checkbox,
  Switch
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconColorPicker,
  IconDental,
  IconUsers,
  IconAdjustments
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DataTable } from 'mantine-datatable';

// Types
interface DentalAct {
  id: string;
  dateModification: string;
  code: string;
  nom: string;
  description: string;
  couleur: string;
  nombreActes: number;
  desactive: boolean;
}

interface DentalGroup {
  code: string;
  nom: string;
  couleur: string;
  description: string;
  procedures: string[];
}

interface Procedure {
  id: string;
  name: string;
  selected: boolean;
}

interface DentalActDetail {
  id: string;
  dateModification: string;
  code: string;
  nom: string;
  description: string;
  plusieursActes: boolean;
  priorite: boolean;
  toujoursPossible: boolean;
}

const Dentaire = () => {
  // États pour les modales
  const [groupModalOpened, { open: openGroupModal, close: closeGroupModal }] = useDisclosure(false);
  const [colorPickerOpened, { open: openColorPicker, close: closeColorPicker }] = useDisclosure(false);
  const [actModalOpened, { open: openActModal, close: closeActModal }] = useDisclosure(false);
  const [editMode, setEditMode] = useState(false);
  const [actEditMode, setActEditMode] = useState(false);

  // États pour les données
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<string | null>('groupes');
  const [selectedColor, setSelectedColor] = useState('#b2106e');
  const [currentGroup, setCurrentGroup] = useState<DentalGroup>({
    code: '',
    nom: '',
    couleur: '#b2106e',
    description: '',
    procedures: []
  });
  const [currentAct, setCurrentAct] = useState<DentalActDetail>({
    id: '',
    dateModification: '',
    code: '',
    nom: '',
    description: '',
    plusieursActes: false,
    priorite: false,
    toujoursPossible: false
  });

  // Données d'exemple pour les actes dentaires
  const [dentalActs] = useState<DentalAct[]>([
    {
      id: '1',
      dateModification: '06/05/2023 21:50',
      code: 'DIAG',
      nom: 'Diagnostic',
      description: '',
      couleur: '#b2106e',
      nombreActes: 15,
      desactive: false
    },
    {
      id: '2',
      dateModification: '27/10/2023 12:46',
      code: 'URG',
      nom: 'Urgence',
      description: '',
      couleur: '#747e9c',
      nombreActes: 11,
      desactive: false
    },
    {
      id: '3',
      dateModification: '27/10/2023 12:46',
      code: 'PEDO',
      nom: 'Pedodontie',
      description: '',
      couleur: '#a1bea8',
      nombreActes: 19,
      desactive: false
    },
    {
      id: '4',
      dateModification: '27/10/2023 12:46',
      code: 'SOIN',
      nom: 'Soins Conservateurs',
      description: '',
      couleur: '#44923b',
      nombreActes: 13,
      desactive: false
    },
    {
      id: '5',
      dateModification: '27/10/2023 12:46',
      code: 'ENDO',
      nom: 'Endodontie',
      description: '',
      couleur: '#f06292',
      nombreActes: 14,
      desactive: false
    }
  ]);

  // Données d'exemple pour les procédures
  const [procedures] = useState<Procedure[]>([
    { id: '1', name: 'Rétroalvéolaire', selected: false },
    { id: '2', name: 'Rx Panoramique', selected: false },
    { id: '3', name: 'Cone Beam', selected: false },
    { id: '4', name: 'Camera Intrabuccale', selected: false },
    { id: '5', name: 'Motivation à l\'HBD', selected: false },
    { id: '6', name: 'Empreinte D\'étude', selected: false },
    { id: '7', name: 'Analyse Biologique', selected: false },
    { id: '8', name: 'Sondage Parodontal', selected: false },
    { id: '9', name: 'Détartrage', selected: false },
    { id: '10', name: 'Aéropolissage', selected: false },
    { id: '11', name: 'Ablation de fil de suture', selected: false },
    { id: '12', name: 'Contention', selected: false },
    { id: '13', name: 'Consultation', selected: false },
    { id: '14', name: 'Ordonnance', selected: false },
    { id: '15', name: 'Curetage interdentaire', selected: false }
  ]);

  // Données d'exemple pour les actes dentaires détaillés
  const [dentalActsDetails] = useState<DentalActDetail[]>([
    {
      id: '1',
      dateModification: '27/10/2023 12:46',
      code: 'RTR',
      nom: 'Rétroalvéolaire',
      description: '',
      plusieursActes: true,
      priorite: false,
      toujoursPossible: true
    },
    {
      id: '2',
      dateModification: '27/10/2023 12:46',
      code: 'RXP',
      nom: 'Rx Panoramique',
      description: '',
      plusieursActes: true,
      priorite: false,
      toujoursPossible: true
    },
    {
      id: '3',
      dateModification: '27/10/2023 12:46',
      code: 'CBM',
      nom: 'Cone Beam',
      description: '',
      plusieursActes: true,
      priorite: false,
      toujoursPossible: true
    },
    {
      id: '4',
      dateModification: '27/10/2023 12:46',
      code: 'CIB',
      nom: 'Camera Intrabuccale',
      description: '',
      plusieursActes: true,
      priorite: false,
      toujoursPossible: true
    },
    {
      id: '5',
      dateModification: '27/10/2023 12:46',
      code: 'HBD',
      nom: 'Motivation à l\'HBD',
      description: 'Motivation à l\'hygiène bucco dentaire',
      plusieursActes: true,
      priorite: false,
      toujoursPossible: true
    }
  ]);

  // Filtrer les actes dentaires selon la recherche
  const filteredDentalActs = useMemo(() => {
    if (!searchQuery) return dentalActs;
    return dentalActs.filter(act =>
      act.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
      act.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      act.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [dentalActs, searchQuery]);

  // Gestionnaires d'événements
  const handleNewGroup = () => {
    setEditMode(false);
    setCurrentGroup({
      code: '',
      nom: '',
      couleur: '#b2106e',
      description: '',
      procedures: []
    });
    openGroupModal();
  };

  const handleEditGroup = (group: DentalAct) => {
    setEditMode(true);
    setCurrentGroup({
      code: group.code,
      nom: group.nom,
      couleur: group.couleur,
      description: group.description,
      procedures: []
    });
    openGroupModal();
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setCurrentGroup(prev => ({ ...prev, couleur: color }));
    closeColorPicker();
  };

  const handleSaveGroup = () => {
    // Logique de sauvegarde ici
    console.log('Sauvegarde du groupe:', currentGroup);
    closeGroupModal();
  };

  // Gestionnaires pour les actes dentaires
  const handleNewAct = () => {
    setActEditMode(false);
    setCurrentAct({
      id: '',
      dateModification: '',
      code: '',
      nom: '',
      description: '',
      plusieursActes: false,
      priorite: false,
      toujoursPossible: false
    });
    openActModal();
  };

  const handleEditAct = (act: DentalActDetail) => {
    setActEditMode(true);
    setCurrentAct(act);
    openActModal();
  };

  const handleSaveAct = () => {
    // Logique de sauvegarde ici
    console.log('Sauvegarde de l\'acte:', currentAct);
    closeActModal();
  };

  // Colonnes pour le tableau des groupes
  const groupColumns = [
    {
      accessor: 'dateModification',
      title: 'Date de modification',
      width: 150,
    },
    {
      accessor: 'code',
      title: 'Code',
      width: 80,
    },
    {
      accessor: 'nom',
      title: 'Nom',
      width: 200,
    },
    {
      accessor: 'description',
      title: 'Description',
      width: 200,
    },
    {
      accessor: 'couleur',
      title: 'Couleur',
      width: 100,
      render: (record: DentalAct) => (
        <Avatar
          size="sm"
          style={{ backgroundColor: record.couleur }}
          radius="sm"
        />
      ),
    },
    {
      accessor: 'nombreActes',
      title: 'Nbre des Actes',
      width: 120,
      textAlign: 'center' as const,
    },
    {
      accessor: 'desactive',
      title: 'Désactiver',
      width: 100,
      textAlign: 'center' as const,
      render: (record: DentalAct) => (
        <Badge color={record.desactive ? 'red' : 'green'} variant="light">
          {record.desactive ? 'Inactif' : 'Actif'}
        </Badge>
      ),
    },
    {
      accessor: 'actions',
      title: 'Actions',
      width: 120,
      textAlign: 'center' as const,
      render: (record: DentalAct) => (
        <Group gap="xs" justify="center">
          <ActionIcon
            size="sm"
            variant="subtle"
            color="blue"
            onClick={() => handleEditGroup(record)}
          >
            <IconEdit size={16} />
          </ActionIcon>
          <ActionIcon
            size="sm"
            variant="subtle"
            color="red"
          >
            <IconTrash size={16} />
          </ActionIcon>
        </Group>
      ),
    },
  ];

  // Colonnes pour le tableau des actes dentaires
  const actColumns = [
    {
      accessor: 'dateModification',
      title: 'Date de modific...',
      width: 120,
    },
    {
      accessor: 'code',
      title: 'Code',
      width: 80,
    },
    {
      accessor: 'nom',
      title: 'Nom',
      width: 200,
    },
    {
      accessor: 'description',
      title: 'Description',
      width: 200,
    },
    {
      accessor: 'plusieursActes',
      title: 'Plu...',
      width: 60,
      textAlign: 'center' as const,
      render: (record: DentalActDetail) => (
        <div className="flex justify-center">
          <div
            className={`w-4 h-4 rounded-full ${
              record.plusieursActes ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
        </div>
      ),
    },
    {
      accessor: 'priorite',
      title: 'Pri...',
      width: 60,
      textAlign: 'center' as const,
      render: (record: DentalActDetail) => (
        <div className="flex justify-center">
          <div
            className={`w-4 h-4 rounded-full ${
              record.priorite ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
        </div>
      ),
    },
    {
      accessor: 'toujoursPossible',
      title: 'Tou...',
      width: 60,
      textAlign: 'center' as const,
      render: (record: DentalActDetail) => (
        <div className="flex justify-center">
          <div
            className={`w-4 h-4 rounded-full ${
              record.toujoursPossible ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
        </div>
      ),
    },
    {
      accessor: 'actions',
      title: 'Actions',
      width: 120,
      textAlign: 'center' as const,
      render: (record: DentalActDetail) => (
        <Group gap="xs" justify="center">
          <ActionIcon
            size="sm"
            variant="subtle"
            color="blue"
            onClick={() => handleEditAct(record)}
          >
            <IconEdit size={16} />
          </ActionIcon>
          <ActionIcon
            size="sm"
            variant="subtle"
            color="red"
          >
            <IconTrash size={16} />
          </ActionIcon>
        </Group>
      ),
    },
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconDental size={32} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-800">Dentaire</h1>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={handleNewGroup}
          className="bg-blue-500 hover:bg-blue-600"
        >
          Nouveau groupe
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
        <Tabs.List>
          <Tabs.Tab value="groupes" leftSection={<IconUsers size={16} />}>
            Groupes
          </Tabs.Tab>
          <Tabs.Tab value="actes-dentaires" leftSection={<IconDental size={16} />}>
            Actes dentaires
          </Tabs.Tab>
          <Tabs.Tab value="general" leftSection={<IconAdjustments size={16} />}>
            Général
          </Tabs.Tab>
        </Tabs.List>

        {/* Contenu de l'onglet Groupes */}
        <Tabs.Panel value="groupes" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            {/* Barre de recherche */}
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>

            {/* Tableau */}
            <div className="bg-white rounded-lg">
              <DataTable
                withTableBorder
                borderRadius="sm"
                withColumnBorders
                striped
                highlightOnHover
                records={filteredDentalActs}
                columns={groupColumns}
                minHeight={400}
                noRecordsText="Aucun enregistrement trouvé"
              />
            </div>
          </Card>
        </Tabs.Panel>

        {/* Contenu de l'onglet Actes dentaires */}
        <Tabs.Panel value="actes-dentaires" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            {/* Header avec bouton Nouvel acte */}
            <div className="flex items-center justify-between mb-4">
              <Text size="lg" fw={600}>Actes dentaires</Text>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={handleNewAct}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Nouvel acte
              </Button>
            </div>

            {/* Barre de recherche */}
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>

            {/* Tableau */}
            <div className="bg-white rounded-lg">
              <DataTable
                withTableBorder
                borderRadius="sm"
                withColumnBorders
                striped
                highlightOnHover
                records={dentalActsDetails}
                columns={actColumns}
                minHeight={400}
                noRecordsText="Aucun enregistrement trouvé"
              />
            </div>
          </Card>
        </Tabs.Panel>

        {/* Contenu de l'onglet Général */}
        <Tabs.Panel value="general" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Text size="lg" fw={600} mb="lg">Paramètres généraux</Text>

            <div className="space-y-6">
              {/* Configuration des couleurs par défaut */}
              <div>
                <Text size="md" fw={500} mb="sm">Couleurs par défaut</Text>
                <div className="grid grid-cols-5 gap-4">
                  {[
                    { name: 'Diagnostic', color: '#b2106e' },
                    { name: 'Urgence', color: '#747e9c' },
                    { name: 'Pédodontie', color: '#a1bea8' },
                    { name: 'Soins', color: '#44923b' },
                    { name: 'Endodontie', color: '#f06292' }
                  ].map((item, index) => (
                    <div key={index} className="text-center">
                      <Avatar
                        size="lg"
                        style={{ backgroundColor: item.color }}
                        radius="sm"
                        className="mx-auto mb-2"
                      />
                      <Text size="xs" className="text-gray-600">{item.name}</Text>
                    </div>
                  ))}
                </div>
              </div>

              {/* Options de configuration */}
              <div className="space-y-4">
                <Text size="md" fw={500}>Options de configuration</Text>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Text size="sm" fw={500}>Affichage des codes</Text>
                    <Text size="xs" c="dimmed">Afficher les codes des actes dans les listes</Text>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Text size="sm" fw={500}>Validation automatique</Text>
                    <Text size="xs" c="dimmed">Valider automatiquement les actes sélectionnés</Text>
                  </div>
                  <Switch />
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Text size="sm" fw={500}>Notifications</Text>
                    <Text size="xs" c="dimmed">Recevoir des notifications pour les nouveaux actes</Text>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Modal pour nouveau/modifier groupe */}
      <Modal
        opened={groupModalOpened}
        onClose={closeGroupModal}
        title={
          <div className="flex items-center gap-2">
            <IconUsers size={20} />
            <Text fw={600}>
              {editMode ? 'Modifier le groupe' : 'Groupes'}
            </Text>
          </div>
        }
        size="lg"
        centered
      >
        <Stack gap="md">
          {/* Code */}
          <TextInput
            label="Code"
            placeholder="Entrez le code du groupe"
            value={currentGroup.code}
            onChange={(e) => setCurrentGroup(prev => ({ ...prev, code: e.target.value }))}
            required
          />

          {/* Nom */}
          <TextInput
            label="Nom"
            placeholder="Entrez le nom du groupe"
            value={currentGroup.nom}
            onChange={(e) => setCurrentGroup(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          {/* Description */}
          <Textarea
            label="Description"
            placeholder="Entrez la description du groupe"
            value={currentGroup.description}
            onChange={(e) => setCurrentGroup(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
          />

          {/* Couleur */}
          <div>
            <Text size="sm" fw={500} mb="xs">Couleur</Text>
            <Group gap="sm">
              <Avatar
                size="lg"
                style={{ backgroundColor: currentGroup.couleur }}
                radius="sm"
                className="cursor-pointer border-2 border-gray-300"
                onClick={openColorPicker}
              />
              <Button
                variant="outline"
                leftSection={<IconColorPicker size={16} />}
                onClick={openColorPicker}
              >
                Choisir une couleur
              </Button>
            </Group>
          </div>

          {/* Liste des procédures */}
          <div>
            <Text size="sm" fw={500} mb="xs">Procédures</Text>
            <ScrollArea h={200} className="border rounded-md p-2">
              <Stack gap="xs">
                {procedures.map((procedure) => (
                  <div key={procedure.id} className="flex items-center gap-2">
                    <Checkbox
                      checked={procedure.selected}
                      onChange={(e) => {
                        // Logique pour gérer la sélection des procédures
                        console.log(`Procédure ${procedure.name} ${e.target.checked ? 'sélectionnée' : 'désélectionnée'}`);
                      }}
                    />
                    <Text size="sm">{procedure.name}</Text>
                  </div>
                ))}
              </Stack>
            </ScrollArea>
          </div>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeGroupModal}>
              Annuler
            </Button>
            <Button onClick={handleSaveGroup}>
              {editMode ? 'Modifier' : 'Enregistrer'}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal pour sélecteur de couleur */}
      <Modal
        opened={colorPickerOpened}
        onClose={closeColorPicker}
        title="Sélectionner une couleur"
        size="auto"
        centered
      >
        <Stack gap="md">
          <ColorPicker
            value={selectedColor}
            onChange={setSelectedColor}
            format="hex"
            size="lg"
            swatches={[
              '#b2106e', '#747e9c', '#a1bea8', '#44923b', '#f06292',
              '#2196f3', '#4caf50', '#ff9800', '#9c27b0', '#f44336',
              '#607d8b', '#795548', '#3f51b5', '#009688', '#ffeb3b'
            ]}
          />
          <Group justify="center" mt="md">
            <Button
              onClick={() => handleColorSelect(selectedColor)}
              style={{ backgroundColor: selectedColor }}
              className="text-white"
            >
              Sélectionner cette couleur
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal pour nouveau/modifier acte dentaire */}
      <Modal
        opened={actModalOpened}
        onClose={closeActModal}
        title={
          <div className="flex items-center gap-2">
            <IconDental size={20} />
            <Text fw={600}>
              {actEditMode ? 'Modifier l\'acte' : 'Nouvel acte'}
            </Text>
          </div>
        }
        size="lg"
        centered
      >
        <Stack gap="md">
          {/* Code */}
          <TextInput
            label="Code"
            placeholder="Entrez le code de l'acte"
            value={currentAct.code}
            onChange={(e) => setCurrentAct(prev => ({ ...prev, code: e.target.value }))}
            required
          />

          {/* Nom */}
          <TextInput
            label="Nom"
            placeholder="Entrez le nom de l'acte"
            value={currentAct.nom}
            onChange={(e) => setCurrentAct(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          {/* Description */}
          <Textarea
            label="Description"
            placeholder="Entrez la description de l'acte"
            value={currentAct.description}
            onChange={(e) => setCurrentAct(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
          />

          {/* Options avec switches */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Text size="sm" fw={500}>Plusieurs actes</Text>
                <Text size="xs" c="dimmed">Autoriser plusieurs actes de ce type</Text>
              </div>
              <Switch
                checked={currentAct.plusieursActes}
                onChange={(e) => setCurrentAct(prev => ({ ...prev, plusieursActes: e.target.checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Text size="sm" fw={500}>Priorité</Text>
                <Text size="xs" c="dimmed">Marquer cet acte comme prioritaire</Text>
              </div>
              <Switch
                checked={currentAct.priorite}
                onChange={(e) => setCurrentAct(prev => ({ ...prev, priorite: e.target.checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Text size="sm" fw={500}>Toujours possible</Text>
                <Text size="xs" c="dimmed">Cet acte est toujours réalisable</Text>
              </div>
              <Switch
                checked={currentAct.toujoursPossible}
                onChange={(e) => setCurrentAct(prev => ({ ...prev, toujoursPossible: e.target.checked }))}
              />
            </div>
          </div>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={closeActModal}>
              Annuler
            </Button>
            <Button onClick={handleSaveAct}>
              {actEditMode ? 'Modifier' : 'Enregistrer'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Dentaire;
