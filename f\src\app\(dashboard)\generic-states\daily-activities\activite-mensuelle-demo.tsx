'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { ActiviteMensuelle } from './ActiviteMensuelle';

export default function ActiviteMensuelleDemo() {
  const mockSummary = {
    total: 0.00,
    encasement_total: 0.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Activité changée:', activity);
  };

  const handleProcedureTypeChange = (type: number) => {
    console.log('Type de procédure changé:', type);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression en cours...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="monthly"
            summary={mockSummary}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={handleProcedureTypeChange}
            onExport={handleExport}
            onPrint={handlePrint}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function ActiviteMensuelleLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="monthly"
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onActivityChange={(activity) => console.log('Activity:', activity)}
            onProcedureTypeChange={(type) => console.log('Type:', type)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle annuel (sans date picker)
export function ActiviteMensuelleAnnuelleDemo() {
  const mockSummaryAnnuelle = {
    total: 125000.00,
    encasement_total: 98000.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="annual"
            summary={mockSummaryAnnuelle}
            loading={false}
            onQueryChange={(query) => console.log('Query annuelle:', query)}
            onActivityChange={(activity) => console.log('Activity annuelle:', activity)}
            onProcedureTypeChange={(type) => console.log('Type annuel:', type)}
            onExport={(format) => alert(`Export annuel ${format}`)}
            onPrint={() => alert('Impression rapport annuel')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle périodique
export function ActiviteMensuellePeriodique() {
  const mockSummaryPeriodique = {
    total: 15000.00,
    encasement_total: 12500.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="periodic"
            summary={mockSummaryPeriodique}
            loading={false}
            onQueryChange={(query) => console.log('Query périodique:', query)}
            onActivityChange={(activity) => console.log('Activity périodique:', activity)}
            onProcedureTypeChange={(type) => console.log('Type périodique:', type)}
            onExport={(format) => alert(`Export périodique ${format}`)}
            onPrint={() => alert('Impression rapport périodique')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données d'activité
export function ActiviteMensuelleWithDataDemo() {
  const mockSummaryWithData = {
    total: 45000.00,
    encasement_total: 38000.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données mensuelles chargées pour:', query);
    }, 1000);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Changement d\'activité mensuelle:', activity);
    // Simuler le rechargement des données selon l'activité
    if (activity.type === 'encasement') {
      console.log('Chargement des données d\'encaissement mensuelles...');
    } else if (activity.type === 'procedure') {
      console.log('Chargement des données de procédures mensuelles...');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="monthly"
            summary={mockSummaryWithData}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={(type) => {
              console.log('Type de procédure mensuelle:', type);
              const typeLabels = ['Chiffre d\'affaire', 'Nombre d\'exécution', 'Les deux'];
              alert(`Type mensuel sélectionné: ${typeLabels[type]}`);
            }}
            onExport={(format) => {
              console.log(`Export ${format} mensuel avec données`);
              alert(`Préparation de l'export ${format.toUpperCase()} avec les données d'activité mensuelle...`);
            }}
            onPrint={() => {
              console.log('Impression mensuelle avec données');
              alert('Préparation de l\'impression du rapport d\'activité mensuelle...');
            }}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec différents types d'activité
export function ActiviteMensuelleEncaissementsDemo() {
  const mockSummaryEncaissements = {
    total: 25000.00,
    encasement_total: 25000.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <ActiviteMensuelle
            cycle="monthly"
            summary={mockSummaryEncaissements}
            loading={false}
            onQueryChange={(query) => console.log('Query encaissements:', query)}
            onActivityChange={(activity) => {
              console.log('Activity encaissements:', activity);
              if (activity.name === 'encasements') {
                alert('Mode encaissements activé - Les options de procédure sont masquées');
              }
            }}
            onProcedureTypeChange={(type) => console.log('Type encaissements:', type)}
            onExport={(format) => alert(`Export encaissements ${format}`)}
            onPrint={() => alert('Impression rapport encaissements')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
