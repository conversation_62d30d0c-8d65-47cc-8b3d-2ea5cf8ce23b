import { Select } from "@mantine/core";
import { useState, useEffect } from "react";
import moment from "moment";
import "moment/locale/fr"; // Importer la localisation française

moment.locale("fr"); // Configurer la localisation en français

interface SelectJourneeProps {
  date: Date;
  setter: (newDate: Date) => void;
  label: string;
}

const SelectJournee: React.FC<SelectJourneeProps> = ({
  date,
  setter,
  label,
}) => {
  // Générer un tableau de dates successives (par exemple, 7 jours)
  const options = Array.from({ length: moment().daysInMonth() }, (_, i) => {
    const nextDay = moment().startOf("month").add(i, "days"); // Start at the first day of the month and add 'i' days
    return {
      value: nextDay.format("YYYY-MM-DD"), // Store the date in ISO format
      label: nextDay.format("dddd D MMM"), // Display as 'jour de la semaine jour mois'
    };
  });

  const currentDay = moment(date).format("YYYY-MM-DD");
  const [selected, setSelected] = useState<string>(currentDay);

  // Synchroniser la valeur sélectionnée avec le label
  useEffect(() => {
    const labelDay = moment(label, "DD MMMM YYYY").format("YYYY-MM-DD");
    setSelected(labelDay);
  }, [label]);

  const handleDateChange = (value: string | null) => {
    if (value) {
      setSelected(value);
      const newDate = moment(value, "YYYY-MM-DD").toDate();
      setter(newDate); // Mettre à jour la date dans le composant parent
    }
  };

  return (
    <Select
      placeholder="Choisir une date"
      data={options}
      value={selected}
      onChange={handleDateChange}
  
      fw={550}
      w={170}
    />
  );
};

export default SelectJournee;
