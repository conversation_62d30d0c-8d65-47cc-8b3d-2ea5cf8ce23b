import React from "react";
import { ActionIcon, Tooltip } from "@mantine/core";
import { IconPower } from "@tabler/icons-react";
const ICON_SIZE = 20;
const Logout = () => {
  return (
    <>
      <Tooltip label="Logout">
        <ActionIcon className="text-[var(--mantine-color-dark-0)]">
          <IconPower size={ICON_SIZE} />
        </ActionIcon>
      </Tooltip>
      <span className="-mx-3.5 text-[var(--bg-base-200)]">|</span>
    </>
  );
};

export default Logout;
