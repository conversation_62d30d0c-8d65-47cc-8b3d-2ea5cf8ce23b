import React, { useState } from 'react';
import {
  Modal,
  TextInput,
  Button,
  Group,
  Select,
  Text,
  Stack
} from '@mantine/core';
import { pauseAPI } from '@/services/api';
import { notifications } from '@mantine/notifications';

interface PauseModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (pauseData: PauseData) => void;
}

interface PauseData {
  title: string;
  dateFrom: string;
  dateTo: string;
  doctor: string;
}

const PauseModal: React.FC<PauseModalProps> = ({ opened, onClose, onSave }) => {
  const [title, setTitle] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [doctor, setDoctor] = useState('DEMO DEMO');

  const handleSave = async () => {
    try {
      const pauseData: PauseData = {
        title,
        dateFrom,
        dateTo,
        doctor
      };

      // Call API to create pause
      const response = await pauseAPI.create(pauseData);

      // Call parent callback with the created pause data
      onSave(pauseData);

      // Show success notification
      notifications.show({
        title: 'Pause créée',
        message: `Pause "${title}" créée avec succès`,
        color: 'green',
        autoClose: 3000
      });

      onClose();

      // Reset form
      setTitle('');
      setDateFrom('');
      setDateTo('');
      setDoctor('DEMO DEMO');

    } catch (error) {
      console.error('Error creating pause:', error);
      notifications.show({
        title: 'Erreur',
        message: `Erreur lors de la création de la pause: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        color: 'red',
        autoClose: 5000
      });
    }
  };

  const handleCancel = () => {
    onClose();
    // Reset form
    setTitle('');
    setDateFrom('');
    setDateTo('');
    setDoctor('DEMO DEMO');
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
            </svg>
          </div>
          <Text fw={600} c="blue">Ajouter rendez-vous</Text>
          <div className="ml-auto flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
            <Text size="sm" c="blue">Pause</Text>
          </div>
        </div>
      }
      size="md"
      centered
      styles={{
        header: {
          backgroundColor: '#1c7ed6',
          color: 'white',
          padding: '1rem'
        },
        title: {
          color: 'white',
          width: '100%'
        }
      }}
    >
      <Stack gap="md" p="md">
        <TextInput
          label={
            <Text size="sm">
              Titre <span style={{ color: 'red' }}>*</span>
            </Text>
          }
          placeholder="Entrez le titre"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />

        <Group grow>
          <TextInput
            label={
              <Text size="sm">
                Date du pause <span style={{ color: 'red' }}>*</span>
              </Text>
            }
            placeholder="De"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            required
          />
          <TextInput
            label={
              <Text size="sm">
                De <span style={{ color: 'red' }}>*</span>
              </Text>
            }
            placeholder="De"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            required
          />
          <TextInput
            label={
              <Text size="sm">
                à <span style={{ color: 'red' }}>*</span>
              </Text>
            }
            placeholder="à"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            required
          />
        </Group>

        <Select
          label={
            <Text size="sm">
              Docteur <span style={{ color: 'red' }}>*</span>
            </Text>
          }
          placeholder="Sélectionnez un docteur"
          value={doctor}
          onChange={(value) => setDoctor(value || 'DEMO DEMO')}
          data={[
            { value: 'DEMO DEMO', label: 'DEMO DEMO' },
            { value: 'Dr. Smith', label: 'Dr. Smith' },
            { value: 'Dr. Johnson', label: 'Dr. Johnson' },
          ]}
          required
          rightSection={
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          }
        />

        <Group justify="flex-end" mt="md">
          <Button
            variant="light"
            color="gray"
            onClick={handleCancel}
          >
            Annuler
          </Button>
          <Button
            color="red"
            onClick={handleSave}
            disabled={!title || !dateFrom || !dateTo}
          >
            Enregistrer
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default PauseModal;
