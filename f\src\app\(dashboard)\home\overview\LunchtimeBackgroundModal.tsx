import React, { useState, useEffect,  } from 'react';
import {
  Modal,
  TextInput,
  Button,
  Group,
  Select,
  Text,
  Stack,
  NumberInput,
  Textarea,
  Switch,
  Card,
  Badge,
  ActionIcon,
  Divider,
  Alert,
  ColorPicker,
  
} from '@mantine/core';
import { DatePickerInput, TimeInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconClock, IconTrash, IconDeviceFloppy, IconReload, IconInfoCircle, IconUser, IconMapPin } from '@tabler/icons-react';
import { pauseAPI, patientAPI } from '@/services/api';
import { useAuth } from '@/hooks/useAuth';

// Define proper error types to avoid using 'any'
interface ApiError extends Error {
  response?: {
    status?: number;
    data?: unknown;
  };
  message: string;
}

interface BackendError extends Error {
  response?: {
    status?: number;
    data?: unknown;
  };
  message: string;
}

interface ConflictEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  doctorId?: string;
  type?: string;
}

interface LunchtimeTemplate {
  id: string;
  name: string;
  title: string;
  duration: number;
  startTime: string;
  endTime: string;
  doctorId: string;
  doctorName: string;
  roomId: string;
  roomName: string;
  color: string;
  notes?: string;
  isRecurring: boolean;
  createdAt: string;
}

interface StaffOption {
  label: string;
  value: string;
}

interface LunchtimeBackgroundModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (lunchData: LunchEventData) => void;
  currentDate: Date;
  selectedRoom?: string;
  selectedDoctor?: string;
  existingEvents?: CalendarEvent[];
  staffOptions?: StaffOption[]; // Add staff options like DayView
  // ✅ NEW: Edit mode support
  editMode?: boolean;
  initialData?: {
    id?: string;
    title: string;
    startTime: Date;
    duration: number;
    doctorId: string;
    roomId: string;
    color: string;
    notes: string;
    isRecurring: boolean;
  };
}

interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  doctorId?: string;
  doctor?: string;
  roomId?: string;
  resourceId?: string | number;
  lunchTime?: boolean;
}

interface LunchEventData {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color: string;
  roomId: string;
  resourceId: string | number;
  lunchTime: boolean;
  doctorId: string;
  doctorName: string;
  notes?: string;
  isRecurring?: boolean;
}

const LunchtimeBackgroundModal: React.FC<LunchtimeBackgroundModalProps> = ({
  opened,
  onClose,
  onSave,
  currentDate,
  selectedRoom = 'room-a',
  selectedDoctor,
  existingEvents = [],
  staffOptions = [],
  // ✅ NEW: Edit mode props
  editMode = false,
  initialData
}) => {
  // Get current user from auth context
  const { user } = useAuth();
  // Form state
  const [title, setTitle] = useState('🍽️ Pause Déjeuner');
  const [startTime, setStartTime] = useState<Date>(new Date());
  const [duration, setDuration] = useState(60);
  // Default to current user if they are a doctor, otherwise use selectedDoctor
  const [doctorId, setDoctorId] = useState(() => {
    if (user?.user_type === 'doctor' && user?.id) {
      return user.id;
    }
    return selectedDoctor || '';
  });
  const [roomId, setRoomId] = useState(selectedRoom);
  const [color, setColor] = useState('#15AABF');
  const [notes, setNotes] = useState('');
  const [isRecurring, setIsRecurring] = useState(false);
  
  // Template management
  const [savedTemplates, setSavedTemplates] = useState<LunchtimeTemplate[]>([]);
  const [templateName, setTemplateName] = useState('');
  const [showTemplateForm, setShowTemplateForm] = useState(false);

  
  // Data loading
  const [loading, setLoading] = useState(false);

  // Function to resolve doctor name (same as DayView)
  const resolveDoctorName = (doctorValue: string | undefined): string => {
    if (!doctorValue) return 'Dr. Non assigné';

    // Check if it's a UUID (36 chars with hyphens)
    const isUUID = doctorValue.includes('-') && doctorValue.length === 36;

    if (isUUID) {
      // Try to find doctor by UUID in staffOptions
      const foundStaff = staffOptions.find(staff => staff.value === doctorValue);
      if (foundStaff) {
        return foundStaff.label;
      }
      return 'Dr. Non assigné';
    }

    // If not a UUID, try to find by value
    const foundStaff = staffOptions.find(staff => staff.value === doctorValue);
    if (foundStaff) {
      return foundStaff.label;
    }

    // If it's already a readable name, use it
    if (!doctorValue.includes('-')) {
      return doctorValue.startsWith('Dr.') ? doctorValue : `Dr. ${doctorValue}`;
    }

    return 'Dr. Non assigné';
  };

  // Room options
  const roomOptions = [
    { value: 'room-a', label: 'Salle A' },
    { value: 'room-b', label: 'Salle B' }
  ];

  // Initialize start time with current date
  useEffect(() => {
    if (currentDate) {
      const newStartTime = new Date(currentDate);
      newStartTime.setHours(12, 0, 0, 0); // Default to 12:00 PM
      setStartTime(newStartTime);
    }
  }, [currentDate]);

  // ✅ NEW: Load initial data when in edit mode
  useEffect(() => {
    if (editMode && initialData && opened) {
      console.log('🔧 EDIT MODE: Loading initial data:', initialData);
      setTitle(initialData.title);
      setStartTime(initialData.startTime);
      setDuration(initialData.duration);
      setDoctorId(initialData.doctorId);
      setRoomId(initialData.roomId);
      setColor(initialData.color);
      setNotes(initialData.notes);
      setIsRecurring(initialData.isRecurring);
    }
  }, [editMode, initialData, opened]);

  // Load templates and existing lunch breaks when modal opens
  useEffect(() => {
    if (opened) {
      loadTemplates();
      loadExistingLunchBreaks();
      console.log('📅 Existing events for conflict detection:', existingEvents.length);
      console.log('👥 Available staff options:', staffOptions.length, staffOptions);
      console.log('🔍 Staff options data:', JSON.stringify(staffOptions, null, 2));
      
      // Warn if no staff options are available
      if (staffOptions.length === 0) {
        console.warn('⚠️ No staff options available. Please ensure doctors/assistants are loaded.');
        notifications.show({
          title: 'Attention',
          message: 'Aucun médecin disponible. Veuillez recharger la page.',
          color: 'orange'
        });
      }
    }
  }, [opened, existingEvents.length, staffOptions]);

  // Load existing lunch breaks from backend
  const loadExistingLunchBreaks = async () => {
    try {
      console.log('🔍 Loading existing lunch breaks for today...');
      const startDate = new Date(currentDate);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(currentDate);
      endDate.setHours(23, 59, 59, 999);

      // Check if pause API endpoint exists
      try {
        console.log('🔍 Attempting to load pauses from backend...');
        const response = await pauseAPI.list({
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0]
        });

        console.log('✅ Successfully loaded existing lunch breaks:', response.results?.length || 0);
        console.log('🔍 Pause API response structure:', {
          hasResults: !!response.results,
          resultsLength: response.results?.length,
          sampleData: response.results?.[0] || 'No data'
        });
        
        // Convert backend pauses to calendar events for conflict detection
        if (response.results && response.results.length > 0) {
          const backendEvents = response.results.map(pause => ({
            id: pause.id,
            title: pause.title,
            start: new Date(pause.date_from),
            end: new Date(pause.date_to),
            doctorId: pause.doctor,
            lunchTime: true,
            type: 'pause'
          }));
          
          console.log('🍽️ Found existing lunch events from backend:', backendEvents.length);
        }
      } catch (error: unknown) {
        const apiError = error as ApiError;
        console.warn('⚠️ Pause API error details:', {
          message: apiError.message,
          status: apiError.response?.status,
          data: apiError.response?.data,
          fullError: apiError
        });
        
        if (apiError.response?.status === 404 || apiError.message?.includes('Not found') || apiError.message?.includes('404')) {
          console.warn('⚠️ Pause API endpoint not found (404). This may be normal if pause functionality is not implemented.');
          notifications.show({
            title: 'Information',
            message: 'Pause backend not available. Lunch breaks will be saved locally only.',
            color: 'blue',
            autoClose: 4000
          });
        } else {
          console.error('❌ Unexpected pause API error:', apiError);
          notifications.show({
            title: 'Avertissement',
            message: 'Erreur lors de la connexion au backend des pauses. Mode local activé.',
            color: 'orange',
            autoClose: 5000
          });
        }
      }
    } catch (error) {
      console.error('❌ Error loading existing lunch breaks:', error);
      // Only show notification for unexpected errors
      notifications.show({
        title: 'Avertissement',
        message: 'Impossible de charger les pauses existantes. Mode local activé.',
        color: 'orange',
        autoClose: 3000
      });
    }
  };

  // Load saved templates
  const loadTemplates = () => {
    try {
      const saved = localStorage.getItem('lunchtime_templates');
      if (saved) {
        setSavedTemplates(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  // Save template to localStorage
  const saveTemplate = () => {
    if (!templateName.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez saisir un nom pour le modèle',
        color: 'red'
      });
      return;
    }

    const selectedRoomData = roomOptions.find(r => r.value === roomId);
    const doctorFullName = resolveDoctorName(doctorId);

    const template: LunchtimeTemplate = {
      id: Date.now().toString(),
      name: templateName.trim(),
      title,
      duration,
      startTime: startTime.toTimeString().slice(0, 5),
      endTime: new Date(startTime.getTime() + duration * 60000).toTimeString().slice(0, 5),
      doctorId,
      doctorName: doctorFullName,
      roomId,
      roomName: selectedRoomData?.label || 'Salle A',
      color,
      notes,
      isRecurring,
      createdAt: new Date().toISOString()
    };

    const updatedTemplates = [...savedTemplates, template];
    setSavedTemplates(updatedTemplates);
    localStorage.setItem('lunchtime_templates', JSON.stringify(updatedTemplates));

    notifications.show({
      title: 'Modèle sauvegardé',
      message: `Le modèle "${templateName}" a été sauvegardé avec succès`,
      color: 'green'
    });

    setTemplateName('');
    setShowTemplateForm(false);
  };

  // Load template
  const loadTemplate = (templateId: string) => {
    const template = savedTemplates.find(t => t.id === templateId);
    if (template) {
      setTitle(template.title);
      setDuration(template.duration);
      setDoctorId(template.doctorId);
      setRoomId(template.roomId);
      setColor(template.color);
      setNotes(template.notes || '');
      setIsRecurring(template.isRecurring);

      // Set start time based on template time but current date
      const newStartTime = new Date(currentDate);
      const [hours, minutes] = template.startTime.split(':').map(Number);
      newStartTime.setHours(hours, minutes, 0, 0);
      setStartTime(newStartTime);

      notifications.show({
        title: 'Modèle chargé',
        message: `Le modèle "${template.name}" a été appliqué`,
        color: 'blue'
      });
    }
  };

  // Delete template
  const deleteTemplate = (templateId: string) => {
    const updatedTemplates = savedTemplates.filter(t => t.id !== templateId);
    setSavedTemplates(updatedTemplates);
    localStorage.setItem('lunchtime_templates', JSON.stringify(updatedTemplates));

    notifications.show({
      title: 'Modèle supprimé',
      message: 'Le modèle a été supprimé avec succès',
      color: 'orange'
    });
  };

  // Calculate end time
  const endTime = new Date(startTime.getTime() + duration * 60000);

  // Enhanced conflict detection function with backend integration
  const checkForConflicts = async (proposedStart: Date, proposedEnd: Date, proposedDoctorId: string, proposedRoomId: string) => {
    const conflicts = [];
    
    // Check frontend events (existing calendar events)
    const frontendConflicts = existingEvents.filter(event => {
      // Check if it's the same doctor or same room
      const sameDoctor = event.doctorId === proposedDoctorId || event.doctor === proposedDoctorId;
      const sameRoom = event.roomId === proposedRoomId ||
                      (event.resourceId === 1 && proposedRoomId === 'room-a') ||
                      (event.resourceId === 2 && proposedRoomId === 'room-b');

      if (!sameDoctor && !sameRoom) return false;

      // Check for time overlap
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      const hasOverlap = (proposedStart < eventEnd && proposedEnd > eventStart);

      return hasOverlap;
    });
    
    conflicts.push(...frontendConflicts);
    
    // Check backend pauses for additional conflicts
    try {
      const startDate = proposedStart.toISOString().split('T')[0];
      const endDate = proposedEnd.toISOString().split('T')[0];
      
      const backendPauses = await pauseAPI.list({
        doctor_id: proposedDoctorId,
        start_date: startDate,
        end_date: endDate
      });
      
      if (backendPauses.results) {
        const backendConflicts = backendPauses.results.filter(pause => {
          const pauseStart = new Date(pause.date_from);
          const pauseEnd = new Date(pause.date_to);
          
          // Check for time overlap
          const hasOverlap = (proposedStart < pauseEnd && proposedEnd > pauseStart);
          return hasOverlap;
        });
        
        // Convert backend pauses to conflict format
        const convertedBackendConflicts = backendConflicts.map(pause => ({
          id: pause.id,
          title: pause.title || 'Pause',
          start: new Date(pause.date_from),
          end: new Date(pause.date_to),
          doctorId: pause.doctor,
          type: 'backend-pause'
        }));
        
        conflicts.push(...convertedBackendConflicts);
      }
    } catch (error: unknown) {
      const typedError = error as Error;
      if (typedError.message?.includes('Not found') || typedError.message?.includes('404')) {
        console.log('ℹ️ Backend pause checking not available, using frontend-only conflict detection');
      } else {
        console.warn('⚠️ Could not check backend conflicts:', typedError);
      }
      // Continue with frontend-only conflict detection
    }
    
    return conflicts;
  };

  // Handle save with proper error handling and persistence
  const handleSave = async () => {
    if (!doctorId) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez sélectionner un médecin',
        color: 'red'
      });
      return;
    }

    // Enhanced conflict checking with backend integration
    const conflicts = await checkForConflicts(startTime, endTime, doctorId, roomId);
    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(event => {
        const eventType = (event as ConflictEvent).type === 'backend-pause' ? '(Pause backend)' : '(Rendez-vous)';
        return `• ${event.title} ${eventType} (${new Date(event.start).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })} - ${new Date(event.end).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })})`;
      }).join('\n');

      const shouldContinue = window.confirm(
        `Conflit détecté!\n\nIl y a déjà des événements programmés à ce moment:\n${conflictMessages}\n\nVoulez-vous continuer malgré le conflit?`
      );
      
      if (!shouldContinue) {
        return;
      }
      
      notifications.show({
        title: 'Conflit ignoré',
        message: 'La pause a été créée malgré le conflit détecté',
        color: 'orange',
        autoClose: 5000
      });
    }

    setLoading(true);

    try {
      const doctorFullName = resolveDoctorName(doctorId);

      // First, try to save to backend - this is the primary source of truth
      let backendPauseId = null;
      try {
        const pauseData = {
          doctor: doctorId,
          title,
          dateFrom: startTime.toISOString(),
          dateTo: endTime.toISOString(),
          room: roomId,
          resource_id: roomId === 'room-a' ? '1' : '2',
          color,
          notes: notes || `Pause déjeuner - ${roomOptions.find(r => r.value === roomId)?.label}`,
          is_recurring: isRecurring
        };

        // ✅ NEW: Check if we're in edit mode
        if (editMode && initialData) {
          console.log('🔧 EDIT MODE: Updating existing pause with pauseAPI.update');
          // Get the pause ID from the initialData
          const pauseId = initialData.id;
          console.log('🔧 Pause ID for update:', pauseId);
          
          if (!pauseId) {
            throw new Error('Pause ID not found for edit operation');
          }
          
          // Call pauseAPI.update instead of pauseAPI.create
          const backendResponse = await pauseAPI.update(pauseId, pauseData);
          backendPauseId = pauseId;
          console.log('✅ Lunch break updated in backend successfully:', backendPauseId);
        } else {
          // Create new pause
          console.log('🎆 CREATE MODE: Creating new pause with pauseAPI.create');
          const backendResponse = await pauseAPI.create(pauseData);
          backendPauseId = backendResponse.id;
          console.log('✅ Lunch break saved to backend successfully:', backendPauseId);
        }
        
      } catch (error: unknown) {
        const backendError = error as BackendError;
        console.error('❌ Backend save failed:', backendError);
        
        if (backendError.message?.includes('Not found') || backendError.message?.includes('404')) {
          console.warn('⚠️ Backend pause API not available, using frontend-only save');
          notifications.show({
            title: 'Mode local',
            message: 'Pause sauvegardée localement (API pause non disponible)',
            color: 'blue',
            autoClose: 3000
          });
        } else {
          // Show error and ask user if they want to continue with frontend-only save
          const shouldContinue = window.confirm(
            'Impossible de sauvegarder sur le serveur. Voulez-vous continuer avec une sauvegarde locale uniquement?\n\n' +
            'Attention: La pause sera perdue lors du rechargement de la page.'
          );
          
          if (!shouldContinue) {
            setLoading(false);
            return;
          }
          
          notifications.show({
            title: 'Sauvegarde locale uniquement',
            message: 'La pause n\'a pu être sauvegardée que localement',
            color: 'orange',
            autoClose: 5000
          });
        }
      }

      // Create frontend event data for immediate UI update
      const lunchEventData: LunchEventData = {
        id: backendPauseId || Date.now().toString(),
        title,
        start: startTime,
        end: endTime,
        desc: `Pause déjeuner - ${doctorFullName} - ${roomOptions.find(r => r.value === roomId)?.label}${notes ? ` - ${notes}` : ''}`,
        color,
        roomId,
        resourceId: roomId === 'room-a' ? 1 : 2,
        lunchTime: true,
        doctorId,
        doctorName: doctorFullName,
        notes,
        isRecurring
      };

      // Call parent callback to update UI
      onSave(lunchEventData);

      notifications.show({
        title: 'Pause déjeuner ajoutée',
        message: `Pause déjeuner programmée pour ${doctorFullName} en ${roomOptions.find(r => r.value === roomId)?.label}`,
        color: 'green'
      });

      handleClose();
    } catch (error) {
      console.error('Error saving lunch break:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la sauvegarde de la pause déjeuner',
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    setTitle('🍽️ Pause Déjeuner');
    setDuration(60);
    // Reset to current user if they are a doctor, otherwise use selectedDoctor
    if (user?.user_type === 'doctor' && user?.id) {
      setDoctorId(user.id);
    } else {
      setDoctorId(selectedDoctor || '');
    }
    setRoomId(selectedRoom);
    setColor('#15AABF');
    setNotes('');
    setIsRecurring(false);
    setTemplateName('');
    setShowTemplateForm(false);
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group gap="sm">
          <IconClock size={20} color="#15AABF" />
          <Text fw={600} c="#15AABF">
            {editMode ? 'Modifier la Pause Déjeuner' : 'Planifier une Pause Déjeuner'}
          </Text>
        </Group>
      }
      size="lg"
      centered
    >
      <Stack gap="md">
        {/* Alert info */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          Planifiez une pause déjeuner pour un médecin dans une salle spécifique. 
          Vous pouvez sauvegarder des modèles pour une utilisation future.
        </Alert>

        {/* Template management */}
        <Card withBorder>
          <Stack gap="sm">
            <Group justify="space-between">
              <Text fw={500}>Modèles sauvegardés</Text>
              <Button
                size="xs"
                variant="light"
                onClick={() => setShowTemplateForm(!showTemplateForm)}
              >
                {showTemplateForm ? 'Annuler' : 'Nouveau modèle'}
              </Button>
            </Group>

            {showTemplateForm && (
              <Group gap="sm">
                <TextInput
                  placeholder="Nom du modèle"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  style={{ flex: 1 }}
                />
                <Button size="xs" onClick={saveTemplate} leftSection={<IconDeviceFloppy size={14} />}>
                  Sauvegarder
                </Button>
              </Group>
            )}

            {savedTemplates.length > 0 && (
              <Stack gap="xs">
                {savedTemplates.map((template) => (
                  <Card key={template.id} withBorder p="xs">
                    <Group justify="space-between">
                      <div>
                        <Text size="sm" fw={500}>{template.name}</Text>
                        <Text size="xs" c="dimmed">
                          {template.doctorName} • {template.roomName} • {template.startTime}-{template.endTime}
                        </Text>
                      </div>
                      <Group gap="xs">
                        <ActionIcon
                          size="sm"
                          variant="light"
                          color="blue"
                          onClick={() => loadTemplate(template.id)}
                        >
                          <IconReload size={14} />
                        </ActionIcon>
                        <ActionIcon
                          size="sm"
                          variant="light"
                          color="red"
                          onClick={() => deleteTemplate(template.id)}
                        >
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Group>
                  </Card>
                ))}
              </Stack>
            )}
          </Stack>
        </Card>

        <Divider />

        {/* Form fields */}
        <TextInput
          label="Titre de la pause"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="🍽️ Pause Déjeuner"
          required
        />

        <Group grow>
          <DatePickerInput
            label="Date"
            value={startTime}
            onChange={(date) => {
              if (date) {
                const newDateTime = new Date(date);
                newDateTime.setHours(startTime.getHours(), startTime.getMinutes());
                setStartTime(newDateTime);
              }
            }}
            required
          />
          <TimeInput
            label="Heure"
            value={startTime.toTimeString().slice(0, 5)}
            onChange={(event) => {
              const timeValue = event.currentTarget.value;
              if (timeValue) {
                const [hours, minutes] = timeValue.split(':').map(Number);
                const newDateTime = new Date(startTime);
                newDateTime.setHours(hours, minutes);
                setStartTime(newDateTime);
              }
            }}
            required
          />
        </Group>

        <NumberInput
          label="Durée (minutes)"
          value={duration}
          onChange={(value) => setDuration(Number(value) || 60)}
          min={15}
          max={180}
          step={15}
          required
        />

        <Group grow>
          <Select
            label="Médecin"
            placeholder={staffOptions.length > 0 ? "Sélectionner un médecin" : "Chargement des médecins..."}
            value={doctorId}
            onChange={(value) => {
              console.log('🔍 Doctor selected:', value);
              setDoctorId(value || '');
            }}
            data={staffOptions}
            leftSection={<IconUser size={16} />}
            required
            disabled={staffOptions.length === 0}
            onDropdownOpen={() => {
              console.log('🔍 Dropdown opened, staffOptions:', staffOptions);
              if (staffOptions.length === 0) {
                notifications.show({
                  title: 'Aucun médecin disponible',
                  message: 'Veuillez vérifier que les médecins sont chargés correctement.',
                  color: 'orange'
                });
              }
            }}
          />

          <Select
            label="Salle"
            value={roomId}
            onChange={(value) => setRoomId(value || 'room-a')}
            data={roomOptions}
            leftSection={<IconMapPin size={16} />}
            required
          />
        </Group>

        <Group gap="sm" align="flex-end">
          <div style={{ flex: 1 }}>
            <Text size="sm" fw={500} mb={5}>Couleur</Text>
            <ColorPicker
              value={color}
              onChange={setColor}
              format="hex"
              size="sm"
            />
          </div>
          <Badge color={color} variant="filled" size="lg">
            Aperçu
          </Badge>
        </Group>

        <Textarea
          label="Notes (optionnel)"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Informations supplémentaires..."
          rows={2}
        />

        <Switch
          label="Pause récurrente"
          description="Sauvegarder comme pause récurrente dans le système"
          checked={isRecurring}
          onChange={(e) => setIsRecurring(e.currentTarget.checked)}
        />

        {/* Summary */}
        <Card withBorder bg="gray.0">
          <Text size="sm" fw={500} mb="xs">Résumé</Text>
          <Text size="sm">
            <strong>Médecin:</strong> {resolveDoctorName(doctorId) || 'Non sélectionné'}<br />
            <strong>Salle:</strong> {roomOptions.find(r => r.value === roomId)?.label}<br />
            <strong>Horaire:</strong> {startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })} - {endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}<br />
            <strong>Durée:</strong> {duration} minutes
          </Text>
        </Card>

        {/* Action buttons */}
        <Group justify="flex-end" gap="sm">
          <Button variant="outline" onClick={handleClose}>
            Annuler
          </Button>
          <Button
            onClick={handleSave}
            loading={loading}
            disabled={!doctorId}
            color="#15AABF"
          >
            Planifier la pause
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default LunchtimeBackgroundModal;
