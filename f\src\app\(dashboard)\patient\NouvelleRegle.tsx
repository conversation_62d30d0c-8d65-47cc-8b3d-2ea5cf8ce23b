import React, { useState } from "react";
import {
  Tabs,
  Radio,
  RadioGroup,
  Select,
  Button,
  Group,
  Text,
  Stack,
} from "@mantine/core";

// Remplacer par import mdi react icons
import { Icon } from '@mdi/react';
import { mdiFormatBold, mdiFormatItalic, mdiFormatUnderline, mdiFormatStrikethrough, mdiFormatColorText, mdiFormatColorFill } from '@mdi/js';

type Column = {
  order_by: string;
  label: string;
  is_shown: boolean;
};

type StyleRule = {
  uid: string;
  type: "COLUMN" | "ROW";
  target_column: string;
  condition: string;
  style: {
    color?: string;
    "background-color"?: string;
    "font-weight"?: string;
    "font-style"?: string;
    "text-decoration"?: string;
  };
};

interface StyleRulesTabProps {
  columns: Column[];
  styleRules: StyleRule[];
  isCreate: boolean;
  onCancel: () => void;
  onSave: (rule: StyleRule) => void;
}

export function NouvelleRegle({
  columns,
  isCreate,
  onCancel,
  onSave,
}: StyleRulesTabProps) {
  // State local pour la règle en création
  const [rule, setRule] = useState<StyleRule>({
    uid: crypto.randomUUID(),
    type: "COLUMN",
    target_column: "",
    condition: "",
    style: {},
  });

  const handleStyleChange = (key: string, value: string) => {
    setRule((r) => ({
      ...r,
      style: {
        ...r.style,
        [key]: value,
      },
    }));
  };

  return (
    <Tabs.Panel value="1" pt="xs">
      {isCreate ? (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            onSave(rule);
          }}
        >
          <Stack gap="sm">
            <Text fw={500}>Type</Text>
            <RadioGroup
              value={rule.type}
              onChange={(value) =>
                setRule((r) => ({ ...r, type: value as "COLUMN" | "ROW" }))
              }
            >
              <Radio value="COLUMN" label="Colonne" />
              <Radio value="ROW" label="Ligne" />
            </RadioGroup>

            <Select
              label="Appliquer à"
              placeholder="Appliquer à"
              required
              value={rule.target_column}
              onChange={(value) =>
                setRule((r) => ({ ...r, target_column: value || "" }))
              }
              data={columns
                .filter((c) => c.is_shown)
                .map((c) => ({ value: c.order_by, label: c.label }))}
            />

            <Select
              label="Condition"
              placeholder="Condition"
              disabled={!rule.target_column}
              required
              value={rule.condition}
              onChange={(value) =>
                setRule((r) => ({ ...r, condition: value || "" }))
              }
              data={["Condition1", "Condition2"]} // à remplacer par vrai tableau conditions
            />

            <Text fw={500}>Style à appliquer</Text>
            <Group gap="xs">
              <Button
                variant={rule.style["font-weight"] === "bold" ? "filled" : "outline"}
                onClick={() => handleStyleChange("font-weight", "bold")}
                aria-label="font weight"
                size="xs"
              >
                <Icon path={mdiFormatBold} size={1} />
              </Button>
              <Button
                variant={rule.style["font-style"] === "italic" ? "filled" : "outline"}
                onClick={() => handleStyleChange("font-style", "italic")}
                aria-label="font italic"
                size="xs"
              >
                <Icon path={mdiFormatItalic} size={1} />
              </Button>
              <Button
                variant={
                  rule.style["text-decoration"] === "underline"
                    ? "filled"
                    : "outline"
                }
                onClick={() => handleStyleChange("text-decoration", "underline")}
                aria-label="font underline"
                size="xs"
              >
                <Icon path={mdiFormatUnderline} size={1} />
              </Button>
              <Button
                variant={
                  rule.style["text-decoration"] === "line-through"
                    ? "filled"
                    : "outline"
                }
                onClick={() => handleStyleChange("text-decoration", "line-through")}
                aria-label="font strikethrough"
                size="xs"
              >
                <Icon path={mdiFormatStrikethrough} size={1} />
              </Button>
              <Button
                variant="outline"
                aria-label="font color"
                size="xs"
                style={{ color: rule.style.color }}
                onClick={() => {
                  // ouvrir picker couleur à implémenter
                }}
              >
                <Icon path={mdiFormatColorText} size={1} />
              </Button>
              <Button
                variant="outline"
                aria-label="font fill"
                size="xs"
                style={{ color: rule.style["background-color"] }}
                onClick={() => {
                  // ouvrir picker couleur à implémenter
                }}
              >
                <Icon path={mdiFormatColorFill} size={1} />
              </Button>
            </Group>

            <Group justify="flex-end" gap="sm" mt="md">
              <Button color="red" onClick={onCancel}>
                Annuler
              </Button>
              <Button type="submit" disabled={!rule.target_column || !rule.condition}>
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      ) : (
        <Text>Aucune règle en création</Text>
      )}
    </Tabs.Panel>
  );
}
