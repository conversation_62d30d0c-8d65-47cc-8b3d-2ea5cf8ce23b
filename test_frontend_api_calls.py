#!/usr/bin/env python3

import requests
import json

def test_frontend_api_calls():
    """Test the API calls that the frontend is making"""
    
    base_url = 'http://127.0.0.1:8000/api'
    
    # Get a test appointment
    print('🔍 Getting test appointment...')
    response = requests.get(f'{base_url}/appointments/?limit=1')
    if response.status_code != 200:
        print(f'❌ Failed to get appointments: {response.status_code}')
        return
    
    data = response.json()
    if not data.get('results'):
        print('❌ No appointments found')
        return
    
    appointment = data['results'][0]
    appointment_id = appointment['id']
    print(f'✅ Using appointment: {appointment_id} - {appointment.get("title", "No title")}')
    
    # Test the exact API calls that the frontend makes
    test_cases = [
        {
            'name': 'Add to Waiting List',
            'method': 'PATCH',
            'endpoint': f'/appointments/{appointment_id}/',
            'data': {
                'is_waiting_list': True,
                'status': 'waiting_list'
            }
        },
        {
            'name': 'Add to Presentation Room',
            'method': 'PATCH',
            'endpoint': f'/appointments/{appointment_id}/',
            'data': {
                'is_in_presentation_room': True,
                'status': 'in_progress'
            }
        },
        {
            'name': 'Add to Active Visits',
            'method': 'PATCH',
            'endpoint': f'/appointments/{appointment_id}/',
            'data': {
                'is_active': True,
                'status': 'in_progress'
            }
        },
        {
            'name': 'Complete Appointment',
            'method': 'PATCH',
            'endpoint': f'/appointments/{appointment_id}/',
            'data': {
                'is_in_history_journal': True,
                'is_active': False,
                'is_in_presentation_room': False,
                'status': 'completed',
                'notes': 'Test completion'
            }
        }
    ]
    
    print('\n🧪 Testing API calls...')
    for test_case in test_cases:
        print(f'\n📝 Testing: {test_case["name"]}')
        print(f'   URL: {base_url}{test_case["endpoint"]}')
        print(f'   Data: {json.dumps(test_case["data"], indent=2)}')
        
        response = requests.patch(
            f'{base_url}{test_case["endpoint"]}',
            json=test_case["data"],
            headers={'Content-Type': 'application/json'}
        )
        
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'   ✅ Success')
            print(f'   Response fields: {list(result.keys())[:10]}...')
        else:
            print(f'   ❌ Failed')
            print(f'   Error: {response.text[:200]}...')
    
    # Test getting appointments by state
    print('\n🔍 Testing state-based queries...')
    state_queries = [
        {'is_waiting_list': 'true'},
        {'is_in_presentation_room': 'true'},
        {'is_active': 'true'},
        {'is_in_history_journal': 'true'}
    ]
    
    for query in state_queries:
        query_string = '&'.join([f'{k}={v}' for k, v in query.items()])
        url = f'{base_url}/appointments/?{query_string}'
        print(f'\n📊 Query: {query_string}')
        
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            count = data.get('count', 0)
            print(f'   ✅ Found {count} appointments')
        else:
            print(f'   ❌ Failed: {response.status_code}')

if __name__ == '__main__':
    test_frontend_api_calls()
