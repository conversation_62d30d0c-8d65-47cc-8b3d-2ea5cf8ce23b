/**
 * Medical Report Quick Access Modal
 * Provides quick access to medical reports and billing from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  SimpleGrid,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconReportMedical,
  IconReceipt,
  IconFileText,
  IconContract,
  IconCurrencyEuro,
  IconExternalLink,
  IconRefresh,
  IconPlus,
  IconEye,
  IconEdit,
} from '@tabler/icons-react';
import { useMedicalReport } from '@/hooks/useMedicalReport';
import MedicalReportWidgets from './MedicalReportWidgets';

// Import existing medical-report components
import { ComptesRendus } from '@/app/(dashboard)/medical-report/list/Comptes_rendus';
import { FacturationList } from '@/app/(dashboard)/medical-report/billing/FacturationList';
import { DevisList } from '@/app/(dashboard)/medical-report/billing/DevisList';
import { ContratsList } from '@/app/(dashboard)/medical-report/billing/ContratsList';

interface MedicalReportQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  patientId?: string;
  patientName?: string;
  defaultTab?: 'dashboard' | 'reports' | 'billing' | 'quotes' | 'contracts';
  dateRange?: { start: string; end: string };
  onNavigateToFullPage?: () => void;
}

const MedicalReportQuickAccess: React.FC<MedicalReportQuickAccessProps> = ({
  opened,
  onClose,
  patientId,
  patientName,
  defaultTab = 'dashboard',
  dateRange,
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [reportFilter, setReportFilter] = useState<string>('all');

  const {
    examReports,
    billingInvoices,
    billingQuotes,
    contracts,
    templates,
    loading,
    refreshAll,
    getPatientMedicalStats,
    getBillingStats,
  } = useMedicalReport({ 
    patientId, 
    dateRange, 
    autoFetch: opened,
    reportTypes: ['reports', 'billing', 'contracts']
  });

  const patientStats = patientId ? getPatientMedicalStats(patientId) : null;
  const billingStats = getBillingStats();

  const handleRefresh = () => {
    refreshAll(patientId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <MedicalReportWidgets 
              patientId={patientId}
              dateRange={dateRange}
              compact={false}
              showBilling={true}
            />
          </Stack>
        );

      case 'reports':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconReportMedical size={20} />
                  <Text fw={600}>Rapports Médicaux</Text>
                  <Badge color="blue">{examReports.length}</Badge>
                </Group>
                <Group gap="xs">
                  <Select
                    value={reportFilter}
                    onChange={(value) => setReportFilter(value || 'all')}
                    data={[
                      { value: 'all', label: 'Tous' },
                      { value: 'completed', label: 'Complétés' },
                      { value: 'draft', label: 'Brouillons' },
                      { value: 'reviewed', label: 'Révisés' },
                    ]}
                    size="xs"
                  />
                  <ActionIcon variant="light" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {examReports
                    .filter(report => reportFilter === 'all' || report.status === reportFilter)
                    .map((report) => (
                    <Card key={report.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{report.template_title}</Text>
                          <Text size="xs" c="dimmed">
                            {report.patient_name} - {new Date(report.exam_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">Dr. {report.doctor_name}</Text>
                          {report.diagnosis && (
                            <Text size="xs" mt="xs">{report.diagnosis}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={
                              report.status === 'completed' ? 'green' : 
                              report.status === 'draft' ? 'yellow' : 
                              report.status === 'reviewed' ? 'blue' : 'gray'
                            }
                          >
                            {report.status}
                          </Badge>
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {examReports.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun rapport médical disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'billing':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconReceipt size={20} />
                  <Text fw={600}>Facturation</Text>
                  <Badge color="green">{billingInvoices.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {billingInvoices.map((invoice) => (
                    <Card key={invoice.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{invoice.invoice_number}</Text>
                          <Text size="xs" c="dimmed">
                            {invoice.patient_name} - {new Date(invoice.issue_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Échéance: {new Date(invoice.due_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600}>{invoice.total_amount}€</Text>
                            <Text size="xs" c="dimmed">Payé: {invoice.paid_amount}€</Text>
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              invoice.status === 'paid' ? 'green' : 
                              invoice.status === 'overdue' ? 'red' : 
                              invoice.status === 'sent' ? 'blue' : 'yellow'
                            }
                          >
                            {invoice.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {billingInvoices.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune facture disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'quotes':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconFileText size={20} />
                  <Text fw={600}>Devis</Text>
                  <Badge color="orange">{billingQuotes.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {billingQuotes.map((quote) => (
                    <Card key={quote.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{quote.quote_number}</Text>
                          <Text size="xs" c="dimmed">
                            {quote.patient_name} - {new Date(quote.issue_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Expire: {new Date(quote.expiry_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Group gap="xs">
                          <Text size="sm" fw={600}>{quote.total_amount}€</Text>
                          <Badge 
                            size="sm" 
                            color={
                              quote.status === 'accepted' ? 'green' : 
                              quote.status === 'rejected' ? 'red' : 
                              quote.status === 'expired' ? 'gray' : 
                              quote.status === 'sent' ? 'blue' : 'yellow'
                            }
                          >
                            {quote.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {billingQuotes.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun devis disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'contracts':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconContract size={20} />
                  <Text fw={600}>Contrats</Text>
                  <Badge color="purple">{contracts.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {contracts.map((contract) => (
                    <Card key={contract.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{contract.contract_number}</Text>
                          <Text size="xs" c="dimmed">
                            {contract.patient_name} - {contract.contract_type}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {new Date(contract.start_date).toLocaleDateString()} - {new Date(contract.end_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Group gap="xs">
                          <div style={{ textAlign: 'right' }}>
                            <Text size="sm" fw={600}>{contract.total_value}€</Text>
                            {contract.monthly_amount && (
                              <Text size="xs" c="dimmed">{contract.monthly_amount}€/mois</Text>
                            )}
                          </div>
                          <Badge 
                            size="sm" 
                            color={
                              contract.status === 'active' ? 'green' : 
                              contract.status === 'expired' ? 'red' : 
                              contract.status === 'cancelled' ? 'gray' : 'yellow'
                            }
                          >
                            {contract.status}
                          </Badge>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {contracts.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun contrat disponible
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconReportMedical size={20} />
          <Text fw={600}>Rapports Médicaux</Text>
          {patientName && <Text c="dimmed">- {patientName}</Text>}
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Accès rapide aux rapports médicaux et facturation
          </Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconReportMedical size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Rapports</Text>
                <Text size="sm" fw={600}>{examReports.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconReceipt size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Factures</Text>
                <Text size="sm" fw={600}>{billingInvoices.length}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconCurrencyEuro size={16} color="teal" />
              <div>
                <Text size="xs" c="dimmed">Revenus</Text>
                <Text size="sm" fw={600}>{billingStats.totalRevenue}€</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconContract size={16} color="purple" />
              <div>
                <Text size="xs" c="dimmed">Contrats</Text>
                <Text size="sm" fw={600}>{contracts.length}</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconReportMedical size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab 
              value="reports" 
              leftSection={<IconReportMedical size={16} />}
              rightSection={<Badge size="xs" color="blue">{examReports.length}</Badge>}
            >
              Rapports
            </Tabs.Tab>
            <Tabs.Tab 
              value="billing" 
              leftSection={<IconReceipt size={16} />}
              rightSection={<Badge size="xs" color="green">{billingInvoices.length}</Badge>}
            >
              Facturation
            </Tabs.Tab>
            <Tabs.Tab 
              value="quotes" 
              leftSection={<IconFileText size={16} />}
              rightSection={<Badge size="xs" color="orange">{billingQuotes.length}</Badge>}
            >
              Devis
            </Tabs.Tab>
            <Tabs.Tab 
              value="contracts" 
              leftSection={<IconContract size={16} />}
              rightSection={<Badge size="xs" color="purple">{contracts.length}</Badge>}
            >
              Contrats
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default MedicalReportQuickAccess;
