'use client';
import React from 'react';
import {
  Title, Text, Paper, Button, Group, ThemeIcon, Select
} from '@mantine/core';
import { IconFileImport, IconFileExport, IconCheck, IconAlertCircle } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import dataMigrationService from '@/services/dataMigrationService';
import DataMigrationPanel from '@/components/data-migration/DataMigrationPanel';

const DataMigrationCard: React.FC = () => {
  const handleExportData = async () => {
    // Get the selected format
    const formatSelect = document.getElementById('export-format') as HTMLSelectElement;
    const format = formatSelect?.value || 'json';
    
    notifications.show({
      title: 'Exporting Data',
      message: 'Your patient data is being prepared for export. This may take a moment.',
      loading: true,
      autoClose: false,
      id: 'export-notification',
    });

    try {
      // Export patient data in the selected format
      const blob = await dataMigrationService.exportPatientData(format as 'json' | 'xlsx' | 'pdf');

      // Determine the file extension based on the format
      let fileExtension = 'json';
      if (format === 'xlsx') fileExtension = 'xlsx';
      if (format === 'pdf') fileExtension = 'pdf';

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `patient_data_${new Date().toISOString().split('T')[0]}.${fileExtension}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      notifications.update({
        id: 'export-notification',
        title: 'Export Complete',
        message: 'Your patient data has been exported successfully.',
        color: 'green',
        icon: <IconCheck size={16} />,
        loading: false,
        autoClose: 5000,
      });
    } catch (error) {
      console.error('Error exporting patient data:', error);
      notifications.update({
        id: 'export-notification',
        title: 'Export Failed',
        message: 'There was an error exporting your patient data. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
        loading: false,
        autoClose: 5000,
      });
    }
  };

  return (
    <Paper withBorder p="xl" radius="md" mb="xl">
      <Group justify="space-between">
        <div>
          <Group mb="xs">
            <ThemeIcon size={32} radius="md" color="teal">
              <IconFileImport size={20} />
            </ThemeIcon>
            <Title order={3}>Data Migration</Title>
          </Group>
          <Text c="dimmed">Import data from other systems or export your data</Text>
        </div>
        <Group>
          <Select
            placeholder="Select format"
            data={[
              { value: 'json', label: 'JSON' },
              { value: 'xlsx', label: 'Excel (.xlsx)' },
              { value: 'pdf', label: 'PDF' },
            ]}
            defaultValue="json"
            style={{ width: 150 }}
            id="export-format"
          />
          <Button
            variant="outline"
            leftSection={<IconFileExport size={16} />}
            onClick={handleExportData}
          >
            Export Data
          </Button>
        </Group>
      </Group>

      <Text mt="md" mb="lg">
        Migrate your patient data from App X or other systems, or manage location data for your practice.
      </Text>

      <DataMigrationPanel />
    </Paper>
  );
};

export default DataMigrationCard;
