1-> Activate the button to transfer the event between room A and room B directly and vice versa without showing the modification model in fille :\projects\f\src\app\(dashboard)\home\overview\This_Day.tsx 2-> When activating the Add to Waiting List button in the edit form, the checkbox is not activated or selected in the background.
http://127.0.0.1:8000/admin/appointments/appointment/977562fd-f0f0-48a0-ba5f-012261fb0eee/change/?_changelist_filters=appointment_date__day%3D7%26appointment_date__month%3D9%26appointment_date__year%3D2025
In Event Settings->Add to waiting list, the event is not displayed in the waitingList and does not appear in the calendar, and vice versa when the button is clicked.
<Menu.Item
leftSection={
<IconCalendarPlus stroke={2} size={14} className="mt-1" color="blue" />
}
onClick={(clickEvent) => {
clickEvent.stopPropagation();
moveToCalendarWithOptions(patient, { addToWaitingList: true });
}}
color="blue"
>
Add to calendar
</Menu.Item> You must remove the selection from Add to waiting list in the background, remove the limit from waitinglist, and display it in calendar 3-> Activate the button <Menu.Item leftSection={<MenuIcons.lastVisit size={16} />} >
In the audience room
</Menu.Item> so that when you click on it, the event will be displayed in waitingRoomVisits and a checkbox will be activated or selected in the background
http://127.0.0.1:8000/admin/appointments/appointment/977562fd-f0f0-48a0-ba5f-012261fb0eee/change/?_changelist_filters=appointment_date__day%3D7%26appointment_date__month%3D9%26appointment_date__year%3D2025
Is in the presentation room and when you click on the button <span onClick={() => moveToActiveVisits(visit)}> is moved to activeVisits, and the Is in presentation room in the background is unchecked or enabled. When you press the <Menu.Item
leftSection={
<Icon path={mdiPower} size={0.65} className="mt-1" color="#3799CE" /> button, click <Menu.Item> >
Terminate visit
</Menu.Item> The event is displayed in the history journal, and the Is in history journal is enabled or selected in the background.4->Emails should not be repeated even if the user is the same. To ensure that the email owner is the same, you must check or compare the entries first_name, last_name, phone_number, address, birth_date, gender, cin, and email with the information in the background. There must be at least 5 matches between the entered information and the stored information to accept the email. Duplicate emails cannot be accepted.I hope you can help me answer the questions one by one without ignoring the test.