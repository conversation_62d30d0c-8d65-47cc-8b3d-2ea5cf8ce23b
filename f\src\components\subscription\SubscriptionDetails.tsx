'use client';

import { useState, useEffect } from 'react';
import { Card, Text, Group, Stack, Badge, Progress, Button, Divider, List, ThemeIcon, rem, Alert, Loader, Center } from '@mantine/core';
import { IconCheck, IconAlertCircle, IconCalendar } from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import subscriptionService, { DoctorSubscription } from '~/services/subscriptionService';
import { AxiosError } from 'axios';

interface SubscriptionDetailsProps {
  subscriptionId?: number;
  onRenew?: () => void;
  onCancel?: () => void;
}

export default function SubscriptionDetails({
  subscriptionId,
  onRenew,
  onCancel
}: SubscriptionDetailsProps) {
  const [subscription, setSubscription] = useState<DoctorSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        setError(null);

        let data: DoctorSubscription;

        if (subscriptionId) {
          const subscriptions = await subscriptionService.getSubscriptions();
          const foundSubscription = subscriptions.find(s => s.id === subscriptionId);

          if (!foundSubscription) {
            throw new Error('Subscription not found');
          }

          data = foundSubscription;
        } else {
          data = await subscriptionService.getCurrentSubscription();
        }

        setSubscription(data);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        const axiosError = err as AxiosError<{ detail: string }>;
        setError(axiosError.response?.data?.detail || 'Failed to load subscription details');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();
  }, [subscriptionId]);

  const handleRenew = async () => {
    if (!subscription) return;

    try {
      const renewed = await subscriptionService.renewSubscription(subscription.id);
      setSubscription(renewed);
      if (onRenew) onRenew();
    } catch (err) {
      console.error('Error renewing subscription:', err);
      const axiosError = err as AxiosError<{ detail: string }>;
      setError(axiosError.response?.data?.detail || 'Failed to renew subscription');
    }
  };

  const handleCancel = () => {
    if (!subscription) return;

    modals.openConfirmModal({
      title: 'Cancel Subscription',
      children: (
        <Text size="sm">
          Are you sure you want to cancel your subscription? You will lose access to premium features
          when your current billing period ends.
        </Text>
      ),
      labels: { confirm: 'Cancel Subscription', cancel: 'Keep Subscription' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const cancelled = await subscriptionService.cancelSubscription(subscription.id);
          setSubscription(cancelled);
          if (onCancel) onCancel();
        } catch (err) {
          console.error('Error cancelling subscription:', err);
          const axiosError = err as AxiosError<{ detail: string }>;
          setError(axiosError.response?.data?.detail || 'Failed to cancel subscription');
        }
      },
    });
  };

  if (loading) {
    return (
      <Center style={{ height: '200px' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  if (!subscription) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} title="No Active Subscription" color="yellow">
        You don&apos;t have an active subscription. Please subscribe to a plan to access premium features.
      </Alert>
    );
  }

  const { package_details: pkg, days_remaining, status, billing_cycle, end_date } = subscription;
  const totalDays = billing_cycle === 'monthly' ? 30 : 365;
  const progressPercentage = Math.max(0, Math.min(100, (days_remaining / totalDays) * 100));

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack>
        <Group justify="space-between">
          <div>
            <Text fw={700} size="lg">{pkg.name} Plan</Text>
            <Text size="sm" c="dimmed">
              {billing_cycle === 'monthly' ? 'Monthly' : 'Annual'} billing
            </Text>
          </div>
          <Badge
            color={
              status === 'active' ? 'green' :
              status === 'pending' ? 'yellow' :
              status === 'cancelled' ? 'orange' : 'red'
            }
            size="lg"
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </Group>

        <Divider my="sm" />

        <Stack gap="xs">
          <Text size="sm">Subscription Period</Text>
          <Group gap="xs">
            <IconCalendar size="1rem" />
            <Text size="sm">Expires on {new Date(end_date).toLocaleDateString()}</Text>
          </Group>
          <Progress
            value={progressPercentage}
            color={progressPercentage > 50 ? 'green' : progressPercentage > 20 ? 'yellow' : 'red'}
            size="sm"
            mt="xs"
          />
          <Text size="sm" ta="center" fw={500}>
            {days_remaining} days remaining
          </Text>
        </Stack>

        <Divider my="sm" />

        <Text fw={500}>Plan Features</Text>
        <List
          spacing="sm"
          size="sm"
          center
          icon={
            <ThemeIcon color="blue" size={20} radius="xl">
              <IconCheck style={{ width: rem(12), height: rem(12) }} />
            </ThemeIcon>
          }
        >
          <List.Item>Up to {pkg.max_assistants} medical assistants</List.Item>
          <List.Item>Up to {pkg.max_users} users</List.Item>
          <List.Item>Up to {pkg.max_specialties} specialties</List.Item>
          {pkg.features.map((feature, index) => (
            <List.Item key={index}>{feature}</List.Item>
          ))}
        </List>

        <Divider my="sm" />

        <Group grow>
          {status === 'active' && (
            <Button variant="outline" color="blue" onClick={handleRenew}>
              Renew Subscription
            </Button>
          )}
          {(status === 'active' || status === 'pending') && (
            <Button variant="outline" color="red" onClick={handleCancel}>
              Cancel Subscription
            </Button>
          )}
        </Group>
      </Stack>
    </Card>
  );
}
