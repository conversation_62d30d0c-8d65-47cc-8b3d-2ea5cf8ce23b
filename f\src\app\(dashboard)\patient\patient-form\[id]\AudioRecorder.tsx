'use client';
import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  Group,
  Text,
  Progress,
  Alert,
  Stack,
  ActionIcon,
  Card,
  Badge,
  Tooltip,
  Loader,
  TextInput,
  Switch,
} from '@mantine/core';
import {
  IconMicrophone,
  IconPlayerStop,
  IconPlayerPlay,
  IconPlayerPause,
  IconTrash,
  IconDownload,
  IconAlertCircle,
  IconCheck,
  IconCloudUpload,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import patientService, { PatientAttachment } from '@/services/patientService';

interface AudioRecorderProps {
  onSave?: (audioBlob: Blob, fileName: string) => void;
  onCancel?: () => void;
  maxDuration?: number; // in seconds
  patientId: string; // Required for Django integration
  patientName?: string; // For display purposes
  autoSaveToDjango?: boolean; // Whether to automatically save to Django
}

export const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onSave,
  onCancel,
  maxDuration = 300, // 5 minutes default
  patientId,
  patientName,
  autoSaveToDjango = true
}) => {
  // Recording state
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  // Django integration state
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [savedAttachment, setSavedAttachment] = useState<PatientAttachment | null>(null);

  // Recording metadata
  const [recordingTitle, setRecordingTitle] = useState('');
  const [recordingDescription, setRecordingDescription] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Check permissions and Django connection on mount
  useEffect(() => {
    const initializeComponent = async () => {
      await checkPermissions();
      await checkDjangoConnection();
    };

    initializeComponent();

    return () => {
      cleanup();
    };
  }, []);

  // Check Django connection
  const checkDjangoConnection = async () => {
    try {
      const status = await patientService.checkDjangoBridgeStatus();
      setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');
    } catch (error) {
      console.error('Error checking Django status:', error);
      setDjangoStatus('disconnected');
    }
  };

  // Timer pour l'enregistrement
  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= maxDuration) {
            stopRecording();
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRecording, isPaused, maxDuration]);

  const checkPermissions = async () => {
    try {
      const result = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      setPermission(result.state);
      
      result.addEventListener('change', () => {
        setPermission(result.state);
      });
    } catch (err) {
      console.warn('Permission API not supported');
    }
  };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  const startRecording = async () => {
    try {
      setError(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        } 
      });
      
      streamRef.current = stream;
      chunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm;codecs=opus' });
        setAudioBlob(blob);
        
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        
        cleanup();
      };

      mediaRecorder.start(1000); // Collecte des données toutes les secondes
      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);
      
    } catch (err) {
      console.error('Erreur lors du démarrage de l\'enregistrement:', err);
      setError('Impossible d\'accéder au microphone. Vérifiez les permissions.');
    }
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.pause();
      setIsPaused(true);
    }
  };

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.resume();
      setIsPaused(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);
    }
  };

  // Audio playback is handled by the HTML audio element controls

  const deleteRecording = () => {
    setAudioBlob(null);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setRecordingTime(0);
  };

  const saveRecording = async () => {
    if (!audioBlob) return;

    const fileName = `audio_${patientId}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;

    // Call original onSave callback if provided
    if (onSave) {
      onSave(audioBlob, fileName);
    }

    // Save to Django if connected and auto-save is enabled
    if (autoSaveToDjango && djangoStatus === 'connected') {
      await saveToDjango(audioBlob, fileName);
    } else if (autoSaveToDjango && djangoStatus !== 'connected') {
      notifications.show({
        title: 'Django Disconnected',
        message: 'Cannot save to Django backend. Please check connection.',
        color: 'orange',
        icon: <IconAlertCircle size={16} />
      });
    }
  };

  const saveToDjango = async (blob: Blob, fileName: string) => {
    if (djangoStatus !== 'connected') {
      setError('Django backend is not connected');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      // Create a File object from the Blob
      const audioFile = new File([blob], fileName, {
        type: 'audio/webm',
        lastModified: Date.now()
      });

      // Prepare metadata
      const metadata: Partial<PatientAttachment> = {
        attachment_type: 'audio',
        category: 'other',
        title: recordingTitle || `Audio Recording - ${new Date().toLocaleDateString()}`,
        description: recordingDescription || `Audio recording for patient ${patientName || patientId}`,
        is_private: isPrivate,
        is_sensitive: false
      };

      // Upload to Django
      const result = await patientService.uploadPatientAttachment(patientId, audioFile, metadata);

      if (result) {
        setSavedAttachment(result);
        notifications.show({
          title: 'Audio Saved',
          message: `${fileName} has been saved to patient records`,
          color: 'green',
          icon: <IconCheck size={16} />
        });
      } else {
        throw new Error('Failed to save audio to Django backend');
      }
    } catch (error) {
      console.error('Error saving audio to Django:', error);
      setError('Error saving audio to patient records. Please try again.');
      notifications.show({
        title: 'Save Failed',
        message: 'Failed to save audio to patient records',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const downloadRecording = () => {
    if (audioBlob && audioUrl) {
      const fileName = `audio_${patientId || 'patient'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = (recordingTime / maxDuration) * 100;

  return (
    <Stack gap="md">
      {/* Header with Django Status */}
      <Group justify="space-between">
        <Text fw={600}>Audio Recorder</Text>
        <Badge
          color={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'yellow'}
          variant="light"
          size="sm"
        >
          Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
        </Badge>
      </Group>

      {/* Patient Info */}
      {patientName && (
        <Text size="sm" c="dimmed">
          Recording for: {patientName} (ID: {patientId})
        </Text>
      )}

      {error && (
        <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
          {error}
        </Alert>
      )}

      {permission === 'denied' && (
        <Alert icon={<IconAlertCircle size={16} />} color="orange" variant="light">
          Microphone access is denied. Please allow access in your browser settings.
        </Alert>
      )}

      {/* Upload Progress */}
      {uploading && (
        <Alert icon={<Loader size={16} />} color="blue" variant="light">
          <Stack gap="xs">
            <Text size="sm">Saving audio to patient records...</Text>
            <Progress value={uploadProgress} size="sm" />
          </Stack>
        </Alert>
      )}

      {/* Success Message */}
      {savedAttachment && (
        <Alert icon={<IconCheck size={16} />} color="green" variant="light">
          Audio successfully saved to patient records: {savedAttachment.original_filename}
        </Alert>
      )}

      {/* Recording Metadata */}
      {autoSaveToDjango && djangoStatus === 'connected' && (
        <Card withBorder padding="md">
          <Stack gap="sm">
            <Text fw={500} size="sm">Recording Information</Text>
            <TextInput
              label="Title"
              placeholder="Enter recording title..."
              value={recordingTitle}
              onChange={(e) => setRecordingTitle(e.currentTarget.value)}
              disabled={isRecording}
            />
            <TextInput
              label="Description"
              placeholder="Enter recording description..."
              value={recordingDescription}
              onChange={(e) => setRecordingDescription(e.currentTarget.value)}
              disabled={isRecording}
            />
            <Switch
              label="Private recording"
              description="Mark this recording as private"
              checked={isPrivate}
              onChange={(e) => setIsPrivate(e.currentTarget.checked)}
              disabled={isRecording}
            />
          </Stack>
        </Card>
      )}

      {/* Recording Zone */}
      <Card withBorder padding="lg" style={{ textAlign: 'center' }}>
        <Stack gap="md">
          {/* Indicateur visuel */}
          <div style={{ position: 'relative', display: 'inline-block' }}>
            <IconMicrophone 
              size={64} 
              color={isRecording ? (isPaused ? '#ffa500' : '#e53935') : '#868e96'} 
            />
            {isRecording && (
              <Badge
                color={isPaused ? 'orange' : 'red'}
                variant="filled"
                style={{
                  position: 'absolute',
                  top: -5,
                  right: -5,
                  animation: isPaused ? 'none' : 'pulse 1s infinite'
                }}
              >
                {isPaused ? 'PAUSE' : 'REC'}
              </Badge>
            )}
          </div>

          {/* Timer et barre de progression */}
          <div>
            <Text size="xl" fw={700} c={isRecording ? 'red' : 'dimmed'}>
              {formatTime(recordingTime)}
            </Text>
            <Text size="sm" c="dimmed">
              / {formatTime(maxDuration)}
            </Text>
            {isRecording && (
              <Progress 
                value={progressPercentage} 
                color={progressPercentage > 90 ? 'red' : 'blue'}
                mt="xs"
              />
            )}
          </div>

          {/* Contrôles d'enregistrement */}
          <Group justify="center" gap="md">
            {!isRecording && !audioBlob && (
              <Button
                leftSection={<IconMicrophone size={16} />}
                onClick={startRecording}
                color="red"
                disabled={permission === 'denied' || uploading}
                loading={uploading}
              >
                Start Recording
              </Button>
            )}

            {isRecording && (
              <>
                {!isPaused ? (
                  <Tooltip label="Mettre en pause">
                    <ActionIcon size="lg" variant="filled" color="orange" onClick={pauseRecording}>
                      <IconPlayerPause size={20} />
                    </ActionIcon>
                  </Tooltip>
                ) : (
                  <Tooltip label="Reprendre">
                    <ActionIcon size="lg" variant="filled" color="red" onClick={resumeRecording}>
                      <IconPlayerPlay size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                <Tooltip label="Arrêter">
                  <ActionIcon size="lg" variant="filled" color="dark" onClick={stopRecording}>
                    <IconPlayerStop size={20} />
                  </ActionIcon>
                </Tooltip>
              </>
            )}
          </Group>
        </Stack>
      </Card>

      {/* Lecture de l'enregistrement */}
      {audioBlob && audioUrl && (
        <Card withBorder padding="md">
          <Stack gap="sm">
            <Group justify="space-between">
              <Text fw={500}>Recording Complete</Text>
              <Badge color="green" leftSection={<IconCheck size={12} />}>
                {formatTime(recordingTime)}
              </Badge>
            </Group>

            <audio
              ref={audioRef}
              src={audioUrl}
              style={{ width: '100%' }}
              controls
            />

            <Group justify="center" gap="xs">
              <Tooltip label="Download">
                <ActionIcon
                  variant="light"
                  color="blue"
                  onClick={downloadRecording}
                  disabled={uploading}
                >
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>

              <Tooltip label="Delete">
                <ActionIcon
                  variant="light"
                  color="red"
                  onClick={deleteRecording}
                  disabled={uploading}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>

              {autoSaveToDjango && djangoStatus === 'connected' && !savedAttachment && audioBlob && (
                <Tooltip label="Save to Patient Records">
                  <ActionIcon
                    variant="light"
                    color="green"
                    onClick={() => {
                      const fileName = `audio_${patientId}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
                      saveToDjango(audioBlob, fileName);
                    }}
                    disabled={uploading}
                    loading={uploading}
                  >
                    <IconCloudUpload size={16} />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Stack>
        </Card>
      )}

      {/* Action Buttons */}
      <Group justify="flex-end" mt="md">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={uploading}
        >
          Cancel
        </Button>

        {audioBlob && (
          <Button
            leftSection={uploading ? <Loader size={16} /> : <IconCheck size={16} />}
            onClick={saveRecording}
            color="green"
            disabled={uploading}
            loading={uploading}
          >
            {uploading ? 'Saving...' : savedAttachment ? 'Saved to Records' : 'Save Recording'}
          </Button>
        )}
      </Group>

      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </Stack>
  );
};

export default AudioRecorder;
