'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
 
  Textarea,
  Table,
 
  Modal,
  FileInput,
 
  Tabs,
  
  Pagination,
 
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';

import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconList,
  IconFile,
  IconUpload,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  
  IconFileInvoice,
} from '@tabler/icons-react';

interface DemandePrixItem {
  id: string;
  code: string;
  designation: string;
  quantite: number;
  depot: string;
}

interface DemandePrix {
  id: string;
  numero: string;
  date: Date | null;
  fournisseur: string;
  items: DemandePrixItem[];
  commentaire: string;
  status: 'draft' | 'validated' | 'processed';
  attachments: string[];
}

export default function DemandePrixPage() {

  const [currentDemandePrix, setCurrentDemandePrix] = useState<DemandePrix>({
    id: '1',
    numero: '9',
    date: new Date('2022-09-16'),
    fournisseur: '',
    items: [],
    commentaire: '',
    status: 'draft',
    attachments: [],
  });


  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [viewMode, setViewMode] = useState<'form' | 'list'>('form');

  const form = useForm({
    initialValues: {
      numero: currentDemandePrix.numero,
      date: currentDemandePrix.date,
      fournisseur: currentDemandePrix.fournisseur,
      commentaire: currentDemandePrix.commentaire,
    },
  });

  const itemForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      quantite: 1,
      depot: '',
    },
  });

  const fournisseurs = [
    'Fournisseur A',
    'Fournisseur B',
    'Fournisseur C',
    'Fournisseur D',
  ];

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
    'Dépôt Principal',
    'Dépôt Secondaire',
  ];

  const addItem = (values: typeof itemForm.values) => {
    const newItem: DemandePrixItem = {
      id: Date.now().toString(),
      code: values.code,
      designation: values.designation,
      quantite: values.quantite,
      depot: values.depot,
    };

    setCurrentDemandePrix(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));

    itemForm.reset();
    close();
  };

  const removeItem = (id: string) => {
    setCurrentDemandePrix(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  // const handleSave = (action: 'save' | 'validate' | 'process') => {
  //   const updatedDemandePrix = {
  //     ...currentDemandePrix,
  //     ...form.values,
  //     status: action === 'save' ? 'draft' : action === 'validate' ? 'validated' : 'processed',
  //   };

  //   setCurrentDemandePrix(updatedDemandePrix);
    
  //   const actionText = action === 'save' ? 'enregistrée' : action === 'validate' ? 'validée' : 'traitée';
  //   notifications.show({
  //     title: 'Succès',
  //     message: `Demande de prix ${actionText} avec succès`,
  //     color: 'green',
  //   });
  // };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconFileInvoice size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Demande de prix N°: {currentDemandePrix.numero}
          </Title>
        </Group>
        <Group>
          <Button
            variant={viewMode === 'form' ? 'filled' : 'outline'}
            leftSection={<IconFile size={16} />}
            onClick={() => setViewMode('form')}
          >
            Nouveau
          </Button>
          <Button
            variant={viewMode === 'list' ? 'filled' : 'outline'}
            leftSection={<IconList size={16} />}
            onClick={() => setViewMode('list')}
          >
            Demandes de prix
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="N° Demande de prix"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <DatePickerInput
                label="Date"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={12}>
              <Select
                label="Fournisseur"
                placeholder="Sélectionner un fournisseur"
                data={fournisseurs}
                rightSection={
                  <Group gap={4}>
                    <ActionIcon variant="subtle" size="sm">
                      <IconSearch size={14} />
                    </ActionIcon>
                    <ActionIcon variant="subtle" size="sm">
                      <IconPlus size={14} />
                    </ActionIcon>
                  </Group>
                }
                {...form.getInputProps('fournisseur')}
                searchable
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs defaultValue="details">
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              <Group justify="space-between" mb="md">
                <Group>
                  <Button leftSection={<IconBarcode size={16} />} color="blue">
                    Code à barres
                  </Button>
                  <Button leftSection={<IconShoppingCart size={16} />} color="blue">
                    Article
                  </Button>
                  <Button leftSection={<IconMessageCircle size={16} />} color="blue">
                    Commentaire
                  </Button>
                </Group>
              </Group>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {currentDemandePrix.items.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={5} className="text-center text-gray-500">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    currentDemandePrix.items.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.code}</Table.Td>
                        <Table.Td>{item.designation}</Table.Td>
                        <Table.Td>{item.quantite}</Table.Td>
                        <Table.Td>{item.depot}</Table.Td>
                        <Table.Td>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeItem(item.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>

              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm">Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['1', '2', '3']}
                    value={currentPage.toString()}
                    onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  />
                  <Text size="sm">Lignes par Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['10', '25', '50']}
                    value={itemsPerPage.toString()}
                    onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
                  />
                  <Text size="sm">0 - 0 de 0</Text>
                </Group>
                <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
              </Group>

              <Button
                leftSection={<IconPlus size={16} />}
                onClick={open}
                mt="md"
              >
                Ajouter un article
              </Button>
            </Tabs.Panel>

            <Tabs.Panel value="pieces" pt="md">
              <FileInput
                label="Ajouter des pièces jointes"
                placeholder="Sélectionner des fichiers"
                leftSection={<IconUpload size={16} />}
                multiple
              />
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Comment Section */}
          <Grid>
            <Grid.Col span={12}>
              <Textarea
                label="Commentaire"
                placeholder="Commentaire général..."
                rows={3}
                {...form.getInputProps('commentaire')}
              />
            </Grid.Col>
          </Grid>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red">
              Annuler
            </Button>
            <Button variant="outline" leftSection={<IconCheck size={16} />}>
              Valider
            </Button>
            <Button color="cyan" leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer et quitter
            </Button>
            <Button leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="lg">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <TextInput
              label="Code"
              placeholder="Code article"
              {...itemForm.getInputProps('code')}
              required
            />
            <TextInput
              label="Désignation"
              placeholder="Désignation"
              {...itemForm.getInputProps('designation')}
              required
            />
            <NumberInput
              label="Quantité"
              placeholder="1"
              {...itemForm.getInputProps('quantite')}
              min={1}
              required
            />
            <Select
              label="Dépôt"
              placeholder="Sélectionner un dépôt"
              data={depots}
              {...itemForm.getInputProps('depot')}
            />
            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
}
