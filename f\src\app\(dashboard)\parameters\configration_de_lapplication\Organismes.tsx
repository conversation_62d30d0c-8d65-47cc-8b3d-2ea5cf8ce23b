import React, { useState } from 'react';
import Icon from '@mdi/react';
import { mdiFilterVariant } from '@mdi/js';
import { mdiFileExcel } from '@mdi/js';
import {
  Button,
  Card,
  Group,
  Table,
  Text,
  TextInput,
  ActionIcon,
  Tooltip,
  Modal,
  Stack,
  Textarea,
  Switch,
  Checkbox,
  Select,
  
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconX,
  IconUsers,
  IconFolderPlus,
  IconFilter,
  IconRefresh,
  IconDots,
} from '@tabler/icons-react';

// Types pour les données des organismes
interface Organisme {
  id: number;
  name: string;
  contactName: string;
  phone: string;
  email: string;
  address: string;
}

interface OrganismeFormData {
  name: string;
  contactName: string;
  registrationNumber: string;
  phone: string;
  email: string;
  address: string;
  convention: string;
}

const Organismes = () => {
  // États pour la gestion des données
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingOrganisme, setEditingOrganisme] = useState<Organisme | null>(null);
  const [formData, setFormData] = useState<OrganismeFormData>({
    name: '',
    contactName: '',
    registrationNumber: '',
    phone: '',
    email: '',
    address: '',
    convention: '',
  });

  // État pour le bouton de commutation "Numéro d'immatriculation obligatoire"
  const [isRegistrationRequired, setIsRegistrationRequired] = useState(false);

  // États pour le modal des conventions
  const [isConventionModalOpen, setIsConventionModalOpen] = useState(false);
  const [conventionSearchTerm, setConventionSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);



  // Données d'exemple des organismes
  const [organismes, setOrganismes] = useState<Organisme[]>([
    {
      id: 1,
      name: 'Aucune',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 2,
      name: 'AMO',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 3,
      name: 'ATLANTA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 4,
      name: 'CNOPS',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 5,
      name: 'CNSS',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 6,
      name: 'ONCF',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 7,
      name: 'RMA WATANYA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 8,
      name: 'AXA ASSURANCE MAROC',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 9,
      name: 'WAFA ASSURANCE',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 10,
      name: 'SAHAM ASSURANCE',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 11,
      name: 'CMIM',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 12,
      name: 'SANAD',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 13,
      name: 'Allianz',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 14,
      name: 'Es Saada',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 15,
      name: 'MAROCAINE VIE',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 16,
      name: 'LYDEC',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 17,
      name: 'MUPRAS',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 18,
      name: 'NM',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 19,
      name: 'BP',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 20,
      name: 'MAMDA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 21,
      name: 'MUTUELLE',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 22,
      name: 'MCMA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 23,
      name: 'MAMT',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 24,
      name: 'ONE',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 25,
      name: 'ATLANTA/SANAD',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 26,
      name: 'ZURICH',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 27,
      name: 'MAS',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 28,
      name: 'FAR',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 29,
      name: 'OCP',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 30,
      name: 'BANK AL MAGHREB',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 31,
      name: 'Autre',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 32,
      name: 'RMA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 33,
      name: 'AXA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 34,
      name: 'CNIA SAADA',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
    {
      id: 35,
      name: 'ZURICH',
      contactName: '',
      phone: '',
      email: '',
      address: '',
    },
  ]);

  // Filtrage des organismes selon le terme de recherche
  const filteredOrganismes = organismes.filter(organisme =>
    organisme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    organisme.contactName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fonction pour supprimer un organisme
  const deleteOrganisme = (id: number) => {
    setOrganismes(organismes.filter(organisme => organisme.id !== id));
  };

  // Ouverture du modal pour nouveau/édition
  const openModal = (organisme?: Organisme) => {
    if (organisme) {
      setEditingOrganisme(organisme);
      setFormData({
        name: organisme.name,
        contactName: organisme.contactName,
        registrationNumber: '',
        email: organisme.email,
        phone: organisme.phone,
        address: organisme.address,
        convention: '',
      });
    } else {
      setEditingOrganisme(null);
      setFormData({
        name: '',
        contactName: '',
        registrationNumber: '',
        email: '',
        phone: '',
        address: '',
        convention: '',
      });
    }
    setIsModalOpen(true);
  };

  // Fonction pour vérifier si le formulaire est valide
  const isFormValid = () => {
    if (isRegistrationRequired) {
      return formData.name.trim() !== '' && formData.registrationNumber.trim() !== '';
    }
    return formData.name.trim() !== '';
  };

  // Fermeture du modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingOrganisme(null);
    setIsRegistrationRequired(false);
    setFormData({
      name: '',
      contactName: '',
      registrationNumber: '',
      email: '',
      phone: '',
      address: '',
      convention: '',
    });
  };

  // Sauvegarde d'un organisme
  const saveOrganisme = () => {
    if (editingOrganisme) {
      // Modification
      setOrganismes(organismes.map(organisme =>
        organisme.id === editingOrganisme.id
          ? { ...organisme, ...formData }
          : organisme
      ));
    } else {
      // Ajout
      const newOrganisme: Organisme = {
        id: Math.max(...organismes.map(o => o.id)) + 1,
        ...formData,
      };
      setOrganismes([...organismes, newOrganisme]);
    }
    closeModal();
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <Text size="xl" fw={600} className="text-gray-900">
              Gestion des Organismes
            </Text>
            <Text size="sm" className="text-gray-600 mt-1">
              Gérez les organismes d&apos;assurance, mutuelles et caisses
            </Text>
          </div>
        </div>
      </div>

      {/* Zone de contenu principal */}
      <div className="flex-1 flex flex-col">
        {/* Barre de recherche et bouton Nouveau */}
        <Card
          shadow="none"
          padding="md"
          radius={0}
          className="bg-white border-b border-gray-200"
        >
          <Group justify="space-between" align="center">
            <TextInput
              placeholder="Rechercher un organisme..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="flex-1 max-w-md"
              size="sm"
            />
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              onClick={() => openModal()}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Nouveau
            </Button>
          </Group>
        </Card>

        {/* Tableau des organismes */}
        <div className="flex-1 bg-white overflow-hidden">
          <Table
            striped={false}
            highlightOnHover={true}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50 sticky top-0">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom complet du contact
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Téléphone
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Email
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Adresse
                </Table.Th>
                <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-center">
                  Actions
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredOrganismes.map((organisme) => (
                <Table.Tr key={organisme.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-200 text-sm font-medium">
                    {organisme.name}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm text-gray-600">
                    {organisme.contactName}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm text-gray-600">
                    {organisme.phone}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm text-gray-600">
                    {organisme.email}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm text-gray-600">
                    {organisme.address}
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Group gap="xs" justify="center">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="sm"
                          onClick={() => openModal(organisme)}
                        >
                          <IconEdit size={14} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="sm"
                          onClick={() => deleteOrganisme(organisme.id)}
                        >
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>

      {/* Modal Formulaire Organisme */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title=""
        size="lg"
        centered
        padding={0}
        styles={{
          header: {
            backgroundColor: '#1976d2',
            color: 'white',
            padding: '12px 16px',
            margin: 0,
          },
          title: {
            color: 'white',
            fontWeight: 500,
          },
          close: {
            color: 'white',
          },
        }}
      >
        {/* En-tête du modal avec titre et bouton fermer */}
        <div className="bg-blue-600 text-white px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <IconUsers size={20} />
            <Text size="lg" fw={500}>Organisme</Text>
          </div>
          <ActionIcon
            variant="transparent"
            color="white"
            onClick={closeModal}
            size="lg"
          >
            <IconX size={20} />
          </ActionIcon>
        </div>

        {/* Formulaire */}
        <div className="p-6">
          <Stack gap="md">
            <Group justify="space-between" align="flex-start">
              <TextInput
                label="Nom"
                placeholder=""
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                size="sm"
                className="flex-1"
                error={isRegistrationRequired && formData.name.trim() === '' ? '' : ''}
                styles={{
                  label: {
                    color: isRegistrationRequired && formData.name.trim() === '' ? '#fa5252' : '#495057',
                    fontWeight: 500,
                  }
                }}
              />
              <TextInput
                label="Nom complet du contact"
                placeholder=""
                value={formData.contactName}
                onChange={(e) => setFormData({ ...formData, contactName: e.target.value })}
                size="sm"
                className="flex-1 ml-4"
              />
            </Group>

            <div className="flex items-center gap-3">
              <Switch
                checked={isRegistrationRequired}
                onChange={(event) => setIsRegistrationRequired(event.currentTarget.checked)}
                color="blue"
                size="sm"
              />
              <Text size="sm" className="text-gray-700">
                Numéro d&apos;immatriculation obligatoire
              </Text>
            </div>

            {isRegistrationRequired && (
              <TextInput
                label="Numéro d'enregistrement"
                placeholder=""
                value={formData.registrationNumber}
                onChange={(e) => setFormData({ ...formData, registrationNumber: e.target.value })}
                size="sm"
                required
                error={formData.registrationNumber.trim() === '' ? '' : ''}
                styles={{
                  label: {
                    color: formData.registrationNumber.trim() === '' ? '#fa5252' : '#495057',
                    fontWeight: 500,
                  }
                }}
              />
            )}

            <Group grow>
              <TextInput
                label="Téléphone"
                placeholder="+212 X XX XX XX XX"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                size="sm"
              />
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                type="email"
                size="sm"
              />
            </Group>

            <Textarea
              label="Adresse"
              placeholder="Adresse complète de l'organisme"
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              rows={3}
              size="sm"
            />

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                leftSection={<IconFolderPlus size={16} />}
                onClick={() => setIsConventionModalOpen(true)}
                className="border-blue-500 text-blue-500 hover:bg-blue-50"
              >
                Convention
              </Button>
              <TextInput
                placeholder="Sélectionner une convention"
                value={formData.convention}
                onChange={(e) => setFormData({ ...formData, convention: e.target.value })}
                className="flex-1"
                size="sm"
                readOnly
              />
            </div>
          </Stack>
        </div>

        {/* Boutons d'action */}
        <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={closeModal}
              size="sm"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                if (isFormValid()) {
                  saveOrganisme();
                }
              }}
              size="sm"
              disabled={!isFormValid()}
              className={`${
                isFormValid()
                  ? 'bg-blue-500 hover:bg-blue-600 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {editingOrganisme ? 'Modifier' : 'Enregistrer'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal Liste des conventions */}
      <Modal
        opened={isConventionModalOpen}
        onClose={() => setIsConventionModalOpen(false)}
        title=""
        size="xl"
        centered
        padding={0}
        styles={{
          header: {
            backgroundColor: '#1976d2',
            color: 'white',
            padding: '12px 16px',
            margin: 0,
          },
          title: {
            color: 'white',
            fontWeight: 500,
          },
          close: {
            color: 'white',
          },
        }}
      >
        {/* En-tête du modal avec titre et bouton fermer */}
        <div className="bg-blue-600 text-white px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <IconFilter size={20} />
            <Text size="lg" fw={500}>Liste des conventions</Text>
          </div>
          <ActionIcon
            variant="transparent"
            color="white"
            onClick={() => setIsConventionModalOpen(false)}
            size="lg"
          >
            <IconX size={20} />
          </ActionIcon>
        </div>

        {/* Barre de recherche */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-2">
            <Tooltip label="Recherche avancee">
                <Icon path={mdiFilterVariant} size={1} className="text-gray-500"/>
            {/* <IconFilter size={16} className="text-gray-500" /> */}
            </Tooltip>
            <TextInput
              placeholder="Rechercher"
              value={conventionSearchTerm}
              onChange={(e) => setConventionSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="flex-1"
              size="sm"
            />
            <ActionIcon variant="subtle" color="gray" size="sm">
              <IconRefresh size={16} />
            </ActionIcon>
              <Tooltip label="Exporter vers Excels">
            <Icon path={mdiFileExcel} size={1} className="text-gray-500"/>
</Tooltip>
            <ActionIcon variant="subtle" color="gray" size="sm">
              <IconDots size={16} />
            </ActionIcon>
          </div>
        </div>

        {/* Zone de contenu principal */}
        <div className="p-4 min-h-[300px]">
          {/* Checkbox pour sélectionner tout */}
          <div className="mb-4">
            <Checkbox
              label=""
              checked={false}
              onChange={() => {}}
              size="sm"
            />
          </div>

          {/* Message "Aucun élément trouvé" */}
          <div className="text-center py-8 text-gray-500">
            <Text size="sm">Aucun élément trouvé.</Text>
          </div>
        </div>

        {/* Pied de page avec pagination */}
        <div className="border-t border-gray-200 px-4 py-3 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  data={['1']}
                  size="xs"
                  className="w-16"
                />
              </div>
              <div className="flex items-center gap-2">
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(parseInt(value || '15'))}
                  data={['15', '25', '50', '100']}
                  size="xs"
                  className="w-16"
                />
              </div>
              <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
            </div>

            <div className="flex items-center gap-1">
              <ActionIcon variant="subtle" color="gray" size="sm" disabled>
                <IconSearch size={14} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" size="sm" disabled>
                <IconSearch size={14} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" size="sm" disabled>
                <IconSearch size={14} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" size="sm" disabled>
                <IconSearch size={14} />
              </ActionIcon>
            </div>
          </div>
        </div>

        {/* Boutons d'action */}
        <div className="border-t border-gray-200 px-4 py-3 bg-white">
          <div className="flex justify-end gap-2">
            <Button
              variant="filled"
              color="blue"
              size="sm"
              className="bg-blue-500 hover:bg-blue-600"
              onClick={() => setIsConventionModalOpen(false)}
            >
              Ok
            </Button>
            <Button
              variant="filled"
              color="red"
              size="sm"
              onClick={() => setIsConventionModalOpen(false)}
              className="bg-red-500 hover:bg-red-600"
            >
              Annuler
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Organismes;
