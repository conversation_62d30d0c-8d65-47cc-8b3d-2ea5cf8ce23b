import api from '~/lib/api';

export interface SubscriptionPackage {
  id: number;
  name: string;
  description: string | null;
  max_assistants: number;
  max_users: number;
  max_specialties: number;
  price_monthly: string;
  price_yearly: string;
  features: string[];
  is_active: boolean;
}

export interface DoctorSubscription {
  id: number;
  doctor: number;
  package: number;
  package_details: SubscriptionPackage;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  billing_cycle: 'monthly' | 'annual';
  start_date: string;
  end_date: string;
  current_assistant_count: number;
  current_user_count: number;
  current_specialty_count: number;
  days_remaining: number;
}

export interface Coupon {
  id: number;
  code: string;
  description: string | null;
  discount_type: 'percentage' | 'fixed';
  discount_value: string;
  valid_from: string;
  valid_until: string | null;
  max_uses: number | null;
  current_uses: number;
  minimum_purchase: string;
  is_valid: boolean;
}

export interface SubscriptionTransaction {
  id: number;
  subscription: number;
  amount: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_method: string | null;
  transaction_id: string | null;
  coupon: number | null;
  discount_amount: string;
  created_at: string;
}

const subscriptionService = {
  // Package methods
  getPackages: async (): Promise<SubscriptionPackage[]> => {
    const response = await api.get('/api/subscriptions/packages/');
    return response.data;
  },

  getPackage: async (id: number): Promise<SubscriptionPackage> => {
    const response = await api.get(`/api/subscriptions/packages/${id}/`);
    return response.data;
  },

  // Subscription methods
  getSubscriptions: async (): Promise<DoctorSubscription[]> => {
    const response = await api.get('/api/subscriptions/subscriptions/');
    return response.data;
  },

  getCurrentSubscription: async (): Promise<DoctorSubscription> => {
    const response = await api.get('/api/subscriptions/subscriptions/current/');
    return response.data;
  },

  createSubscription: async (data: {
    package: number;
    billing_cycle: 'monthly' | 'annual';
    is_six_month?: boolean;
  }): Promise<DoctorSubscription> => {
    const response = await api.post('/api/subscriptions/subscriptions/', data);
    return response.data;
  },

  cancelSubscription: async (id: number): Promise<DoctorSubscription> => {
    const response = await api.post(`/api/subscriptions/subscriptions/${id}/cancel/`);
    return response.data;
  },

  renewSubscription: async (id: number): Promise<DoctorSubscription> => {
    const response = await api.post(`/api/subscriptions/subscriptions/${id}/renew/`);
    return response.data;
  },

  // Coupon methods
  validateCoupon: async (code: string, packageId?: number): Promise<{ coupon: Coupon; valid: boolean }> => {
    const response = await api.post('/api/subscriptions/coupons/validate/', {
      code,
      package_id: packageId,
    });
    return response.data;
  },

  // Transaction methods
  getTransactions: async (): Promise<SubscriptionTransaction[]> => {
    const response = await api.get('/api/subscriptions/transactions/');
    return response.data;
  },
};

export default subscriptionService;
