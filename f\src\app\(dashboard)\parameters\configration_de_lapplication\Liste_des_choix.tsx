import React, { useState } from 'react';
import {
  Button,
  Card,
  Checkbox,
  Group,
  Stack,
  Table,
  Text,
  TextInput,
  ActionIcon,
  Tooltip,
  Modal,
  Alert,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconTrash,
  IconAlertCircle,
} from '@tabler/icons-react';

// Types pour les données
interface ChoiceItem {
  id: number;
  value: string;
}

interface ChoiceList {
  id: number;
  title: string;
  choices: ChoiceItem[];
  isMultiple: boolean;
}

const Liste_des_choix = () => {
  // États pour la gestion des données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedList, setSelectedList] = useState<ChoiceList | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingList, setEditingList] = useState<ChoiceList | null>(null);

  // États pour le formulaire
  const [formData, setFormData] = useState({
    title: '',
    isMultiple: false,
  });
  const [choices, setChoices] = useState<ChoiceItem[]>([]);
  const [newChoiceValue, setNewChoiceValue] = useState('');

  // Données de test pour les listes de choix
  const [choiceLists, setChoiceLists] = useState<ChoiceList[]>([
    {
      id: 1,
      title: 'Types de consultation',
      choices: [
        { id: 1, value: 'Consultation générale' },
        { id: 2, value: 'Consultation spécialisée' },
        { id: 3, value: 'Urgence' },
      ],
      isMultiple: false,
    },
    {
      id: 2,
      title: 'Moyens de paiement',
      choices: [
        { id: 1, value: 'Espèces' },
        { id: 2, value: 'Carte bancaire' },
        { id: 3, value: 'Chèque' },
        { id: 4, value: 'Virement' },
      ],
      isMultiple: true,
    },
  ]);

  // Filtrage des listes
  const filteredLists = choiceLists.filter(list =>
    list.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Ouverture du modal pour nouveau/édition
  const openModal = (list?: ChoiceList) => {
    if (list) {
      setEditingList(list);
      setFormData({
        title: list.title,
        isMultiple: list.isMultiple,
      });
      setChoices([...list.choices]);
    } else {
      setEditingList(null);
      setFormData({
        title: '',
        isMultiple: false,
      });
      setChoices([]);
    }
    setNewChoiceValue('');
    setIsModalOpen(true);
  };

  // Fermeture du modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingList(null);
    setFormData({
      title: '',
      isMultiple: false,
    });
    setChoices([]);
    setNewChoiceValue('');
  };

  // Ajouter un choix
  const addChoice = () => {
    if (newChoiceValue.trim()) {
      const newChoice: ChoiceItem = {
        id: Date.now(),
        value: newChoiceValue.trim(),
      };
      setChoices(prev => [...prev, newChoice]);
      setNewChoiceValue('');
    }
  };

  // Supprimer un choix
  const removeChoice = (id: number) => {
    setChoices(prev => prev.filter(choice => choice.id !== id));
  };

  // Sauvegarde d'une liste
  const saveList = () => {
    if (!formData.title.trim() || choices.length === 0) {
      return;
    }

    if (editingList) {
      // Modification
      setChoiceLists(prev =>
        prev.map(list =>
          list.id === editingList.id
            ? {
                ...list,
                title: formData.title,
                isMultiple: formData.isMultiple,
                choices: [...choices],
              }
            : list
        )
      );
    } else {
      // Création
      const newList: ChoiceList = {
        id: Date.now(),
        title: formData.title,
        isMultiple: formData.isMultiple,
        choices: [...choices],
      };
      setChoiceLists(prev => [...prev, newList]);
    }
    closeModal();
  };

  // Suppression d'une liste
  const deleteList = (id: number) => {
    setChoiceLists(prev => prev.filter(list => list.id !== id));
    if (selectedList?.id === id) {
      setSelectedList(null);
    }
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* Sidebar gauche avec les listes */}
      <div className="w-64 bg-white border-r border-gray-200">
        {/* Header avec bouton nouveau */}
        <div className="bg-blue-500 text-white p-3">
          <Group justify="space-between" align="center">
            <Text size="sm" fw={600}>
              Liste des choix
            </Text>
            <Tooltip label="Nouveau liste des choix">
              <ActionIcon
                variant="subtle"
                color="white"
                size="sm"
                onClick={() => openModal()}
              >
                <IconPlus size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </div>

        {/* Section Listes avec recherche */}
        <div className="p-3">
          <div className="bg-blue-100 p-2 rounded mb-3">
            <Group gap="xs" align="center">
              <IconSearch size={16} className="text-blue-600" />
              <Text size="sm" fw={500} className="text-blue-800">
                Listes
              </Text>
            </Group>
          </div>

          <TextInput
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftSection={<IconSearch size={16} />}
            size="sm"
            className="mb-3"
          />

          {/* Liste des choix */}
          <Stack gap="xs">
            {filteredLists.length > 0 ? (
              filteredLists.map((list) => (
                <Card
                  key={list.id}
                  padding="xs"
                  radius="sm"
                  className={`cursor-pointer border transition-colors ${
                    selectedList?.id === list.id
                      ? 'bg-blue-50 border-blue-300'
                      : 'hover:bg-gray-50 border-gray-200'
                  }`}
                  onClick={() => setSelectedList(list)}
                >
                  <Group justify="space-between" align="center">
                    <Text size="sm" className="flex-1">
                      {list.title}
                    </Text>
                    <Group gap="xs">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            openModal(list);
                          }}
                        >
                          <IconPlus size={12} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteList(list.id);
                          }}
                        >
                          <IconTrash size={12} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Group>
                </Card>
              ))
            ) : (
              <Alert
                icon={<IconAlertCircle size={16} />}
                color="yellow"
                variant="light"
                size="sm"
              >
                <Text size="sm">Aucun élément trouvé</Text>
              </Alert>
            )}
          </Stack>
        </div>
      </div>

      {/* Zone de contenu principal */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Header avec bouton Nouveau liste des choix */}
        <div className="bg-blue-500 text-white p-3 flex justify-between items-center">
          <Text size="lg" fw={600}>
            Liste des choix
          </Text>
          <Button
            variant="subtle"
            color="white"
            leftSection={<IconPlus size={16} />}
            onClick={() => openModal()}
            className="text-white hover:bg-blue-600"
          >
            Nouveau liste des choix
          </Button>
        </div>

        {/* Contenu principal */}
        <div className="flex-1 p-6">
          {selectedList ? (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Stack gap="md">
                {/* Titre */}
                <div>
                  <Text size="sm" fw={500} mb="xs" className="text-red-500">
                    Titre *
                  </Text>
                  <TextInput
                    value={selectedList.title}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>

                {/* Tableau des choix */}
                <div>
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                  >
                    <Table.Thead className="bg-blue-50">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 text-center w-16">
                          #
                        </Table.Th>
                        <Table.Th className="text-left">
                          Choix
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {selectedList.choices.length > 0 ? (
                        selectedList.choices.map((choice, index) => (
                          <Table.Tr key={choice.id}>
                            <Table.Td className="border-r border-gray-200 text-center text-sm">
                              {index + 1}
                            </Table.Td>
                            <Table.Td className="text-sm">
                              {choice.value}
                            </Table.Td>
                          </Table.Tr>
                        ))
                      ) : (
                        <Table.Tr>
                          <Table.Td colSpan={2} className="text-center text-gray-500 py-8">
                            Aucun élément trouvé
                          </Table.Td>
                        </Table.Tr>
                      )}
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Choix multiple checkbox */}
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedList.isMultiple}
                    readOnly
                    label="Choix multiple"
                    size="sm"
                  />
                </div>

                {/* Boutons d'action */}
                <Group justify="flex-end" gap="sm" mt="md">
                  <Button
                    variant="outline"
                    color="gray"
                    onClick={() => openModal(selectedList)}
                  >
                    Ajouter Choix
                  </Button>
                  <Button
                    color="blue"
                    onClick={() => openModal(selectedList)}
                  >
                    Enregistrer
                  </Button>
                </Group>
              </Stack>
            </Card>
          ) : (
            <div className="flex items-center justify-center h-full">
              <Text size="lg" className="text-gray-500">
                Sélectionnez une liste pour voir les détails
              </Text>
            </div>
          )}
        </div>
      </div>

      {/* Modal pour ajouter/modifier une liste */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title={editingList ? 'Modifier la liste' : 'Nouvelle liste des choix'}
        size="lg"
        centered
      >
        <Stack gap="md">
          {/* Titre */}
          <div>
            <Text size="sm" fw={500} mb="xs" className="text-red-500">
              Titre *
            </Text>
            <TextInput
              placeholder="Entrez le titre de la liste"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />
          </div>

          {/* Tableau des choix */}
          <div>
            <Text size="sm" fw={500} mb="xs">
              Choix
            </Text>
            <Table
              striped={false}
              highlightOnHover={true}
              withTableBorder={true}
              withColumnBorders={true}
            >
              <Table.Thead className="bg-blue-50">
                <Table.Tr>
                  <Table.Th className="border-r border-gray-300 text-center w-16">
                    #
                  </Table.Th>
                  <Table.Th className="text-left">
                    Choix
                  </Table.Th>
                  <Table.Th className="text-center w-20">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {choices.length > 0 ? (
                  choices.map((choice, index) => (
                    <Table.Tr key={choice.id}>
                      <Table.Td className="border-r border-gray-200 text-center text-sm">
                        {index + 1}
                      </Table.Td>
                      <Table.Td className="text-sm">
                        {choice.value}
                      </Table.Td>
                      <Table.Td className="text-center">
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => removeChoice(choice.id)}
                          >
                            <IconTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      </Table.Td>
                    </Table.Tr>
                  ))
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={3} className="text-center text-gray-500 py-4">
                      Aucun élément trouvé
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>

            {/* Ajouter un nouveau choix */}
            <Group gap="sm" mt="sm">
              <TextInput
                placeholder="Nouveau choix"
                value={newChoiceValue}
                onChange={(e) => setNewChoiceValue(e.target.value)}
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    addChoice();
                  }
                }}
              />
              <Button
                variant="outline"
                color="blue"
                onClick={addChoice}
                disabled={!newChoiceValue.trim()}
              >
                Ajouter
              </Button>
            </Group>
          </div>

          {/* Choix multiple checkbox */}
          <Checkbox
            label="Choix multiple"
            checked={formData.isMultiple}
            onChange={(e) => setFormData({ ...formData, isMultiple: e.currentTarget.checked })}
            size="sm"
          />

          {/* Boutons d'action */}
          <Group justify="flex-end" gap="sm" mt="md">
            <Button variant="outline" onClick={closeModal}>
              Annuler
            </Button>
            <Button
              onClick={saveList}
              disabled={!formData.title.trim() || choices.length === 0}
              color="blue"
            >
              {editingList ? 'Modifier' : 'Enregistrer'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Liste_des_choix;
