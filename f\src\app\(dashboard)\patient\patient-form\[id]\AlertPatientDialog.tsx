'use client';

import { useState, useEffect } from 'react';
import {
  Button,
  Textarea,
  Select,
  Radio,
  Group,
  Switch,
  MultiSelect,
  Badge,
  Alert,
  Loader,
  
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useForm } from '@mantine/form';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import patientService, { PatientAlert } from '@/services/patientService';

interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}

interface AlertPatientDialogProps {
  opened: boolean;
  onClose: () => void;
  onSubmit?: (values: AlertFormValues, autoTrigger: boolean) => void;
  patientId: string;
  fullName?: string;
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
}

export default function AlertPatientDialog({
 // opened,
  onClose,
  onSubmit,
  patientId,
  fullName,
  staffOptions = [],
  triggerOptions = [],
}: AlertPatientDialogProps) {
  // State management
 // const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [djangoStatus, setDjangoStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [submitting, setSubmitting] = useState(false);
  // Default trigger options if none provided
  const defaultTriggerOptions = [
    { label: 'Appointment', value: 'appointment' },
    { label: 'Medication', value: 'medication' },
    { label: 'Follow-up', value: 'follow_up' },
    { label: 'Emergency', value: 'emergency' },
    { label: 'Reminder', value: 'reminder' },
    { label: 'Custom', value: 'custom' },
  ];

  const defaultStaffOptions = [
    { label: 'All Staff', value: 'all' },
    { label: 'Doctors', value: 'doctors' },
    { label: 'Nurses', value: 'nurses' },
  ];

  // Check Django connection on component mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const status = await patientService.checkDjangoBridgeStatus();
        setDjangoStatus(status.status === 'active' ? 'connected' : 'disconnected');
      } catch (error) {
        console.error('Error checking Django status:', error);
        setDjangoStatus('disconnected');
      }
    };

    checkConnection();
  }, []);

  const form = useForm<AlertFormValues>({
    initialValues: {
      trigger_for: [],
      trigger: '',
      level: 'MINIMUM',
      description: '',
      is_permanent: false,
    },
    validate: {
      trigger_for: (value) => (value.length === 0 ? 'Staff selection required' : null),
      trigger: (value) => (!value ? 'Trigger type required' : null),
      description: (value) => (!value ? 'Description required' : null),
    },
  });

  // Handle form submission with Django integration
  const handleSubmit = async (values: AlertFormValues, autoTrigger: boolean = false) => {
    if (djangoStatus !== 'connected') {
      setError('Django backend is not connected');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // Prepare alert data for Django
      const alertData: Partial<PatientAlert> & { auto_trigger?: boolean } = {
        trigger: values.trigger,
        trigger_custom: values.trigger === 'custom' ? values.description : '',
        level: values.level,
        description: values.description,
        is_permanent: values.is_permanent,
        trigger_for: values.trigger_for.map(id => ({ id, name: '', user_type: '' })),
        auto_trigger: autoTrigger
      };

      // Create alert in Django
      const result = await patientService.createPatientAlert(patientId, alertData);

      if (result) {
        notifications.show({
          title: 'Alert Created',
          message: `Alert "${values.description}" has been created successfully`,
          color: 'green',
          icon: <IconCheck size={16} />
        });

        // Call original onSubmit if provided
        if (onSubmit) {
          onSubmit(values, autoTrigger);
        }

        // Reset form and close
        form.reset();
        onClose();
      } else {
        setError('Failed to create alert in Django backend');
      }
    } catch (error) {
      console.error('Error creating alert:', error);
      setError('Error creating alert. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
      {/* Django Status Badge */}
      <Group justify="space-between" align="center">
        <Badge
          color={djangoStatus === 'connected' ? 'green' : djangoStatus === 'disconnected' ? 'red' : 'yellow'}
          variant="light"
          size="sm"
        >
          Django: {djangoStatus === 'connected' ? 'Connected' : djangoStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
        </Badge>
        {fullName && <span>Patient: {fullName}</span>}
      </Group>

      {/* Error Alert */}
      {error && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Error"
          color="red"
          onClose={() => setError(null)}
          withCloseButton
        >
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {submitting && (
        <Group justify="center" p="md">
          <Loader size="sm" />
          <span>Creating alert...</span>
        </Group>
      )}

      {/* Main Form */}
      <form
        onSubmit={form.onSubmit((values) => handleSubmit(values, false))}
        style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
      >
        <MultiSelect
          label="Trigger For (Staff)"
          description="Select staff members who should receive this alert"
          data={staffOptions.length > 0 ? staffOptions : defaultStaffOptions}
          {...form.getInputProps('trigger_for')}
          required
          disabled={djangoStatus !== 'connected' || submitting}
        />

        <Select
          label="Alert Trigger"
          description="Select the type of trigger for this alert"
          data={triggerOptions.length > 0 ? triggerOptions : defaultTriggerOptions}
          {...form.getInputProps('trigger')}
          required
          disabled={djangoStatus !== 'connected' || submitting}
        />

        <Radio.Group
          label="Alert Level"
          description="Select the priority level of this alert"
          {...form.getInputProps('level')}
        >
          <Group>
            <Radio value="MINIMUM" label="Minimum" disabled={submitting} />
            <Radio value="MEDIUM" label="Medium" disabled={submitting} />
            <Radio value="HIGH" label="High" disabled={submitting} />
          </Group>
        </Radio.Group>

        <Textarea
          label="Description"
          description="Provide a detailed description of the alert"
          placeholder="Enter alert description..."
          {...form.getInputProps('description')}
          required
          disabled={djangoStatus !== 'connected' || submitting}
          minRows={3}
        />

        <Switch
          label="Permanent Alert"
          description="Check if this alert should be permanent"
          {...form.getInputProps('is_permanent', { type: 'checkbox' })}
          disabled={submitting}
        />

        <Group justify="flex-end" mt="md">
          <Button
            color="gray"
            onClick={onClose}
            disabled={submitting}
          >
            Cancel
          </Button>

          <Button
            onClick={() => {
              if (form.isValid()) {
                handleSubmit(form.values, true);
              }
            }}
            disabled={!form.isValid() || djangoStatus !== 'connected' || submitting}
            loading={submitting}
            color="orange"
          >
            Save & Trigger
          </Button>

          <Button
            type="submit"
            disabled={!form.isValid() || djangoStatus !== 'connected' || submitting}
            loading={submitting}
          >
            Save Alert
          </Button>
        </Group>
      </form>
    </div>
  );
}
