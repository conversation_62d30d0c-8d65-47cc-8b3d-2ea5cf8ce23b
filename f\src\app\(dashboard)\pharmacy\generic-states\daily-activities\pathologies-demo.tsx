'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { Pathologies } from './Pathologies';

export default function PathologiesDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    console.log(`Période sélectionnée: du ${startDate} au ${endDate}`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé pour les pathologies');
    } else {
      alert('Filtre avancé désactivé');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des pathologies en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des pathologies en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'total': 'Total'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={false}
            onQueryChange={handleQueryChange}
            onFilterChange={handleFilterChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function PathologiesLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onFilterChange={(filter) => console.log('Filter:', filter)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
            onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function PathologiesWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données des pathologies chargées pour:', query);
    }, 1000);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé - Recherche approfondie des pathologies');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des pathologies avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des pathologies avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    alert(`Tri des pathologies par ${columnId} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={false}
            onQueryChange={handleQueryChange}
            onFilterChange={handleFilterChange}
            onExport={handleExport}
            onPrint={handlePrint}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function PathologiesErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec gestion d\'erreurs:', query);
    
    // Simuler une validation de période
    const startDate = new Date(query.start);
    const endDate = new Date(query.end);
    
    if (endDate < startDate) {
      alert('Erreur: La date de fin ne peut pas être antérieure à la date de début');
      return;
    }
    
    // Simuler une période trop longue
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      alert('Attention: La période sélectionnée est très longue (plus d\'un an). Cela peut affecter les performances.');
    }
    
    console.log('Période validée, chargement des pathologies...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={false}
            onQueryChange={handleQueryChange}
            onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
            onExport={(format) => {
              console.log(`Export ${format} avec validation`);
              if (confirm(`Êtes-vous sûr de vouloir exporter les pathologies en ${format.toUpperCase()} ?`)) {
                alert('Export en cours...');
              }
            }}
            onPrint={() => {
              console.log('Impression avec validation');
              if (confirm('Êtes-vous sûr de vouloir imprimer les pathologies ?')) {
                alert('Impression en cours...');
              }
            }}
            onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données de pathologies simulées
export function PathologiesSimulatedDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query données simulées:', query);
    
    // Simuler des données de pathologies
    const mockPathologies = [
      { name: 'Hypertension artérielle', patients: 45, severity: 'Modérée' },
      { name: 'Diabète type 2', patients: 32, severity: 'Élevée' },
      { name: 'Asthme bronchique', patients: 28, severity: 'Légère' },
      { name: 'Arthrose', patients: 22, severity: 'Modérée' },
      { name: 'Dépression', patients: 18, severity: 'Variable' },
      { name: 'Migraine chronique', patients: 15, severity: 'Modérée' },
      { name: 'Insuffisance cardiaque', patients: 12, severity: 'Élevée' },
      { name: 'BPCO', patients: 10, severity: 'Élevée' }
    ];
    
    const totalPatients = mockPathologies.reduce((sum, pathology) => sum + pathology.patients, 0);
    alert(`${mockPathologies.length} pathologies trouvées avec ${totalPatients} patients concernés`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre données simulées:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé: Recherche par gravité, type de pathologie, et nombre de patients');
    }
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri données simulées:', columnId, direction);
    
    // Simuler le tri des données
    if (columnId === 'total') {
      alert(`Tri des pathologies par nombre total de patients en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={false}
            onQueryChange={handleQueryChange}
            onFilterChange={handleFilterChange}
            onExport={(format) => alert(`Export ${format} des données simulées de pathologies`)}
            onPrint={() => alert('Impression des données simulées de pathologies')}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec analyse médicale avancée
export function PathologiesAdvancedAnalysisDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query analyse avancée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    alert(`Analyse médicale avancée des pathologies du ${startDate} au ${endDate}`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre analyse avancée:', filter);
    if (filter.showAdvancedFilter) {
      alert('Mode analyse avancée activé:\n- Classification par gravité\n- Groupement par spécialité\n- Analyse épidémiologique\n- Corrélations pathologiques');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export analyse ${format}`);
    alert(`Export ${format.toUpperCase()} de l'analyse médicale:\n- Statistiques détaillées\n- Graphiques de répartition\n- Tendances temporelles\n- Recommandations cliniques`);
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri analyse avancée:', columnId, direction);
    alert(`Analyse triée par ${columnId} - Identification des pathologies prioritaires`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Pathologies
            loading={false}
            onQueryChange={handleQueryChange}
            onFilterChange={handleFilterChange}
            onExport={handleExport}
            onPrint={() => alert('Impression du rapport d\'analyse médicale avancée')}
            onSort={handleSort}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
