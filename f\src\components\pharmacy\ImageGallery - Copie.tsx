'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  ActionIcon,
  Card,
  Stack,
  Text,
  Badge,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Tooltip,
  Pagination,
  Box,
  Center,
  Loader,
  Avatar,
  Textarea,
  Divider,
  ScrollArea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconPhoto,
  IconX,
  IconEdit,
  IconTrash,
  IconEye,
  IconUpload,
  IconDownload,
  IconFilter,
  IconTag,
  IconCalendar,
  IconUser,
  IconFileText,
  IconZoomIn,
  IconShare,
  IconHeart,
  IconHeartFilled,
} from '@tabler/icons-react';

interface ImageItem {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnail: string;
  category: string;
  tags: string[];
  uploadDate: Date;
  uploadedBy: string;
  size: number;
  format: string;
  dimensions: { width: number; height: number };
  isFavorite: boolean;
}

export default function ImageGalleryPage() {
  const [images, setImages] = useState<ImageItem[]>([
    {
      id: '1',
      title: 'Médicament Amoxicilline',
      description: 'Antibiotique à large spectre pour traiter les infections bactériennes',
      url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
      category: 'Antibiotiques',
      tags: ['antibiotique', 'infection', 'bactérie'],
      uploadDate: new Date('2024-01-15'),
      uploadedBy: 'Dr. Martin',
      size: 245760,
      format: 'JPG',
      dimensions: { width: 800, height: 600 },
      isFavorite: false,
    },
    {
      id: '2',
      title: 'Équipement Médical',
      description: 'Stéthoscope professionnel pour examens médicaux',
      url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop',
      category: 'Équipement',
      tags: ['stéthoscope', 'examen', 'médical'],
      uploadDate: new Date('2024-01-10'),
      uploadedBy: 'Dr. Dubois',
      size: 512000,
      format: 'PNG',
      dimensions: { width: 800, height: 600 },
      isFavorite: true,
    },
    {
      id: '3',
      title: 'Vaccin COVID-19',
      description: 'Vaccin contre le coronavirus pour la prévention',
      url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
      category: 'Vaccins',
      tags: ['vaccin', 'covid', 'prévention'],
      uploadDate: new Date('2024-01-12'),
      uploadedBy: 'Dr. Leroy',
      size: 320000,
      format: 'JPG',
      dimensions: { width: 800, height: 600 },
      isFavorite: false,
    },
    {
      id: '4',
      title: 'Thermomètre Digital',
      description: 'Thermomètre électronique pour mesure de température',
      url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop',
      category: 'Équipement',
      tags: ['thermomètre', 'température', 'diagnostic'],
      uploadDate: new Date('2024-01-08'),
      uploadedBy: 'Dr. Moreau',
      size: 180000,
      format: 'PNG',
      dimensions: { width: 800, height: 600 },
      isFavorite: true,
    },
  ]);

  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [uploading, setUploading] = useState(false);

  const [opened, { open, close }] = useDisclosure(false);
  const [viewerOpened, { open: openViewer, close: closeViewer }] = useDisclosure(false);
  const [uploadOpened, { open: openUpload, close: closeUpload }] = useDisclosure(false);

  const form = useForm({
    initialValues: {
      title: '',
      description: '',
      category: '',
      tags: '',
      file: null as File | null,
    },
  });

  const categories = [
    'Antibiotiques',
    'Équipement',
    'Vaccins',
    'Analgésiques',
    'Cardiovasculaire',
    'Dermatologie',
    'Autre',
  ];

  const itemsPerPage = 12;

  const filteredImages = images.filter((image) => {
    const matchesSearch = image.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         image.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         image.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = !selectedCategory || image.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const totalPages = Math.ceil(filteredImages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedImages = filteredImages.slice(startIndex, startIndex + itemsPerPage);

  const handleImageUpload = async (values: typeof form.values) => {
    if (!values.file) return;

    setUploading(true);
    try {
      // Simulate upload process
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newImage: ImageItem = {
        id: Date.now().toString(),
        title: values.title,
        description: values.description,
        url: URL.createObjectURL(values.file),
        thumbnail: URL.createObjectURL(values.file),
        category: values.category,
        tags: values.tags.split(',').map(tag => tag.trim()),
        uploadDate: new Date(),
        uploadedBy: 'Utilisateur actuel',
        size: values.file.size,
        format: values.file.type.split('/')[1].toUpperCase(),
        dimensions: { width: 800, height: 600 },
        isFavorite: false,
      };

      setImages(prev => [newImage, ...prev]);
      form.reset();
      closeUpload();

      notifications.show({
        title: 'Succès',
        message: 'Image téléchargée avec succès',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors du téléchargement de l\'image',
        color: 'red',
      });
    } finally {
      setUploading(false);
    }
  };

  const toggleFavorite = (imageId: string) => {
    setImages(prev => prev.map(img => 
      img.id === imageId ? { ...img, isFavorite: !img.isFavorite } : img
    ));
  };

  const deleteImage = (imageId: string) => {
    setImages(prev => prev.filter(img => img.id !== imageId));
    notifications.show({
      title: 'Succès',
      message: 'Image supprimée avec succès',
      color: 'green',
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconPhoto size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Galerie d'Images
          </Title>
        </Group>
        <Group>
          <Button
            leftSection={<IconUpload size={16} />}
            onClick={openUpload}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Télécharger
          </Button>
          <Button
            variant="outline"
            leftSection={<IconFilter size={16} />}
            onClick={open}
          >
            Filtres
          </Button>
        </Group>
      </Group>

      {/* Search and Filters */}
      <Grid mb="md">
        <Grid.Col span={6}>
          <TextInput
            placeholder="Rechercher des images..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Select
            placeholder="Catégorie"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Group>
            <ActionIcon
              variant={viewMode === 'grid' ? 'filled' : 'outline'}
              onClick={() => setViewMode('grid')}
            >
              <IconPhoto size={16} />
            </ActionIcon>
            <ActionIcon
              variant={viewMode === 'list' ? 'filled' : 'outline'}
              onClick={() => setViewMode('list')}
            >
              <IconFileText size={16} />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>

      {/* Images Grid */}
      {paginatedImages.length === 0 ? (
        <Center h={300}>
          <Stack align="center">
            <IconPhoto size={48} className="text-gray-400" />
            <Text c="dimmed">Aucune image trouvée</Text>
          </Stack>
        </Center>
      ) : (
        <SimpleGrid
          cols={viewMode === 'grid' ? { base: 1, sm: 2, md: 3, lg: 4 } : 1}
          spacing="md"
          mb="xl"
        >
          {paginatedImages.map((image) => (
            <Card key={image.id} withBorder shadow="sm" className="hover:shadow-md transition-shadow">
              <Card.Section>
                <Box pos="relative">
                  <Image
                    src={image.thumbnail}
                    height={viewMode === 'grid' ? 200 : 150}
                    alt={image.title}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedImage(image);
                      openViewer();
                    }}
                  />
                  <ActionIcon
                    pos="absolute"
                    top={8}
                    right={8}
                    variant="filled"
                    color={image.isFavorite ? 'red' : 'gray'}
                    onClick={() => toggleFavorite(image.id)}
                  >
                    {image.isFavorite ? <IconHeartFilled size={16} /> : <IconHeart size={16} />}
                  </ActionIcon>
                </Box>
              </Card.Section>

              <Stack gap="xs" mt="md">
                <Group justify="space-between">
                  <Text fw={500} size="sm" truncate>
                    {image.title}
                  </Text>
                  <Badge size="xs" color="blue">
                    {image.category}
                  </Badge>
                </Group>

                <Text size="xs" c="dimmed" lineClamp={2}>
                  {image.description}
                </Text>

                <Group justify="space-between" mt="xs">
                  <Group gap="xs">
                    <Text size="xs" c="dimmed">
                      {formatFileSize(image.size)}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {image.format}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ActionIcon size="sm" variant="subtle" onClick={() => {
                      setSelectedImage(image);
                      openViewer();
                    }}>
                      <IconEye size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle" color="red" onClick={() => deleteImage(image.id)}>
                      <IconTrash size={14} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Group justify="center">
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
          />
        </Group>
      )}

      {/* Upload Modal */}
      <Modal opened={uploadOpened} onClose={closeUpload} title="Télécharger une image" size="lg">
        <form onSubmit={form.onSubmit(handleImageUpload)}>
          <Stack>
            <FileInput
              label="Fichier image"
              placeholder="Sélectionner une image"
              accept="image/*"
              leftSection={<IconUpload size={16} />}
              {...form.getInputProps('file')}
              required
            />

            <TextInput
              label="Titre"
              placeholder="Titre de l'image"
              {...form.getInputProps('title')}
              required
            />

            <Textarea
              label="Description"
              placeholder="Description de l'image"
              rows={3}
              {...form.getInputProps('description')}
            />

            <Select
              label="Catégorie"
              placeholder="Sélectionner une catégorie"
              data={categories}
              {...form.getInputProps('category')}
              required
            />

            <TextInput
              label="Tags"
              placeholder="Tags séparés par des virgules"
              {...form.getInputProps('tags')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={closeUpload}>
                Annuler
              </Button>
              <Button type="submit" loading={uploading} leftSection={<IconUpload size={16} />}>
                Télécharger
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        opened={viewerOpened}
        onClose={closeViewer}
        title={selectedImage?.title}
        size="xl"
        centered
      >
        {selectedImage && (
          <Stack>
            <Image
              src={selectedImage.url}
              alt={selectedImage.title}
              fit="contain"
              h={400}
            />

            <Grid>
              <Grid.Col span={8}>
                <Stack gap="xs">
                  <Text fw={500}>{selectedImage.title}</Text>
                  <Text size="sm" c="dimmed">{selectedImage.description}</Text>

                  <Group gap="xs">
                    {selectedImage.tags.map((tag, index) => (
                      <Badge key={index} size="sm" variant="light">
                        {tag}
                      </Badge>
                    ))}
                  </Group>
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="xs">
                  <Group>
                    <IconUser size={16} />
                    <Text size="sm">{selectedImage.uploadedBy}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">{selectedImage.uploadDate.toLocaleDateString()}</Text>
                  </Group>

                  <Group>
                    <IconTag size={16} />
                    <Text size="sm">{selectedImage.category}</Text>
                  </Group>

                  <Divider />

                  <Text size="xs" c="dimmed">
                    Taille: {formatFileSize(selectedImage.size)}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Format: {selectedImage.format}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Dimensions: {selectedImage.dimensions.width} × {selectedImage.dimensions.height}
                  </Text>
                </Stack>
              </Grid.Col>
            </Grid>

            <Group justify="space-between" mt="md">
              <Group>
                <ActionIcon
                  variant="filled"
                  color={selectedImage.isFavorite ? 'red' : 'gray'}
                  onClick={() => toggleFavorite(selectedImage.id)}
                >
                  {selectedImage.isFavorite ? <IconHeartFilled size={16} /> : <IconHeart size={16} />}
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconShare size={16} />
                </ActionIcon>
              </Group>

              <Group>
                <Button variant="outline" leftSection={<IconDownload size={16} />}>
                  Télécharger
                </Button>
                <Button variant="outline" color="red" leftSection={<IconTrash size={16} />}
                  onClick={() => {
                    deleteImage(selectedImage.id);
                    closeViewer();
                  }}
                >
                  Supprimer
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Filters Modal */}
      <Modal opened={opened} onClose={close} title="Filtres avancés">
        <Stack>
          <Select
            label="Catégorie"
            placeholder="Toutes les catégories"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />

          <DatePickerInput
            label="Date de téléchargement"
            placeholder="Sélectionner une date"
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={close}>
              Fermer
            </Button>
            <Button onClick={close}>
              Appliquer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Paper>
  );
}
