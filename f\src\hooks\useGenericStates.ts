/**
 * Custom hook for managing generic-states data
 * Provides easy access to reports, analytics, and statistics
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  genericStatesService, 
  ActivityReport,
  FinancialReport,
  PatientAnalytics,
  MedicalReport,
  AppointmentAnalytics,
  GenericStatesSummary
} from '@/services/genericStatesService';

interface UseGenericStatesOptions {
  dateRange?: { start: string; end: string };
  autoFetch?: boolean;
  refreshInterval?: number;
  reportTypes?: string[];
}

interface UseGenericStatesReturn {
  // Data
  activityReports: ActivityReport[];
  financialReports: FinancialReport[];
  patientAnalytics: PatientAnalytics | null;
  medicalReports: MedicalReport[];
  appointmentAnalytics: AppointmentAnalytics | null;
  summary: GenericStatesSummary | null;
  
  // Loading states
  loading: boolean;
  activityLoading: boolean;
  financialLoading: boolean;
  patientLoading: boolean;
  medicalLoading: boolean;
  appointmentLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchActivityReports: (type?: 'daily' | 'periodic' | 'monthly' | 'annual') => Promise<void>;
  fetchFinancialReports: (type?: 'checks' | 'cash' | 'balance' | 'aged_balance') => Promise<void>;
  fetchPatientAnalytics: () => Promise<void>;
  fetchMedicalReports: (type?: 'doctor_performance' | 'medication_usage' | 'procedures') => Promise<void>;
  fetchAppointmentAnalytics: () => Promise<void>;
  fetchSummary: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Utility functions
  getActivityByType: (type: string) => ActivityReport[];
  getFinancialByType: (type: string) => FinancialReport[];
  getTotalRevenue: () => number;
  getTotalPatients: () => number;
  getTopProcedures: (limit?: number) => Array<{ name: string; count: number; revenue: number }>;
  getDoctorPerformance: () => Array<{ name: string; appointments: number; revenue: number }>;
  getRecentTrends: () => {
    appointmentTrend: 'up' | 'down' | 'stable';
    revenueTrend: 'up' | 'down' | 'stable';
    patientTrend: 'up' | 'down' | 'stable';
  };
}

export const useGenericStates = (options: UseGenericStatesOptions = {}): UseGenericStatesReturn => {
  const { dateRange, autoFetch = true, refreshInterval, reportTypes } = options;

  // State
  const [activityReports, setActivityReports] = useState<ActivityReport[]>([]);
  const [financialReports, setFinancialReports] = useState<FinancialReport[]>([]);
  const [patientAnalytics, setPatientAnalytics] = useState<PatientAnalytics | null>(null);
  const [medicalReports, setMedicalReports] = useState<MedicalReport[]>([]);
  const [appointmentAnalytics, setAppointmentAnalytics] = useState<AppointmentAnalytics | null>(null);
  const [summary, setSummary] = useState<GenericStatesSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [activityLoading, setActivityLoading] = useState(false);
  const [financialLoading, setFinancialLoading] = useState(false);
  const [patientLoading, setPatientLoading] = useState(false);
  const [medicalLoading, setMedicalLoading] = useState(false);
  const [appointmentLoading, setAppointmentLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchActivityReports = useCallback(async (type?: 'daily' | 'periodic' | 'monthly' | 'annual') => {
    setActivityLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getActivityReports(type, dateRange);
      setActivityReports(data);
    } catch (err) {
      setError(`Failed to fetch activity reports: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setActivityLoading(false);
    }
  }, [dateRange]);

  const fetchFinancialReports = useCallback(async (type?: 'checks' | 'cash' | 'balance' | 'aged_balance') => {
    setFinancialLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getFinancialReports(type, dateRange);
      setFinancialReports(data);
    } catch (err) {
      setError(`Failed to fetch financial reports: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setFinancialLoading(false);
    }
  }, [dateRange]);

  const fetchPatientAnalytics = useCallback(async () => {
    setPatientLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getPatientAnalytics(dateRange);
      setPatientAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch patient analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setPatientLoading(false);
    }
  }, [dateRange]);

  const fetchMedicalReports = useCallback(async (type?: 'doctor_performance' | 'medication_usage' | 'procedures') => {
    setMedicalLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getMedicalReports(type);
      setMedicalReports(data);
    } catch (err) {
      setError(`Failed to fetch medical reports: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setMedicalLoading(false);
    }
  }, []);

  const fetchAppointmentAnalytics = useCallback(async () => {
    setAppointmentLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getAppointmentAnalytics(dateRange);
      setAppointmentAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch appointment analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setAppointmentLoading(false);
    }
  }, [dateRange]);

  const fetchSummary = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await genericStatesService.getGenericStatesSummary(dateRange);
      setSummary(data);
      setActivityReports(data.activityReports);
      setFinancialReports(data.financialReports);
      setPatientAnalytics(data.patientAnalytics);
      setMedicalReports(data.medicalReports);
      setAppointmentAnalytics(data.appointmentAnalytics);
    } catch (err) {
      setError(`Failed to fetch generic states summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  const refreshAll = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchActivityReports(),
        fetchFinancialReports(),
        fetchPatientAnalytics(),
        fetchMedicalReports(),
        fetchAppointmentAnalytics(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchActivityReports, fetchFinancialReports, fetchPatientAnalytics, fetchMedicalReports, fetchAppointmentAnalytics]);

  // Utility functions
  const getActivityByType = useCallback((type: string) => {
    return activityReports.filter(report => report.type === type);
  }, [activityReports]);

  const getFinancialByType = useCallback((type: string) => {
    return financialReports.filter(report => report.type === type);
  }, [financialReports]);

  const getTotalRevenue = useCallback(() => {
    return activityReports.reduce((total, report) => total + report.revenue, 0);
  }, [activityReports]);

  const getTotalPatients = useCallback(() => {
    return patientAnalytics?.totalPatients || 0;
  }, [patientAnalytics]);

  const getTopProcedures = useCallback((limit: number = 5) => {
    const procedureMap = new Map<string, { count: number; revenue: number }>();
    
    activityReports.forEach(report => {
      report.procedures.forEach(procedure => {
        const existing = procedureMap.get(procedure.name) || { count: 0, revenue: 0 };
        procedureMap.set(procedure.name, {
          count: existing.count + procedure.count,
          revenue: existing.revenue + procedure.revenue,
        });
      });
    });

    return Array.from(procedureMap.entries())
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }, [activityReports]);

  const getDoctorPerformance = useCallback(() => {
    if (!medicalReports.length) return [];
    
    const doctorPerformance = medicalReports
      .flatMap(report => report.doctorStats)
      .map(doctor => ({
        name: doctor.doctorName,
        appointments: doctor.totalAppointments,
        revenue: doctor.revenue,
      }));

    return doctorPerformance.sort((a, b) => b.revenue - a.revenue);
  }, [medicalReports]);

  const getRecentTrends = useCallback(() => {
    // Simple trend analysis based on recent data
    const recentReports = activityReports.slice(-7); // Last 7 reports
    const olderReports = activityReports.slice(-14, -7); // Previous 7 reports

    const recentAvgAppointments = recentReports.reduce((sum, r) => sum + r.totalAppointments, 0) / recentReports.length || 0;
    const olderAvgAppointments = olderReports.reduce((sum, r) => sum + r.totalAppointments, 0) / olderReports.length || 0;

    const recentAvgRevenue = recentReports.reduce((sum, r) => sum + r.revenue, 0) / recentReports.length || 0;
    const olderAvgRevenue = olderReports.reduce((sum, r) => sum + r.revenue, 0) / olderReports.length || 0;

    const recentAvgPatients = recentReports.reduce((sum, r) => sum + r.summary.totalPatients, 0) / recentReports.length || 0;
    const olderAvgPatients = olderReports.reduce((sum, r) => sum + r.summary.totalPatients, 0) / olderReports.length || 0;

    const getTrend = (recent: number, older: number): 'up' | 'down' | 'stable' => {
      const change = ((recent - older) / older) * 100;
      if (change > 5) return 'up';
      if (change < -5) return 'down';
      return 'stable';
    };

    return {
      appointmentTrend: getTrend(recentAvgAppointments, olderAvgAppointments),
      revenueTrend: getTrend(recentAvgRevenue, olderAvgRevenue),
      patientTrend: getTrend(recentAvgPatients, olderAvgPatients),
    };
  }, [activityReports]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (reportTypes && reportTypes.length > 0) {
        // Fetch specific report types
        reportTypes.forEach(type => {
          switch (type) {
            case 'activity':
              fetchActivityReports();
              break;
            case 'financial':
              fetchFinancialReports();
              break;
            case 'patient':
              fetchPatientAnalytics();
              break;
            case 'medical':
              fetchMedicalReports();
              break;
            case 'appointment':
              fetchAppointmentAnalytics();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, reportTypes, refreshAll, fetchActivityReports, fetchFinancialReports, fetchPatientAnalytics, fetchMedicalReports, fetchAppointmentAnalytics]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    activityReports,
    financialReports,
    patientAnalytics,
    medicalReports,
    appointmentAnalytics,
    summary,
    
    // Loading states
    loading,
    activityLoading,
    financialLoading,
    patientLoading,
    medicalLoading,
    appointmentLoading,
    
    // Error state
    error,
    
    // Actions
    fetchActivityReports,
    fetchFinancialReports,
    fetchPatientAnalytics,
    fetchMedicalReports,
    fetchAppointmentAnalytics,
    fetchSummary,
    refreshAll,
    
    // Utility functions
    getActivityByType,
    getFinancialByType,
    getTotalRevenue,
    getTotalPatients,
    getTopProcedures,
    getDoctorPerformance,
    getRecentTrends,
  };
};

export default useGenericStates;
