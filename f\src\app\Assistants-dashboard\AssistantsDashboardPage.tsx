'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Group,
  Loader,
  Stack,
  Card,
  SimpleGrid,
  Badge,
  List,
  ThemeIcon,
  Timeline
} from '@mantine/core';
import {
  IconCalendar,
  IconUsers,
  IconFileInvoice,
  IconCheck,
  IconClock,
  IconMessage,
  IconCircleCheck,
  IconAlertCircle,
  IconHourglass
} from '@tabler/icons-react';
import authService from '~/services/authService';

// Define interface for User
interface User {
  first_name?: string;
  last_name?: string;
  email?: string;
  user_type?: string;
  assigned_doctor_name?: string;
  id?: string;
}

export default function AssistantsDashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [tasks, setTasks] = useState([
    { id: 1, title: '<PERSON><PERSON>par<PERSON> les dossiers pour les consultations du jour', status: 'completed' },
    { id: 2, title: 'Appeler les patients pour confirmer les rendez-vous de demain', status: 'pending' },
    { id: 3, title: 'Mettre à jour les dossiers médicaux des patients vus hier', status: 'pending' },
    { id: 4, title: 'Commander des fournitures médicales', status: 'pending' },
    { id: 5, title: 'Organiser la réunion d\'équipe hebdomadaire', status: 'pending' },
  ]);

  const [upcomingAppointments, ] = useState([
    { id: 1, patientName: 'Sophie Martin', time: '09:30', status: 'confirmed' },
    { id: 2, patientName: 'Jean Dupont', time: '10:15', status: 'confirmed' },
    { id: 3, patientName: 'Marie Lambert', time: '11:00', status: 'waiting' },
    { id: 4, patientName: 'Pierre Durand', time: '14:30', status: 'confirmed' },
  ]);

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      try {
        // Check if token exists in localStorage
        const token = localStorage.getItem('token');
        console.log('Current auth token:', token ? 'exists' : 'missing');

        if (!token) {
          console.log('No token found, redirecting to login');
          router.push('/login');
          return;
        }

        // Log all authentication data for debugging
        console.log('Authentication data:');
        console.log('- Token:', token ? `${token.substring(0, 15)}...` : 'missing');
        console.log('- User Type:', localStorage.getItem('userType') || 'not set');
        console.log('- Cookies:', document.cookie);

        // Check if user is assistant
        const userType = localStorage.getItem('userType');
        if (userType !== 'assistant') {
          console.log('User is not assistant, redirecting to appropriate dashboard');
          if (userType === 'doctor') {
            router.push('/dashboard');
          } else if (userType === 'staff') {
            router.push('/staff-dashboard');
          } else {
            router.push('/login');
          }
          return;
        }

        // Set a default user if we can't fetch the profile
        setUser({
          first_name: 'Assistant',
          last_name: 'Médical',
          email: '<EMAIL>',
          user_type: 'assistant'
        });

        // Try to fetch the actual user profile, but don't block on it
        try {
          const userProfile = await authService.getProfile();
          setUser(userProfile);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Continue with the default user
        }
      } catch (error) {
        console.error('Authentication error:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  const toggleTaskStatus = (taskId: number) => {
    setTasks(tasks.map(task =>
      task.id === taskId
        ? { ...task, status: task.status === 'completed' ? 'pending' : 'completed' }
        : task
    ));
  };

  if (loading) {
    return (
      <Container size="xl" py="xl">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <Loader size="xl" />
        </div>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Paper p="xl" radius="md" withBorder mb="xl" bg="blue.0">
        <Group justify="space-between" mb="md">
          <div>
            <Title order={2}>Bienvenue, {user?.first_name || 'Assistant'}</Title>
            <Text c="dimmed">Tableau de bord d&apos;assistant médical</Text>
            {user?.assigned_doctor_name && (
              <Text size="sm" c="dimmed">Médecin superviseur: Dr. {user.assigned_doctor_name}</Text>
            )}
          </div>
          <Badge size="xl" color="blue">Portail Assistant</Badge>
        </Group>
      </Paper>

      <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
        <Paper p="xl" radius="md" withBorder>
          <Title order={3} mb="md">Rendez-vous d&apos;aujourd&apos;hui</Title>
          <Timeline active={1} bulletSize={24} lineWidth={2}>
            {upcomingAppointments.map((appointment) => (
              <Timeline.Item
                key={appointment.id}
                bullet={
                  appointment.status === 'confirmed' ?
                    <IconCircleCheck size={12} /> :
                    appointment.status === 'waiting' ?
                      <IconHourglass size={12} /> :
                      <IconAlertCircle size={12} />
                }
                title={appointment.patientName}
              >
                <Text size="sm" c="dimmed">{appointment.time}</Text>
                <Badge
                  color={
                    appointment.status === 'confirmed' ? 'green' :
                    appointment.status === 'waiting' ? 'yellow' : 'red'
                  }
                  size="sm"
                  mt={4}
                >
                  {appointment.status === 'confirmed' ? 'Confirmé' :
                   appointment.status === 'waiting' ? 'En attente' : 'Annulé'}
                </Badge>
              </Timeline.Item>
            ))}
          </Timeline>
          <Button
            fullWidth
            mt="md"
            onClick={() => router.push('/appointments')}
          >
            Voir tous les rendez-vous
          </Button>
        </Paper>

        <Paper p="xl" radius="md" withBorder>
          <Title order={3} mb="md">Tâches</Title>
          <List spacing="md">
            {tasks.map(task => (
              <List.Item
                key={task.id}
                icon={
                  <ThemeIcon color={task.status === 'completed' ? 'green' : 'blue'} size={24} radius="xl">
                    {task.status === 'completed' ? <IconCheck size={16} /> : <IconClock size={16} />}
                  </ThemeIcon>
                }
                onClick={() => toggleTaskStatus(task.id)}
                style={{ cursor: 'pointer' }}
              >
                <Text style={{ textDecoration: task.status === 'completed' ? 'line-through' : 'none' }}>
                  {task.title}
                </Text>
              </List.Item>
            ))}
          </List>
        </Paper>
      </SimpleGrid>

      <Title order={3} mt="xl" mb="md">Accès rapide</Title>
      <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="lg">
        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconCalendar size={48} color="#228be6" />
            <Title order={3}>Rendez-vous</Title>
            <Text ta="center">Gérer les rendez-vous des patients</Text>
            <Button fullWidth onClick={() => router.push('/appointments')}>
              Accéder
            </Button>
          </Stack>
        </Card>

        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconUsers size={48} color="#40c057" />
            <Title order={3}>Patients</Title>
            <Text ta="center">Gérer les dossiers patients</Text>
            <Button fullWidth onClick={() => router.push('/patients')}>
              Accéder
            </Button>
          </Stack>
        </Card>

        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconMessage size={48} color="#7950f2" />
            <Title order={3}>Messages</Title>
            <Text ta="center">Communiquer avec l&apos;équipe médicale</Text>
            <Button fullWidth onClick={() => router.push('/messaging')}>
              Accéder
            </Button>
          </Stack>
        </Card>

        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconFileInvoice size={48} color="#fa5252" />
            <Title order={3}>Facturation</Title>
            <Text ta="center">Gérer la facturation des patients</Text>
            <Button fullWidth onClick={() => router.push('/billing')}>
              Accéder
            </Button>
          </Stack>
        </Card>
      </SimpleGrid>

      <Group justify="flex-start" mt="xl">
        <Button variant="subtle" onClick={() => authService.logout().then(() => router.push('/login'))}>
          Déconnexion
        </Button>
      </Group>
    </Container>
  );
}
