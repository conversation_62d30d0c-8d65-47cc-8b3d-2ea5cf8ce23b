import React, { useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Group,
  Stack,
  Table,
  Text,
  TextInput,
  ActionIcon,
  Tooltip,
  Modal,
  Select,
  Textarea,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
} from '@tabler/icons-react';

// Types pour les données du dictionnaire
interface DictionaryItem {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
}

interface Category {
  id: string;
  name: string;
  checked: boolean;
}

const Dictionnaire = () => {
  // États pour la gestion des données
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<DictionaryItem | null>(null);

  // États pour le formulaire modal
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    category: '',
    isActive: true,
  });

  // Données de test pour les catégories
  const [categories, setCategories] = useState<Category[]>([
    { id: 'pathologies', name: 'Pathologies', checked: false },
    { id: 'symptomes', name: 'Symptômes', checked: false },
    { id: 'medicaments', name: 'Médicaments', checked: false },
    { id: 'examens', name: 'Examens', checked: false },
    { id: 'procedures', name: 'Procédures', checked: false },
    { id: 'diagnostics', name: 'Diagnostics', checked: false },
  ]);

  // Données de test pour les éléments du dictionnaire
  const [dictionaryItems, setDictionaryItems] = useState<DictionaryItem[]>([
    {
      id: 1,
      code: 'PATH001',
      name: 'Hypertension artérielle',
      description: 'Élévation anormale de la pression artérielle',
      category: 'pathologies',
      isActive: true,
    },
    {
      id: 2,
      code: 'SYMP001',
      name: 'Céphalée',
      description: 'Douleur de la tête',
      category: 'symptomes',
      isActive: true,
    },
    {
      id: 3,
      code: 'MED001',
      name: 'Paracétamol',
      description: 'Antalgique et antipyrétique',
      category: 'medicaments',
      isActive: true,
    },
    {
      id: 4,
      code: 'EXAM001',
      name: 'Radiographie thoracique',
      description: 'Examen radiologique du thorax',
      category: 'examens',
      isActive: true,
    },
  ]);

  // Options pour les catégories dans le formulaire
  const categoryOptions = categories.map(cat => ({
    value: cat.id,
    label: cat.name,
  }));

  // Gestion des catégories sélectionnées
  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    setCategories(prev =>
      prev.map(cat =>
        cat.id === categoryId ? { ...cat, checked } : cat
      )
    );

    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId]);
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
    }
  };

  // Filtrage des éléments
  const filteredItems = dictionaryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategories.length === 0 ||
                           selectedCategories.includes(item.category);

    return matchesSearch && matchesCategory;
  });

  // Ouverture du modal pour nouveau/édition
  const openModal = (item?: DictionaryItem) => {
    if (item) {
      setEditingItem(item);
      setFormData({
        code: item.code,
        name: item.name,
        description: item.description,
        category: item.category,
        isActive: item.isActive,
      });
    } else {
      setEditingItem(null);
      setFormData({
        code: '',
        name: '',
        description: '',
        category: '',
        isActive: true,
      });
    }
    setIsModalOpen(true);
  };

  // Fermeture du modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingItem(null);
    setFormData({
      code: '',
      name: '',
      description: '',
      category: '',
      isActive: true,
    });
  };

  // Sauvegarde d'un élément
  const saveItem = () => {
    if (editingItem) {
      // Modification
      setDictionaryItems(prev =>
        prev.map(item =>
          item.id === editingItem.id
            ? { ...item, ...formData }
            : item
        )
      );
    } else {
      // Création
      const newItem: DictionaryItem = {
        id: Date.now(),
        ...formData,
      };
      setDictionaryItems(prev => [...prev, newItem]);
    }
    closeModal();
  };

  // Suppression d'un élément
  const deleteItem = (id: number) => {
    setDictionaryItems(prev => prev.filter(item => item.id !== id));
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* Sidebar gauche avec les catégories */}
      <div className="w-64 bg-white border-r border-gray-200 p-4">
        <Text size="sm" fw={600} mb="md" className="text-gray-800">
          Catégories
        </Text>
        <Stack gap="xs">
          {categories.map((category) => (
            <Checkbox
              key={category.id}
              label={category.name}
              checked={category.checked}
              onChange={(event) =>
                handleCategoryChange(category.id, event.currentTarget.checked)
              }
              size="sm"
            />
          ))}
        </Stack>
      </div>

      {/* Zone de contenu principal */}
      <div className="flex-1 flex flex-col">
        {/* Barre de recherche et bouton Nouveau */}
        <Card
          shadow="none"
          padding="md"
          radius={0}
          className="bg-white border-b border-gray-200"
        >
          <Group justify="space-between" align="center">
            <TextInput
              placeholder="Rechercher dans le dictionnaire..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="flex-1 max-w-md"
              size="sm"
            />
            <Button
              size="sm"
              variant="filled"
              color="blue"
              leftSection={<IconPlus size={16} />}
              onClick={() => openModal()}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Nouveau
            </Button>
          </Group>
        </Card>

        {/* Tableau des éléments du dictionnaire */}
        <div className="flex-1 bg-white overflow-hidden">
          <Table
            striped={false}
            highlightOnHover={true}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50 sticky top-0">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Code
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Description
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Catégorie
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Statut
                </Table.Th>
                <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-center">
                  Actions
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredItems.map((item) => (
                <Table.Tr key={item.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-200 text-sm">
                    {item.code}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm font-medium">
                    {item.name}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm text-gray-600">
                    {item.description}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm">
                    {categories.find(cat => cat.id === item.category)?.name || item.category}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-200 text-sm">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {item.isActive ? 'Actif' : 'Inactif'}
                    </span>
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Group gap="xs" justify="center">
                      <Tooltip label="Modifier">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          size="sm"
                          onClick={() => openModal(item)}
                        >
                          <IconEdit size={14} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Supprimer">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          size="sm"
                          onClick={() => deleteItem(item.id)}
                        >
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {filteredItems.length === 0 && (
            <div className="flex items-center justify-center h-32 text-gray-500">
              <Text size="sm">Aucun élément trouvé</Text>
            </div>
          )}
        </div>
      </div>

      {/* Modal pour ajouter/modifier un élément */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title={editingItem ? 'Modifier l\'élément' : 'Nouvel élément'}
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Code"
            placeholder="Entrez le code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
            required
          />

          <TextInput
            label="Nom"
            placeholder="Entrez le nom"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />

          <Textarea
            label="Description"
            placeholder="Entrez la description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
          />

          <Select
            label="Catégorie"
            placeholder="Sélectionnez une catégorie"
            value={formData.category}
            onChange={(value) => setFormData({ ...formData, category: value || '' })}
            data={categoryOptions}
            required
          />

          <Checkbox
            label="Actif"
            checked={formData.isActive}
            onChange={(e) => setFormData({ ...formData, isActive: e.currentTarget.checked })}
          />

          <Group justify="flex-end" gap="sm" mt="md">
            <Button variant="outline" onClick={closeModal}>
              Annuler
            </Button>
            <Button onClick={saveItem} disabled={!formData.code || !formData.name || !formData.category}>
              {editingItem ? 'Modifier' : 'Ajouter'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Dictionnaire;
