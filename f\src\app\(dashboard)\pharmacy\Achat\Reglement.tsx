'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  Tabs,
  Table,
  Text,
  NumberInput,
  Textarea,
  ScrollArea,
  Pagination,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconSearch,
  IconPlus,
  IconList,
  IconFileText,
  IconDeviceFloppy,
  IconCheck,
  IconX,
} from '@tabler/icons-react';

// TypeScript interfaces
interface ReglementDocument {
  id: string;
  numeroDocument: string;
  document: string;
  date: string;
  montant: number;
  resteARegler: number;
  montantEncaisse: number;
}

interface ReglementData {
  numero: string;
  date: Date | null;
  dateEcheance: Date | null;
  fournisseur: string;
  mode: string;
  banque: string;
  ref: string;
  montantEncaisse: number;
  montantConsomme: number;
  reliquat: number;
  commentaire: string;
  documents: ReglementDocument[];
}

const Reglement = () => {
  const [activeTab, setActiveTab] = useState<string>('documents');
  const [currentReglement, setCurrentReglement] = useState<ReglementData>({
    numero: '2',
    date: new Date('2022-09-16'),
    dateEcheance: new Date('2022-09-16'),
    fournisseur: '',
    mode: 'Espèce',
    banque: 'Aucune',
    ref: '',
    montantEncaisse: 0.00,
    montantConsomme: 0.00,
    reliquat: 0.00,
    commentaire: '',
    documents: [],
  });

  const form = useForm({
    initialValues: currentReglement,
  });

  const modes = [
    'Espèce',
    'Chèque',
    'Virement',
    'Carte bancaire',
  ];

  const banques = [
    'Aucune',
    'Banque A',
    'Banque B',
    'Banque C',
  ];

  const handleSave = () => {
    notifications.show({
      title: 'Règlement enregistré',
      message: 'Le règlement a été enregistré avec succès',
      color: 'green',
    });
  };

  const handleValidateHeader = () => {
    notifications.show({
      title: 'En-tête validé',
      message: 'L\'en-tête du règlement a été validé',
      color: 'blue',
    });
  };

  const handleCancel = () => {
    form.reset();
    notifications.show({
      title: 'Annulé',
      message: 'Les modifications ont été annulées',
      color: 'orange',
    });
  };

  return (
    <div className="p-4 w-full">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group justify="space-between" align="center">
          <Group align="center">
            <Title order={3} className="text-slate-600">
              Règlement N°: {currentReglement.numero}
            </Title>
          </Group>
          <Button
            variant="outline"
            className="text-white border-white hover:bg-white hover:text-slate-600"
            leftSection={<IconList size={16} />}
          >
            Liste des règlements
          </Button>
        </Group>
      </Paper>

      {/* Main Form */}
      <Paper p="md" mb="md" withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="Fournisseur"
                placeholder="Fournisseur"
                {...form.getInputProps('fournisseur')}
                leftSection={<IconSearch size={16} />}
                rightSection={<IconPlus size={16} />}
              />
            </Grid.Col>
            <Grid.Col span={9}>
              {/* Empty space for layout */}
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={2}>
              <TextInput
                label="N°.Règlement *"
                placeholder="2"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <DatePickerInput
                label="Date *"
                placeholder="16/09/2022"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <DatePickerInput
                label="Date d'échéanc..."
                placeholder="16/09/2022"
                {...form.getInputProps('dateEcheance')}
                styles={{
                  input: {
                    borderColor: '#ff6b6b',
                    color: '#ff6b6b',
                  },
                  label: {
                    color: '#ff6b6b',
                  }
                }}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              {/* Empty space for layout */}
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={2}>
              <Select
                label="Mode"
                placeholder="Espèce"
                data={modes}
                {...form.getInputProps('mode')}
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <TextInput
                label="Ref."
                placeholder=""
                {...form.getInputProps('ref')}
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <Select
                label="Banque"
                placeholder="Aucune"
                data={banques}
                {...form.getInputProps('banque')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              {/* Empty space for layout */}
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={2}>
              <NumberInput
                label="Montant encaissé *"
                placeholder="0,00"
                {...form.getInputProps('montantEncaisse')}
                decimalScale={2}
                fixedDecimalScale
                required
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <NumberInput
                label="Montant consommé"
                placeholder="0,00"
                {...form.getInputProps('montantConsomme')}
                decimalScale={2}
                fixedDecimalScale
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <NumberInput
                label="Reliquat"
                placeholder="0,00"
                {...form.getInputProps('reliquat')}
                decimalScale={2}
                fixedDecimalScale
              />
            </Grid.Col>
            <Grid.Col span={6}>
              {/* Empty space for layout */}
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={12}>
              <Textarea
                label="Commentaire"
                placeholder=""
                {...form.getInputProps('commentaire')}
                minRows={2}
              />
            </Grid.Col>
          </Grid>
        </form>
      </Paper>

      {/* Tabs Section */}
      <Paper withBorder mb="md">
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'documents')}>
          <Tabs.List>
            <Tabs.Tab value="documents" leftSection={<IconFileText size={16} />}>
              Documents à régler
            </Tabs.Tab>
            <Tabs.Tab value="lignes" leftSection={<IconList size={16} />}>
              Lignes règlement
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="documents" pt="md">
            {/* Documents Table */}
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>N°. Document</Table.Th>
                    <Table.Th>Document</Table.Th>
                    <Table.Th>Date</Table.Th>
                    <Table.Th>Montant</Table.Th>
                    <Table.Th>Reste à régler</Table.Th>
                    <Table.Th>Montant encaissé</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {currentReglement.documents.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={6} className="text-center text-gray-500 py-8">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    currentReglement.documents.map((doc) => (
                      <Table.Tr key={doc.id}>
                        <Table.Td>{doc.numeroDocument}</Table.Td>
                        <Table.Td>{doc.document}</Table.Td>
                        <Table.Td>{doc.date}</Table.Td>
                        <Table.Td>{doc.montant.toFixed(2)}</Table.Td>
                        <Table.Td>{doc.resteARegler.toFixed(2)}</Table.Td>
                        <Table.Td>{doc.montantEncaisse.toFixed(2)}</Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Pagination */}
            <Group justify="space-between" mt="md">
              <Group>
                <Text size="sm">Page</Text>
                <Select
                  size="sm"
                  w={60}
                  data={['1']}
                  value="1"
                />
                <Text size="sm">Lignes par Page</Text>
                <Select
                  size="sm"
                  w={80}
                  data={['5', '10', '25', '50']}
                  value="5"
                />
                <Text size="sm">0 - 0 de 0</Text>
              </Group>
              <Group>
                <Button size="xs" variant="outline">K</Button>
                <Button size="xs" variant="outline">‹</Button>
                <Button size="xs" variant="outline">›</Button>
                <Button size="xs" variant="outline">⟩</Button>
              </Group>
            </Group>
          </Tabs.Panel>

          <Tabs.Panel value="lignes" pt="md">
            <Text className="text-gray-500 text-center py-8">
              Aucune ligne de règlement
            </Text>
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Action Buttons */}
      <Group justify="flex-end" mt="md" gap="md">
        <Button
          variant="filled"
          color="red"
          onClick={handleCancel}
          leftSection={<IconX size={16} />}
        >
          Annuler
        </Button>
        <Button
          variant="filled"
          color="gray"
          onClick={handleValidateHeader}
          leftSection={<IconCheck size={16} />}
        >
          Valider l&apos;en-tête
        </Button>
        <Button
          variant="filled"
          className="bg-blue-500 hover:bg-blue-600"
          onClick={handleSave}
          leftSection={<IconDeviceFloppy size={16} />}
        >
          Enregistrer et quitter
        </Button>
        <Button
          variant="filled"
          className="bg-green-500 hover:bg-green-600"
          onClick={handleSave}
          leftSection={<IconDeviceFloppy size={16} />}
        >
          Enregistrer
        </Button>
      </Group>
    </div>
  );
};

export default Reglement;
