import companyData from './companies.json';


export type Company = {
   uid: string;
  date: string;
  type: 'visit' | 'plan' | 'medical-plan';
  Beneficiaire: string;
  Montant_du: number;
  Montant_encaisse: number;
  Avancement: number;
  Etat: 'paid' | 'partial' | 'unpaid' | 'exempt';
  Reste_A_regler: number;
  Remise: number;
  MontantEncaisse: number;
 selected: false
 
};

export type Department = {
  id: string;
  Date: string;
  company: Company;
};

export type Employee = {
  id: string;
  sex: 'male' | 'female';
  firstName: string;
  lastName: string;
  email: string;
  birthDate: string;
  department: Department;
};

export const companies: Company[] = companyData;


