
"use client";
import { useState } from "react";
import React from "react";

import Icon from '@mdi/react';
import { mdiCalendarOutline } from '@mdi/js';
import MetaSeo from"./MetaSeo"
import  MesFacturesList from"./Mes_facuers_list"
import  Mes_devis_list from "./Mes_devis_list"
import  Mes_reglements from "./Mes_reglements"
import  Mes_depenses from "./Mes_depenses"

import Mes_contrats from "./Mes_contrats"
import "~/styles/tab.css";

function  AppointmentsPage() {

  const [toggleState, setToggleState] = useState(1);
 

const icons = [
  
{ icon: <Icon path={mdiCalendarOutline} size={1} key="Mes_facuers" />, label: "Mes facuers" },
{ icon: <Icon path={mdiCalendarOutline} size={1} key="Mes_devis_list" />, label: "Mes devis" },
{ icon: <Icon path={mdiCalendarOutline} size={1} key="Mes_reglements" />, label: "Mes reglements" },
{ icon: <Icon path={mdiCalendarOutline} size={1} key="Mes_depenses" />, label: "Mes depenses" },
{ icon: <Icon path={mdiCalendarOutline} size={1} key="Mes_contrats" />, label: "Mes contrats" },




];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<MesFacturesList/> )
   case 2:
      return (<Mes_devis_list/> )
       case 3:
      return (<Mes_reglements/> )
       case 4:
      return (<Mes_depenses/> )
      case 5:
      return (<Mes_contrats/> )
    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 
