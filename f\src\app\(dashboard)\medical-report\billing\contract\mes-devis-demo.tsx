'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { MesDevis } from './Mes_devis';

export default function MesDevisDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Recherche de devis:\nTerme: "${query.searchAll}"\nPage: ${query.page}\nLignes: ${query.limit}\nFiltres: ${Object.keys(query.filters).length} actifs\nRecherche par colonne: ${Object.keys(query.search).length} actives`);
  };

  const handleAddQuotation = () => {
    console.log('Ajouter devis');
    alert(`Création d'un nouveau devis:\nOuverture du formulaire de saisie\nChamps: N°. Devis, Date, Bénéficiaire, Valide Jusqu'au, Montant Total`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export:', format);
    alert(`Export Excel des devis en cours...\nFormat: ${format.toUpperCase()}\nTéléchargement démarré`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action:', action, 'Items:', items);
    const actionLabels: { [key: string]: string } = {
      'reload': 'Actualiser'
    };
    const actionLabel = actionLabels[action] || action;
    alert(`Action: ${actionLabel}\nNombre d'éléments sélectionnés: ${items.length}\nTraitement en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onAddQuotation={handleAddQuotation}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MesDevisLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onAddQuotation={() => console.log('Add quotation')}
          onExport={(format) => console.log('Export:', format)}
          onAction={(action, items) => console.log('Action:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MesDevisWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      quotationNumber: 'DEV-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      validUntil: new Date('2025-07-16'),
      totalAmount: 1250.00
    },
    {
      id: '2',
      quotationNumber: 'DEV-002',
      date: new Date('2025-07-01'),
      beneficiary: 'DUPONT Marie',
      validUntil: new Date('2025-07-15'),
      totalAmount: 850.00
    },
    {
      id: '3',
      quotationNumber: 'DEV-003',
      date: new Date('2025-06-30'),
      beneficiary: 'BERNARD Paul',
      validUntil: new Date('2025-07-14'),
      totalAmount: 2100.00
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    alert(`Recherche dans ${sampleItems.length} devis:\nTerme: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nFiltres actifs: ${Object.keys(query.filters).length}\nRecherche par colonne: ${Object.keys(query.search).length}`);
  };

  const handleAddQuotation = () => {
    console.log('Ajouter devis avec données');
    alert(`Nouveau devis:\n\nProchain numéro: DEV-${String(sampleItems.length + 1).padStart(3, '0')}\nFormulaire de création ouvert\nDonnées pré-remplies disponibles\nMontant total actuel: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export avec données:', format);
    alert(`Export Excel de ${sampleItems.length} devis:\n\nContenu:\n- Total des devis: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€\n- Bénéficiaires: ${new Set(sampleItems.map(item => item.beneficiary)).size}\n- Période: ${new Date(Math.min(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')} - ${new Date(Math.max(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')}\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action avec données:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'reload': `Actualisation des données:\n- ${sampleItems.length} devis rechargés\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}\n- Total: ${sampleItems.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(2)}€`
    };
    
    const message = actionMessages[action] || `Action ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddQuotation={handleAddQuotation}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres
export function MesDevisFiltersDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtres:', query);
    if (query.searchAll) {
      alert(`Recherche avancée:\nTerme: "${query.searchAll}"\nRecherche dans: N°. Devis, Bénéficiaires, etc.`);
    }
    if (Object.keys(query.filters).length > 0) {
      alert(`Filtres appliqués:\n${Object.entries(query.filters).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`);
    }
    if (Object.keys(query.search).length > 0) {
      alert(`Recherche par colonne:\n${Object.entries(query.search).map(([key, value]) => `- ${key}: "${value}"`).join('\n')}`);
    }
  };

  const handleAddQuotation = () => {
    console.log('Ajouter avec filtres');
    alert(`Nouveau devis:\nLes filtres actuels seront conservés après création`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddQuotation={handleAddQuotation}
          onExport={(format) => alert(`Export ${format} avec filtres appliqués`)}
          onAction={(action, items) => console.log('Action filtres:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function MesDevisPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les devis`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={[]}
          total={150} // Simule 150 devis pour tester la pagination
          onQueryChange={handleQueryChange}
          onAddQuotation={() => console.log('Add pagination')}
          onExport={(format) => console.log('Export pagination:', format)}
          onAction={(action, items) => console.log('Action pagination:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MesDevisErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleAddQuotation = () => {
    console.log('Ajouter avec validation');
    if (confirm('Êtes-vous sûr de vouloir créer un nouveau devis ?')) {
      alert('Devis créé avec succès !');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddQuotation={handleAddQuotation}
          onExport={(format) => {
            if (confirm(`Êtes-vous sûr de vouloir exporter en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onAction={(action, items) => console.log('Action avec validation:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec statuts de devis
export function MesDevisStatusDemo() {
  const sampleItemsWithStatus = [
    {
      id: '1',
      quotationNumber: 'DEV-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      validUntil: new Date('2025-07-16'),
      totalAmount: 1250.00
    },
    {
      id: '2',
      quotationNumber: 'DEV-002',
      date: new Date('2025-07-01'),
      beneficiary: 'DUPONT Marie',
      validUntil: new Date('2025-07-15'),
      totalAmount: 850.00
    },
    {
      id: '3',
      quotationNumber: 'DEV-003',
      date: new Date('2025-06-30'),
      beneficiary: 'BERNARD Paul',
      validUntil: new Date('2025-07-14'),
      totalAmount: 2100.00
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête par statuts:', query);
    if (query.searchAll) {
      const searchTerm = query.searchAll.toLowerCase();
      const validQuotations = sampleItemsWithStatus.filter(item => 
        item.validUntil > new Date()
      );
      const expiredQuotations = sampleItemsWithStatus.filter(item => 
        item.validUntil <= new Date()
      );
      
      alert(`Statuts des devis:\n- Valides: ${validQuotations.length}\n- Expirés: ${expiredQuotations.length}\n- Total: ${sampleItemsWithStatus.length}`);
    }
  };

  const handleAddQuotation = () => {
    console.log('Ajouter par statuts');
    alert(`Nouveau devis:\nStatuts disponibles:\n- Brouillon\n- En attente\n- Validé\n- Expiré\n- Annulé`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDevis
          loading={false}
          items={sampleItemsWithStatus}
          total={sampleItemsWithStatus.length}
          onQueryChange={handleQueryChange}
          onAddQuotation={handleAddQuotation}
          onExport={(format) => alert(`Export ${format} par statuts de devis`)}
          onAction={(action, items) => console.log('Action statuts:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}
