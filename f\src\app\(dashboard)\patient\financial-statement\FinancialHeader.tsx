// components/FinancialHeader.tsx
import { Group, Button, Text, Flex, Title, Anchor, ActionIcon } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiArrowLeft, mdiCurrencyUsd, mdiRefreshCircle } from '@mdi/js';
// import Link from 'next/link';
type PatientInfo = {
  id: number;
  full_name: string;
  gender: string;
  age?: number;
  file_number?: string;
  default_insurance?: string;
};

type FinancialHeaderProps = {
  patient: PatientInfo;
  patientContainer: boolean;
  onGoBack: () => void;
  onRefresh: (event: React.MouseEvent<HTMLButtonElement>) => void;
};

export const FinancialHeader = ({
  patient,
  patientContainer,
  onGoBack,
  onRefresh,
}: FinancialHeaderProps) => {
  return (
    <Group justify="space-between" align="center" wrap="nowrap" className="financial-header" >
        <Group>
      {/* Back Button */}
      {patientContainer && (
        <ActionIcon
          onClick={onGoBack}
          variant="default"
          aria-label="go back"
          size="lg"
        >
          <Icon path={mdiArrowLeft} size={1} />
        </ActionIcon>
      )}

      {/* Title */}
      <Flex direction="row" justify="center" gap={0}>
        {patientContainer && <Title order={3} mr={8}>État financier de :</Title>}
        <Flex gap="xs" className={!patientContainer ? 'flex' : ''}>
          <Text fw={600}>{patient.full_name}</Text>
          <Text>{patient.gender}</Text>
          {patient.age !== undefined && <Text>{patient.age} ans</Text>}
          {patient.file_number && <Text>{patient.file_number}</Text>}
          {patient.default_insurance && <Text>{patient.default_insurance}</Text>}
        </Flex>
      </Flex>
</Group>
 <Group justify="flex-end">
      {/* Spacer */}
      <Flex justify="flex-end" className="flex" />

      {/* New Payment Link */}
      <Anchor
        //href={`/pratisoft/payment/form/?patientId=${patient.id}`}
        underline="never"
      >
        <Button leftSection={<Icon path={mdiCurrencyUsd} size={1} />} variant="subtle" 
         href={`/patient/financial-statement/NouvelEncaissement`}
        component="a"
        >
          Nouvel encaissement
        </Button>
      </Anchor>

      {/* Refresh Button */}
      <ActionIcon
        onClick={onRefresh}
        variant="default"
        aria-label="refresh"
        size="lg"
      >
        <Icon path={mdiRefreshCircle} size={1} />
      </ActionIcon>
      </Group>
    </Group>
  );
};
