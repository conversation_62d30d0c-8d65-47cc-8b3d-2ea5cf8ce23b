'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  Textarea,
  Table,
  Modal,
  Tabs,
  Radio,
  Pagination,
  ScrollArea,
  Badge,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconList,
  IconTrash,
  IconFileText,
  IconPaperclip,
  IconMessageCircle,
  IconBarcode,
  IconShoppingCart,
  IconCheck,
  IconDeviceFloppy,
  IconX,
  IconPackage,
  IconCurrencyEuro,
  IconEdit,
  IconTransfer,
  IconPlus,
} from '@tabler/icons-react';

// Interface pour les articles d'échange
interface ArticleEchange {
  id: string;
  code: string;
  designation: string;
  qte: number;
  prix: number;
  tva: number;
  depotSource: string;
  depotDestination: string;
  montant: number;
}

// Interface pour l'échange inter-dépôts
interface EchangeInterDepots {
  numero: string;
  date: Date;
  depotSource: string;
  depotDestination: string;
  commentaire: string;
  articles: ArticleEchange[];
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
}

const List_Echange_inter_depots = () => {
  // États pour la gestion de l'interface
  const [activeTab, setActiveTab] = useState('details');
  const [opened, { open, close }] = useDisclosure(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Données d'exemple des dépôts
  const depots = [
    { value: 'depot1', label: 'Dépôt 1' },
    { value: 'depot2', label: 'Dépôt 2' },
    { value: 'depot3', label: 'Dépôt 3' },
    { value: 'depot4', label: 'Dépôt 4' },
  ];

  // État pour l'échange actuel
  const [currentEchange, setCurrentEchange] = useState<EchangeInterDepots>({
    numero: '1',
    date: new Date(),
    depotSource: 'depot1',
    depotDestination: '',
    commentaire: '',
    articles: [],
    montantHT: 0,
    montantTVA: 0,
    montantTTC: 0,
  });

  // Formulaire principal
  const form = useForm({
    initialValues: {
      numero: currentEchange.numero,
      date: currentEchange.date,
      depotSource: currentEchange.depotSource,
      depotDestination: currentEchange.depotDestination,
      commentaire: currentEchange.commentaire,
    },
  });

  // Formulaire pour ajouter un article
  const itemForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      qte: 1,
      prix: 0,
      tva: 20,
      depotSource: '',
      depotDestination: '',
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      designation: (value) => (!value ? 'La désignation est requise' : null),
      qte: (value) => (value <= 0 ? 'La quantité doit être positive' : null),
    },
  });

  // Calcul des montants
  const calculateMontants = (articles: ArticleEchange[]) => {
    const montantHT = articles.reduce((sum, article) => sum + article.montant, 0);
    const montantTVA = articles.reduce((sum, article) => sum + (article.montant * article.tva / 100), 0);
    const montantTTC = montantHT + montantTVA;

    return { montantHT, montantTVA, montantTTC };
  };

  // Ajouter un article
  const addItem = (values: typeof itemForm.values) => {
    const montant = values.qte * values.prix;
    const newArticle: ArticleEchange = {
      id: Date.now().toString(),
      code: values.code,
      designation: values.designation,
      qte: values.qte,
      prix: values.prix,
      tva: values.tva,
      depotSource: values.depotSource,
      depotDestination: values.depotDestination,
      montant,
    };

    const updatedArticles = [...currentEchange.articles, newArticle];
    const montants = calculateMontants(updatedArticles);

    setCurrentEchange(prev => ({
      ...prev,
      articles: updatedArticles,
      ...montants,
    }));

    itemForm.reset();
    close();

    notifications.show({
      title: 'Succès',
      message: 'Article ajouté avec succès',
      color: 'green',
    });
  };

  // Supprimer un article
  const removeItem = (id: string) => {
    const updatedArticles = currentEchange.articles.filter(article => article.id !== id);
    const montants = calculateMontants(updatedArticles);

    setCurrentEchange(prev => ({
      ...prev,
      articles: updatedArticles,
      ...montants,
    }));

    notifications.show({
      title: 'Succès',
      message: 'Article supprimé avec succès',
      color: 'red',
    });
  };

  // Pagination des articles
  const totalPages = Math.ceil(currentEchange.articles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentArticles = currentEchange.articles.slice(startIndex, endIndex);

  // Sauvegarder l'échange
  const handleSave = () => {
    notifications.show({
      title: 'Succès',
      message: 'Échange inter-dépôts enregistré avec succès',
      color: 'green',
    });
  };

  // Valider l'échange
  const handleValidate = () => {
    notifications.show({
      title: 'Succès',
      message: 'Échange inter-dépôts validé avec succès',
      color: 'blue',
    });
  };

  return (
    <Paper p="md" withBorder className="w-full">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <Group align="center">
          <IconTransfer size={24} color="blue" />
          <Title order={3} c="blue">
            Échange inter-dépôts
          </Title>
        </Group>
        <Group>
          <Button
            leftSection={<IconList size={16} />}
            variant="outline"
            color="blue"
          >
            Échange inter-dépôts
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N°"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="16/09/2024"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Dépôt source"
                placeholder="Dépôt 1"
                data={depots}
                {...form.getInputProps('depotSource')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Dépôt de destination"
                placeholder="Sélectionner un dépôt"
                data={depots}
                {...form.getInputProps('depotDestination')}
                required
              />
            </Grid.Col>
          </Grid>
        </form>
      </Card>

      <Divider my="md" />

      {/* Tabs */}
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
            Détails
          </Tabs.Tab>
          <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
            Pièces jointes
          </Tabs.Tab>
          <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
            Commentaires
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="details" pt="md">
          {/* Action Buttons */}
          <Group justify="flex-end" mb="md">
            <Button
              leftSection={<IconBarcode size={16} />}
              variant="outline"
              color="blue"
            >
              Code à barres
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              color="blue"
              onClick={open}
            >
              Article
            </Button>
            <Button
              leftSection={<IconMessageCircle size={16} />}
              variant="outline"
              color="gray"
            >
              Commentaire
            </Button>
          </Group>

          {/* Articles Table */}
          <ScrollArea>
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Code</Table.Th>
                  <Table.Th>Désignation</Table.Th>
                  <Table.Th>Qté</Table.Th>
                  <Table.Th>Prix</Table.Th>
                  <Table.Th>Tva</Table.Th>
                  <Table.Th>Dépôt</Table.Th>
                  <Table.Th>Dépôt de destination</Table.Th>
                  <Table.Th>Montant</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {currentArticles.length === 0 ? (
                  <Table.Tr>
                    <Table.Td colSpan={9} style={{ textAlign: 'center', padding: '2rem' }}>
                      <Text c="dimmed">Aucun élément trouvé</Text>
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  currentArticles.map((article) => (
                    <Table.Tr key={article.id}>
                      <Table.Td>{article.code}</Table.Td>
                      <Table.Td>{article.designation}</Table.Td>
                      <Table.Td>{article.qte}</Table.Td>
                      <Table.Td>{article.prix.toFixed(2)}</Table.Td>
                      <Table.Td>{article.tva}%</Table.Td>
                      <Table.Td>
                        <Badge color="blue" variant="light" size="sm">
                          {depots.find(d => d.value === article.depotSource)?.label || article.depotSource}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color="green" variant="light" size="sm">
                          {depots.find(d => d.value === article.depotDestination)?.label || article.depotDestination}
                        </Badge>
                      </Table.Td>
                      <Table.Td>{article.montant.toFixed(2)}</Table.Td>
                      <Table.Td>
                        <ActionIcon
                          color="red"
                          variant="subtle"
                          onClick={() => removeItem(article.id)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {/* Pagination */}
          {totalPages > 1 && (
            <Group justify="center" mt="md">
              <Pagination
                value={currentPage}
                onChange={setCurrentPage}
                total={totalPages}
                size="sm"
              />
            </Group>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="pieces-jointes" pt="md">
          <Text c="dimmed" ta="center" py="xl">
            Aucune pièce jointe
          </Text>
        </Tabs.Panel>

        <Tabs.Panel value="commentaires" pt="md">
          <Textarea
            label="Commentaire"
            placeholder="Ajouter un commentaire..."
            rows={4}
            {...form.getInputProps('commentaire')}
          />
        </Tabs.Panel>
      </Tabs>

      {/* Summary */}
      <Card withBorder mt="md">
        <Group justify="space-between" align="center">
          <Group>
            <Text size="sm" c="dimmed">
              Page {currentPage} sur {totalPages} - {currentEchange.articles.length} articles
            </Text>
          </Group>
          <Group>
            <Stack gap="xs" align="flex-end">
              <Text size="sm">
                <strong>MONTANT HT :</strong> {currentEchange.montantHT.toFixed(2)}
              </Text>
              <Text size="sm">
                <strong>MONTANT TVA :</strong> {currentEchange.montantTVA.toFixed(2)}
              </Text>
              <Text size="sm">
                <strong>MONTANT TTC :</strong> {currentEchange.montantTTC.toFixed(2)}
              </Text>
            </Stack>
          </Group>
        </Group>
      </Card>

      {/* Action Buttons */}
      <Group justify="flex-end" mt="md">
        <Button variant="outline" color="red">
          Annuler
        </Button>
        <Button variant="outline" color="gray">
          Valider
        </Button>
        <Button
          leftSection={<IconDeviceFloppy size={16} />}
          color="blue"
          onClick={handleSave}
        >
          Enregistrer et quitter
        </Button>
        <Button
          leftSection={<IconCheck size={16} />}
          color="green"
          onClick={handleValidate}
        >
          Enregistrer
        </Button>
      </Group>

      {/* Modal pour ajouter un article */}
      <Modal
        opened={opened}
        onClose={close}
        title="Ajouter un article"
        size="lg"
      >
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder="Code article"
                  {...itemForm.getInputProps('code')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Désignation"
                  placeholder="Désignation"
                  {...itemForm.getInputProps('designation')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité"
                  placeholder="1"
                  {...itemForm.getInputProps('qte')}
                  min={1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Prix"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('prix')}
                  min={0}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="TVA (%)"
                  placeholder="20"
                  {...itemForm.getInputProps('tva')}
                  min={0}
                  max={100}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Dépôt source"
                  placeholder="Sélectionner un dépôt"
                  data={depots}
                  {...itemForm.getInputProps('depotSource')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Dépôt de destination"
                  placeholder="Sélectionner un dépôt"
                  data={depots}
                  {...itemForm.getInputProps('depotDestination')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
};

export default List_Echange_inter_depots
