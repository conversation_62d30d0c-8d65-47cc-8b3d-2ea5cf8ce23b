import {
  Button,
  Group,
  Modal,
  NumberInput,
  Stack,
  Textarea,
  Select,
  Text,
  Loader,
  Alert
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { IconPlus, IconAlertCircle } from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { notifications } from '@mantine/notifications';
import patientService, { BiometricMeasureDefinition } from '@/services/patientService';

interface MeasurementValues {
  measure_definition_id: string;
  value: string;
  measurement_date: Date;
  notes?: string;
}

interface MeasurementDialogProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (values: MeasurementValues) => void;
  patientId?: string;
}

export function MeasurementDialog({
  opened,
  onClose,
  onSubmit,
  patientId,
}: MeasurementDialogProps) {
  const [loading, setLoading] = useState(false);
  const [biometricDefinitions, setBiometricDefinitions] = useState<BiometricMeasureDefinition[]>([]);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<MeasurementValues>({
    initialValues: {
      measure_definition_id: '',
      value: '',
      measurement_date: new Date(),
      notes: '',
    },
    validate: {
      measure_definition_id: (value) => (!value ? 'Please select a measurement type' : null),
      value: (value) => (!value ? 'Please enter a value' : null),
    },
  });

  // Load biometric definitions from Django
  useEffect(() => {
    if (opened) {
      loadBiometricDefinitions();
    }
  }, [opened]);

  const loadBiometricDefinitions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        throw new Error('Django backend is not connected');
      }

      // Load biometric definitions
      const definitions = await patientService.getBiometricMeasureDefinitions();
      if (definitions) {
        setBiometricDefinitions(definitions);
      }
    } catch (error) {
      console.error('Error loading biometric definitions:', error);
      setError('Failed to load measurement types. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: MeasurementValues) => {
    if (!patientId) {
      notifications.show({
        title: 'Error',
        message: 'No patient ID provided',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);

      // Save measurement to Django
      const result = await patientService.addPatientBiometricMeasurement(patientId, {
        measure_definition_id: values.measure_definition_id,
        value: values.value,
        measurement_date: values.measurement_date.toISOString(),
        notes: values.notes,
        is_abnormal: false
      });

      if (result) {
        notifications.show({
          title: 'Success',
          message: 'Measurement saved successfully',
          color: 'green',
        });

        // Call parent onSubmit callback
        onSubmit(values);

        // Reset form and close
        form.reset();
        onClose();
      } else {
        throw new Error('Failed to save measurement');
      }
    } catch (error) {
      console.error('Error saving measurement:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save measurement. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconPlus size={20} />
          <span>Add Biometric Measurement</span>
        </Group>
      }
      centered
      size="md"
    >
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} color="red" mb="md">
          {error}
        </Alert>
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <DateInput
            label="Measurement Date"
            {...form.getInputProps('measurement_date')}
            disabled={loading}
          />

          <Select
            label="Measurement Type"
            placeholder="Select measurement type"
            data={biometricDefinitions.map(def => ({
              value: def.id,
              label: `${def.label}${def.unit ? ` (${def.unit})` : ''}`
            }))}
            {...form.getInputProps('measure_definition_id')}
            disabled={loading}
            searchable
          />

          <NumberInput
            label="Value"
            placeholder="Enter measurement value"
            {...form.getInputProps('value')}
            disabled={loading}
          />

          <Textarea
            label="Notes (Optional)"
            placeholder="Additional notes..."
            {...form.getInputProps('notes')}
            disabled={loading}
            rows={3}
          />
        </Stack>

        <Group justify="flex-end" mt="xl">
          <Button variant="light" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            disabled={!form.values.measure_definition_id || !form.values.value}
          >
            Save Measurement
          </Button>
        </Group>
      </form>

      {loading && biometricDefinitions.length === 0 && (
        <Group justify="center" mt="md">
          <Loader size="sm" />
          <Text size="sm" c="dimmed">Loading measurement types...</Text>
        </Group>
      )}
    </Modal>
  );
}
