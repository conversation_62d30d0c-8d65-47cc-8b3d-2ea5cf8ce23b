import api from '../lib/api';

// Global Patient Types for the entire application
export interface GlobalPatient {
  id: string;
  title?: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email?: string;
  phone_number?: string;
  date_of_birth?: string;
  gender?: 'M' | 'F' | 'Other';
  address?: string;
  social_security?: string;
  medical_history?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  default_insurance?: string;
  file_number?: string;
  notes?: string;
  age?: number;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_visit_date?: string;
  next_appointment_date?: string;
  total_visits?: number;
  total_appointments?: number;
}

export interface PatientSearchResult {
  id: string;
  full_name: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  email?: string;
  file_number?: string;
  last_visit_date?: string;
  age?: number;
  gender?: string;
}

export interface PatientSummary {
  id: string;
  full_name: string;
  age?: number;
  gender?: string;
  phone_number?: string;
  email?: string;
  last_visit_date?: string;
  next_appointment_date?: string;
  total_visits: number;
  active_insurances: number;
  recent_diagnoses: string[];
  risk_factors: string[];
  allergies: string[];
}

export interface PatientAppointment {
  id: string;
  patient: string;
  appointment_date: string;
  appointment_time: string;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  appointment_type: string;
  doctor?: string;
  notes?: string;
  created_at: string;
}

export interface PatientVisit {
  id: string;
  patient: string;
  visit_date: string;
  visit_type: string;
  chief_complaint?: string;
  diagnosis?: string;
  treatment?: string;
  follow_up_date?: string;
  doctor?: string;
  notes?: string;
  created_at: string;
}

export interface PatientStats {
  total_patients: number;
  new_patients_this_month: number;
  active_patients: number;
  patients_with_appointments_today: number;
  average_age: number;
  gender_distribution: {
    male: number;
    female: number;
    other: number;
  };
}

// API Helper function
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: object | null,
  params?: Record<string, string | number | boolean | undefined>
) => {
  try {
    const config = { params };
    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      const response = await api[method](endpoint, data || {}, config);
      return response.data;
    }
  } catch (error) {
    console.error(`❌ Global Patient API Error [${method.toUpperCase()} ${endpoint}]:`, error);
    throw error;
  }
};

// Global Patient Service - Used throughout the entire application
export const globalPatientService = {
  // Core Patient Operations
  getAllPatients: async (params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
    gender?: string;
    age_min?: number;
    age_max?: number;
  }): Promise<{ results: GlobalPatient[]; count: number; next?: string; previous?: string }> => {
    console.log('👥 Fetching all patients...', params);
    return makeApiCall('/api/patients/', 'get', undefined, params);
  },

  getPatient: async (id: string): Promise<GlobalPatient> => {
    console.log(`👤 Fetching patient ${id}...`);
    return makeApiCall(`/api/patients/${id}/`);
  },

  createPatient: async (data: Partial<GlobalPatient>): Promise<GlobalPatient> => {
    console.log('👤 Creating new patient...', data);
    // Use the auth register endpoint for patient creation
    // The data should already be properly formatted from the calling code
    return makeApiCall('/api/auth/register/', 'post', data);
  },

  updatePatient: async (id: string, data: Partial<GlobalPatient>): Promise<GlobalPatient> => {
    console.log(`👤 Updating patient ${id}...`, data);
    return makeApiCall(`/api/patients/${id}/`, 'put', data);
  },

  deletePatient: async (id: string): Promise<void> => {
    console.log(`🗑️ Deleting patient ${id}...`);
    return makeApiCall(`/api/patients/${id}/`, 'delete');
  },

  // Search and Filter Operations
  searchPatients: async (query: string, limit = 10): Promise<PatientSearchResult[]> => {
    console.log(`🔍 Searching patients: "${query}"...`);
    const response = await makeApiCall('/api/patients/search/', 'get', undefined, { 
      q: query, 
      limit 
    });
    return response.results || response;
  },

  getPatientSummary: async (id: string): Promise<PatientSummary> => {
    console.log(`📊 Fetching patient summary for ${id}...`);
    return makeApiCall(`/api/patients/${id}/summary/`);
  },

  // Appointment Operations
  getPatientAppointments: async (
    patientId: string, 
    params?: { 
      status?: string; 
      date_from?: string; 
      date_to?: string; 
      limit?: number 
    }
  ): Promise<PatientAppointment[]> => {
    console.log(`📅 Fetching appointments for patient ${patientId}...`);
    const response = await makeApiCall('/api/appointments/', 'get', undefined, {
      patient: patientId,
      ...params
    });
    return response.results || response;
  },

  getUpcomingAppointments: async (patientId: string): Promise<PatientAppointment[]> => {
    console.log(`📅 Fetching upcoming appointments for patient ${patientId}...`);
    const today = new Date().toISOString().split('T')[0];
    return globalPatientService.getPatientAppointments(patientId, {
      date_from: today,
      status: 'scheduled'
    });
  },

  // Visit Operations
  getPatientVisits: async (
    patientId: string,
    params?: { 
      date_from?: string; 
      date_to?: string; 
      limit?: number 
    }
  ): Promise<PatientVisit[]> => {
    console.log(`🏥 Fetching visits for patient ${patientId}...`);
    const response = await makeApiCall('/api/visits/', 'get', undefined, {
      patient: patientId,
      ...params
    });
    return response.results || response;
  },

  getRecentVisits: async (patientId: string, limit = 5): Promise<PatientVisit[]> => {
    console.log(`🏥 Fetching recent visits for patient ${patientId}...`);
    return globalPatientService.getPatientVisits(patientId, { limit });
  },

  // Statistics and Analytics
  getPatientStats: async (): Promise<PatientStats> => {
    console.log('📊 Fetching patient statistics...');
    return makeApiCall('/api/patients/stats/');
  },

  getPatientsWithAppointmentsToday: async (): Promise<GlobalPatient[]> => {
    console.log('📅 Fetching patients with appointments today...');
    const today = new Date().toISOString().split('T')[0];
    const response = await makeApiCall('/api/patients/with-appointments/', 'get', undefined, {
      date: today
    });
    return response.results || response;
  },

  getNewPatientsThisMonth: async (): Promise<GlobalPatient[]> => {
    console.log('👥 Fetching new patients this month...');
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const response = await makeApiCall('/api/patients/', 'get', undefined, {
      created_at__gte: startOfMonth.toISOString().split('T')[0]
    });
    return response.results || response;
  },

  // Utility Functions
  formatPatientName: (patient: GlobalPatient | PatientSearchResult): string => {
    const title = 'title' in patient && patient.title ? `${patient.title} ` : '';
    return `${title}${patient.first_name} ${patient.last_name}`.trim();
  },

  calculateAge: (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  },

  getPatientInitials: (patient: GlobalPatient | PatientSearchResult): string => {
    return `${patient.first_name.charAt(0)}${patient.last_name.charAt(0)}`.toUpperCase();
  },

  formatPhoneNumber: (phone?: string): string => {
    if (!phone) return '';
    // Basic phone formatting - can be enhanced based on locale
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  },

  getPatientStatusColor: (patient: GlobalPatient): string => {
    if (!patient.is_active) return 'gray';
    if (patient.next_appointment_date) return 'green';
    if (patient.last_visit_date) {
      const lastVisit = new Date(patient.last_visit_date);
      const monthsAgo = (Date.now() - lastVisit.getTime()) / (1000 * 60 * 60 * 24 * 30);
      if (monthsAgo > 12) return 'red';
      if (monthsAgo > 6) return 'orange';
    }
    return 'blue';
  },

  // Export/Import Functions
  exportPatients: async (format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> => {
    console.log(`📤 Exporting patients as ${format}...`);
    const response = await api.get(`/api/patients/export/`, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  },

  importPatients: async (file: File): Promise<{ success: number; errors: string[] }> => {
    console.log('📥 Importing patients from file...');
    const formData = new FormData();
    formData.append('file', file);
    return makeApiCall('/api/patients/import/', 'post', formData);
  }
};

export default globalPatientService;
