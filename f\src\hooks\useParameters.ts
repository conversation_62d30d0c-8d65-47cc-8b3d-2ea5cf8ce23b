/**
 * Custom hook for managing parameters data
 * Provides easy access to system parameters, users, specialties, and configurations
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  parametersService, 
  SystemParameter,
  UserProfile,
  SystemUser,
  Specialty,
  Contact,
  Technician,
  ApplicationConfiguration,
  DataBackup,
  SystemAnalytics,
  ParametersSummary
} from '@/services/parametersService';

interface UseParametersOptions {
  autoFetch?: boolean;
  refreshInterval?: number;
  dataTypes?: string[];
}

interface UseParametersReturn {
  // Data
  systemParameters: SystemParameter[];
  userProfiles: UserProfile[];
  systemUsers: SystemUser[];
  specialties: Specialty[];
  contacts: Contact[];
  technicians: Technician[];
  applicationConfigurations: ApplicationConfiguration[];
  dataBackups: DataBackup[];
  analytics: SystemAnalytics | null;
  summary: ParametersSummary | null;
  
  // Loading states
  loading: boolean;
  parametersLoading: boolean;
  usersLoading: boolean;
  specialtiesLoading: boolean;
  contactsLoading: boolean;
  techniciansLoading: boolean;
  configurationsLoading: boolean;
  backupsLoading: boolean;
  analyticsLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchSystemParameters: (category?: string, subcategory?: string) => Promise<void>;
  fetchUserProfiles: () => Promise<void>;
  fetchSystemUsers: (profileId?: string, specialtyId?: string) => Promise<void>;
  fetchSpecialties: () => Promise<void>;
  fetchContacts: (type?: string) => Promise<void>;
  fetchTechnicians: (specialty?: string) => Promise<void>;
  fetchApplicationConfigurations: (module?: string) => Promise<void>;
  fetchDataBackups: () => Promise<void>;
  fetchAnalytics: () => Promise<void>;
  fetchSummary: () => Promise<void>;
  refreshAll: () => Promise<void>;
  updateSystemParameter: (parameterId: string, value: string | number | boolean) => Promise<void>;
  
  // Utility functions
  getParametersByCategory: (category: string) => SystemParameter[];
  getParameterValue: (parameterKey: string) => string | number | boolean | null;
  getUsersByProfile: (profileId: string) => SystemUser[];
  getUsersBySpecialty: (specialtyId: string) => SystemUser[];
  getActiveUsers: () => SystemUser[];
  getActiveSpecialties: () => Specialty[];
  getContactsByType: (type: string) => Contact[];
  getTechniciansBySpecialty: (specialty: string) => Technician[];
  getConfigurationsByModule: (module: string) => ApplicationConfiguration[];
  getRecentBackups: (limit?: number) => DataBackup[];
  getSystemStats: () => {
    totalUsers: number;
    activeUsers: number;
    totalSpecialties: number;
    activeSpecialties: number;
    totalParameters: number;
    totalContacts: number;
    totalTechnicians: number;
    lastBackupDate: string | null;
    systemUptime: number;
    configurationChanges: number;
  };
  getSystemHealth: () => {
    status: 'healthy' | 'warning' | 'critical';
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    responseTime: number;
    errorRate: number;
    uptime: number;
  };
}

export const useParameters = (options: UseParametersOptions = {}): UseParametersReturn => {
  const { autoFetch = true, refreshInterval, dataTypes } = options;

  // State
  const [systemParameters, setSystemParameters] = useState<SystemParameter[]>([]);
  const [userProfiles, setUserProfiles] = useState<UserProfile[]>([]);
  const [systemUsers, setSystemUsers] = useState<SystemUser[]>([]);
  const [specialties, setSpecialties] = useState<Specialty[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [applicationConfigurations, setApplicationConfigurations] = useState<ApplicationConfiguration[]>([]);
  const [dataBackups, setDataBackups] = useState<DataBackup[]>([]);
  const [analytics, setAnalytics] = useState<SystemAnalytics | null>(null);
  const [summary, setSummary] = useState<ParametersSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [parametersLoading, setParametersLoading] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [specialtiesLoading, setSpecialtiesLoading] = useState(false);
  const [contactsLoading, setContactsLoading] = useState(false);
  const [techniciansLoading, setTechniciansLoading] = useState(false);
  const [configurationsLoading, setConfigurationsLoading] = useState(false);
  const [backupsLoading, setBackupsLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchSystemParameters = useCallback(async (category?: string, subcategory?: string) => {
    setParametersLoading(true);
    setError(null);
    try {
      const data = await parametersService.getSystemParameters(category, subcategory);
      setSystemParameters(data);
    } catch (err) {
      setError(`Failed to fetch system parameters: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setParametersLoading(false);
    }
  }, []);

  const fetchUserProfiles = useCallback(async () => {
    setError(null);
    try {
      const data = await parametersService.getUserProfiles();
      setUserProfiles(data);
    } catch (err) {
      setError(`Failed to fetch user profiles: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, []);

  const fetchSystemUsers = useCallback(async (profileId?: string, specialtyId?: string) => {
    setUsersLoading(true);
    setError(null);
    try {
      const data = await parametersService.getSystemUsers(profileId, specialtyId);
      setSystemUsers(data);
    } catch (err) {
      setError(`Failed to fetch system users: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setUsersLoading(false);
    }
  }, []);

  const fetchSpecialties = useCallback(async () => {
    setSpecialtiesLoading(true);
    setError(null);
    try {
      const data = await parametersService.getSpecialties();
      setSpecialties(data);
    } catch (err) {
      setError(`Failed to fetch specialties: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setSpecialtiesLoading(false);
    }
  }, []);

  const fetchContacts = useCallback(async (type?: string) => {
    setContactsLoading(true);
    setError(null);
    try {
      const data = await parametersService.getContacts(type);
      setContacts(data);
    } catch (err) {
      setError(`Failed to fetch contacts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setContactsLoading(false);
    }
  }, []);

  const fetchTechnicians = useCallback(async (specialty?: string) => {
    setTechniciansLoading(true);
    setError(null);
    try {
      const data = await parametersService.getTechnicians(specialty);
      setTechnicians(data);
    } catch (err) {
      setError(`Failed to fetch technicians: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setTechniciansLoading(false);
    }
  }, []);

  const fetchApplicationConfigurations = useCallback(async (module?: string) => {
    setConfigurationsLoading(true);
    setError(null);
    try {
      const data = await parametersService.getApplicationConfigurations(module);
      setApplicationConfigurations(data);
    } catch (err) {
      setError(`Failed to fetch application configurations: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setConfigurationsLoading(false);
    }
  }, []);

  const fetchDataBackups = useCallback(async () => {
    setBackupsLoading(true);
    setError(null);
    try {
      const data = await parametersService.getDataBackups();
      setDataBackups(data);
    } catch (err) {
      setError(`Failed to fetch data backups: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setBackupsLoading(false);
    }
  }, []);

  const fetchAnalytics = useCallback(async () => {
    setAnalyticsLoading(true);
    setError(null);
    try {
      const data = await parametersService.getSystemAnalytics();
      setAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch system analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setAnalyticsLoading(false);
    }
  }, []);

  const fetchSummary = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await parametersService.getParametersSummary();
      setSummary(data);
      setSystemParameters(data.systemParameters);
      setUserProfiles(data.userProfiles);
      setSystemUsers(data.systemUsers);
      setSpecialties(data.specialties);
      setContacts(data.contacts);
      setTechnicians(data.technicians);
      setApplicationConfigurations(data.applicationConfigurations);
      setDataBackups(data.dataBackups);
      setAnalytics(data.analytics);
    } catch (err) {
      setError(`Failed to fetch parameters summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAll = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchSystemParameters(),
        fetchUserProfiles(),
        fetchSystemUsers(),
        fetchSpecialties(),
        fetchContacts(),
        fetchTechnicians(),
        fetchApplicationConfigurations(),
        fetchDataBackups(),
        fetchAnalytics(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchSystemParameters, fetchUserProfiles, fetchSystemUsers, fetchSpecialties, fetchContacts, fetchTechnicians, fetchApplicationConfigurations, fetchDataBackups, fetchAnalytics]);

  const updateSystemParameter = useCallback(async (parameterId: string, value: string | number | boolean) => {
    setError(null);
    try {
      const updatedParameter = await parametersService.updateSystemParameter(parameterId, value);
      setSystemParameters(prev => prev.map(p => p.id === parameterId ? updatedParameter : p));
    } catch (err) {
      setError(`Failed to update system parameter: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const getParametersByCategory = useCallback((category: string) => {
    return systemParameters.filter(p => p.category === category);
  }, [systemParameters]);

  const getParameterValue = useCallback((parameterKey: string) => {
    const parameter = systemParameters.find(p => p.parameter_key === parameterKey);
    return parameter ? parameter.parameter_value : null;
  }, [systemParameters]);

  const getUsersByProfile = useCallback((profileId: string) => {
    return systemUsers.filter(u => u.profile_id === profileId);
  }, [systemUsers]);

  const getUsersBySpecialty = useCallback((specialtyId: string) => {
    return systemUsers.filter(u => u.specialty_id === specialtyId);
  }, [systemUsers]);

  const getActiveUsers = useCallback(() => {
    return systemUsers.filter(u => u.is_active);
  }, [systemUsers]);

  const getActiveSpecialties = useCallback(() => {
    return specialties.filter(s => s.is_active);
  }, [specialties]);

  const getContactsByType = useCallback((type: string) => {
    return contacts.filter(c => c.type === type);
  }, [contacts]);

  const getTechniciansBySpecialty = useCallback((specialty: string) => {
    return technicians.filter(t => t.specialty === specialty);
  }, [technicians]);

  const getConfigurationsByModule = useCallback((module: string) => {
    return applicationConfigurations.filter(c => c.module === module);
  }, [applicationConfigurations]);

  const getRecentBackups = useCallback((limit: number = 5) => {
    return dataBackups
      .sort((a, b) => new Date(b.backup_date).getTime() - new Date(a.backup_date).getTime())
      .slice(0, limit);
  }, [dataBackups]);

  const getSystemStats = useCallback(() => {
    const activeUsers = getActiveUsers();
    const activeSpecialties = getActiveSpecialties();
    const lastBackup = getRecentBackups(1)[0];

    return {
      totalUsers: systemUsers.length,
      activeUsers: activeUsers.length,
      totalSpecialties: specialties.length,
      activeSpecialties: activeSpecialties.length,
      totalParameters: systemParameters.length,
      totalContacts: contacts.length,
      totalTechnicians: technicians.length,
      lastBackupDate: lastBackup?.backup_date || null,
      systemUptime: analytics?.system_uptime || 0,
      configurationChanges: analytics?.configuration_changes.length || 0,
    };
  }, [systemUsers, specialties, systemParameters, contacts, technicians, analytics, getActiveUsers, getActiveSpecialties, getRecentBackups]);

  const getSystemHealth = useCallback(() => {
    if (!analytics) {
      return {
        status: 'warning' as const,
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        responseTime: 0,
        errorRate: 0,
        uptime: 0,
      };
    }

    const { system_performance, system_uptime } = analytics;
    const { cpu_usage, memory_usage, disk_usage, response_time, error_rate } = system_performance;

    // Determine system health status
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (cpu_usage > 90 || memory_usage > 90 || disk_usage > 95 || error_rate > 5 || system_uptime < 95) {
      status = 'critical';
    } else if (cpu_usage > 70 || memory_usage > 80 || disk_usage > 85 || error_rate > 1 || system_uptime < 99) {
      status = 'warning';
    }

    return {
      status,
      cpuUsage: cpu_usage,
      memoryUsage: memory_usage,
      diskUsage: disk_usage,
      responseTime: response_time,
      errorRate: error_rate,
      uptime: system_uptime,
    };
  }, [analytics]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (dataTypes && dataTypes.length > 0) {
        // Fetch specific data types
        dataTypes.forEach(type => {
          switch (type) {
            case 'parameters':
              fetchSystemParameters();
              break;
            case 'users':
              fetchSystemUsers();
              break;
            case 'specialties':
              fetchSpecialties();
              break;
            case 'contacts':
              fetchContacts();
              break;
            case 'technicians':
              fetchTechnicians();
              break;
            case 'configurations':
              fetchApplicationConfigurations();
              break;
            case 'backups':
              fetchDataBackups();
              break;
            case 'analytics':
              fetchAnalytics();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, dataTypes, refreshAll, fetchSystemParameters, fetchSystemUsers, fetchSpecialties, fetchContacts, fetchTechnicians, fetchApplicationConfigurations, fetchDataBackups, fetchAnalytics]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    systemParameters,
    userProfiles,
    systemUsers,
    specialties,
    contacts,
    technicians,
    applicationConfigurations,
    dataBackups,
    analytics,
    summary,
    
    // Loading states
    loading,
    parametersLoading,
    usersLoading,
    specialtiesLoading,
    contactsLoading,
    techniciansLoading,
    configurationsLoading,
    backupsLoading,
    analyticsLoading,
    
    // Error state
    error,
    
    // Actions
    fetchSystemParameters,
    fetchUserProfiles,
    fetchSystemUsers,
    fetchSpecialties,
    fetchContacts,
    fetchTechnicians,
    fetchApplicationConfigurations,
    fetchDataBackups,
    fetchAnalytics,
    fetchSummary,
    refreshAll,
    updateSystemParameter,
    
    // Utility functions
    getParametersByCategory,
    getParameterValue,
    getUsersByProfile,
    getUsersBySpecialty,
    getActiveUsers,
    getActiveSpecialties,
    getContactsByType,
    getTechniciansBySpecialty,
    getConfigurationsByModule,
    getRecentBackups,
    getSystemStats,
    getSystemHealth,
  };
};

export default useParameters;
