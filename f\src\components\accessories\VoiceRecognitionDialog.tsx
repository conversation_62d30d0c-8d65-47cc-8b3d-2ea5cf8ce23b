'use client';

import React, { useState, useRef, useEffect } from 'react';

import {Group,Text,Modal,Divider,Button,Indicator} from '@mantine/core';
import Icon from '@mdi/react';
import { mdiHeadset,mdiMicrophone } from '@mdi/js';

// Type declarations for Speech Recognition API (not fully supported in all TypeScript versions)
interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string;
  readonly message: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  serviceURI: string;
  grammars: SpeechGrammarList;
  
  onstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null;
  onend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onnomatch: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onaudiostart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onaudioend: ((this: SpeechRecognition, ev: Event) => void) | null;
  
  start(): void;
  stop(): void;
  abort(): void;
}

interface SpeechRecognitionConstructor {
  new (): SpeechRecognition;
  readonly prototype: SpeechRecognition;
}

declare global {
  interface Window {
    SpeechRecognition?: SpeechRecognitionConstructor;
    webkitSpeechRecognition?: SpeechRecognitionConstructor;
  }
}

interface VoiceRecognitionDialogProps {
  isOpen: boolean;
  onClose?: () => void;
  onSubmit?: (speechText: string) => void;
}

 export const VoiceRecognitionDialog: React.FC<VoiceRecognitionDialogProps> = ({ isOpen, onClose, onSubmit }) => {
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [validSpeech, setValidSpeech] = useState('');
  const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
  const [isListening, setIsListening] = useState(false);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const validSpeechRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    // Initialize speech recognition if available
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognitionClass = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognitionClass) {
        recognitionRef.current = new SpeechRecognitionClass();
      
        if (recognitionRef.current) {
          recognitionRef.current.continuous = true;
          recognitionRef.current.interimResults = true;
          recognitionRef.current.lang = 'fr-FR';

          recognitionRef.current.onstart = (): void => {
            setIsListening(true);
            setInvalidSpeech('Parlez maintenant...');
          };
          recognitionRef.current.onresult = (event: SpeechRecognitionEvent): void => {
            let interimTranscript = '';
            let finalTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
              const transcript = event.results[i][0].transcript;
              if (event.results[i].isFinal) {
                finalTranscript += transcript;
              } else {
                interimTranscript += transcript;
              }
            }
            setValidSpeech(finalTranscript);
            setInvalidSpeech(interimTranscript || 'Parlez maintenant...');
          };

          recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent): void => {
            console.error('Speech recognition error:', event.error);
            setIsListening(false);
            setIsRecognizing(false);
            setInvalidSpeech('Erreur de reconnaissance vocale.');
          };
          recognitionRef.current.onend = (): void => {
            setIsListening(false);
            if (isRecognizing) {
              // Restart if still in recognition mode
              recognitionRef.current?.start();
            }
          };
        }
      }
    }
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [isRecognizing]);
  const toggleRecognition = () => {
    if (!recognitionRef.current) {
      alert('La reconnaissance vocale n\'est pas supportée par ce navigateur.');
      return;
    }
    if (isRecognizing) {
      recognitionRef.current.stop();
      setIsRecognizing(false);
      setIsListening(false);
    } else {
      recognitionRef.current.start();
      setIsRecognizing(true);
    }
  };
  const emptyContent = () => {
    setValidSpeech('');
    setInvalidSpeech('Parlez maintenant.');
    if (validSpeechRef.current) {
      validSpeechRef.current.textContent = '';
    }
  };

  const handleSubmit = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsRecognizing(false);
    onSubmit?.(validSpeech);
  };
  const handleCancel = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsRecognizing(false);
    setIsListening(false);
    onClose?.();
  };
  const handleValidSpeechEdit = (e: React.FormEvent<HTMLSpanElement>) => {
    const target = e.target as HTMLSpanElement;
    setValidSpeech(target.textContent || '');
  };

  if (!isOpen) return null;
  return (
    <>
  <Modal.Content className="overflow-y-hidden">
      <Modal.Header style={{ height: '40px', background: "#3799CE", padding: "8px 11px", minHeight: "calc(1.75rem * var(--mantine-scale))" }}>
        <Modal.Title>
          <Group justify="space-between">
         <Icon path={mdiHeadset} size={1} color={"var(--mantine-color-white)"}/>
            <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Reconnaissance vocale</Text>
          </Group>
        </Modal.Title>
        <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }}/>
      </Modal.Header>
      <Modal.Body>
  
           
        <div className="h-[220px]">
          
           
      {/* Dialog Content */}
        <div className="p-2 mt-4">
          <div className="flex items-start gap-4">
            <div className="flex-1">
              {/* <p className="text-sm text-gray-600 mb-2">Reconnaissance vocale:</p> */}
              <div className="min-h-[120px] p-3 border rounded-md bg-gray-50 border-[#f0f0f4]">
                <span
                  ref={validSpeechRef}
                  contentEditable={true}
                  suppressContentEditableWarning={true}
                 
                  onInput={handleValidSpeechEdit}
                  style={{ minHeight: '1.2em',  }}
                >
                  {validSpeech}
                </span>
                <span className="text-gray-500 italic">
                  {!validSpeech && invalidSpeech}
                </span>
              </div>
            </div>
            
            {/* Control Buttons */}
            <div className="flex flex-col gap-2 mt-4">
               {/* <Indicator color="red" withBorder processing>  */}
              <button
                onClick={toggleRecognition}
                className={`p-2 rounded-full transition-colors ${
                  isRecognizing 
                    ? 'bg-red-500 hover:bg-red-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                } ${isListening ? 'animate-pulse' : ''}`}
                aria-label="Toggle microphone"
                type="button"
              >
                
               <Icon path={mdiMicrophone} size={1} />
                
              </button>
               {/* </Indicator>  */}
              <button
                onClick={emptyContent}
                className="p-2 rounded-full bg-red-100 hover:bg-red-200 text-red-600 transition-colors mt-4"
                aria-label="Clear content"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
              </button>
            </div>
          </div>
        </div>
           <Divider my="md" />
        {/* Dialog Actions */}
        <div className="flex justify-end gap-2 p-6 pt-0">
          <Button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            type="submit"
            aria-label="Submit"
            variant="subtle"
          >
            Valider
          </Button>
          <Button
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md transition-colors"
            type="button"
            aria-label="Cancel"
            variant="light" color="gray"
            style={{
          hover: '#E4F1FC',
          color: 'var(--mantine-color-red-9)',
          border: 'none',}}
          >
          Annuler
          </Button>
        </div>
     
      
           
          </div>
        </Modal.Body>
      </Modal.Content>
    </>
  
  );
};