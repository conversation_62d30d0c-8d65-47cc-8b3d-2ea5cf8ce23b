/**
 * Payment Background Data Fetcher
 * Handles background fetching and caching of payment data
 * Provides real-time updates without blocking the UI
 */

import { paymentService, PaymentSummary } from '@/services/paymentService';

interface CacheEntry {
  data: PaymentSummary;
  timestamp: number;
  expiresAt: number;
}

interface FetchOptions {
  forceRefresh?: boolean;
  cacheTimeout?: number; // in milliseconds
  priority?: 'low' | 'normal' | 'high';
  patientId?: string;
  dateRange?: { start: string; end: string };
}

interface BackgroundFetcherConfig {
  defaultCacheTimeout: number;
  maxConcurrentFetches: number;
  retryAttempts: number;
  retryDelay: number;
}

class PaymentBackgroundFetcher {
  private cache = new Map<string, CacheEntry>();
  private activeFetches = new Map<string, Promise<PaymentSummary>>();
  private fetchQueue: Array<{ 
    cacheKey: string; 
    options: FetchOptions; 
    resolve: Function; 
    reject: Function 
  }> = [];
  private isProcessingQueue = false;
  
  private config: BackgroundFetcherConfig = {
    defaultCacheTimeout: 2 * 60 * 1000, // 2 minutes (payment data changes frequently)
    maxConcurrentFetches: 4, // Higher for payment operations
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  };

  private listeners = new Map<string, Set<(data: PaymentSummary) => void>>();

  /**
   * Get payment data with background fetching
   */
  async getPaymentData(options: FetchOptions = {}): Promise<PaymentSummary> {
    const {
      forceRefresh = false,
      cacheTimeout = this.config.defaultCacheTimeout,
      priority = 'normal',
      patientId,
      dateRange
    } = options;

    const cacheKey = this.generateCacheKey(patientId, dateRange);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        // Start background refresh if data is getting old
        const age = Date.now() - cached.timestamp;
        if (age > cacheTimeout * 0.5) { // Refresh when 50% of cache time has passed (payment data changes frequently)
          this.queueBackgroundFetch(cacheKey, { ...options, priority: 'low' });
        }
        return cached.data;
      }
    }

    // Check if already fetching
    const activeFetch = this.activeFetches.get(cacheKey);
    if (activeFetch) {
      return activeFetch;
    }

    // Queue the fetch
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options: { ...options, cacheTimeout }, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Subscribe to real-time updates for payment data
   */
  subscribe(callback: (data: PaymentSummary) => void, patientId?: string, dateRange?: { start: string; end: string }): () => void {
    const cacheKey = this.generateCacheKey(patientId, dateRange);
    
    if (!this.listeners.has(cacheKey)) {
      this.listeners.set(cacheKey, new Set());
    }
    
    this.listeners.get(cacheKey)!.add(callback);

    // Start background fetching for this patient/date range
    this.queueBackgroundFetch(cacheKey, { priority: 'normal', patientId, dateRange });

    // Return unsubscribe function
    return () => {
      const keyListeners = this.listeners.get(cacheKey);
      if (keyListeners) {
        keyListeners.delete(callback);
        if (keyListeners.size === 0) {
          this.listeners.delete(cacheKey);
        }
      }
    };
  }

  /**
   * Preload payment data for multiple patients
   */
  async preloadPatients(patientIds: string[], options: FetchOptions = {}): Promise<void> {
    const promises = patientIds.map(patientId => 
      this.queueBackgroundFetch(
        this.generateCacheKey(patientId, options.dateRange), 
        { ...options, patientId, priority: 'low' }
      )
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Clear cache for a specific patient/date range or all data
   */
  clearCache(patientId?: string, dateRange?: { start: string; end: string }): void {
    if (patientId || dateRange) {
      const cacheKey = this.generateCacheKey(patientId, dateRange);
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    averageAge: number;
    patientEntries: number;
    globalEntries: number;
  } {
    const now = Date.now();
    let expiredCount = 0;
    let totalAge = 0;
    let patientEntries = 0;
    let globalEntries = 0;
    
    for (const [cacheKey, entry] of this.cache) {
      const age = now - entry.timestamp;
      if (entry.expiresAt < now) {
        expiredCount++;
      }
      totalAge += age;
      
      if (cacheKey.includes('patient_')) {
        patientEntries++;
      } else {
        globalEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would need to track hits/misses
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
      patientEntries,
      globalEntries,
    };
  }

  /**
   * Force refresh data for a patient and notify all subscribers
   */
  async refreshPatientData(patientId?: string, dateRange?: { start: string; end: string }): Promise<PaymentSummary> {
    const cacheKey = this.generateCacheKey(patientId, dateRange);
    this.clearCache(patientId, dateRange);
    const data = await this.getPaymentData({ 
      forceRefresh: true, 
      priority: 'high',
      patientId,
      dateRange 
    });
    this.notifyListeners(cacheKey, data);
    return data;
  }

  /**
   * Get payment summary for dashboard
   */
  async getPaymentDashboardData(dateRange?: { start: string; end: string }) {
    const data = await this.getPaymentData({ dateRange });
    
    const totalCollections = data.paymentCollections.reduce((sum, c) => sum + c.amount_collected, 0);
    const totalOutstanding = data.accountBalances.reduce((sum, b) => sum + b.remaining_balance, 0);
    const overdueAccounts = data.accountBalances.filter(b => b.account_status === 'overdue');
    
    const collectionRate = data.paymentAnalytics.collection_rate;
    const topPaymentMethod = data.paymentAnalytics.payment_methods_breakdown
      .sort((a, b) => b.total_amount - a.total_amount)[0]?.method || 'unknown';

    return {
      totalCollections,
      totalOutstanding,
      totalAccounts: data.accountBalances.length,
      overdueAccounts: overdueAccounts.length,
      collectionRate,
      topPaymentMethod,
      totalTransactions: data.paymentCollections.length,
      recentCollections: data.paymentCollections.slice(0, 5),
    };
  }

  // Private methods

  private generateCacheKey(patientId?: string, dateRange?: { start: string; end: string }): string {
    let key = 'global';
    
    if (patientId) {
      key = `patient_${patientId}`;
    }
    
    if (dateRange) {
      key += `_${dateRange.start}_${dateRange.end}`;
    }
    
    return key;
  }

  private getCachedData(cacheKey: string): CacheEntry | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    const now = Date.now();
    if (entry.expiresAt < now) {
      this.cache.delete(cacheKey);
      return null;
    }

    return entry;
  }

  private async queueBackgroundFetch(cacheKey: string, options: FetchOptions): Promise<PaymentSummary> {
    return new Promise((resolve, reject) => {
      this.fetchQueue.push({ cacheKey, options, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.fetchQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.fetchQueue.length > 0 && this.activeFetches.size < this.config.maxConcurrentFetches) {
      // Sort queue by priority
      this.fetchQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.options.priority || 'normal'] - priorityOrder[a.options.priority || 'normal'];
      });

      const item = this.fetchQueue.shift();
      if (!item) break;

      const { cacheKey, options, resolve, reject } = item;

      // Skip if already fetching this cache key
      if (this.activeFetches.has(cacheKey)) {
        const existingFetch = this.activeFetches.get(cacheKey)!;
        existingFetch.then(resolve).catch(reject);
        continue;
      }

      // Start the fetch
      const fetchPromise = this.performFetch(cacheKey, options);
      this.activeFetches.set(cacheKey, fetchPromise);

      fetchPromise
        .then((data) => {
          resolve(data);
          this.notifyListeners(cacheKey, data);
        })
        .catch(reject)
        .finally(() => {
          this.activeFetches.delete(cacheKey);
          // Continue processing queue
          setTimeout(() => this.processQueue(), 0);
        });
    }

    this.isProcessingQueue = false;
  }

  private async performFetch(cacheKey: string, options: FetchOptions): Promise<PaymentSummary> {
    const { cacheTimeout = this.config.defaultCacheTimeout, patientId, dateRange } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const data = await paymentService.getPaymentSummary(patientId, dateRange);
        
        // Cache the result
        const now = Date.now();
        this.cache.set(cacheKey, {
          data,
          timestamp: now,
          expiresAt: now + cacheTimeout,
        });

        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError || new Error('Failed to fetch payment data');
  }

  private notifyListeners(cacheKey: string, data: PaymentSummary): void {
    const listeners = this.listeners.get(cacheKey);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in payment listener:', error);
        }
      });
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [cacheKey, entry] of this.cache) {
      if (entry.expiresAt < now) {
        this.cache.delete(cacheKey);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startPeriodicCleanup(interval: number = 60000): () => void { // 1 minute
    const intervalId = setInterval(() => {
      this.cleanupCache();
    }, interval);

    return () => clearInterval(intervalId);
  }
}

// Create singleton instance
export const paymentBackgroundFetcher = new PaymentBackgroundFetcher();

// Auto-start periodic cleanup
paymentBackgroundFetcher.startPeriodicCleanup();

export default paymentBackgroundFetcher;
