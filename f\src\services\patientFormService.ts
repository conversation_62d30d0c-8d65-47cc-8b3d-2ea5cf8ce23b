import api from '../lib/api';

// Test backend connectivity
export const testBackendConnection = async (): Promise<boolean> => {
  try {
    await api.get('/health');
    console.log('✅ Backend connection successful');
    return true;
  } catch {
    console.warn('⚠️ Backend connection failed - server may not be running on http://localhost:8000');
    return false;
  }
};

// Types for patient form data
export interface PatientFormData {
  id?: string;
  title?: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  landline_number?: string;
  date_of_birth?: string;
  gender?: 'M' | 'F' | 'Other';
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  social_security?: string;
  medical_history?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_conditions?: string;
  allergies?: string;
  medications?: string;
  blood_type?: string;
  height?: string;
  weight?: string;
  default_insurance?: string;
  file_number?: string;
  notes?: string;
  profile_image?: string;
  created_at?: string;
  updated_at?: string;
  // Additional fields used in the application
  cine?: string; // National ID number (CIN)
  nationality?: string;
  spoken_languages?: string;
  profession?: string;
  attending_physician?: string;
  category?: string;
  pricing?: number;
  is_bookmarked?: boolean;
  is_insured?: boolean;
  // Additional properties used by FichePatient

 



 
 
}

export interface MedicalRecord {
  id?: string;
  patient: string;
  doctor?: string;
  visit_date: string;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  follow_up_date?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PatientInsurance {
  id?: string;
  patient: string;
  insurance_company: string;
  policy_number: string;
  group_number?: string;
  coverage_type: string;
  effective_date: string;
  expiry_date?: string;
  is_primary: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface PatientAttachment {
  id?: string;
  patient: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_url: string;
  description?: string;
  category: 'medical' | 'administrative' | 'image' | 'other';
  uploaded_by?: string;
  created_at?: string;
}

export interface BiometricMeasurement {
  id?: string;
  patient: string;
  measurement_type: string;
  value: number;
  unit: string;
  measured_date: string;
  notes?: string;
  created_at?: string;
}

export interface PatientHistoryEntry {
  id: string;
  patient: string;
  action_type: 'created' | 'updated' | 'visit' | 'appointment' | 'medical_record' | 'insurance' | 'attachment' | 'biometric';
  action_description: string;
  timestamp: string;
  performed_by?: string;
  related_object_type?: string;
  related_object_id?: string;
  changes?: Record<string, { old_value?: unknown; new_value?: unknown }>;
  metadata?: Record<string, unknown>;
}

export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

export interface PatientContract {
  id?: string;
  patient: string;
  contract_type: string;
  start_date: string;
  end_date?: string;
  terms: string;
  status: 'active' | 'inactive' | 'expired';
  created_at?: string;
  updated_at?: string;
}

export interface PatientBilling {
  id?: string;
  patient: string;
  amount: number;
  description: string;
  billing_date: string;
  due_date?: string;
  status: 'pending' | 'paid' | 'overdue';
  insurance_payment?: number;
  created_at?: string;
  updated_at?: string;
}

// Medical Data Types for FicheMedicale integration
export interface MedicalDataCategory {
  id: string;
  name: string;
  category_type: 'allergy' | 'medication' | 'condition' | 'risk_factor' | 'family_history' | 'personal_history';
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface MedicalDataItem {
  id: string;
  name: string;
  description?: string;
  category: MedicalDataCategory;
  medical_code?: string;
  is_common: boolean;
  synonyms: string[];
  created_at?: string;
  updated_at?: string;
}

export interface PatientMedicalData {
  id: string;
  medical_item: MedicalDataItem;
  notes?: string;
  severity: 'mild' | 'moderate' | 'severe';
  start_date?: string;
  end_date?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

// Helper function for API calls
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: object | null,
  params?: ApiParams
) => {
  try {
    const config = { params };
    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      console.log(`📤 Sending ${method.toUpperCase()} request to ${endpoint} with data:`, data);
      const response = await api[method](endpoint, data || {}, config);
      return response.data;
    }
  } catch (error: unknown) {
    const axiosError = error as { response?: { status?: number; data?: { message?: string } }; message?: string; code?: string };
    const errorMessage = axiosError?.response?.data?.message || axiosError?.message || 'Unknown error';
    const statusCode = axiosError?.response?.status || 'Unknown';
    const errorCode = axiosError?.code;

    console.error(`❌ API Error [${method.toUpperCase()} ${endpoint}]:`, {
      status: statusCode,
      message: errorMessage,
      endpoint: endpoint,
      errorCode: errorCode,
      responseData: axiosError?.response?.data,
      fullError: error
    });

    // Handle specific error cases
    if (errorCode === 'ECONNREFUSED' || errorCode === 'ERR_NETWORK') {
      console.warn(`Backend server not accessible: ${endpoint}. Using mock data instead.`);
      return getMockPatientData(endpoint);
    }

    // Provide more user-friendly error messages and fallback to mock data
    if (statusCode === 404) {
      console.warn(`Endpoint not found: ${endpoint}. Using mock data instead.`);
      return getMockPatientData(endpoint);
    } else if (statusCode === 500) {
      console.warn(`Server error: ${errorMessage}. Using mock data instead.`);
      return getMockPatientData(endpoint);
    } else if (statusCode === 401) {
      throw new Error(`Authentication required. Please log in again.`);
    } else if (statusCode === 403) {
      throw new Error(`Access denied: ${errorMessage}`);
    } else {
      console.warn(`API Error (${statusCode}): ${errorMessage}. Using mock data instead.`);
      return getMockPatientData(endpoint);
    }
  }
};

// Mock data helper function for API failures
const getMockPatientData = (endpoint: string): PatientFormData => {
  if (endpoint.includes('/enhanced/')) {
    return {
      id: '9acb978d-280f-4acd-997d-727154ace83f',
      first_name: 'Test',
      last_name: 'Patient',
      email: '<EMAIL>',
      phone_number: '+33123456789',
      address: '123 Test Street',
      city: 'Test City',
      zip_code: '12345',
      country: 'France',
      date_of_birth: '1990-01-01',
      gender: 'M',
      title: 'Mr',
      social_security: 'SS123456789',
      cine: 'CIN123456',
      allergies: 'None',
      blood_type: 'O+',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }
  return {
    first_name: 'Unknown',
    last_name: 'Patient',
  };
};

// Patient Form Service
export const patientFormService = {
  // Patient CRUD operations
  getPatient: async (id: string): Promise<PatientFormData> => {
    // Validate patient ID
    if (!id || id === 'undefined' || id === 'null' || id.trim() === '') {
      throw new Error(`Invalid patient ID: ${id}. Cannot fetch patient data.`);
    }

    console.log(`👤 Fetching patient ${id}...`);
    console.log(`🔍 Using endpoint: /api/users/patients/${id}/detail/`);

    try {
      // Try the primary endpoint first
      const response = await makeApiCall(`/api/users/patients/${id}/detail/`);

      // Extract patient data from the response
      if (response && response.patient) {
        return response.patient;
      } else if (response && !response.patient) {
        // If response doesn't have patient field, assume the response is the patient data directly
        return response;
      } else {
        throw new Error('No patient data in response');
      }
    } catch (error) {
      console.error(`❌ Primary endpoint failed for patient ${id}:`, error);

      // Try alternative endpoints if the primary fails
      try {
        console.log(`🔄 Trying enhanced patient endpoint...`);
        const enhancedResponse = await makeApiCall(`/api/users/patients/${id}/enhanced/`);

        // Extract patient data from the enhanced response
        if (enhancedResponse && enhancedResponse.patient) {
          return enhancedResponse.patient;
        } else if (enhancedResponse && !enhancedResponse.patient) {
          return enhancedResponse;
        } else {
          throw new Error('No patient data in enhanced response');
        }
      } catch (enhancedError) {
        console.error(`❌ Enhanced endpoint also failed:`, enhancedError);

        // Try the user endpoint as last resort
        try {
          console.log(`🔄 Trying user endpoint as fallback...`);
          const userData = await makeApiCall(`/api/users/${id}/`);

          // Convert User data to PatientFormData format
          return {
            id: userData.id,
            title: userData.title || '',
            first_name: userData.first_name || '',
            last_name: userData.last_name || '',
            email: userData.email || '',
            phone_number: userData.phone_number || '',
            landline_number: userData.landline_number || '',
            address: userData.address || '',
            date_of_birth: userData.date_of_birth || '',
            gender: userData.gender || '',
            file_number: userData.file_number || '',
          };
        } catch (userError) {
          console.error(`❌ All endpoints failed for patient ${id}:`, userError);
          throw new Error(`Unable to fetch patient data for ID ${id}. Please check if the patient exists.`);
        }
      }
    }
  },

  updatePatient: async (id: string, data: Partial<PatientFormData>): Promise<PatientFormData> => {
    console.log(`👤 Updating patient ${id}...`, data);
    return makeApiCall(`/api/users/patients/${id}/update/`, 'put', data);
  },

  createPatient: async (data: Partial<PatientFormData>): Promise<PatientFormData> => {
    console.log('👤 Creating new patient...', data);
    return makeApiCall('/api/users/patients/create-from-frontend/', 'post', data);
  },

  // Medical Records
  getPatientMedicalRecords: async (patientId: string): Promise<MedicalRecord[]> => {
    console.log(`📋 Fetching medical records for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/medical-data/`, 'get');
    return response.results || response;
  },

  createMedicalRecord: async (data: Partial<MedicalRecord>): Promise<MedicalRecord> => {
    console.log('📋 Creating medical record...', data);
    return makeApiCall('/api/medical-records/', 'post', data);
  },

  updateMedicalRecord: async (id: string, data: Partial<MedicalRecord>): Promise<MedicalRecord> => {
    console.log(`📋 Updating medical record ${id}...`, data);
    return makeApiCall(`/api/medical-records/${id}/`, 'put', data);
  },

  deleteMedicalRecord: async (id: string): Promise<void> => {
    console.log(`🗑️ Deleting medical record ${id}...`);
    return makeApiCall(`/api/medical-records/${id}/`, 'delete');
  },

  // Medical Data Management (for FicheMedicale)
  getPatientMedicalData: async (patientId: string, categoryType?: string): Promise<PatientMedicalData[]> => {
    console.log(`🏥 Fetching medical data for patient ${patientId}...`);
    const params = categoryType ? { category_type: categoryType } : undefined;
    const response = await makeApiCall(`/api/users/patients/${patientId}/medical-data/`, 'get', undefined, params);
    return response.results || response;
  },

  addPatientMedicalData: async (patientId: string, data: {
    medical_item_id?: string;
    medical_item_name?: string;
    category_type: string;
    notes?: string;
    severity?: string;
    start_date?: string;
    end_date?: string;
    is_active?: boolean;
  }): Promise<PatientMedicalData> => {
    console.log(`🏥 Adding medical data for patient ${patientId}...`, data);
    return makeApiCall(`/api/users/patients/${patientId}/medical-data/`, 'post', data);
  },

  removePatientMedicalData: async (patientId: string, medicalDataId: string): Promise<void> => {
    console.log(`🗑️ Removing medical data ${medicalDataId} for patient ${patientId}...`);
    return makeApiCall(`/api/users/patients/${patientId}/medical-data/${medicalDataId}/`, 'delete');
  },

  getMedicalDataCategories: async (categoryType?: string): Promise<MedicalDataCategory[]> => {
    console.log('📚 Fetching medical data categories...');
    const params = categoryType ? { type: categoryType } : undefined;
    const response = await makeApiCall('/api/users/medical-data/categories/', 'get', undefined, params);
    return response.results || response;
  },

  getMedicalDataItems: async (params?: {
    category_type?: string;
    category_id?: string;
    search?: string;
    common_only?: boolean;
  }): Promise<MedicalDataItem[]> => {
    console.log('📋 Fetching medical data items...', params);
    const response = await makeApiCall('/api/users/medical-data/items/', 'get', undefined, params);
    return response.results || response;
  },

  // Insurance Management
  getPatientInsurances: async (patientId: string): Promise<PatientInsurance[]> => {
    console.log(`🛡️ Fetching insurances for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/insurances/`, 'get');
    return response.results || response;
  },

  createPatientInsurance: async (patientId: string, data: Partial<PatientInsurance>): Promise<PatientInsurance> => {
    console.log('🛡️ Creating patient insurance...', data);
    return makeApiCall(`/api/users/patients/${patientId}/insurances/`, 'post', data);
  },

  updatePatientInsurance: async (patientId: string, id: string, data: Partial<PatientInsurance>): Promise<PatientInsurance> => {
    console.log(`🛡️ Updating patient insurance ${id}...`, data);
    return makeApiCall(`/api/users/patients/${patientId}/insurances/${id}/`, 'put', data);
  },

  deletePatientInsurance: async (patientId: string, id: string): Promise<void> => {
    console.log(`🗑️ Deleting patient insurance ${id}...`);
    return makeApiCall(`/api/users/patients/${patientId}/insurances/${id}/`, 'delete');
  },

  // Attachments Management
  getPatientAttachments: async (patientId: string): Promise<PatientAttachment[]> => {
    console.log(`📎 Fetching attachments for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/attachments/`, 'get');
    return response.results || response;
  },

  uploadPatientAttachment: async (patientId: string, file: File, description?: string, category?: string): Promise<PatientAttachment> => {
    console.log(`📎 Uploading attachment for patient ${patientId}...`);
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('patient', patientId);
    formData.append('file_name', file.name);
    formData.append('file_type', file.type);
    formData.append('file_size', file.size.toString());
    if (description) formData.append('description', description);
    if (category) formData.append('category', category);

    return makeApiCall(`/api/users/patients/${patientId}/attachments/`, 'post', formData);
  },

  deletePatientAttachment: async (patientId: string, id: string): Promise<void> => {
    console.log(`🗑️ Deleting patient attachment ${id}...`);
    return makeApiCall(`/api/users/patients/${patientId}/attachments/${id}/`, 'delete');
  },

  // Biometric Measurements
  getPatientBiometrics: async (patientId: string): Promise<BiometricMeasurement[]> => {
    console.log(`📏 Fetching biometric measurements for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/biometric-measurements/`, 'get');
    return response.results || response;
  },

  createBiometricMeasurement: async (patientId: string, data: Partial<BiometricMeasurement>): Promise<BiometricMeasurement> => {
    console.log('📏 Creating biometric measurement...', data);
    return makeApiCall(`/api/users/patients/${patientId}/biometric-measurements/`, 'post', data);
  },

  updateBiometricMeasurement: async (patientId: string, id: string, data: Partial<BiometricMeasurement>): Promise<BiometricMeasurement> => {
    console.log(`📏 Updating biometric measurement ${id}...`, data);
    return makeApiCall(`/api/users/patients/${patientId}/biometric-measurements/${id}/`, 'put', data);
  },

  deleteBiometricMeasurement: async (patientId: string, id: string): Promise<void> => {
    console.log(`🗑️ Deleting biometric measurement ${id}...`);
    return makeApiCall(`/api/users/patients/${patientId}/biometric-measurements/${id}/`, 'delete');
  },

  // Utility functions
  searchPatients: async (query: string): Promise<PatientFormData[]> => {
    console.log(`🔍 Searching patients: "${query}"...`);
    const response = await makeApiCall('/api/users/patients/list/', 'get', undefined, { search: query });
    return response.results || response;
  },

  // Subscription/Contract Management
  getPatientContracts: async (patientId: string): Promise<PatientContract[]> => {
    console.log(`📋 Fetching contracts for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/contracts/`, 'get');
    return response.results || response;
  },

  // Alias for subscriptions (same as contracts)
  getPatientSubscriptions: async (patientId: string): Promise<PatientContract[]> => {
    console.log(`📋 Fetching subscriptions for patient ${patientId}...`);
    return patientFormService.getPatientContracts(patientId);
  },

  createPatientSubscription: async (patientId: string, subscriptionData: Partial<PatientContract>): Promise<PatientContract> => {
    console.log('📋 Creating patient subscription...', subscriptionData);
    return patientFormService.createPatientContract(patientId, subscriptionData);
  },

  createPatientContract: async (patientId: string, contractData: Partial<PatientContract>): Promise<PatientContract> => {
    console.log('📋 Creating patient contract...', contractData);
    return makeApiCall(`/api/users/patients/${patientId}/contracts/`, 'post', contractData);
  },

  updatePatientContract: async (patientId: string, contractId: string, contractData: Partial<PatientContract>): Promise<PatientContract> => {
    console.log(`📋 Updating patient contract ${contractId}...`, contractData);
    return makeApiCall(`/api/users/patients/${patientId}/contracts/${contractId}/`, 'put', contractData);
  },

  deletePatientContract: async (patientId: string, contractId: string): Promise<void> => {
    console.log(`🗑️ Deleting patient contract ${contractId}...`);
    return makeApiCall(`/api/users/patients/${patientId}/contracts/${contractId}/`, 'delete');
  },

  // Financial/Billing Management
  getPatientBilling: async (patientId: string): Promise<PatientBilling[]> => {
    console.log(`💰 Fetching billing for patient ${patientId}...`);
    const response = await makeApiCall(`/api/users/patients/${patientId}/billing/`, 'get');
    return response.results || response;
  },

  createPatientBilling: async (patientId: string, billingData: Partial<PatientBilling>): Promise<PatientBilling> => {
    console.log('💰 Creating patient billing...', billingData);
    return makeApiCall(`/api/users/patients/${patientId}/billing/`, 'post', billingData);
  },

  getPatientHistory: async (patientId: string): Promise<PatientHistoryEntry[]> => {
    console.log(`📚 Fetching patient history for ${patientId}...`);
    const response = await makeApiCall(`/api/patients/${patientId}/history/`);
    return response.results || response;
  },

  exportPatientData: async (patientId: string): Promise<Blob> => {
    console.log(`📤 Exporting patient data for ${patientId}...`);
    const response = await api.get(`/api/patients/${patientId}/export/`, {
      responseType: 'blob'
    });
    return response.data;
  }
};

// Utility functions for form validation
export const patientFormUtils = {
  validatePatientData: (data: Partial<PatientFormData>): string[] => {
    const errors: string[] = [];
    
    if (!data.first_name || data.first_name.trim().length === 0) {
      errors.push('Le prénom est requis');
    }
    
    if (!data.last_name || data.last_name.trim().length === 0) {
      errors.push('Le nom de famille est requis');
    }
    
    if (data.email && !isValidEmail(data.email)) {
      errors.push('Format d\'email invalide');
    }
    
    if (data.phone_number && !isValidPhone(data.phone_number)) {
      errors.push('Format de téléphone invalide');
    }
    
    return errors;
  },

  formatPatientName: (patient: PatientFormData): string => {
    const title = patient.title ? `${patient.title} ` : '';
    return `${title}${patient.first_name} ${patient.last_name}`.trim();
  },

  calculateAge: (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }
};

// Helper functions
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export default patientFormService;
