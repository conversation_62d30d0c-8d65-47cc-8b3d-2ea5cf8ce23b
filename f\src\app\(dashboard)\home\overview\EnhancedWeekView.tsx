'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { CalendarEvent } from '../CalendarPatient';

// Configure moment localizer
const localizer = momentLocalizer(moment);

// Configure French locale
moment.locale('fr', {
  months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),
  monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),
  weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),
  weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),
  weekdaysMin: 'Di_Lu_Ma_Me_Je_Ve_Sa'.split('_'),
});

interface EnhancedWeekViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick: (date: Date, hour: number, minute?: number, roomId?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
}

// Custom event component
const CustomEvent = ({ event }: { event: CalendarEvent }) => {
  return (
    <div className="rbc-event-content">
      <div className="font-semibold text-xs">{event.title}</div>
      <div className="text-xs opacity-75">
        {event.roomId && `${event.roomId === 'room-a' ? 'Salle A' : 'Salle B'}`}
      </div>
      <div className="text-xs opacity-75">
        {event.duration}min
      </div>
    </div>
  );
};

const EnhancedWeekView: React.FC<EnhancedWeekViewProps> = ({
  currentDate,
  events,
  onTimeSlotClick,
  onEventClick,
  onDateChange,
  onNavigate
}) => {
  const [selectedRoom, setSelectedRoom] = useState<string>('all');

  // Room configuration
  const rooms = useMemo(() => [
    { id: 'room-a', name: 'Salle A', color: '#3b82f6' },
    { id: 'room-b', name: 'Salle B', color: '#10b981' },
  ], []);

  // Filter events by selected room and current week
  const filteredEvents = useMemo(() => {
    const weekStart = moment(currentDate).startOf('week');
    const weekEnd = moment(currentDate).endOf('week');

    let filtered = events.filter(event => {
      const eventMoment = moment(event.start);
      return eventMoment.isBetween(weekStart, weekEnd, 'day', '[]');
    });

    // Filter by room if not 'all'
    if (selectedRoom !== 'all') {
      filtered = filtered.filter(event => event.roomId === selectedRoom);
    }

    return filtered;
  }, [events, currentDate, selectedRoom]);

  // Convert events to react-big-calendar format
  const calendarEvents = useMemo(() => {
    return filteredEvents.map(event => ({
      ...event,
      start: event.start,
      end: event.end || new Date(event.start.getTime() + (event.duration || 30) * 60000),
      resource: event.roomId,
    }));
  }, [filteredEvents]);

  // Event style getter
  const eventStyleGetter = useCallback((event: CalendarEvent) => {
    const room = rooms.find(r => r.id === event.roomId);
    return {
      style: {
        backgroundColor: event.color || room?.color || '#3b82f6',
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '11px',
        padding: '2px 4px',
      }
    };
  }, [rooms]);

  // Handle slot selection
  const handleSelectSlot = useCallback((slotInfo: { start: Date; end: Date; slots: Date[] }) => {
    const { start } = slotInfo;
    const hour = start.getHours();
    const minute = start.getMinutes();
    const roomId = selectedRoom !== 'all' ? selectedRoom : 'room-a';
    
    onTimeSlotClick(start, hour, minute, roomId);
  }, [onTimeSlotClick, selectedRoom]);

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    onEventClick(event);
  }, [onEventClick]);

  // Handle navigation
  const handleNavigate = useCallback((newDate: Date) => {
    if (onDateChange) {
      onDateChange(newDate);
    }
  }, [onDateChange]);

  // Custom formats
  const formats = {
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
    },
    dayHeaderFormat: 'dddd DD/MM',
    dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) => {
      return `${moment(start).format('DD MMM')} - ${moment(end).format('DD MMM YYYY')}`;
    },
  };

  // Get week stats
  const weekStats = useMemo(() => {
    const weekStart = moment(currentDate).startOf('week');
    const weekEnd = moment(currentDate).endOf('week');
    
    const weekEvents = events.filter(event => {
      const eventMoment = moment(event.start);
      return eventMoment.isBetween(weekStart, weekEnd, 'day', '[]');
    });

    const dailyStats = [];
    for (let i = 0; i < 7; i++) {
      const day = moment(weekStart).add(i, 'days');
      const dayEvents = weekEvents.filter(event => 
        moment(event.start).isSame(day, 'day')
      );
      dailyStats.push({
        date: day.format('ddd DD'),
        count: dayEvents.length
      });
    }

    return {
      total: weekEvents.length,
      daily: dailyStats
    };
  }, [events, currentDate]);

  return (
    <div className="enhanced-week-view h-full flex flex-col">
      {/* Header Controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-800">
            Semaine du {moment(currentDate).startOf('week').format('DD MMM')} au {moment(currentDate).endOf('week').format('DD MMM YYYY')}
          </h3>
          
          {/* Room Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Salle:</span>
            <div className="flex gap-1">
              <button
                onClick={() => setSelectedRoom('all')}
                className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                  selectedRoom === 'all'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Toutes
              </button>
              {rooms.map(room => (
                <button
                  key={room.id}
                  onClick={() => setSelectedRoom(room.id)}
                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                    selectedRoom === room.id
                      ? 'text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  style={{
                    backgroundColor: selectedRoom === room.id ? room.color : undefined
                  }}
                >
                  {room.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Week Stats */}
        <div className="flex items-center gap-4 text-sm">
          <div className="text-gray-600">
            <span className="font-medium">{filteredEvents.length}</span> rendez-vous cette semaine
          </div>
          <div className="flex gap-2">
            {weekStats.daily.map((day, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-gray-500">{day.date}</div>
                <div className="text-sm font-medium text-blue-600">{day.count}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Calendar */}
      <div className="flex-1 p-4">
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          defaultView="week"
          view="week"
          views={['week']}
          date={currentDate}
          onNavigate={handleNavigate}
          step={15}
          timeslots={4}
          min={new Date(2025, 0, 1, 7, 0)} // 7:00 AM
          max={new Date(2025, 0, 1, 22, 0)} // 10:00 PM
          formats={formats}
          eventPropGetter={eventStyleGetter}
          onSelectSlot={handleSelectSlot}
          onSelectEvent={handleSelectEvent}
          selectable
          components={{
            event: CustomEvent,
          }}
          messages={{
            allDay: 'Toute la journée',
            previous: 'Précédent',
            next: 'Suivant',
            today: "Aujourd'hui",
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour',
            agenda: 'Agenda',
            date: 'Date',
            time: 'Heure',
            event: 'Événement',
            noEventsInRange: 'Aucun rendez-vous cette semaine.',
          }}
          className="rbc-calendar-enhanced"
        />
      </div>

      {/* Debug Info */}
      <div className="p-2 bg-blue-50 border-t text-xs text-blue-700">
        <strong>🔍 Enhanced Week View:</strong><br/>
        Semaine: {moment(currentDate).startOf('week').format('DD/MM')} - {moment(currentDate).endOf('week').format('DD/MM/YYYY')}<br/>
        Total events: {events.length} | Filtered: {filteredEvents.length} | Room: {selectedRoom}
      </div>
    </div>
  );
};

export default EnhancedWeekView;
