import { NextRequest, NextResponse } from 'next/server';

// Mock database of existing emails
const existingEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

export async function GET(request: NextRequest) {
  try {
    // Get the email from the query parameters
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      );
    }

    console.log(`Checking if email exists: ${email}`);

    // In a real application, you would check against your database
    // For now, we'll check against our mock list
    const exists = existingEmails.includes(email.toLowerCase());

    // Return the result
    return NextResponse.json({ exists });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
