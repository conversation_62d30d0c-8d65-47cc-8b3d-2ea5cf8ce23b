/**
 * Parameters Quick Access Modal
 * Provides quick access to system parameters and configuration from the calendar
 */

import React, { useState } from 'react';
import {
  Modal,
  Tabs,
  Group,
  Text,
  Button,
  Stack,
  Badge,
  Card,
  ScrollArea,
  Select,
  SimpleGrid,
  ActionIcon,
  Tooltip,
  TextInput,
  Switch,
  NumberInput,
  Divider,
} from '@mantine/core';
import {
  IconSettings,
  IconUsers,
  IconShield,
  IconDatabase,
  IconServer,
  IconChartPie,
  IconExternalLink,
  IconRefresh,
  IconPlus,
  IconEye,
  IconEdit,
  IconSearch,
  IconAlertTriangle,
  IconCheck,
} from '@tabler/icons-react';
import { useParameters } from '@/hooks/useParameters';
import ParametersWidgets from './ParametersWidgets';

interface ParametersQuickAccessProps {
  opened: boolean;
  onClose: () => void;
  defaultTab?: 'dashboard' | 'parameters' | 'users' | 'specialties' | 'system' | 'backups';
  onNavigateToFullPage?: () => void;
}

const ParametersQuickAccess: React.FC<ParametersQuickAccessProps> = ({
  opened,
  onClose,
  defaultTab = 'dashboard',
  onNavigateToFullPage,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [editingParameter, setEditingParameter] = useState<string | null>(null);

  const {
    systemParameters,
    systemUsers,
    specialties,
    contacts,
    technicians,
    dataBackups,
    analytics,
    loading,
    refreshAll,
    updateSystemParameter,
    getActiveUsers,
    getActiveSpecialties,
    getRecentBackups,
    getSystemStats,
    getSystemHealth,
    getParametersByCategory,
  } = useParameters({ 
    autoFetch: opened,
    dataTypes: ['parameters', 'users', 'specialties', 'contacts', 'technicians', 'backups', 'analytics']
  });

  const systemStats = getSystemStats();
  const systemHealth = getSystemHealth();
  const activeUsers = getActiveUsers();
  const activeSpecialties = getActiveSpecialties();
  const recentBackups = getRecentBackups(5);

  const handleRefresh = () => {
    refreshAll();
  };

  const handleParameterUpdate = async (parameterId: string, value: string | number | boolean) => {
    try {
      await updateSystemParameter(parameterId, value);
      setEditingParameter(null);
    } catch (error) {
      console.error('Failed to update parameter:', error);
    }
  };

  const filteredParameters = systemParameters.filter(param => {
    const matchesSearch = searchTerm === '' || 
      param.parameter_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      param.parameter_key.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || param.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const parameterCategories = Array.from(new Set(systemParameters.map(p => p.category)));

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <Stack gap="md">
            <ParametersWidgets 
              compact={false}
              showSystemHealth={true}
            />
          </Stack>
        );

      case 'parameters':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconSettings size={20} />
                  <Text fw={600}>Paramètres Système</Text>
                  <Badge color="blue">{filteredParameters.length}</Badge>
                </Group>
                <Group gap="xs">
                  <TextInput
                    placeholder="Rechercher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    leftSection={<IconSearch size={14} />}
                    size="xs"
                    style={{ width: 200 }}
                  />
                  <Select
                    value={selectedCategory}
                    onChange={(value) => setSelectedCategory(value || 'all')}
                    data={[
                      { value: 'all', label: 'Toutes catégories' },
                      ...parameterCategories.map(cat => ({ value: cat, label: cat }))
                    ]}
                    size="xs"
                  />
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {filteredParameters.map((parameter) => (
                    <Card key={parameter.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div style={{ flex: 1 }}>
                          <Group gap="xs" mb="xs">
                            <Text size="sm" fw={500}>{parameter.parameter_name}</Text>
                            <Badge size="xs" color="gray">{parameter.category}</Badge>
                            {parameter.is_required && (
                              <Badge size="xs" color="red">Requis</Badge>
                            )}
                          </Group>
                          <Text size="xs" c="dimmed" mb="xs">
                            Clé: {parameter.parameter_key}
                          </Text>
                          <Text size="xs" c="dimmed" mb="xs">
                            {parameter.description}
                          </Text>
                          
                          {editingParameter === parameter.id ? (
                            <Group gap="xs" mt="xs">
                              {parameter.parameter_type === 'boolean' ? (
                                <Switch
                                  checked={Boolean(parameter.parameter_value)}
                                  onChange={(event) => 
                                    handleParameterUpdate(parameter.id, event.currentTarget.checked)
                                  }
                                />
                              ) : parameter.parameter_type === 'number' ? (
                                <NumberInput
                                  value={Number(parameter.parameter_value)}
                                  onChange={(value) => 
                                    handleParameterUpdate(parameter.id, value || 0)
                                  }
                                  size="xs"
                                  style={{ width: 150 }}
                                />
                              ) : (
                                <TextInput
                                  value={String(parameter.parameter_value)}
                                  onChange={(event) => 
                                    handleParameterUpdate(parameter.id, event.currentTarget.value)
                                  }
                                  size="xs"
                                  style={{ width: 200 }}
                                />
                              )}
                              <ActionIcon 
                                size="sm" 
                                color="green"
                                onClick={() => setEditingParameter(null)}
                              >
                                <IconCheck size={14} />
                              </ActionIcon>
                            </Group>
                          ) : (
                            <Group gap="xs" mt="xs">
                              <Text size="sm" fw={600}>
                                {parameter.parameter_type === 'boolean' ? 
                                  (parameter.parameter_value ? 'Activé' : 'Désactivé') :
                                  String(parameter.parameter_value)
                                }
                              </Text>
                              {parameter.is_editable && (
                                <ActionIcon 
                                  size="sm" 
                                  variant="subtle"
                                  onClick={() => setEditingParameter(parameter.id)}
                                >
                                  <IconEdit size={14} />
                                </ActionIcon>
                              )}
                            </Group>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={parameter.parameter_type === 'boolean' ? 'blue' : 
                                   parameter.parameter_type === 'number' ? 'green' : 'purple'}
                          >
                            {parameter.parameter_type}
                          </Badge>
                          <Text size="xs" c="dimmed">
                            Modifié: {new Date(parameter.updated_at).toLocaleDateString()}
                          </Text>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {filteredParameters.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun paramètre trouvé
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'users':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconUsers size={20} />
                  <Text fw={600}>Utilisateurs Système</Text>
                  <Badge color="blue">{systemUsers.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {systemUsers.map((user) => (
                    <Card key={user.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{user.first_name} {user.last_name}</Text>
                          <Text size="xs" c="dimmed">
                            @{user.username} | {user.email}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Profil: {user.profile_name}
                          </Text>
                          {user.specialty_name && (
                            <Text size="xs" c="dimmed">
                              Spécialité: {user.specialty_name}
                            </Text>
                          )}
                          {user.last_login && (
                            <Text size="xs" c="dimmed">
                              Dernière connexion: {new Date(user.last_login).toLocaleDateString()}
                            </Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={user.is_active ? 'green' : 'red'}
                          >
                            {user.is_active ? 'Actif' : 'Inactif'}
                          </Badge>
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {systemUsers.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucun utilisateur trouvé
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'specialties':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconShield size={20} />
                  <Text fw={600}>Spécialités</Text>
                  <Badge color="purple">{specialties.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <SimpleGrid cols={2} spacing="md">
                  {specialties.map((specialty) => (
                    <Card key={specialty.id} padding="sm" radius="sm" withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Group gap="xs">
                            <div 
                              style={{ 
                                width: 16, 
                                height: 16, 
                                borderRadius: '50%', 
                                backgroundColor: specialty.color 
                              }} 
                            />
                            <Text size="sm" fw={500}>{specialty.name}</Text>
                          </Group>
                          <Badge size="xs" color={specialty.is_active ? 'green' : 'red'}>
                            {specialty.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </Group>
                        <Text size="xs" c="dimmed">Code: {specialty.code}</Text>
                        <Text size="xs" c="dimmed">{specialty.description}</Text>
                        <Divider size="xs" />
                        <Group justify="space-between">
                          <Text size="xs" c="dimmed">Modules activés</Text>
                          <Badge size="xs" color="blue">
                            {specialty.configuration.modules_enabled.length}
                          </Badge>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs" c="dimmed">Formulaires</Text>
                          <Badge size="xs" color="green">
                            {specialty.configuration.forms_enabled.length}
                          </Badge>
                        </Group>
                        <Group justify="space-between">
                          <Text size="xs" c="dimmed">Procédures</Text>
                          <Badge size="xs" color="orange">
                            {specialty.configuration.procedures_enabled.length}
                          </Badge>
                        </Group>
                      </Stack>
                    </Card>
                  ))}
                  {specialties.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl" style={{ gridColumn: '1 / -1' }}>
                      Aucune spécialité trouvée
                    </Text>
                  )}
                </SimpleGrid>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'system':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconServer size={20} />
                  <Text fw={600}>État du Système</Text>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="md">
                  {/* System Health Overview */}
                  <Card padding="sm" radius="sm" withBorder>
                    <Group justify="space-between" mb="md">
                      <Text size="sm" fw={600}>Santé du Système</Text>
                      <Badge 
                        size="lg" 
                        color={
                          systemHealth.status === 'healthy' ? 'green' : 
                          systemHealth.status === 'warning' ? 'orange' : 'red'
                        }
                      >
                        {systemHealth.status === 'healthy' ? 'Sain' : 
                         systemHealth.status === 'warning' ? 'Attention' : 'Critique'}
                      </Badge>
                    </Group>
                    <SimpleGrid cols={2} spacing="md">
                      <Group justify="space-between">
                        <Text size="sm">CPU</Text>
                        <Text size="sm" fw={500} c={systemHealth.cpuUsage > 80 ? 'red' : 'blue'}>
                          {systemHealth.cpuUsage.toFixed(1)}%
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Mémoire</Text>
                        <Text size="sm" fw={500} c={systemHealth.memoryUsage > 80 ? 'red' : 'green'}>
                          {systemHealth.memoryUsage.toFixed(1)}%
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Disque</Text>
                        <Text size="sm" fw={500} c={systemHealth.diskUsage > 85 ? 'red' : 'purple'}>
                          {systemHealth.diskUsage.toFixed(1)}%
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Disponibilité</Text>
                        <Text size="sm" fw={500} c={systemHealth.uptime < 99 ? 'orange' : 'teal'}>
                          {systemHealth.uptime.toFixed(2)}%
                        </Text>
                      </Group>
                    </SimpleGrid>
                  </Card>

                  {/* System Statistics */}
                  <Card padding="sm" radius="sm" withBorder>
                    <Text size="sm" fw={600} mb="md">Statistiques</Text>
                    <SimpleGrid cols={2} spacing="md">
                      <Group justify="space-between">
                        <Text size="sm">Utilisateurs totaux</Text>
                        <Text size="sm" fw={500}>{systemStats.totalUsers}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Utilisateurs actifs</Text>
                        <Text size="sm" fw={500}>{systemStats.activeUsers}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Spécialités</Text>
                        <Text size="sm" fw={500}>{systemStats.totalSpecialties}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Paramètres</Text>
                        <Text size="sm" fw={500}>{systemStats.totalParameters}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Contacts</Text>
                        <Text size="sm" fw={500}>{systemStats.totalContacts}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Techniciens</Text>
                        <Text size="sm" fw={500}>{systemStats.totalTechnicians}</Text>
                      </Group>
                    </SimpleGrid>
                  </Card>

                  {/* Performance Metrics */}
                  {analytics && (
                    <Card padding="sm" radius="sm" withBorder>
                      <Text size="sm" fw={600} mb="md">Performance</Text>
                      <SimpleGrid cols={2} spacing="md">
                        <Group justify="space-between">
                          <Text size="sm">Temps de réponse</Text>
                          <Text size="sm" fw={500}>{systemHealth.responseTime}ms</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">Taux d'erreur</Text>
                          <Text size="sm" fw={500} c={systemHealth.errorRate > 1 ? 'red' : 'green'}>
                            {systemHealth.errorRate.toFixed(2)}%
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">Modifications config</Text>
                          <Text size="sm" fw={500}>{analytics.configuration_changes.length}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">Activité utilisateurs</Text>
                          <Text size="sm" fw={500}>{analytics.user_activity.length}</Text>
                        </Group>
                      </SimpleGrid>
                    </Card>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      case 'backups':
        return (
          <Card padding="md" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="xs">
                  <IconDatabase size={20} />
                  <Text fw={600}>Sauvegardes</Text>
                  <Badge color="yellow">{dataBackups.length}</Badge>
                </Group>
              </Group>
              
              <ScrollArea h={400}>
                <Stack gap="xs">
                  {dataBackups.map((backup) => (
                    <Card key={backup.id} padding="sm" radius="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500}>{backup.backup_name}</Text>
                          <Text size="xs" c="dimmed">
                            Type: {backup.backup_type} | Date: {new Date(backup.backup_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Taille: {(backup.file_size / 1024 / 1024).toFixed(0)} MB
                          </Text>
                          <Text size="xs" c="dimmed">
                            Chemin: {backup.file_path}
                          </Text>
                          <Text size="xs" c="dimmed">
                            Créé par: {backup.created_by}
                          </Text>
                          {backup.notes && (
                            <Text size="xs" c="dimmed">Notes: {backup.notes}</Text>
                          )}
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="sm" 
                            color={
                              backup.status === 'completed' ? 'green' : 
                              backup.status === 'in_progress' ? 'blue' : 'red'
                            }
                          >
                            {backup.status}
                          </Badge>
                          <Group gap="xs">
                            <Tooltip label="Voir">
                              <ActionIcon variant="subtle" size="sm">
                                <IconEye size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Group>
                      </Group>
                    </Card>
                  ))}
                  {dataBackups.length === 0 && (
                    <Text size="sm" c="dimmed" ta="center" p="xl">
                      Aucune sauvegarde trouvée
                    </Text>
                  )}
                </Stack>
              </ScrollArea>
            </Stack>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="xs">
          <IconSettings size={20} />
          <Text fw={600}>Configuration Système</Text>
        </Group>
      }
      size="xl"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* Header Controls */}
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Accès rapide aux paramètres et configuration système
          </Text>
          <Group gap="xs">
            <Button
              variant="light"
              size="xs"
              leftSection={<IconRefresh size={14} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            {onNavigateToFullPage && (
              <Button
                variant="light"
                size="xs"
                leftSection={<IconExternalLink size={14} />}
                onClick={onNavigateToFullPage}
              >
                Page Complète
              </Button>
            )}
          </Group>
        </Group>

        {/* Quick Stats */}
        <SimpleGrid cols={4} spacing="xs">
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconUsers size={16} color="blue" />
              <div>
                <Text size="xs" c="dimmed">Utilisateurs</Text>
                <Text size="sm" fw={600}>{systemStats.totalUsers}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconSettings size={16} color="green" />
              <div>
                <Text size="xs" c="dimmed">Paramètres</Text>
                <Text size="sm" fw={600}>{systemStats.totalParameters}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconShield size={16} color="purple" />
              <div>
                <Text size="xs" c="dimmed">Spécialités</Text>
                <Text size="sm" fw={600}>{systemStats.totalSpecialties}</Text>
              </div>
            </Group>
          </Card>
          <Card padding="xs" radius="sm" withBorder>
            <Group gap="xs">
              <IconServer size={16} color={systemHealth.status === 'healthy' ? 'green' : 'orange'} />
              <div>
                <Text size="xs" c="dimmed">Système</Text>
                <Text size="sm" fw={600}>{systemHealth.uptime.toFixed(1)}%</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
          <Tabs.List>
            <Tabs.Tab value="dashboard" leftSection={<IconChartPie size={16} />}>
              Tableau de Bord
            </Tabs.Tab>
            <Tabs.Tab 
              value="parameters" 
              leftSection={<IconSettings size={16} />}
              rightSection={<Badge size="xs" color="blue">{systemParameters.length}</Badge>}
            >
              Paramètres
            </Tabs.Tab>
            <Tabs.Tab 
              value="users" 
              leftSection={<IconUsers size={16} />}
              rightSection={<Badge size="xs" color="green">{systemUsers.length}</Badge>}
            >
              Utilisateurs
            </Tabs.Tab>
            <Tabs.Tab 
              value="specialties" 
              leftSection={<IconShield size={16} />}
              rightSection={<Badge size="xs" color="purple">{specialties.length}</Badge>}
            >
              Spécialités
            </Tabs.Tab>
            <Tabs.Tab value="system" leftSection={<IconServer size={16} />}>
              Système
            </Tabs.Tab>
            <Tabs.Tab 
              value="backups" 
              leftSection={<IconDatabase size={16} />}
              rightSection={<Badge size="xs" color="yellow">{dataBackups.length}</Badge>}
            >
              Sauvegardes
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTab} pt="md">
            {renderTabContent()}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Modal>
  );
};

export default ParametersQuickAccess;
