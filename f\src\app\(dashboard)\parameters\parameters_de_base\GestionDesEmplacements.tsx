import React, { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Tabs,
  Paper,
  Text,
  TextInput,
  Button,
  Modal,
  Table,
  ActionIcon,
  Group,
  Tooltip,
  ScrollArea,
  Select,
  Loader,
  Alert,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconMapPin,
  IconAlertCircle,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { parametersService, Country, Region,  } from '../../../../services/parametersService';

// Legacy types for compatibility (will be mapped to backend types)
interface Pays {
  id: number;
  abre: string;
  nom: string;
}

interface Ville {
  id: number;
  abre: string;
  nom: string;
  pays: string;
}

interface Prefecture {
  id: number;
  abre: string;
  nom: string;
  ville: string;
}

const GestionDesEmplacements = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string>('pays');

  // États pour les modales
  const [paysModalOpened, setPaysModalOpened] = useState(false);
  const [villeModalOpened, setVilleModalOpened] = useState(false);
  const [prefectureModalOpened, setPrefectureModalOpened] = useState(false);

  // États pour l'édition
  const [editingPays, setEditingPays] = useState<Pays | null>(null);
  const [editingVille, setEditingVille] = useState<Ville | null>(null);
  const [editingPrefecture, setEditingPrefecture] = useState<Prefecture | null>(null);

  // États pour le chargement et les erreurs
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // États pour les données backend
  const [countries, setCountries] = useState<Country[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  //const [cities, setCities] = useState<City[]>([]);

  // États pour les formulaires
  const [paysForm, setPaysForm] = useState({
    abre: '',
    nom: '',
  });

  const [villeForm, setVilleForm] = useState({
    abre: '',
    nom: '',
    pays: '',
  });

  const [prefectureForm, setPrefectureForm] = useState({
    abre: '',
    nom: '',
    ville: '',
  });

  // État pour la recherche
  const [searchTerm, setSearchTerm] = useState('');

  // Données converties pour compatibilité avec l'interface existante
  const [paysList, setPaysList] = useState<Pays[]>([]);
  const [villesList, setVillesList] = useState<Ville[]>([]);
  const [prefecturesList, setPrefecturesList] = useState<Prefecture[]>([]);

  // Fonctions de chargement des données
  const loadCountries = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await parametersService.getCountries();
      setCountries(data);

      // Convertir pour compatibilité avec l'interface existante
      const convertedPays = data.map(country => ({
        id: parseInt(country.id),
        abre: country.code,
        nom: country.name
      }));
      setPaysList(convertedPays);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError('Erreur lors du chargement des pays: ' + errorMessage);
      console.error('Error loading countries:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadRegions = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await parametersService.getRegions();
      setRegions(data);

      // Convertir pour compatibilité avec l'interface existante
      const convertedVilles = data.map(region => {
        const country = countries.find(c => c.id === region.country);
        return {
          id: parseInt(region.id),
          abre: region.code || region.name,
          nom: region.name,
          pays: country?.name || 'Unknown'
        };
      });
      setVillesList(convertedVilles);
    } catch (err: unknown) {
       const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError('Erreur lors du chargement des régions: ' + errorMessage);
      console.error('Error loading regions:', err);
    } finally {
      setLoading(false);
    }
  };

  // Effet pour charger les données au montage du composant
  useEffect(() => {
    loadCountries();
  }, []);

  // Effet pour charger les régions quand les pays sont chargés
  useEffect(() => {
    if (countries.length > 0) {
      loadRegions();
    }
  }, [countries]);

  // Fonctions pour filtrer les données selon la recherche
  const getFilteredData = () => {
    const term = searchTerm.toLowerCase();
    switch (activeTab) {
      case 'pays':
        return paysList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term)
        );
      case 'ville':
        return villesList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term) ||
          item.pays.toLowerCase().includes(term)
        );
      case 'prefecture':
        return prefecturesList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term) ||
          item.ville.toLowerCase().includes(term)
        );
      default:
        return [];
    }
  };

  // Fonctions pour gérer les modales - Pays
  const openPaysModal = (pays?: Pays) => {
    if (pays) {
      setEditingPays(pays);
      setPaysForm({
        abre: pays.abre,
        nom: pays.nom,
      });
    } else {
      setEditingPays(null);
      setPaysForm({
        abre: '',
        nom: '',
      });
    }
    setPaysModalOpened(true);
  };

  const closePaysModal = () => {
    setPaysModalOpened(false);
    setEditingPays(null);
  };

  const handleSavePays = async () => {
    if (!paysForm.nom.trim()) return;

    try {
      setLoading(true);

      const countryData = {
        name: paysForm.nom,
        code: paysForm.abre || paysForm.nom.substring(0, 2).toUpperCase(),
        is_active: true
      };

      if (editingPays) {
        // Trouver le pays correspondant dans les données backend
        const country = countries.find(c => parseInt(c.id) === editingPays.id);
        if (country) {
          await parametersService.updateCountry(country.id, countryData);
          notifications.show({
            title: 'Succès',
            message: 'Pays mis à jour avec succès',
            color: 'green'
          });
        }
      } else {
        await parametersService.createCountry(countryData);
        notifications.show({
          title: 'Succès',
          message: 'Pays créé avec succès',
          color: 'green'
        });
      }

      // Recharger les données
      await loadCountries();
      closePaysModal();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      notifications.show({
        title: 'Erreur',
         message: 'Erreur lors de la suppression: ' + errorMessage,
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePays = async (id: number) => {
    try {
      setLoading(true);

      // Trouver le pays correspondant dans les données backend
      const country = countries.find(c => parseInt(c.id) === id);
      if (country) {
        await parametersService.deleteCountry(country.id);
        notifications.show({
          title: 'Succès',
          message: 'Pays supprimé avec succès',
          color: 'green'
        });

        // Recharger les données
        await loadCountries();
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      notifications.show({
        title: 'Erreur',
         message: 'Erreur lors de la suppression: ' + errorMessage,
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  // Fonctions pour gérer les modales - Ville
  const openVilleModal = (ville?: Ville) => {
    if (ville) {
      setEditingVille(ville);
      setVilleForm({
        abre: ville.abre,
        nom: ville.nom,
        pays: ville.pays,
      });
    } else {
      setEditingVille(null);
      setVilleForm({
        abre: '',
        nom: '',
        pays: paysList.length > 0 ? paysList[0].nom : '',
      });
    }
    setVilleModalOpened(true);
  };

  const closeVilleModal = () => {
    setVilleModalOpened(false);
    setEditingVille(null);
  };

  const handleSaveVille = async () => {
    if (!villeForm.nom.trim()) return;

    try {
      setLoading(true);

      // Trouver le pays correspondant
      const selectedCountry = countries.find(c => c.name === villeForm.pays);
      if (!selectedCountry) {
        notifications.show({
          title: 'Erreur',
          message: 'Pays sélectionné introuvable',
          color: 'red'
        });
        return;
      }

      const regionData = {
        name: villeForm.nom,
        country: selectedCountry.id,
        code: villeForm.abre || villeForm.nom.substring(0, 3).toUpperCase(),
        is_active: true
      };

      if (editingVille) {
        // Trouver la région correspondante dans les données backend
        const region = regions.find(r => parseInt(r.id) === editingVille.id);
        if (region) {
          await parametersService.updateRegion(region.id, regionData);
          notifications.show({
            title: 'Succès',
            message: 'Région mise à jour avec succès',
            color: 'green'
          });
        }
      } else {
        await parametersService.createRegion(regionData);
        notifications.show({
          title: 'Succès',
          message: 'Région créée avec succès',
          color: 'green'
        });
      }

      // Recharger les données
      await loadRegions();
      closeVilleModal();
    } catch (err: unknown) {
       const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      notifications.show({
        title: 'Erreur',
         message: 'Erreur lors de la sauvegarde: ' + errorMessage,
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVille = async (id: number) => {
    try {
      setLoading(true);

      // Trouver la région correspondante dans les données backend
      const region = regions.find(r => parseInt(r.id) === id);
      if (region) {
        await parametersService.deleteRegion(region.id);
        notifications.show({
          title: 'Succès',
          message: 'Région supprimée avec succès',
          color: 'green'
        });

        // Recharger les données
        await loadRegions();
      }
    } catch (err: unknown) {
       const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      notifications.show({
        title: 'Erreur',
         message: 'Erreur lors de la suppression: ' + errorMessage,
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  };

  // Fonctions pour gérer les modales - Prefecture
  const openPrefectureModal = (prefecture?: Prefecture) => {
    if (prefecture) {
      setEditingPrefecture(prefecture);
      setPrefectureForm({
        abre: prefecture.abre,
        nom: prefecture.nom,
        ville: prefecture.ville,
      });
    } else {
      setEditingPrefecture(null);
      setPrefectureForm({
        abre: '',
        nom: '',
        ville: villesList.length > 0 ? villesList[0].nom : '',
      });
    }
    setPrefectureModalOpened(true);
  };

  const closePrefectureModal = () => {
    setPrefectureModalOpened(false);
    setEditingPrefecture(null);
  };

  const handleSavePrefecture = () => {
    if (!prefectureForm.nom.trim()) return;

    const newPrefecture: Prefecture = {
      id: editingPrefecture ? editingPrefecture.id : Date.now(),
      abre: prefectureForm.abre,
      nom: prefectureForm.nom,
      ville: prefectureForm.ville,
    };

    if (editingPrefecture) {
      setPrefecturesList(prev => prev.map(p => p.id === editingPrefecture.id ? newPrefecture : p));
    } else {
      setPrefecturesList(prev => [...prev, newPrefecture]);
    }

    closePrefectureModal();
  };

  const handleDeletePrefecture = (id: number) => {
    setPrefecturesList(prev => prev.filter(p => p.id !== id));
  };

  return (
    <Stack gap="lg" className="w-full" style={{ width: '100%', maxWidth: '100%' }}>
      <Title order={2} className="text-gray-800 flex items-center gap-2">
        <IconMapPin size={24} className="text-blue-600" />
        Gestion des emplacements
      </Title>

      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Erreur" color="red">
          {error}
        </Alert>
      )}

      {loading && (
        <Group justify="center">
          <Loader size="md" />
          <Text>Chargement en cours...</Text>
        </Group>
      )}

      <Tabs
        value={activeTab}
        onChange={(value) => setActiveTab(value || 'pays')}
        variant="outline"
        style={{ width: '100%' }}
        className="w-full"
      >
        <Tabs.List style={{ width: '100%' }}>
          <Tabs.Tab value="pays" leftSection={<IconMapPin size={16} />}>
            Pays
          </Tabs.Tab>
          <Tabs.Tab value="ville" leftSection={<IconMapPin size={16} />}>
            Ville
          </Tabs.Tab>
          <Tabs.Tab value="prefecture" leftSection={<IconMapPin size={16} />}>
            Préfecture
          </Tabs.Tab>
        </Tabs.List>

        {/* Onglet Pays */}
        <Tabs.Panel value="pays" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openPaysModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter pays
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Pays[]).map((pays) => (
                    <Table.Tr key={pays.id}>
                      <Table.Td>
                        <Text fw={500}>{pays.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{pays.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openPaysModal(pays)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeletePays(pays.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Ville */}
        <Tabs.Panel value="ville" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openVilleModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter ville
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Pays</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Ville[]).map((ville) => (
                    <Table.Tr key={ville.id}>
                      <Table.Td>
                        <Text fw={500}>{ville.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{ville.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{ville.pays}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openVilleModal(ville)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteVille(ville.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Préfecture */}
        <Tabs.Panel value="prefecture" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openPrefectureModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter préfecture
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Ville</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Prefecture[]).map((prefecture) => (
                    <Table.Tr key={prefecture.id}>
                      <Table.Td>
                        <Text fw={500}>{prefecture.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{prefecture.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{prefecture.ville}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openPrefectureModal(prefecture)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeletePrefecture(prefecture.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Modal pour Pays */}
      <Modal
        opened={paysModalOpened}
        onClose={closePaysModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Pays
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={paysForm.abre}
            onChange={(e) => setPaysForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={paysForm.nom}
            onChange={(e) => setPaysForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closePaysModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSavePays}
                disabled={!paysForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Ville */}
      <Modal
        opened={villeModalOpened}
        onClose={closeVilleModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Ville
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={villeForm.abre}
            onChange={(e) => setVilleForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={villeForm.nom}
            onChange={(e) => setVilleForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <Select
            label={<span>Pays <span className="text-red-500">*</span></span>}
            value={villeForm.pays}
            onChange={(value) => setVilleForm(prev => ({ ...prev, pays: value || '' }))}
            data={paysList.map(p => ({ value: p.nom, label: p.nom }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closeVilleModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSaveVille}
                disabled={!villeForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Préfecture */}
      <Modal
        opened={prefectureModalOpened}
        onClose={closePrefectureModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Préfecture
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={prefectureForm.abre}
            onChange={(e) => setPrefectureForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={prefectureForm.nom}
            onChange={(e) => setPrefectureForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <Select
            label={<span>Ville <span className="text-red-500">*</span></span>}
            value={prefectureForm.ville}
            onChange={(value) => setPrefectureForm(prev => ({ ...prev, ville: value || '' }))}
            data={villesList.map(v => ({ value: v.nom, label: v.nom }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closePrefectureModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSavePrefecture}
                disabled={!prefectureForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default GestionDesEmplacements;
