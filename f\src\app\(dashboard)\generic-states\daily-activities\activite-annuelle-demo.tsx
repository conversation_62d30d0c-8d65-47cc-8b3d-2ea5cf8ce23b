'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { ActiviteAnnuelle } from './ActiviteAnnuelle';

export default function ActiviteAnnuelleDemo() {
  const mockSummary = {
    total: 220125.00,
    encasement_total: 180700.00,
    loaded: true
  };

  const mockPivotData = {
    'Janvier': {
      'Amalgame cl simple': 1200.00,
      'Camera Intrabuccale': 800.00,
      'CCC': 4500.00,
      'Coiffe zircone': 1600.00,
      'Consultation': 2400.00
    },
    'Février': {
      'Amalgame cl simple': 900.00,
      'Brossage Prophylactique': 400.00,
      'Camera Intrabuccale': 600.00,
      'CCC': 3000.00,
      'Consultation': 1800.00,
      'Détartrage': 1500.00
    },
    'Mars': {
      'Amalgame cl simple': 300.00,
      'Camera Intrabuccale': 300.00,
      'Consultation': 1200.00,
      'Détartrage': 800.00
    },
    'Avril': {
      'Ablation de fil de suture': 350.00,
      'Aéropolissage': 500.00,
      'Amalgame cl simple': 357.14,
      'Analyse Biologique': 0.00,
      'Apexification': 1500.00,
      'Apexogénèse': 1152.48,
      'Brossage Prophylactique': 200.00,
      'Camera Intrabuccale': 4400.00,
      'CCC': 9000.00,
      'Coiffe zircone': 800.00,
      'Collage Couronne': 1500.00,
      'Collage Facette': 384.16,
      'Collage Fragment Dentaire': 500.00,
      'Consultation': 3600.00,
      'Détartrage': 2200.00
    },
    'Mai': {
      'Biopulpotomie': 800.00,
      'Brossage Prophylactique': 200.00,
      'Camera Intrabuccale': 1200.00,
      'Consultation': 2400.00,
      'Détartrage': 1000.00
    },
    'Juin': {
      'Camera Intrabuccale': 300.00,
      'Coiffe zircone': 800.00,
      'Consultation': 1800.00,
      'Détartrage': 600.00
    },
    'Juillet': {
      'Amalgame cl simple': 600.00,
      'Camera Intrabuccale': 900.00,
      'CCC': 2250.00,
      'Consultation': 2100.00,
      'Détartrage': 1200.00
    },
    'Août': {
      'Brossage Prophylactique': 300.00,
      'Camera Intrabuccale': 600.00,
      'Consultation': 1500.00,
      'Détartrage': 900.00
    },
    'Septembre': {
      'Amalgame cl simple': 450.00,
      'Camera Intrabuccale': 750.00,
      'CCC': 1800.00,
      'Consultation': 2700.00,
      'Détartrage': 1350.00
    },
    'Octobre': {
      'Camera Intrabuccale': 1050.00,
      'CCC': 3600.00,
      'Coiffe zircone': 1200.00,
      'Consultation': 3000.00,
      'Détartrage': 1800.00
    },
    'Novembre': {
      'Amalgame cl simple': 750.00,
      'Camera Intrabuccale': 1200.00,
      'CCC': 4050.00,
      'Consultation': 2400.00,
      'Détartrage': 1500.00
    },
    'Décembre': {
      'Camera Intrabuccale': 900.00,
      'CCC': 2700.00,
      'Consultation': 1800.00,
      'Détartrage': 1200.00
    }
  };

  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Activité changée:', activity);
  };

  const handleProcedureTypeChange = (type: number) => {
    console.log('Type de procédure changé:', type);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression en cours...');
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <ActiviteAnnuelle
          cycle="annual"
          summary={mockSummary}
          pivotData={mockPivotData}
          loading={false}
          onQueryChange={handleQueryChange}
          onActivityChange={handleActivityChange}
          onProcedureTypeChange={handleProcedureTypeChange}
          onExport={handleExport}
          onPrint={handlePrint}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function ActiviteAnnuelleLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <ActiviteAnnuelle
          cycle="annual"
          loading={true}
          onQueryChange={(query) => console.log('Query:', query)}
          onActivityChange={(activity) => console.log('Activity:', activity)}
          onProcedureTypeChange={(type) => console.log('Type:', type)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle mensuel
export function ActiviteAnnuelleMensuelleDemo() {
  const mockSummaryMensuelle = {
    total: 18500.00,
    encasement_total: 15200.00,
    loaded: true
  };

  const mockPivotDataMensuelle = {
    'Semaine 1': {
      'Consultation': 1200.00,
      'Détartrage': 800.00,
      'Camera Intrabuccale': 600.00
    },
    'Semaine 2': {
      'Consultation': 1500.00,
      'Détartrage': 1000.00,
      'CCC': 2250.00,
      'Amalgame cl simple': 450.00
    },
    'Semaine 3': {
      'Consultation': 1800.00,
      'Détartrage': 1200.00,
      'Camera Intrabuccale': 900.00,
      'Coiffe zircone': 800.00
    },
    'Semaine 4': {
      'Consultation': 2100.00,
      'Détartrage': 1400.00,
      'CCC': 3000.00,
      'Brossage Prophylactique': 300.00
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <ActiviteAnnuelle
          cycle="monthly"
          summary={mockSummaryMensuelle}
          pivotData={mockPivotDataMensuelle}
          loading={false}
          onQueryChange={(query) => console.log('Query mensuelle:', query)}
          onActivityChange={(activity) => console.log('Activity mensuelle:', activity)}
          onProcedureTypeChange={(type) => console.log('Type mensuel:', type)}
          onExport={(format) => alert(`Export mensuel ${format}`)}
          onPrint={() => alert('Impression mensuelle')}
        />
      </div>
    </MantineProvider>
  );
}
