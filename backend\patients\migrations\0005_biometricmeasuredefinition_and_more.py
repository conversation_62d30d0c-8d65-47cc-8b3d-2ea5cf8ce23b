# Generated by Django 5.1.3 on 2025-08-06 14:21

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("patients", "0004_medicaldatacategory_medicaldataitem_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BiometricMeasureDefinition",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(help_text="Measurement name", max_length=100),
                ),
                ("label", models.Char<PERSON>ield(help_text="Display label", max_length=150)),
                (
                    "unit",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Unit of measurement",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "measurement_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("float", "Float"),
                            ("integer", "Integer"),
                            ("boolean", "Boolean"),
                            ("string", "String"),
                            ("date", "Date"),
                            ("calculated", "Calculated"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("vital_signs", "Vital Signs"),
                            ("body_measurements", "Body Measurements"),
                            ("cardiovascular", "Cardiovascular"),
                            ("respiratory", "Respiratory"),
                            ("neurological", "Neurological"),
                            ("laboratory", "Laboratory"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                    ),
                ),
                (
                    "min_value",
                    models.FloatField(
                        blank=True, help_text="Minimum valid value", null=True
                    ),
                ),
                (
                    "max_value",
                    models.FloatField(
                        blank=True, help_text="Maximum valid value", null=True
                    ),
                ),
                ("is_required", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("display_order", models.IntegerField(default=0)),
                (
                    "calculation_formula",
                    models.TextField(
                        blank=True,
                        help_text="Formula for calculated fields (e.g., BMI = weight / (height/100)^2)",
                        null=True,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Biometric Measure Definition",
                "verbose_name_plural": "Biometric Measure Definitions",
                "ordering": ["category", "display_order", "name"],
                "indexes": [
                    models.Index(
                        fields=["category", "is_active"],
                        name="patients_bi_categor_133fb1_idx",
                    ),
                    models.Index(
                        fields=["measurement_type", "is_active"],
                        name="patients_bi_measure_97cde3_idx",
                    ),
                ],
                "unique_together": {("name", "category")},
            },
        ),
        migrations.CreateModel(
            name="PatientBiometricMeasurement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "measurement_date",
                    models.DateTimeField(help_text="When the measurement was taken"),
                ),
                ("value_float", models.FloatField(blank=True, null=True)),
                ("value_integer", models.IntegerField(blank=True, null=True)),
                ("value_boolean", models.BooleanField(blank=True, null=True)),
                (
                    "value_string",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                ("value_date", models.DateField(blank=True, null=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes", null=True
                    ),
                ),
                (
                    "is_abnormal",
                    models.BooleanField(
                        default=False, help_text="Is this measurement abnormal"
                    ),
                ),
                (
                    "device_used",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("location", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "measure_definition",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="measurements",
                        to="patients.biometricmeasuredefinition",
                    ),
                ),
                (
                    "measured_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recorded_measurements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        limit_choices_to={"user_type": "patient"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="biometric_measurements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Biometric Measurement",
                "verbose_name_plural": "Patient Biometric Measurements",
                "ordering": ["-measurement_date", "measure_definition__display_order"],
                "indexes": [
                    models.Index(
                        fields=["patient", "measurement_date"],
                        name="patients_pa_patient_1c41c5_idx",
                    ),
                    models.Index(
                        fields=["measure_definition", "measurement_date"],
                        name="patients_pa_measure_5fd8f0_idx",
                    ),
                    models.Index(
                        fields=["patient", "measure_definition"],
                        name="patients_pa_patient_038474_idx",
                    ),
                ],
            },
        ),
    ]
