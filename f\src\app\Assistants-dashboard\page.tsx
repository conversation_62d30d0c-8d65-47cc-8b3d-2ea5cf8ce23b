
"use client";
import { useState } from "react";
import React from "react";

import Icon from '@mdi/react';
import { mdiCalendarClockOutline } from '@mdi/js';
import MetaSeo from"./MetaSeo"
import AssistantsDashboardPage from"./AssistantsDashboardPage"

import "~/styles/tab.css";

function  Page() {

  const [toggleState, setToggleState] = useState(1);
 

const icons = [
  { icon: <Icon path={mdiCalendarClockOutline} size={1} key="Home" />, label: "Home" },
  {
    icon: <Icon path={mdiCalendarClockOutline} size={1} key="Test-1" />,
    label: "Test-1",
  },
  {
    icon: <Icon path={mdiCalendarClockOutline} size={1} key="Test-2" />,
    label: "Test-2",
  },
  {
    icon: <Icon path={mdiCalendarClockOutline} size={1} key="Test-3" />,
    label: "Test-3",
  },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<AssistantsDashboardPage/> )
    
     case 2:
      return (  <div>Test-2</div>)
      case 3:
        return (  <div>Test-3</div>)
        case 4:
          return <div>Test-4</div>;

    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default Page;

 