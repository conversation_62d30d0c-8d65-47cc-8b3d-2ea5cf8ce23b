# Patient Model Implementation Guide

## Overview

This document outlines the implementation of a dedicated Patient model that separates patient-specific data from the User model, following Django best practices for data modeling and separation of concerns.

## Architecture Decision

### Before: Monolithic User Model
- All patient fields were mixed into the User model
- Poor separation of concerns
- Difficult to maintain and extend
- User model became bloated with patient-specific fields

### After: Dedicated Patient Model
- Clean separation between authentication (User) and patient data (Patient)
- OneToOneField relationship between User and Patient
- Better data organization and maintainability
- Follows Django best practices

## Model Structure

### User Model (Authentication & Basic Info)
```python
class User(AbstractUser):
    # Core authentication fields
    email = models.EmailField(unique=True)
    user_type = models.CharField(choices=USER_TYPE_CHOICES)
    
    # Basic contact information
    first_name = models.CharField(max_length=150)
    last_name = models.Char<PERSON>ield(max_length=150)
    phone_number = models.CharField(max_length=15)
    address = models.TextField()
    
    # Profile and location fields
    # ... (doctor-specific fields remain here)
```

### Patient Model (Comprehensive Patient Data)
```python
class Patient(models.Model):
    # Link to User model
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='patient_profile')
    
    # Patient identification
    patient_id = models.CharField(max_length=50, unique=True)
    medical_record_number = models.CharField(max_length=50, unique=True)
    
    # Comprehensive patient fields organized by category
    # ... (see full model definition)
```

## Field Categories

### 1. Patient Identification
- `patient_id` - Auto-generated unique identifier (PAT-YYYYMMDD-XXXX)
- `medical_record_number` - Auto-generated MRN (MRN-XXXXXXXX)
- `title` - Patient title (Mr., Mrs., Dr., etc.)

### 2. Personal Information
- `date_of_birth`, `age`, `gender`, `marital_status`
- `nationality`, `spoken_languages`, `preferred_language`

### 3. Identification Documents
- `social_security`, `cin`, `passport_number`

### 4. Contact Information (extends User)
- `secondary_phone`, `work_phone`, `preferred_contact_method`
- `secondary_address`, `zip_code`, `state`

### 5. Emergency Contact
- `emergency_contact_name`, `emergency_contact_phone`
- `emergency_contact_relationship`, `emergency_contact_address`

### 6. Medical Information
- `blood_type`, `height`, `weight`, `allergies`
- `current_medications`, `medical_history`
- `chronic_conditions`, `previous_surgeries`, `family_medical_history`

### 7. Lifestyle Information
- `smoking_status`, `alcohol_consumption`, `exercise_frequency`

### 8. Insurance Information
- `insurance_provider`, `insurance_policy_number`
- `insurance_group_number`, `secondary_insurance`

### 9. Employment Information
- `occupation`, `employer`, `work_address`, `education_level`

### 10. Communication Preferences
- `communication_preference`, `appointment_reminder_preference`
- `do_not_call`, `do_not_email`, `do_not_sms`, `best_time_to_call`

### 11. Patient Status and Care
- `patient_status`, `patient_priority`, `referral_source`
- `primary_care_physician`, `assigned_doctor`

### 12. Visit Tracking
- `first_visit_date`, `last_visit_date`, `total_visits`
- `missed_appointments`, `follow_up_required`, `follow_up_date`

### 13. Special Needs and Accessibility
- `mobility_assistance_needed`, `interpreter_needed`
- `special_accommodations`

### 14. Legal and Consent
- `consent_for_treatment`, `consent_for_communication`
- `hipaa_authorization`, `consent_date`

### 15. Financial Information
- `payment_method_preference`, `outstanding_balance`, `credit_limit`

### 16. Quality Metrics
- `patient_satisfaction_score`, `care_quality_rating`

### 17. Administrative Fields
- `registration_date`, `last_updated_by`, `data_source`
- `chart_location`, `digital_chart_id`

### 18. Notes and Comments
- `notes` - General patient notes
- `internal_notes` - Internal staff notes (not visible to patient)

## Key Features

### Auto-Generated IDs
```python
def generate_patient_id(self):
    """Generate format: PAT-YYYYMMDD-XXXX"""
    # Implementation ensures uniqueness

def generate_medical_record_number(self):
    """Generate format: MRN-XXXXXXXX"""
    # Implementation ensures uniqueness
```

### Calculated Properties
```python
@property
def age_calculated(self):
    """Calculate age from date of birth"""

@property
def has_emergency_contact(self):
    """Check if patient has emergency contact information"""

@property
def has_insurance(self):
    """Check if patient has insurance information"""

@property
def is_high_priority(self):
    """Check if patient is high priority"""
```

### Utility Methods
```python
def update_visit_count(self):
    """Update the total visit count"""

def add_missed_appointment(self):
    """Increment missed appointment count"""

def get_contact_info(self):
    """Get formatted contact information"""

def get_medical_summary(self):
    """Get a summary of medical information"""
```

## Database Migration

### Migration Files Created
1. `0005_create_patient_model.py` - Creates the Patient model
2. Indexes created for optimal query performance:
   - `patient_id`
   - `medical_record_number`
   - `patient_status`
   - `assigned_doctor`
   - `registration_date`

### Migration Command
```bash
python manage.py migrate users
```

## API Integration

### Serializers Created
- `PatientCreateSerializer` - For creating new patients
- `PatientDetailSerializer` - For detailed patient information
- `PatientUpdateSerializer` - For updating patient information
- `PatientListSerializer` - For patient list views
- `PatientSearchSerializer` - For patient search functionality
- `PatientMedicalInfoSerializer` - Medical information only
- `PatientContactInfoSerializer` - Contact information only

### Usage Example
```python
from users.serializers.patient_serializers import PatientCreateSerializer

# Create a new patient
serializer = PatientCreateSerializer(data=patient_data)
if serializer.is_valid():
    patient = serializer.save()
```

## Admin Interface

### Comprehensive Admin
- Organized fieldsets by category
- Custom list display with calculated fields
- Advanced filtering options
- Search functionality across multiple fields
- Custom actions for common operations

### Admin Features
- Patient status management
- Visit count tracking
- Quality metrics monitoring
- Communication preference management

## Benefits of This Implementation

### 1. Separation of Concerns
- User model handles authentication and basic info
- Patient model handles comprehensive patient data
- Clear boundaries between different data types

### 2. Maintainability
- Easier to add new patient-specific fields
- Better code organization
- Reduced complexity in User model

### 3. Performance
- Optimized queries with proper indexing
- Selective loading of patient data when needed
- Better database performance

### 4. Extensibility
- Easy to add new patient-related models
- Clear relationship patterns
- Future-proof architecture

### 5. Data Integrity
- Auto-generated unique identifiers
- Proper field validation
- Consistent data structure

## Usage Guidelines

### Creating a Patient
```python
# Create user first
user = User.objects.create_user(
    email='<EMAIL>',
    password='password',
    first_name='John',
    last_name='Doe',
    user_type='patient'
)

# Create patient profile
patient = Patient.objects.create(
    user=user,
    date_of_birth='1990-01-01',
    gender='M',
    # ... other patient fields
)
```

### Accessing Patient Data
```python
# From user to patient
user = User.objects.get(email='<EMAIL>')
patient = user.patient_profile

# From patient to user
patient = Patient.objects.get(patient_id='PAT-*************')
user = patient.user
```

### Querying Patients
```python
# Get all active patients
active_patients = Patient.objects.filter(patient_status='active')

# Get patients by doctor
doctor_patients = Patient.objects.filter(assigned_doctor=doctor)

# Get high priority patients
urgent_patients = Patient.objects.filter(patient_priority='urgent')
```

## Next Steps

1. **Run Migrations**: Apply the database changes
2. **Update Frontend**: Modify frontend to use new Patient model
3. **API Endpoints**: Create/update API endpoints for patient management
4. **Data Migration**: Migrate existing patient data from User to Patient model
5. **Testing**: Comprehensive testing of new patient functionality

This implementation provides a solid foundation for comprehensive patient management while maintaining clean, maintainable code structure.
