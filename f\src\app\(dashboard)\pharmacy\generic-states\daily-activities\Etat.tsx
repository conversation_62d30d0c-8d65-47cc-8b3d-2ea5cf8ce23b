import React from 'react'
import { Tabs } from '@mantine/core';
import { ScrollArea } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiCalendarToday ,mdiCalendarWeekend,mdiCalendarMonth,mdiCalendarAlert,mdiAccountDetails,mdiAccountGroup,mdiAccountPlus,
  mdiStethoscope,mdiMedicalBag,mdiCalendarSync,mdiAccountCog,mdiPill,mdiCashRefund,mdiCashRegister,mdiHumanPregnant,mdiCurrencyUsd,mdiCashMultiple
} from '@mdi/js';
import {ActivitEjournaliere} from './ActivitEjournaliere'
import {Activiteperiodique} from './Activiteperiodique'
import {ActiviteMensuelle} from './ActiviteMensuelle'
import {ActiviteAnnuelle} from './ActiviteAnnuelle'
import EcheanceDesCheques from './Echeance_des_chequeq'
import EtatsDuPatient from './Etats_du_patient'
import {Patient_par_tranche_dage} from './Patient_par_tranche_dage'
import NouveauPatients from './Nouveau_patients'
import MedecinTraitants from './Medecin_traitants'

import Pathologies from './Pathologies'
import RapportDesRendezVous from './Rapport_des_rendez_vous'
import PatientVisitsAssures from './Patient-visits-assures'
import UtilisationDesMedicamentsAPI from './Utilisation_des_medicaments_api'

import BalanceAgee from './Balance_Agee'
import EtatDeRelance from './Etat_de_relance'
import  Calendrier_daccouchement from './Calendrier_daccouchement'
import  EcheanceDesChequesEspece from './Echeance_des_cheques_espece'
import Prestations_par_Medecin from './Prestations_par_Medecin'

const Etat = () => {
  return (
     
    <div className='w-full'>
        <ScrollArea h={'100vh'}>
<Tabs variant="outline" radius="md" orientation="vertical" defaultValue="ActivitEjournaliere" w={"100%"} mt={10}>

      <Tabs.List>
        <Tabs.Tab value="Rapport_des_rendez_vous" leftSection={<Icon path={mdiCalendarToday} size={1} />}>
          Activité journalière
        </Tabs.Tab>
        <Tabs.Tab value="Activiteperiodique" leftSection={<Icon path={mdiCalendarWeekend} size={1} />}>
          Activité périodique
        </Tabs.Tab>
         <Tabs.Tab value="ActiviteMensuelle" leftSection={<Icon path={mdiCalendarMonth} size={1} />}>
          Activité mensuelle
        </Tabs.Tab>
        <Tabs.Tab value="ActiviteAnnuelle" leftSection={<Icon path={mdiCalendarMonth} size={1} />}>
          Activité annuelle
        </Tabs.Tab>
        <Tabs.Tab value="EcheanceDesCheques" leftSection={<Icon path={mdiCalendarAlert} size={1} />}>
         Échéance des chèques
        </Tabs.Tab>
        <Tabs.Tab value="EtatsDuPatient" leftSection={<Icon path={mdiAccountDetails} size={1} />}>
         Etats Du Patient
        </Tabs.Tab>
        <Tabs.Tab value="Patient_par_tranche_dage" leftSection={<Icon path={mdiAccountGroup} size={1} />}>
         Patient par tranche d&apos;âge
        </Tabs.Tab>
        <Tabs.Tab value="NouveauPatients" leftSection={<Icon path={mdiAccountPlus} size={1} />}>
         Nouveau Patients
        </Tabs.Tab>

         <Tabs.Tab value="MedecinTraitants" leftSection={<Icon path={mdiStethoscope} size={1} />}>
         Medecin traitants
        </Tabs.Tab>
         <Tabs.Tab value="Pathologies" leftSection={<Icon path={mdiMedicalBag} size={1} />}>
         Pathologies
        </Tabs.Tab>
         <Tabs.Tab value="RapportDesRendezVous" leftSection={<Icon path={mdiCalendarSync} size={1} />}>
         Rapport des rdv & Motifs
        </Tabs.Tab>
         <Tabs.Tab value="PatientVisitsAssures" leftSection={<Icon path={mdiAccountCog} size={1} />}>
         Patients/Visits assurés
        </Tabs.Tab>
         <Tabs.Tab value="UtilisationDesMedicamentsAPI" leftSection={<Icon path={mdiPill} size={1} />}>
         Utilisation des médicaments
        </Tabs.Tab>

        <Tabs.Tab value="BalanceAgee" leftSection={<Icon path={mdiCashRefund} size={1} />}>
       Balance âgée
        </Tabs.Tab>
       <Tabs.Tab value="Calendrier_daccouchement" leftSection={<Icon path={mdiHumanPregnant} size={1} />}>
       Calendrier d&apos;accouchement
        </Tabs.Tab>
        <Tabs.Tab value="EcheanceDesChequesEspece" leftSection={<Icon path={mdiCurrencyUsd} size={1} />}>
       Échéance des chèques
        </Tabs.Tab>
        <Tabs.Tab value="Prestations_par_Medecin" leftSection={<Icon path={mdiCashMultiple} size={1} />}>
       Prestations par Médecin
        </Tabs.Tab>
        <Tabs.Tab value="EtatDeRelance" leftSection={<Icon path={mdiCashRegister} size={1} />}>
       État de relance
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Rapport_des_rendez_vous" ml={20}>
       <ActivitEjournaliere/>
      </Tabs.Panel>

      <Tabs.Panel value="Activiteperiodique"ml={20} >
      <Activiteperiodique/>
      </Tabs.Panel>

      <Tabs.Panel value="ActiviteMensuelle" ml={20}>
     <ActiviteMensuelle/>
      </Tabs.Panel>
         <Tabs.Panel value="ActiviteAnnuelle" ml={20}>
      <ActiviteAnnuelle/>
      </Tabs.Panel>
         <Tabs.Panel value="EcheanceDesCheques" ml={20}>
       <EcheanceDesCheques/>
      </Tabs.Panel>
         <Tabs.Panel value="EtatsDuPatient" ml={20}>
      <EtatsDuPatient/>
      </Tabs.Panel>
         <Tabs.Panel value="Patient_par_tranche_dage" ml={20}>
        <Patient_par_tranche_dage/>
      </Tabs.Panel>
       <Tabs.Panel value="NouveauPatients" ml={20}>
        <NouveauPatients/>
      </Tabs.Panel>

       <Tabs.Panel value="MedecinTraitants" ml={20}>
        <MedecinTraitants/>
      </Tabs.Panel>
       <Tabs.Panel value="Pathologies" ml={20}>
        <Pathologies/>
      </Tabs.Panel>
       <Tabs.Panel value="RapportDesRendezVous" ml={20}>
        <RapportDesRendezVous/>
      </Tabs.Panel>
       <Tabs.Panel value="PatientVisitsAssures" ml={20}>
        <PatientVisitsAssures/>
      </Tabs.Panel>
       <Tabs.Panel value="UtilisationDesMedicamentsAPI" ml={20}>
        <UtilisationDesMedicamentsAPI/>
      </Tabs.Panel>
      <Tabs.Panel value="BalanceAgee" ml={20}>
        <BalanceAgee/>
      </Tabs.Panel>
      <Tabs.Panel value="EtatDeRelance" ml={20}>
        <EtatDeRelance/>
      </Tabs.Panel>

     < Tabs.Panel value="Calendrier_daccouchement" ml={20}>
        <Calendrier_daccouchement/>
      </Tabs.Panel>
      <Tabs.Panel value="EcheanceDesChequesEspece" ml={20}>
        <EcheanceDesChequesEspece/>
      </Tabs.Panel>
      <Tabs.Panel value="Prestations_par_Medecin" ml={20}>
        <Prestations_par_Medecin/>
      </Tabs.Panel>
     
    </Tabs>
   </ScrollArea>
    </div>
     
  )
}

export default Etat
