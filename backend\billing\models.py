"""
Billing models for the patient management system.
"""

from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal
import uuid


class Invoice(models.Model):
    """
    Invoice model for billing patients.
    """
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
        ('cancelled', _('Cancelled')),
        ('refunded', _('Refunded')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    invoice_number = models.CharField(max_length=50, unique=True)
    
    # Patient and appointment
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='invoices',
        limit_choices_to={'user_type': 'patient'}
    )
    appointment = models.ForeignKey(
        'appointments.Appointment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='invoices'
    )
    
    # Invoice details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    issue_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    
    # Amounts
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Payment tracking
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    balance_due = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    terms_and_conditions = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_invoices'
    )

    class Meta:
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-created_at']

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_full_name method on ForeignKey
        return f"Invoice {self.invoice_number} - {self.patient.get_full_name()}"  # type: ignore

    def save(self, *args, **kwargs):
        """Calculate totals when saving."""
        # Type ignore for basedpyright operator issues with DecimalField
        self.tax_amount = self.subtotal * (self.tax_rate / 100)  # type: ignore
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        self.balance_due = self.total_amount - self.paid_amount
        super().save(*args, **kwargs)


class InvoiceItem(models.Model):
    """
    Individual items on an invoice.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    
    description = models.CharField(max_length=200)
    quantity = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('1.00'))
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Service/product details
    service_code = models.CharField(max_length=50, blank=True, null=True)
    service_category = models.CharField(max_length=100, blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Invoice Item')
        verbose_name_plural = _('Invoice Items')

    def __str__(self):
        # Type ignore for basedpyright not recognizing invoice_number attribute
        return f"{self.description} - {self.invoice.invoice_number}"  # type: ignore

    def save(self, *args, **kwargs):
        """Calculate total price when saving."""
        # Type ignore for basedpyright operator issues with DecimalField
        self.total_price = self.quantity * self.unit_price  # type: ignore
        super().save(*args, **kwargs)


class Payment(models.Model):
    """
    Payment records for invoices.
    """
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('credit_card', _('Credit Card')),
        ('debit_card', _('Debit Card')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('insurance', _('Insurance')),
        ('other', _('Other')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('refunded', _('Refunded')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment_number = models.CharField(max_length=50, unique=True)
    
    # Invoice and patient
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments')
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments',
        limit_choices_to={'user_type': 'patient'}
    )
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Payment processing
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    
    # Dates
    payment_date = models.DateTimeField(default=timezone.now)
    processed_date = models.DateTimeField(null=True, blank=True)
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='processed_payments'
    )

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']

    def __str__(self):
        return f"Payment {self.payment_number} - {self.amount}"


class Subscription(models.Model):
    """
    Subscription model for recurring billing.
    """
    PLAN_CHOICES = [
        ('basic', _('Basic Plan')),
        ('premium', _('Premium Plan')),
        ('professional', _('Professional Plan')),
        ('enterprise', _('Enterprise Plan')),
    ]

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('cancelled', _('Cancelled')),
        ('expired', _('Expired')),
        ('suspended', _('Suspended')),
    ]

    BILLING_CYCLE_CHOICES = [
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('yearly', _('Yearly')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    subscription_number = models.CharField(max_length=50, unique=True)
    
    # Customer
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='billing_subscriptions',
        limit_choices_to={'user_type': 'patient'}
    )
    
    # Subscription details
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES, default='monthly')
    
    # Pricing
    monthly_price = models.DecimalField(max_digits=10, decimal_places=2)
    setup_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Dates
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    next_billing_date = models.DateField()
    
    # Features and limits
    features = models.JSONField(default=dict, blank=True)
    usage_limits = models.JSONField(default=dict, blank=True)
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Subscription')
        verbose_name_plural = _('Subscriptions')
        ordering = ['-created_at']

    def __str__(self):
        # Type ignore for basedpyright not recognizing get_full_name method on ForeignKey
        return f"{self.plan} - {self.patient.get_full_name()}"  # type: ignore


class License(models.Model):
    """
    License model for software/service licensing.
    """
    LICENSE_TYPE_CHOICES = [
        ('trial', _('Trial License')),
        ('standard', _('Standard License')),
        ('professional', _('Professional License')),
        ('enterprise', _('Enterprise License')),
    ]

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('expired', _('Expired')),
        ('suspended', _('Suspended')),
        ('revoked', _('Revoked')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    license_key = models.CharField(max_length=100, unique=True)
    
    # License holder
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='licenses',
        limit_choices_to={'user_type': 'patient'}
    )
    
    # License details
    license_type = models.CharField(max_length=20, choices=LICENSE_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Validity
    issue_date = models.DateField(default=timezone.now)
    expiry_date = models.DateField()
    
    # Features and restrictions
    features = models.JSONField(default=dict, blank=True)
    restrictions = models.JSONField(default=dict, blank=True)
    
    # Usage tracking
    activation_count = models.PositiveIntegerField(default=0)  # type: ignore
    max_activations = models.PositiveIntegerField(default=1)  # type: ignore
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('License')
        verbose_name_plural = _('Licenses')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.license_type} - {self.license_key}"

    @property
    def is_expired(self):
        """Check if license is expired."""
        return self.expiry_date < timezone.now().date()

    @property
    def days_until_expiry(self):
        """Calculate days until expiry."""
        if self.is_expired:
            return 0
        # Type ignore for basedpyright operator issues with DateField
        return (self.expiry_date - timezone.now().date()).days  # type: ignore
