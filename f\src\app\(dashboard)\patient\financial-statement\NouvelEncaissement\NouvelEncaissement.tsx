import {  Group, Title, Button, Box } from '@mantine/core';
import { mdiCurrencyUsd, mdiFormatListBulleted } from '@mdi/js';
import Icon from '@mdi/react';
import Link from 'next/link';

export default function NouvelEncaissement({ readOnly }: { readOnly: boolean }) {
  return (
    <Box className="mn-module-header" bg="blue.6" px="md" py="sm">
      <Group justify="space-between">
        <Group>
          <Icon path={mdiCurrencyUsd} size={1.2} className="mn-header-icon" />
          <Title order={2}>
            {!readOnly && 'Nouvel encaissement'}
          </Title>
        </Group>

        <Link href="/pratisoft/payment/" passHref>
          <Button
            leftSection={<Icon path={mdiFormatListBulleted} size={1} />}
            variant="subtle"
          >
            Recettes
          </Button>
        </Link>
      </Group>
    </Box>
  );
}

