import { Patient } from '@/services/patientService';

// Interface for the legacy VisiteData format used in patient list components
export interface VisiteData {
  id: number;
  date: string;
  nom: string;
  prenom: string;
  dateNaissance: string;
  age: string;
  cin: string;
  telephone: string;
  ville: string;
  assurance: string;
  selected?: boolean;
}

/**
 * Converts Patient data from backend to VisiteData format for compatibility
 * with existing patient list components
 */
export const convertPatientsToVisiteData = (patients: Patient[]): VisiteData[] => {
  return patients.map((patient, index) => ({
    id: parseInt(patient.id) || index + 1,
    date: patient.created_at ? new Date(patient.created_at).toLocaleDateString('fr-FR') : '',
    nom: patient.last_name || '',
    prenom: patient.first_name || '',
    dateNaissance: patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString('fr-FR') : '',
    age: patient.age?.toString() || '',
    cin: patient.national_id_number || '',
    telephone: patient.phone_number || '',
    ville: patient.address || '',
    assurance: patient.insurance_company || '',
  }));
};

/**
 * Creates a search handler function for patient components
 */
export const createPatientSearchHandler = (
  setSearchTerm: (term: string) => void,
  searchPatients: (filters: any) => Promise<void>,
  fetchPatientsByStatus: (status: any) => Promise<void>,
  status: string
) => {
  return (term: string) => {
    setSearchTerm(term);
    if (term.trim()) {
      searchPatients({ search: term, status });
    } else {
      fetchPatientsByStatus(status as any);
    }
  };
};

/**
 * Creates loading state JSX for patient tables
 */
export const createLoadingState = (loading: boolean, error: string | null, dataLength: number, emptyMessage: string) => {
  if (loading) {
    return (
      <tr>
        <td colSpan={10} className="text-center py-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3">Chargement des patients...</span>
          </div>
        </td>
      </tr>
    );
  }
  
  if (error) {
    return (
      <tr>
        <td colSpan={10} className="text-center py-8">
          <span className="text-red-600">Erreur: {error}</span>
        </td>
      </tr>
    );
  }
  
  if (dataLength === 0) {
    return (
      <tr>
        <td colSpan={10} className="text-center py-8">
          <span className="text-gray-500">{emptyMessage}</span>
        </td>
      </tr>
    );
  }
  
  return null;
};
