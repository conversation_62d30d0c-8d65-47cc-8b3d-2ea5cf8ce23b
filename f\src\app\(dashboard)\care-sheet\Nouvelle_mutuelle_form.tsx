'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Select,
  TextInput,
  Button,
  Tabs,
  Textarea,
  Checkbox,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconFileText,
  IconSearch,
  IconPlus,
  IconMinus,
  IconList,
  IconPrinter,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

// Import du hook pour les mutuelles
import { useMutuelles } from '@/hooks/useMutuelles';

// Import des modales
import Liste_des_visites_modal from './Liste_des_visites_modal';
import Liste_des_visites_dentaire_modal from './Liste_des_visites_dentaire_modal';
import Liste_des_devis_modal from './Liste_des_devis_modal';
import Liste_des_procedures_modal from './Liste_des_procedures_modal';

const Nouvelle_mutuelle_form = () => {
  // Hook pour les mutuelles
  const { createMutuelle } = useMutuelles();

  // États pour les données de la mutuelle
  const [date, setDate] = useState<Date | null>(new Date('2022-09-16'));
  const [numeroDossier, setNumeroDossier] = useState('');
  const [docteur, setDocteur] = useState('MEDECIN');
  const [numeroAffiliation, setNumeroAffiliation] = useState('');
  const [beneficiaire, setBeneficiaire] = useState('Lui même');
  const [titre, setTitre] = useState('SOUAD');
  const [prenom, setPrenom] = useState('');
  const [nom, setNom] = useState('ABADI');
  const [dateNaissance, setDateNaissance] = useState<Date | null>(new Date('1986-01-01'));
  const [ville, setVille] = useState('CASABLANCA');
  const [adresse, setAdresse] = useState('');
  const [patient, setPatient] = useState('ABADI SOUAD');
  const [assurance, setAssurance] = useState('');
  const [organisme, setOrganisme] = useState('CNSS');
  const [remisesGlobales, setRemisesGlobales] = useState(false);
  const [commentaire, setCommentaire] = useState('');
  const [activeTab, setActiveTab] = useState('visites');

  // États pour les modales
  const [isVisitesModalOpen, setIsVisitesModalOpen] = useState(false);
  const [isVisitesDentaireModalOpen, setIsVisitesDentaireModalOpen] = useState(false);
  const [isDevisModalOpen, setIsDevisModalOpen] = useState(false);
  const [isProceduresModalOpen, setIsProceduresModalOpen] = useState(false);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // État pour le chargement
  const [saving, setSaving] = useState(false);

  // Handlers for date inputs
  const handleDateChange = (value: string | null) => {
    if (value) {
      setDate(new Date(value));
    } else {
      setDate(null);
    }
  };

  const handleDateNaissanceChange = (value: string | null) => {
    if (value) {
      setDateNaissance(new Date(value));
    } else {
      setDateNaissance(null);
    }
  };

  // Fonction pour sauvegarder la mutuelle
  const handleSave = async (shouldClose = false) => {
    try {
      setSaving(true);

      // Préparer les données de la mutuelle
      const mutuelleData = {
        date: date?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        organisme: organisme,
        patient: patient,
        nomAssure: `${titre} ${prenom} ${nom}`.trim(),
        numeroAffiliate: numeroAffiliation,
        beneficiaire: beneficiaire as 'PATIENT' | 'CONJOINT' | 'ENFANT',
        montant: 0, // Sera calculé selon les éléments sélectionnés
        etat: 'non-validee' as const,
        numeroDossier: numeroDossier,
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString(),
      };

      await createMutuelle(mutuelleData);

      notifications.show({
        title: 'Succès',
        message: 'Mutuelle créée avec succès',
        color: 'green'
      });

      if (shouldClose) {
        // Fermer le modal ou naviguer vers la liste
        window.history.back();
      }
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la création de la mutuelle',
        color: 'red'
      });
    } finally {
      setSaving(false);
    }
  };

  // Données d'exemple pour les éléments
  const [elements, setElements] = useState([
    { id: 1, code: '', description: 'Aucun élément trouvé', qte: 0, prix: 0, montant: 0 }
  ]);

  // Calcul du total
  const total = elements.reduce((sum, element) => sum + element.montant, 0);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconFileText size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Nouvelle Mutuelle
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Mutuelles">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconList size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[600px]">
        {/* Section gauche avec les informations de base */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="sm">
            {/* Date */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Date *
              </Text>
              <DatePickerInput
                value={date ? date.toISOString().split('T')[0] : null}
                onChange={handleDateChange}
                size="xs"
                className="w-full"
                placeholder="16/09/2022"
              />
            </div>

            {/* Numéro de dossier */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Numéro de dossier
              </Text>
              <TextInput
                value={numeroDossier}
                onChange={(e) => setNumeroDossier(e.target.value)}
                size="xs"
                className="w-full"
              />
            </div>

            {/* Docteur */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Docteur *
              </Text>
              <Group gap="xs">
                <Select
                  value={docteur}
                  onChange={(value) => setDocteur(value || 'MEDECIN')}
                  size="xs"
                  className="flex-1"
                  data={['MEDECIN']}
                />
                <ActionIcon
                  variant="light"
                  color="red"
                  size="sm"
                >
                  <IconMinus size={14} />
                </ActionIcon>
              </Group>
            </div>

            {/* N° Affiliation */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                N° Affiliation
              </Text>
              <TextInput
                value={numeroAffiliation}
                onChange={(e) => setNumeroAffiliation(e.target.value)}
                size="xs"
                className="w-full"
              />
            </div>

            {/* Bénéficiaire */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Bénéficiaire (Patient - Assuré)
              </Text>
              <Radio.Group
                value={beneficiaire}
                onChange={setBeneficiaire}
                size="xs"
              >
                <Stack gap="xs">
                  <Radio value="Lui même" label="Lui même" />
                  <Radio value="Conjoint" label="Conjoint" />
                  <Radio value="Enfant" label="Enfant" />
                  <Radio value="Parent" label="Parent" />
                </Stack>
              </Radio.Group>
            </div>

            {/* Patient */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Patient *
              </Text>
              <Group gap="xs">
                <TextInput
                  value={patient}
                  onChange={(e) => setPatient(e.target.value)}
                  size="xs"
                  className="flex-1"
                  placeholder="ABADI SOUAD"
                />
                <ActionIcon
                  variant="light"
                  color="blue"
                  size="sm"
                >
                  <IconSearch size={14} />
                </ActionIcon>
              </Group>
            </div>
          </Stack>
        </Card>

        {/* Section droite avec les détails de l'assuré */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-80 bg-white border-r border-gray-200"
        >
          <Stack gap="sm">
            {/* Titre */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Titre
              </Text>
              <Select
                value={titre}
                onChange={(value) => setTitre(value || 'SOUAD')}
                size="xs"
                className="w-full"
                data={['SOUAD', 'Mr', 'Mme', 'Mlle']}
              />
            </div>

            {/* Prénom */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Prénom
              </Text>
              <TextInput
                value={prenom}
                onChange={(e) => setPrenom(e.target.value)}
                size="xs"
                className="w-full"
              />
            </div>

            {/* Nom */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Nom
              </Text>
              <TextInput
                value={nom}
                onChange={(e) => setNom(e.target.value)}
                size="xs"
                className="w-full"
                placeholder="ABADI"
              />
            </div>

            {/* Date de naissance */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Date de naissa...
              </Text>
              <DatePickerInput
                value={dateNaissance ? dateNaissance.toISOString().split('T')[0] : null}
                onChange={handleDateNaissanceChange}
                size="xs"
                className="w-full"
                placeholder="01/01/1986"
              />
            </div>

            {/* Ville */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Ville
              </Text>
              <Group gap="xs">
                <Select
                  value={ville}
                  onChange={(value) => setVille(value || 'CASABLANCA')}
                  size="xs"
                  className="flex-1"
                  data={['CASABLANCA', 'RABAT', 'FES', 'MARRAKECH']}
                />
                <ActionIcon
                  variant="light"
                  color="blue"
                  size="sm"
                >
                  <IconPlus size={14} />
                </ActionIcon>
                <ActionIcon
                  variant="light"
                  color="red"
                  size="sm"
                >
                  <IconMinus size={14} />
                </ActionIcon>
              </Group>
            </div>

            {/* Adresse */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Adresse
              </Text>
              <TextInput
                value={adresse}
                onChange={(e) => setAdresse(e.target.value)}
                size="xs"
                className="w-full"
              />
            </div>

            {/* Assurance */}
            <div>
              <Text size="xs" fw={500} className="text-gray-700 mb-1">
                Assurance
              </Text>
              <Group gap="xs">
                <Select
                  value={assurance}
                  onChange={(value) => setAssurance(value || '')}
                  size="xs"
                  className="flex-1"
                  data={[
                    'Aucune',
                    'AMO',
                    'ATLANTA',
                    'CNOPS',
                    'CNSS',
                    'CNCF',
                    'RMA WATANYA',
                    'AXA ASSURANCE MAROC',
                    'WAFA ASSURANCE',
                    'SAHAM ASSURANCE',
                    'CMIM',
                    'SANAD',
                    'Allianz',
                    'Es Saada',
                    'MAROCAINE VIE',
                    'LYDEC',
                    'MUFRAS',
                    'NM',
                    'BP',
                    'MAMDA',
                    'MUTUELLE',
                    'MCMA',
                    'MAMT',
                    'ONE',
                    'ATLANTA/SANAD',
                    'ZURICH',
                    'MNE',
                    'FAR',
                    'OCP',
                    'BANK AL MAGHREB',
                    'Autre',
                    'RAM',
                    'CNIA SAADA'
                  ]}
                  placeholder="Sélectionner une assurance"
                  searchable
                />
                <ActionIcon
                  variant="light"
                  color="blue"
                  size="sm"
                >
                  <IconPlus size={14} />
                </ActionIcon>
                <ActionIcon
                  variant="light"
                  color="red"
                  size="sm"
                >
                  <IconMinus size={14} />
                </ActionIcon>
              </Group>
            </div>

            {/* Checkbox Remises globales */}
            <div className="mt-4">
              <Checkbox
                checked={remisesGlobales}
                onChange={(event) => setRemisesGlobales(event.currentTarget.checked)}
                label="Prendre en compte 'Les remises globales'"
                size="xs"
              />
            </div>
          </Stack>
        </Card>

        {/* Zone principale du contenu avec onglets */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Section onglets */}
          <div className="flex-1 flex flex-col">
            <Tabs
              value={activeTab}
              onChange={(value) => setActiveTab(value || 'visites')}
              className="flex-1 flex flex-col"
            >
              <Tabs.List className="bg-gray-100 border-b border-gray-200">
                <Tabs.Tab
                  value="visites"
                  className="text-sm font-medium text-blue-500 cursor-pointer"
                  onClick={() => setIsVisitesModalOpen(true)}
                >
                  Visites
                </Tabs.Tab>
                <Tabs.Tab
                  value="visites-dentaire"
                  className="text-sm font-medium text-blue-500 cursor-pointer"
                  onClick={() => setIsVisitesDentaireModalOpen(true)}
                >
                  Visites dentaire
                </Tabs.Tab>
                <Tabs.Tab
                  value="devis-dentaire"
                  className="text-sm font-medium text-blue-500 cursor-pointer"
                  onClick={() => setIsDevisModalOpen(true)}
                >
                  Devis dentaire
                </Tabs.Tab>
                <Tabs.Tab
                  value="plans-traitement"
                  className="text-sm font-medium text-blue-500 cursor-pointer"
                >
                  Plans de traitement
                </Tabs.Tab>
                <Tabs.Tab
                  value="procedure"
                  className="text-sm font-medium text-blue-500 cursor-pointer"
                  onClick={() => setIsProceduresModalOpen(true)}
                >
                  Procédure
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="visites" className="flex-1 flex flex-col">
                <div className="flex-1 overflow-auto">
                  <Table
                    striped={false}
                    highlightOnHover={true}
                    withTableBorder={true}
                    withColumnBorders={true}
                    className="h-full"
                  >
                    <Table.Thead className="bg-gray-50 sticky top-0">
                      <Table.Tr>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Code
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                          Description
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-20">
                          Qté
                        </Table.Th>
                        <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Prix
                        </Table.Th>
                        <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm w-24">
                          Montant
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {elements.map((element, index) => (
                        <Table.Tr key={index} className="hover:bg-gray-50">
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {element.code}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm">
                            {element.description}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-center">
                            {element.qte}
                          </Table.Td>
                          <Table.Td className="border-r border-gray-300 text-sm text-right">
                            {element.prix.toFixed(2)}
                          </Table.Td>
                          <Table.Td className="text-sm text-right">
                            {element.montant.toFixed(2)}
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </div>

                {/* Section total */}
                <div className="border-t border-gray-300 bg-gray-50 p-3">
                  <Group justify="flex-end" align="center">
                    <Text size="sm" fw={600} className="text-gray-800">
                      Total : {total.toFixed(2)}
                    </Text>
                  </Group>
                </div>
              </Tabs.Panel>

              {/* Autres onglets */}
              {['visites-dentaire', 'devis-dentaire', 'plans-traitement', 'procedure'].map((tab) => (
                <Tabs.Panel key={tab} value={tab} className="flex-1 flex flex-col">
                  <div className="flex-1 flex items-center justify-center">
                    <Text size="sm" className="text-gray-500">
                      Contenu de l&apos;onglet {tab}
                    </Text>
                  </div>
                </Tabs.Panel>
              ))}
            </Tabs>
          </div>
        </div>
      </div>

      {/* Section commentaire */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <div>
          <Text size="xs" fw={500} className="text-gray-700 mb-2">
            Commentaire
          </Text>
          <Textarea
            value={commentaire}
            onChange={(e) => setCommentaire(e.target.value)}
            size="xs"
            className="w-full"
            rows={3}
          />
        </div>
      </Card>

      {/* Boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-gray-50 border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="xs">
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
            >
              <IconPrinter size={16} />
            </ActionIcon>
          </Group>

          <Group gap="sm">
            <Button
              variant="outline"
              color="red"
              size="sm"
              onClick={() => window.history.back()}
            >
              Annuler
            </Button>
            <Button
              variant="outline"
              color="gray"
              size="sm"
              loading={saving}
              onClick={() => handleSave(false)}
            >
              Valider
            </Button>
            <Button
              variant="outline"
              color="blue"
              size="sm"
              loading={saving}
              onClick={() => handleSave(true)}
            >
              Enregistrer et quitter
            </Button>
            <Button
              color="blue"
              size="sm"
              loading={saving}
              onClick={() => handleSave(false)}
            >
              Enregistrer
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Modales */}
      <Liste_des_visites_modal
        opened={isVisitesModalOpen}
        onClose={() => setIsVisitesModalOpen(false)}
      />

      <Liste_des_visites_dentaire_modal
        opened={isVisitesDentaireModalOpen}
        onClose={() => setIsVisitesDentaireModalOpen(false)}
      />

      <Liste_des_devis_modal
        opened={isDevisModalOpen}
        onClose={() => setIsDevisModalOpen(false)}
      />

      <Liste_des_procedures_modal
        opened={isProceduresModalOpen}
        onClose={() => setIsProceduresModalOpen(false)}
      />
    </Box>
  );
};

export default Nouvelle_mutuelle_form;
