'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { Activiteperiodique } from './Activiteperiodique';

export default function ActiviteperiodiqueDemo() {
  const mockSummary = {
    total: 0.00,
    encasement_total: 0.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Activité changée:', activity);
  };

  const handleProcedureTypeChange = (type: number) => {
    console.log('Type de procédure changé:', type);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression en cours...');
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="periodic"
            summary={mockSummary}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={handleProcedureTypeChange}
            onExport={handleExport}
            onPrint={handlePrint}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function ActiviteperiodiqueLoadingDemo() {
  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="periodic"
            loading={true}
            onQueryChange={(query) => console.log('Query:', query)}
            onActivityChange={(activity) => console.log('Activity:', activity)}
            onProcedureTypeChange={(type) => console.log('Type:', type)}
            onExport={(format) => console.log('Export:', format)}
            onPrint={() => console.log('Print')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle annuel (sans date picker)
export function ActiviteperiodiqueAnnuelleDemo() {
  const mockSummaryAnnuelle = {
    total: 125000.00,
    encasement_total: 98000.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="annual"
            summary={mockSummaryAnnuelle}
            loading={false}
            onQueryChange={(query) => console.log('Query annuelle:', query)}
            onActivityChange={(activity) => console.log('Activity annuelle:', activity)}
            onProcedureTypeChange={(type) => console.log('Type annuel:', type)}
            onExport={(format) => alert(`Export annuel ${format}`)}
            onPrint={() => alert('Impression rapport annuel')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec cycle mensuel
export function ActiviteperiodiqueMensuelleDemo() {
  const mockSummaryMensuelle = {
    total: 15000.00,
    encasement_total: 12500.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="monthly"
            summary={mockSummaryMensuelle}
            loading={false}
            onQueryChange={(query) => console.log('Query mensuelle:', query)}
            onActivityChange={(activity) => console.log('Activity mensuelle:', activity)}
            onProcedureTypeChange={(type) => console.log('Type mensuel:', type)}
            onExport={(format) => alert(`Export mensuel ${format}`)}
            onPrint={() => alert('Impression rapport mensuel')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données d'activité
export function ActiviteperiodiqueWithDataDemo() {
  const mockSummaryWithData = {
    total: 45000.00,
    encasement_total: 38000.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données périodiques chargées pour:', query);
    }, 1000);
  };

  const handleActivityChange = (activity: any) => {
    console.log('Changement d\'activité périodique:', activity);
    // Simuler le rechargement des données selon l'activité
    if (activity.type === 'encasement') {
      console.log('Chargement des données d\'encaissement périodiques...');
    } else if (activity.type === 'procedure') {
      console.log('Chargement des données de procédures périodiques...');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="periodic"
            summary={mockSummaryWithData}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={handleActivityChange}
            onProcedureTypeChange={(type) => {
              console.log('Type de procédure périodique:', type);
              const typeLabels = ['Chiffre d\'affaire', 'Nombre d\'exécution', 'Les deux'];
              alert(`Type périodique sélectionné: ${typeLabels[type]}`);
            }}
            onExport={(format) => {
              console.log(`Export ${format} périodique avec données`);
              alert(`Préparation de l'export ${format.toUpperCase()} avec les données d'activité périodique...`);
            }}
            onPrint={() => {
              console.log('Impression périodique avec données');
              alert('Préparation de l\'impression du rapport d\'activité périodique...');
            }}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec différents types d'activité
export function ActiviteperiodiqueEncaissementsDemo() {
  const mockSummaryEncaissements = {
    total: 25000.00,
    encasement_total: 25000.00,
    loaded: true
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="periodic"
            summary={mockSummaryEncaissements}
            loading={false}
            onQueryChange={(query) => console.log('Query encaissements périodiques:', query)}
            onActivityChange={(activity) => {
              console.log('Activity encaissements périodiques:', activity);
              if (activity.name === 'encasements') {
                alert('Mode encaissements périodiques activé - Les options de procédure sont masquées');
              }
            }}
            onProcedureTypeChange={(type) => console.log('Type encaissements périodiques:', type)}
            onExport={(format) => alert(`Export encaissements périodiques ${format}`)}
            onPrint={() => alert('Impression rapport encaissements périodiques')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec période personnalisée
export function ActiviteperiodiqueCustomPeriodDemo() {
  const mockSummaryCustom = {
    total: 8500.00,
    encasement_total: 7200.00,
    loaded: true
  };

  const handleQueryChange = (query: any) => {
    console.log('Query période personnalisée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    alert(`Période sélectionnée: du ${startDate} au ${endDate}`);
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <Activiteperiodique
            cycle="periodic"
            summary={mockSummaryCustom}
            loading={false}
            onQueryChange={handleQueryChange}
            onActivityChange={(activity) => console.log('Activity période personnalisée:', activity)}
            onProcedureTypeChange={(type) => console.log('Type période personnalisée:', type)}
            onExport={(format) => alert(`Export période personnalisée ${format}`)}
            onPrint={() => alert('Impression rapport période personnalisée')}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
