"use client";
import { IconArrowsLeftRight } from "@tabler/icons-react";
import { ActionIcon, Tooltip } from "@mantine/core";
import { useFontSize } from "~/contexts/FontSizeContext";
import { Menu, rem } from "@mantine/core";
const FontSizeSlider: React.FC = () => {
  const { fontSize, setFontSize, saveFontSize } = useFontSize();

  // Handle slider change (immediate visual feedback)
  const handleSliderChange = (value: string) => {
    const newSize = parseInt(value, 10);
    setFontSize(newSize);
  };

  // Handle slider release (save to backend)
  const handleSliderRelease = () => {
    saveFontSize(fontSize);
  };

  // Handle value click (immediate visual feedback and save)
  const handleValueClick = (value: number) => {
    setFontSize(value);
    saveFontSize(value);
  };
  return (
    <>
      <Menu shadow="lg" width={400} zIndex={1000010}>
        <Menu.Target>
          <Tooltip
            label="Augmenter la police"
            withArrow
            style={{color:"var(--mantine-color-text)"}}  className="bg-[var(--tooltip-bg)]"
          >
            <ActionIcon
              size="lg"
              className="h-10 navBarButtonicon"
              style={{color:"var(--mantine-color-text)"}}
            >
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  fontSize={18}
                  width="1em"
                  height="1em"
                  viewBox="0 0 512 512"
                  className=" navBarButtonicon "
                >
                  <path
                    fill="currentColor"
                    d="M32 256h192v64h-64v192H96V320H32zm448-128H354.125v384h-68.25V128H160V64h320z"
                  />
                </svg>
              </>
            </ActionIcon>
          </Tooltip>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Divider />
          <Menu.Item
            leftSection={
              <IconArrowsLeftRight
                style={{ width: rem(14), height: rem(14) }}
              />
            }
          >
            Augmenter la police
          </Menu.Item>
          <input
            step={2}
            className="range range-primary range-xs"
            type="range"
            min="10"
            max="22"
            value={fontSize}
            onChange={(e) => handleSliderChange(e.target.value)}
            onMouseUp={handleSliderRelease}
            onTouchEnd={handleSliderRelease}
            style={{ width: rem(350), height: rem(14), marginLeft: rem(16) }}
          />
          <div className="flex w-full justify-between px-2 text-xs">
            {[...Array<number>(7)].map((_, i) => (
              <span
                key={i}
                className="flex cursor-pointer flex-col items-center"
                onClick={() => handleValueClick(10 + i * 2)}
              >
                <span>|</span>
                {10 + i * 2}px
              </span>
            ))}
          </div>
        </Menu.Dropdown>
      </Menu>
      {/* <span className="-mx-3 text-[var(--bg-base-200)]">|</span> */}
    </>
  );
};

export default FontSizeSlider;
