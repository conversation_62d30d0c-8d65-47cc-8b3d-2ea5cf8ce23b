<div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon ng-class="vm.currentState.icon || 'mdi-arrow-right-drop-circle'" md-font-set="mdi" class="mdi mdi-account-cog" role="img" aria-hidden="true"></md-icon>
        </div>
        <h2 translate-once="states_insured_patients_visits">Patients/Visits assurés</h2>
        <span class="flex"></span>
        <button class="mn-header-icon md-button ng-isolate-scope md-ink-ripple" type="button" ng-transclude="" advanced-filter="vm.query" changed="vm.queryChanged()" aria-label="" md-labeled-by-tooltip="md-tooltip-1115">
            <md-icon md-font-icon="mdi-filter-multiple" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-multiple" role="img" aria-label="mdi-filter-multiple"></md-icon>
            
        </button>
    </div>
</md-toolbar>

<md-content class="mn-module-body layout-fill flex layout-column md-padding ng-scope _md">
    <div class="layout-row flex-nogrow">
        <md-input-container class="mn-datepicker-container md-auto-horizontal-margin _md-datepicker-floating-label _md-datepicker-has-calendar-icon md-input-has-value">
            <label translate-once="from_date">Du</label>
            <md-datepicker mn-date="" ng-model="vm.query.start" ng-change="vm.queryChanged()" md-max-date="vm.maxDate" class="ng-pristine ng-untouched ng-valid _md-datepicker-has-triangle-icon ng-isolate-scope ng-not-empty ng-valid-mindate ng-valid-maxdate ng-valid-filtered ng-valid-valid" tabindex="-1" aria-owns="md-date-pane-1116" type="date" aria-invalid="false" style=""><button class="md-datepicker-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" tabindex="-1" aria-hidden="true" ng-click="ctrl.openCalendarPane($event)"><md-icon class="md-datepicker-calendar-icon ng-scope" aria-label="md-calendar" md-svg-src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjExIDAtMS45OS45LTEuOTkgMkwzIDE5YzAgMS4xLjg5IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMTZINVY4aDE0djExek03IDEwaDV2NUg3eiIvPjwvc3ZnPg==" role="img"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"></path></svg></md-icon></button><div class="md-datepicker-input-container" ng-class="{'md-datepicker-focused': ctrl.isFocused}"><input class="md-datepicker-input md-input" aria-haspopup="dialog" ng-focus="ctrl.setFocused(true)" ng-blur="ctrl.setFocused(false)" autocomplete="off" id="input_1117" size="13"> <button class="md-datepicker-triangle-button md-icon-button md-button" type="button" ng-transclude="" md-no-ink="" ng-click="ctrl.openCalendarPane($event)" aria-label="Open calendar" tabindex="-1"><div class="md-datepicker-expand-triangle ng-scope"></div></button></div><div class="md-datepicker-calendar-pane md-whiteframe-z1" id="md-date-pane-1116"><div class="md-datepicker-input-mask"><div class="md-datepicker-input-mask-opaque"></div></div><div class="md-datepicker-calendar"><!-- ngIf: ctrl.isCalendarOpen --></div></div></md-datepicker><div><div class="md-errors-spacer"></div></div>
        </md-input-container>

        <md-input-container class="mn-datepicker-container md-auto-horizontal-margin _md-datepicker-floating-label _md-datepicker-has-calendar-icon md-input-has-value">
            <label translate-once="to_date">Au</label>
            <md-datepicker mn-date="" md-max-date="vm.maxDate" ng-model="vm.query.end" ng-change="vm.queryChanged()" class="ng-pristine ng-untouched _md-datepicker-has-triangle-icon ng-isolate-scope ng-not-empty ng-valid-mindate ng-valid-filtered ng-valid-valid ng-invalid ng-invalid-maxdate" tabindex="-1" aria-owns="md-date-pane-1118" type="date" aria-invalid="true" style=""><button class="md-datepicker-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" tabindex="-1" aria-hidden="true" ng-click="ctrl.openCalendarPane($event)"><md-icon class="md-datepicker-calendar-icon ng-scope" aria-label="md-calendar" md-svg-src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjExIDAtMS45OS45LTEuOTkgMkwzIDE5YzAgMS4xLjg5IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMTZINVY4aDE0djExek03IDEwaDV2NUg3eiIvPjwvc3ZnPg==" role="img"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"></path></svg></md-icon></button><div class="md-datepicker-input-container md-datepicker-invalid" ng-class="{'md-datepicker-focused': ctrl.isFocused}"><input class="md-datepicker-input md-input" aria-haspopup="dialog" ng-focus="ctrl.setFocused(true)" ng-blur="ctrl.setFocused(false)" autocomplete="off" id="input_1119" size="13"> <button class="md-datepicker-triangle-button md-icon-button md-button" type="button" ng-transclude="" md-no-ink="" ng-click="ctrl.openCalendarPane($event)" aria-label="Open calendar" tabindex="-1"><div class="md-datepicker-expand-triangle ng-scope"></div></button></div><div class="md-datepicker-calendar-pane md-whiteframe-z1" id="md-date-pane-1118"><div class="md-datepicker-input-mask"><div class="md-datepicker-input-mask-opaque"></div></div><div class="md-datepicker-calendar"><!-- ngIf: ctrl.isCalendarOpen --></div></div></md-datepicker><div><div class="md-errors-spacer"></div></div>
        </md-input-container>

        <div flex="nogrow" layout="column" class="mn-radio-group-container layout-column flex-nogrow" style="margin-left: 12px">
            <label translate-once="states_data_source">Source de données</label>
            <md-radio-group ng-model="vm.activeState" layout="row" required="" ng-change="vm.handleStatesChange()" class="ng-pristine ng-untouched ng-valid _md layout-row ng-not-empty ng-valid-required" role="radiogroup" tabindex="0" aria-required="true" aria-invalid="false" aria-activedescendant="radio_1122">
                <!-- ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="activity base" class="ng-scope md-auto-horizontal-margin md-checked" id="radio_1122" role="radio" aria-checked="true" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <spanc ng-bind="state.label|translate" class="ng-binding ng-scope">Tous les objets payables</spanc>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="activity base" class="ng-scope md-auto-horizontal-margin" id="radio_1123" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <spanc ng-bind="state.label|translate" class="ng-binding ng-scope">Seulement déclarer en mutuelles</spanc>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name --><md-radio-button ng-repeat="state in vm.states track by state.name" ng-value="state" aria-label="activity base" class="ng-scope md-auto-horizontal-margin" id="radio_1124" role="radio" aria-checked="false" value="[object Object]"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-off"></div><div class="md-on"></div></div><div ng-transclude="" class="md-label">
                    <spanc ng-bind="state.label|translate" class="ng-binding ng-scope">Nombre de patient par organisme</spanc>
                </div></md-radio-button><!-- end ngRepeat: state in vm.states track by state.name -->
            </md-radio-group>
        </div>

        <span class="flex"></span>

        <md-switch class="flex-none md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-not-empty md-checked" ng-model="vm.onlyInsured" ng-disabled="vm.activeState.name !== 'all_objects'" aria-label="handle user account" ng-change="vm.queryChanged()" tabindex="0" type="checkbox" role="checkbox" aria-checked="true" aria-disabled="false" aria-invalid="false"><div class="md-container" style="touch-action: pan-x;"><div class="md-bar"></div><div class="md-thumb-container"><div class="md-thumb md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""></div></div></div><div ng-transclude="" class="md-label">
            <span translate-once="states_only_insured" class="ng-scope">Patients assurée seulement</span>
        </div></md-switch>
    </div>

    <hr class="mn-sep flex-nogrow">

    <mn-pivot-table class="flex ng-isolate-scope wdr-ui-element" cg-busy="vm.promise" data="vm.$dataSource" report-complete="vm.onComplete(pivot)" customize-cells="vm.customizeCells(cellBuilder, cellData)" export-file-name="vm.handleFileName()" state-name="insured_report" style="position: relative;"><div class="flex layout-column layout-fill mn-pivot-container wdr-ui-element empty-data" ng-class="{'empty-data': vm.emptyData,'incomplete-data':vm.incompleteData}" id="pivotElement" style="width: 100%; height: 500px; position: relative;"><div id="wdr-toolbar-wrapper" class="wdr-toolbar-ui wdr-ui-element" style="width: 100%;"><ul id="wdr-toolbar"><div class="wdr-toolbar-group-right"><li class="wdr-divider"></li><li id="wdr-tab-export-print"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-printer"></md-icon></div><span>Imprimer</span></a></li><li id="wdr-tab-export"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-database-export"></md-icon></div><span>Exporter</span></a><div class="wdr-dropdown wdr-shadow-container"><ul class="wdr-dropdown-content"><li id="wdr-tab-export-excel"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-file-excel-outline"></md-icon></div><span>Pour Excel</span></a></li><li id="wdr-tab-export-pdf"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-file-pdf-box"></md-icon></div><span>PDF</span></a></li></ul></div></li><li id="wdr-tab-format"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-table-edit"></md-icon></div><span>Format</span></a><div class="wdr-dropdown wdr-shadow-container"><ul class="wdr-dropdown-content"><li id="wdr-tab-format-cells"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-format-letter-matches"></md-icon></div><span>Format de cellule</span></a></li><li id="wdr-tab-format-conditional"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-format-color-highlight"></md-icon></div><span>La mise en forme conditionnelle</span></a></li></ul></div></li><li id="wdr-tab-fields"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-table-settings"></md-icon></div><span>Champs</span></a></li><li id="wdr-tab-options"><a href="javascript:void(0)"><div class="wdr-svg-icon"><md-icon class="mdi mdi-cog"></md-icon></div><span>Options</span></a></li></div></ul></div><div class="wdr-ui-element wdr-ui wdr-ui-container" id="wdr-pivot-view" style="width: 1238px; height: 230px;" tabindex="0"><div class="wdr-ui-element wdr-ui wdr-ui-container" id="wdr-grid-view" style="position: absolute; display: block; height: 228.4px; width: 1236.4px;"><div class="wdr-ui-element wdr-grid-layout wdr-noselect wdr-compact-view wdr-droppable" tabindex="0" id="wdr-grid-layout" style="width: 1236.4px; height: 228.4px;"><div class="wdr-ui-element wdr-auto-calculation-bar" style="display: none;"><div class="wdr-ui-element" style="position: relative;"><div class="wdr-ui-element wdr-auto-calculation-bar-conainer" style="position: absolute;"><div class="wdr-ui-element wdr-auto-calculation-bar-content"><div class="wdr-ui-element wdr-auto-calculation-bar-content-text">Moyenne:</div><div class="wdr-ui-element wdr-auto-calculation-bar-content-results"></div></div><div class="wdr-ui-element wdr-auto-calculation-bar-content"><div class="wdr-ui-element wdr-auto-calculation-bar-content-text">Compte:</div><div class="wdr-ui-element wdr-auto-calculation-bar-content-results"></div></div><div class="wdr-ui-element wdr-auto-calculation-bar-content"><div class="wdr-ui-element wdr-auto-calculation-bar-content-text">Somme:</div><div class="wdr-ui-element wdr-auto-calculation-bar-content-results"></div></div></div></div></div><span class="wdr-ui-element wdr-ui wdr-ui-label wdr-pivot-title wdr-selectable" id="wdr-grid-title" style="position: relative; display: none;"></span><div class="wdr-ui-element" id="wdr-sheet-headers" style="top: 0px; left: 0px; width: 169px; height: 150px;"></div><div class="wdr-ui-element wdr-sheet-canvas" id="wdr-cols-sheet" style="left: 169px; top: 0px; min-width: 1067.4px; height: 228.4px;"><div class="wdr-ui-element wdr-scroll-pane" style="width: 1067.4px; height: 228.4px;"><div class="wdr-ui-element wdr-scroll-content" style="right: 0px; display: block; left: 0px;"><div class="wdr-row" data-r="0"><div style="min-width:144px;min-height:30px;" class="wdr-cell" data-c="1" data-r="0" data-n="even"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell" data-c="2" data-r="0" data-n="even"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell" data-c="3" data-r="0" data-n="even"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell" data-c="4" data-r="0" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="0" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="0" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="0" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="0" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="0" data-n="even"></div></div><div class="wdr-row" data-r="1"><div style="min-width:144px;min-height:30px;" class="wdr-cell" data-c="1" data-r="1" data-n="odd"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell" data-c="2" data-r="1" data-n="odd"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell" data-c="3" data-r="1" data-n="odd"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell" data-c="4" data-r="1" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="1" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="1" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="1" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="1" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="1" data-n="odd"></div></div><div class="wdr-row" data-r="2"><div style="min-width:144px;min-height:30px;" class="wdr-cell" data-c="1" data-r="2" data-n="even"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell" data-c="2" data-r="2" data-n="even"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell" data-c="3" data-r="2" data-n="even"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell" data-c="4" data-r="2" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="2" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="2" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="2" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="2" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="2" data-n="even"></div></div><div class="wdr-row" data-r="3"><div style="min-width:144px;min-height:30px;" class="wdr-cell" data-c="1" data-r="3" data-n="odd"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell" data-c="2" data-r="3" data-n="odd"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell" data-c="3" data-r="3" data-n="odd"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell" data-c="4" data-r="3" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="3" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="3" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="3" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="3" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="3" data-n="odd"></div></div><div class="wdr-row" data-r="4"><div style="min-width:144px;min-height:30px;" class="wdr-cell wdr-header wdr-header-c wdr-measure-header wdr-total wdr-grand-total wdr-v-sort" data-c="1" data-r="4" data-n="even">Somme de Montant dû<i class="wdr-icon wdr-sort-icon wdr-v-sort-icon" title="Cliquez pour trier Desc"></i></div><div style="min-width:161px;min-height:30px;" class="wdr-cell wdr-header wdr-header-c wdr-measure-header wdr-total wdr-grand-total wdr-v-sort" data-c="2" data-r="4" data-n="even">Somme de Montant régler<i class="wdr-icon wdr-sort-icon wdr-v-sort-icon" title="Cliquez pour trier Desc"></i></div><div style="min-width:122px;min-height:30px;" class="wdr-cell wdr-header wdr-header-c wdr-measure-header wdr-total wdr-grand-total wdr-v-sort" data-c="3" data-r="4" data-n="even">Somme de Remise<i class="wdr-icon wdr-sort-icon wdr-v-sort-icon" title="Cliquez pour trier Desc"></i></div><div style="min-width:155px;min-height:30px;" class="wdr-cell wdr-header wdr-header-c wdr-measure-header wdr-total wdr-grand-total wdr-v-sort" data-c="4" data-r="4" data-n="even">Somme de Reste à régler<i class="wdr-icon wdr-sort-icon wdr-v-sort-icon" title="Cliquez pour trier Desc"></i></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="4" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="4" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="4" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="4" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="4" data-n="even"></div></div></div><div class="wdr-ui-element wdr-scroll-placeholder" style="width: 582px; display: none;"></div></div></div><div class="wdr-ui-element wdr-sheet-canvas" id="wdr-rows-sheet" style="left: 0px; top: 150px; min-width: 1236.4px; height: 78.4px;"><div class="wdr-ui-element wdr-scroll-pane" style="width: 1236.4px; height: 78.4px;"><div class="wdr-ui-element wdr-scroll-content" style="bottom: 0px; display: block; top: 0px;"><div class="wdr-row" data-r="5"><div style="height:30px;min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-0 wdr-measure-header wdr-total wdr-grand-total wdr-h-sort" data-c="0" data-r="5" data-n="odd">Total<i class="wdr-icon wdr-sort-icon wdr-h-sort-icon" title="Cliquez pour trier Desc"></i></div></div><div class="wdr-row" data-r="6"><div style="height:30px;min-width:169px;min-height:30px;" class="wdr-cell wdr-empty" data-c="0" data-r="6" data-n="even"></div></div><div class="wdr-row" data-r="7"><div style="height:30px;min-width:169px;min-height:30px;" class="wdr-cell wdr-empty" data-c="0" data-r="7" data-n="odd"></div></div></div><div class="wdr-ui-element wdr-scroll-placeholder" style="height: 30px; display: none;"></div></div></div><div class="wdr-ui-element wdr-sheet-canvas" id="wdr-data-sheet" style="left: 168px; top: 149px; min-width: 1067.4px; height: 79.4px;"><div class="wdr-ui-element wdr-scroll-pane" style="width: 1067.4px; height: 79.4px; overflow: hidden;"><div class="wdr-ui-element wdr-scroll-content" style="inset: 0px; display: block;"><div class="wdr-row" data-r="5"><div style="min-width:144px;height:30px;" class="wdr-cell wdr-total wdr-grand-total grand-total-row" data-c="1" data-r="5" data-n="odd"> </div><div style="min-width:161px;height:30px;" class="wdr-cell wdr-total wdr-grand-total grand-total-row wdr-details" data-c="2" data-r="5" data-n="odd"> </div><div style="min-width:122px;height:30px;" class="wdr-cell wdr-total wdr-grand-total grand-total-row wdr-details" data-c="3" data-r="5" data-n="odd"> </div><div style="min-width:155px;height:30px;" class="wdr-cell wdr-total wdr-grand-total grand-total-row wdr-details" data-c="4" data-r="5" data-n="odd"> </div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="5" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="5" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="5" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="5" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="5" data-n="odd"></div></div><div class="wdr-row" data-r="6"><div style="min-width:144px;min-height:30px;" class="wdr-cell wdr-empty" data-c="1" data-r="6" data-n="even"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell wdr-empty" data-c="2" data-r="6" data-n="even"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell wdr-empty" data-c="3" data-r="6" data-n="even"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell wdr-empty" data-c="4" data-r="6" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="6" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="6" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="6" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="6" data-n="even"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="6" data-n="even"></div></div><div class="wdr-row" data-r="7"><div style="min-width:144px;min-height:30px;" class="wdr-cell wdr-empty" data-c="1" data-r="7" data-n="odd"></div><div style="min-width:161px;min-height:30px;" class="wdr-cell wdr-empty" data-c="2" data-r="7" data-n="odd"></div><div style="min-width:122px;min-height:30px;" class="wdr-cell wdr-empty" data-c="3" data-r="7" data-n="odd"></div><div style="min-width:155px;min-height:30px;" class="wdr-cell wdr-empty" data-c="4" data-r="7" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="5" data-r="7" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="6" data-r="7" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="7" data-r="7" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="8" data-r="7" data-n="odd"></div><div style="min-width:100px;min-height:30px;" class="wdr-cell wdr-empty" data-c="9" data-r="7" data-n="odd"></div></div></div><div class="wdr-ui-element wdr-scroll-placeholder" style="width: 1067.4px; height: 78.4px; display: none;"></div></div></div><div class="wdr-ui-element wdr-resize-handles" id="wdr-cols-resize" style="left: 0px; top: 0px; right: 0px; height: 0px;"><div class="wdr-ui-element wdr-indicator" style="display: none;"></div><div class="wdr-ui-element wdr-wrapper" title="Double-cliquez pour l'ajustement"><div style="left:169px;" class="wdr-handle" data-idx="0" title="Faites glisser pour redimensionner"></div><div style="left:313px;" class="wdr-handle" data-idx="1" title="Faites glisser pour redimensionner"></div><div style="left:474px;" class="wdr-handle" data-idx="2" title="Faites glisser pour redimensionner"></div><div style="left:596px;" class="wdr-handle" data-idx="3" title="Faites glisser pour redimensionner"></div><div style="left:751px;" class="wdr-handle" data-idx="4" title="Faites glisser pour redimensionner"></div></div></div><div class="wdr-ui-element wdr-resize-handles" id="wdr-rows-resize" style="left: 0px; top: 0px; bottom: 0px; width: 0px;"><div class="wdr-ui-element wdr-indicator" style="display: none;"></div><div class="wdr-ui-element wdr-wrapper" title="Double-cliquez pour l'ajustement"><div style="top:30px;" class="wdr-handle" data-idx="0" title="Faites glisser pour redimensionner"></div><div style="top:60px;" class="wdr-handle" data-idx="1" title="Faites glisser pour redimensionner"></div><div style="top:90px;" class="wdr-handle" data-idx="2" title="Faites glisser pour redimensionner"></div><div style="top:120px;" class="wdr-handle" data-idx="3" title="Faites glisser pour redimensionner"></div><div style="top:150px;" class="wdr-handle" data-idx="4" title="Faites glisser pour redimensionner"></div><div style="top:180px;" class="wdr-handle" data-idx="5" title="Faites glisser pour redimensionner"></div></div></div><div class="wdr-ui-element wdr-filters" id="wdr-cols-filter" style="display: none; top: 90px; left: 169px; right: 485.4px;"><div style="height:30px;" class="wdr-row"></div></div><div class="wdr-ui-element wdr-filters" id="wdr-rows-filter" style="left: 0px; top: 0px;"><div class="wdr-row"><div style="min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-0 wdr-filter-header wdr-ui-element wdr-draggable" title="Cliquez pour filtrer" data-c="0" data-r="0" data-h="0">Médecin<i class="wdr-icon wdr-filter-icon"></i></div></div><div class="wdr-row"><div style="min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-1 wdr-filter-header wdr-ui-element wdr-draggable" title="Cliquez pour filtrer" data-c="0" data-r="1" data-h="1">Organisme<i class="wdr-icon wdr-filter-icon"></i></div></div><div class="wdr-row"><div style="min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-2 wdr-filter-header wdr-ui-element wdr-draggable" title="Cliquez pour filtrer" data-c="0" data-r="2" data-h="2">Date de paiement<i class="wdr-icon wdr-filter-icon"></i></div></div><div class="wdr-row"><div style="min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-3 wdr-filter-header wdr-ui-element wdr-draggable" title="Cliquez pour filtrer" data-c="0" data-r="3" data-h="3">Nom du patient<i class="wdr-icon wdr-filter-icon"></i></div></div><div class="wdr-row"><div style="min-width:169px;min-height:30px;" class="wdr-cell wdr-header wdr-header-r wdr-level-4 wdr-filter-header wdr-ui-element wdr-draggable" title="Cliquez pour filtrer" data-c="0" data-r="4" data-h="4">Identifiant Unique<i class="wdr-icon wdr-filter-icon"></i></div></div></div><div class="wdr-ui-element wdr-ui-hgroup wdr-filters" id="wdr-page-filter" style="display: none;"></div></div></div><div class="wdr-ui-element wdr-ui wdr-fields-view-wrap" style="display: block;"><a class="wdr-ui-element wdr-ui wdr-ui-btn" id="wdr-btn-open-fields"></a></div><span class="wdr-ui-element wdr-ui wdr-ui-label wdr-credits"><a href="https://webdatarocks.com/?r=wdr" target="_blank">
                <span class="wdr-created">Created using</span>
                <svg height="14" viewBox="0 0 272 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M84.0225 14.1016L82.2646 14.3213L77.9434 32.8516H73.9736L70.0625 18.7744H69.9746L66.0635 32.8516H62.0938L57.7432 14.3213L56 14.1016V11.5234H63.8516V14.1016L61.9033 14.4385L64.3496 26.1279L64.4375 26.1426L68.4365 11.5234H71.5859L75.6143 26.1426L75.7021 26.1279L78.1338 14.4385L76.1855 14.1016V11.5234H84.0225V14.1016Z" fill="#303030"></path>
                <path d="M92.9039 33.1592C90.6188 33.1592 88.7926 32.4268 87.4254 30.9619C86.0582 29.4971 85.3746 27.6367 85.3746 25.3809V24.7949C85.3746 22.4414 86.0191 20.5029 87.3082 18.9795C88.607 17.4561 90.3453 16.6992 92.523 16.709C94.6617 16.709 96.3219 17.3535 97.5035 18.6426C98.6852 19.9316 99.276 21.6748 99.276 23.8721V26.2012H89.8131L89.7838 26.2891C89.8619 27.334 90.2086 28.1934 90.8238 28.8672C91.4488 29.541 92.2936 29.8779 93.358 29.8779C94.3053 29.8779 95.0914 29.7852 95.7164 29.5996C96.3414 29.4043 97.025 29.1016 97.7672 28.6914L98.9244 31.3281C98.2701 31.8457 97.4205 32.2803 96.3756 32.6318C95.3404 32.9834 94.1832 33.1592 92.9039 33.1592ZM92.523 20.0049C91.732 20.0049 91.107 20.3076 90.648 20.9131C90.1891 21.5186 89.9059 22.3145 89.7984 23.3008L89.8424 23.374H95.0865V22.9932C95.0865 22.085 94.8717 21.3623 94.442 20.8252C94.0221 20.2783 93.3824 20.0049 92.523 20.0049Z" fill="#303030"></path>
                <path d="M117.034 25.3662C117.034 27.7295 116.507 29.6191 115.452 31.0352C114.398 32.4512 112.874 33.1592 110.882 33.1592C109.954 33.1592 109.149 32.9688 108.465 32.5879C107.781 32.1973 107.205 31.6357 106.737 30.9033L106.429 32.8516H102.737V13.0176L100.467 12.5781V10H107V18.6865C107.459 18.0518 108.006 17.5635 108.641 17.2217C109.285 16.8799 110.023 16.709 110.853 16.709C112.864 16.709 114.398 17.4707 115.452 18.9941C116.507 20.5078 117.034 22.5293 117.034 25.0586V25.3662ZM112.772 25.0586C112.772 23.5156 112.542 22.2949 112.083 21.3965C111.634 20.4883 110.862 20.0342 109.769 20.0342C109.105 20.0342 108.543 20.1758 108.084 20.459C107.625 20.7324 107.264 21.1279 107 21.6455V28.3398C107.264 28.8281 107.625 29.1992 108.084 29.4531C108.553 29.707 109.124 29.834 109.798 29.834C110.902 29.834 111.673 29.4482 112.112 28.6768C112.552 27.8955 112.772 26.792 112.772 25.3662V25.0586Z" fill="#303030"></path>
                <path d="M128.919 11.5234C131.526 11.5234 133.675 12.3584 135.364 14.0283C137.054 15.6885 137.898 17.8223 137.898 20.4297V23.96C137.898 26.5771 137.054 28.7158 135.364 30.376C133.675 32.0264 131.526 32.8516 128.919 32.8516H119.324V30.2881L121.595 29.8486V14.541L119.324 14.1016V11.5234H121.595H128.919ZM125.872 14.8193V29.5703H128.699C130.232 29.5703 131.433 29.0527 132.303 28.0176C133.172 26.9824 133.606 25.6299 133.606 23.96V20.4004C133.606 18.75 133.172 17.4072 132.303 16.3721C131.433 15.3369 130.232 14.8193 128.699 14.8193H125.872Z" fill="#303030"></path>
                <path d="M150.603 32.8516C150.486 32.5879 150.383 32.3193 150.295 32.0459C150.207 31.7627 150.139 31.4795 150.09 31.1963C149.582 31.7822 148.962 32.2559 148.23 32.6172C147.507 32.9785 146.677 33.1592 145.74 33.1592C144.187 33.1592 142.952 32.7393 142.034 31.8994C141.125 31.0498 140.671 29.8975 140.671 28.4424C140.671 26.958 141.267 25.8105 142.458 25C143.65 24.1895 145.398 23.7842 147.703 23.7842H149.885V22.2314C149.885 21.4697 149.665 20.8789 149.226 20.459C148.787 20.0391 148.137 19.8291 147.278 19.8291C146.789 19.8291 146.355 19.8877 145.974 20.0049C145.593 20.1123 145.286 20.2441 145.051 20.4004L144.773 22.0996H141.55L141.565 18.584C142.346 18.0566 143.24 17.6123 144.246 17.251C145.261 16.8896 146.365 16.709 147.556 16.709C149.519 16.709 151.111 17.1924 152.331 18.1592C153.562 19.1162 154.177 20.4834 154.177 22.2607V28.8086C154.177 29.0527 154.177 29.2822 154.177 29.4971C154.187 29.7119 154.206 29.917 154.236 30.1123L155.539 30.2881V32.8516H150.603ZM146.897 29.9951C147.541 29.9951 148.127 29.8584 148.655 29.585C149.182 29.3018 149.592 28.9502 149.885 28.5303V26.1133H147.703C146.794 26.1133 146.106 26.3281 145.637 26.7578C145.168 27.1777 144.934 27.6855 144.934 28.2812C144.934 28.8184 145.105 29.2383 145.447 29.541C145.798 29.8438 146.282 29.9951 146.897 29.9951Z" fill="#303030"></path>
                <path d="M162.546 13.1201V17.002H165.329V20.0049H162.546V28.0762C162.546 28.6914 162.673 29.1309 162.927 29.3945C163.181 29.6582 163.522 29.79 163.952 29.79C164.245 29.79 164.499 29.7803 164.714 29.7607C164.938 29.7314 165.188 29.6875 165.461 29.6289L165.827 32.7197C165.349 32.8662 164.875 32.9736 164.406 33.042C163.938 33.1201 163.43 33.1592 162.883 33.1592C161.408 33.1592 160.271 32.7539 159.47 31.9434C158.679 31.1328 158.283 29.8486 158.283 28.0908V20.0049H155.954V17.002H158.283V13.1201H162.546Z" fill="#303030"></path>
                <path d="M177.917 32.8516C177.799 32.5879 177.697 32.3193 177.609 32.0459C177.521 31.7627 177.453 31.4795 177.404 31.1963C176.896 31.7822 176.276 32.2559 175.544 32.6172C174.821 32.9785 173.991 33.1592 173.053 33.1592C171.501 33.1592 170.265 32.7393 169.347 31.8994C168.439 31.0498 167.985 29.8975 167.985 28.4424C167.985 26.958 168.581 25.8105 169.772 25C170.963 24.1895 172.712 23.7842 175.016 23.7842H177.199V22.2314C177.199 21.4697 176.979 20.8789 176.54 20.459C176.1 20.0391 175.451 19.8291 174.591 19.8291C174.103 19.8291 173.669 19.8877 173.288 20.0049C172.907 20.1123 172.599 20.2441 172.365 20.4004L172.087 22.0996H168.864L168.879 18.584C169.66 18.0566 170.553 17.6123 171.559 17.251C172.575 16.8896 173.678 16.709 174.87 16.709C176.833 16.709 178.424 17.1924 179.645 18.1592C180.876 19.1162 181.491 20.4834 181.491 22.2607V28.8086C181.491 29.0527 181.491 29.2822 181.491 29.4971C181.501 29.7119 181.52 29.917 181.549 30.1123L182.853 30.2881V32.8516H177.917ZM174.211 29.9951C174.855 29.9951 175.441 29.8584 175.968 29.585C176.496 29.3018 176.906 28.9502 177.199 28.5303V26.1133H175.016C174.108 26.1133 173.42 26.3281 172.951 26.7578C172.482 27.1777 172.248 27.6855 172.248 28.2812C172.248 28.8184 172.419 29.2383 172.76 29.541C173.112 29.8438 173.595 29.9951 174.211 29.9951Z" fill="#303030"></path>
                <path d="M194.196 11.5234C196.617 11.5234 198.517 12.0801 199.894 13.1934C201.271 14.2969 201.959 15.8252 201.959 17.7783C201.959 18.8525 201.671 19.7852 201.095 20.5762C200.519 21.3672 199.674 22.0068 198.561 22.4951C199.821 22.8662 200.724 23.4863 201.271 24.3555C201.827 25.2148 202.106 26.2842 202.106 27.5635V28.6475C202.106 29.1162 202.198 29.4824 202.384 29.7461C202.57 30 202.877 30.1514 203.307 30.2002L203.834 30.2734V32.8516H201.622C200.226 32.8516 199.244 32.4756 198.678 31.7236C198.112 30.9717 197.828 30 197.828 28.8086V27.5928C197.828 26.5576 197.55 25.7471 196.993 25.1611C196.446 24.5654 195.67 24.2529 194.664 24.2236H191.163V29.8486L193.448 30.2881V32.8516H184.615V30.2881L186.886 29.8486V14.541L184.615 14.1016V11.5234H186.886H194.196ZM191.163 20.9277H194.122C195.314 20.9277 196.207 20.6738 196.803 20.166C197.399 19.6582 197.696 18.9258 197.696 17.9688C197.696 17.0117 197.399 16.25 196.803 15.6836C196.217 15.1074 195.348 14.8193 194.196 14.8193H191.163V20.9277Z" fill="#303030"></path>
                <path d="M205.743 24.7803C205.743 22.417 206.402 20.4834 207.721 18.9795C209.049 17.4658 210.88 16.709 213.214 16.709C215.557 16.709 217.388 17.4609 218.707 18.9648C220.035 20.4688 220.699 22.4072 220.699 24.7803V25.0879C220.699 27.4707 220.035 29.4141 218.707 30.918C217.388 32.4121 215.567 33.1592 213.243 33.1592C210.889 33.1592 209.049 32.4121 207.721 30.918C206.402 29.4141 205.743 27.4707 205.743 25.0879V24.7803ZM210.02 25.0879C210.02 26.5332 210.274 27.6953 210.782 28.5742C211.29 29.4434 212.11 29.8779 213.243 29.8779C214.346 29.8779 215.152 29.4385 215.66 28.5596C216.178 27.6807 216.436 26.5234 216.436 25.0879V24.7803C216.436 23.374 216.178 22.2266 215.66 21.3379C215.142 20.4492 214.327 20.0049 213.214 20.0049C212.1 20.0049 211.29 20.4492 210.782 21.3379C210.274 22.2266 210.02 23.374 210.02 24.7803V25.0879Z" fill="#303030"></path>
                <path d="M230.401 29.8779C231.163 29.8779 231.773 29.6582 232.232 29.2188C232.691 28.7695 232.92 28.1738 232.92 27.4316H236.788L236.831 27.5195C236.871 29.1211 236.27 30.4639 235.03 31.5479C233.789 32.6221 232.246 33.1592 230.401 33.1592C228.038 33.1592 226.216 32.4121 224.937 30.918C223.658 29.4238 223.018 27.5049 223.018 25.1611V24.7217C223.018 22.3877 223.672 20.4688 224.981 18.9648C226.299 17.4609 228.174 16.709 230.606 16.709C231.885 16.709 233.033 16.8994 234.048 17.2803C235.064 17.6611 235.909 18.1982 236.582 18.8916L236.641 23.125H233.169L232.466 20.6494C232.251 20.4639 231.988 20.3125 231.675 20.1953C231.363 20.0684 231.006 20.0049 230.606 20.0049C229.395 20.0049 228.54 20.4443 228.042 21.3232C227.544 22.2021 227.295 23.335 227.295 24.7217V25.1611C227.295 26.5771 227.525 27.7197 227.984 28.5889C228.443 29.4482 229.248 29.8779 230.401 29.8779Z" fill="#303030"></path>
                <path d="M238.726 12.5781V10H245.273V23.2422H246.226L248.687 19.7705L247.295 19.5801V17.002H255.088V19.5801L253.213 20.0049L250.356 23.9307L254.355 29.9512L255.981 30.2881V32.8516H248.657V30.2881L249.536 30.1416L247.104 26.2158H245.273V29.8486L247.119 30.2881V32.8516H238.945V30.2881L241.011 29.8486V13.0176L238.726 12.5781Z" fill="#303030"></path>
                <path d="M270.825 22.4512H267.954L267.5 20.4736C267.207 20.2393 266.836 20.0439 266.386 19.8877C265.947 19.7314 265.459 19.6533 264.921 19.6533C264.17 19.6533 263.574 19.8242 263.134 20.166C262.695 20.498 262.475 20.918 262.475 21.4258C262.475 21.9043 262.685 22.3047 263.105 22.627C263.525 22.9395 264.365 23.2178 265.625 23.4619C267.587 23.8525 269.043 24.4287 269.99 25.1904C270.937 25.9424 271.411 26.9824 271.411 28.3105C271.411 29.7363 270.796 30.9033 269.565 31.8115C268.344 32.71 266.733 33.1592 264.731 33.1592C263.51 33.1592 262.387 32.9834 261.362 32.6318C260.346 32.2705 259.443 31.7578 258.652 31.0938L258.608 27.5342H261.596L262.182 29.5996C262.436 29.8145 262.783 29.9756 263.222 30.083C263.662 30.1807 264.121 30.2295 264.599 30.2295C265.468 30.2295 266.128 30.0732 266.577 29.7607C267.036 29.4482 267.265 29.0234 267.265 28.4863C267.265 28.0176 267.036 27.6123 266.577 27.2705C266.118 26.9287 265.273 26.626 264.043 26.3623C262.177 25.9814 260.766 25.4199 259.809 24.6777C258.862 23.9258 258.388 22.9053 258.388 21.6162C258.388 20.2881 258.935 19.1455 260.029 18.1885C261.123 17.2217 262.68 16.7383 264.702 16.7383C265.932 16.7383 267.094 16.9043 268.188 17.2363C269.292 17.5684 270.156 17.9932 270.781 18.5107L270.825 22.4512Z" fill="#303030"></path>
                <g clip-path="url(#clip0)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M22.4 44H22C19.2 44 16.3 43.5 13.7 42.4C11.2 41.4 8.8 39.8 6.6 37.7C4.6 35.7 3 33.3 1.8 30.6C0.6 27.8 0 24.9 0 22C0.1 16 2.4 10.4 6.6 6.3C10.7 2.2 16.3 -0.0999995 22 4.87547e-07H42.6C42.8 4.87547e-07 43.2 4.7707e-07 43.6 0.4L43.9 0.7V0.9C44 1 44 1.2 44 1.4V22C43.9 28 41.6 33.6 37.4 37.7C33.4 41.7 27.9 44 22.4 44ZM21.7 2C16.6 2 11.6 4.1 8 7.7C4.2 11.5 2.1 16.6 2 22C2 24.6 2.6 27.3 3.6 29.8C4.7 32.2 6.2 34.5 8 36.3C10 38.2 12.2 39.7 14.5 40.6C16.8 41.5 19.4 42 22 42C27.2 42.1 32.3 40 36 36.3C39.8 32.5 41.9 27.4 42 22V2H22H21.7ZM22 26.5C19.5 26.5 17.5 24.5 17.5 22C17.5 19.5 19.5 17.5 22 17.5C24.5 17.5 26.5 19.5 26.5 22C26.5 24.5 24.5 26.5 22 26.5ZM22 19.5C20.6 19.5 19.5 20.6 19.5 22C19.5 23.4 20.6 24.5 22 24.5C23.4 24.5 24.5 23.4 24.5 22C24.5 20.6 23.4 19.5 22 19.5ZM27.6 15.5V10.3V8.00001H29.9H35.1V10.3H31.4C34.8 13.1 37 17.3 37 22C37 30.3 30.3 37 22 37C13.7 37 7 30.3 7 22C7 17.7 8.8 13.8 11.7 11.1L13.1 12.5C10.6 14.8 9 18.2 9 22C9 29.2 14.8 35 22 35C29.2 35 35 29.2 35 22C35 17.8 33 14.1 29.9 11.7V15.5H27.6Z" fill="url(#paint0_linear)"></path>
                </g>
                <defs>
                <linearGradient id="paint0_linear" x1="22" y1="1.33773e-05" x2="22" y2="44" gradientUnits="userSpaceOnUse">
                <stop offset="0.0187499" stop-color="#06B6E1"></stop>
                <stop offset="1" stop-color="#057FDD"></stop>
                </linearGradient>
                <clipPath id="clip0">
                <rect width="44" height="44" fill="white"></rect>
                </clipPath>
                </defs>
                </svg>            
                </a></span><div class="wdr-ui-element" style="position: fixed; left: 0px; top: 0px; opacity: 0; width: 0px; height: 0px; display: none;"><textarea class="wdr-ui-element wdr-ui wdr-ui-text-area" style="width: 1px; height: 1px; padding: 0px;"></textarea></div><div class="wdr-ui-element wdr-ui wdr-ui-modal-overlay wdr-overlay-preloader" style="display: none;"></div><div class="wdr-ui-element wdr-ui wdr-ui-container wdr-ui-window wdr-ui-popup" id="wdr-preloader-view" style="display: none;"><div class="wdr-ui-element wdr-ui" id="wdr-spinner"></div><span class="wdr-ui-element wdr-ui wdr-ui-label" id="wdr-message-label"></span><span class="wdr-ui-element wdr-ui wdr-ui-label" id="wdr-details-label"></span></div></div></div><div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div><div class="wdr-resize-triggers"><div class="wdr-expand-trigger"><div style="width: 1257px; height: 304px;"></div></div><div class="wdr-contract-trigger"></div></div></mn-pivot-table>

</md-content></div>