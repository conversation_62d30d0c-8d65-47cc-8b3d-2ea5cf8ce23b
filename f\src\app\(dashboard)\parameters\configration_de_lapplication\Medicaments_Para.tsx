'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  ActionIcon,
  Modal,
  Text,
  Tabs,
  Container,
  Stack,
  ScrollArea,
  Pagination,
  Textarea,
  Checkbox,
  Badge,
  Tooltip,
  NumberInput,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconTrash,
  IconStar,
  IconStarFilled,
  IconCircle,
  IconCircleFilled,
  IconMedicalCross,
  IconX,
  IconPill,
} from '@tabler/icons-react';

// Types pour les données
interface Medication {
  id: number;
  nomCommercial: string;
  formeGalenique: string;
  unite: string;
  categorie: string;
  laboratoire: string;
  classeTherapeutique: string;
  substanceActive: string;
  isFavorite: boolean;
  isActive: boolean;
  presentations?: Presentation[];
}

interface ParamedicalProduct {
  id: number;
  nomProduit: string;
  formeGalenique: string;
  unite: string;
  categorie: string;
  laboratoire: string;
  isFavorite: boolean;
  isActive: boolean;
  presentations?: Presentation[];
}

interface Presentation {
  id: number;
  presentation: string;
  ppv: number;
}

const Medicaments_Para = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string | null>('pratisoft');

  // États pour les modals
  const [medicationModalOpened, { open: openMedicationModal, close: closeMedicationModal }] = useDisclosure(false);
  const [productModalOpened, { open: openProductModal, close: closeProductModal }] = useDisclosure(false);
  const [posologyModalOpened, { open: openPosologyModal, close: closePosologyModal }] = useDisclosure(false);

  // États pour l'édition
  const [editingMedication, setEditingMedication] = useState<Medication | null>(null);
  const [editingProduct, setEditingProduct] = useState<ParamedicalProduct | null>(null);

  // États pour la recherche et pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Données mockées pour les médicaments
  const [medications, setMedications] = useState<Medication[]>([
    {
      id: 1,
      nomCommercial: 'A-gram 250 mg',
      formeGalenique: 'sachet',
      unite: 'sachet',
      categorie: '',
      laboratoire: 'MAPHAR',
      classeTherapeutique: 'antibiotique, pénicilline à large spectre',
      substanceActive: 'AMOXICILLINE',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 2,
      nomCommercial: 'A-gram 500 mg',
      formeGalenique: 'gélule',
      unite: 'gélule',
      categorie: '',
      laboratoire: 'MAPHAR',
      classeTherapeutique: 'antibiotique, pénicilline à large spectre',
      substanceActive: 'AMOXICILLINE',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 3,
      nomCommercial: 'Agithine 200 mg',
      formeGalenique: 'cuillère',
      unite: 'cuillère',
      categorie: '',
      laboratoire: 'MAPHAR',
      classeTherapeutique: 'ANTIBIOTIQUE DE LA FAMILLE',
      substanceActive: 'ERYTHROMYCINE',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 4,
      nomCommercial: 'Abilify 10 mg',
      formeGalenique: 'comprimé',
      unite: 'comprimé',
      categorie: '',
      laboratoire: 'MAPHAR',
      classeTherapeutique: 'ANTIPSYCHOTIQUE',
      substanceActive: 'ARIPIPRAZOLE',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 5,
      nomCommercial: 'Abstral 100 μg',
      formeGalenique: 'comprimé',
      unite: 'comprimé',
      categorie: '',
      laboratoire: 'SOTHEMA',
      classeTherapeutique: 'analgiques opioïdes',
      substanceActive: 'FENTANYL',
      isFavorite: false,
      isActive: true,
    },
  ]);

  // Données mockées pour les produits paramédicaux
  const [paramedicalProducts, setParamedicalProducts] = useState<ParamedicalProduct[]>([
    {
      id: 1,
      nomProduit: '113.3variknal gel 75ml',
      formeGalenique: '',
      unite: '',
      categorie: 'Para - autres',
      laboratoire: '',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 2,
      nomProduit: '32.27halita gratte-langue',
      formeGalenique: '',
      unite: '',
      categorie: 'Para - autres',
      laboratoire: '',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 3,
      nomProduit: '8882 crème régénératrice spéciale 40 ml',
      formeGalenique: '',
      unite: '',
      categorie: 'Para - autres',
      laboratoire: '',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 4,
      nomProduit: '8882 crème teinte opale spf 50+',
      formeGalenique: '',
      unite: '',
      categorie: 'Para - autres',
      laboratoire: '',
      isFavorite: false,
      isActive: true,
    },
    {
      id: 5,
      nomProduit: '8882 lait après soleil apaisant',
      formeGalenique: '',
      unite: '',
      categorie: 'Para - autres',
      laboratoire: '',
      isFavorite: false,
      isActive: true,
    },
  ]);

  // Filtrage et pagination pour médicaments
  const filteredMedications = medications.filter((medication) =>
    medication.nomCommercial.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filtrage et pagination pour produits paramédicaux
  const filteredProducts = paramedicalProducts.filter((product) =>
    product.nomProduit.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(
    (activeTab === 'pratisoft' ? filteredMedications.length : 
     activeTab === 'personnel' ? 0 : 
     filteredProducts.length) / itemsPerPage
  );

  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = activeTab === 'pratisoft' 
    ? filteredMedications.slice(startIndex, startIndex + itemsPerPage)
    : activeTab === 'personnel' 
    ? []
    : filteredProducts.slice(startIndex, startIndex + itemsPerPage);

  const toggleFavorite = (id: number, type: 'medication' | 'product') => {
    if (type === 'medication') {
      setMedications(medications.map(med => 
        med.id === id ? { ...med, isFavorite: !med.isFavorite } : med
      ));
    } else {
      setParamedicalProducts(paramedicalProducts.map(prod => 
        prod.id === id ? { ...prod, isFavorite: !prod.isFavorite } : prod
      ));
    }
  };

  const toggleActive = (id: number, type: 'medication' | 'product') => {
    if (type === 'medication') {
      setMedications(medications.map(med => 
        med.id === id ? { ...med, isActive: !med.isActive } : med
      ));
    } else {
      setParamedicalProducts(paramedicalProducts.map(prod => 
        prod.id === id ? { ...prod, isActive: !prod.isActive } : prod
      ));
    }
  };

  const handleEdit = (item: Medication | ParamedicalProduct, type: 'medication' | 'product') => {
    if (type === 'medication') {
      setEditingMedication(item as Medication);
      openMedicationModal();
    } else {
      setEditingProduct(item as ParamedicalProduct);
      openProductModal();
    }
  };

  const handleDelete = (id: number, type: 'medication' | 'product') => {
    if (type === 'medication') {
      setMedications(medications.filter(med => med.id !== id));
      notifications.show({
        title: 'Médicament supprimé',
        message: 'Le médicament a été supprimé avec succès',
        color: 'green',
      });
    } else {
      setParamedicalProducts(paramedicalProducts.filter(prod => prod.id !== id));
      notifications.show({
        title: 'Produit supprimé',
        message: 'Le produit a été supprimé avec succès',
        color: 'green',
      });
    }
  };

  return (
    <Container size="xl" className="py-6">
      <Paper shadow="sm" radius="md" p="xl" className="bg-white">
        {/* En-tête */}
        <Group justify="space-between" mb="xl">
          <Group>
            <IconPill size={24} className="text-blue-600" />
            <Title order={2} className="text-gray-800">Médicaments & Para</Title>
          </Group>
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={openMedicationModal}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Ajouter Médicament
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={openProductModal}
              className="bg-green-600 hover:bg-green-700"
            >
              Ajouter Produit Para
            </Button>
          </Group>
        </Group>

        {/* Onglets principaux */}
        <Tabs value={activeTab} onChange={setActiveTab} className="w-full">
          <Tabs.List className="mb-6">
            <Tabs.Tab value="pratisoft" leftSection={<IconMedicalCross size={16} />}>
              Base de donnée Pratisoft
            </Tabs.Tab>
            <Tabs.Tab value="personnel" leftSection={<IconMedicalCross size={16} />}>
              Base de donnée Personnel
            </Tabs.Tab>
            <Tabs.Tab value="paramedical" leftSection={<IconMedicalCross size={16} />}>
              Paramédicaux & Parapharmaceutiques
            </Tabs.Tab>
          </Tabs.List>

          {/* Onglet Base de donnée Pratisoft */}
          <Tabs.Panel value="pratisoft">
            <div className="space-y-6">
              {/* Barre de recherche */}
              <Group justify="space-between" mb="md">
                <TextInput
                  placeholder="Rechercher"
                  leftSection={<IconSearch size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.currentTarget.value)}
                  className="w-96"
                />
              </Group>

              {/* Table des médicaments */}
              <ScrollArea>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Nom commercial</Table.Th>
                      <Table.Th>Forme galénique - Unité</Table.Th>
                      <Table.Th>Catégorie</Table.Th>
                      <Table.Th>Laboratoire</Table.Th>
                      <Table.Th>Classe thérapeutique</Table.Th>
                      <Table.Th>Substance active</Table.Th>
                      <Table.Th width={50}></Table.Th>
                      <Table.Th width={50}></Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {currentItems.map((medication: any) => (
                      <Table.Tr key={medication.id}>
                        <Table.Td>
                          <Text fw={500} c="blue">
                            {medication.nomCommercial}
                          </Text>
                        </Table.Td>
                        <Table.Td>{medication.formeGalenique}</Table.Td>
                        <Table.Td>{medication.categorie}</Table.Td>
                        <Table.Td>{medication.laboratoire}</Table.Td>
                        <Table.Td>
                          <Text c="blue" style={{ cursor: 'pointer' }}>
                            {medication.classeTherapeutique}
                          </Text>
                        </Table.Td>
                        <Table.Td>{medication.substanceActive}</Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <Tooltip label="Favoris">
                              <ActionIcon
                                variant="subtle"
                                color={medication.isFavorite ? "yellow" : "gray"}
                                onClick={() => toggleFavorite(medication.id, 'medication')}
                              >
                                {medication.isFavorite ? <IconStarFilled size={14} /> : <IconStar size={14} />}
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Actif/Inactif">
                              <ActionIcon
                                variant="subtle"
                                color={medication.isActive ? "green" : "red"}
                                onClick={() => toggleActive(medication.id, 'medication')}
                              >
                                {medication.isActive ? <IconCircleFilled size={14} /> : <IconCircle size={14} />}
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                onClick={() => handleEdit(medication, 'medication')}
                              >
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Copier">
                              <ActionIcon
                                variant="subtle"
                                color="gray"
                              >
                                <IconX size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Supprimer">
                              <ActionIcon
                                variant="subtle"
                                color="red"
                                onClick={() => handleDelete(medication.id, 'medication')}
                              >
                                <IconTrash size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm" c="dimmed">
                    Page {currentPage} de {totalPages}
                  </Text>
                  <Text size="sm" c="dimmed">
                    Lignes par Page: {itemsPerPage}
                  </Text>
                  <Text size="sm" c="dimmed">
                    {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredMedications.length)} de {filteredMedications.length}
                  </Text>
                </Group>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </div>
          </Tabs.Panel>

          {/* Onglet Base de donnée Personnel */}
          <Tabs.Panel value="personnel">
            <div className="space-y-6">
              {/* Barre de recherche */}
              <Group justify="space-between" mb="md">
                <TextInput
                  placeholder="Rechercher"
                  leftSection={<IconSearch size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.currentTarget.value)}
                  className="w-96"
                />
              </Group>

              {/* Table vide avec message */}
              <ScrollArea>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Nom commercial</Table.Th>
                      <Table.Th>Forme galénique - Unité</Table.Th>
                      <Table.Th>Catégorie</Table.Th>
                      <Table.Th>Laboratoire</Table.Th>
                      <Table.Th>Classe thérapeutique</Table.Th>
                      <Table.Th>Substance active</Table.Th>
                      <Table.Th width={50}></Table.Th>
                      <Table.Th width={50}></Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    <Table.Tr>
                      <Table.Td colSpan={8} className="text-center py-8">
                        <Text c="dimmed">Aucun élément trouvé.</Text>
                      </Table.Td>
                    </Table.Tr>
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm" c="dimmed">
                    Page 1 de 0
                  </Text>
                  <Text size="sm" c="dimmed">
                    Lignes par Page: {itemsPerPage}
                  </Text>
                  <Text size="sm" c="dimmed">
                    0 - 0 de 0
                  </Text>
                </Group>
                <Pagination
                  value={1}
                  onChange={() => {}}
                  total={0}
                  size="sm"
                />
              </Group>
            </div>
          </Tabs.Panel>

          {/* Onglet Paramédicaux & Parapharmaceutiques */}
          <Tabs.Panel value="paramedical">
            <div className="space-y-6">
              {/* Barre de recherche */}
              <Group justify="space-between" mb="md">
                <TextInput
                  placeholder="Rechercher"
                  leftSection={<IconSearch size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.currentTarget.value)}
                  className="w-96"
                />
              </Group>

              {/* Table des produits paramédicaux */}
              <ScrollArea>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Nom commercial</Table.Th>
                      <Table.Th>Forme galénique - Unité</Table.Th>
                      <Table.Th>Catégorie</Table.Th>
                      <Table.Th>Laboratoire</Table.Th>
                      <Table.Th width={50}></Table.Th>
                      <Table.Th width={50}></Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {(activeTab === 'paramedical' ? filteredProducts.slice(startIndex, startIndex + itemsPerPage) : []).map((product: any) => (
                      <Table.Tr key={product.id}>
                        <Table.Td>
                          <Text fw={500} c="blue">
                            {product.nomProduit}
                          </Text>
                        </Table.Td>
                        <Table.Td>{product.formeGalenique}</Table.Td>
                        <Table.Td>{product.categorie}</Table.Td>
                        <Table.Td>{product.laboratoire}</Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <Tooltip label="Favoris">
                              <ActionIcon
                                variant="subtle"
                                color={product.isFavorite ? "yellow" : "gray"}
                                onClick={() => toggleFavorite(product.id, 'product')}
                              >
                                {product.isFavorite ? <IconStarFilled size={14} /> : <IconStar size={14} />}
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Actif/Inactif">
                              <ActionIcon
                                variant="subtle"
                                color={product.isActive ? "green" : "red"}
                                onClick={() => toggleActive(product.id, 'product')}
                              >
                                {product.isActive ? <IconCircleFilled size={14} /> : <IconCircle size={14} />}
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Modifier">
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                onClick={() => handleEdit(product, 'product')}
                              >
                                <IconEdit size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Copier">
                              <ActionIcon
                                variant="subtle"
                                color="gray"
                              >
                                <IconX size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Supprimer">
                              <ActionIcon
                                variant="subtle"
                                color="red"
                                onClick={() => handleDelete(product.id, 'product')}
                              >
                                <IconTrash size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm" c="dimmed">
                    Page {currentPage} de {Math.ceil(filteredProducts.length / itemsPerPage)}
                  </Text>
                  <Text size="sm" c="dimmed">
                    Lignes par Page: {itemsPerPage}
                  </Text>
                  <Text size="sm" c="dimmed">
                    {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredProducts.length)} de {filteredProducts.length}
                  </Text>
                </Group>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={Math.ceil(filteredProducts.length / itemsPerPage)}
                  size="sm"
                />
              </Group>
            </div>
          </Tabs.Panel>
        </Tabs>

        {/* Modal pour les médicaments */}
        <Modal
          opened={medicationModalOpened}
          onClose={() => {
            closeMedicationModal();
            setEditingMedication(null);
          }}
          title={
            <Group>
              <IconMedicalCross size={20} />
              <Text fw={600} c="white">
                Ajouter médicament
              </Text>
            </Group>
          }
          size="xl"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <MedicationForm
            medication={editingMedication}
            onSubmit={(data) => {
              if (editingMedication) {
                setMedications(medications.map(m => m.id === editingMedication.id ? { ...editingMedication, ...data } : m));
                notifications.show({
                  title: 'Médicament modifié',
                  message: 'Le médicament a été modifié avec succès',
                  color: 'green',
                });
              } else {
                const newMedication: Medication = {
                  id: Math.max(...medications.map(m => m.id)) + 1,
                  nomCommercial: data.nomCommercial || '',
                  formeGalenique: data.formeGalenique || '',
                  unite: data.unite || '',
                  categorie: data.categorie || '',
                  laboratoire: data.laboratoire || '',
                  classeTherapeutique: data.classeTherapeutique || '',
                  substanceActive: data.substanceActive || '',
                  isFavorite: false,
                  isActive: true,
                  presentations: data.presentations || [],
                };
                setMedications([...medications, newMedication]);
                notifications.show({
                  title: 'Médicament créé',
                  message: 'Le médicament a été créé avec succès',
                  color: 'green',
                });
              }
              closeMedicationModal();
              setEditingMedication(null);
            }}
            onCancel={() => {
              closeMedicationModal();
              setEditingMedication(null);
            }}
          />
        </Modal>

        {/* Modal pour les produits paramédicaux */}
        <Modal
          opened={productModalOpened}
          onClose={() => {
            closeProductModal();
            setEditingProduct(null);
          }}
          title={
            <Group>
              <IconMedicalCross size={20} />
              <Text fw={600} c="white">
                Ajouter produit paramédical ou Parapharmaceutique
              </Text>
            </Group>
          }
          size="xl"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <ProductForm
            product={editingProduct}
            onSubmit={(data) => {
              if (editingProduct) {
                setParamedicalProducts(paramedicalProducts.map(p => p.id === editingProduct.id ? { ...editingProduct, ...data } : p));
                notifications.show({
                  title: 'Produit modifié',
                  message: 'Le produit a été modifié avec succès',
                  color: 'green',
                });
              } else {
                const newProduct: ParamedicalProduct = {
                  id: Math.max(...paramedicalProducts.map(p => p.id)) + 1,
                  nomProduit: data.nomProduit || '',
                  formeGalenique: data.formeGalenique || '',
                  unite: data.unite || '',
                  categorie: data.categorie || '',
                  laboratoire: data.laboratoire || '',
                  isFavorite: false,
                  isActive: true,
                  presentations: data.presentations || [],
                };
                setParamedicalProducts([...paramedicalProducts, newProduct]);
                notifications.show({
                  title: 'Produit créé',
                  message: 'Le produit a été créé avec succès',
                  color: 'green',
                });
              }
              closeProductModal();
              setEditingProduct(null);
            }}
            onCancel={() => {
              closeProductModal();
              setEditingProduct(null);
            }}
          />
        </Modal>

        {/* Modal pour les posologies */}
        <Modal
          opened={posologyModalOpened}
          onClose={closePosologyModal}
          title={
            <Group>
              <IconMedicalCross size={20} />
              <Text fw={600} c="white">
                Vos posologies prédéfinies
              </Text>
            </Group>
          }
          size="lg"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <div className="p-4">
            <div className="bg-yellow-100 border border-yellow-300 rounded-md p-3 mb-4">
              <Text size="sm" c="orange">
                ⚠️ Aucun élément trouvé.
              </Text>
            </div>
            <Group justify="flex-end">
              <Button
                leftSection={<IconPlus size={16} />}
                className="bg-green-600 hover:bg-green-700"
              >
                ➤
              </Button>
            </Group>
          </div>
        </Modal>
      </Paper>
    </Container>
  );
};

// Composant de formulaire pour les médicaments
interface MedicationFormProps {
  medication: Medication | null;
  onSubmit: (data: Partial<Medication>) => void;
  onCancel: () => void;
}

const MedicationForm: React.FC<MedicationFormProps> = ({ medication, onSubmit, onCancel }) => {
  const [nomCommercial, setNomCommercial] = useState(medication?.nomCommercial || '');
  const [formeGalenique, setFormeGalenique] = useState(medication?.formeGalenique || '');
  const [formeGaleniquePresentation, setFormeGaleniquePresentation] = useState('');
  const [classeTherapeutique, setClasseTherapeutique] = useState(medication?.classeTherapeutique || '');
  const [laboratoire, setLaboratoire] = useState(medication?.laboratoire || '');
  const [categorie, setCategorie] = useState(medication?.categorie || '');
  const [substanceActive, setSubstanceActive] = useState(medication?.substanceActive || '');
  const [indication, setIndication] = useState('');
  const [posologieDefaut, setPosologieDefaut] = useState('');
  const [remboursable, setRemboursable] = useState(medication ? true : false);
  const [generique, setGenerique] = useState(medication ? true : false);
  const [supplementAlimentaire, setSupplementAlimentaire] = useState(false);
  const [presentations, setPresentations] = useState<Presentation[]>(medication?.presentations || [
    { id: 1, presentation: '1 FLACON 60 ML', ppv: 32.70 }
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!nomCommercial.trim()) return;

    onSubmit({
      nomCommercial: nomCommercial.trim(),
      formeGalenique: formeGalenique.trim(),
      classeTherapeutique: classeTherapeutique.trim(),
      laboratoire: laboratoire.trim(),
      categorie: categorie.trim(),
      substanceActive: substanceActive.trim(),
      presentations,
    });
  };

  const addPresentation = () => {
    const newPresentation: Presentation = {
      id: Math.max(...presentations.map(p => p.id), 0) + 1,
      presentation: '',
      ppv: 0,
    };
    setPresentations([...presentations, newPresentation]);
  };

  const removePresentation = (id: number) => {
    setPresentations(presentations.filter(p => p.id !== id));
  };

  const updatePresentation = (id: number, field: keyof Presentation, value: string | number) => {
    setPresentations(presentations.map(p =>
      p.id === id ? { ...p, [field]: value } : p
    ));
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        {/* Première ligne */}
        <Group grow>
          <div>
            <Text size="sm" fw={500} mb="xs">
              <span style={{ color: 'red' }}>Nom commercial</span>
            </Text>
            <TextInput
              placeholder={medication ? medication.nomCommercial : ""}
              value={nomCommercial}
              onChange={(e) => setNomCommercial(e.currentTarget.value)}
              required
              styles={{
                input: {
                  borderBottom: '2px solid red',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderRadius: 0,
                  backgroundColor: 'transparent',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Forme galénique - Unité
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={formeGalenique}
              onChange={(e) => setFormeGalenique(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Forme galénique - Présentation
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={formeGaleniquePresentation}
              onChange={(e) => setFormeGaleniquePresentation(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>
        </Group>

        {/* Deuxième ligne */}
        <Group grow>
          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Classe thérapeutique
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={classeTherapeutique}
              onChange={(e) => setClasseTherapeutique(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Laboratoire
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={laboratoire}
              onChange={(e) => setLaboratoire(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Catégorie
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={categorie}
              onChange={(e) => setCategorie(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>
        </Group>

        {/* Substance active */}
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Substance active
          </Text>
          <Group>
            <Badge variant="filled" color="gray" size="lg">
              {substanceActive || 'AMOXICILLINE'}
              <ActionIcon size="xs" color="white" ml="xs">
                <IconX size={10} />
              </ActionIcon>
            </Badge>
            <TextInput
              placeholder="Saisir"
              value={substanceActive}
              onChange={(e) => setSubstanceActive(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
            <Group>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
              <ActionIcon size="sm" color="gray">
                <IconX size={12} />
              </ActionIcon>
            </Group>
          </Group>
        </div>

        {/* Checkboxes */}
        <Group>
          <Checkbox
            checked={remboursable}
            onChange={(event) => setRemboursable(event.currentTarget.checked)}
            label="Remboursable"
          />
          <Checkbox
            checked={generique}
            onChange={(event) => setGenerique(event.currentTarget.checked)}
            label="Générique"
          />
          <Checkbox
            checked={supplementAlimentaire}
            onChange={(event) => setSupplementAlimentaire(event.currentTarget.checked)}
            label="Supplément Alimentaire"
          />
        </Group>

        {/* Indication */}
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Indication
          </Text>
          <Textarea
            placeholder=""
            value={indication}
            onChange={(e) => setIndication(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        {/* Posologie par défaut */}
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Posologie par défaut
          </Text>
          <Textarea
            placeholder=""
            value={posologieDefaut}
            onChange={(e) => setPosologieDefaut(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        {/* Présentations */}
        <div>
          <Text size="sm" fw={500} mb="md" c="dimmed">
            Présentations
          </Text>
          {presentations.map((presentation) => (
            <Group key={presentation.id} mb="sm" align="end">
              <div style={{ flex: 1 }}>
                <Text size="xs" c="red" mb="xs">
                  Présentation *
                </Text>
                <TextInput
                  value={presentation.presentation}
                  onChange={(e) => updatePresentation(presentation.id, 'presentation', e.currentTarget.value)}
                  placeholder="1 FLACON 60 ML"
                />
              </div>
              <div style={{ width: 120 }}>
                <Text size="xs" c="red" mb="xs">
                  PPV *
                </Text>
                <NumberInput
                  value={presentation.ppv}
                  onChange={(value) => updatePresentation(presentation.id, 'ppv', Number(value) || 0)}
                  placeholder="32,70"
                  decimalScale={2}
                />
              </div>
              <ActionIcon
                color="red"
                variant="filled"
                onClick={() => removePresentation(presentation.id)}
              >
                <IconTrash size={16} />
              </ActionIcon>
            </Group>
          ))}
        </div>

        {/* Boutons d'action */}
        <Group justify="space-between" mt="xl">
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={addPresentation}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Ajouter une présentation
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Ajouter une présentation
            </Button>
          </Group>
          <Group>
            <Button
              variant="filled"
              color="gray"
              onClick={() => onSubmit({
                nomCommercial,
                formeGalenique,
                classeTherapeutique,
                laboratoire,
                categorie,
                substanceActive,
                presentations,
              })}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={onCancel}
            >
              Annuler
            </Button>
          </Group>
        </Group>
      </Stack>
    </form>
  );
};

// Composant de formulaire pour les produits paramédicaux
interface ProductFormProps {
  product: ParamedicalProduct | null;
  onSubmit: (data: Partial<ParamedicalProduct>) => void;
  onCancel: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSubmit, onCancel }) => {
  const [nomProduit, setNomProduit] = useState(product?.nomProduit || '');
  const [formeGalenique, setFormeGalenique] = useState(product?.formeGalenique || '');
  const [laboratoire, setLaboratoire] = useState(product?.laboratoire || '');
  const [categorie, setCategorie] = useState(product?.categorie || 'Para - autres');
  const [indication, setIndication] = useState('');
  const [posologieDefaut, setPosologieDefaut] = useState('');
  const [remboursable, setRemboursable] = useState(false);
  const [generique, setGenerique] = useState(false);
  const [supplementAlimentaire, setSupplementAlimentaire] = useState(false);
  const [presentations, setPresentations] = useState<Presentation[]>(product?.presentations || [
    { id: 1, presentation: 'Produit', ppv: 26.67 }
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!nomProduit.trim()) return;

    onSubmit({
      nomProduit: nomProduit.trim(),
      formeGalenique: formeGalenique.trim(),
      laboratoire: laboratoire.trim(),
      categorie: categorie.trim(),
      presentations,
    });
  };

  const addPresentation = () => {
    const newPresentation: Presentation = {
      id: Math.max(...presentations.map(p => p.id), 0) + 1,
      presentation: '',
      ppv: 0,
    };
    setPresentations([...presentations, newPresentation]);
  };

  const removePresentation = (id: number) => {
    setPresentations(presentations.filter(p => p.id !== id));
  };

  const updatePresentation = (id: number, field: keyof Presentation, value: string | number) => {
    setPresentations(presentations.map(p =>
      p.id === id ? { ...p, [field]: value } : p
    ));
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        {/* Première ligne */}
        <Group grow>
          <div>
            <Text size="sm" fw={500} mb="xs">
              <span style={{ color: 'red' }}>Nom du produit</span>
            </Text>
            <TextInput
              placeholder={product ? product.nomProduit : ""}
              value={nomProduit}
              onChange={(e) => setNomProduit(e.currentTarget.value)}
              required
              styles={{
                input: {
                  borderBottom: '2px solid red',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderRadius: 0,
                  backgroundColor: 'transparent',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Forme galénique - Unité
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={formeGalenique}
              onChange={(e) => setFormeGalenique(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>
        </Group>

        {/* Deuxième ligne */}
        <Group grow>
          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Laboratoire
              </Text>
              <ActionIcon size="sm" color="blue">
                <IconPlus size={12} />
              </ActionIcon>
            </Group>
            <TextInput
              placeholder=""
              value={laboratoire}
              onChange={(e) => setLaboratoire(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>

          <div>
            <Group justify="space-between" align="center" mb="xs">
              <Text size="sm" fw={500} c="dimmed">
                Catégorie
              </Text>
              <Group>
                <ActionIcon size="sm" color="blue">
                  <IconPlus size={12} />
                </ActionIcon>
                <ActionIcon size="sm" color="gray">
                  <IconX size={12} />
                </ActionIcon>
              </Group>
            </Group>
            <TextInput
              placeholder="Para - autres"
              value={categorie}
              onChange={(e) => setCategorie(e.currentTarget.value)}
              styles={{
                input: {
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                },
              }}
            />
          </div>
        </Group>

        {/* Checkboxes */}
        <Group>
          <Checkbox
            checked={remboursable}
            onChange={(event) => setRemboursable(event.currentTarget.checked)}
            label="Remboursable"
          />
          <Checkbox
            checked={generique}
            onChange={(event) => setGenerique(event.currentTarget.checked)}
            label="Générique"
          />
          <Checkbox
            checked={supplementAlimentaire}
            onChange={(event) => setSupplementAlimentaire(event.currentTarget.checked)}
            label="Supplément Alimentaire"
          />
        </Group>

        {/* Indication */}
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Indication
          </Text>
          <Textarea
            placeholder=""
            value={indication}
            onChange={(e) => setIndication(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        {/* Posologie par défaut */}
        <div>
          <Text size="sm" fw={500} mb="xs" c="dimmed">
            Posologie par défaut
          </Text>
          <Textarea
            placeholder=""
            value={posologieDefaut}
            onChange={(e) => setPosologieDefaut(e.currentTarget.value)}
            rows={3}
            styles={{
              input: {
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
              },
            }}
          />
        </div>

        {/* Présentations */}
        <div>
          <Text size="sm" fw={500} mb="md" c="dimmed">
            Présentations
          </Text>
          {presentations.map((presentation) => (
            <Group key={presentation.id} mb="sm" align="end">
              <div style={{ flex: 1 }}>
                <Text size="xs" c="red" mb="xs">
                  Présentation *
                </Text>
                <TextInput
                  value={presentation.presentation}
                  onChange={(e) => updatePresentation(presentation.id, 'presentation', e.currentTarget.value)}
                  placeholder="Produit"
                />
              </div>
              <div style={{ width: 120 }}>
                <Text size="xs" c="red" mb="xs">
                  PPV *
                </Text>
                <NumberInput
                  value={presentation.ppv}
                  onChange={(value) => updatePresentation(presentation.id, 'ppv', Number(value) || 0)}
                  placeholder="26,67"
                  decimalScale={2}
                />
              </div>
              <ActionIcon
                color="red"
                variant="filled"
                onClick={() => removePresentation(presentation.id)}
              >
                <IconTrash size={16} />
              </ActionIcon>
            </Group>
          ))}
        </div>

        {/* Boutons d'action */}
        <Group justify="space-between" mt="xl">
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => {
                // Ouvrir modal posologies
              }}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Ajouter des Posologies
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={addPresentation}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Ajouter une présentation
            </Button>
          </Group>
          <Group>
            <Button
              variant="filled"
              color="gray"
              onClick={() => onSubmit({
                nomProduit,
                formeGalenique,
                laboratoire,
                categorie,
                presentations,
              })}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={onCancel}
            >
              Annuler
            </Button>
          </Group>
        </Group>
      </Stack>
    </form>
  );
};

export default Medicaments_Para;
