# Types de Filtres pour la Facturation

Ce dossier contient les définitions TypeScript pour les filtres utilisés dans les modules de facturation.

## 🎯 Objectif

Remplacer les types `{ [key: string]: any }` par des types stricts et spécifiques pour améliorer :
- La sécurité des types
- L'autocomplétion dans l'IDE
- La validation à la compilation
- La documentation du code

## 📁 Structure

```
types/
├── filters.ts          # Définitions des types de filtres
├── README.md          # Cette documentation
└── examples/
    └── filter-usage.tsx # Exemples d'utilisation
```

## 🔧 Types Disponibles

### BaseFilters
Type de base commun à tous les filtres :
```typescript
interface BaseFilters {
  dateRange?: { start?: string; end?: string; };
  status?: string[];
  amount?: { min?: number; max?: number; };
  patient?: string;
  physician?: string;
}
```

### Types Spécialisés

#### QuotationFilters (Devis)
```typescript
interface QuotationFilters extends BaseFilters {
  quotationType?: string[];
  validityStatus?: string[];
  acceptanceStatus?: string[];
  expirationDate?: { start?: string; end?: string; };
}
```

#### InvoiceFilters (Factures)
```typescript
interface InvoiceFilters extends BaseFilters {
  invoiceType?: string[];
  paymentStatus?: string[];
  dueDate?: { start?: string; end?: string; };
  paymentMethod?: string[];
}
```

#### PaymentFilters (Règlements)
```typescript
interface PaymentFilters extends BaseFilters {
  paymentMethod?: string[];
  paymentType?: string[];
  reference?: string;
  bank?: string;
}
```

#### ContractFilters (Contrats)
```typescript
interface ContractFilters extends BaseFilters {
  contractType?: string[];
  subscriptionStatus?: string[];
  renewalDate?: { start?: string; end?: string; };
  organization?: string;
}
```

#### ExpenseFilters (Dépenses)
```typescript
interface ExpenseFilters extends BaseFilters {
  expenseCategory?: string[];
  expenseType?: string[];
  supplier?: string;
  paymentMethod?: string[];
  receiptStatus?: string[];
}
```

## 🚀 Utilisation

### Import
```typescript
import { QuotationFilters, validateFilters, cleanFilters } from './types/filters';
```

### Dans une interface de requête
```typescript
interface QuotationQuery {
  searchAll: string;
  page: number;
  limit: number;
  filters: QuotationFilters; // ✅ Au lieu de { [key: string]: any }
  search: { [key: string]: string };
}
```

### Validation des filtres
```typescript
const filters: QuotationFilters = {
  dateRange: { start: '2025-01-01', end: '2025-12-31' },
  amount: { min: 100, max: 5000 },
  quotationType: ['dental', 'orthodontic']
};

try {
  const validatedFilters = validateFilters(filters);
  const cleanedFilters = cleanFilters(validatedFilters);
  // Utiliser cleanedFilters...
} catch (error) {
  console.error('Erreur de validation:', error);
}
```

## 🛠️ Fonctions Utilitaires

### validateFilters<T>(filters: T): T
Valide les filtres selon des règles métier :
- Date de début < Date de fin
- Montant minimum < Montant maximum
- Montants non négatifs

### cleanFilters<T>(filters: T): Partial<T>
Nettoie les filtres en supprimant :
- Les valeurs undefined/null/vides
- Les tableaux vides
- Les objets vides

## 📋 Fichiers Modifiés

Les fichiers suivants ont été mis à jour pour utiliser les types stricts :

1. ✅ `Mes_devis.tsx` - QuotationFilters
2. ✅ `Mes_facuers.tsx` - InvoiceFilters  
3. ✅ `Mes_reglements.tsx` - PaymentFilters
4. ✅ `Mes_contrats.tsx` - ContractFilters
5. ✅ `Mes_depenses.tsx` - ExpenseFilters

## 🎉 Avantages

### Avant (avec `any`)
```typescript
filters: { [key: string]: any }; // ❌ Pas de sécurité des types
```

### Après (avec types stricts)
```typescript
filters: QuotationFilters; // ✅ Types stricts et sécurisés
```

### Bénéfices
- 🔒 **Sécurité des types** - Détection d'erreurs à la compilation
- 🧠 **IntelliSense** - Autocomplétion intelligente dans l'IDE
- 📚 **Documentation** - Types auto-documentés
- 🔧 **Refactoring** - Changements sûrs et assistés
- 🐛 **Moins de bugs** - Validation à la compilation
- 🚀 **Productivité** - Développement plus rapide et sûr

## 🔍 Exemple Complet

Voir le fichier `examples/filter-usage.tsx` pour des exemples complets d'utilisation de chaque type de filtre.

## 🤝 Contribution

Pour ajouter de nouveaux types de filtres :
1. Étendre `BaseFilters` si nécessaire
2. Créer une nouvelle interface spécialisée
3. Ajouter la validation dans `validateFilters`
4. Mettre à jour cette documentation
5. Ajouter des exemples d'utilisation
