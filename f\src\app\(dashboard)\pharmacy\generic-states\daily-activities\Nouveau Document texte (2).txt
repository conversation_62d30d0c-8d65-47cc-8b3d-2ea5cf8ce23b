3368:onClick={() => {handleEditClick(appointment)}}Cannot find name 'appointment'. Did you mean 'appointments'?ts(2552)
CetteJournee.tsx(404, 12): 'appointments' is declared here.
any
4820:appointmentForm={appointmentForm} Type 'UseFormReturnType<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }, (values: { ...; }) => { ...; }>' is not assignable to type 'UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>'.
  Types of property 'initialize' are incompatible.
    Type 'Initialize<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }>' is not assignable to type 'Initialize<Record<string, any>>'.
      Type 'Record<string, any>' is missing the following properties from type '{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }': patientId, notes, date, duration, and 5 more.ts(2322)
AjouterUnRendezVous.tsx(59, 3): The expected type comes from property 'appointmentForm' which is declared here on type 'IntrinsicAttributes & AjouterUnRendezVousProps'
(property) AjouterUnRendezVousProps.appointmentForm: UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>
4821:handleSubmit={handleSubmit as (values: Record<string, any>) => void}Unexpected any. Specify a different type.eslint@typescript-eslint/no-explicit-any
4911: appointmentForm={appointmentForm}Type 'UseFormReturnType<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }, (values: { ...; }) => { ...; }>' is not assignable to type 'UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>'.
  Types of property 'initialize' are incompatible.
    Type 'Initialize<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }>' is not assignable to type 'Initialize<Record<string, any>>'.
      Type 'Record<string, any>' is missing the following properties from type '{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }': patientId, notes, date, duration, and 5 more.ts(2322)
AjouterUnRendezVous.tsx(59, 3): The expected type comes from property 'appointmentForm' which is declared here on type 'IntrinsicAttributes & AjouterUnRendezVousProps'
(property) AjouterUnRendezVousProps.appointmentForm: UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>
5013:appointmentForm={appointmentForm}Type 'UseFormReturnType<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }, (values: { ...; }) => { ...; }>' is not assignable to type 'UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>'.
  Types of property 'initialize' are incompatible.
    Type 'Initialize<{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }>' is not assignable to type 'Initialize<Record<string, any>>'.
      Type 'Record<string, any>' is missing the following properties from type '{ patientId: string; notes: string; date: Date; duration: number; type: string; resourceId: number; addToWaitingList: boolean; removeFromCalendar: boolean; rescheduleDateTime: string; }': patientId, notes, date, duration, and 5 more.ts(2322)
AjouterUnRendezVous.tsx(59, 3): The expected type comes from property 'appointmentForm' which is declared here on type 'IntrinsicAttributes & AjouterUnRendezVousProps'
(property) AjouterUnRendezVousProps.appointmentForm: UseFormReturnType<Record<string, any>, _TransformValues<Record<string, any>>>
5014:   handleSubmit={handleUpdatePatient}