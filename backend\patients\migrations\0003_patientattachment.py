# Generated by Django 5.1.3 on 2025-08-06 13:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("appointments", "0001_initial"),
        ("patients", "0002_patientalert"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PatientAttachment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        help_text="Uploaded file",
                        upload_to="patient_attachments/%Y/%m/%d/",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(help_text="Original filename", max_length=255),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(help_text="File size in bytes"),
                ),
                (
                    "mime_type",
                    models.Char<PERSON><PERSON>(help_text="MIME type of the file", max_length=100),
                ),
                (
                    "attachment_type",
                    models.CharField(
                        choices=[
                            ("document", "Document"),
                            ("image", "Image"),
                            ("audio", "Audio"),
                            ("video", "Video"),
                            ("dicom", "DICOM"),
                            ("other", "Other"),
                        ],
                        default="document",
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("medical_record", "Medical Record"),
                            ("lab_result", "Lab Result"),
                            ("imaging", "Medical Imaging"),
                            ("prescription", "Prescription"),
                            ("insurance", "Insurance Document"),
                            ("identification", "Identification"),
                            ("consent", "Consent Form"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="Title or description of the attachment",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description", null=True
                    ),
                ),
                (
                    "tags",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated tags",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(
                        default=False,
                        help_text="Is this attachment private to the creator",
                    ),
                ),
                (
                    "is_sensitive",
                    models.BooleanField(
                        default=False,
                        help_text="Contains sensitive medical information",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "is_processed",
                    models.BooleanField(
                        default=False, help_text="Has the file been processed/scanned"
                    ),
                ),
                (
                    "processing_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=50,
                    ),
                ),
                (
                    "medical_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the medical document was created",
                        null=True,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        limit_choices_to={"user_type": "patient"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="patient_attachments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "related_appointment",
                    models.ForeignKey(
                        blank=True,
                        help_text="Related appointment if applicable",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="attachments",
                        to="appointments.appointment",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="uploaded_patient_attachments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Attachment",
                "verbose_name_plural": "Patient Attachments",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["patient", "attachment_type"],
                        name="patients_pa_patient_df4ed5_idx",
                    ),
                    models.Index(
                        fields=["patient", "category"],
                        name="patients_pa_patient_329151_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="patients_pa_created_4adb95_idx"
                    ),
                ],
            },
        ),
    ]
