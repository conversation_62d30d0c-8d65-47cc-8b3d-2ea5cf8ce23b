'use client';
import React from 'react';
import Link from 'next/link';
import {
  Title, Text, Paper, Button, Group, ThemeIcon, 
  SimpleGrid, Card, Badge, List
} from '@mantine/core';
import { IconUsers, IconUserPlus } from '@tabler/icons-react';

const UserManagementCard: React.FC = () => {
  return (
    <>
      <Group justify="space-between">
        <div>
          <Group mb="xs">
            <ThemeIcon size={32} radius="md" color="indigo">
              <IconUsers size={20} />
            </ThemeIcon>
            <Title order={3}>User Management</Title>
          </Group>
          <Text c="dimmed">Manage user accounts for your assistants and patients based on your subscription limits</Text>
        </div>
        <Button
          component={Link}
          href="/user-management"
          leftSection={<IconUserPlus size={16} />}
        >
          Manage Users
        </Button>
      </Group>

      <Text mt="md">
        Create and manage accounts for your staff and patients. Your subscription determines how many accounts you can create.
      </Text>

      <SimpleGrid cols={2} mt="md" w={"100%"}>
        <Card withBorder p="md" >
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Assistant Accounts</Text>
            <Badge color="blue">Subscription Feature</Badge>
          </Group>
          <Text size="sm" c="dimmed" mb="md">
            Create accounts for your medical assistants and staff members
          </Text>
          <List size="sm" spacing="xs">
            <List.Item>Manage appointment scheduling</List.Item>
            <List.Item>Handle patient records</List.Item>
            <List.Item>Assist with administrative tasks</List.Item>
          </List>
        </Card>

        <Card withBorder p="md">
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Patient Accounts</Text>
            <Badge color="green">Subscription Feature</Badge>
          </Group>
          <Text size="sm" c="dimmed" mb="md">
            Create accounts for your patients to access their medical information
          </Text>
          <List size="sm" spacing="xs">
            <List.Item>View medical records</List.Item>
            <List.Item>Schedule appointments</List.Item>
            <List.Item>Receive notifications</List.Item>
          </List>
        </Card>
      </SimpleGrid>
    </>
  );
};

export default UserManagementCard;
