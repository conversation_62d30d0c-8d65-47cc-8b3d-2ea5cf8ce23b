/**
 * Custom hook for managing payment data
 * Provides easy access to payment collections, account balances, and analytics
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  paymentService, 
  PaymentCollection,
  AccountBalance,
  PaymentMethod,
  PaymentTransaction,
  PaymentAnalytics,
  PaymentSummary
} from '@/services/paymentService';

interface UsePaymentOptions {
  patientId?: string;
  dateRange?: { start: string; end: string };
  autoFetch?: boolean;
  refreshInterval?: number;
  dataTypes?: string[];
}

interface UsePaymentReturn {
  // Data
  paymentCollections: PaymentCollection[];
  accountBalances: AccountBalance[];
  paymentMethods: PaymentMethod[];
  paymentTransactions: PaymentTransaction[];
  paymentAnalytics: PaymentAnalytics | null;
  summary: PaymentSummary | null;
  
  // Loading states
  loading: boolean;
  collectionsLoading: boolean;
  balancesLoading: boolean;
  transactionsLoading: boolean;
  analyticsLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchPaymentCollections: (patientId?: string) => Promise<void>;
  fetchAccountBalances: (patientId?: string) => Promise<void>;
  fetchPaymentMethods: () => Promise<void>;
  fetchPaymentTransactions: (patientId?: string) => Promise<void>;
  fetchPaymentAnalytics: () => Promise<void>;
  fetchSummary: (patientId?: string) => Promise<void>;
  refreshAll: (patientId?: string) => Promise<void>;
  createPaymentCollection: (collectionData: Omit<PaymentCollection, 'id' | 'created_at'>) => Promise<void>;
  
  // Utility functions
  getCollectionsByPatient: (patientId: string) => PaymentCollection[];
  getBalanceByPatient: (patientId: string) => AccountBalance | null;
  getTransactionsByPatient: (patientId: string) => PaymentTransaction[];
  getPatientPaymentStats: (patientId: string) => {
    totalCollected: number;
    totalTransactions: number;
    remainingBalance: number;
    lastPaymentDate: string | null;
    preferredPaymentMethod: string;
    accountStatus: string;
  };
  getPaymentMethodStats: () => {
    totalMethods: number;
    activeMethods: number;
    methodBreakdown: Array<{ method: string; count: number; amount: number }>;
  };
  getCollectionTrends: () => {
    dailyAverage: number;
    weeklyTotal: number;
    monthlyTotal: number;
    collectionRate: number;
    topPaymentMethod: string;
  };
  getOverdueAccounts: () => AccountBalance[];
}

export const usePayment = (options: UsePaymentOptions = {}): UsePaymentReturn => {
  const { patientId, dateRange, autoFetch = true, refreshInterval, dataTypes } = options;

  // State
  const [paymentCollections, setPaymentCollections] = useState<PaymentCollection[]>([]);
  const [accountBalances, setAccountBalances] = useState<AccountBalance[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [paymentTransactions, setPaymentTransactions] = useState<PaymentTransaction[]>([]);
  const [paymentAnalytics, setPaymentAnalytics] = useState<PaymentAnalytics | null>(null);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [collectionsLoading, setCollectionsLoading] = useState(false);
  const [balancesLoading, setBalancesLoading] = useState(false);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchPaymentCollections = useCallback(async (targetPatientId?: string) => {
    setCollectionsLoading(true);
    setError(null);
    try {
      const data = await paymentService.getPaymentCollections(targetPatientId || patientId, dateRange);
      setPaymentCollections(data);
    } catch (err) {
      setError(`Failed to fetch payment collections: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setCollectionsLoading(false);
    }
  }, [patientId, dateRange]);

  const fetchAccountBalances = useCallback(async (targetPatientId?: string) => {
    setBalancesLoading(true);
    setError(null);
    try {
      const data = await paymentService.getAccountBalances(targetPatientId || patientId);
      setAccountBalances(data);
    } catch (err) {
      setError(`Failed to fetch account balances: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setBalancesLoading(false);
    }
  }, [patientId]);

  const fetchPaymentMethods = useCallback(async () => {
    setError(null);
    try {
      const data = await paymentService.getPaymentMethods();
      setPaymentMethods(data);
    } catch (err) {
      setError(`Failed to fetch payment methods: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, []);

  const fetchPaymentTransactions = useCallback(async (targetPatientId?: string) => {
    setTransactionsLoading(true);
    setError(null);
    try {
      const data = await paymentService.getPaymentTransactions(targetPatientId || patientId, dateRange);
      setPaymentTransactions(data);
    } catch (err) {
      setError(`Failed to fetch payment transactions: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setTransactionsLoading(false);
    }
  }, [patientId, dateRange]);

  const fetchPaymentAnalytics = useCallback(async () => {
    setAnalyticsLoading(true);
    setError(null);
    try {
      const data = await paymentService.getPaymentAnalytics(dateRange);
      setPaymentAnalytics(data);
    } catch (err) {
      setError(`Failed to fetch payment analytics: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setAnalyticsLoading(false);
    }
  }, [dateRange]);

  const fetchSummary = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await paymentService.getPaymentSummary(targetPatientId || patientId, dateRange);
      setSummary(data);
      setPaymentCollections(data.paymentCollections);
      setAccountBalances(data.accountBalances);
      setPaymentMethods(data.paymentMethods);
      setPaymentTransactions(data.paymentTransactions);
      setPaymentAnalytics(data.paymentAnalytics);
    } catch (err) {
      setError(`Failed to fetch payment summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [patientId, dateRange]);

  const refreshAll = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    try {
      await Promise.all([
        fetchPaymentCollections(targetPatientId),
        fetchAccountBalances(targetPatientId),
        fetchPaymentMethods(),
        fetchPaymentTransactions(targetPatientId),
        fetchPaymentAnalytics(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchPaymentCollections, fetchAccountBalances, fetchPaymentMethods, fetchPaymentTransactions, fetchPaymentAnalytics]);

  const createPaymentCollection = useCallback(async (collectionData: Omit<PaymentCollection, 'id' | 'created_at'>) => {
    setError(null);
    try {
      const newCollection = await paymentService.createPaymentCollection(collectionData);
      setPaymentCollections(prev => [newCollection, ...prev]);
    } catch (err) {
      setError(`Failed to create payment collection: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const getCollectionsByPatient = useCallback((targetPatientId: string) => {
    return paymentCollections.filter(c => c.patient_id === targetPatientId);
  }, [paymentCollections]);

  const getBalanceByPatient = useCallback((targetPatientId: string) => {
    return accountBalances.find(b => b.patient_id === targetPatientId) || null;
  }, [accountBalances]);

  const getTransactionsByPatient = useCallback((targetPatientId: string) => {
    return paymentTransactions.filter(t => t.patient_id === targetPatientId);
  }, [paymentTransactions]);

  const getPatientPaymentStats = useCallback((targetPatientId: string) => {
    const patientCollections = getCollectionsByPatient(targetPatientId);
    const patientBalance = getBalanceByPatient(targetPatientId);
    const patientTransactions = getTransactionsByPatient(targetPatientId);

    const totalCollected = patientCollections.reduce((sum, c) => sum + c.amount_collected, 0);
    const lastPayment = patientCollections
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

    // Find most used payment method
    const methodCounts = patientCollections.reduce((acc, c) => {
      acc[c.payment_method] = (acc[c.payment_method] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const preferredMethod = Object.entries(methodCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';

    return {
      totalCollected,
      totalTransactions: patientTransactions.length,
      remainingBalance: patientBalance?.remaining_balance || 0,
      lastPaymentDate: lastPayment?.date || null,
      preferredPaymentMethod: preferredMethod,
      accountStatus: patientBalance?.account_status || 'unknown',
    };
  }, [getCollectionsByPatient, getBalanceByPatient, getTransactionsByPatient]);

  const getPaymentMethodStats = useCallback(() => {
    const activeMethods = paymentMethods.filter(m => m.is_active);
    
    const methodBreakdown = paymentAnalytics?.payment_methods_breakdown || [];

    return {
      totalMethods: paymentMethods.length,
      activeMethods: activeMethods.length,
      methodBreakdown: methodBreakdown.map(m => ({
        method: m.method,
        count: m.count,
        amount: m.total_amount,
      })),
    };
  }, [paymentMethods, paymentAnalytics]);

  const getCollectionTrends = useCallback(() => {
    if (!paymentAnalytics) {
      return {
        dailyAverage: 0,
        weeklyTotal: 0,
        monthlyTotal: 0,
        collectionRate: 0,
        topPaymentMethod: 'unknown',
      };
    }

    const dailyCollections = paymentAnalytics.daily_collections;
    const dailyAverage = dailyCollections.reduce((sum, d) => sum + d.total_amount, 0) / dailyCollections.length || 0;
    const weeklyTotal = dailyCollections.slice(-7).reduce((sum, d) => sum + d.total_amount, 0);
    const monthlyTotal = paymentAnalytics.total_collections;

    const topMethod = paymentAnalytics.payment_methods_breakdown
      .sort((a, b) => b.total_amount - a.total_amount)[0]?.method || 'unknown';

    return {
      dailyAverage,
      weeklyTotal,
      monthlyTotal,
      collectionRate: paymentAnalytics.collection_rate,
      topPaymentMethod: topMethod,
    };
  }, [paymentAnalytics]);

  const getOverdueAccounts = useCallback(() => {
    return accountBalances.filter(b => b.account_status === 'overdue');
  }, [accountBalances]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (dataTypes && dataTypes.length > 0) {
        // Fetch specific data types
        dataTypes.forEach(type => {
          switch (type) {
            case 'collections':
              fetchPaymentCollections();
              break;
            case 'balances':
              fetchAccountBalances();
              break;
            case 'methods':
              fetchPaymentMethods();
              break;
            case 'transactions':
              fetchPaymentTransactions();
              break;
            case 'analytics':
              fetchPaymentAnalytics();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, dataTypes, refreshAll, fetchPaymentCollections, fetchAccountBalances, fetchPaymentMethods, fetchPaymentTransactions, fetchPaymentAnalytics]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    paymentCollections,
    accountBalances,
    paymentMethods,
    paymentTransactions,
    paymentAnalytics,
    summary,
    
    // Loading states
    loading,
    collectionsLoading,
    balancesLoading,
    transactionsLoading,
    analyticsLoading,
    
    // Error state
    error,
    
    // Actions
    fetchPaymentCollections,
    fetchAccountBalances,
    fetchPaymentMethods,
    fetchPaymentTransactions,
    fetchPaymentAnalytics,
    fetchSummary,
    refreshAll,
    createPaymentCollection,
    
    // Utility functions
    getCollectionsByPatient,
    getBalanceByPatient,
    getTransactionsByPatient,
    getPatientPaymentStats,
    getPaymentMethodStats,
    getCollectionTrends,
    getOverdueAccounts,
  };
};

export default usePayment;
