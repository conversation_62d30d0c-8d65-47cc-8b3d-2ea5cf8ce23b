// Types pour les filtres de facturation

// Filtres de base communs
export interface BaseFilters {
  dateRange?: {
    start?: string;
    end?: string;
  };
  status?: string[];
  amount?: {
    min?: number;
    max?: number;
  };
  patient?: string;
  physician?: string;
}

// Filtres spécifiques aux devis
export interface QuotationFilters extends BaseFilters {
  quotationType?: string[];
  validityStatus?: string[];
  acceptanceStatus?: string[];
  expirationDate?: {
    start?: string;
    end?: string;
  };
}

// Filtres spécifiques aux factures
export interface InvoiceFilters extends BaseFilters {
  invoiceType?: string[];
  paymentStatus?: string[];
  dueDate?: {
    start?: string;
    end?: string;
  };
  paymentMethod?: string[];
}

// Filtres spécifiques aux règlements
export interface PaymentFilters extends BaseFilters {
  paymentMethod?: string[];
  paymentType?: string[];
  reference?: string;
  bank?: string;
}

// Filtres spécifiques aux contrats
export interface ContractFilters extends BaseFilters {
  contractType?: string[];
  subscriptionStatus?: string[];
  renewalDate?: {
    start?: string;
    end?: string;
  };
  organization?: string;
}

// Filtres spécifiques aux dépenses
export interface ExpenseFilters extends BaseFilters {
  expenseCategory?: string[];
  expenseType?: string[];
  supplier?: string;
  paymentMethod?: string[];
  receiptStatus?: string[];
}

// Type union pour tous les filtres
export type BillingFilters = 
  | QuotationFilters 
  | InvoiceFilters 
  | PaymentFilters 
  | ContractFilters 
  | ExpenseFilters;

// Fonction utilitaire pour valider les filtres
export function validateFilters<T extends BaseFilters>(filters: T): T {
  // Validation de base
  if (filters.dateRange) {
    if (filters.dateRange.start && filters.dateRange.end) {
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      if (startDate > endDate) {
        throw new Error('La date de début doit être antérieure à la date de fin');
      }
    }
  }

  if (filters.amount) {
    if (filters.amount.min !== undefined && filters.amount.max !== undefined) {
      if (filters.amount.min > filters.amount.max) {
        throw new Error('Le montant minimum doit être inférieur au montant maximum');
      }
    }
    if (filters.amount.min !== undefined && filters.amount.min < 0) {
      throw new Error('Le montant minimum ne peut pas être négatif');
    }
  }

  return filters;
}

// Fonction pour nettoyer les filtres vides
export function cleanFilters<T extends BaseFilters>(filters: T): Partial<T> {
  const cleaned: Partial<T> = {};
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value) && value.length > 0) {
        (cleaned as Record<string, unknown>)[key] = value;
      } else if (typeof value === 'object' && value !== null) {
        const cleanedObject = cleanFilters(value as BaseFilters);
        if (Object.keys(cleanedObject).length > 0) {
          (cleaned as Record<string, unknown>)[key] = cleanedObject;
        }
      } else if (typeof value !== 'object') {
        (cleaned as Record<string, unknown>)[key] = value;
      }
    }
  });
  
  return cleaned;
}

// Types pour les options de filtre
export interface FilterOption {
  value: string;
  label: string;
  color?: string;
  count?: number;
}

export interface FilterGroup {
  key: string;
  label: string;
  options: FilterOption[];
  multiple?: boolean;
  searchable?: boolean;
}

// Configuration des filtres par module
export interface FilterConfig {
  groups: FilterGroup[];
  defaultFilters?: BaseFilters;
  maxFilters?: number;
}
