'use client';

import React from 'react';

interface MigrationStatusProps {
  className?: string;
}

const MigrationStatus: React.FC<MigrationStatusProps> = ({ className = '' }) => {
  const migrationSteps = [
    {
      step: 1,
      title: 'Installation des dépendances',
      description: 'react-big-calendar, moment, types',
      status: 'completed',
      details: 'Packages installés et configurés'
    },
    {
      step: 2,
      title: 'Vue Jour Améliorée',
      description: 'Nouvelle implémentation avec react-big-calendar',
      status: 'completed',
      details: 'EnhancedDayView créé avec filtrage par salle'
    },
    {
      step: 3,
      title: 'Système de Basculement',
      description: 'Toggle entre vue classique et améliorée',
      status: 'completed',
      details: 'DayViewToggle permet de comparer les deux vues'
    },
    {
      step: 4,
      title: 'Vue Semaine',
      description: 'Migration de WeekView vers react-big-calendar',
      status: 'pending',
      details: 'Prochaine étape de migration'
    },
    {
      step: 5,
      title: 'Vue Mois',
      description: 'Migration de MonthView vers react-big-calendar',
      status: 'pending',
      details: 'Migration après la vue semaine'
    },
    {
      step: 6,
      title: 'Fonctionnalités Avancées',
      description: 'Drag & drop, redimensionnement, récurrence',
      status: 'pending',
      details: 'Fonctionnalités natives de react-big-calendar'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'in-progress':
        return '🔄';
      case 'pending':
        return '⏳';
      default:
        return '❓';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'in-progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'pending':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const completedSteps = migrationSteps.filter(step => step.status === 'completed').length;
  const totalSteps = migrationSteps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <div className={`migration-status bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Migration vers react-big-calendar
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Progression de la migration du calendrier personnalisé vers react-big-calendar
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              {completedSteps}/{totalSteps}
            </div>
            <div className="text-sm text-gray-500">étapes complétées</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Progression</span>
            <span className="text-sm text-gray-500">{Math.round(progressPercentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Migration Steps */}
        <div className="space-y-4">
          {migrationSteps.map((step) => (
            <div 
              key={step.step}
              className={`p-4 rounded-lg border ${getStatusColor(step.status)}`}
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 text-lg">
                  {getStatusIcon(step.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-xs font-medium text-gray-500">
                      ÉTAPE {step.step}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      step.status === 'completed' 
                        ? 'bg-green-100 text-green-800'
                        : step.status === 'in-progress'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {step.status === 'completed' ? 'Terminé' : 
                       step.status === 'in-progress' ? 'En cours' : 'En attente'}
                    </span>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">
                    {step.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    {step.description}
                  </p>
                  <p className="text-xs text-gray-500">
                    {step.details}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Benefits */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">
            🎯 Avantages de react-big-calendar
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Gestion native des dates et fuseaux horaires</li>
            <li>• Performance optimisée pour de gros volumes de données</li>
            <li>• Drag & drop et redimensionnement intégrés</li>
            <li>• Vues multiples (jour, semaine, mois, agenda)</li>
            <li>• Accessibilité et navigation clavier</li>
            <li>• Maintenance réduite et mises à jour régulières</li>
          </ul>
        </div>

        {/* Next Steps */}
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h4 className="font-medium text-yellow-900 mb-2">
            🚀 Prochaines étapes
          </h4>
          <p className="text-sm text-yellow-800">
            Tester la vue jour améliorée, puis procéder à la migration des vues semaine et mois.
            Une fois toutes les vues migrées, nous pourrons supprimer l&apos;ancien code et bénéficier
            pleinement des fonctionnalités avancées.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MigrationStatus;
