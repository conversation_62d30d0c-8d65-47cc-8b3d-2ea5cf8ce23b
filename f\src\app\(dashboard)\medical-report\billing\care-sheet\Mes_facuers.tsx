'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  TextInput,
  Button,
  Checkbox,
  Tabs,
  Drawer,
  ScrollArea,
  Divider,
  Menu,
  Pagination,
  Select,
  Box,
  Loader,
  Tooltip,
  Radio
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiPlus,
  mdiFilterVariant,
  mdiMagnify,
  mdiReload,
  mdiFileExcel,
  mdiDotsVertical,
  mdiFindReplace,
  mdiMagnifyPlus,
  mdiFilterRemoveOutline,
  mdiFilterOutline,
  mdiCheck,
  mdiPencil,
  mdiPrinter
} from '@mdi/js';

// Types et interfaces
interface InvoiceColumn {
  id: string;
  label: string;
  isFilter: boolean;
  isShown: boolean;
  isRequired?: boolean;
  type?: 'text' | 'date' | 'number' | 'boolean';
}

import { InvoiceFilters } from './types/filters';

interface InvoiceQuery {
  searchAll: string;
  page: number;
  limit: number;
  filters: InvoiceFilters;
  search: { [key: string]: string };
}

interface InvoiceItem {
  id: string;
  invoiceNumber: string;
  date: Date;
  beneficiary: string;
  totalAmount: number;
  insurance: string;
  paymentMode: string;
}

interface MesFacturesProps {
  loading?: boolean;
  items?: InvoiceItem[];
  total?: number;
  onQueryChange?: (query: InvoiceQuery) => void;
  onAddInvoice?: () => void;
  onExport?: (format: 'excel') => void;
  onAction?: (action: string, items: InvoiceItem[]) => void;
  onEdit?: (item: InvoiceItem) => void;
  onPrint?: (item: InvoiceItem) => void;
}

export const MesFactures: React.FC<MesFacturesProps> = ({
  loading = false,
  items = [],
  total = 0,
  onQueryChange,
  onAddInvoice,
  onExport,
  onAction,
  onEdit,
  onPrint
}) => {
  // États locaux
  const [query, setQuery] = useState<InvoiceQuery>({
    searchAll: '',
    page: 1,
    limit: 15,
    filters: {},
    search: {}
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('filters');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sequenceModel, setSequenceModel] = useState('Model1');

  // Configuration des colonnes
  const columns: InvoiceColumn[] = [
    { id: 'invoiceNumber', label: 'N°. Facture', isFilter: false, isShown: true, isRequired: true },
    { id: 'date', label: 'Date', isFilter: false, isShown: true, type: 'date' },
    { id: 'beneficiary', label: 'Bénéficiaire', isFilter: false, isShown: true },
    { id: 'totalAmount', label: 'Montant Total', isFilter: false, isShown: true, type: 'number' },
    { id: 'insurance', label: 'Assurance', isFilter: false, isShown: true },
    { id: 'paymentMode', label: 'Mode de paiement', isFilter: false, isShown: true }
  ];

  // Données d'exemple
  const sampleItems: InvoiceItem[] = [
    {
      id: '1',
      invoiceNumber: 'FAC-001',
      date: new Date('2025-07-02'),
      beneficiary: 'MARTIN Jean',
      totalAmount: 1250.00,
      insurance: 'CPAM',
      paymentMode: 'Espèce'
    }
  ];

  const displayItems = items.length > 0 ? items : sampleItems;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<InvoiceQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleSearchChange = (value: string) => {
    handleQueryChange({ searchAll: value, page: 1 });
  };

  const handlePageChange = (page: number) => {
    handleQueryChange({ page });
  };

  const handleLimitChange = (limit: string | null) => {
    if (limit) {
      handleQueryChange({ limit: parseInt(limit), page: 1 });
    }
  };

  const handleAddInvoice = () => {
    console.log('Ajouter facture');
    onAddInvoice?.();
  };

  const handleExport = () => {
    console.log('Exporter Excel');
    onExport?.('excel');
  };

  const handleAction = (action: string) => {
    console.log('Action:', action, 'Items:', selectedItems);
    onAction?.(action, displayItems.filter(item => selectedItems.includes(item.id)));
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === displayItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(displayItems.map(item => item.id));
    }
  };

  const handleColumnFilter = (columnId: string, isFilter: boolean) => {
    console.log('Filtre colonne:', columnId, isFilter);
  };

  const handleColumnVisibility = (columnId: string) => {
    console.log('Visibilité colonne:', columnId);
  };

  const handleEdit = (item: InvoiceItem) => {
    console.log('Modifier facture:', item);
    onEdit?.(item);
  };

  const handlePrint = (item: InvoiceItem) => {
    console.log('Imprimer facture:', item);
    onPrint?.(item);
  };

  const totalPages = Math.ceil(total / query.limit);
  const startItem = (query.page - 1) * query.limit + 1;
  const endItem = Math.min(query.page * query.limit, total);

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header avec radio buttons et bouton d'ajout */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#f8f9fa' }}>
        <Group justify="space-between" align="center">
          <Radio.Group
            value={sequenceModel}
            onChange={setSequenceModel}
          >
            <Group gap="md">
              <Radio value="Model1" label="Model1" />
              <Radio value="all" label="Tous" />
            </Group>
          </Radio.Group>

          <Button
            leftSection={<Icon path={mdiPlus} size={0.8} />}
            onClick={handleAddInvoice}
            variant="filled"
          >
            Facture
          </Button>
        </Group>
      </Paper>

      <Box style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Sidebar avec filtres */}
        <Drawer
          opened={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          position="left"
          size="400px"
          title="Filtres et Options"
          overlayProps={{ opacity: 0.5, blur: 4 }}
        >
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'filters')}>
            <Tabs.List>
              <Tabs.Tab value="filters">Filtre avancé</Tabs.Tab>
              <Tabs.Tab value="style">Règles de mise en forme</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="filters" pt="md">
              <ScrollArea style={{ height: 'calc(100vh - 200px)' }}>
                <Group justify="space-between" mb="md">
                  <Select
                    placeholder="Aucun filtre Enregistré"
                    data={[]}
                    disabled
                    style={{ flex: 1 }}
                  />
                </Group>

                <Group justify="flex-end" mb="md" gap="xs">
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFindReplace} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiMagnifyPlus} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="red" disabled>
                    <Icon path={mdiFilterRemoveOutline} size={0.8} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" disabled>
                    <Icon path={mdiFilterOutline} size={0.8} />
                  </ActionIcon>
                </Group>

                {columns.map((column) => (
                  <Box key={column.id} mb="sm">
                    <Checkbox
                      label={column.label}
                      checked={column.isFilter}
                      onChange={(event) => handleColumnFilter(column.id, event.currentTarget.checked)}
                      size="sm"
                    />
                    {column.id !== columns[columns.length - 1].id && <Divider my="xs" />}
                  </Box>
                ))}
              </ScrollArea>
            </Tabs.Panel>

            <Tabs.Panel value="style" pt="md">
              <Text size="sm" c="dimmed">Règles de mise en forme</Text>
            </Tabs.Panel>
          </Tabs>
        </Drawer>
 </Box>
        {/* Contenu principal */}
        <Box style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Toolbar */}
          <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <ActionIcon
                  variant="subtle"
                  onClick={() => setSidebarOpen(true)}
                  title="Filtre avancé"
                >
                  <Icon path={mdiFilterVariant} size={0.8} />
                </ActionIcon>

                <Group gap="xs">
                  <Icon path={mdiMagnify} size={0.8} />
                  <TextInput
                    placeholder="Rechercher"
                    value={query.searchAll}
                    onChange={(event) => handleSearchChange(event.currentTarget.value)}
                    style={{ width: 300 }}
                  />
                </Group>
              </Group>

              <Group gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={() => handleAction('reload')}
                  title="Actualiser"
                >
                  <Icon path={mdiReload} size={0.8} />
                </ActionIcon>

                <ActionIcon
                  variant="subtle"
                  onClick={handleExport}
                  title="Exporter Excel"
                >
                  <Icon path={mdiFileExcel} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Plus d'options">
                      <Icon path={mdiDotsVertical} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    {columns.map((column) => (
                      <Menu.Item
                        key={column.id}
                        leftSection={column.isShown ? <Icon path={mdiCheck} size={0.6} /> : null}
                        onClick={() => handleColumnVisibility(column.id)}
                        disabled={column.isRequired}
                      >
                        {column.label}
                      </Menu.Item>
                    ))}
                  </Menu.Dropdown>
                </Menu>
              </Group>
            </Group>
          </Paper>

          {/* Tableau avec recherche par colonne */}
          <Box style={{ flex: 1, overflow: 'auto' }}>
            {loading ? (
              <Box p="xl" style={{ textAlign: 'center' }}>
                <Loader size="lg" />
              </Box>
            ) : (
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: 50 }}>
                      <Checkbox
                        checked={selectedItems.length === displayItems.length && displayItems.length > 0}
                        indeterminate={selectedItems.length > 0 && selectedItems.length < displayItems.length}
                        onChange={handleSelectAll}
                        size="sm"
                      />
                    </Table.Th>
                    {columns.filter(col => col.isShown).map((column) => (
                      <Table.Th key={column.id} style={{ minWidth: 120 }}>
                        <Text size="sm" fw={500}>{column.label}</Text>
                      </Table.Th>
                    ))}
                    <Table.Th style={{ width: 100 }}>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>

                {/* Ligne de recherche par colonne */}
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th></Table.Th>
                    {columns.filter(col => col.isShown).map((column) => (
                      <Table.Th key={`search-${column.id}`}>
                        <TextInput
                          placeholder="Rechercher"
                          size="xs"
                          value={query.search[column.id] || ''}
                          onChange={(event) => {
                            const newSearch = { ...query.search };
                            newSearch[column.id] = event.currentTarget.value;
                            handleQueryChange({ search: newSearch, page: 1 });
                          }}
                        />
                      </Table.Th>
                    ))}
                    <Table.Th></Table.Th>
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {displayItems.map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onChange={() => handleSelectItem(item.id)}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.invoiceNumber}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.date.toLocaleDateString('fr-FR')}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.beneficiary}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.totalAmount.toFixed(2)}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.insurance}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{item.paymentMode}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              size="sm"
                              onClick={() => handleEdit(item)}
                            >
                              <Icon path={mdiPencil} size={0.7} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Imprimer">
                            <ActionIcon
                              variant="subtle"
                              size="sm"
                              onClick={() => handlePrint(item)}
                            >
                              <Icon path={mdiPrinter} size={0.7} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          {/* Pagination */}
          <Paper p="md" withBorder style={{ borderTop: '1px solid #e9ecef' }}>
            <Group justify="space-between" align="center">
              <Group gap="md">
                <Text size="sm">Page</Text>
                <Pagination
                  value={query.page}
                  onChange={handlePageChange}
                  total={totalPages}
                  size="sm"
                  disabled={loading}
                />
              </Group>

              <Group gap="md">
                <Text size="sm">Lignes par Page</Text>
                <Select
                  value={query.limit.toString()}
                  onChange={handleLimitChange}
                  data={['5', '10', '15', '20']}
                  size="sm"
                  style={{ width: 80 }}
                  disabled={loading}
                />
              </Group>

              <Text size="sm" c="dimmed">
                {total > 0 ? `${startItem} - ${endItem} de ${total}` : '0 - 0 de 0'}
              </Text>
            </Group>
          </Paper>
        </Box>
      </Box>
    </Paper>
  );
};

export default MesFactures;
