"use client";
import React from "react";
import {
  Modal,
  Text,
  Group,
  Switch,
  Select,
  Menu,
  Avatar,
  TextInput,
  ActionIcon,
  Radio,
  InputBase,
  ColorPicker,
  Button,
  Textarea,
  NumberInput,
  Autocomplete
} from '@mantine/core';
import SimpleBar from "simplebar-react";
import { IconHexagonPlusFilled, IconCheck, IconColorPicker } from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiAccountCheckOutline,mdiMicrophone } from '@mdi/js';
import { IconTextPlus } from '@tabler/icons-react';
// import { ListPlus } from 'lucide-react';
import { IMaskInput } from 'react-imask';
import { notifications } from '@mantine/notifications';
import moment from "moment";
import PauseModal from './PauseModal';
import {
  Patient,
  AppointmentEvent,
  ConsultationType,
  TitleOption,
  AgendaType,
  EventType
} from '@/types/typesCalendarPatient';

// Interface for backend patient data
interface BackendPatientData {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  phone_numbers?: string;
  address?: string;
  date_of_birth?: string;
  birth_date?: string;
  age?: number;
  gender?: string;
  cin?: string;
  social_security?: string;
  socialSecurity?: string;
  etat_civil?: string;
  etatCivil?: string;
  etat_aganda?: string;
  etatAgenda?: string;
  doctor_assigned?: string;
  docteur?: string;
  title?: string;
  notes?: string;
  comment?: string;
  agenda?: string;
  is_active?: boolean;
}

import { mdiPowerSocketUs } from '@mdi/js';
// Helper function to convert consultation type label to EventType
const getEventTypeFromLabel = (label: string): EventType | undefined => {
  const mapping = [
    { value: "Visite de malade", eventType: "visit" as EventType },
    { value: "Visitor Counter", eventType: "visitor-counter" as EventType },
    { value: "Completed", eventType: "completed" as EventType },
    { value: "Consultation", eventType: "Consultation" as EventType },
    { value: "Contrôle", eventType: "Contrôle" as EventType },
    { value: "Urgence", eventType: "Urgence" as EventType },
    { value: "Re-diagnose", eventType: "Re-diagnose" as EventType },
    { value: "Autre", eventType: "Autre" as EventType },
    { value: "diagnosis", eventType: "diagnosis" as EventType },
  ];

  return mapping.find(item => item.value === label)?.eventType;
};

// Type spécifique pour le formulaire de rendez-vous
interface AppointmentFormValues {
  patientId: string;
  notes: string;
  date: Date;
  duration: number;
  type: string;
  resourceId: number;
  addToWaitingList: boolean;
  removeFromCalendar: boolean;
  rescheduleDateTime: string;
}

interface AjouterUnRendezVousProps {
  opened: boolean;
  onClose: () => void;
  appointmentForm: ReturnType<typeof import('@mantine/form').useForm<AppointmentFormValues>>;
  handleSubmit: (values: AppointmentFormValues) => void;
  eventTitle: string;
  setEventTitle: (value: string) => void;
  titleOptions: TitleOption[];
  setTitleOptions: (options: TitleOption[]) => void;
  newTitle: string;
  setNewTitle: (value: string) => void;
  patientName: string;
  setPatientName: (value: string) => void;
  patientlastName: string;
  setPatientlastName: (value: string) => void;
  openListDesPatient: () => void;
  // New props for patient selection
  allPatients?: BackendPatientData[];
  onPatientSelect?: (patient: BackendPatientData) => void;
  isLoadingPatients?: boolean;
  eventDateDeNaissance: string;
  handleDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  eventAge: number | null;
  genderOption: string;
  handleOptionChange: (value: string) => void;
  eventEtatCivil: string;
  setEventEtatCivil: (value: string) => void;
  eventCin: string;
  setEventCin: (value: string) => void;
  address: string;
  setAddress: (value: string) => void;
  eventTelephone: string;
  setEventTelephone: (value: string) => void;
  email: string;
  setEmail: (value: string) => void;
  patientdoctor: string;
  setPatientDocteur: (value: string) => void;
  patientsocialSecurity: string;
  setSocialSecurity: (value: string) => void;
  consultationTypes: ConsultationType[];
  setConsultationTypes: (types: ConsultationType[]) => void;
  patienttypeConsultation: string;
  setPatientTypeConsultation: (value: string) => void;
  setEventType: (type: EventType) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
  dureeDeLexamen: string;
  getEventTypeColor: (type: EventType | undefined) => string;
  newConsultationType: string;
  setNewConsultationType: (value: string) => void;
  newConsultationColor: string;
  setNewConsultationColor: (value: string) => void;
  ColorPickeropened: boolean;
  openedColorPicker: () => void;
  closeColorPicker: () => void;
  changeEndValue: string;
  setChangeEndValue: (value: string) => void;
  setDureeDeLexamen: (value: string) => void;
  eventAganda: string;
  setEventAganda: (value: string) => void;
  agendaTypes: AgendaType[];
  setAgendaTypes: (types: AgendaType[]) => void;
  newAgendaType: string;
  setNewAgendaType: (value: string) => void;
  isWaitingList: boolean;
  eventDate: string;
  setEventDate: (value: string) => void;
  eventTime: string;
  setEventTime: (value: string) => void;
  eventConsultation: string;
  openListRendezVous: () => void;
  ListRendezVousOpened: boolean;
  closeListRendezVous: () => void;
  patientcomment: string;
  setPatientcomment: (value: string) => void;
  patientnotes: string;
  setPatientNotes: (value: string) => void;
  patientcommentairelistedattente: string;
  setPatientCommentairelistedattente: (value: string) => void;
  eventResourceId: number;
  setEventResourceId: (value: number) => void;
  eventType: EventType;
  checkedAppelvideo: boolean;
  handleAppelvideoChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checkedRappelSms: boolean;
  handleRappelSmsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checkedRappelEmail: boolean;
  handleRappelEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  currentPatient: Patient | null;
  waitingList: Patient[];
  setWaitingList: (list: Patient[]) => void;
  setPatientModalOpen: (open: boolean) => void;
  notifications: typeof import('@mantine/notifications').notifications;
  // New props for Edit Modal
  showEditModal: boolean;
  setShowEditModal: (show: boolean) => void;
  selectedEvent: AppointmentEvent | null;
  setSelectedEvent: (event: AppointmentEvent | null) => void;
  resetForm: () => void;
  handleEditSubmit: (e: React.FormEvent) => void;
  closeRendezVous: () => void;
  initialConsultationTypes: ConsultationType[];
}

// Composant RendezVousSelector
interface RendezVousSelectorProps {
  onClose: () => void;
}

const RendezVousSelector: React.FC<RendezVousSelectorProps> = ({ onClose }) => {
  const [selectedPeriod, setSelectedPeriod] = React.useState<'15days' | '1month' | '3months'>('15days');
  const [, setStartDate] = React.useState('12/06/2025');
  const [duration, setDuration] = React.useState(30);
  const [numberOfDays, setNumberOfDays] = React.useState(3);
  const [selectedSlots, setSelectedSlots] = React.useState<Set<string>>(new Set());

  // Générer les créneaux horaires
  const generateTimeSlots = () => {
    const slots = [];
    const startHour = 8;
    const endHour = 14;
    
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const endMinute = minute + 30;
        const endHour = endMinute >= 60 ? hour + 1 : hour;
        const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;
        const endTime = `${endHour.toString().padStart(2, '0')}:${adjustedEndMinute.toString().padStart(2, '0')}`;
        
        slots.push({
          id: `${hour}-${minute}`,
          startTime,
          endTime,
        });
      }
    }
    return slots;
  };

  // Calculer la date selon la période sélectionnée
  const getDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12 juin 2025';
      case '1month':
        return '26 juin 2025';
      case '3months':
        return '10 juillet 2025';
      default:
        return '12 juin 2025';
    }
  };

  // Calculer la date de début selon la période
  const getStartDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12/06/2025';
      case '1month':
        return '25/06/2025';
      case '3months':
        return '10/07/2025';
      default:
        return '12/06/2025';
    }
  };

  // Calculer la date formatée selon la période
  const getFormattedDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12/06/2025';
      case '1month':
        return '26/06/2025';
      case '3months':
        return '10/07/2025';
      default:
        return '12/06/2025';
    }
  };

  const timeSlots = generateTimeSlots();

  const handleSlotToggle = (slotId: string) => {
    const newSelectedSlots = new Set(selectedSlots);
    if (newSelectedSlots.has(slotId)) {
      newSelectedSlots.delete(slotId);
    } else {
      newSelectedSlots.add(slotId);
    }
    setSelectedSlots(newSelectedSlots);
  };

  const isValidateEnabled = selectedSlots.size > 0;

  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-4">
        <div className="p-4 bg-gray-50">
          <div className="space-y-4">
            <div>
              <Text size="sm" fw={500} mb="xs">À partir de</Text>
              <Select
                value={getStartDateForPeriod()}
                onChange={(value) => setStartDate(value || '')}
                data={[
                  { value: '12/06/2025', label: '12/06/2025' },
                  { value: '25/06/2025', label: '25/06/2025' },
                  { value: '10/07/2025', label: '10/07/2025' },
                  { value: '10/09/2025', label: '10/09/2025' },
                ]}
              />
            </div>

            <div>
              <Text size="sm" fw={500} mb="xs">Durée (min)</Text>
              <NumberInput
                value={duration}
                onChange={(value) => setDuration(Number(value))}
                min={15}
                max={120}
                step={15}
              />
            </div>

            <div>
              <Text size="sm" fw={500} mb="xs">Nbre des jours</Text>
              <NumberInput
                value={numberOfDays}
                onChange={(value) => setNumberOfDays(Number(value))}
                min={1}
                max={30}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-8">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <Text size="lg" fw={600}>{getDateForPeriod()}</Text>
            <Text size="sm" color="dimmed">24</Text>
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {timeSlots.map((slot) => (
              <div key={slot.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div className="flex items-center space-x-2">
                  <Text size="sm">le</Text>
                  <Text size="sm" color="blue" fw={500}>
                    {getFormattedDateForPeriod()}
                  </Text>
                  <Text size="sm">de</Text>
                  <Text size="sm" color="red" fw={500}>
                    {slot.startTime}
                  </Text>
                  <Text size="sm">à</Text>
                  <Text size="sm" color="green" fw={500}>
                    {slot.endTime}
                  </Text>
                </div>
                <input
                  type="checkbox"
                  checked={selectedSlots.has(slot.id)}
                  onChange={() => handleSlotToggle(slot.id)}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-6 pt-4 border-t">
            <div className="flex space-x-4">
              <Button
                variant={selectedPeriod === '15days' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('15days')}
                size="sm"
              >
                15 jours
              </Button>
              <Button
                variant={selectedPeriod === '1month' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('1month')}
                size="sm"
              >
                1 Mois
              </Button>
              <Button
                variant={selectedPeriod === '3months' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('3months')}
                size="sm"
              >
                3 Mois
              </Button>
            </div>

            <div className="flex space-x-2">
              <Button
                color={isValidateEnabled ? 'blue' : 'gray'}
                disabled={!isValidateEnabled}
                onClick={() => {
                  // Logique de validation ici
                  onClose();
                }}
              >
                Valider
              </Button>
              <Button
                variant="outline"
                color="red"
                onClick={onClose}
              >
                Annuler
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AjouterUnRendezVous: React.FC<AjouterUnRendezVousProps> = (props) => {
  // État pour gérer le mode Pause
  const [isPauseMode, setIsPauseMode] = React.useState(false);
  const [pauseModalOpened, setPauseModalOpened] = React.useState(false);

  const {
    opened,
    onClose,
    appointmentForm,
    handleSubmit,
    eventTitle,
    setEventTitle,
    titleOptions,
    setTitleOptions,
    newTitle,
    setNewTitle,
    patientName,
    setPatientName,
    patientlastName,
    setPatientlastName,
    openListDesPatient,
    // New props for patient selection
    allPatients = [],
    onPatientSelect,
    isLoadingPatients = false,
    eventDateDeNaissance,
    handleDateChange,
    eventAge,
    genderOption,
    handleOptionChange,
    eventEtatCivil,
    setEventEtatCivil,
    eventCin,
    setEventCin,
    address,
    setAddress,
    eventTelephone,
    setEventTelephone,
    email,
    setEmail,
    patientdoctor,
    setPatientDocteur,
    patientsocialSecurity,
    setSocialSecurity,
    consultationTypes,
    setConsultationTypes,
    patienttypeConsultation,
    setPatientTypeConsultation,
    setEventType,
    searchValue,
    setSearchValue,
    dureeDeLexamen,
    getEventTypeColor,
    newConsultationType,
    setNewConsultationType,
    newConsultationColor,
    setNewConsultationColor,
    ColorPickeropened,
    openedColorPicker,
    closeColorPicker,
    changeEndValue,
    setChangeEndValue,
    setDureeDeLexamen,
    eventAganda,
    setEventAganda,
    agendaTypes,
    setAgendaTypes,
    newAgendaType,
    setNewAgendaType,
    isWaitingList,
    eventDate,
    setEventDate,
    eventTime,
    setEventTime,
    eventConsultation,
    openListRendezVous,
    ListRendezVousOpened,
    closeListRendezVous,
    patientcomment,
    setPatientcomment,
    patientnotes,
    setPatientNotes,
    patientcommentairelistedattente,
    setPatientCommentairelistedattente,
    eventResourceId,
    setEventResourceId,
    eventType,
    checkedAppelvideo,
    handleAppelvideoChange,
    checkedRappelSms,
    handleRappelSmsChange,
    checkedRappelEmail,
    handleRappelEmailChange,
    currentPatient,
    waitingList,
    setWaitingList,
    setPatientModalOpen,
    // New props for Edit Modal
    showEditModal,
    setShowEditModal,
    //selectedEvent,
    //setSelectedEvent,
    resetForm,
    handleEditSubmit,
    closeRendezVous,
    initialConsultationTypes,
  } = props;

  return (
    <>
    <Modal.Root
      opened={opened}
      onClose={onClose}
      size="70%"
    >
      <Modal.Overlay />
      <Modal.Content className="overflow-y-hidden">
        <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
          <Modal.Title>
            <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
              >
                <g
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                >
                  <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                  <circle cx={16} cy={16} r={6}></circle>
                </g>
              </svg>
              Ajouter un rendez-vous 
            </Text>
            <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              Remplissez les détails ci-dessous pour ajouter un nouvel événement.
            </p>
          </Modal.Title>
          <Group justify="flex-end">
            <Switch
              checked={isPauseMode}
              onChange={(event) => {
                setIsPauseMode(event.currentTarget.checked);
                if (event.currentTarget.checked) {
                  setPauseModalOpened(true);
                }
              }}
              color="teal"
              size="xs"
            />
            <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              Pause
            </p>
            <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
          </Group>
        </Modal.Header>

        <Modal.Body style={{ padding: '0px' }}>
          <div className="py-2 pl-4 h-[600px]">
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-4">
                <form onSubmit={(e) => { e.preventDefault(); handleSubmit(appointmentForm.values); }}>
                  <div className="grid gap-3 py-2 pr-4">
                    {/* Titre, Nom, Prénom */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        value={eventTitle}
                        onChange={(value) => setEventTitle(value ?? "")}
                        placeholder="Titre"
                        data={titleOptions}
                        className="w-full"
                          leftSection={<span className="">
                        <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg></span>}
                        />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                            placeholder="Ajouter des titres"
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newTitle.trim()) {
                                    const newTitleOption = { value: newTitle, label: newTitle };
                                    setTitleOptions([...titleOptions, newTitleOption]);
                                    setEventTitle(newTitle);
                                    setNewTitle("");
                                    notifications.show({
                                      title: 'Titre ajouté',
                                      message: `"${newTitle}" a été ajouté à la liste des titres`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newTitle.trim()}
                              >
                                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newTitle.trim()) {
                                const newTitleOption = { value: newTitle, label: newTitle };
                                setTitleOptions([...titleOptions, newTitleOption]);
                                setEventTitle(newTitle);
                                setNewTitle("");
                                notifications.show({
                                  title: 'Titre ajouté',
                                  message: `"${newTitle}" a été ajouté à la liste des titres`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>

                      {/* Patient Selection Autocomplete */}
                      {allPatients && allPatients.length > 0 && (
                        <Autocomplete
                          label="Sélectionner un patient existant"
                          placeholder={isLoadingPatients ? "Chargement des patients..." : "Rechercher un patient..."}
                          data={allPatients.map((patient: BackendPatientData) =>
                            `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}`
                          )}
                          onOptionSubmit={(value) => {
                            // Find the patient based on the selected value
                            const selectedPatient = allPatients.find((patient: BackendPatientData) =>
                              `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}` === value
                            );
                            if (selectedPatient && onPatientSelect) {
                              onPatientSelect(selectedPatient);
                            }
                          }}
                          disabled={isLoadingPatients}
                          leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                          className="mb-2"
                          limit={10}
                        />
                      )}

                      <TextInput
                        id="event-nom"
                        placeholder="Nom *"
                        type="text"
                        value={patientName}
                        onChange={(e) => setPatientName(e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                      />
                      <TextInput
                        id="event-prenom"
                        placeholder="Prénom *"
                        type="text"
                        value={patientlastName}
                        onChange={(e) => setPatientlastName(e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<Icon path={mdiAccountCheckOutline} size={0.75} />}
                      />
                      <Avatar color="#4BA3D3" radius="sm" h={36} onClick={openListDesPatient}>
                        <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="20" width="20" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 5h2v3h10V5h2v5h2V5c0-1.1-.9-2-2-2h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h5v-2H5V5zm7-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"></path><path d="M20.3 18.9c.4-.7.7-1.5.7-2.4 0-2.5-2-4.5-4.5-4.5S12 14 12 16.5s2 4.5 4.5 4.5c.9 0 1.7-.3 2.4-.7l2.7 2.7 1.4-1.4-2.7-2.7zm-3.8.1c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5z"></path></svg>
                      </Avatar>
                    </div>       
           <div className="flex gap-4 mb-2">
                       <TextInput
                        type="date"
                        placeholder="Date de Naissance..."
                        id="event-dateDeNaissance"
                        value={eventDateDeNaissance}
                        onChange={handleDateChange}
                        required
                        className="input input-bordered  w-full"
                      />
                      <Avatar
                    color="#4BA3D3"
                    radius="sm"
                    h={36}
                    style={{
                      opacity: 0.5,
                      cursor: 'not-allowed',
                      pointerEvents: 'none',
                    }}
                  >
                    <Icon path={mdiPowerSocketUs} size={1} />
                  </Avatar>

                      <TextInput
                        type="text"
                        id="event-age"
                        value={eventAge?.toString() ?? ""}
                        placeholder={
                          eventAge !== null
                            ? eventAge.toString()
                            : "Veuillez entrer votre date de naissance"
                        }
                        readOnly
                        className="input input-bordered  w-full "
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 16 1.25 L 15.1875 2.4375 C 15.1875 2.4375 14.648438 3.191406 14.125 4.09375 C 13.863281 4.546875 13.617188 5.019531 13.40625 5.5 C 13.195313 5.980469 13 6.421875 13 7 C 13 8.644531 14.355469 10 16 10 C 17.644531 10 19 8.644531 19 7 C 19 6.421875 18.804688 5.980469 18.59375 5.5 C 18.382813 5.019531 18.136719 4.546875 17.875 4.09375 C 17.351563 3.191406 16.8125 2.4375 16.8125 2.4375 Z M 16 10 L 13 10 L 13 14 L 7 14 C 4.789063 14 3 15.789063 3 18 C 3 19.015625 3.375 19.949219 4 20.65625 L 4 28 L 28 28 L 28 20.65625 C 28.625 19.949219 29 19.015625 29 18 C 29 15.789063 27.210938 14 25 14 L 19 14 L 19 10 Z M 16 4.875 C 16.066406 4.984375 16.058594 4.976563 16.125 5.09375 C 16.363281 5.503906 16.617188 5.941406 16.78125 6.3125 C 16.945313 6.683594 17 7.027344 17 7 C 17 7.554688 16.554688 8 16 8 C 15.445313 8 15 7.554688 15 7 C 15 7.027344 15.054688 6.683594 15.21875 6.3125 C 15.382813 5.941406 15.636719 5.503906 15.875 5.09375 C 15.941406 4.976563 15.933594 4.984375 16 4.875 Z M 15 12 L 17 12 L 17 14 L 15 14 Z M 7 16 L 25 16 C 26.191406 16 27 16.808594 27 18 C 27 19.191406 26.191406 20 25 20 C 23.808594 20 23 19.191406 23 18 L 21 18 C 21 19.191406 20.191406 20 19 20 C 17.808594 20 17 19.191406 17 18 L 15 18 C 15 19.191406 14.191406 20 13 20 C 11.808594 20 11 19.191406 11 18 L 9 18 C 9 19.191406 8.191406 20 7 20 C 5.808594 20 5 19.191406 5 18 C 5 16.808594 5.808594 16 7 16 Z M 10 20.65625 C 10.734375 21.484375 11.804688 22 13 22 C 14.195313 22 15.265625 21.484375 16 20.65625 C 16.734375 21.484375 17.804688 22 19 22 C 20.195313 22 21.265625 21.484375 22 20.65625 C 22.734375 21.484375 23.804688 22 25 22 C 25.347656 22 25.679688 21.925781 26 21.84375 L 26 26 L 6 26 L 6 21.84375 C 6.320313 21.925781 6.652344 22 7 22 C 8.195313 22 9.265625 21.484375 10 20.65625 Z"></path></svg>}
                      />
                      <Radio.Group
                          value={genderOption}
                          onChange={handleOptionChange}
                        >
                          <div className="flex items-center space-x-4 mt-2">
                            <Radio key="homme" value="Homme" label="Homme" />
                            <Radio key="femme" value="Femme" label="Femme" />
                            <Radio key="enfant" value="Enfant" label="Enfant" />
                          </div>
                        </Radio.Group>
                     
                    </div>      

                    {/* État civil, CIN, Adresse */}
                    <div className="flex gap-4">
                      <Select
                        value={eventEtatCivil}
                        onChange={(value) => setEventEtatCivil(value ?? "")}
                        placeholder="État civil"
                        data={[
                          { value: "Célibataire", label: "Célibataire" },
                          { value: "Marié(e)", label: "Marié(e)" },
                          { value: "Divorcé(e)", label: "Divorcé(e)" },
                          { value: "Veuf(ve)", label: "Veuf(ve)" },
                          { value: "Autre chose", label: "Autre chose" },
                        ]}
                        className="select w-full max-w-xs"
                      />
                      <TextInput
                        placeholder="CIN"
                        disabled={genderOption === 'Enfant'}
                        value={eventCin}
                        onChange={(e) => setEventCin(e.target.value)}
                        styles={{
                          input: {
                            backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,
                            color: genderOption === 'Enfant' ? '#999' : undefined
                          }
                        }}
                        className="input input-bordered mb-2 w-full"
                        leftSection={
                        <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 17v-10l7 10v-10"></path><path d="M15 17h5"></path><path d="M17.5 10m-2.5 0a2.5 3 0 1 0 5 0a2.5 3 0 1 0 -5 0"></path></svg>}
                      />
                      <TextInput
                        id="Adresse"
                        placeholder="Adressé par"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 3 6 L 3 26 L 29 26 L 29 6 Z M 5 8 L 27 8 L 27 24 L 23.59375 24 C 23.515625 23.863281 23.550781 23.675781 23.4375 23.5625 C 23.058594 23.183594 22.523438 23 22 23 C 21.476563 23 20.941406 23.183594 20.5625 23.5625 C 20.449219 23.675781 20.484375 23.863281 20.40625 24 L 11.59375 24 C 11.515625 23.863281 11.550781 23.675781 11.4375 23.5625 C 11.058594 23.183594 10.523438 23 10 23 C 9.476563 23 8.941406 23.183594 8.5625 23.5625 C 8.449219 23.675781 8.484375 23.863281 8.40625 24 L 5 24 Z M 12 10 C 9.800781 10 8 11.800781 8 14 C 8 15.113281 8.476563 16.117188 9.21875 16.84375 C 7.886719 17.746094 7 19.285156 7 21 L 9 21 C 9 19.34375 10.34375 18 12 18 C 13.65625 18 15 19.34375 15 21 L 17 21 C 17 19.285156 16.113281 17.746094 14.78125 16.84375 C 15.523438 16.117188 16 15.113281 16 14 C 16 11.800781 14.199219 10 12 10 Z M 12 12 C 13.117188 12 14 12.882813 14 14 C 14 15.117188 13.117188 16 12 16 C 10.882813 16 10 15.117188 10 14 C 10 12.882813 10.882813 12 12 12 Z M 19 13 L 19 15 L 25 15 L 25 13 Z M 19 17 L 19 19 L 25 19 L 25 17 Z"></path></svg>}
                      />
                    </div>

                    {/* Téléphone, Email */}
                    <div className="flex gap-4">
                      <InputBase
                        id="Téléphone"
                        component={IMaskInput}
                        mask="00-00-00-00-00"
                        placeholder="Téléphone"
                        value={eventTelephone}
                        onAccept={(value) => setEventTelephone(value)}
                        unmask={true}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>}
                      />
                      <TextInput
                        id="Email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><g id="At"><path d="M12.09,21.925a9.846,9.846,0,0,1-3.838-.747A9.673,9.673,0,0,1,3.005,15.93,10.034,10.034,0,0,1,2.244,12a10.425,10.425,0,0,1,.695-3.8,9.606,9.606,0,0,1,2-3.169A9.269,9.269,0,0,1,8.1,2.862a10.605,10.605,0,0,1,4.175-.787,10.516,10.516,0,0,1,4.334.827A8.437,8.437,0,0,1,19.64,5.119a8.622,8.622,0,0,1,1.707,3.1,9.263,9.263,0,0,1,.377,3.487,5.809,5.809,0,0,1-1.3,3.6A3.6,3.6,0,0,1,17.7,16.473a3.628,3.628,0,0,1-2.162-.609,2.82,2.82,0,0,1-1.119-1.694l.5.106a2.582,2.582,0,0,1-1.3,1.3A4.37,4.37,0,0,1,11.746,16,3.681,3.681,0,0,1,9.88,15.54a3.2,3.2,0,0,1-1.237-1.271A3.843,3.843,0,0,1,8.2,12.4a3.88,3.88,0,0,1,.456-1.926A3.191,3.191,0,0,1,9.919,9.214a3.792,3.792,0,0,1,1.853-.443,4.716,4.716,0,0,1,1.767.364,2.622,2.622,0,0,1,1.383,1.3l-.5.5V9.461a.4.4,0,0,1,.4-.4h.232a.4.4,0,0,1,.4.4v3.518a2.723,2.723,0,0,0,.529,1.674,2.173,2.173,0,0,0,1.853.708,2.281,2.281,0,0,0,1.323-.41,2.938,2.938,0,0,0,.967-1.178,4.947,4.947,0,0,0,.437-1.852,9.439,9.439,0,0,0-.417-3.574A7.285,7.285,0,0,0,18.5,5.588a7.424,7.424,0,0,0-2.679-1.78,9.605,9.605,0,0,0-3.547-.622,9.041,9.041,0,0,0-3.758.741,8.252,8.252,0,0,0-2.773,2,8.8,8.8,0,0,0-1.72,2.838,9.27,9.27,0,0,0-.589,3.262,8.568,8.568,0,0,0,.682,3.408A8.951,8.951,0,0,0,6,18.24a8.707,8.707,0,0,0,2.785,1.892,8.515,8.515,0,0,0,3.389.682,9.851,9.851,0,0,0,2.679-.378,8.451,8.451,0,0,0,2-.831.4.4,0,0,1,.553.158l.1.192a.4.4,0,0,1-.141.526,9.832,9.832,0,0,1-2.391,1.04A10.5,10.5,0,0,1,12.09,21.925ZM11.8,14.859a2.469,2.469,0,0,0,1.786-.649,2.427,2.427,0,0,0,.675-1.839,2.414,2.414,0,0,0-.7-1.886A2.532,2.532,0,0,0,11.8,9.856a2.482,2.482,0,0,0-1.839.649,2.523,2.523,0,0,0-.65,1.866,2.4,2.4,0,0,0,.682,1.865A2.574,2.574,0,0,0,11.8,14.859Z"></path></g></svg>}
                      />
                    </div>

                    {/* Docteur, Sécurité sociale */}
                    <div className="flex gap-4">
                      <Select
                        value={patientdoctor}
                        onChange={(value) => setPatientDocteur(value ?? "")}
                        placeholder="Docteur"
                        data={[
                          { value: "Docteur", label: "Docteur" },
                          { value: "dr.Kader", label: "dr.Kader" },
                          { value: "dr.Kaders", label: "dr.Kaders" },
                        ]}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                      <Select
                        value={patientsocialSecurity || 'Aucune'}
                        onChange={(value) => setSocialSecurity(value || 'Aucune')}
                        placeholder="Sécurité sociale"
                        data={[
                          { value: "Aucune", label: "Aucune" },
                          { value: "CNSS", label: "CNSS" },
                          { value: "AMO", label: "AMO" },
                        ]}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M4 5c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2zm4.78 3.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 2 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zM18 7c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2.78 1.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 14 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zm-2.77 4.43-1.41 1.41L18.17 16H5.83l1.58-1.59L6 13l-4 4 3.99 3.99 1.41-1.41L5.83 18h12.34l-1.58 1.58L18 20.99 22 17l-3.99-3.99z"></path></svg>}
                      />
                    </div>

                    {/* Type de consultation */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        label="Type de consultation"
                        placeholder="Rechercher ou saisir..."
                        data={consultationTypes}
                        value={patienttypeConsultation}
                        onChange={(value) => {
                          setPatientTypeConsultation(value ?? "");
                          const selectedLabel = [
                            { value: "Visite de malade", eventType: "visit" },
                            { value: "Visitor Counter", eventType: "visitor-counter" },
                            { value: "Completed", eventType: "completed" },
                          ].find(item => item.value === value);

                          if (selectedLabel) {
                            setEventType(selectedLabel.eventType as EventType);
                          }
                        }}
                        searchable
                        searchValue={searchValue}
                        onSearchChange={setSearchValue}
                        clearable
                        maxDropdownHeight={280}
                        rightSectionWidth={70}
                        required
                        rightSection={
                          <span className="bg-[#4CAF50] text-white px-2 py-1 rounded text-xs">{dureeDeLexamen}</span>
                        }
                        allowDeselect
                        className="w-full"
                      />
                      <Menu width={260} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <div className="flex">
                            <TextInput
                              leftSectionPointerEvents="none"
                              leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                              placeholder="Ajouter des Consultation"
                              value={newConsultationType}
                              onChange={(e) => setNewConsultationType(e.target.value)}
                              rightSection={
                                <ActionIcon
                                  size="sm"
                                  onClick={() => {
                                    if (newConsultationType.trim()) {
                                      const newType = {
                                        value: newConsultationType,
                                        label: newConsultationType,
                                        duration: dureeDeLexamen || "15 min"
                                      };
                                      setConsultationTypes([...consultationTypes, newType]);
                                      setPatientTypeConsultation(newConsultationType);
                                      setNewConsultationType("");
                                      notifications.show({
                                        title: 'Type de consultation ajouté',
                                        message: `"${newConsultationType}" a été ajouté`,
                                        color: 'green',
                                        autoClose: 2000
                                      });
                                    }
                                  }}
                                  disabled={!newConsultationType.trim()}
                                >
                                  <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                                </ActionIcon>
                              }
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && newConsultationType.trim()) {
                                  const newType = {
                                    value: newConsultationType,
                                    label: newConsultationType,
                                    duration: dureeDeLexamen || "15 min"
                                  };
                                  setConsultationTypes([...consultationTypes, newType]);
                                  setPatientTypeConsultation(newConsultationType);
                                  setNewConsultationType("");
                                  notifications.show({
                                    title: 'Type de consultation ajouté',
                                    message: `"${newConsultationType}" a été ajouté`,
                                    color: 'green',
                                    autoClose: 2000
                                  });
                                }
                              }}
                            />
                            <Avatar
                              color={newConsultationColor}
                              radius="sm"
                              ml={4}
                              h={36}
                              onClick={openedColorPicker}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
                                style={{width: "26px", height:"26px"}}
                              >
                                <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path>
                              </svg>
                            </Avatar>
                          </div>
                        </Menu.Dropdown>
                      </Menu>
                      <Modal opened={ColorPickeropened} onClose={closeColorPicker} size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
                        <ColorPicker
                          defaultValue={newConsultationColor}
                          value={newConsultationColor}
                          onChange={setNewConsultationColor}
                          onChangeEnd={setChangeEndValue}
                          format="hex"
                          swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']}
                        />
                        <Group justify="center" mt={8}>
                          <Button
                            variant="filled"
                            w={"100%"}
                            color={`${newConsultationColor}`}
                            leftSection={<IconColorPicker stroke={1} size={18} />}
                            onClick={() => {
                              setNewConsultationColor(changeEndValue);
                              closeColorPicker();
                            }}
                          >
                            Sélectionner cette couleur
                          </Button>
                        </Group>
                      </Modal>

                      <Select
                        label="Durée"
                        value={dureeDeLexamen}
                        onChange={(value) => setDureeDeLexamen(value ?? "")}
                        placeholder="15 min"
                        data={["10 min", "15 min", "20 min","25 min","30 min", "35 min" ,"40 min","45 min"]}
                        className="select w-full max-w-xs"
                      />
                      <Select
                        label="Agenda"
                        value={eventAganda}
                        onChange={(value) => setEventAganda(value ?? "")}
                        placeholder="Ajouter des Agenda"
                        data={agendaTypes}
                        className="w-full"
                        leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                            placeholder="Ajouter des Agenda"
                            value={newAgendaType}
                            onChange={(e) => setNewAgendaType(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newAgendaType.trim()) {
                                    const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                    setAgendaTypes([...agendaTypes, newAgendaOption]);
                                    setEventAganda(newAgendaType);
                                    setNewAgendaType("");
                                    notifications.show({
                                      title: 'Agenda ajouté',
                                      message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newAgendaType.trim()}
                              >
                                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm316 140c0-6.6-5.4-12-12-12h-60v-60c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v60h-60c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h60v60c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-60h60c6.6 0 12-5.4 12-12v-40z"></path></svg>
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newAgendaType.trim()) {
                                const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                setAgendaTypes([...agendaTypes, newAgendaOption]);
                                setEventAganda(newAgendaType);
                                setNewAgendaType("");
                                notifications.show({
                                  title: 'Agenda ajouté',
                                  message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>
                    </div>

                    {/* Date et heure du RDV */}
                    {!isWaitingList && !appointmentForm.values.addToWaitingList && (
                      <div className="mx-auto flex gap-4 mb-2">
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>Date du RDV</Text>
                        <TextInput
                          id="event-date"
                          type="date"
                          value={eventDate}
                          onChange={(e) => setEventDate(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>De*</Text>
                        <TextInput
                          id="event-time"
                          type="time"
                          value={eventTime}
                          onChange={(e) => setEventTime(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>à*</Text>
                        <TextInput
                          id="event-time-end"
                          type="text"
                          placeholder={
                            eventTime !== null
                              ? moment(eventTime, "HH:mm")
                                  .add(parseInt(eventConsultation), "minutes")
                                  .format("HH:mm")
                              : "Please enter your date of birth"
                          }
                          readOnly
                          className="input input-bordered mb-2 w-40"
                        />
                        <Avatar color="#4BA3D3" radius="sm" h={36}>
                          <IconTextPlus stroke={2} size={30} className="text-[#3799CE] cursor-pointer"
                            onClick={openListRendezVous}
                          />
                        </Avatar>

                        <Modal
                          opened={ListRendezVousOpened}
                          onClose={closeListRendezVous}
                          size="xl"
                          centered
                          withCloseButton={false}
                        >
                          <RendezVousSelector onClose={closeListRendezVous} />
                        </Modal>
                      </div>
                    )}

                    {/* Commentaires */}
                    <div className="flex gap-4 mb-2 -mt-2 pr-4">
                      <Textarea
                        id="event-Commentaire"
                        value={patientcomment}
                        onChange={(event) => setPatientcomment(event.currentTarget.value ?? "")}
                        placeholder="Commentaire ..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                      <Textarea
                        id="event-Notes"
                        value={patientnotes}
                        onChange={(event) => setPatientNotes(event.currentTarget.value ?? "")}
                        placeholder="Notes ..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                      <Textarea
                        id="event-Commentairelistedattente"
                        value={patientcommentairelistedattente}
                        onChange={(event) => setPatientCommentairelistedattente(event.currentTarget.value ?? "")}
                        placeholder="Commentaire (liste d'attente)..."
                        className="w-full"
                        rightSection={<Icon path={mdiMicrophone} size={1} />}
                      />
                    </div>

                 
             <Group justify="space-between">        
       <div className="flex items-center space-x-1">
                          <Select
                            value={eventResourceId ? eventResourceId.toString() : ""}
                            onChange={(value) => {
                              setEventResourceId(Number(value) || 1);
                            }}
                            name="resourceId"
                            placeholder="Room"
                            data={[
                              { value: "1", label: "Room A" },
                              { value: "2", label: "Room B" },
                            ]}
                            required
                            className="select w-[370px] "
                            leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0V0z"></path><path d="M20 4v16H4V4h16m0-2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-3.5 8.67V9c0-1.1-.9-2-2-2h-5c-1.1 0-2 .9-2 2v1.67c-.88.35-1.5 1.2-1.5 2.2V17h1.5v-1.5h9V17H18v-4.13c0-1-.62-1.85-1.5-2.2zM15 8.5v2H9v-2h6zm-7.5 4.37c0-.48.39-.87.87-.87h7.27c.48 0 .87.39.87.87V14h-9v-1.13H7.5z"></path></svg>}
                          />
                        </div>
      
          <Group justify="space-between">

                        <div className="flex items-center space-x-1">
                          <input
                            id="visit"
                            type="radio"
                            name="eventType"
                            value="visit"
                            className="peer hidden"
                            checked={eventType === "visit"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="visit"
                            className={`${
                              eventType === "visit"
                                ? "peer-checked:text-[#34D1BF]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visite de malade
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="visitor-counter"
                            type="radio"
                            name="eventType"
                            value="visitor-counter"
                            className="peer hidden"
                            checked={eventType === "visitor-counter"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="visitor-counter"
                            className={`${
                              eventType === "visitor-counter"
                                ? "peer-checked:text-[#F17105]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visitor Counter
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="completed"
                            type="radio"
                            name="eventType"
                            value="completed"
                            className="peer hidden"
                            checked={eventType === "completed"}
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="completed"
                            className={`${
                              eventType === "completed"
                                ? "peer-checked:text-[#3799CE]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Completed
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="diagnosis"
                            type="radio"
                            name="eventType"
                            value="diagnosis"
                            checked={eventType === "diagnosis"}
                            className="peer hidden"
                            onChange={(e) => setEventType(e.target.value as EventType)}
                          />
                          <label
                            htmlFor="diagnosis"
                            className={`${
                              eventType === "diagnosis"
                                ? "peer-checked:text-[#F3124E]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Re-diagnose
                            </li>
                          </label>
                        </div>
                        </Group> 
    </Group>
                    {/* Switches et boutons */}
                  
                       <Group justify="space-between" mt={'-4px'}>
        <Group gap="xs">
                        <Switch
                          color="teal"
                          size="xs"
                          label="Add to Waiting List"
                          checked={appointmentForm.values.addToWaitingList}
                          onChange={(event) => {
                            appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);
                            appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);
                          }}
                          thumbIcon={
                            appointmentForm.values.addToWaitingList ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />

                        <Switch
                          checked={checkedAppelvideo}
                          onChange={handleAppelvideoChange}
                          color="teal"
                          size="xs"
                          label="Appel video"
                          thumbIcon={
                            checkedAppelvideo ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={checkedRappelSms}
                          onChange={handleRappelSmsChange}
                          color="teal"
                          size="xs"
                          label="Rappel Sms"
                          thumbIcon={
                            checkedRappelSms ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={checkedRappelEmail}
                          onChange={handleRappelEmailChange}
                          color="teal"
                          size="xs"
                          label="Rappel e-mail"
                          thumbIcon={
                            checkedRappelEmail ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                      </Group>
     
       <Group >
                      <Button
                        type="submit"
                        className=" mb-2 btn-Enr"
                        onClick={() => {
                          onClose();
                        }}
                      >
                        {currentPatient ? "Enregistrer" : "Ajouter"}
                      </Button>
                      {currentPatient && (
                        <Button
                          color="red"
                          onClick={() => {
                            if (currentPatient) {
                              setWaitingList(waitingList.filter(p => p.id !== currentPatient.id));
                              setPatientModalOpen(false);
                            }
                          }}
                          className="mb-2 btn-Supp"
                        >
                          Supprimer
                        </Button>
                      )}
                      <Button
                        onClick={() => {
                          onClose();
                        }}
                        className="mb-2 btn-Ann"
                      >
                        Annuler
                      </Button>
                      </Group>
    </Group>
                  </div>
                </form>
              </div>
            </SimpleBar>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>

    {/* Edit Patient Modal */}
    {showEditModal && (
      <Modal.Root
        opened={true}
        onClose={() => {
          resetForm();
          setShowEditModal(false);
        }}
        size="70%"
      >
        {/* <Modal.Overlay /> */}
        <Modal.Content className="overflow-y-hidden">
          <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  >
                    <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                    <circle cx={16} cy={16} r={6}></circle>
                  </g>
                </svg>
                Modifier rendez-vous
              </Text>
              <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
                Modifiez les détails de l&apos;événement ci-dessous
              </p>
            </Modal.Title>
            <Modal.CloseButton className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]" />
          </Modal.Header>
          <Modal.Body style={{ padding: '0px' }}>
            <div className="py-2 pl-4 h-[600px]">
              <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                <div className="pr-4">
                  <form onSubmit={handleEditSubmit}>
                    <div className="grid gap-3 py-2 pr-4">
                      <div className="flex gap-4 mb-2">
                        <Select
                          value={eventTitle}
                          onChange={(value) => setEventTitle(value ?? "")}
                          placeholder="Titre"
                          data={[
                            { value: "Titre1", label: "Titre1" },
                            { value: "Titre2", label: "Titre2" },
                            { value: "Titre3", label: "Titre3" },
                          ]}
                          className="w-full"
                           leftSection={<span className="">
                        <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg></span>}
                        />

                        {/* Patient Selection Autocomplete */}
                        {allPatients && allPatients.length > 0 && (
                          <Autocomplete
                            label="Sélectionner un patient existant"
                            placeholder={isLoadingPatients ? "Chargement des patients..." : "Rechercher un patient..."}
                            data={allPatients.map((patient: BackendPatientData) =>
                              `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}`
                            )}
                            onOptionSubmit={(value) => {
                              // Find the patient based on the selected value
                              const selectedPatient = allPatients.find((patient: BackendPatientData) =>
                                `${patient.first_name} ${patient.last_name} - ${patient.email || 'Pas d\'email'}` === value
                              );
                              if (selectedPatient && onPatientSelect) {
                                onPatientSelect(selectedPatient);
                              }
                            }}
                            disabled={isLoadingPatients}
                            leftSection={<Icon path={mdiAccountCheckOutline} size={1} />}
                            className="mb-2"
                            limit={10}
                          />
                        )}

                        <TextInput
                          id="event-nom"
                          placeholder="Nom *"
                          type="text"
                          value={patientName}
                          onChange={(e) => setPatientName(e.target.value)}
                          required
                          className="input input-bordered w-full"
                          leftSection={<Icon path={mdiAccountCheckOutline} size={1} />}
                        />
                        <TextInput
                          id="event-prenom"
                          placeholder="Prénom *"
                          type="text"
                          value={patientlastName}
                          onChange={(e) => setPatientlastName(e.target.value)}
                          required
                          className="input input-bordered w-full"
                          leftSection={<Icon path={mdiAccountCheckOutline} size={1} />}
                        />
                        <Avatar color="#4BA3D3" radius="sm" h={36} onClick={openListDesPatient}>
                          <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="200px" width="200px" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 5h2v3h10V5h2v5h2V5c0-1.1-.9-2-2-2h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h5v-2H5V5zm7-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"></path><path d="M20.3 18.9c.4-.7.7-1.5.7-2.4 0-2.5-2-4.5-4.5-4.5S12 14 12 16.5s2 4.5 4.5 4.5c.9 0 1.7-.3 2.4-.7l2.7 2.7 1.4-1.4-2.7-2.7zm-3.8.1c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5z"></path></svg>
                        </Avatar>
                      </div>

                      <div className="flex gap-4 mb-2">
                        <TextInput
                          type="date"
                          placeholder="Date de Naissance..."
                          id="event-birth_date"
                          value={eventDateDeNaissance}
                          onChange={handleDateChange}
                          required
                          className="input input-bordered max-w-[278px] w-full"
                        />
                        <TextInput
                          type="text"
                          id="event-age"
                          value={eventAge?.toString() ?? ""}
                          placeholder={
                            eventAge !== null
                              ? eventAge.toString()
                              : "Veuillez entrer votre date de naissance"
                          }
                          readOnly
                          className="input input-bordered max-w-[278px] w-full"
                          leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 16 1.25 L 15.1875 2.4375 C 15.1875 2.4375 14.648438 3.191406 14.125 4.09375 C 13.863281 4.546875 13.617188 5.019531 13.40625 5.5 C 13.195313 5.980469 13 6.421875 13 7 C 13 8.644531 14.355469 10 16 10 C 17.644531 10 19 8.644531 19 7 C 19 6.421875 18.804688 5.980469 18.59375 5.5 C 18.382813 5.019531 18.136719 4.546875 17.875 4.09375 C 17.351563 3.191406 16.8125 2.4375 16.8125 2.4375 Z M 16 10 L 13 10 L 13 14 L 7 14 C 4.789063 14 3 15.789063 3 18 C 3 19.015625 3.375 19.949219 4 20.65625 L 4 28 L 28 28 L 28 20.65625 C 28.625 19.949219 29 19.015625 29 18 C 29 15.789063 27.210938 14 25 14 L 19 14 L 19 10 Z M 16 4.875 C 16.066406 4.984375 16.058594 4.976563 16.125 5.09375 C 16.363281 5.503906 16.617188 5.941406 16.78125 6.3125 C 16.945313 6.683594 17 7.027344 17 7 C 17 7.554688 16.554688 8 16 8 C 15.445313 8 15 7.554688 15 7 C 15 7.027344 15.054688 6.683594 15.21875 6.3125 C 15.382813 5.941406 15.636719 5.503906 15.875 5.09375 C 15.941406 4.976563 15.933594 4.984375 16 4.875 Z M 15 12 L 17 12 L 17 14 L 15 14 Z M 7 16 L 25 16 C 26.191406 16 27 16.808594 27 18 C 27 19.191406 26.191406 20 25 20 C 23.808594 20 23 19.191406 23 18 L 21 18 C 21 19.191406 20.191406 20 19 20 C 17.808594 20 17 19.191406 17 18 L 15 18 C 15 19.191406 14.191406 20 13 20 C 11.808594 20 11 19.191406 11 18 L 9 18 C 9 19.191406 8.191406 20 7 20 C 5.808594 20 5 19.191406 5 18 C 5 16.808594 5.808594 16 7 16 Z M 10 20.65625 C 10.734375 21.484375 11.804688 22 13 22 C 14.195313 22 15.265625 21.484375 16 20.65625 C 16.734375 21.484375 17.804688 22 19 22 C 20.195313 22 21.265625 21.484375 22 20.65625 C 22.734375 21.484375 23.804688 22 25 22 C 25.347656 22 25.679688 21.925781 26 21.84375 L 26 26 L 6 26 L 6 21.84375 C 6.320313 21.925781 6.652344 22 7 22 C 8.195313 22 9.265625 21.484375 10 20.65625 Z"></path></svg>}
                        />
                        <div className="flex items-center space-x-2">
                          <Radio.Group
                            value={genderOption}
                            onChange={handleOptionChange}
                          >
                            <div className="flex items-center space-x-4">
                              <Radio key="homme" value="Homme" label="Homme" />
                              <Radio key="femme" value="Femme" label="Femme" />
                              <Radio key="enfant" value="Enfant" label="Enfant" />
                            </div>
                          </Radio.Group>
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Select
                          value={eventEtatCivil}
                          onChange={(value) => setEventEtatCivil(value ?? "")}
                          placeholder="Etat civil"
                          data={[
                            { value: "Célibataire", label: "Célibataire" },
                            { value: "Marié", label: "Marié" },
                            { value: "Autre chose", label: "Autre chose" },
                          ]}
                          className="select w-full max-w-xs"
                        />
                        {genderOption !== "Enfant" ? (
                          <TextInput
                            id="Cin"
                            placeholder="Cin"
                            value={eventCin}
                            onChange={(e) => setEventCin(e.target.value)}
                            className="input input-bordered mb-2 w-full"
                            leftSection={
                        <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 17v-10l7 10v-10"></path><path d="M15 17h5"></path><path d="M17.5 10m-2.5 0a2.5 3 0 1 0 5 0a2.5 3 0 1 0 -5 0"></path></svg>}
                      />
                        ) : (
                          <TextInput
                            id="Cin"
                            placeholder="Cin"
                            disabled
                            className="input input-bordered mb-2 w-full"
                            leftSection={
                        <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 17v-10l7 10v-10"></path><path d="M15 17h5"></path><path d="M17.5 10m-2.5 0a2.5 3 0 1 0 5 0a2.5 3 0 1 0 -5 0"></path></svg>}
                      />
                        )}
                        <TextInput
                          id="Adresse"
                          placeholder="Adressé par"
                          value={address}
                          onChange={(e) => setAddress(e.target.value)}
                          className="input input-bordered mb-2 w-full"
                          leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M 3 6 L 3 26 L 29 26 L 29 6 Z M 5 8 L 27 8 L 27 24 L 23.59375 24 C 23.515625 23.863281 23.550781 23.675781 23.4375 23.5625 C 23.058594 23.183594 22.523438 23 22 23 C 21.476563 23 20.941406 23.183594 20.5625 23.5625 C 20.449219 23.675781 20.484375 23.863281 20.40625 24 L 11.59375 24 C 11.515625 23.863281 11.550781 23.675781 11.4375 23.5625 C 11.058594 23.183594 10.523438 23 10 23 C 9.476563 23 8.941406 23.183594 8.5625 23.5625 C 8.449219 23.675781 8.484375 23.863281 8.40625 24 L 5 24 Z M 12 10 C 9.800781 10 8 11.800781 8 14 C 8 15.113281 8.476563 16.117188 9.21875 16.84375 C 7.886719 17.746094 7 19.285156 7 21 L 9 21 C 9 19.34375 10.34375 18 12 18 C 13.65625 18 15 19.34375 15 21 L 17 21 C 17 19.285156 16.113281 17.746094 14.78125 16.84375 C 15.523438 16.117188 16 15.113281 16 14 C 16 11.800781 14.199219 10 12 10 Z M 12 12 C 13.117188 12 14 12.882813 14 14 C 14 15.117188 13.117188 16 12 16 C 10.882813 16 10 15.117188 10 14 C 10 12.882813 10.882813 12 12 12 Z M 19 13 L 19 15 L 25 15 L 25 13 Z M 19 17 L 19 19 L 25 19 L 25 17 Z"></path></svg>}
                        />
                      </div>

                      <div className="flex gap-4">
                        <InputBase
                          id="Téléphone"
                          component={IMaskInput}
                          mask="00-00-00-00-00"
                          placeholder="Téléphone"
                          value={eventTelephone}
                          onAccept={(value) => setEventTelephone(value)}
                          unmask={true}
                          className="input input-bordered mb-2 w-full"
                         leftSection={<svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>}
                      />
                        <TextInput
                          id="Email"
                          placeholder="Email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="input input-bordered mb-2 w-full"
                          leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><g id="At"><path d="M12.09,21.925a9.846,9.846,0,0,1-3.838-.747A9.673,9.673,0,0,1,3.005,15.93,10.034,10.034,0,0,1,2.244,12a10.425,10.425,0,0,1,.695-3.8,9.606,9.606,0,0,1,2-3.169A9.269,9.269,0,0,1,8.1,2.862a10.605,10.605,0,0,1,4.175-.787,10.516,10.516,0,0,1,4.334.827A8.437,8.437,0,0,1,19.64,5.119a8.622,8.622,0,0,1,1.707,3.1,9.263,9.263,0,0,1,.377,3.487,5.809,5.809,0,0,1-1.3,3.6A3.6,3.6,0,0,1,17.7,16.473a3.628,3.628,0,0,1-2.162-.609,2.82,2.82,0,0,1-1.119-1.694l.5.106a2.582,2.582,0,0,1-1.3,1.3A4.37,4.37,0,0,1,11.746,16,3.681,3.681,0,0,1,9.88,15.54a3.2,3.2,0,0,1-1.237-1.271A3.843,3.843,0,0,1,8.2,12.4a3.88,3.88,0,0,1,.456-1.926A3.191,3.191,0,0,1,9.919,9.214a3.792,3.792,0,0,1,1.853-.443,4.716,4.716,0,0,1,1.767.364,2.622,2.622,0,0,1,1.383,1.3l-.5.5V9.461a.4.4,0,0,1,.4-.4h.232a.4.4,0,0,1,.4.4v3.518a2.723,2.723,0,0,0,.529,1.674,2.173,2.173,0,0,0,1.853.708,2.281,2.281,0,0,0,1.323-.41,2.938,2.938,0,0,0,.967-1.178,4.947,4.947,0,0,0,.437-1.852,9.439,9.439,0,0,0-.417-3.574A7.285,7.285,0,0,0,18.5,5.588a7.424,7.424,0,0,0-2.679-1.78,9.605,9.605,0,0,0-3.547-.622,9.041,9.041,0,0,0-3.758.741,8.252,8.252,0,0,0-2.773,2,8.8,8.8,0,0,0-1.72,2.838,9.27,9.27,0,0,0-.589,3.262,8.568,8.568,0,0,0,.682,3.408A8.951,8.951,0,0,0,6,18.24a8.707,8.707,0,0,0,2.785,1.892,8.515,8.515,0,0,0,3.389.682,9.851,9.851,0,0,0,2.679-.378,8.451,8.451,0,0,0,2-.831.4.4,0,0,1,.553.158l.1.192a.4.4,0,0,1-.141.526,9.832,9.832,0,0,1-2.391,1.04A10.5,10.5,0,0,1,12.09,21.925ZM11.8,14.859a2.469,2.469,0,0,0,1.786-.649,2.427,2.427,0,0,0,.675-1.839,2.414,2.414,0,0,0-.7-1.886A2.532,2.532,0,0,0,11.8,9.856a2.482,2.482,0,0,0-1.839.649,2.523,2.523,0,0,0-.65,1.866,2.4,2.4,0,0,0,.682,1.865A2.574,2.574,0,0,0,11.8,14.859Z"></path></g></svg>}
                        />
                      </div>

                      <div className="flex gap-4">
                        <Select
                          value={patientdoctor}
                          onChange={(value) => setPatientDocteur(value ?? "")}
                          placeholder="Docteur"
                          data={[
                            { value: "Docteur", label: "Docteur" },
                            { value: "dr.Kader", label: "dr.Kader" },
                            { value: "dr.Kaders", label: "dr.Kaders" },
                          ]}
                          className="w-full"
                          leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                        <Select
                          value={patientsocialSecurity || 'Aucune'}
                          onChange={(value) => setSocialSecurity(value || 'Aucune')}
                          placeholder="Sécurité sociale"
                          data={[
                            { value: "Aucune", label: "Aucune" },
                            { value: "CNSS", label: "CNSS" },
                            { value: "AMO", label: "AMO" },
                          ]}
                          className="w-full"
                          leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M4 5c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2zm4.78 3.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 2 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zM18 7c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2.78 1.58a6.95 6.95 0 0 0-5.56 0A2.01 2.01 0 0 0 14 10.43V11h8v-.57c0-.81-.48-1.53-1.22-1.85zm-2.77 4.43-1.41 1.41L18.17 16H5.83l1.58-1.59L6 13l-4 4 3.99 3.99 1.41-1.41L5.83 18h12.34l-1.58 1.58L18 20.99 22 17l-3.99-3.99z"></path></svg>}
                        />
                      </div>

                      <div className="flex gap-4 mb-2">
                        <Select
                          label="Type de consultation"
                          placeholder="Rechercher ou saisir..."
                          data={initialConsultationTypes}
                          value={patienttypeConsultation}
                          onChange={(value) => {
                            setPatientTypeConsultation(value ?? "");
                            const selectedLabel = [
                              { value: "Visite de malade", eventType: "visit" },
                              { value: "Visitor Counter", eventType: "visitor-counter" },
                              { value: "Completed", eventType: "completed" },
                            ].find(item => item.value === value);

                            if (selectedLabel) {
                              setEventType(selectedLabel.eventType as EventType);
                            }
                          }}
                          searchable
                          searchValue={searchValue}
                          onSearchChange={setSearchValue}
                          clearable
                          maxDropdownHeight={280}
                          rightSectionWidth={70}
                          required
                          rightSection={
                            <span className="bg-[#4CAF50] text-white px-2 py-1 rounded text-xs">{dureeDeLexamen}</span>
                          }
                          allowDeselect
                          styles={(theme) => ({
                            rightSection: {
                              backgroundColor: "#4CAF50",
                              color: "white",
                              borderRadius: "4px",
                              padding: "2px 8px",
                              fontSize: "12px",
                              width: "auto",
                              marginRight: "8px",
                            },
                            input: {
                              color: patienttypeConsultation ? getEventTypeColor(getEventTypeFromLabel(patienttypeConsultation)) || theme.colors.dark[9] : theme.colors.dark[9],
                              fontWeight: patienttypeConsultation ? 'bold' : 'normal',
                              "&:focus": {
                                borderColor: theme.colors.blue[5],
                              },
                            },
                            item: {
                              color: ({ value }: { value: string }) => getEventTypeColor(getEventTypeFromLabel(value)) || theme.colors.dark[9],
                              fontWeight: ({ selected }: { selected: boolean }) => selected ? 'bold' : 'normal',
                              '&[data-selected]': {
                                backgroundColor: theme.colors.gray[1],
                              },
                            },
                          })}
                          className="w-full"
                        />
                        <Select
                          label="Durée"
                          value={dureeDeLexamen}
                          onChange={(value) => setDureeDeLexamen(value ?? "")}
                          placeholder="15 min"
                          data={["10 min", "15 min", "20 min","25 min","30 min", "35 min" ,"40 min","45 min"]}
                          className="select w-full max-w-xs"
                        />
                        <Select
                          label="Aganda"
                          value={eventAganda}
                          onChange={(value) => setEventAganda(value ?? "")}
                          placeholder="Aganda"
                          data={[
                            { value: "Cabinet", label: "Cabinet" },
                            { value: "Center", label: "Center" },
                            { value: "Kaders", label: "Kaders" },
                          ]}
                          className="w-full"
                         leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>}
                      />
                      </div>

                      <div className="mx-auto flex gap-4 mb-2">
                        <label htmlFor="event-date" className="label mt-2">
                          Date du RDV
                        </label>
                        <TextInput
                          id="event-date"
                          type="date"
                          value={eventDate}
                          onChange={(e) => setEventDate(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <label htmlFor="event-time" className="label mt-2">
                          De*
                        </label>
                        <TextInput
                          id="event-time"
                          type="time"
                          value={eventTime}
                          onChange={(e) => setEventTime(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <label htmlFor="event-time" className="label mt-2">
                          à*
                        </label>
                        <TextInput
                          id="event-time"
                          type="text"
                          placeholder={
                            eventTime !== null
                              ? moment(eventTime, "HH:mm")
                                  .add(parseInt(eventConsultation), "minutes")
                                  .format("HH:mm")
                              : "Please enter your date of birth"
                          }
                          readOnly
                          className="input input-bordered mb-2 w-40"
                        />
                      </div>

                      <div className="flex gap-4 mb-2 -mt-2 pr-4">
                        <Textarea
                          id="event-Commentaire"
                          value={patientcomment}
                          onChange={(event) => setPatientcomment(event.currentTarget.value ?? "")}
                          placeholder="Commentaire ..."
                          className="w-full"
                          rightSection={<span className="mr-6"><Icon path={mdiMicrophone} size={1} /></span>}
                        />
                        <Textarea
                          id="event-Notes"
                          value={patientnotes}
                          onChange={(event) => setPatientNotes(event.currentTarget.value ?? "")}
                          placeholder="Notes ..."
                          className="w-full"
                          rightSection={<span className="mr-6"><Icon path={mdiMicrophone} size={1} /></span>}
                        />
                      </div>

                      <div className="bg-base-100 px-[4px] py-[0px] ">
                        <ul className="text-daisy flex flex-wrap gap-x-4 gap-y-2">
                          <div className="flex items-center space-x-1">
                            <Select
                              value={eventResourceId ? eventResourceId.toString() : ""}
                              onChange={(value) => {
                                setEventResourceId(Number(value) || 1);
                              }}
                              name="resourceId"
                              placeholder="Room"
                              data={[
                                { value: "1", label: "Room A" },
                                { value: "2", label: "Room B" },
                              ]}
                              required
                              className="select w-full max-w-xs"
                              leftSection={<svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0V0z"></path><path d="M20 4v16H4V4h16m0-2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-3.5 8.67V9c0-1.1-.9-2-2-2h-5c-1.1 0-2 .9-2 2v1.67c-.88.35-1.5 1.2-1.5 2.2V17h1.5v-1.5h9V17H18v-4.13c0-1-.62-1.85-1.5-2.2zM15 8.5v2H9v-2h6zm-7.5 4.37c0-.48.39-.87.87-.87h7.27c.48 0 .87.39.87.87V14h-9v-1.13H7.5z"></path></svg>}
                            />
                          </div>

                          <div className="flex items-center space-x-1">
                            <input
                              id="visit"
                              type="radio"
                              name="eventType"
                              value="visit"
                              className="peer hidden"
                              checked={eventType === "visit"}
                              onChange={(e) => setEventType(e.target.value as EventType)}
                            />
                            <label
                              htmlFor="visit"
                              className={`${
                                eventType === "visit"
                                  ? "peer-checked:text-[#34D1BF]"
                                  : "text-[var(--mantine-color-dark-0)]"
                              } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                            >
                              <li className="flex items-center gap-2 text-xs uppercase">
                                <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                                Visite de malade
                              </li>
                            </label>
                          </div>

                          <div className="flex items-center space-x-1">
                            <input
                              id="visitor-counter"
                              type="radio"
                              name="eventType"
                              value="visitor-counter"
                              className="peer hidden"
                              checked={eventType === "visitor-counter"}
                              onChange={(e) => setEventType(e.target.value as EventType)}
                            />
                            <label
                              htmlFor="visitor-counter"
                              className={`${
                                eventType === "visitor-counter"
                                  ? "peer-checked:text-[#F17105]"
                                  : "text-[var(--mantine-color-dark-0)]"
                              } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                            >
                              <li className="flex items-center gap-2 text-xs uppercase">
                                <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                                Visitor Counter
                              </li>
                            </label>
                          </div>

                          <div className="flex items-center space-x-1">
                            <input
                              id="completed"
                              type="radio"
                              name="eventType"
                              value="completed"
                              className="peer hidden"
                              checked={eventType === "completed"}
                              onChange={(e) => setEventType(e.target.value as EventType)}
                            />
                            <label
                              htmlFor="completed"
                              className={`${
                                eventType === "completed"
                                  ? "peer-checked:text-[#3799CE]"
                                  : "text-[var(--mantine-color-dark-0)]"
                              } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                            >
                              <li className="flex items-center gap-2 text-xs uppercase">
                                <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                                Completed
                              </li>
                            </label>
                          </div>

                          <div className="flex items-center space-x-1">
                            <input
                              id="diagnosis"
                              type="radio"
                              name="eventType"
                              value="diagnosis"
                              checked={eventType === "diagnosis"}
                              className="peer hidden"
                              onChange={(e) => setEventType(e.target.value as EventType)}
                            />
                            <label
                              htmlFor="diagnosis"
                              className={`${
                                eventType === "diagnosis"
                                  ? "peer-checked:text-[#F3124E]"
                                  : "text-[var(--mantine-color-dark-0)]"
                              } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                            >
                              <li className="flex items-center gap-2 text-xs uppercase">
                                <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                                Re-diagnose
                              </li>
                            </label>
                          </div>
                        </ul>
                      </div>

                      <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pr-4 ">
                        <Group gap="xs">
                          <Switch
                            color="teal"
                            size="xs"
                            label="Add to Waiting List"
                            checked={appointmentForm.values.addToWaitingList}
                            onChange={(event) => {
                              appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);
                              appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);
                            }}
                            thumbIcon={
                              appointmentForm.values.addToWaitingList ? (
                                <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                              ) : (
                                null
                              )
                            }
                          />
                          <Switch
                            checked={checkedAppelvideo}
                            onChange={handleAppelvideoChange}
                            color="teal"
                            size="xs"
                            label="Appel video"
                            thumbIcon={
                              checkedAppelvideo ? (
                                <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                              ) : (
                                null
                              )
                            }
                          />
                          <Switch
                            checked={checkedRappelSms}
                            onChange={handleRappelSmsChange}
                            color="teal"
                            size="xs"
                            label="Rappel Sms"
                          />
                          <Switch
                            color="teal"
                            size="xs"
                            label="Rappel e-mail"
                          />
                        </Group>
                        <Button
                          type="submit"
                          className="btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90"
                          onClick={() => {
                            closeRendezVous();
                          }}
                        >
                          {currentPatient ? "Enregistrer" : "Ajouter"}
                        </Button>
                        {currentPatient && (
                          <Button
                            color="red"
                            onClick={() => {
                              if (currentPatient) {
                                setWaitingList(waitingList.filter(p => p.id !== currentPatient.id));
                                setPatientModalOpen(false);
                              }
                            }}
                            className="btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90"
                          >
                            Supprimer
                          </Button>
                        )}
                        <Button
                          onClick={() => {
                            resetForm();
                            setShowEditModal(false);
                          }}
                          className="btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90"
                        >
                          Annuler
                        </Button>
                      </div>
                    </div>
                  </form>
                </div>
              </SimpleBar>
            </div>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    )}

    {/* Modal Pause */}
    <PauseModal
      opened={pauseModalOpened}
      onClose={() => {
        setPauseModalOpened(false);
        setIsPauseMode(false);
      }}
      onSave={(pauseData) => {
        console.log('Pause data saved:', pauseData);
        notifications.show({
          title: 'Pause ajoutée',
          message: 'La pause a été ajoutée avec succès',
          color: 'green',
          autoClose: 3000
        });
      }}
    />
    </>
  );
};

export default AjouterUnRendezVous;
