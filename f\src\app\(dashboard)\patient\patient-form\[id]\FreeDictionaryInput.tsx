import React, { useState, useEffect } from 'react'
import { Textarea, Stack, Group, Button, Text, Loader } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import patientService, { PatientMedicalData } from '@/services/patientService';
export interface FreeDictionaryField {
  id: string;
  key: string;
  type: string;
  label: string;
  value: string;
  meta_data?: {
    small?: boolean;
  };
  dictUid?: string;
  blockUid?: string;
}



interface Props {
  fields: FreeDictionaryField[];
  values: Record<string, string>;
  onChange: (key: string, value: string) => void;
  readOnly?: boolean;
  patientId?: string;
  fieldType?: 'allergies' | 'medical_conditions' | 'medications' | 'custom';
  label?: string;
  placeholder?: string;
}

export function FreeDictionaryInput({
  values,
  onChange,
  readOnly = false,
  patientId,
  fieldType = 'allergies',
  label,
  placeholder = 'Ajouter',
}: Props) {
  const [loading, setLoading] = useState(false);
  const [patientMedicalData, setPatientMedicalData] = useState<PatientMedicalData[]>([]);

  // Load medical categories and patient data from Django
  useEffect(() => {
    if (patientId) {
      loadMedicalData();
    }
  }, [patientId, fieldType]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadMedicalData = async () => {
    try {
      setLoading(true);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        console.warn('Django backend is not connected');
        return;
      }

      if (patientId) {
        // Load patient medical data from Django
        const medicalData = await patientService.getPatientMedicalData(patientId, fieldType);
        if (medicalData) {
          setPatientMedicalData(medicalData);

          // Update the form values with Django data
          const combinedValue = medicalData.map(data => data.medical_item || '').join(', ');
          onChange(fieldType, combinedValue);
        }
      }
    } catch (error) {
      console.error('Error loading medical data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveToPatient = async () => {
    if (!patientId || !values[fieldType]) return;

    try {
      setLoading(true);

      // Save medical data to Django
      const result = await patientService.addPatientMedicalData(patientId, {
        category_type: fieldType,
        medical_item_name: values[fieldType],
        notes: `Added via ${fieldType} field`
      });

      if (result) {
        notifications.show({
          title: 'Success',
          message: `${label || fieldType} data saved successfully`,
          color: 'green',
        });

        // Reload data to get updated list
        await loadMedicalData();
      }
    } catch (error) {
      console.error('Error saving medical data:', error);
      notifications.show({
        title: 'Error',
        message: `Failed to save ${label || fieldType} data`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const displayLabel = label || (fieldType.charAt(0).toUpperCase() + fieldType.slice(1).replace('_', ' '));
  const fieldKey = fieldType;

  return (
    <Stack gap="sm">
      <Group justify="space-between" align="flex-end">
        <Textarea
          label={displayLabel}
          value={values[fieldKey] || ''}
          onChange={(e) => onChange(fieldKey, e.currentTarget.value)}
          placeholder={placeholder}
          disabled={readOnly || loading}
          style={{ flex: 1 }}
          rows={3}
        />
        {!readOnly && patientId && (
          <Button
            onClick={handleSaveToPatient}
            disabled={!values[fieldKey] || loading}
            loading={loading}
            size="sm"
          >
            Save to Patient
          </Button>
        )}
      </Group>

      {/* Display existing patient medical data */}
      {patientMedicalData.length > 0 && (
        <Stack gap="xs">
          <Text size="sm" fw={500} c="dimmed">
            Existing {displayLabel}:
          </Text>
          {patientMedicalData.map((data, index) => (
            <Text key={data.id || index} size="sm" p="xs" style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              border: '1px solid #e9ecef'
            }}>
              {data.medical_item?.name || data.medical_item?.id || 'Unknown item'}
              {data.notes && (
                <Text size="xs" c="dimmed" mt="xs">
                  Note: {data.notes}
                </Text>
              )}
            </Text>
          ))}
        </Stack>
      )}

      {loading && (
        <Group justify="center">
          <Loader size="sm" />
          <Text size="sm" c="dimmed">Loading medical data...</Text>
        </Group>
      )}
    </Stack>
  );
}
