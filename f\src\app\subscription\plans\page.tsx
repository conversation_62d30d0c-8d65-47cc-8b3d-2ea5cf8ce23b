'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Badge,
  Button,
  SimpleGrid,
  List,
  ThemeIcon,
  Divider,
  Alert,
  // useMantineTheme,

} from '@mantine/core';
import { IconCheck, IconX, IconAlertCircle, IconArrowLeft } from '@tabler/icons-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '~/hooks/useAuth';

import paymentConfigService, { PaymentConfiguration } from '~/services/paymentConfigService';

// Define types for subscription plans
interface PlanFeature {
  text: string;
  included: boolean;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  price_monthly: number;
  price_annual: number;
  max_users: number;
  max_assistants: number;
  color: string;
  popular?: boolean;
  features: PlanFeature[];
}

// Initial plans structure - will be populated with dynamic configuration
const initialPlans: SubscriptionPlan[] = [
  {
    id: 'basic',
    name: 'Basic',
    price_monthly: 0,
    price_annual: 0,
    max_users: 0,
    max_assistants: 0,
    color: 'blue',
    features: [
      { text: 'Up to 100 patients', included: true },
      { text: 'Assistant accounts', included: true },
      { text: 'Basic appointment scheduling', included: true },
      { text: 'Patient records management', included: true },
      { text: 'Email notifications', included: true },
      { text: 'Basic reporting', included: true },
      { text: 'Advanced analytics', included: false },
      { text: 'Custom branding', included: false },
      { text: 'Priority support', included: false },
    ],
  },
  {
    id: 'standard',
    name: 'Standard',
    price_monthly: 0,
    price_annual: 0,
    max_users: 0,
    max_assistants: 0,
    color: 'violet',
    popular: true,
    features: [
      { text: 'Unlimited patients', included: true },
      { text: 'Assistant accounts', included: true },
      { text: 'Advanced appointment scheduling', included: true },
      { text: 'Patient records management', included: true },
      { text: 'Email & SMS notifications', included: true },
      { text: 'Advanced reporting', included: true },
      { text: 'Basic analytics', included: true },
      { text: 'Custom branding', included: true },
      { text: 'Priority support', included: false },
    ],
  },
  {
    id: 'premium',
    name: 'Premium',
    price_monthly: 0,
    price_annual: 0,
    max_users: 0,
    max_assistants: 0,
    color: 'green',
    features: [
      { text: 'Unlimited patients', included: true },
      { text: 'Assistant accounts', included: true },
      { text: 'Advanced appointment scheduling', included: true },
      { text: 'Patient records management', included: true },
      { text: 'Email & SMS notifications', included: true },
      { text: 'Advanced reporting', included: true },
      { text: 'Advanced analytics', included: true },
      { text: 'Custom branding', included: true },
      { text: 'Priority support', included: true },
    ],
  },
];

export default function SubscriptionPlansPage() {

  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [, setSelectedPlanId] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  const [isChangingPackage, setIsChangingPackage] = useState(false);
  const [paymentConfig, setPaymentConfig] = useState<PaymentConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<SubscriptionPlan[]>(initialPlans);

  // Fetch payment configuration
  useEffect(() => {
    const fetchPaymentConfig = async () => {
      try {
        setLoading(true);
        const config = await paymentConfigService.getActiveConfig();
        setPaymentConfig(config);

        // Store in localStorage for use in registration
        localStorage.setItem('paymentConfig', JSON.stringify(config));

        // Create new plans with dynamic pricing
        const updatedPlans = [...initialPlans];

        // Update Basic plan
        updatedPlans[0].price_monthly = Number(config.basic_price_6months) / 6;
        updatedPlans[0].price_annual = Number(config.basic_price_annual);
        updatedPlans[0].max_users = Number(config.basic_max_users);
        updatedPlans[0].max_assistants = Number(config.basic_max_assistants);

        // Update Standard plan
        updatedPlans[1].price_monthly = Number(config.standard_price_6months) / 6;
        updatedPlans[1].price_annual = Number(config.standard_price_annual);
        updatedPlans[1].max_users = Number(config.standard_max_users);
        updatedPlans[1].max_assistants = Number(config.standard_max_assistants);

        // Update Premium plan
        updatedPlans[2].price_monthly = Number(config.premium_price_6months) / 6;
        updatedPlans[2].price_annual = Number(config.premium_price_annual);
        updatedPlans[2].max_users = Number(config.premium_max_users);
        updatedPlans[2].max_assistants = Number(config.premium_max_assistants);

        // Update features text with dynamic values
        updatedPlans[0].features[1].text = `${config.basic_max_assistants} assistant accounts`;
        updatedPlans[1].features[1].text = `${config.standard_max_assistants} assistant accounts`;
        updatedPlans[2].features[1].text = `${config.premium_max_assistants} assistant accounts`;

        setPlans(updatedPlans);
      } catch (err) {
        console.error('Error fetching payment configuration:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentConfig();
  }, []);

  // Check if we're changing a package during registration
  useEffect(() => {
    if (searchParams) {
      const changingPackage = searchParams.get('changingPackage');
      if (changingPackage === 'true') {
        setIsChangingPackage(true);
      }
    }
  }, [searchParams]);

  const handleSelectPlan = (planId: string) => {
    setSelectedPlanId(planId);

    // If we're changing a package during registration
    if (isChangingPackage) {
      // Get the selected plan details
      const selectedPlan = plans.find((p) => p.id === planId);
      if (selectedPlan) {
        // Calculate price based on billing cycle
        const price = billingCycle === 'annual'
          ? (typeof selectedPlan.price_annual === 'number' ? selectedPlan.price_annual.toFixed(2) : Number(selectedPlan.price_annual).toFixed(2))
          : (typeof selectedPlan.price_monthly === 'number' ? (selectedPlan.price_monthly * 6).toFixed(2) : (Number(selectedPlan.price_monthly) * 6).toFixed(2));

        // Redirect back to registration with the new package using direct navigation
        window.location.href = `/register?packageId=${planId}&packageName=${encodeURIComponent(selectedPlan.name)}&packagePrice=${price}&billingCycle=${billingCycle}&resumeRegistration=true`;
      }
    } else {
      // Normal flow - redirect to checkout
      router.push(`/subscription/checkout?plan=${planId}&billing=${billingCycle}`);
    }
  };

  return (
    <Container size="lg" py="xl">
      {isChangingPackage && (
        <Group mb="xl">
          <Button
            leftSection={<IconArrowLeft size={16} />}
            variant="subtle"
            onClick={() => {
              // Return to registration page with the original package using direct navigation
              window.location.href = '/register?resumeRegistration=true';
            }}
          >
            Back to Registration
          </Button>
        </Group>
      )}

      <Title order={1} ta="center" mb="sm">
        Subscription Plans
      </Title>
      <Text c="dimmed" size="lg" ta="center" mb="xl">
        Choose the perfect plan for your practice
      </Text>

      {loading && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Loading subscription plans"
          color="blue"
          mb="xl"
        >
          Please wait while we load the latest pricing information...
        </Alert>
      )}

      {user?.is_trial && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="You're currently on a trial plan"
          color="blue"
          mb="xl"
        >
          Upgrade to a paid plan to continue using all features after your trial ends.
        </Alert>
      )}

      {!loading && (
        <>
          {paymentConfig && (
            <Text ta="center" size="sm" c="dimmed" mb="md">
              Prices last updated: {new Date(paymentConfig.updated_at).toLocaleDateString()}. Choose your preferred billing cycle:
            </Text>
          )}
          {!paymentConfig && (
            <Text ta="center" size="sm" c="dimmed" mb="md">
              Prices are updated regularly. Choose your preferred billing cycle:
            </Text>
          )}

          <Group justify="center" mb="xl">
            <Button.Group>
              <Button
                variant={billingCycle === 'monthly' ? 'filled' : 'light'}
                onClick={() => setBillingCycle('monthly')}
              >
                6 Months
              </Button>
              <Button
                variant={billingCycle === 'annual' ? 'filled' : 'light'}
                onClick={() => setBillingCycle('annual')}
              >
                Annual (Save 20%)
              </Button>
            </Button.Group>
          </Group>

          <SimpleGrid cols={{ base: 1, sm: 1, md: 3 }} spacing="lg">
            {plans.map((plan) => (
              <Card key={plan.id} shadow="sm" padding="lg" radius="md" withBorder>
                {plan.popular && (
                  <Badge
                    color={plan.color}
                    variant="filled"
                    style={{ position: 'absolute', top: 10, right: 10 }}
                  >
                    Most Popular
                  </Badge>
                )}
                <Title order={3} ta="center" c={plan.color}>
                  {plan.name}
                </Title>
                <Text ta="center" size="xl" fw={700} my="md">
                  ${billingCycle === 'annual'
                    ? (typeof plan.price_annual === 'number' ? plan.price_annual.toFixed(2) : Number(plan.price_annual).toFixed(2))
                    : (typeof plan.price_monthly === 'number' ? (plan.price_monthly * 6).toFixed(2) : (Number(plan.price_monthly) * 6).toFixed(2))}
                  <Text span size="sm" c="dimmed">
                    /{billingCycle === 'annual' ? 'year' : '6 months'}
                  </Text>
                </Text>
                {billingCycle === 'annual' && (
                  <Badge color="green" variant="light" fullWidth mb="md">
                    Save 20% with annual billing
                  </Badge>
                )}
                <Divider my="md" />
                <List
                  spacing="sm"
                  size="sm"
                  center
                  mb="xl"
                  styles={{
                    itemWrapper: {
                      display: 'flex',
                      alignItems: 'flex-start',
                    },
                  }}
                >
                  {plan.features.map((feature, index) => (
                    <List.Item
                      key={index}
                      icon={
                        <ThemeIcon
                          color={feature.included ? plan.color : 'gray'}
                          variant={feature.included ? 'light' : 'subtle'}
                          size={24}
                          radius="xl"
                        >
                          {feature.included ? <IconCheck size={16} /> : <IconX size={16} />}
                        </ThemeIcon>
                      }
                    >
                      <Text c={feature.included ? 'dark' : 'dimmed'}>
                        {feature.text}
                      </Text>
                    </List.Item>
                  ))}
                </List>
                <Button
                  fullWidth
                  color={plan.color}
                  onClick={() => handleSelectPlan(plan.id)}
                >
                  Select Plan
                </Button>
              </Card>
            ))}
          </SimpleGrid>

          <Card withBorder p="xl" radius="md" mt="xl">
            <Title order={3} mb="md">
              Need a custom plan?
            </Title>
            <Text mb="md">
              If you need a custom plan for your practice with specific features or more assistant accounts,
              please contact our sales team.
            </Text>
            <Button variant="outline" component="a" href={`mailto:${paymentConfig?.sales_email || '<EMAIL>'}`}>
              Contact Sales
            </Button>

            {paymentConfig && (
              <div className ="mt-lg">
                <Divider my="md" label="Payment Methods" labelPosition="center" />
                <Group mt="md" justify="center">
                  {paymentConfig.enable_credit_card && (
                    <Badge size="lg" color="blue" variant="outline">Credit Card</Badge>
                  )}
                  {paymentConfig.enable_bank_transfer && (
                    <Badge size="lg" color="green" variant="outline">Bank Transfer</Badge>
                  )}
                  {paymentConfig.enable_paypal && (
                    <Badge size="lg" color="indigo" variant="outline">PayPal</Badge>
                  )}
                </Group>
                <Text size="xs" c="dimmed" ta="center" mt="sm">
                  For support, contact: {paymentConfig.support_email}
                </Text>
              </div>
            )}
          </Card>
        </>
      )}
    </Container>
  );
}
