# Generated by Django 4.2.7 on 2025-08-29 14:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0009_merge_20250828_1426'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctorpause',
            name='color',
            field=models.CharField(blank=True, default='#15AABF', help_text='Hex color code for calendar display', max_length=7, null=True),
        ),
        migrations.AddField(
            model_name='doctorpause',
            name='resource_id',
            field=models.Char<PERSON>ield(blank=True, help_text='Resource/Room ID for calendar integration', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='doctorpause',
            name='room',
            field=models.Char<PERSON>ield(blank=True, choices=[('room-a', 'Room A'), ('room-b', 'Room B'), ('room-c', 'Room C'), ('other', 'Other')], help_text='Room where the pause takes place (especially for lunch breaks)', max_length=20, null=True),
        ),
    ]
