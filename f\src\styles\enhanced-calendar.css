/* Enhanced Calendar Styles for react-big-calendar */

.rbc-calendar-enhanced {
  height: 100%;
  font-family: inherit;
}

/* Time gutter styling */
.rbc-calendar-enhanced .rbc-time-gutter {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.rbc-calendar-enhanced .rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-calendar-enhanced .rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

/* Header styling */
.rbc-calendar-enhanced .rbc-header {
  background-color: #e6e9ec;
  border-bottom: 1px solid #d1d5db;
  padding: 8px;
  font-weight: 600;
  color: #565b61;
  text-align: center;
}

/* Event styling */
.rbc-calendar-enhanced .rbc-event {
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
  line-height: 1.2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-calendar-enhanced .rbc-event:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.rbc-calendar-enhanced .rbc-event-content {
  overflow: hidden;
}

/* Time labels */
.rbc-calendar-enhanced .rbc-time-header-gutter {
  background-color: #f8fafc;
}

.rbc-calendar-enhanced .rbc-label {
  color: #6b7280;
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
}

/* Selection styling */
.rbc-calendar-enhanced .rbc-slot-selection {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
  border-radius: 4px;
}

/* Today highlighting */
.rbc-calendar-enhanced .rbc-today {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Time slot hover effects */
.rbc-calendar-enhanced .rbc-time-slot:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Current time indicator */
.rbc-calendar-enhanced .rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
  z-index: 10;
}

/* Drag and drop styling */
.rbc-calendar-enhanced .rbc-addons-dnd-drag-preview {
  opacity: 0.7;
  transform: rotate(5deg);
}

.rbc-calendar-enhanced .rbc-addons-dnd-drop-preview {
  background-color: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  border-radius: 4px;
}

/* Room-specific event colors */
.rbc-event-room-a {
  background-color: #3b82f6 !important;
}

.rbc-event-room-b {
  background-color: #10b981 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-calendar-enhanced .rbc-time-gutter {
    width: 60px;
  }
  
  .rbc-calendar-enhanced .rbc-label {
    font-size: 10px;
    padding: 2px 4px;
  }
  
  .rbc-calendar-enhanced .rbc-event {
    font-size: 11px;
    padding: 1px 2px;
  }
}

/* Custom scrollbar for calendar */
.rbc-calendar-enhanced .rbc-time-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar {
  width: 8px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.rbc-calendar-enhanced .rbc-time-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading state */
.rbc-calendar-enhanced.loading {
  opacity: 0.6;
  pointer-events: none;
}

.rbc-calendar-enhanced.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Event overlap handling */
.rbc-calendar-enhanced .rbc-event-overlaps {
  box-shadow: -2px 0 0 0 #ffffff, -4px 0 0 0 currentColor;
}

/* Accessibility improvements */
.rbc-calendar-enhanced .rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rbc-calendar-enhanced .rbc-time-slot:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background-color: rgba(59, 130, 246, 0.1);
}

/* Print styles */
@media print {
  .rbc-calendar-enhanced {
    background: white !important;
  }
  
  .rbc-calendar-enhanced .rbc-event {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}
