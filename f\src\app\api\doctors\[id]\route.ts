import { NextRequest, NextResponse } from 'next/server';

// Define the <PERSON> type
interface Doctor {
  id: string;
  first_name: string;
  last_name: string;
  specialization: string;
  email: string;
  phone_number: string;
  license_number: string;
  bio: string;
  profile_image_url: string | null;
}

// Mock data for doctors
const mockDoctors: Record<string, Doctor> = {
  'e62a0736-0ad9-4f12-a63b-e67c90183231': {
    id: 'e62a0736-0ad9-4f12-a63b-e67c90183231',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    specialization: 'Cardiology',
    email: '<EMAIL>',
    phone_number: '(*************',
    license_number: 'MD12345',
    bio: '<PERSON><PERSON> <PERSON> is a board-certified cardiologist with over 15 years of experience in treating heart conditions.',
    profile_image_url: null
  },
  'a279d849-e5d5-403f-9ddf-dfa2a5e981da': {
    id: 'a279d849-e5d5-403f-9ddf-dfa2a5e981da',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    specialization: 'Dermatology',
    email: 'dr.joh<PERSON>@example.com',
    phone_number: '(*************',
    license_number: 'MD23456',
    bio: 'Dr. <PERSON> specializes in treating skin conditions and has been practicing for over 10 years.',
    profile_image_url: null
  },
  '3': {
    id: '3',
    first_name: 'Michael',
    last_name: '<PERSON>',
    specialization: 'Neurology',
    email: '<EMAIL>',
    phone_number: '(*************',
    license_number: 'MD34567',
    bio: 'Dr. Lee is a neurologist with expertise in treating headaches, seizures, and other neurological disorders.',
    profile_image_url: null
  }
};

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const doctorId = params.id;
    console.log(`API: Fetching doctor with ID: ${doctorId}`);

    // In a real application, you would fetch this data from a database or external API
    // For now, we'll use mock data
    const doctor = mockDoctors[doctorId];

    if (!doctor) {
      console.log(`API: Doctor with ID ${doctorId} not found`);
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      );
    }

    console.log(`API: Found doctor:`, doctor);
    return NextResponse.json(doctor);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
