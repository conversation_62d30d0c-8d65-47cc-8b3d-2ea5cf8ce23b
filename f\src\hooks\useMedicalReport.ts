/**
 * Custom hook for managing medical-report data
 * Provides easy access to exam reports, billing, contracts, and templates
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  medicalReportService, 
  ExamReport,
  BillingInvoice,
  BillingQuote,
  Payment,
  Contract,
  ExamTemplate,
  MedicalReportSummary
} from '@/services/medicalReportService';

interface UseMedicalReportOptions {
  patientId?: string;
  dateRange?: { start: string; end: string };
  autoFetch?: boolean;
  refreshInterval?: number;
  reportTypes?: string[];
}

interface UseMedicalReportReturn {
  // Data
  examReports: ExamReport[];
  billingInvoices: BillingInvoice[];
  billingQuotes: BillingQuote[];
  payments: Payment[];
  contracts: Contract[];
  templates: ExamTemplate[];
  summary: MedicalReportSummary | null;
  
  // Loading states
  loading: boolean;
  examReportsLoading: boolean;
  billingLoading: boolean;
  paymentsLoading: boolean;
  contractsLoading: boolean;
  templatesLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchExamReports: (patientId?: string) => Promise<void>;
  fetchBillingInvoices: (patientId?: string, status?: string) => Promise<void>;
  fetchBillingQuotes: (patientId?: string, status?: string) => Promise<void>;
  fetchPayments: (patientId?: string) => Promise<void>;
  fetchContracts: (patientId?: string, status?: string) => Promise<void>;
  fetchTemplates: () => Promise<void>;
  fetchSummary: (patientId?: string) => Promise<void>;
  refreshAll: (patientId?: string) => Promise<void>;
  createExamReport: (reportData: Omit<ExamReport, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateExamReport: (id: string, reportData: Partial<ExamReport>) => Promise<void>;
  deleteExamReport: (id: string) => Promise<void>;
  
  // Utility functions
  getReportsByPatient: (patientId: string) => ExamReport[];
  getInvoicesByPatient: (patientId: string) => BillingInvoice[];
  getQuotesByPatient: (patientId: string) => BillingQuote[];
  getPaymentsByPatient: (patientId: string) => Payment[];
  getContractsByPatient: (patientId: string) => Contract[];
  getPatientMedicalStats: (patientId: string) => {
    totalReports: number;
    totalInvoices: number;
    totalQuotes: number;
    totalPayments: number;
    totalContracts: number;
    totalRevenue: number;
    pendingAmount: number;
    lastReportDate: string | null;
    activeContracts: number;
  };
  getBillingStats: () => {
    totalRevenue: number;
    pendingRevenue: number;
    paidInvoices: number;
    unpaidInvoices: number;
    activeQuotes: number;
    expiredQuotes: number;
  };
}

export const useMedicalReport = (options: UseMedicalReportOptions = {}): UseMedicalReportReturn => {
  const { patientId, dateRange, autoFetch = true, refreshInterval, reportTypes } = options;

  // State
  const [examReports, setExamReports] = useState<ExamReport[]>([]);
  const [billingInvoices, setBillingInvoices] = useState<BillingInvoice[]>([]);
  const [billingQuotes, setBillingQuotes] = useState<BillingQuote[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [templates, setTemplates] = useState<ExamTemplate[]>([]);
  const [summary, setSummary] = useState<MedicalReportSummary | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [examReportsLoading, setExamReportsLoading] = useState(false);
  const [billingLoading, setBillingLoading] = useState(false);
  const [paymentsLoading, setPaymentsLoading] = useState(false);
  const [contractsLoading, setContractsLoading] = useState(false);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Fetch functions
  const fetchExamReports = useCallback(async (targetPatientId?: string) => {
    setExamReportsLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getExamReports(targetPatientId || patientId, dateRange);
      setExamReports(data);
    } catch (err) {
      setError(`Failed to fetch exam reports: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setExamReportsLoading(false);
    }
  }, [patientId, dateRange]);

  const fetchBillingInvoices = useCallback(async (targetPatientId?: string, status?: string) => {
    setBillingLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getBillingInvoices(targetPatientId || patientId, status);
      setBillingInvoices(data);
    } catch (err) {
      setError(`Failed to fetch billing invoices: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setBillingLoading(false);
    }
  }, [patientId]);

  const fetchBillingQuotes = useCallback(async (targetPatientId?: string, status?: string) => {
    setBillingLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getBillingQuotes(targetPatientId || patientId, status);
      setBillingQuotes(data);
    } catch (err) {
      setError(`Failed to fetch billing quotes: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setBillingLoading(false);
    }
  }, [patientId]);

  const fetchPayments = useCallback(async (targetPatientId?: string) => {
    setPaymentsLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getPayments(targetPatientId || patientId, dateRange);
      setPayments(data);
    } catch (err) {
      setError(`Failed to fetch payments: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setPaymentsLoading(false);
    }
  }, [patientId, dateRange]);

  const fetchContracts = useCallback(async (targetPatientId?: string, status?: string) => {
    setContractsLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getContracts(targetPatientId || patientId, status);
      setContracts(data);
    } catch (err) {
      setError(`Failed to fetch contracts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setContractsLoading(false);
    }
  }, [patientId]);

  const fetchTemplates = useCallback(async () => {
    setTemplatesLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getExamTemplates();
      setTemplates(data);
    } catch (err) {
      setError(`Failed to fetch templates: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setTemplatesLoading(false);
    }
  }, []);

  const fetchSummary = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await medicalReportService.getMedicalReportSummary(targetPatientId || patientId, dateRange);
      setSummary(data);
      setExamReports(data.examReports);
      setBillingInvoices(data.billingInvoices);
      setBillingQuotes(data.billingQuotes);
      setPayments(data.payments);
      setContracts(data.contracts);
      setTemplates(data.templates);
    } catch (err) {
      setError(`Failed to fetch medical report summary: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [patientId, dateRange]);

  const refreshAll = useCallback(async (targetPatientId?: string) => {
    setLoading(true);
    try {
      await Promise.all([
        fetchExamReports(targetPatientId),
        fetchBillingInvoices(targetPatientId),
        fetchBillingQuotes(targetPatientId),
        fetchPayments(targetPatientId),
        fetchContracts(targetPatientId),
        fetchTemplates(),
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchExamReports, fetchBillingInvoices, fetchBillingQuotes, fetchPayments, fetchContracts, fetchTemplates]);

  const createExamReport = useCallback(async (reportData: Omit<ExamReport, 'id' | 'created_at' | 'updated_at'>) => {
    setError(null);
    try {
      const newReport = await medicalReportService.createExamReport(reportData);
      setExamReports(prev => [newReport, ...prev]);
    } catch (err) {
      setError(`Failed to create exam report: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const updateExamReport = useCallback(async (id: string, reportData: Partial<ExamReport>) => {
    setError(null);
    try {
      const updatedReport = await medicalReportService.updateExamReport(id, reportData);
      setExamReports(prev => prev.map(report => report.id === id ? updatedReport : report));
    } catch (err) {
      setError(`Failed to update exam report: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  const deleteExamReport = useCallback(async (id: string) => {
    setError(null);
    try {
      await medicalReportService.deleteExamReport(id);
      setExamReports(prev => prev.filter(report => report.id !== id));
    } catch (err) {
      setError(`Failed to delete exam report: ${err instanceof Error ? err.message : 'Unknown error'}`);
      throw err;
    }
  }, []);

  // Utility functions
  const getReportsByPatient = useCallback((targetPatientId: string) => {
    return examReports.filter(r => r.patient_id === targetPatientId);
  }, [examReports]);

  const getInvoicesByPatient = useCallback((targetPatientId: string) => {
    return billingInvoices.filter(i => i.patient_id === targetPatientId);
  }, [billingInvoices]);

  const getQuotesByPatient = useCallback((targetPatientId: string) => {
    return billingQuotes.filter(q => q.patient_id === targetPatientId);
  }, [billingQuotes]);

  const getPaymentsByPatient = useCallback((targetPatientId: string) => {
    return payments.filter(p => p.patient_id === targetPatientId);
  }, [payments]);

  const getContractsByPatient = useCallback((targetPatientId: string) => {
    return contracts.filter(c => c.patient_id === targetPatientId);
  }, [contracts]);

  const getPatientMedicalStats = useCallback((targetPatientId: string) => {
    const patientReports = getReportsByPatient(targetPatientId);
    const patientInvoices = getInvoicesByPatient(targetPatientId);
    const patientQuotes = getQuotesByPatient(targetPatientId);
    const patientPayments = getPaymentsByPatient(targetPatientId);
    const patientContracts = getContractsByPatient(targetPatientId);

    const totalRevenue = patientPayments.reduce((sum, p) => sum + p.amount, 0);
    const pendingAmount = patientInvoices
      .filter(i => i.status !== 'paid')
      .reduce((sum, i) => sum + (i.total_amount - i.paid_amount), 0);

    const lastReport = patientReports
      .sort((a, b) => new Date(b.exam_date).getTime() - new Date(a.exam_date).getTime())[0];

    const activeContracts = patientContracts.filter(c => c.status === 'active').length;

    return {
      totalReports: patientReports.length,
      totalInvoices: patientInvoices.length,
      totalQuotes: patientQuotes.length,
      totalPayments: patientPayments.length,
      totalContracts: patientContracts.length,
      totalRevenue,
      pendingAmount,
      lastReportDate: lastReport?.exam_date || null,
      activeContracts,
    };
  }, [getReportsByPatient, getInvoicesByPatient, getQuotesByPatient, getPaymentsByPatient, getContractsByPatient]);

  const getBillingStats = useCallback(() => {
    const totalRevenue = payments.reduce((sum, p) => sum + p.amount, 0);
    const pendingRevenue = billingInvoices
      .filter(i => i.status !== 'paid')
      .reduce((sum, i) => sum + (i.total_amount - i.paid_amount), 0);

    const paidInvoices = billingInvoices.filter(i => i.status === 'paid').length;
    const unpaidInvoices = billingInvoices.filter(i => i.status !== 'paid').length;
    const activeQuotes = billingQuotes.filter(q => q.status === 'sent' || q.status === 'draft').length;
    const expiredQuotes = billingQuotes.filter(q => q.status === 'expired').length;

    return {
      totalRevenue,
      pendingRevenue,
      paidInvoices,
      unpaidInvoices,
      activeQuotes,
      expiredQuotes,
    };
  }, [payments, billingInvoices, billingQuotes]);

  // Auto-fetch on mount and when options change
  useEffect(() => {
    if (autoFetch) {
      if (reportTypes && reportTypes.length > 0) {
        // Fetch specific report types
        reportTypes.forEach(type => {
          switch (type) {
            case 'reports':
              fetchExamReports();
              break;
            case 'billing':
              fetchBillingInvoices();
              fetchBillingQuotes();
              break;
            case 'payments':
              fetchPayments();
              break;
            case 'contracts':
              fetchContracts();
              break;
            case 'templates':
              fetchTemplates();
              break;
            default:
              break;
          }
        });
      } else {
        // Fetch all data
        refreshAll();
      }
    }
  }, [autoFetch, reportTypes, refreshAll, fetchExamReports, fetchBillingInvoices, fetchBillingQuotes, fetchPayments, fetchContracts, fetchTemplates]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, refreshAll]);

  return {
    // Data
    examReports,
    billingInvoices,
    billingQuotes,
    payments,
    contracts,
    templates,
    summary,
    
    // Loading states
    loading,
    examReportsLoading,
    billingLoading,
    paymentsLoading,
    contractsLoading,
    templatesLoading,
    
    // Error state
    error,
    
    // Actions
    fetchExamReports,
    fetchBillingInvoices,
    fetchBillingQuotes,
    fetchPayments,
    fetchContracts,
    fetchTemplates,
    fetchSummary,
    refreshAll,
    createExamReport,
    updateExamReport,
    deleteExamReport,
    
    // Utility functions
    getReportsByPatient,
    getInvoicesByPatient,
    getQuotesByPatient,
    getPaymentsByPatient,
    getContractsByPatient,
    getPatientMedicalStats,
    getBillingStats,
  };
};

export default useMedicalReport;
