#!/usr/bin/env node

/**
 * Test script to verify duplicate email handling in appointment creation
 * This script tests the patient identity verification and duplicate email detection
 */

// Import required modules
const https = require('https');
const http = require('http');
const fs = require('fs');

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://127.0.0.1:8000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PATIENT_1 = {
  // User fields (required for PatientCreateSerializer)
  email: TEST_EMAIL,
  password: 'TestPass123!',
  password2: 'TestPass123!',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  phone_number: '**********',
  address: '123 Test Street',
  date_of_birth: '1990-01-01',
  gender: 'Homme',
  
  // Patient fields
  marital_status: 'Single', // Using English value as per Django model
  cin: 'CIN123456',
  social_security: 'SS123456789',
  profession: 'Engineer',
  allergies: 'None',
  medical_history: 'No known conditions'
};

const TEST_PATIENT_2 = {
  // User fields (required for PatientCreateSerializer)
  email: TEST_EMAIL, // Same email as TEST_PATIENT_1
  password: 'TestPass456!',
  password2: 'TestPass456!',
  first_name: 'Jane',
  last_name: 'Smith',
  phone_number: '**********',
  address: '456 Different Street',
  date_of_birth: '1995-05-05',
  gender: 'Femme',
  
  // Patient fields
  marital_status: 'Married', // Using English value as per Django model
  cin: 'CIN789012',
  social_security: 'SS987654321',
  profession: 'Doctor',
  allergies: 'Penicillin',
  medical_history: 'Seasonal allergies'
};

console.log('🧪 Testing Duplicate Email Handling');
console.log('====================================');

/**
 * Make HTTP request to API
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = API_BASE_URL.startsWith('https') ? https : http;
    const req = protocol.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      console.log('Sending data:', JSON.stringify(data));
      const jsonData = JSON.stringify(data);
      req.setHeader('Content-Length', Buffer.byteLength(jsonData));
      req.write(jsonData);
    }
    
    req.end();
  });
}

/**
 * Create a patient
 */
async function createPatient(patientData) {
  console.log(`\n👤 Creating patient: ${patientData.first_name} ${patientData.last_name}`);
  
  const options = {
    hostname: new URL(API_BASE_URL).hostname,
    port: new URL(API_BASE_URL).port,
    path: '/api/users/patients/create-from-frontend/',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, patientData);
    console.log(`Status: ${response.statusCode}`);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log('✅ Patient created/updated successfully');
      console.log(`Patient ID: ${response.data.patient_id}`);
      return response.data;
    } else {
      console.log('❌ Patient creation failed');
      console.log('Response:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Error creating patient:', error.message);
    return null;
  }
}

/**
 * Search for patients by email
 */
async function searchPatients(email) {
  console.log(`\n🔍 Searching for patients with email: ${email}`);
  
  const options = {
    hostname: new URL(API_BASE_URL).hostname,
    port: new URL(API_BASE_URL).port,
    path: `/api/users/patients/search/?q=${encodeURIComponent(email)}`,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options);
    console.log(`Status: ${response.statusCode}`);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log(`✅ Found ${response.data.results?.length || 0} patients`);
      return response.data.results || [];
    } else {
      console.log('❌ Patient search failed');
      console.log('Response:', response.data);
      return [];
    }
  } catch (error) {
    console.log('❌ Error searching patients:', error.message);
    return [];
  }
}

/**
 * Create an appointment
 */
async function createAppointment(appointmentData) {
  console.log(`\n📅 Creating appointment for: ${appointmentData.patient_first_name} ${appointmentData.patient_last_name}`);
  
  const options = {
    hostname: new URL(API_BASE_URL).hostname,
    port: new URL(API_BASE_URL).port,
    path: '/api/appointments/',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, appointmentData);
    console.log(`Status: ${response.statusCode}`);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log('✅ Appointment created successfully');
      console.log(`Appointment ID: ${response.data.id}`);
      return response.data;
    } else {
      console.log('❌ Appointment creation failed');
      console.log('Response:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Error creating appointment:', error.message);
    return null;
  }
}

/**
 * Main test function
 */
async function runTest() {
  console.log('Starting duplicate email handling test...\n');
  
  // Step 1: Create first patient
  console.log('Step 1: Creating first patient with email');
  const patient1 = await createPatient(TEST_PATIENT_1);
  if (!patient1) {
    console.log('❌ Failed to create first patient');
    return;
  }
  
  // Step 2: Verify patient was created
  console.log('\nStep 2: Verifying patient creation');
  const patients1 = await searchPatients(TEST_EMAIL);
  if (patients1.length === 0) {
    console.log('❌ No patients found with the email');
    return;
  }
  console.log(`✅ Found ${patients1.length} patient(s) with email ${TEST_EMAIL}`);
  
  // Step 3: Try to create appointment with same email but different patient info
  console.log('\nStep 3: Creating appointment with same email but different patient');
  const appointmentData1 = {
    title: `Consultation - ${TEST_PATIENT_1.first_name} ${TEST_PATIENT_1.last_name}`,
    description: 'Test appointment',
    appointment_type: 'consultation',
    status: 'scheduled',
    appointment_date: '2025-09-01',
    appointment_time: '10:00:00',
    duration_minutes: 30,
    patient_first_name: TEST_PATIENT_1.first_name,
    patient_last_name: TEST_PATIENT_1.last_name,
    patient_email: TEST_PATIENT_1.email,
    patient_phone: TEST_PATIENT_1.phone_number,
    patient_address: TEST_PATIENT_1.address
  };
  
  const appointment1 = await createAppointment(appointmentData1);
  if (!appointment1) {
    console.log('❌ Failed to create first appointment');
    return;
  }
  
  // Step 4: Try to create second patient with same email but different info
  console.log('\nStep 4: Attempting to create second patient with same email');
  const patient2 = await createPatient(TEST_PATIENT_2);
  
  if (patient2 && !patient2.existing) {
    console.log('⚠️ Second patient created - this might indicate an issue with duplicate detection');
    
    // Step 5: Try to create appointment with second patient info
    console.log('\nStep 5: Creating appointment with second patient info');
    const appointmentData2 = {
      title: `Consultation - ${TEST_PATIENT_2.first_name} ${TEST_PATIENT_2.last_name}`,
      description: 'Test appointment',
      appointment_type: 'consultation',
      status: 'scheduled',
      appointment_date: '2025-09-02',
      appointment_time: '11:00:00',
      duration_minutes: 30,
      patient_first_name: TEST_PATIENT_2.first_name,
      patient_last_name: TEST_PATIENT_2.last_name,
      patient_email: TEST_PATIENT_2.email,
      patient_phone: TEST_PATIENT_2.phone_number,
      patient_address: TEST_PATIENT_2.address
    };
    
    const appointment2 = await createAppointment(appointmentData2);
    if (appointment2) {
      console.log('⚠️ Second appointment created - this might indicate an issue with duplicate detection');
    } else {
      console.log('✅ Second appointment correctly blocked');
    }
  } else if (patient2 && patient2.existing) {
    console.log('✅ Second patient correctly identified as existing patient');
  } else {
    console.log('✅ Second patient correctly blocked due to duplicate email');
  }
  
  // Step 6: Verify final state
  console.log('\nStep 6: Verifying final state');
  const finalPatients = await searchPatients(TEST_EMAIL);
  console.log(`Final count of patients with email ${TEST_EMAIL}: ${finalPatients.length}`);
  
  console.log('\n🏁 Test completed');
  console.log('====================================');
  if (finalPatients.length === 1) {
    console.log('✅ PASS: Duplicate email handling is working correctly');
  } else {
    console.log('❌ FAIL: Duplicate email handling may have issues');
  }
}

// Run the test
runTest().catch(error => {
  console.error('Test failed with error:', error);
});