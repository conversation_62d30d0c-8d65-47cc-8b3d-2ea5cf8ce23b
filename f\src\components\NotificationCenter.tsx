import React, { useState, useEffect } from 'react';
import {
  Popover,
  ActionIcon,
  Badge,
  Text,
  Group,
  Stack,
  Button,
  Divider,
  ScrollArea,
  Box,
  Card,
  Loader,
  Center,
  ThemeIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconBell,
  IconCalendarTime,
  IconCreditCard,
  IconCheck,
  IconX,
  IconInfoCircle,
} from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
//import api from '~/lib/api';
import { AxiosError } from 'axios';
import { formatDistanceToNow } from 'date-fns';

interface Notification {
  id: string;
  notification_type: string;
  title: string;
  message: string;
  is_read: boolean;
  priority: string;
  action_url?: string;
  days_until_expiration?: number;
  expiration_date?: string;
  created_at: string;
}

export default function NotificationCenter() {
  const [opened, setOpened] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      //const response = await api.get('/api/notifications/');
      //setNotifications(response.data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      const err = error as AxiosError;
      setError(err.response?.status === 404 ? 'No notifications found' : 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (opened) {
      fetchNotifications();
    }
  }, [opened]);

  const handleMarkAsRead = async (id: string) => {
    try {
     // await api.post(`/api/notifications/${id}/mark_read/`);
      setNotifications(
        notifications.map((notification) =>
          notification.id === id ? { ...notification, is_read: true } : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  const handleDismiss = async (id: string) => {
    try {
     // await api.post(`/api/notifications/${id}/dismiss/`);
      setNotifications(notifications.filter((notification) => notification.id !== id));
    } catch (err) {
      console.error('Error dismissing notification:', err);
    }
  };

  const handleMarkAllRead = async () => {
    try {
     // await api.post('/api/notifications/mark_all_read/');
      setNotifications(
        notifications.map((notification) => ({ ...notification, is_read: true }))
      );
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  const handleDismissAll = async () => {
    try {
     // await api.post('/api/notifications/dismiss_all/');
      setNotifications([]);
    } catch (err) {
      console.error('Error dismissing all notifications:', err);
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (notification.action_url) {
      router.push(notification.action_url);
    }
    handleMarkAsRead(notification.id);
    setOpened(false);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'subscription_expiring':
      case 'trial_expiring':
        return (
          <ThemeIcon color="orange" variant="light" size="lg" radius="xl">
            <IconCalendarTime size={18} />
          </ThemeIcon>
        );
      case 'subscription_expired':
      case 'trial_expired':
        return (
          <ThemeIcon color="red" variant="light" size="lg" radius="xl">
            <IconCreditCard size={18} />
          </ThemeIcon>
        );
      default:
        return (
          <ThemeIcon color="blue" variant="light" size="lg" radius="xl">
            <IconInfoCircle size={18} />
          </ThemeIcon>
        );
    }
  };

  const unreadCount = notifications.filter((n) => !n.is_read).length;

  return (
    <Popover
      width={350}
      position="bottom-end"
      shadow="md"
      opened={opened}
      onChange={setOpened}
    >
      <Popover.Target>
        <div style={{ position: 'relative' }}>
          <ActionIcon
            variant="default"
            size={36}
            onClick={() => setOpened((o) => !o)}
            aria-label="Notifications"
          >
            <IconBell size={20} />
          </ActionIcon>
          {unreadCount > 0 && (
            <Badge
              size="xs"
              color="red"
              variant="filled"
              style={{
                position: 'absolute',
                top: -5,
                right: -5,
                padding: '0 4px',
                minWidth: 16,
                height: 16,
                pointerEvents: 'none',
              }}
            >
              {unreadCount}
            </Badge>
          )}
        </div>
      </Popover.Target>

      <Popover.Dropdown>
        <Group justify="space-between" mb="xs">
          <Text fw={600}>Notifications</Text>
          <Group gap="xs">
            <Tooltip label="Mark all as read">
              <ActionIcon size="sm" onClick={handleMarkAllRead}>
                <IconCheck size={14} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Dismiss all">
              <ActionIcon size="sm" onClick={handleDismissAll}>
                <IconX size={14} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>

        <Divider mb="xs" />

        {loading ? (
          <Center p="md">
            <Loader size="sm" />
          </Center>
        ) : error ? (
          <Text c="red" size="sm" p="md">
            {error}
          </Text>
        ) : notifications.length === 0 ? (
          <Text c="dimmed" size="sm" ta="center" p="md">
            No notifications
          </Text>
        ) : (
          <ScrollArea h={400} offsetScrollbars>
            <Stack gap="xs">
              {notifications.map((notification) => (
                <Card
                  key={notification.id}
                  p="sm"
                  withBorder
                  styles={(theme) => ({
                    root: {
                      backgroundColor: notification.is_read
                        ? theme.colors.dark[6]
                        : theme.colors.gray[0],
                      cursor: notification.action_url ? 'pointer' : 'default',
                      '&:hover': notification.action_url
                        ? {
                            backgroundColor: theme.colors.gray[1],
                          }
                        : {},
                    }
                  })}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <Group wrap="nowrap" align="flex-start">
                    {getNotificationIcon(notification.notification_type)}
                    <Box style={{ flex: 1 }}>
                      <Group justify="space-between" wrap="nowrap">
                        <Text size="sm" fw={notification.is_read ? 400 : 700}>
                          {notification.title}
                        </Text>
                        <Group gap={4} wrap="nowrap">
                          <Tooltip label="Dismiss">
                            <ActionIcon
                              size="xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDismiss(notification.id);
                              }}
                            >
                              <IconX size={12} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                      <Text size="xs" c="dimmed" lineClamp={2}>
                        {notification.message}
                      </Text>
                      <Group justify="space-between" mt={4}>
                        {notification.days_until_expiration !== undefined && (
                          <Badge
                            size="xs"
                            color={
                              notification.days_until_expiration > 5
                                ? 'blue'
                                : notification.days_until_expiration > 0
                                ? 'orange'
                                : 'red'
                            }
                          >
                            {notification.days_until_expiration > 0
                              ? `${notification.days_until_expiration} days left`
                              : 'Expired'}
                          </Badge>
                        )}
                        <Text size="xs" c="dimmed">
                          {formatDistanceToNow(new Date(notification.created_at), {
                            addSuffix: true,
                          })}
                        </Text>
                      </Group>
                    </Box>
                  </Group>
                </Card>
              ))}
            </Stack>
          </ScrollArea>
        )}

        {notifications.length > 0 && (
          <>
            <Divider my="xs" />
            <Group justify="center">
              <Button
                variant="subtle"
                size="xs"

                onClick={() => router.push('/notifications')}
              >
                View All
              </Button>
            </Group>
          </>
        )}
      </Popover.Dropdown>
    </Popover>
  );
}
