import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown, Edit, Printer, DollarSign, Search } from 'lucide-react';

interface InvoiceData {
  id: number;
  number: number;
  date: string;
  beneficiary_name: string;
  taxed_amount: number;
  insurance: string;
  payment_mode: string;
}

interface ColumnTable {
  key: keyof InvoiceData;
  label: string;
  type: string;
  searchable: boolean;
  sortable: boolean;
}

interface SortConfig {
  key: keyof InvoiceData | null;
  direction: 'asc' | 'desc';
}

interface SearchFilters {
  number: string;
  date: string;
  beneficiary_name: string;
  taxed_amount: string;
  insurance: string;
  payment_mode: string;
}

const InvoiceTable = () => {
  const [data, ] = useState<InvoiceData[]>([
    {
      id: 1,
      number: 8,
      date: '16/06/2025',
      beneficiary_name: 'JAMAL RIIDA',
      taxed_amount: 500.00,
      insurance: 'CNSS',
      payment_mode: 'Espèce'
    },
    {
      id: 2,
      number: 9,
      date: '17/06/2025',
      beneficiary_name: 'SARA ALAMI',
      taxed_amount: 750.00,
      insurance: 'AMO',
      payment_mode: 'Chèque'
    },
    {
      id: 3,
      number: 10,
      date: '18/06/2025',
      beneficiary_name: 'AHMED BENALI',
      taxed_amount: 300.00,
      insurance: 'CNSS',
      payment_mode: 'Carte'
    }
  ]);

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'asc' });
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    number: '',
    date: '',
    beneficiary_name: '',
    taxed_amount: '',
    insurance: '',
    payment_mode: ''
  });
  const [filteredData, setFilteredData] = useState<InvoiceData[]>(data);

  const columnsTable: ColumnTable[] = [
    { key: 'number', label: 'N°. Facture', type: 'number', searchable: true, sortable: true },
    { key: 'date', label: 'Date', type: 'date', searchable: true, sortable: true },
    { key: 'beneficiary_name', label: 'Bénéficiaire', type: 'text', searchable: true, sortable: true },
    { key: 'taxed_amount', label: 'Montant Total', type: 'currency', searchable: true, sortable: true },
    { key: 'insurance', label: 'Assurance', type: 'text', searchable: true, sortable: true },
    { key: 'payment_mode', label: 'Mode de paiement', type: 'text', searchable: true, sortable: true }
  ];

  // Fonction de tri
  const handleSort = (key: keyof InvoiceData) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Fonction de recherche
  const handleSearch = (ColumnTable: keyof SearchFilters, value: string) => {
    setSearchFilters(prev => ({
      ...prev,
      [ColumnTable]: value
    }));
  };

  // Fonction de sélection
  const toggleRowSelection = (id: number) => {
    setSelectedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  const toggleAllSelection = () => {
    setSelectedRows(
      selectedRows.length === filteredData.length ? [] : filteredData.map(row => row.id)
    );
  };

  // Actions sur les lignes
  const handleEdit = (row: InvoiceData) => {
    console.log('Modifier:', row);
  };

  const handlePrint = (row: InvoiceData) => {
    console.log('Imprimer:', row);
  };

  const handlePayment = (row: InvoiceData) => {
    console.log('Paiement:', row);
  };

  // Formatage de la devise
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Effet pour filtrer et trier les données
  useEffect(() => {
    let filtered = data.filter(row => {
      return Object.keys(searchFilters).every(key => {
        const filterValue = searchFilters[key as keyof SearchFilters].toLowerCase();
        const rowValue = String(row[key as keyof InvoiceData] || '').toLowerCase();
        return rowValue.includes(filterValue);
      });
    });

    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.key as keyof InvoiceData];
        const bVal = b[sortConfig.key as keyof InvoiceData];
        
        if (sortConfig.direction === 'asc') {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });
    }

    setFilteredData(filtered);
  }, [data, searchFilters, sortConfig]);

  const getSortIcon = (columnKey: keyof InvoiceData) => {
    if (sortConfig.key !== columnKey) {
      return <ChevronUp className="w-4 h-4 opacity-30" />;
    }
    return sortConfig.direction === 'asc' 
      ? <ChevronUp className="w-4 h-4" />
      : <ChevronDown className="w-4 h-4" />;
  };

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="w-12 px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedRows.length === filteredData.length && filteredData.length > 0}
                  onChange={toggleAllSelection}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              {columnsTable.map((column) => (
                <th
                  key={column.key}
                  className={`px-4 py-3 text-left font-medium text-gray-700 cursor-pointer hover:bg-gray-100 ${
                    column.type === 'currency' ? 'text-right' : ''
                  }`}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center justify-between">
                    <span>{column.label}</span>
                    {column.sortable && getSortIcon(column.key)}
                  </div>
                </th>
              ))}
              <th className="px-4 py-3 text-left font-medium text-gray-700 w-32">Actions</th>
            </tr>
          </thead>
          
          <tbody className="bg-white">
            {/* Ligne de recherche */}
            <tr className="border-b bg-gray-50">
              <td className="px-4 py-2"></td>
              {columnsTable.map((column) => (
                <td key={`search-${column.key}`} className="px-4 py-2">
                  {column.searchable && (
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Rechercher"
                        value={searchFilters[column.key]}
                        onChange={(e) => handleSearch(column.key, e.target.value)}
                        className="w-full pl-8 pr-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  )}
                </td>
              ))}
              <td className="px-4 py-2"></td>
            </tr>
            
            {/* Données */}
            {filteredData.map((row) => (
              <tr
                key={row.id}
                className={`border-b hover:bg-gray-50 ${
                  selectedRows.includes(row.id) ? 'bg-blue-50' : ''
                }`}
              >
                <td className="px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedRows.includes(row.id)}
                    onChange={() => toggleRowSelection(row.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900">
                  {row.number}
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900">
                  {row.date}
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900">
                  {row.beneficiary_name}
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900 text-right font-medium">
                  {formatCurrency(row.taxed_amount)}
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900">
                  {row.insurance}
                </td>
                
                <td className="px-4 py-3 text-sm text-gray-900">
                  {row.payment_mode}
                </td>
                
                <td className="px-4 py-3">
                  <div className="flex space-x-1">
                    <button
                      onClick={() => handleEdit(row)}
                      className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                      title="Modifier"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => handlePrint(row)}
                      className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-full transition-colors"
                      title="Imprimer"
                    >
                      <Printer className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => handlePayment(row)}
                      className="p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-full transition-colors"
                      title="Paiement"
                    >
                      <DollarSign className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            
            {filteredData.length === 0 && (
              <tr>
                <td colSpan={columnsTable.length + 2} className="px-4 py-8 text-center text-gray-500">
                  Aucune donnée trouvée
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {/* Informations sur la sélection */}
      {selectedRows.length > 0 && (
        <div className="px-4 py-3 bg-blue-50 border-t">
          <span className="text-sm text-blue-700">
            {selectedRows.length} ligne(s) sélectionnée(s)
          </span>
        </div>
      )}
    </div>
  );
};

export default InvoiceTable;