import React, { useState } from 'react';
import {
  Stack,
  Title,
  Paper,
  Group,
  Text,
  TextInput,
  Button,
  Grid,
  Box,
  Table,
  Checkbox,
  Select,
  Alert,
} from '@mantine/core';
import {
  IconClipboardList,
  IconPlus,
  IconAlertCircle,
} from '@tabler/icons-react';

// Types
interface Question {
  id: string;
  titre: string;
  obligatoire: boolean;
  type: 'text' | 'select' | 'checkbox' | 'textarea' | 'number';
}

const Questionnaires_patient = () => {
  const [activeSection, setActiveSection] = useState('questionnaires');
  const [searchTitle, setSearchTitle] = useState('');
  const [questions, setQuestions] = useState<Question[]>([]);

  return (
    <Stack gap="lg" className="p-6">
      {/* Header */}
      <Paper className="bg-blue-600 text-white p-4 rounded-lg">
        <Group gap="sm" align="center" justify="space-between">
          <Group gap="sm" align="center">
            <IconClipboardList size={24} />
            <Title order={3} className="text-white">
              Questionnaires patient
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="white"
            color="blue"
            size="sm"
          >
            Ajouter
          </Button>
        </Group>
      </Paper>

      {/* Main Content */}
      <Grid>
        {/* Sidebar */}
        <Grid.Col span={3}>
          <Paper shadow="sm" p="md" radius="md" withBorder className="h-full">
            <Stack gap="sm">
              {/* Questionnaires Section */}
              <Box
                className={`p-3 rounded cursor-pointer ${
                  activeSection === 'questionnaires'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setActiveSection('questionnaires')}
              >
                <Group gap="sm" align="center">
                  <IconClipboardList size={16} />
                  <Text size="sm" fw={500}>
                    Questionnaires
                  </Text>
                </Group>
              </Box>

              {/* No Elements Found */}
              <Alert
                icon={<IconAlertCircle size={16} />}
                color="orange"
                variant="light"
                className="mt-4"
              >
                <Text size="sm">
                  Aucun élément trouvé
                </Text>
              </Alert>
            </Stack>
          </Paper>
        </Grid.Col>

        {/* Main Content Area */}
        <Grid.Col span={9}>
          <Paper shadow="sm" p="md" radius="md" withBorder className="h-full">
            <Stack gap="lg">
              {/* Title Search Field */}
              <Box>
                <Text size="sm" fw={500} mb={5} className="text-red-500">
                  Titre *
                </Text>
                <TextInput
                  placeholder="Titre"
                  value={searchTitle}
                  onChange={(event) => setSearchTitle(event.currentTarget.value)}
                  size="sm"
                />
              </Box>

              {/* Questions Table */}
              <Box>
                <Table striped highlightOnHover withTableBorder>
                  <Table.Thead>
                    <Table.Tr className="bg-gray-50">
                      <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                        Titre
                      </Table.Th>
                      <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                        Obligatoire
                      </Table.Th>
                      <Table.Th className="font-semibold text-gray-700 text-center">
                        Type
                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {questions.length === 0 ? (
                      <Table.Tr>
                        <Table.Td colSpan={3} className="text-center py-8">
                          <Text className="text-blue-500">
                            Aucun élément trouvé
                          </Text>
                        </Table.Td>
                      </Table.Tr>
                    ) : (
                      questions.map((question) => (
                        <Table.Tr key={question.id} className="hover:bg-gray-50">
                          <Table.Td className="border-r border-gray-200">
                            {question.titre}
                          </Table.Td>
                          <Table.Td className="text-center border-r border-gray-200">
                            <Checkbox
                              checked={question.obligatoire}
                              onChange={(event) => {
                                setQuestions(prev =>
                                  prev.map(q =>
                                    q.id === question.id
                                      ? { ...q, obligatoire: event.currentTarget.checked }
                                      : q
                                  )
                                );
                              }}
                              size="sm"
                            />
                          </Table.Td>
                          <Table.Td className="text-center">
                            <Select
                              value={question.type}
                              data={[
                                { value: 'text', label: 'Texte' },
                                { value: 'select', label: 'Liste déroulante' },
                                { value: 'checkbox', label: 'Case à cocher' },
                                { value: 'textarea', label: 'Zone de texte' },
                                { value: 'number', label: 'Nombre' },
                              ]}
                              onChange={(value) => {
                                setQuestions(prev =>
                                  prev.map(q =>
                                    q.id === question.id
                                      ? { ...q, type: value as Question['type'] }
                                      : q
                                  )
                                );
                              }}
                              size="sm"
                              className="w-full"
                            />
                          </Table.Td>
                        </Table.Tr>
                      ))
                    )}
                  </Table.Tbody>
                </Table>
              </Box>

              {/* Action Buttons */}
              <Group justify="flex-end" gap="sm">
                <Button
                  variant="outline"
                  color="gray"
                  size="sm"
                >
                  Enregistrer
                </Button>
                <Button
                  variant="light"
                  color="blue"
                  size="sm"
                  leftSection={<IconPlus size={16} />}
                  onClick={() => {
                    const newQuestion: Question = {
                      id: Date.now().toString(),
                      titre: 'Nouvelle question',
                      obligatoire: false,
                      type: 'text'
                    };
                    setQuestions(prev => [...prev, newQuestion]);
                  }}
                >
                  Ajouter question
                </Button>
              </Group>
            </Stack>
          </Paper>
        </Grid.Col>
      </Grid>
    </Stack>
  );
};

export default Questionnaires_patient;
