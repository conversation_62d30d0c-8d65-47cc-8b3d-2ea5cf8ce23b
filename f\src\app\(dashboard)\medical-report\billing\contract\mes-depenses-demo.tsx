'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { MesDepenses } from './Mes_depenses';

export default function MesDepensesDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Recherche de dépenses:\nTerme: "${query.searchAll}"\nPage: ${query.page}\nLignes: ${query.limit}\nFiltres: ${Object.keys(query.filters).length} actifs\nRecherche par colonne: ${Object.keys(query.search).length} actives`);
  };

  const handleAddExpense = () => {
    console.log('Ajouter dépense');
    alert(`Création d'une nouvelle dépense:\nOuverture du formulaire de saisie\nChamps: Date, Marchand, Nom, Prénom, N°.ref, Montant TTC`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export:', format);
    alert(`Export Excel des dépenses en cours...\nFormat: ${format.toUpperCase()}\nTéléchargement démarré`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action:', action, 'Items:', items);
    const actionLabels: { [key: string]: string } = {
      'reload': 'Actualiser'
    };
    const actionLabel = actionLabels[action] || action;
    alert(`Action: ${actionLabel}\nNombre d'éléments sélectionnés: ${items.length}\nTraitement en cours...`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={[]}
          total={1}
          onQueryChange={handleQueryChange}
          onAddExpense={handleAddExpense}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function MesDepensesLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={true}
          items={[]}
          total={0}
          onQueryChange={(query) => console.log('Query:', query)}
          onAddExpense={() => console.log('Add expense')}
          onExport={(format) => console.log('Export:', format)}
          onAction={(action, items) => console.log('Action:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function MesDepensesWithDataDemo() {
  const sampleItems = [
    {
      id: '1',
      date: new Date('2025-07-02'),
      merchant: 'Pharmacie Centrale',
      lastName: 'MARTIN',
      firstName: 'Jean',
      refNumber: 'REF-001',
      taxedAmount: 125.50
    },
    {
      id: '2',
      date: new Date('2025-07-01'),
      merchant: 'Matériel Médical Pro',
      lastName: 'DUPONT',
      firstName: 'Marie',
      refNumber: 'REF-002',
      taxedAmount: 450.00
    },
    {
      id: '3',
      date: new Date('2025-06-30'),
      merchant: 'Fournitures Bureau',
      lastName: 'BERNARD',
      firstName: 'Paul',
      refNumber: 'REF-003',
      taxedAmount: 89.75
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    alert(`Recherche dans ${sampleItems.length} dépenses:\nTerme: "${query.searchAll}"\nPage: ${query.page}/${Math.ceil(sampleItems.length / query.limit)}\nLignes par page: ${query.limit}\nFiltres actifs: ${Object.keys(query.filters).length}\nRecherche par colonne: ${Object.keys(query.search).length}`);
  };

  const handleAddExpense = () => {
    console.log('Ajouter dépense avec données');
    alert(`Nouvelle dépense:\n\nProchain numéro: REF-${String(sampleItems.length + 1).padStart(3, '0')}\nFormulaire de création ouvert\nDonnées pré-remplies disponibles\nMontant total actuel: ${sampleItems.reduce((sum, item) => sum + item.taxedAmount, 0).toFixed(2)}€`);
  };

  const handleExport = (format: 'excel') => {
    console.log('Export avec données:', format);
    alert(`Export Excel de ${sampleItems.length} dépenses:\n\nContenu:\n- Total des dépenses: ${sampleItems.reduce((sum, item) => sum + item.taxedAmount, 0).toFixed(2)}€\n- Marchands: ${new Set(sampleItems.map(item => item.merchant)).size}\n- Période: ${new Date(Math.min(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')} - ${new Date(Math.max(...sampleItems.map(item => item.date.getTime()))).toLocaleDateString('fr-FR')}\n\nTéléchargement en cours...`);
  };

  const handleAction = (action: string, items: any[]) => {
    console.log('Action avec données:', action, items);
    
    const actionMessages: { [key: string]: string } = {
      'reload': `Actualisation des données:\n- ${sampleItems.length} dépenses rechargées\n- Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}\n- Total: ${sampleItems.reduce((sum, item) => sum + item.taxedAmount, 0).toFixed(2)}€`
    };
    
    const message = actionMessages[action] || `Action ${action} sur ${items.length} élément(s)`;
    alert(message);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={sampleItems}
          total={sampleItems.length}
          onQueryChange={handleQueryChange}
          onAddExpense={handleAddExpense}
          onExport={handleExport}
          onAction={handleAction}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec filtres
export function MesDepensesFiltersDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Filtres:', query);
    if (query.searchAll) {
      alert(`Recherche avancée:\nTerme: "${query.searchAll}"\nRecherche dans: Marchands, Noms, Prénoms, N°.ref, etc.`);
    }
    if (Object.keys(query.filters).length > 0) {
      alert(`Filtres appliqués:\n${Object.entries(query.filters).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`);
    }
    if (Object.keys(query.search).length > 0) {
      alert(`Recherche par colonne:\n${Object.entries(query.search).map(([key, value]) => `- ${key}: "${value}"`).join('\n')}`);
    }
  };

  const handleAddExpense = () => {
    console.log('Ajouter avec filtres');
    alert(`Nouvelle dépense:\nLes filtres actuels seront conservés après création`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddExpense={handleAddExpense}
          onExport={(format) => alert(`Export ${format} avec filtres appliqués`)}
          onAction={(action, items) => console.log('Action filtres:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec pagination
export function MesDepensesPaginationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Pagination:', query);
    alert(`Navigation:\nPage: ${query.page}\nÉléments par page: ${query.limit}\nNavigation dans les dépenses`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={[]}
          total={150} // Simule 150 dépenses pour tester la pagination
          onQueryChange={handleQueryChange}
          onAddExpense={() => console.log('Add pagination')}
          onExport={(format) => console.log('Export pagination:', format)}
          onAction={(action, items) => console.log('Action pagination:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function MesDepensesErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    if (query.searchAll && query.searchAll.length < 2) {
      alert('Attention: Veuillez saisir au moins 2 caractères pour la recherche.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  const handleAddExpense = () => {
    console.log('Ajouter avec validation');
    if (confirm('Êtes-vous sûr de vouloir créer une nouvelle dépense ?')) {
      alert('Dépense créée avec succès !');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={[]}
          total={0}
          onQueryChange={handleQueryChange}
          onAddExpense={handleAddExpense}
          onExport={(format) => {
            if (confirm(`Êtes-vous sûr de vouloir exporter en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onAction={(action, items) => console.log('Action avec validation:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec types de dépenses
export function MesDepensesTypesDemo() {
  const sampleItemsWithTypes = [
    {
      id: '1',
      date: new Date('2025-07-02'),
      merchant: 'Pharmacie Centrale',
      lastName: 'MARTIN',
      firstName: 'Jean',
      refNumber: 'MED-001',
      taxedAmount: 125.50
    },
    {
      id: '2',
      date: new Date('2025-07-01'),
      merchant: 'Matériel Médical Pro',
      lastName: 'DUPONT',
      firstName: 'Marie',
      refNumber: 'MAT-002',
      taxedAmount: 450.00
    },
    {
      id: '3',
      date: new Date('2025-06-30'),
      merchant: 'Fournitures Bureau',
      lastName: 'BERNARD',
      firstName: 'Paul',
      refNumber: 'BUR-003',
      taxedAmount: 89.75
    }
  ];

  const handleQueryChange = (query: any) => {
    console.log('Requête par types:', query);
    if (query.searchAll) {
      const searchTerm = query.searchAll.toLowerCase();
      const matchingTypes = [];
      if (searchTerm.includes('med') || searchTerm.includes('pharma')) matchingTypes.push('Médicaments');
      if (searchTerm.includes('mat') || searchTerm.includes('médical')) matchingTypes.push('Matériel médical');
      if (searchTerm.includes('bur') || searchTerm.includes('bureau')) matchingTypes.push('Fournitures bureau');
      
      if (matchingTypes.length > 0) {
        alert(`Types de dépenses trouvés:\n${matchingTypes.join(', ')}`);
      }
    }
  };

  const handleAddExpense = () => {
    console.log('Ajouter par types');
    alert(`Nouvelle dépense:\nTypes disponibles:\n- Médicaments (MED-xxx)\n- Matériel médical (MAT-xxx)\n- Fournitures bureau (BUR-xxx)\n- Autres (AUT-xxx)`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <MesDepenses
          loading={false}
          items={sampleItemsWithTypes}
          total={sampleItemsWithTypes.length}
          onQueryChange={handleQueryChange}
          onAddExpense={handleAddExpense}
          onExport={(format) => alert(`Export ${format} par types de dépenses`)}
          onAction={(action, items) => console.log('Action types:', action, items)}
        />
      </div>
    </MantineProvider>
  );
}
