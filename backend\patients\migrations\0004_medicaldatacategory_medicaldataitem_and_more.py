# Generated by Django 5.1.3 on 2025-08-06 13:57

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("patients", "0003_patientattachment"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="MedicalDataCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Category name", max_length=200)),
                (
                    "category_type",
                    models.CharField(
                        choices=[
                            ("allergy", "Allergy"),
                            ("medication", "Medication"),
                            ("condition", "Medical Condition"),
                            ("treatment", "Treatment"),
                            ("pathology", "Pathology"),
                            ("symptom", "Symptom"),
                            ("procedure", "Medical Procedure"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Medical Data Category",
                "verbose_name_plural": "Medical Data Categories",
                "ordering": ["category_type", "name"],
                "indexes": [
                    models.Index(
                        fields=["category_type", "is_active"],
                        name="patients_me_categor_c19a76_idx",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="MedicalDataItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Item name", max_length=300)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "severity_level",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("mild", "Mild"),
                            ("moderate", "Moderate"),
                            ("severe", "Severe"),
                            ("critical", "Critical"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "synonyms",
                    models.TextField(
                        blank=True,
                        help_text="Comma-separated synonyms for search",
                        null=True,
                    ),
                ),
                (
                    "medical_code",
                    models.CharField(
                        blank=True,
                        help_text="Medical code (ICD-10, etc.)",
                        max_length=50,
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_common",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as commonly used for quick access",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="patients.medicaldatacategory",
                    ),
                ),
            ],
            options={
                "verbose_name": "Medical Data Item",
                "verbose_name_plural": "Medical Data Items",
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="PatientMedicalData",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Patient-specific notes", null=True
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("mild", "Mild"),
                            ("moderate", "Moderate"),
                            ("severe", "Severe"),
                            ("critical", "Critical"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "start_date",
                    models.DateField(
                        blank=True,
                        help_text="When this condition/medication started",
                        null=True,
                    ),
                ),
                (
                    "end_date",
                    models.DateField(
                        blank=True,
                        help_text="When this condition/medication ended",
                        null=True,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Is this currently active for the patient",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Has this been verified by medical staff",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "added_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="added_medical_data",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "medical_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="patient_records",
                        to="patients.medicaldataitem",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        limit_choices_to={"user_type": "patient"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_data",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_medical_data",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Medical Data",
                "verbose_name_plural": "Patient Medical Data",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="medicaldataitem",
            index=models.Index(
                fields=["category", "is_active"], name="patients_me_categor_65adf9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="medicaldataitem",
            index=models.Index(
                fields=["is_common", "is_active"], name="patients_me_is_comm_ac26db_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="medicaldataitem",
            unique_together={("category", "name")},
        ),
        migrations.AddIndex(
            model_name="patientmedicaldata",
            index=models.Index(
                fields=["patient", "is_active"], name="patients_pa_patient_3b7b96_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="patientmedicaldata",
            index=models.Index(
                fields=["medical_item", "is_active"],
                name="patients_pa_medical_45755a_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="patientmedicaldata",
            unique_together={("patient", "medical_item")},
        ),
    ]
