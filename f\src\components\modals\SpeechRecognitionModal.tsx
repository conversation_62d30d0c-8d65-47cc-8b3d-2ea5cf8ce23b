'use client';
import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  Text,
  Group,
  Button,
  ActionIcon,
  Textarea,
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiMicrophone,
  mdiDeleteSweep,
  mdiClose,
} from '@mdi/js';

// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};

interface SpeechRecognitionModalProps {
  opened: boolean;
  onClose: () => void;
  onValidate: (text: string) => void;
  initialText?: string;
}

const SpeechRecognitionModal = ({ opened, onClose, onValidate, initialText = '' }: SpeechRecognitionModalProps) => {
  // États pour la reconnaissance vocale
  const [isListening, setIsListening] = useState(false);
  const [validSpeech, setValidSpeech] = useState(initialText);
  const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
  const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
  const [microphoneColor, setMicrophoneColor] = useState('#3799CE');

  // Initialiser la reconnaissance vocale au montage du composant
  useEffect(() => {
    initSpeechRecognition();
  }, []);

  // Mettre à jour le texte initial quand il change
  useEffect(() => {
    setValidSpeech(initialText);
  }, [initialText]);

  const initSpeechRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionConstructor = (window as unknown as {
        webkitSpeechRecognition: new () => SpeechRecognitionInstance;
        SpeechRecognition: new () => SpeechRecognitionInstance;
      }).webkitSpeechRecognition || (window as unknown as {
        webkitSpeechRecognition: new () => SpeechRecognitionInstance;
        SpeechRecognition: new () => SpeechRecognitionInstance;
      }).SpeechRecognition;

      const newRecognition = new SpeechRecognitionConstructor();

      newRecognition.continuous = true;
      newRecognition.interimResults = true;
      newRecognition.lang = 'fr-FR';

      newRecognition.onstart = () => {
        setIsListening(true);
        setMicrophoneColor('green');
        setInvalidSpeech('Écoute en cours...');
      };

      newRecognition.onresult = (event: unknown) => {
        const results = (event as { results: { [key: number]: { transcript: string; isFinal: boolean }[] } }).results;
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = 0; i < Object.keys(results).length; i++) {
          const result = results[i];
          const transcript = result[0].transcript;

          if (result[0].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setValidSpeech(finalTranscript);
        setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
      };

      newRecognition.onerror = () => {
        setIsListening(false);
        setMicrophoneColor('#3799CE');
        setInvalidSpeech('Erreur de reconnaissance vocale');
      };

      newRecognition.onend = () => {
        setIsListening(false);
        setMicrophoneColor('#3799CE');
        setInvalidSpeech('Parlez maintenant.');
      };

      setRecognition(newRecognition);
    }
  };

  const toggleRecognition = () => {
    if (!recognition) {
      initSpeechRecognition();
      return;
    }

    if (isListening) {
      recognition.stop();
    } else {
      recognition.start();
    }
  };

  const emptyContent = () => {
    setValidSpeech('');
    setInvalidSpeech('Parlez maintenant.');
    setMicrophoneColor('#3799CE');
    if (recognition && isListening) {
      recognition.stop();
    }
  };

  const handleValidate = () => {
    onValidate(validSpeech);
    onClose();
  };

  const handleCancel = () => {
    if (recognition && isListening) {
      recognition.stop();
    }
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleCancel}
      title={
        <Group>
          <Icon path={mdiMicrophone} size={1} />
          <Text fw={500}>Reconnaissance vocale</Text>
          <ActionIcon variant="subtle" color="red" onClick={handleCancel}>
            <Icon path={mdiClose} size={0.8} />
          </ActionIcon>
        </Group>
      }
      size="md"
      radius={0}
      transitionProps={{ transition: 'fade', duration: 200 }}
      centered
      withCloseButton={false}
      yOffset="30vh"
    >
      <div style={{ padding: '20px' }}>
        <Stack>
          {/* Zone de texte avec reconnaissance vocale */}
          <div
            style={{
              backgroundColor: '#fafafa',
              height: '150px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              padding: '12px',
              position: 'relative',
              overflow: 'auto'
            }}
          >
            <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
              {validSpeech}
              <span style={{ color: '#888', fontStyle: 'italic' }}>
                {invalidSpeech}
              </span>
            </Text>
          </div>

          {/* Boutons de contrôle */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <ActionIcon
              variant="subtle"
              color={isListening ? 'orange' : 'blue'}
              size="lg"
              onClick={toggleRecognition}
              style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
            >
              <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
            </ActionIcon>

            <ActionIcon
              variant="subtle"
              color="red"
              size="lg"
              onClick={emptyContent}
            >
              <Icon path={mdiDeleteSweep} size={1} />
            </ActionIcon>
          </div>

          {/* Boutons d'action */}
          <Group justify="flex-end" mt="md">
            <Button onClick={handleValidate}>
              Valider
            </Button>
            <Button variant="outline" color="red" onClick={handleCancel}>
              Annuler
            </Button>
          </Group>
        </Stack>
      </div>
    </Modal>
  );
};

export default SpeechRecognitionModal;
