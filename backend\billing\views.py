"""
Views for the billing app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Sum
from django.utils import timezone
from .models import Invoice, InvoiceItem, Payment, Subscription, License
from .serializers import (
    InvoiceSerializer,
    InvoiceItemSerializer,
    PaymentSerializer,
    SubscriptionSerializer,
    LicenseSerializer,
    PaymentConfigurationSerializer
)


class InvoiceViewSet(viewsets.ModelViewSet):
    """ViewSet for Invoice model."""
    
    queryset = Invoice.objects.all()  # type: ignore
    serializer_class = InvoiceSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Type ignore for basedpyright not recognizing objects attribute
        queryset = Invoice.objects.select_related('patient', 'appointment').all()  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Search
        search = self.request.query_params.get('search', None)
        if search:
            # Type ignore for basedpyright operator issue with Q objects
            q_objects = (  # type: ignore
                Q(invoice_number__icontains=search) |
                Q(patient__first_name__icontains=search) |
                Q(patient__last_name__icontains=search)
            )
            queryset = queryset.filter(q_objects)
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue invoices."""
        today = timezone.now().date()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['sent', 'overdue'],
            balance_due__gt=0
        )
        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get billing summary."""
        queryset = self.get_queryset()
        
        summary = {
            'total_invoices': queryset.count(),
            'total_amount': queryset.aggregate(Sum('total_amount'))['total_amount__sum'] or 0,
            'total_paid': queryset.aggregate(Sum('paid_amount'))['paid_amount__sum'] or 0,
            'total_outstanding': queryset.aggregate(Sum('balance_due'))['balance_due__sum'] or 0,
            'overdue_count': queryset.filter(
                due_date__lt=timezone.now().date(),
                status__in=['sent', 'overdue'],
                balance_due__gt=0
            ).count()
        }
        
        return Response(summary)


class PaymentViewSet(viewsets.ModelViewSet):
    """ViewSet for Payment model."""
    
    queryset = Payment.objects.all()  # type: ignore
    serializer_class = PaymentSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Type ignore for basedpyright not recognizing objects attribute
        queryset = Payment.objects.select_related('invoice', 'patient').all()  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by payment method
        payment_method = self.request.query_params.get('payment_method', None)
        if payment_method:
            queryset = queryset.filter(payment_method=payment_method)
        
        return queryset.order_by('-payment_date')


class SubscriptionViewSet(viewsets.ModelViewSet):
    """ViewSet for Subscription model."""
    
    queryset = Subscription.objects.all()  # type: ignore
    serializer_class = SubscriptionSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Type ignore for basedpyright not recognizing objects attribute
        queryset = Subscription.objects.select_related('patient').all()  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by plan
        plan = self.request.query_params.get('plan', None)
        if plan:
            queryset = queryset.filter(plan=plan)
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active subscriptions."""
        active_subscriptions = self.get_queryset().filter(status='active')
        serializer = self.get_serializer(active_subscriptions, many=True)
        return Response(serializer.data)


class LicenseViewSet(viewsets.ModelViewSet):
    """ViewSet for License model."""
    
    queryset = License.objects.all()  # type: ignore
    serializer_class = LicenseSerializer
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        # Type ignore for basedpyright not recognizing objects attribute
        queryset = License.objects.select_related('patient').all()  # type: ignore
        
        # Filter by patient
        patient_id = self.request.query_params.get('patient', None)
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by license type
        license_type = self.request.query_params.get('license_type', None)
        if license_type:
            queryset = queryset.filter(license_type=license_type)
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """Get licenses expiring within 30 days."""
        from datetime import timedelta
        thirty_days_from_now = timezone.now().date() + timedelta(days=30)
        
        expiring_licenses = self.get_queryset().filter(
            expiry_date__lte=thirty_days_from_now,
            status='active'
        )
        serializer = self.get_serializer(expiring_licenses, many=True)
        return Response(serializer.data)


class PaymentConfigurationViewSet(viewsets.ViewSet):
    """ViewSet for payment configuration."""
    
    permission_classes = [permissions.AllowAny]  # Simplified for development
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active payment configuration."""
        serializer = PaymentConfigurationSerializer()
        return Response(serializer.to_representation(None))
