from django.core.management.base import BaseCommand
from patients.models import MedicalDataCategory, MedicalDataItem


class Command(BaseCommand):
    help = 'Populate sample medical data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Populating medical data...')

        # Create categories
        categories_data = [
            ('allergy', 'Common Allergies', 'Common allergic reactions and substances'),
            ('medication', 'Common Medications', 'Frequently prescribed medications'),
            ('condition', 'Medical Conditions', 'Common medical conditions and diseases'),
            ('treatment', 'Medical Treatments', 'Common medical treatments and procedures'),
            ('pathology', 'Pathologies', 'Medical pathologies and disorders'),
            ('symptom', 'Symptoms', 'Common medical symptoms'),
        ]

        categories = {}
        for cat_type, name, description in categories_data:
            # Type ignore for basedpyright not recognizing objects attribute
            category, created = MedicalDataCategory.objects.get_or_create(  # type: ignore
                category_type=cat_type,
                name=name,
                defaults={'description': description}
            )
            categories[cat_type] = category
            if created:
                self.stdout.write(f'Created category: {name}')

        # Create medical items
        medical_items_data = {
            'allergy': [
                ('Penicillin', 'Antibiotic allergy', 'severe', True),
                ('Peanuts', 'Food allergy to peanuts', 'severe', True),
                ('Shellfish', 'Seafood allergy', 'moderate', True),
                ('Latex', 'Latex rubber allergy', 'moderate', True),
                ('Dust Mites', 'Environmental allergy', 'mild', True),
                ('Pollen', 'Seasonal allergy', 'mild', True),
                ('Cats', 'Pet dander allergy', 'mild', True),
                ('Aspirin', 'NSAID allergy', 'moderate', True),
                ('Iodine', 'Contrast dye allergy', 'severe', False),
                ('Sulfa', 'Sulfonamide allergy', 'moderate', False),
            ],
            'medication': [
                ('Aspirin', 'Pain reliever and blood thinner', 'mild', True),
                ('Ibuprofen', 'NSAID pain reliever', 'mild', True),
                ('Acetaminophen', 'Pain reliever and fever reducer', 'mild', True),
                ('Lisinopril', 'ACE inhibitor for blood pressure', 'moderate', True),
                ('Metformin', 'Diabetes medication', 'moderate', True),
                ('Atorvastatin', 'Cholesterol medication', 'moderate', True),
                ('Omeprazole', 'Proton pump inhibitor', 'mild', True),
                ('Levothyroxine', 'Thyroid hormone replacement', 'moderate', True),
                ('Warfarin', 'Blood thinner', 'severe', False),
                ('Insulin', 'Diabetes hormone therapy', 'severe', True),
            ],
            'condition': [
                ('Hypertension', 'High blood pressure', 'moderate', True),
                ('Diabetes Type 2', 'Adult-onset diabetes', 'severe', True),
                ('Asthma', 'Respiratory condition', 'moderate', True),
                ('Arthritis', 'Joint inflammation', 'moderate', True),
                ('Depression', 'Mental health condition', 'moderate', True),
                ('Anxiety', 'Mental health condition', 'moderate', True),
                ('COPD', 'Chronic obstructive pulmonary disease', 'severe', False),
                ('Heart Disease', 'Cardiovascular condition', 'severe', True),
                ('Osteoporosis', 'Bone density condition', 'moderate', False),
                ('Migraine', 'Chronic headache condition', 'moderate', True),
            ],
            'treatment': [
                ('Physical Therapy', 'Rehabilitation therapy', 'mild', True),
                ('Chemotherapy', 'Cancer treatment', 'severe', False),
                ('Radiation Therapy', 'Cancer treatment', 'severe', False),
                ('Surgery', 'Surgical intervention', 'severe', True),
                ('Dialysis', 'Kidney treatment', 'severe', False),
                ('Blood Transfusion', 'Blood replacement therapy', 'moderate', False),
                ('Oxygen Therapy', 'Respiratory support', 'moderate', False),
                ('Immunotherapy', 'Immune system treatment', 'moderate', False),
            ],
            'pathology': [
                ('Hypertrophy', 'Tissue enlargement', 'moderate', False),
                ('Atrophy', 'Tissue wasting', 'moderate', False),
                ('Inflammation', 'Tissue inflammation', 'mild', True),
                ('Necrosis', 'Tissue death', 'severe', False),
                ('Fibrosis', 'Tissue scarring', 'moderate', False),
                ('Edema', 'Fluid retention', 'mild', True),
            ],
            'symptom': [
                ('Fever', 'Elevated body temperature', 'mild', True),
                ('Cough', 'Respiratory symptom', 'mild', True),
                ('Shortness of Breath', 'Breathing difficulty', 'moderate', True),
                ('Chest Pain', 'Thoracic discomfort', 'moderate', True),
                ('Nausea', 'Stomach discomfort', 'mild', True),
                ('Dizziness', 'Balance or orientation issues', 'mild', True),
                ('Fatigue', 'Extreme tiredness', 'mild', True),
                ('Headache', 'Head pain', 'mild', True),
            ],
        }

        total_created = 0
        for category_type, items in medical_items_data.items():
            category = categories[category_type]
            for name, description, severity, is_common in items:
                # Type ignore for basedpyright not recognizing objects attribute
                item, created = MedicalDataItem.objects.get_or_create(  # type: ignore
                    category=category,
                    name=name,
                    defaults={
                        'description': description,
                        'severity_level': severity,
                        'is_common': is_common,
                    }
                )
                if created:
                    total_created += 1

        # Type ignore for basedpyright not recognizing SUCCESS attribute
        self.stdout.write(  # type: ignore
            self.style.SUCCESS(  # type: ignore
                f'Successfully populated medical data! Created {total_created} new items.'
            )
        )