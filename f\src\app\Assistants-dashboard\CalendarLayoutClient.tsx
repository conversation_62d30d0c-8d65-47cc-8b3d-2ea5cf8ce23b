"use client";
import { useEffect } from "react";
import { useState } from 'react';
import SimpleBar from "simplebar-react";
import { LanguageProvider } from "~/contexts/LanguageContext";
import { FontSizeProvider, useFontSize } from "~/contexts/FontSizeContext";
import classes from '~/styles/layout.module.css';
import Navbar from '~/layout/navbar/Navbar';
import { SideNavbar } from "~/layout/sideNavbar/SideNavbar"
import Header from '~/layout/header/Header';
import Footer from '~/layout/footer/Footer';
import NavBarButton from '~/layout/navBarButton/NavBarButton';
const GlobalFontSize: React.FC = () => {
  const { fontSize } = useFontSize();
    useEffect(() => {
      document.documentElement.style.fontSize = `${fontSize}px`;
    }, [fontSize]);
  
    return null;
  };
export default function CalendarLayoutClient({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const toggleSidebar = () => setSidebarVisible(!sidebarVisible);
  const toggle = () => setSidebarVisible(!sidebarVisible);

  return (
    <SimpleBar className="simplebar-scrollable-y h-[calc(100vh)] lg:h-[calc(100vh)]"> 
       <FontSizeProvider>
             <LanguageProvider>
       <GlobalFontSize />
      <Header toggleSidebar={toggleSidebar} />
      <div className={classes.pageContainer}>
        <nav className={`${classes.navbar} ${classes.navbarDesktop} ${sidebarVisible ? classes.navbarHidden : ''}`}>
          <Navbar toggleSidebar={toggle} />
        </nav>
        
        <div className={`${classes.sidebarContainer} ${sidebarVisible ? classes.sidebarVisible : classes.sidebarHidden}`}>
          <SideNavbar toggleSidebar={toggleSidebar} />
        </div>

        <main className={`${classes.mainContent} ${sidebarVisible ? classes.mainContentWithSidebar : ''}`}>
        <div className="flex w-[100%]  h-40  ">
        <div className={`${classes.tabsButton} flex-1  top-0 left-0 mt-2 relative`}>{children}</div>
      <NavBarButton/>
    </div>
          
        </main>
      </div>
      <Footer sidebarVisible={sidebarVisible} />
      </LanguageProvider>
      </FontSizeProvider>
      </SimpleBar> 
  );
}