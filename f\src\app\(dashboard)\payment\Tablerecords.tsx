import React, { useState, useMemo } from 'react';
// import { ChevronUp, ChevronDown, Eye, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import Icon from '@mdi/react';
import { mdiViewHeadline,mdiEyeOutline,} from '@mdi/js';
import {Group,Text,Table,Modal} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import SimpleBar from "simplebar-react";
type TablerecordsProps = {
  record: CompteData;
};
interface Transaction {
  id: number;
  date: string;
  type: 'encasement' | 'visit' | 'plan' | 'org';
  title: string;
  total_amount: number | null;
  financial_status: { total: number } | null;
}

interface SortConfig {
  key: keyof Transaction;
  direction: 'asc' | 'desc';
}

const Tablerecords = ({ record }: TablerecordsProps) => {
  // Données de test basées sur l'HTML original
  const [data] = useState<Transaction[]>([
    {
      id: 1,
      date: '15/04/2025',
      type: 'encasement',
      title: 'Encaissement du 15/04/2025 | Reliquat: 0',
      total_amount: 3900.00,
      financial_status: null
    },
    {
      id: 2,
      date: '15/04/2025',
      type: 'visit',
      title: 'Visite du  15/04/2025',
      total_amount: null,
      financial_status: { total: 6900.00 }
    },
    {
      id: 3,
      date: '25/04/2025',
      type: 'encasement',
      title: 'Encaissement du 25/04/2025 | Reliquat: 0',
      total_amount: 350.00,
      financial_status: null
    },
    {
      id: 4,
      date: '25/04/2025',
      type: 'visit',
      title: 'Visite du  25/04/2025',
      total_amount: null,
      financial_status: { total: 350.00 }
    }
  ]);

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'date',
    direction: 'asc'
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Calculs des totaux
  const totals = useMemo(() => {
    const debit = data
      .filter(item => item.type === 'visit' || item.type === 'plan')
      .reduce((sum, item) => sum + (item.financial_status?.total || 0), 0);
    
    const credit = data
      .filter(item => item.type === 'encasement' || item.type === 'org')
      .reduce((sum, item) => sum + (item.total_amount || 0), 0);
    
    return {
      debit,
      credit,
      balance: credit - debit
    };
  }, [data]);

  // Tri des données
  const sortedData = useMemo(() => {
    const sortableItems = [...data];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        // Conversion des dates pour le tri
        if (sortConfig.key === 'date') {
          const aDate = new Date(a.date.split('/').reverse().join('-'));
          const bDate = new Date(b.date.split('/').reverse().join('-'));
          
          if (aDate < bDate) {
            return sortConfig.direction === 'asc' ? -1 : 1;
          }
          if (aDate > bDate) {
            return sortConfig.direction === 'asc' ? 1 : -1;
          }
          return 0;
        }
        
        // Pour les autres champs
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [data, sortConfig]);

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  // Fonction de tri
  const requestSort = (key: keyof Transaction) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Formatage des montants
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Gestion du détail (placeholder)
  // const showDetail = (item: Transaction) => {
  //   alert(`Détail pour: ${item.title}`);
  // };

  const SortIcon: React.FC<{ column: keyof Transaction; sortConfig: SortConfig }> = ({ column, sortConfig }) => {
    if (sortConfig.key !== column) return null;
    return sortConfig.direction === 'asc' ? 
    <svg  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" stroke-linejoin="round" className="lucide lucide-chevron-up-icon lucide-chevron-up w-4 h-4 ml-1"><path d="m18 15-6-6-6 6"/></svg>
      : 
      <svg  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" stroke-linejoin="round" className="lucide lucide-chevron-down-icon lucide-chevron-down w-4 h-4 ml-1"><path d="m6 9 6 6 6-6"/></svg>
      
  };
 const [opened, { open, close }] = useDisclosure(false);
      const elements = [
{ Code: 'TBM', Actes: 'Traitement ortho brackets métalliques', Dent: 'Plusieurs dents', Honoraire: '25 000,00' ,Remise: "0,00", Total: 25000.00, percentages: '50.00', Commentaire: 'Carbon'},
{ Code: 'SGL', Actes: 'Soulagement endodontique', Dent: '47', Honoraire: '1 000,00' ,Remise: "0,00", Total: 1000.00, percentages: '100.00', Commentaire: 'Carbon'},
{ Code: 'SGL', Actes: 'Soulagement endodontique', Dent: '47', Honoraire: '1 000,00' ,Remise: "0,00", Total: 1000.00, percentages: '100.00', Commentaire: 'Carbon'},
{ Code: 'CIB', Actes: 'Camera Intrabuccale', Dent: 'Plusieurs dents', Honoraire: '300,00' ,Remise: "0,00", Total: 3000.00, percentages: '50.00', Commentaire: 'Carbon'},
     
];
 const rows = elements.map((element) => (
    <Table.Tr key={element.Code}>
        <Table.Td>{element.Code}</Table.Td>
      <Table.Td>{element.Actes}</Table.Td>
      <Table.Td>{element.Dent}</Table.Td>
      <Table.Td>{element.Honoraire}</Table.Td>
      <Table.Td>{element.Remise}</Table.Td>
      <Table.Td>{element.Total}</Table.Td>
      <Table.Td>{element.percentages}</Table.Td>
      <Table.Td>{element.Commentaire}</Table.Td>
    </Table.Tr>
  ));
  return (
    <>
    <div className="w-full bg-white">
      {/* Table Container */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <Table className="w-full" striped highlightOnHover withTableBorder withColumnBorders>
          <Table.Thead className="bg-gray-50 border-b border-gray-200">
            <Table.Tr>
              <Table.Th 
                className="px-4 py-3 text-left "
                onClick={() => requestSort('date')}
              >
                <Group gap="xs" align="center" className="ml-auto"justify="space-between">
                    <Text size="md" fw={600}  ml='md'>
                      Date 
                    </Text>
                    <Text size="sm" fw={600} c={"#3799CE"}>
                        <SortIcon column="date" sortConfig={sortConfig} />
                    </Text>
                  </Group>
                
              </Table.Th>
              <Table.Th className="px-4 py-3 ">
               <Text size="md" ta="left" fw={600}  ml='md'>  Titre </Text> 
              </Table.Th>
              <Table.Th className="px-4 py-3 ">
               <Text size="md" fw={600} ta="left" ml='md'>  Débit </Text>  
              </Table.Th>
              <Table.Th className="px-4 py-3 ">
               <Text size="md" fw={600} ta="left" ml='md'>  Crédit </Text>   
              </Table.Th>
              <Table.Th className="px-4 py-3 text-left w-16">
                {/* Actions */}
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((item, index) => (
              <Table.Tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <Table.Td className="px-4 py-3 text-sm text-gray-900">
                  {item.date}
                </Table.Td>
                <Table.Td className="px-4 py-3 text-sm text-gray-900">
                  {item.title}
                </Table.Td>
                <Table.Td className="px-4 py-3 text-sm text-right">
                  {(item.type === 'visit' || item.type === 'plan') && item.financial_status ? (
                    <span className="text-red-600 font-medium">
                      {formatCurrency(item.financial_status.total)}
                    </span>
                  ) : null}
                </Table.Td>
                <Table.Td className="px-4 py-3 text-sm text-right">
                  {(item.type === 'encasement' || item.type === 'org') && item.total_amount ? (
                    <span className="text-green-600 font-medium">
                      {formatCurrency(item.total_amount)}
                    </span>
                  ) : null}
                </Table.Td>
                <Table.Td className="px-4 py-3 text-center">
                  {item.type !== 'org' && (
                    <button
                      // onClick={() => showDetail(item)}
                      onClick={open}
                      className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
                    >
                      
                      {/* <Eye className="w-4 h-4" /> */}
                      <Icon path={mdiEyeOutline} size={1} className="w-4 h-4"/>
                    </button>
                  )}
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
          <tfoot className=" border-t-3  border-t-[#3799CE]">{/**bg-gray-100 */}
            <Table.Tr className="font-semibold text-sm">
              <Table.Td  colSpan={2}>
                 <Text size="md" fw={700} className="text-gray-700">
                               Total :
                            </Text>
               
              </Table.Td>
              <Table.Td className="px-4 py-3 text-right">
                <span className="text-red-600 font-bold">
                  {formatCurrency(totals.debit)}
                </span>
              </Table.Td>
              <Table.Td className="px-4 py-3 text-right">
                <span className="text-green-600 font-bold">
                  {formatCurrency(totals.credit)}
                </span>
              </Table.Td>
              <Table.Td className="px-4 py-3"></Table.Td>
            </Table.Tr>
            <Table.Tr className="font-bold text-base">
              <Table.Td  colSpan={3}>{/*/colSpan="3"/*/}
                 <Text size="md" fw={700} className="text-gray-700">
              Balance :
            </Text>
              </Table.Td>
              <Table.Td className="px-4 py-3 text-right">
                <span className={`font-bold ${totals.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(Math.abs(totals.balance))}
                </span>
              </Table.Td>
              <Table.Td className="px-4 py-3"></Table.Td>
            </Table.Tr>
          </tfoot>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div className="flex items-center space-x-4">
          {/* Page selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">Page</span>
            <select
              value={currentPage}
              onChange={(e) => setCurrentPage(Number(e.target.value))}
              className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {Array.from({ length: totalPages }, (_, i) => (
                <option key={i + 1} value={i + 1}>
                  {i + 1}
                </option>
              ))}
            </select>
          </div>

          {/* Items per page selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">Lignes par Page</span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">
            {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, sortedData.length)} de {sortedData.length}
          </span>

          {/* Navigation buttons */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 lucide lucide-chevrons-left-icon lucide-chevrons-left"><path d="m11 17-5-5 5-5"/><path d="m18 17-5-5 5-5"/></svg>
              {/* <ChevronsLeft className="w-4 h-4" /> */}
            </button>
            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>
              
            </button>
            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 lucide lucide-chevron-right-icon lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
              {/* <ChevronRight className="w-4 h-4" /> */}
            </button>
            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 lucide lucide-chevrons-right-icon lucide-chevrons-right"><path d="m6 17 5-5-5-5"/><path d="m13 17 5-5-5-5"/></svg>
              {/* <ChevronsRight className="w-4 h-4" /> */}
            </button>
          </div>
        </div>
      </div>
    </div>

      <Modal.Root
            opened={opened}
            onClose={close}
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            centered
            size="xl"
        >
               <Modal.Content className="overflow-y-hidden">
               <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                <Modal.Title>
                    <Group>
                    <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                        <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                            Plan de traitement du 26/04/2025</Text>
                    </Group>
                </Modal.Title>
                    <Group justify="flex-end">
                            <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }}   />
                            </Group>
                </Modal.Header>
                 <Modal.Body style={{ padding: '0px' }}>
                        <div className={rows.length <=3 ?"py-2 pl-4 w-auto overflow-hidden" : "py-2 pl-4 w-[780px]  overflow-hidden"}>   
                <SimpleBar className="simplebar-scrollable-x w-[calc(100%)]">
                <div className="pr-4">
                    <Table striped highlightOnHover withTableBorder withColumnBorders>
                    <Table.Thead>
                <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Actes</Table.Th>
                    <Table.Th>Dent</Table.Th>
                    <Table.Th>Honoraire</Table.Th>
                    <Table.Th>Remise</Table.Th>
                    <Table.Th>Total</Table.Th>
                    <Table.Th>%</Table.Th>
                    <Table.Th>Commentaire</Table.Th>
                 
                </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
                {rows.length === 0 && (
                <Table.Caption>Aucun élément trouvé.</Table.Caption>
                )}
                            </Table>
                        </div>
                        </SimpleBar>
                    </div>
                    </Modal.Body>
               </Modal.Content>
        </Modal.Root>
        </>
  );
};



export default Tablerecords;