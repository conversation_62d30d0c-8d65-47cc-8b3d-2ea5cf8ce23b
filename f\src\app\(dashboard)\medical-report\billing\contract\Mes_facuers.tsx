
import { useState } from 'react';
import MesFacturesList from './Mes_facuers_list';
import {MesFacture} from './MesFactures';
 // or any button component you're using

export const MesFactures = () => {
  const [showFirstList, setShowFirstList] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageLimit, setPageLimit] = useState(10);
  const [totalInvoices, setTotalInvoices] = useState(0);

  const toggleList = () => {
    setShowFirstList((prev) => !prev);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleLimitChange = (limit: number) => {
    setPageLimit(limit);
  };

  return (
    <>
      {/* <Button onClick={toggleList}>
        {showFirstList ? 'Afficher la 2ᵉ liste' : 'Afficher la 1ʳᵉ liste'}
      </Button> */}

      {showFirstList ? (
        <MesFacturesList toggleList={toggleList}/>
      ) : (
        <MesFacture
          invoiceNumber={currentPage}
          total={totalInvoices}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
        />
      )}
    </>
  );
};

export default MesFactures;

