'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  
  Textarea,
  Table,
  
  Modal,
  FileInput,
 
  Tabs,
  Radio,
  Pagination,
 
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';

import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconList,
  IconFile,
  
  IconUpload,
  
  IconTrash,
  
  IconFileText,
  IconPaperclip,
  IconMessageCircle,

  IconShoppingCart,
 
  IconCheck,
  IconDeviceFloppy,
 
  IconRotateClockwise,
} from '@tabler/icons-react';

interface BonRetourConsignationItem {
  id: string;
  numeroCN: string;
  code: string;
  designation: string;
  quantite: number;
  prix: number;
  tva: number;
  depot: string;
  montant: number;
}

interface BonRetourConsignation {
  id: string;
  numero: string;
  date: Date | null;
  depot: string;
  affaire: string;
  choisirPatient: string;
  organisme: string;
  modePrix: 'HT' | 'TTC';
  items: BonRetourConsignationItem[];
  commentaire: string;
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
  status: 'draft' | 'validated' | 'processed';
  attachments: string[];
}

export default function BonRetourConsignationPage() {
  
  const [currentBonRetourConsignation, setCurrentBonRetourConsignation] = useState<BonRetourConsignation>({
    id: '1',
    numero: '1',
    date: new Date('2022-09-16'),
    depot: 'Dépôt 1',
    affaire: '',
    choisirPatient: '',
    organisme: '',
    modePrix: 'HT',
    items: [],
    commentaire: '',
    montantHT: 0,
    montantTVA: 0,
    montantTTC: 0,
    status: 'draft',
    attachments: [],
  });


  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [opened, { open, close }] = useDisclosure(false);
  const [viewMode, setViewMode] = useState<'form' | 'list'>('form');

  const form = useForm({
    initialValues: {
      numero: currentBonRetourConsignation.numero,
      date: currentBonRetourConsignation.date,
      depot: currentBonRetourConsignation.depot,
      affaire: currentBonRetourConsignation.affaire,
      choisirPatient: currentBonRetourConsignation.choisirPatient,
      organisme: currentBonRetourConsignation.organisme,
      modePrix: currentBonRetourConsignation.modePrix,
      commentaire: currentBonRetourConsignation.commentaire,
    },
  });

  const itemForm = useForm({
    initialValues: {
      numeroCN: '',
      code: '',
      designation: '',
      quantite: 1,
      prix: 0,
      tva: 20,
      depot: '',
    },
  });

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
    'Dépôt Principal',
    'Dépôt Secondaire',
  ];

  const affaires = [
    'Affaire A',
    'Affaire B',
    'Affaire C',
    'Affaire D',
  ];

  const organismes = [
    'Organisme A',
    'Organisme B',
    'Organisme C',
    'Organisme D',
  ];

  const calculateMontant = (item: Partial<BonRetourConsignationItem>) => {
    const { quantite = 0, prix = 0 } = item;
    return quantite * prix;
  };

  const calculateTotals = () => {
    const montantHT = currentBonRetourConsignation.items.reduce((sum, item) => sum + item.montant, 0);
    const montantTVA = currentBonRetourConsignation.items.reduce((sum, item) => {
      const tvaAmount = item.montant * (item.tva / 100);
      return sum + tvaAmount;
    }, 0);
    const montantTTC = montantHT + montantTVA;

    setCurrentBonRetourConsignation(prev => ({
      ...prev,
      montantHT,
      montantTVA,
      montantTTC,
    }));
  };

  const addItem = (values: typeof itemForm.values) => {
    const newItem: BonRetourConsignationItem = {
      id: Date.now().toString(),
      numeroCN: values.numeroCN,
      code: values.code,
      designation: values.designation,
      quantite: values.quantite,
      prix: values.prix,
      tva: values.tva,
      depot: values.depot,
      montant: calculateMontant(values),
    };

    setCurrentBonRetourConsignation(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));

    itemForm.reset();
    close();
  };

  const removeItem = (id: string) => {
    setCurrentBonRetourConsignation(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  // const handleSave = (action: 'save' | 'validate' | 'process') => {
  //   const updatedBonRetourConsignation = {
  //     ...currentBonRetourConsignation,
  //     ...form.values,
  //     status: action === 'save' ? 'draft' : action === 'validate' ? 'validated' : 'processed',
  //   };

  //   setCurrentBonRetourConsignation(updatedBonRetourConsignation);
    
  //   const actionText = action === 'save' ? 'enregistré' : action === 'validate' ? 'validé' : 'traité';
  //   notifications.show({
  //     title: 'Succès',
  //     message: `Bon de retour consignation ${actionText} avec succès`,
  //     color: 'green',
  //   });
  // };

  React.useEffect(() => {
    calculateTotals();
  }, [currentBonRetourConsignation.items]);

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconRotateClockwise size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Bon de retour consignation N°: {currentBonRetourConsignation.numero}
          </Title>
        </Group>
        <Group>
          <Button
            variant={viewMode === 'form' ? 'filled' : 'outline'}
            leftSection={<IconFile size={16} />}
            onClick={() => setViewMode('form')}
          >
            Nouveau
          </Button>
          <Button
            variant={viewMode === 'list' ? 'filled' : 'outline'}
            leftSection={<IconList size={16} />}
            onClick={() => setViewMode('list')}
          >
            Retours de consignation
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Retour consignation"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Text size="sm" fw={500} mb="xs">Mode de prix</Text>
              <Radio.Group
                value={form.values.modePrix}
                onChange={(value) => form.setFieldValue('modePrix', value as 'HT' | 'TTC')}
              >
                <Group>
                  <Radio value="HT" label="HT" />
                  <Radio value="TTC" label="TTC" />
                </Group>
              </Radio.Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Dépôt"
                placeholder="Sélectionner un dépôt"
                data={depots}
                {...form.getInputProps('depot')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Affaire"
                placeholder="Sélectionner une affaire"
                data={affaires}
                {...form.getInputProps('affaire')}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="Choisir un patient"
                placeholder="Rechercher un patient"
                rightSection={<IconSearch size={16} />}
                {...form.getInputProps('choisirPatient')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Organisme"
                placeholder="Sélectionner un organisme"
                data={organismes}
                {...form.getInputProps('organisme')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs defaultValue="details">
            <Tabs.List>
              <Tabs.Tab value="details" leftSection={<IconFileText size={16} />}>
                Détails
              </Tabs.Tab>
              <Tabs.Tab value="pieces" leftSection={<IconPaperclip size={16} />}>
                Pièces jointes
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="details" pt="md">
              <Group justify="space-between" mb="md">
                <Group>
                  <Button leftSection={<IconShoppingCart size={16} />} color="blue">
                    Bons de consignation
                  </Button>
                  <Button leftSection={<IconMessageCircle size={16} />} color="blue">
                    Commentaire
                  </Button>
                </Group>
              </Group>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>N° CN</Table.Th>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix</Table.Th>
                    <Table.Th>Tva</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Montant</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {currentBonRetourConsignation.items.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={9} className="text-center text-gray-500">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    currentBonRetourConsignation.items.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.numeroCN}</Table.Td>
                        <Table.Td>{item.code}</Table.Td>
                        <Table.Td>{item.designation}</Table.Td>
                        <Table.Td>{item.quantite}</Table.Td>
                        <Table.Td>{item.prix.toFixed(2)} €</Table.Td>
                        <Table.Td>{item.tva}%</Table.Td>
                        <Table.Td>{item.depot}</Table.Td>
                        <Table.Td>{item.montant.toFixed(2)} €</Table.Td>
                        <Table.Td>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeItem(item.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>

              <Group justify="space-between" mt="md">
                <Group>
                  <Text size="sm">Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['1', '2', '3']}
                    value={currentPage.toString()}
                    onChange={(value) => setCurrentPage(parseInt(value || '1'))}
                  />
                  <Text size="sm">Lignes par Page</Text>
                  <Select
                    size="sm"
                    w={60}
                    data={['10', '25', '50']}
                    value={itemsPerPage.toString()}
                    onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
                  />
                  <Text size="sm">0 - 0 de 0</Text>
                </Group>
                <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
              </Group>

              <Button
                leftSection={<IconPlus size={16} />}
                onClick={open}
                mt="md"
              >
                Ajouter un article
              </Button>
            </Tabs.Panel>

            <Tabs.Panel value="pieces" pt="md">
              <FileInput
                label="Ajouter des pièces jointes"
                placeholder="Sélectionner des fichiers"
                leftSection={<IconUpload size={16} />}
                multiple
              />
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaire"
                placeholder="Ajouter un commentaire..."
                rows={4}
                {...form.getInputProps('commentaire')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Totals */}
          <Grid>
            <Grid.Col span={8}>
              <Textarea
                label="Commentaire"
                placeholder="Commentaire général..."
                rows={3}
                {...form.getInputProps('commentaire')}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Stack>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT HT :</Text>
                  <Text>{currentBonRetourConsignation.montantHT.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TVA :</Text>
                  <Text>{currentBonRetourConsignation.montantTVA.toFixed(2)}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>MONTANT TTC :</Text>
                  <Text>{currentBonRetourConsignation.montantTTC.toFixed(2)}</Text>
                </Group>
              </Stack>
            </Grid.Col>
          </Grid>

          {/* Action Buttons */}
          <Group justify="flex-end" mt="xl">
            <Button variant="outline" color="red">
              Annuler
            </Button>
            <Button variant="outline" leftSection={<IconCheck size={16} />}>
              Valider
            </Button>
            <Button color="cyan" leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer et quitter
            </Button>
            <Button leftSection={<IconDeviceFloppy size={16} />}>
              Enregistrer
            </Button>
          </Group>
        </form>
      </Card>

      {/* Add Item Modal */}
      <Modal opened={opened} onClose={close} title="Ajouter un article" size="lg">
        <form onSubmit={itemForm.onSubmit(addItem)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="N° CN"
                  placeholder="Numéro CN"
                  {...itemForm.getInputProps('numeroCN')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder="Code article"
                  {...itemForm.getInputProps('code')}
                  required
                />
              </Grid.Col>
            </Grid>
            <TextInput
              label="Désignation"
              placeholder="Désignation"
              {...itemForm.getInputProps('designation')}
              required
            />
            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Quantité"
                  placeholder="1"
                  {...itemForm.getInputProps('quantite')}
                  min={1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Prix"
                  placeholder="0.00"
                  decimalScale={2}
                  {...itemForm.getInputProps('prix')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="TVA (%)"
                  placeholder="20"
                  {...itemForm.getInputProps('tva')}
                  min={0}
                  max={100}
                />
              </Grid.Col>
            </Grid>
            <Select
              label="Dépôt"
              placeholder="Sélectionner un dépôt"
              data={depots}
              {...itemForm.getInputProps('depot')}
            />
            <Group justify="flex-end">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit">
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
}
