{"name": "fron_end", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@mantine/carousel": "^8.2.4", "@mantine/charts": "^8.2.4", "@mantine/code-highlight": "^8.2.4", "@mantine/core": "^8.2.4", "@mantine/dates": "^8.2.4", "@mantine/dropzone": "^8.2.4", "@mantine/form": "^8.2.4", "@mantine/hooks": "^8.2.4", "@mantine/modals": "^8.2.4", "@mantine/notifications": "^8.2.4", "@mantine/nprogress": "^8.2.4", "@mantine/spotlight": "^8.2.4", "@mantine/tiptap": "^8.2.4", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@tabler/icons-react": "^3.34.1", "@tiptap/extension-link": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "axios": "^1.11.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel": "^8.5.2", "embla-carousel-react": "^8.5.2", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-resources-to-backend": "^1.2.1", "mantine-datatable": "^8.2.0", "moment": "^2.30.1", "next": "15.4.6", "nextjs-toploader": "^3.8.16", "react": "19.1.0", "react-big-calendar": "^1.19.4", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-i18next": "^15.6.1", "react-imask": "^7.6.1", "recharts": "^3.1.2", "simplebar-react": "^3.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}