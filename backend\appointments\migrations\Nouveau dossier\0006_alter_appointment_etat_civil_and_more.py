# Generated by Django 5.1.3 on 2025-08-18 11:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0005_appointment_add_to_waiting_list_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='appointment',
            name='etat_civil',
            field=models.CharField(blank=True, choices=[('Célibataire', 'Célibataire'), ('<PERSON><PERSON>(e)', '<PERSON><PERSON>(e)'), ('<PERSON>vor<PERSON>(e)', '<PERSON>vor<PERSON>(e)'), ('Veuf(ve)', 'Veuf(ve)'), ('<PERSON><PERSON> chose', '<PERSON><PERSON> chose')], help_text='Civil status', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='gender',
            field=models.CharField(blank=True, choices=[('Homme', 'Homme'), ('Femme', 'Femme'), ('Enfant', 'Enfant'), ('<PERSON><PERSON>', '<PERSON><PERSON>')], help_text='Patient gender', max_length=10, null=True),
        ),
    ]
