"""
URL configuration for appointments app.
"""

from django.urls import path, include
from django.http import JsonResponse
from rest_framework.routers import DefaultRouter
from . import views

# Import new views
try:
    from .view_modules.appointment_management_views import AppointmentManagementViewSet
    from .view_modules.waiting_list_views import (
        get_waiting_list, add_to_waiting_list, move_to_calendar,
        remove_from_waiting_list, update_waiting_list_item, activate_appointment
    )
    from .view_modules.pause_management_views import (
        DoctorPauseViewSet, create_pause_from_modal, get_doctor_pauses,
        delete_pause, update_pause
    )
    ENHANCED_VIEWS_AVAILABLE = True
    print("✓ Enhanced appointment views imported successfully")
except ImportError as e:
    print(f"✗ Enhanced views not available: {e}")
    ENHANCED_VIEWS_AVAILABLE = False

router = DefaultRouter()
router.register(r'appointments', views.AppointmentViewSet)
router.register(r'dentistry-appointments', views.DentistryAppointmentViewSet)
router.register(r'reminders', views.AppointmentReminderViewSet)
router.register(r'patient-lists', views.PatientListViewSet)
router.register(r'active-visits', views.ActiveVisitViewSet)
router.register(r'presence-list', views.PresenceListViewSet)
router.register(r'history-journal', views.HistoryJournalViewSet)

# Enhanced appointment management (if available)
if ENHANCED_VIEWS_AVAILABLE:
    router.register(r'enhanced-appointments', AppointmentManagementViewSet, basename='enhanced-appointments')  # type: ignore
    router.register(r'pauses', DoctorPauseViewSet, basename='doctor-pauses')  # type: ignore

# Start with custom URLs first (before router) to avoid conflicts
urlpatterns = []

# Add enhanced URLs only if views are available
if ENHANCED_VIEWS_AVAILABLE:
    enhanced_patterns = [
        # Test URL to verify routing works
        path('test-waiting/', lambda request: JsonResponse({'message': 'URL routing works!'}), name='test-waiting'),
        # Waiting list management - MUST come before router
        path('waiting-list/', get_waiting_list, name='get-waiting-list'),
        path('waiting-list/add/', add_to_waiting_list, name='add-to-waiting-list'),
        path('waiting-list/<str:appointment_id>/move-to-calendar/', move_to_calendar, name='move-to-calendar'),
        path('waiting-list/<str:appointment_id>/remove/', remove_from_waiting_list, name='remove-from-waiting-list'),
        path('waiting-list/<str:appointment_id>/update/', update_waiting_list_item, name='update-waiting-list-item'),
        path('waiting-list/<str:appointment_id>/activate/', activate_appointment, name='activate-appointment'),

        # Pause management - using different paths to avoid conflicts with ViewSet
        path('pause-modal/create/', create_pause_from_modal, name='create-pause-from-modal'),
        path('pause-data/doctor/<str:doctor_id>/', get_doctor_pauses, name='get-doctor-pauses'),
        path('pause-data/<str:pause_id>/delete/', delete_pause, name='delete-pause'),
        path('pause-data/<str:pause_id>/update/', update_pause, name='update-pause'),
    ]
    urlpatterns.extend(enhanced_patterns)
else:
    # Fallback to simple waiting list endpoint if enhanced views not available
    urlpatterns.append(path('waiting-list/', views.simple_waiting_list, name='simple-waiting-list'))

# Add router URLs after custom URLs
urlpatterns.extend([
    path('', include(router.urls)),
    # History journal patient history endpoint
    path('history-journal/patient-history/', views.HistoryJournalViewSet.as_view({'get': 'patient_history'}), name='patient-history'),
])