// components/VisitTableLegend.tsx
import { Box, Group, Text, Pagination, Select, Flex, ThemeIcon } from '@mantine/core';
import { useState } from 'react';

const statusColors: Record<string, string> = {
  paid: '#4caf50', // green
  partial: '#ff9800', // orange
  unpaid: '#f44336', // red
  closed: '#9e9e9e', // grey
};

export const VisitTableLegend = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState('10');

  const limits = ['5', '10', '15', '20'];

  return (
    <Box className="legend-container" mt="md">
           <Group justify="space-between">
      {/* Legend Section */}
      <Group >
        <LegendItem color={statusColors.paid} label="Réglé(e)" />
        <LegendItem color={statusColors.partial} label="Réglé(e) partiellement" />
        <LegendItem color={statusColors.unpaid} label="Non Réglé(e)" />
        <LegendItem color={statusColors.closed} label="Dispensé(e)/Clôturé(e)" />
      </Group>
  <Group justify="flex-end">
      {/* Pagination Section */}
        <Flex align="center" gap="sm">
          <Text size="sm">Page</Text>
          <Pagination value={page} onChange={setPage} total={5} />
        </Flex>
        <Flex align="center" gap="sm">
          <Text size="sm">Lignes par page</Text>
          <Select
            data={limits}
            value={limit}
            onChange={(value) => setLimit(value || '10')}
            placeholder="Sélectionner"
            
            w={100}
          />
        </Flex>
        <Text size="sm">
          {(page - 1) * Number(limit)} - {(page - 1) * Number(limit)} de 0
        </Text>
    
      </Group>
      </Group>
    </Box>
  );
};

type LegendItemProps = {
  color: string;
  label: string;
};

const LegendItem = ({ color, label }: LegendItemProps) => (
  <Group gap="xs" align="center">
    <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: color }} />
    <Text size="sm">{label}</Text>
  </Group>
);
