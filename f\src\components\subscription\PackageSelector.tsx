'use client';

import { useState, useEffect } from 'react';
import { Container, Title, Text, Grid, SegmentedControl, Group, Center, Loader, Alert } from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import PackageCard from './PackageCard';
import subscriptionService, { SubscriptionPackage } from '~/services/subscriptionService';

interface PackageSelectorProps {
  onSelectPackage: (pkg: SubscriptionPackage, billingCycle: 'monthly' | 'annual') => void;
  initialPackageId?: number;
  initialBillingCycle?: 'monthly' | 'annual';
  preSelectedPackage?: SubscriptionPackage;
}

export default function PackageSelector({
  onSelectPackage,
  initialPackageId,
  initialBillingCycle = 'monthly',
  preSelectedPackage
}: PackageSelectorProps) {
  const [packages, setPackages] = useState<SubscriptionPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<SubscriptionPackage | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>(initialBillingCycle);

  useEffect(() => {
    // If we have a pre-selected package, use it immediately
    if (preSelectedPackage) {
      setSelectedPackage(preSelectedPackage);
      onSelectPackage(preSelectedPackage, billingCycle);
    }
  }, [preSelectedPackage, billingCycle, onSelectPackage]);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const data = await subscriptionService.getPackages();
        setPackages(data);

        // If initialPackageId is provided and we don't have a pre-selected package, select that package
        if (initialPackageId && !preSelectedPackage) {
          const initialPackage = data.find(pkg => pkg.id === initialPackageId);
          if (initialPackage) {
            setSelectedPackage(initialPackage);
          }
        }
      } catch (err) {
        console.error('Error fetching packages:', err);
        setError('Failed to load subscription packages. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, [initialPackageId, preSelectedPackage]);

  const handleSelectPackage = (pkg: SubscriptionPackage) => {
    setSelectedPackage(pkg);
    onSelectPackage(pkg, billingCycle);
  };

  const handleBillingCycleChange = (value: string) => {
    const cycle = value as 'monthly' | 'annual';
    setBillingCycle(cycle);
    if (selectedPackage) {
      onSelectPackage(selectedPackage, cycle);
    }
  };

  if (loading) {
    return (
      <Center style={{ height: '200px' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  return (
    <Container size="lg" py="xl" w={"100%"}>
      <Title order={2} ta="center" mt="sm">
        Choose Your Subscription Plan
      </Title>

      <Text c="dimmed" ta="center" mt="md" mb="xl">
        Select the plan that best fits your practice needs
      </Text>

      <Group justify="center" mb="xl">
        <SegmentedControl
          value={billingCycle}
          onChange={handleBillingCycleChange}
          data={[
            { label: 'Monthly Billing', value: 'monthly' },
            { label: 'Annual Billing (Save 15%)', value: 'annual' },
          ]}
          size="sm"
        />
      </Group>

      <Grid>
        {packages.map((pkg) => (
          <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={pkg.id}>
            <PackageCard
              package={pkg}
              selectedBillingCycle={billingCycle}
              onSelect={handleSelectPackage}
              isSelected={selectedPackage?.id === pkg.id}
            />
          </Grid.Col>
        ))}
      </Grid>
    </Container>
  );
}
