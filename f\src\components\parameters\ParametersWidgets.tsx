/**
 * Parameters Dashboard Widgets
 * Displays key system parameters and configuration metrics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconSettings,
  IconUsers,
  IconShield,
  IconDatabase,
  IconServer,
  IconAlertTriangle,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconAlertCircle,
  IconChartPie,
  IconCalendarDollar,
  IconCloudUpload,
  IconCpu,
} from '@tabler/icons-react';
import { useParameters } from '@/hooks/useParameters';

interface ParametersWidgetsProps {
  compact?: boolean;
  showSystemHealth?: boolean;
}

const ParametersWidgets: React.FC<ParametersWidgetsProps> = ({
  compact = false,
  showSystemHealth = true,
}) => {
  const {
    systemParameters,
    systemUsers,
    specialties,
    contacts,
    technicians,
    dataBackups,
    analytics,
    loading,
    error,
    getActiveUsers,
    getActiveSpecialties,
    getRecentBackups,
    getSystemStats,
    getSystemHealth,
  } = useParameters({ 
    autoFetch: true,
    dataTypes: ['parameters', 'users', 'specialties', 'contacts', 'technicians', 'backups', 'analytics']
  });

  const systemStats = getSystemStats();
  const systemHealth = getSystemHealth();
  const activeUsers = getActiveUsers();
  const activeSpecialties = getActiveSpecialties();
  const recentBackups = getRecentBackups(3);

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Total Users Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconUsers size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Utilisateurs</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="blue">
            {systemStats.totalUsers}
          </Text>
          <Text size="xs" c="dimmed">
            {systemStats.activeUsers} actifs
          </Text>
        </Card>

        {/* System Parameters Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="green" size="sm">
                <IconSettings size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Paramètres</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="green">
            {systemStats.totalParameters}
          </Text>
          <Progress value={85} size="xs" mt="xs" />
          <Text size="xs" c="dimmed">
            85% configurés
          </Text>
        </Card>

        {/* Specialties Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconShield size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Spécialités</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="purple">
            {systemStats.totalSpecialties}
          </Text>
          <Text size="xs" c="dimmed">
            {systemStats.activeSpecialties} actives
          </Text>
        </Card>

        {/* System Health Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconServer size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Système</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ 
                value: systemHealth.uptime, 
                color: systemHealth.status === 'healthy' ? 'green' : 
                       systemHealth.status === 'warning' ? 'orange' : 'red' 
              }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {systemHealth.uptime.toFixed(1)}%
                </Text>
              }
            />
          </Group>
        </Card>
      </SimpleGrid>

      {!compact && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={3} spacing="md">
            {/* Recent Users */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconUsers size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Utilisateurs Actifs</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {activeUsers.slice(0, 3).map((user) => (
                  <Group key={user.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{user.first_name} {user.last_name}</Text>
                      <Text size="xs" c="dimmed">
                        {user.profile_name} | {user.specialty_name || 'N/A'}
                      </Text>
                      {user.last_login && (
                        <Text size="xs" c="dimmed">
                          Dernière connexion: {new Date(user.last_login).toLocaleDateString()}
                        </Text>
                      )}
                    </div>
                    <Badge size="xs" color="green">
                      Actif
                    </Badge>
                  </Group>
                ))}
                {activeUsers.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucun utilisateur actif
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Active Specialties */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="indigo" size="sm">
                    <IconShield size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Spécialités Actives</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {activeSpecialties.slice(0, 3).map((specialty) => (
                  <Group key={specialty.id} justify="space-between">
                    <Group gap="xs">
                      <div 
                        style={{ 
                          width: 12, 
                          height: 12, 
                          borderRadius: '50%', 
                          backgroundColor: specialty.color 
                        }} 
                      />
                      <div>
                        <Text size="xs" fw={500}>{specialty.name}</Text>
                        <Text size="xs" c="dimmed">Code: {specialty.code}</Text>
                      </div>
                    </Group>
                    <Badge size="xs" color="indigo">
                      {specialty.configuration.modules_enabled.length} modules
                    </Badge>
                  </Group>
                ))}
                {activeSpecialties.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune spécialité active
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Recent Backups */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="yellow" size="sm">
                    <IconDatabase size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Sauvegardes Récentes</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {recentBackups.map((backup) => (
                  <Group key={backup.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{backup.backup_name}</Text>
                      <Text size="xs" c="dimmed">
                        {new Date(backup.backup_date).toLocaleDateString()}
                      </Text>
                      <Text size="xs" c="dimmed">
                        Taille: {(backup.file_size / 1024 / 1024).toFixed(0)} MB
                      </Text>
                    </div>
                    <Badge 
                      size="xs" 
                      color={
                        backup.status === 'completed' ? 'green' : 
                        backup.status === 'in_progress' ? 'blue' : 'red'
                      }
                    >
                      {backup.status}
                    </Badge>
                  </Group>
                ))}
                {recentBackups.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune sauvegarde récente
                  </Text>
                )}
              </Stack>
            </Card>
          </SimpleGrid>

          {/* System Health Details */}
          {showSystemHealth && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>État du Système</Text>
                <Badge 
                  size="sm" 
                  color={
                    systemHealth.status === 'healthy' ? 'green' : 
                    systemHealth.status === 'warning' ? 'orange' : 'red'
                  }
                >
                  {systemHealth.status === 'healthy' ? 'Sain' : 
                   systemHealth.status === 'warning' ? 'Attention' : 'Critique'}
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Group gap="xs">
                      <IconCpu size={16} />
                      <Text size="xs" c="dimmed">CPU</Text>
                    </Group>
                    <Text size="lg" fw={700} c={systemHealth.cpuUsage > 80 ? 'red' : 'blue'}>
                      {systemHealth.cpuUsage.toFixed(1)}%
                    </Text>
                    <Progress 
                      value={systemHealth.cpuUsage} 
                      size="xs" 
                      color={systemHealth.cpuUsage > 80 ? 'red' : 'blue'}
                      w="100%"
                    />
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Group gap="xs">
                      <IconServer size={16} />
                      <Text size="xs" c="dimmed">Mémoire</Text>
                    </Group>
                    <Text size="lg" fw={700} c={systemHealth.memoryUsage > 80 ? 'red' : 'green'}>
                      {systemHealth.memoryUsage.toFixed(1)}%
                    </Text>
                    <Progress 
                      value={systemHealth.memoryUsage} 
                      size="xs" 
                      color={systemHealth.memoryUsage > 80 ? 'red' : 'green'}
                      w="100%"
                    />
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Group gap="xs">
                      <IconDatabase size={16} />
                      <Text size="xs" c="dimmed">Disque</Text>
                    </Group>
                    <Text size="lg" fw={700} c={systemHealth.diskUsage > 85 ? 'red' : 'purple'}>
                      {systemHealth.diskUsage.toFixed(1)}%
                    </Text>
                    <Progress 
                      value={systemHealth.diskUsage} 
                      size="xs" 
                      color={systemHealth.diskUsage > 85 ? 'red' : 'purple'}
                      w="100%"
                    />
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Group gap="xs">
                      <IconCloudUpload size={16} />
                      <Text size="xs" c="dimmed">Disponibilité</Text>
                    </Group>
                    <Text size="lg" fw={700} c={systemHealth.uptime < 99 ? 'orange' : 'teal'}>
                      {systemHealth.uptime.toFixed(2)}%
                    </Text>
                    <Progress 
                      value={systemHealth.uptime} 
                      size="xs" 
                      color={systemHealth.uptime < 99 ? 'orange' : 'teal'}
                      w="100%"
                    />
                  </Stack>
                </Grid.Col>
              </Grid>
              
              <Divider my="md" />
              
              <SimpleGrid cols={2} spacing="md">
                <Group justify="space-between">
                  <Text size="sm" c="dimmed">Temps de réponse</Text>
                  <Text size="sm" fw={500}>{systemHealth.responseTime}ms</Text>
                </Group>
                <Group justify="space-between">
                  <Text size="sm" c="dimmed">Taux d'erreur</Text>
                  <Text size="sm" fw={500} c={systemHealth.errorRate > 1 ? 'red' : 'green'}>
                    {systemHealth.errorRate.toFixed(2)}%
                  </Text>
                </Group>
              </SimpleGrid>
            </Card>
          )}

          {/* Configuration Changes Alert */}
          {systemStats.configurationChanges > 0 && (
            <Alert icon={<IconAlertTriangle size={16} />} color="blue" variant="light">
              <Group justify="space-between">
                <div>
                  <Text size="sm" fw={600}>Modifications de Configuration</Text>
                  <Text size="xs">
                    {systemStats.configurationChanges} modification(s) récente(s) détectée(s)
                  </Text>
                </div>
                <Badge size="sm" color="blue">
                  {systemStats.configurationChanges}
                </Badge>
              </Group>
            </Alert>
          )}

          {/* System Statistics */}
          <Card padding="md" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <Text size="sm" fw={600}>Statistiques Système</Text>
            </Group>
            <SimpleGrid cols={4} spacing="md">
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed">Contacts</Text>
                <Text size="lg" fw={700} c="blue">{systemStats.totalContacts}</Text>
              </Stack>
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed">Techniciens</Text>
                <Text size="lg" fw={700} c="green">{systemStats.totalTechnicians}</Text>
              </Stack>
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed">Dernière sauvegarde</Text>
                <Text size="xs" fw={500} ta="center">
                  {systemStats.lastBackupDate ? 
                    new Date(systemStats.lastBackupDate).toLocaleDateString() : 
                    'Aucune'
                  }
                </Text>
              </Stack>
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed">Disponibilité</Text>
                <Text size="lg" fw={700} c="teal">{systemStats.systemUptime.toFixed(1)}%</Text>
              </Stack>
            </SimpleGrid>
          </Card>
        </>
      )}
    </Stack>
  );
};

export default ParametersWidgets;
