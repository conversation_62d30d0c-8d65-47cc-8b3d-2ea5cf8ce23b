'use client';

import {  Stack,  } from '@mantine/core';
import { DataTable, useDataTableColumns, type DataTableSortStatus } from 'mantine-datatable'

import { sortBy } from 'lodash';
import { useEffect, useState } from 'react';
import { companies, type Company } from '@/data';
const PAGE_SIZE = 15;
export default function ResizingComplexExample() {
  const key = 'resize-complex-example';

  const [sortStatus, setSortStatus] = useState<DataTableSortStatus<Company>>({
    columnAccessor: 'Date',
    direction: 'asc',
  });

  const [selectedRecords, setSelectedRecords] = useState<Company[]>([]);

  useEffect(() => {
    const data = sortBy(companies, sortStatus.columnAccessor);
    setRecords(sortStatus.direction === 'desc' ? data.reverse() : data);
  }, [sortStatus]);

  const [withTableBorder, ] = useState(true);
  const [withColumnBorders, ] = useState(true);

  const props = {
    resizable: true,
    sortable: true,
    toggleable: true,
    draggable: true,
  };

  const { effectiveColumns,  } = useDataTableColumns<Company>({
    key,
    columns: [
        {
        accessor: 'Date',
        title: "Date", 
        render: () => <span>29/06/2025</span>,
        ...props,
        },
     {
        accessor: 'Type',
        render: () => <span>visit</span>,
        ...props,
        },
      {
        accessor: 'Bénéficiaire',
        ellipsis: true,
        title: "Bénéficiaire", 
        render: () => <span>ACHRAF KARIM</span>,
        ...props,
        },
         {
        accessor: 'Montant dû',
        textAlign: 'right',
        title: "Montant dû", 
        render: () => <span>800.00</span>,
        ...props,
        },
       {
        accessor: 'Montant encaissé',
        title: "Montant encaissé", 
        render: () => <span> 0.00</span>,
        ...props,
        },
        {
        accessor: 'Avancement',
        title: "Avancement", 
        render: () => <span>100</span>,
        ...props,
        },
       {
        accessor: 'État',
        title: "État", 
        render: () => <span>unpaid</span>,
        ...props,
        },
        {
        accessor: 'Reste à régler',
        title: "Reste à régler", 
        textAlign: 'right',
        render: () => <span>800.00</span>,
        ...props,
        },
         {
        accessor: 'Remise',
        title: "Remise", 
        textAlign: 'right',
        render: () => <span>800.00</span>,
        ...props,
        },


     
    ],
  });
const [page, setPage] = useState(1);

const [records, setRecords] = useState(() =>
  sortBy(companies, 'Date').slice(0, PAGE_SIZE)
);
  useEffect(() => {
    const from = (page - 1) * PAGE_SIZE;
    const to = from + PAGE_SIZE;
    setRecords(companies.slice(from, to));
  }, [page]);
  return (
    <Stack>
      <DataTable
        withTableBorder={withTableBorder}
        withColumnBorders={withColumnBorders}
        selectedRecords={selectedRecords}
        onSelectedRecordsChange={setSelectedRecords}
        storeColumnsKey={key}
        records={records}
        columns={effectiveColumns}
        sortStatus={sortStatus}
        onSortStatusChange={setSortStatus}
        totalRecords={companies.length}
      recordsPerPage={PAGE_SIZE}
      page={page}
      onPageChange={(p) => setPage(p)}
      />
    
    </Stack>
  );
}