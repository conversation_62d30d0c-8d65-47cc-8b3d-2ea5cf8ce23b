from django.contrib import admin
from django import forms
from django.contrib.auth import get_user_model
from django.db import models
from django.core.exceptions import ValidationError
from .models import Appointment, DentistryAppointment, Appoint<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ActiveVisit, PresenceList, HistoryJournal

User = get_user_model()


class DoctorAssistantChoiceField(forms.ModelChoiceField):
    """Custom choice field that displays full names instead of emails and filters by current user."""
    
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        self.appointment_instance = kwargs.pop('appointment_instance', None)
        super().__init__(*args, **kwargs)
        
    def label_from_instance(self, obj):  # type: ignore
        """Display formatted name with fallback logic."""
        if not obj:
            return "Non assigné"
            
        # Multi-level fallback name formatting
        first_name = getattr(obj, 'first_name', '').strip() if hasattr(obj, 'first_name') else ''
        last_name = getattr(obj, 'last_name', '').strip() if hasattr(obj, 'last_name') else ''
        email = getattr(obj, 'email', '').strip() if hasattr(obj, 'email') else ''
        user_type = getattr(obj, 'user_type', '').strip() if hasattr(obj, 'user_type') else ''
        
        # Level 1: firstName + lastName
        if first_name and last_name:
            full_name = f"{first_name} {last_name}"
        # Level 2: firstName only
        elif first_name:
            full_name = first_name
        # Level 3: lastName only  
        elif last_name:
            full_name = last_name
        # Level 4: email prefix (before @)
        elif email and '@' in email:
            full_name = email.split('@')[0]
        # Level 5: default value
        else:
            full_name = "Utilisateur sans nom"
            
        # Add appropriate prefix based on user type
        if user_type == 'doctor':
            return f"Dr. {full_name}"
        elif user_type == 'assistant':
            return f"Assistant {full_name}"
        else:
            return full_name
    
    def get_queryset(self):
        """Filter queryset to show only current doctor and their assistants, plus the currently selected user."""
        base_queryset = User.objects.none()
        
        if not self.request or not self.request.user.is_authenticated:
            return base_queryset
            
        current_user = self.request.user
        
        # Start with empty queryset
        user_ids_to_include = set()
        
        # If current user is a doctor, include self + assistants
        if hasattr(current_user, 'user_type') and current_user.user_type == 'doctor':
            user_ids_to_include.add(current_user.id)
            
            # Add their assistants
            assistants = User.objects.filter(
                user_type='assistant', 
                assigned_doctor=current_user
            )
            for assistant in assistants:
                user_ids_to_include.add(assistant.id)
        
        # If current user is an assistant, include their assigned doctor + self
        elif hasattr(current_user, 'user_type') and current_user.user_type == 'assistant':
            user_ids_to_include.add(current_user.id)
            
            if hasattr(current_user, 'assigned_doctor') and current_user.assigned_doctor:
                user_ids_to_include.add(current_user.assigned_doctor.id)
        
        # For admin/staff, show all doctors and assistants
        elif current_user.is_staff or current_user.is_superuser:
            # Include all doctors and assistants
            doctors_and_assistants = User.objects.filter(
                user_type__in=['doctor', 'assistant']
            )
            for user in doctors_and_assistants:
                user_ids_to_include.add(user.id)
        
        # CRITICAL: Always include the currently selected value if it exists
        current_doctor = None
        if self.appointment_instance and hasattr(self.appointment_instance, 'doctor_or_assistant'):
            current_doctor = self.appointment_instance.doctor_or_assistant
        
        if current_doctor:
            user_ids_to_include.add(current_doctor.id)
        
        # Build final queryset from collected user IDs
        if user_ids_to_include:
            base_queryset = User.objects.filter(id__in=user_ids_to_include)
        
        return base_queryset.distinct().order_by('user_type', 'first_name', 'last_name')


class AppointmentAdminForm(forms.ModelForm):
    """Custom form for appointment admin with enhanced doctor/assistant field."""
    
    class Meta:
        model = Appointment
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Check if doctor_or_assistant field exists in the model
        field_exists = False
        try:
            # Use a try/except approach to check field existence
            try:
                Appointment._meta.get_field('doctor_or_assistant')  # type: ignore
                field_exists = True
            except Exception:
                field_exists = False
        except Exception:
            field_exists = False
            
        if field_exists:
            # Create the custom field dynamically with the current instance
            self.fields['doctor_or_assistant'] = DoctorAssistantChoiceField(
                queryset=User.objects.none(),  # Will be set dynamically
                required=False,
                empty_label="Sélectionner un docteur ou assistant",
                help_text="Docteur ou assistant responsable de ce rendez-vous",
                request=request,
                appointment_instance=self.instance if self.instance and self.instance.pk else None
            )
            
            # Set the queryset using the field's method
            queryset = self.fields['doctor_or_assistant'].get_queryset()
            self.fields['doctor_or_assistant'].queryset = queryset
            
            # Set initial value if editing an existing appointment
            if self.instance and self.instance.pk and hasattr(self.instance, 'doctor_or_assistant'):
                if self.instance.doctor_or_assistant:
                    self.fields['doctor_or_assistant'].initial = self.instance.doctor_or_assistant

    def clean(self):
        """Custom validation to prevent duplicate emails when creating patients"""
        cleaned_data = super().clean()

        # Check if we're creating a new patient (no patient selected)
        patient = cleaned_data.get('patient')

        if not patient:
            # This means we might be creating a new patient
            # Check if there are any patient-related fields that might indicate email
            # Since the admin form might have inline patient creation
            pass  # For now, the User model validation will catch duplicates

        return cleaned_data


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    form = AppointmentAdminForm
    
    # Default list display - will be updated in __init__ if field exists
    list_display = ('title', 'patient', 'doctor', 'appointment_date', 'status', 'appointment_type')
    list_filter = ('status', 'appointment_type', 'priority', 'appointment_date', 'created_at')
    search_fields = ('title', 'patient__first_name', 'patient__last_name', 'doctor__first_name', 'doctor__last_name')
    date_hierarchy = 'appointment_date'
    ordering = ('-appointment_date', '-appointment_time')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Check if doctor_or_assistant field exists and adjust list_display accordingly
        field_exists = False
        try:
            from .models import Appointment
            # Use a try/except approach to check field existence
            try:
                Appointment._meta.get_field('doctor_or_assistant')  # type: ignore
                field_exists = True
            except Exception:
                field_exists = False
        except Exception:
            field_exists = False
            
        if field_exists:
            # Field exists, use enhanced display
            self.list_display = ('title', 'patient', 'display_doctor_or_assistant', 'appointment_date', 'status', 'appointment_type')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Limit doctor_or_assistant choices to doctors and their assistants."""
        if db_field.name == "doctor_or_assistant":
            # Only show doctors and their assistants
            kwargs["queryset"] = User.objects.filter(
                models.Q(user_type='doctor') | models.Q(user_type='assistant')
            ).order_by('user_type', 'first_name', 'last_name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_queryset(self, request):
        """Filter appointments based on user role."""
        qs = super().get_queryset(request)
        current_user = request.user

        # For superusers, show all appointments
        if current_user.is_superuser:
            return qs

        # For staff users, show all appointments
        if current_user.is_staff:
            return qs

        # For doctors, show their appointments and appointments of their assistants
        if current_user.is_doctor:
            try:
                # Create Q objects separately to avoid operator issues
                q1 = models.Q(doctor=current_user)
                q2 = models.Q(doctor_or_assistant=current_user)
                q3 = models.Q(doctor_or_assistant__assigned_doctor=current_user)
                return qs.filter(q1 | q2 | q3)  # type: ignore
            except Exception:
                # Fallback if doctor_or_assistant field doesn't exist
                return qs.filter(doctor=current_user)

        # For assistants, show their appointments and appointments of their assigned doctor
        if current_user.is_assistant:
            try:
                # Create Q objects separately to avoid operator issues
                q1 = models.Q(doctor=current_user.assigned_doctor)
                q2 = models.Q(doctor_or_assistant=current_user.assigned_doctor)
                q3 = models.Q(doctor_or_assistant=current_user)
                return qs.filter(q1 | q2 | q3)  # type: ignore
            except Exception:
                # Fallback if doctor_or_assistant field doesn't exist
                return qs.filter(doctor=current_user.assigned_doctor)

        # For other users, show nothing
        return qs.none()

    def get_fieldsets(self, request, obj=None):  # type: ignore
        """Dynamic fieldsets that handle missing doctor_or_assistant field."""
        # Check if doctor_or_assistant field exists using a safer approach
        field_exists = False
        try:
            from .models import Appointment
            # Use a try/except approach to check field existence
            try:
                Appointment._meta.get_field('doctor_or_assistant')  # type: ignore
                field_exists = True
            except Exception:
                field_exists = False
        except Exception:
            field_exists = False
            
        if field_exists:
            # Use enhanced fieldsets with doctor_or_assistant
            return [
                ('Basic Information', {
                    'fields': ('patient', 'patient_title', 'doctor_or_assistant', 'title', 'description', 'appointment_type', 'status', 'priority')
                }),
                ('Legacy Doctor Field', {
                    'fields': ('doctor',),
                    'classes': ('collapse',),
                    'description': 'Legacy doctor field - use Doctor/Assistant field above instead'
                }),
                ('Schedule', {
                    'fields': ('appointment_date', 'appointment_time', 'end_time', 'duration_minutes', 'consultation_duration')
                }),
                ('Location & Resources', {
                    'fields': ('room', 'equipment_needed'),
                    'classes': ('collapse',)
                }),
                ('Medical Information', {
                    'fields': ('reason_for_visit', 'symptoms', 'notes'),
                    'classes': ('collapse',)
                }),
                ('Billing', {
                    'fields': ('estimated_cost', 'insurance_covered'),
                    'classes': ('collapse',)
                }),
                ('Patient Details', {
                    'fields': ('gender', 'etat_civil', 'cin', 'social_security', 'profession', 'birth_place', 'father_name', 'mother_name', 'blood_group', 'allergies'),
                    'classes': ('collapse',)
                }),
                ('Patient Contact & Personal Info', {
                    'fields': ('patient_gender_display', 'patient_phone_display', 'patient_address_display', 'patient_date_of_birth_display', 'patient_age_display', 'patient_landline_display', 'patient_email_display'),
                    'classes': ('collapse',)
                }),
                ('Comments', {
                    'fields': ('comment', 'Commentairelistedattente'),
                    'classes': ('collapse',)
                }),
                ('Event Settings', {
                    'fields': ('event_resource_id', 'event_type', 'is_waiting_list', 'add_to_waiting_list', 'is_active', 'is_in_presentation_room', 'is_in_history_journal', 'checked_appel_video', 'checked_rappel_sms', 'checked_rappel_email'),
                    'classes': ('collapse',)
                }),
                ('Reminders', {
                    'fields': ('reminder_sent', 'reminder_sent_at'),
                    'classes': ('collapse',)
                }),
            ]
        else:
            # Use legacy fieldsets with only doctor field
            return [
                ('Basic Information', {
                    'fields': ('patient', 'patient_title', 'doctor', 'title', 'description', 'appointment_type', 'status', 'priority')
                }),
                ('Schedule', {
                    'fields': ('appointment_date', 'appointment_time', 'end_time', 'duration_minutes', 'consultation_duration')
                }),
                ('Location & Resources', {
                    'fields': ('room', 'equipment_needed'),
                    'classes': ('collapse',)
                }),
                ('Medical Information', {
                    'fields': ('reason_for_visit', 'symptoms', 'notes'),
                    'classes': ('collapse',)
                }),
                ('Billing', {
                    'fields': ('estimated_cost', 'insurance_covered'),
                    'classes': ('collapse',)
                }),
                ('Patient Details', {
                    'fields': ('gender', 'etat_civil', 'cin', 'social_security', 'profession', 'birth_place', 'father_name', 'mother_name', 'blood_group', 'allergies'),
                    'classes': ('collapse',)
                }),
                ('Patient Contact & Personal Info', {
                    'fields': ('patient_gender_display', 'patient_phone_display', 'patient_address_display', 'patient_date_of_birth_display', 'patient_age_display', 'patient_landline_display', 'patient_email_display'),
                    'classes': ('collapse',)
                }),
                ('Comments', {
                    'fields': ('comment', 'Commentairelistedattente'),
                    'classes': ('collapse',)
                }),
                ('Event Settings', {
                    'fields': ('event_resource_id', 'event_type', 'is_waiting_list', 'add_to_waiting_list', 'is_active', 'is_in_presentation_room', 'is_in_history_journal', 'checked_appel_video', 'checked_rappel_sms', 'checked_rappel_email'),
                    'classes': ('collapse',)
                }),
                ('Reminders', {
                    'fields': ('reminder_sent', 'reminder_sent_at'),
                    'classes': ('collapse',)
                }),
            ]
    
    readonly_fields = ('created_at', 'updated_at', 'patient_gender_display', 'patient_phone_display', 'patient_address_display', 'patient_date_of_birth_display', 'patient_age_display', 'patient_landline_display', 'patient_email_display')

    def display_doctor_or_assistant(self, obj):
        """Display the doctor or assistant assigned to this appointment."""
        if obj.doctor_or_assistant:
            user = obj.doctor_or_assistant
            if hasattr(user, 'get_full_name') and user.get_full_name():
                name = user.get_full_name()
            else:
                name = user.email if hasattr(user, 'email') else str(user)
            
            user_type = getattr(user, 'user_type', '')
            if user_type == 'doctor':
                return f"Dr. {name}"
            elif user_type == 'assistant':
                return f"Assistant {name}"
            else:
                return name
        return "-"
    display_doctor_or_assistant.short_description = 'Doctor/Assistant'  # type: ignore

    def formatted_appointment_time(self, obj):
        """Display formatted appointment time."""
        if obj.appointment_time:
            return obj.appointment_time.strftime('%H:%M')
        return "-"
    formatted_appointment_time.short_description = 'Time'  # type: ignore

    # Display methods for patient information
    def patient_gender_display(self, obj):
        """Display patient gender."""
        if obj.patient and hasattr(obj.patient, 'gender'):
            return obj.patient.gender or "-"
        return "-"
    patient_gender_display.short_description = 'Patient Gender'  # type: ignore

    def patient_phone_display(self, obj):
        """Display patient phone."""
        if obj.patient and hasattr(obj.patient, 'phone_number'):
            return obj.patient.phone_number or "-"
        return "-"
    patient_phone_display.short_description = 'Patient Phone'  # type: ignore

    def patient_address_display(self, obj):
        """Display patient address."""
        if obj.patient and hasattr(obj.patient, 'address'):
            return obj.patient.address or "-"
        return "-"
    patient_address_display.short_description = 'Patient Address'  # type: ignore

    def patient_date_of_birth_display(self, obj):
        """Display patient date of birth."""
        if obj.patient and hasattr(obj.patient, 'date_of_birth'):
            return obj.patient.date_of_birth or "-"
        return "-"
    patient_date_of_birth_display.short_description = 'Patient DOB'  # type: ignore

    def patient_age_display(self, obj):
        """Display patient age."""
        if obj.patient and hasattr(obj.patient, 'age'):
            return obj.patient.age or "-"
        return "-"
    patient_age_display.short_description = 'Patient Age'  # type: ignore

    def patient_landline_display(self, obj):
        """Display patient landline."""
        if obj.patient and hasattr(obj.patient, 'landline_number'):
            return obj.patient.landline_number or "-"
        return "-"
    patient_landline_display.short_description = 'Patient Landline'  # type: ignore

    def patient_email_display(self, obj):
        """Display patient email."""
        if obj.patient and hasattr(obj.patient, 'email'):
            return obj.patient.email or "-"
        return "-"
    patient_email_display.short_description = 'Patient Email'  # type: ignore


@admin.register(DentistryAppointment)
class DentistryAppointmentAdmin(admin.ModelAdmin):
    list_display = ('appointment', 'procedure_type', 'tooth_numbers', 'anesthesia_required', 'follow_up_required')
    list_filter = ('procedure_type', 'quadrant', 'anesthesia_required', 'follow_up_required', 'lab_work_required')
    search_fields = ('appointment__title', 'appointment__patient__first_name', 'appointment__patient__last_name', 'procedure_type')
    
    fieldsets = (
        ('Appointment Link', {
            'fields': ('appointment',)
        }),
        ('Procedure Details', {
            'fields': ('procedure_type', 'tooth_numbers', 'quadrant')
        }),
        ('Anesthesia', {
            'fields': ('anesthesia_required', 'anesthesia_type', 'pre_medication'),
            'classes': ('collapse',)
        }),
        ('Treatment', {
            'fields': ('post_care_instructions', 'materials_used'),
            'classes': ('collapse',)
        }),
        ('Follow-up', {
            'fields': ('follow_up_required', 'follow_up_date', 'follow_up_notes'),
            'classes': ('collapse',)
        }),
        ('Lab Work', {
            'fields': ('lab_work_required', 'lab_instructions'),
            'classes': ('collapse',)
        }),
    )


@admin.register(AppointmentReminder)
class AppointmentReminderAdmin(admin.ModelAdmin):
    list_display = ('appointment', 'reminder_type', 'status', 'send_at', 'sent_at', 'attempts')
    list_filter = ('reminder_type', 'status', 'send_at', 'created_at')
    search_fields = ('appointment__title', 'appointment__patient__first_name', 'appointment__patient__last_name')
    date_hierarchy = 'send_at'
    ordering = ('-send_at',)
    
    fieldsets = (
        ('Appointment', {
            'fields': ('appointment',)
        }),
        ('Reminder Details', {
            'fields': ('reminder_type', 'status', 'send_at', 'sent_at')
        }),
        ('Content', {
            'fields': ('subject', 'message')
        }),
        ('Tracking', {
            'fields': ('attempts', 'error_message'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('sent_at', 'created_at', 'updated_at')


@admin.register(DoctorPause)
class DoctorPauseAdmin(admin.ModelAdmin):
    list_display = ('title', 'doctor', 'date_from', 'date_to', 'duration_minutes', 'is_recurring')
    list_filter = ('doctor', 'is_recurring', 'date_from')
    search_fields = ('title', 'doctor__first_name', 'doctor__last_name', 'doctor__email')
    readonly_fields = ('created_at', 'updated_at', 'duration_minutes')

    fieldsets = (
        ('Basic Information', {
            'fields': ('doctor', 'title')
        }),
        ('Schedule', {
            'fields': ('date_from', 'date_to', 'duration_minutes')
        }),
        ('Options', {
            'fields': ('is_recurring', 'notes')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PatientList)
class PatientListAdmin(admin.ModelAdmin):
    list_display = ('name', 'list_type', 'is_active', 'patient_count', 'created_at')
    list_filter = ('list_type', 'is_active', 'is_public', 'created_at')
    search_fields = ('name', 'description')
    filter_horizontal = ('patients', 'assigned_staff')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'list_type', 'is_active', 'is_public')
        }),
        ('Patients', {
            'fields': ('patients',)
        }),
        ('Staff Assignment', {
            'fields': ('assigned_staff', 'created_by')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def patient_count(self, obj):
        """Display the number of patients in the list."""
        return obj.patients.count()
    patient_count.short_description = 'Number of Patients'  # type: ignore

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ActiveVisit)
class ActiveVisitAdmin(admin.ModelAdmin):
    list_display = ('patient', 'visit_status', 'current_location', 'assigned_staff', 'check_in_time', 'wait_time_minutes')
    list_filter = ('visit_status', 'priority', 'check_in_time', 'assigned_staff')
    search_fields = ('patient__first_name', 'patient__last_name', 'current_location', 'notes')
    readonly_fields = ('check_in_time', 'status_change_time', 'created_at', 'updated_at', 'wait_time_minutes')
    
    fieldsets = (
        ('Visit Information', {
            'fields': ('patient', 'appointment', 'visit_status', 'priority')
        }),
        ('Location & Assignment', {
            'fields': ('current_location', 'room', 'assigned_staff')
        }),
        ('Timing', {
            'fields': ('check_in_time', 'status_change_time', 'estimated_duration', 'wait_time_minutes')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def wait_time_minutes(self, obj):
        """Calculate and display the wait time in minutes."""
        if obj.average_wait_time:
            return int(obj.average_wait_time.total_seconds() / 60)
        return 0
    wait_time_minutes.short_description = 'Wait Time (minutes)'  # type: ignore


@admin.register(PresenceList)
class PresenceListAdmin(admin.ModelAdmin):
    list_display = ('staff_member', 'status', 'location', 'start_time', 'duration_minutes', 'is_current')
    list_filter = ('status', 'is_available_for_patients', 'start_time')
    search_fields = ('staff_member__first_name', 'staff_member__last_name', 'location', 'notes')
    readonly_fields = ('start_time', 'end_time', 'created_at', 'updated_at', 'duration_minutes', 'is_current')
    
    fieldsets = (
        ('Staff Member', {
            'fields': ('staff_member',)
        }),
        ('Presence Information', {
            'fields': ('status', 'location', 'assigned_room', 'is_available_for_patients')
        }),
        ('Timing', {
            'fields': ('start_time', 'end_time', 'duration_minutes', 'is_current')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def duration_minutes(self, obj):
        """Display the duration in minutes."""
        return obj.duration
    duration_minutes.short_description = 'Duration (minutes)'  # type: ignore
    
    def is_current(self, obj):
        return obj.is_current
    is_current.short_description = 'Currently Active'  # type: ignore
    is_current.boolean = True  # type: ignore


@admin.register(HistoryJournal)
class HistoryJournalAdmin(admin.ModelAdmin):
    list_display = ('patient', 'title', 'entry_type', 'category', 'event_date', 'created_by', 'is_important')
    list_filter = ('entry_type', 'category', 'is_private', 'is_important', 'event_date', 'created_at')
    search_fields = ('patient__first_name', 'patient__last_name', 'title', 'description')
    filter_horizontal = ('related_staff',)
    readonly_fields = ('created_at', 'updated_at', 'formatted_event_date')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('patient', 'appointment', 'title', 'description', 'summary')
        }),
        ('Categorization', {
            'fields': ('entry_type', 'category', 'is_private', 'is_important')
        }),
        ('Staff Involvement', {
            'fields': ('created_by', 'related_staff')
        }),
        ('References', {
            'fields': ('related_document', 'related_attachment'),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': ('event_date', 'formatted_event_date', 'created_at', 'updated_at')
        }),
    )
    
    def formatted_event_date(self, obj):
        """Display the event date in a formatted way."""
        if obj.event_date:
            return obj.event_date.strftime('%Y-%m-%d %H:%M')
        return '-'
    formatted_event_date.short_description = 'Event Date'  # type: ignore
