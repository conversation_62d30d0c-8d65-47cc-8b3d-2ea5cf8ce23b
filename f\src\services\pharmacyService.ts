/**
 * Pharmacy Service
 * Handles all pharmacy-related data operations including:
 * - Medication and drug management
 * - Inventory and stock tracking
 * - Purchase orders and supplier management
 * - Sales and delivery management
 * - Pharmacy analytics and reporting
 */

// Types for Pharmacy data
export interface Medication {
  id: string;
  name: string;
  generic_name?: string;
  brand_name?: string;
  dosage: string;
  form: 'tablet' | 'capsule' | 'liquid' | 'injection' | 'cream' | 'other';
  family_id: string;
  family_name: string;
  supplier_id: string;
  supplier_name: string;
  barcode?: string;
  description?: string;
  active_ingredient: string;
  contraindications?: string;
  side_effects?: string;
  storage_conditions: string;
  prescription_required: boolean;
  status: 'active' | 'discontinued' | 'out_of_stock';
  created_at: string;
  updated_at: string;
}

export interface InventoryItem {
  id: string;
  medication_id: string;
  medication_name: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  unit_cost: number;
  selling_price: number;
  expiry_date: string;
  batch_number: string;
  location: string;
  depot_id: string;
  depot_name: string;
  last_updated: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'expired';
}

export interface PurchaseOrder {
  id: string;
  order_number: string;
  supplier_id: string;
  supplier_name: string;
  order_date: string;
  expected_delivery: string;
  status: 'draft' | 'sent' | 'confirmed' | 'received' | 'cancelled';
  total_amount: number;
  items: PurchaseOrderItem[];
  notes?: string;
  created_by: string;
  created_at: string;
}

export interface PurchaseOrderItem {
  id: string;
  medication_id: string;
  medication_name: string;
  quantity_ordered: number;
  quantity_received: number;
  unit_cost: number;
  total_cost: number;
  expiry_date?: string;
  batch_number?: string;
}

export interface SalesOrder {
  id: string;
  order_number: string;
  customer_name?: string;
  customer_id?: string;
  order_date: string;
  delivery_date?: string;
  status: 'draft' | 'confirmed' | 'delivered' | 'cancelled';
  total_amount: number;
  items: SalesOrderItem[];
  delivery_address?: string;
  notes?: string;
  created_by: string;
  created_at: string;
}

export interface SalesOrderItem {
  id: string;
  medication_id: string;
  medication_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  discount?: number;
}

export interface Supplier {
  id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  payment_terms?: string;
  delivery_time?: number;
  rating?: number;
  status: 'active' | 'inactive';
  created_at: string;
}

export interface MedicationFamily {
  id: string;
  name: string;
  description?: string;
  parent_family_id?: string;
  medication_count: number;
  created_at: string;
}

export interface StockMovement {
  id: string;
  medication_id: string;
  medication_name: string;
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment' | 'expired';
  quantity: number;
  unit_cost?: number;
  reference_id?: string;
  reference_type?: 'purchase' | 'sale' | 'transfer' | 'adjustment';
  from_depot?: string;
  to_depot?: string;
  notes?: string;
  created_by: string;
  created_at: string;
}

export interface PharmacyAnalytics {
  id: number;
  report_date: string;
  total_medications: number;
  total_stock_value: number;
  low_stock_items: number;
  expired_items: number;
  top_selling_medications: TopSellingMedication[];
  supplier_performance: SupplierPerformance[];
  stock_turnover_rate: number;
  expiry_alerts: ExpiryAlert[];
  purchase_trends: PurchaseTrend[];
  sales_trends: SalesTrend[];
}

export interface TopSellingMedication {
  medication_id: string;
  medication_name: string;
  quantity_sold: number;
  revenue: number;
  profit_margin: number;
}

export interface SupplierPerformance {
  supplier_id: string;
  supplier_name: string;
  total_orders: number;
  on_time_deliveries: number;
  average_delivery_time: number;
  total_value: number;
}

export interface ExpiryAlert {
  medication_id: string;
  medication_name: string;
  batch_number: string;
  expiry_date: string;
  days_to_expiry: number;
  current_stock: number;
  estimated_loss: number;
}

export interface PurchaseTrend {
  month: string;
  total_purchases: number;
  total_value: number;
  average_order_value: number;
}

export interface SalesTrend {
  month: string;
  total_sales: number;
  total_value: number;
  average_sale_value: number;
}

export interface PharmacySummary {
  reportDate: string;
  medications: Medication[];
  inventory: InventoryItem[];
  purchaseOrders: PurchaseOrder[];
  salesOrders: SalesOrder[];
  suppliers: Supplier[];
  medicationFamilies: MedicationFamily[];
  stockMovements: StockMovement[];
  analytics: PharmacyAnalytics;
  lastUpdate: string;
}

class PharmacyService {
  private baseURL = '/api/pharmacy';

  // Medications operations
  async getMedications(familyId?: string, supplierId?: string): Promise<Medication[]> {
    try {
      let url = `${this.baseURL}/medications`;
      const params = new URLSearchParams();
      
      if (familyId) params.append('family_id', familyId);
      if (supplierId) params.append('supplier_id', supplierId);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch medications: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching medications:', error);
      return this.getMockMedications();
    }
  }

  // Inventory operations
  async getInventory(depotId?: string, lowStockOnly?: boolean): Promise<InventoryItem[]> {
    try {
      let url = `${this.baseURL}/inventory`;
      const params = new URLSearchParams();
      
      if (depotId) params.append('depot_id', depotId);
      if (lowStockOnly) params.append('low_stock_only', 'true');
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch inventory: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching inventory:', error);
      return this.getMockInventory();
    }
  }

  // Purchase Orders operations
  async getPurchaseOrders(status?: string, supplierId?: string): Promise<PurchaseOrder[]> {
    try {
      let url = `${this.baseURL}/purchase-orders`;
      const params = new URLSearchParams();
      
      if (status) params.append('status', status);
      if (supplierId) params.append('supplier_id', supplierId);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch purchase orders: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      return this.getMockPurchaseOrders();
    }
  }

  // Sales Orders operations
  async getSalesOrders(status?: string, dateRange?: { start: string; end: string }): Promise<SalesOrder[]> {
    try {
      let url = `${this.baseURL}/sales-orders`;
      const params = new URLSearchParams();
      
      if (status) params.append('status', status);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch sales orders: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching sales orders:', error);
      return this.getMockSalesOrders();
    }
  }

  // Suppliers operations
  async getSuppliers(): Promise<Supplier[]> {
    try {
      const response = await fetch(`${this.baseURL}/suppliers`);
      if (!response.ok) {
        throw new Error(`Failed to fetch suppliers: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      return this.getMockSuppliers();
    }
  }

  // Medication Families operations
  async getMedicationFamilies(): Promise<MedicationFamily[]> {
    try {
      const response = await fetch(`${this.baseURL}/medication-families`);
      if (!response.ok) {
        throw new Error(`Failed to fetch medication families: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching medication families:', error);
      return this.getMockMedicationFamilies();
    }
  }

  // Stock Movements operations
  async getStockMovements(medicationId?: string, dateRange?: { start: string; end: string }): Promise<StockMovement[]> {
    try {
      let url = `${this.baseURL}/stock-movements`;
      const params = new URLSearchParams();
      
      if (medicationId) params.append('medication_id', medicationId);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch stock movements: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching stock movements:', error);
      return this.getMockStockMovements();
    }
  }

  // Analytics operations
  async getPharmacyAnalytics(dateRange?: { start: string; end: string }): Promise<PharmacyAnalytics> {
    try {
      let url = `${this.baseURL}/analytics`;
      if (dateRange) {
        const params = new URLSearchParams({
          start: dateRange.start,
          end: dateRange.end,
        });
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch pharmacy analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching pharmacy analytics:', error);
      return this.getMockPharmacyAnalytics();
    }
  }

  // Get comprehensive pharmacy summary
  async getPharmacySummary(dateRange?: { start: string; end: string }): Promise<PharmacySummary> {
    try {
      const [medications, inventory, purchaseOrders, salesOrders, suppliers, medicationFamilies, stockMovements, analytics] = await Promise.all([
        this.getMedications(),
        this.getInventory(),
        this.getPurchaseOrders(),
        this.getSalesOrders(undefined, dateRange),
        this.getSuppliers(),
        this.getMedicationFamilies(),
        this.getStockMovements(undefined, dateRange),
        this.getPharmacyAnalytics(dateRange),
      ]);

      return {
        reportDate: new Date().toISOString(),
        medications,
        inventory,
        purchaseOrders,
        salesOrders,
        suppliers,
        medicationFamilies,
        stockMovements,
        analytics,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching pharmacy summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockMedications(): Medication[] {
    return [
      {
        id: '1',
        name: 'Paracétamol 500mg',
        generic_name: 'Paracétamol',
        brand_name: 'Doliprane',
        dosage: '500mg',
        form: 'tablet',
        family_id: 'analgesics',
        family_name: 'Analgésiques',
        supplier_id: 'sup1',
        supplier_name: 'Pharma Supply Co',
        active_ingredient: 'Paracétamol',
        storage_conditions: 'Température ambiante',
        prescription_required: false,
        status: 'active',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockInventory(): InventoryItem[] {
    return [
      {
        id: '1',
        medication_id: '1',
        medication_name: 'Paracétamol 500mg',
        current_stock: 150,
        minimum_stock: 50,
        maximum_stock: 500,
        unit_cost: 0.25,
        selling_price: 0.50,
        expiry_date: '2025-12-31',
        batch_number: 'BATCH001',
        location: 'A1-B2',
        depot_id: 'depot1',
        depot_name: 'Dépôt Principal',
        last_updated: '2024-01-20T10:00:00Z',
        status: 'in_stock',
      },
    ];
  }

  private getMockPurchaseOrders(): PurchaseOrder[] {
    return [
      {
        id: '1',
        order_number: 'PO-2024-001',
        supplier_id: 'sup1',
        supplier_name: 'Pharma Supply Co',
        order_date: '2024-01-20',
        expected_delivery: '2024-01-25',
        status: 'confirmed',
        total_amount: 2500,
        items: [
          {
            id: '1',
            medication_id: '1',
            medication_name: 'Paracétamol 500mg',
            quantity_ordered: 1000,
            quantity_received: 0,
            unit_cost: 0.25,
            total_cost: 250,
          },
        ],
        created_by: 'admin',
        created_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockSalesOrders(): SalesOrder[] {
    return [
      {
        id: '1',
        order_number: 'SO-2024-001',
        customer_name: 'Clinique Centrale',
        order_date: '2024-01-20',
        status: 'delivered',
        total_amount: 150,
        items: [
          {
            id: '1',
            medication_id: '1',
            medication_name: 'Paracétamol 500mg',
            quantity: 100,
            unit_price: 0.50,
            total_price: 50,
          },
        ],
        created_by: 'admin',
        created_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockSuppliers(): Supplier[] {
    return [
      {
        id: 'sup1',
        name: 'Pharma Supply Co',
        contact_person: 'Jean Martin',
        email: '<EMAIL>',
        phone: '+33123456789',
        address: '123 Rue de la Pharmacie',
        city: 'Paris',
        country: 'France',
        payment_terms: '30 jours',
        delivery_time: 5,
        rating: 4.5,
        status: 'active',
        created_at: '2024-01-01T10:00:00Z',
      },
    ];
  }

  private getMockMedicationFamilies(): MedicationFamily[] {
    return [
      {
        id: 'analgesics',
        name: 'Analgésiques',
        description: 'Médicaments contre la douleur',
        medication_count: 25,
        created_at: '2024-01-01T10:00:00Z',
      },
    ];
  }

  private getMockStockMovements(): StockMovement[] {
    return [
      {
        id: '1',
        medication_id: '1',
        medication_name: 'Paracétamol 500mg',
        movement_type: 'in',
        quantity: 1000,
        unit_cost: 0.25,
        reference_id: '1',
        reference_type: 'purchase',
        notes: 'Réception commande PO-2024-001',
        created_by: 'admin',
        created_at: '2024-01-20T10:00:00Z',
      },
    ];
  }

  private getMockPharmacyAnalytics(): PharmacyAnalytics {
    return {
      id: 1,
      report_date: '2024-01-20',
      total_medications: 250,
      total_stock_value: 125000,
      low_stock_items: 15,
      expired_items: 3,
      top_selling_medications: [
        {
          medication_id: '1',
          medication_name: 'Paracétamol 500mg',
          quantity_sold: 500,
          revenue: 250,
          profit_margin: 50,
        },
      ],
      supplier_performance: [
        {
          supplier_id: 'sup1',
          supplier_name: 'Pharma Supply Co',
          total_orders: 12,
          on_time_deliveries: 11,
          average_delivery_time: 4.5,
          total_value: 25000,
        },
      ],
      stock_turnover_rate: 6.5,
      expiry_alerts: [
        {
          medication_id: '2',
          medication_name: 'Aspirine 100mg',
          batch_number: 'BATCH002',
          expiry_date: '2024-03-15',
          days_to_expiry: 45,
          current_stock: 25,
          estimated_loss: 12.5,
        },
      ],
      purchase_trends: [
        { month: 'Jan', total_purchases: 15, total_value: 25000, average_order_value: 1666.67 },
      ],
      sales_trends: [
        { month: 'Jan', total_sales: 45, total_value: 15000, average_sale_value: 333.33 },
      ],
    };
  }
}

export const pharmacyService = new PharmacyService();
export default pharmacyService;
