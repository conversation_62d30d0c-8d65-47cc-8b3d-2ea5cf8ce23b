'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  NumberInput,
  Radio,
  Switch,
  Select,
  Menu,
  Divider
} from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiCalendarMonth,
  mdiFilterMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiCog
} from '@mdi/js';

// Types et interfaces
interface ActivityState {
  name: string;
  label: string;
  type: 'procedure' | 'dental' | 'medical' | 'encasement' | 'payment';
  deactivated?: boolean;
}

interface ProcedureActivityType {
  value: number;
  label: string;
}

interface FinancialSummary {
  total: number;
  encasement_total: number;
  loaded: boolean;
}

interface PivotData {
  [key: string]: {
    [procedure: string]: number;
  };
}

interface ActivityQuery {
  year: number;
  use_discount: boolean;
}

interface ActiviteAnnuelleProps {
  cycle?: 'annual' | 'monthly' | 'periodic';
  summary?: FinancialSummary;
  pivotData?: PivotData;
  loading?: boolean;
  onQueryChange?: (query: ActivityQuery) => void;
  onActivityChange?: (activity: ActivityState) => void;
  onProcedureTypeChange?: (type: number) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
}

export const ActiviteAnnuelle: React.FC<ActiviteAnnuelleProps> = ({
  cycle = 'annual',
  summary,
  pivotData,
  loading = false,
  onQueryChange,
  onActivityChange,
  onProcedureTypeChange,
  onExport,
  onPrint
}) => {
  // États locaux
  const [query, setQuery] = useState<ActivityQuery>({
    year: new Date().getFullYear(),
    use_discount: true
  });

  const [selectedActivity, setSelectedActivity] = useState<ActivityState>({
    name: 'procedures',
    label: 'Les actes',
    type: 'procedure'
  });

  const [procedureActivityType, setProcedureActivityType] = useState<number>(0);

  // Données mockées
  const mockSummary: FinancialSummary = {
    total: 220125.00,
    encasement_total: 180700.00,
    loaded: true
  };

  const activityStates: ActivityState[] = [
    { name: 'procedures', label: 'Les actes', type: 'procedure' },
    { name: 'dental_procedures', label: 'Les actes dentaires', type: 'dental' },
    { name: 'medical_procedures', label: 'les actes de soins', type: 'medical' },
    { name: 'encasements', label: 'Encaissements', type: 'encasement' },
    { name: 'payments', label: 'Paiements', type: 'payment' }
  ];

  const procedureActivityTypes: ProcedureActivityType[] = [
    { value: 0, label: 'État en chiffre d\'affaire' },
    { value: 1, label: 'État en nombre d\'execution' },
    { value: 2, label: 'Les deux' }
  ];

  const mockPivotData: PivotData = {
    'Mars': {
      'Amalgame cl simple': 300.00,
      'Camera Intrabuccale': 300.00
    },
    'Avril': {
      'Ablation de fil de suture': 350.00,
      'Aéropolissage': 500.00,
      'Amalgame cl simple': 357.14,
      'Analyse Biologique': 0.00,
      'Apexification': 1500.00,
      'Apexogénèse': 1152.48,
      'Brossage Prophylactique': 200.00,
      'Camera Intrabuccale': 4400.00,
      'CCC': 9000.00,
      'Coiffe zircone': 800.00,
      'Collage Couronne': 1500.00,
      'Collage Facette': 384.16,
      'Collage Fragment Dentaire': 500.00
    },
    'Mai': {
      'Biopulpotomie': 800.00,
      'Brossage Prophylactique': 200.00
    },
    'Juin': {
      'Camera Intrabuccale': 300.00,
      'Coiffe zircone': 800.00
    }
  };

  const currentSummary = summary || mockSummary;
  const currentPivotData = pivotData || mockPivotData;

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<ActivityQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleActivityChange = (activity: ActivityState) => {
    setSelectedActivity(activity);
    onActivityChange?.(activity);
  };

  const handleProcedureTypeChange = (value: string | null) => {
    if (value !== null) {
      const type = parseInt(value);
      setProcedureActivityType(type);
      onProcedureTypeChange?.(type);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  // Fonctions utilitaires
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 2
    }).format(amount).replace('MAD', 'DH');
  };

  const calculateTotal = (monthData: { [procedure: string]: number }) => {
    return Object.values(monthData).reduce((sum, value) => sum + value, 0);
  };

  const getAllProcedures = () => {
    const procedures = new Set<string>();
    Object.values(currentPivotData).forEach(monthData => {
      Object.keys(monthData).forEach(procedure => procedures.add(procedure));
    });
    return Array.from(procedures).sort();
  };

  const procedures = getAllProcedures();
  const months = Object.keys(currentPivotData);

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiCalendarMonth} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Activité annuelle</Text>
          </Group>

          {/* Résumé financier */}
          {currentSummary.loaded && (
            <Group gap="md" style={{ flex: 1, justifyContent: 'center' }}>
              <Text size="sm">
                <Text component="span" fw={500}>Total actes: </Text>
                <Text component="span" fw={700}>{formatCurrency(currentSummary.total)}</Text>
              </Text>
              <Text size="sm">
                <Text component="span" fw={500}>Total encaissements: </Text>
                <Text component="span" fw={700}>{formatCurrency(currentSummary.encasement_total)}</Text>
              </Text>
              <Text size="sm">
                <Text component="span" fw={500}>Différence: </Text>
                <Text
                  component="span"
                  fw={700}
                  c={currentSummary.encasement_total - currentSummary.total < 0 ? 'red' : 'green'}
                >
                  {formatCurrency(currentSummary.encasement_total - currentSummary.total)}
                </Text>
              </Text>
            </Group>
          )}

          {/* Bouton filtre */}
          <ActionIcon variant="subtle" size="lg">
            <Icon path={mdiFilterMultiple} size={1} />
          </ActionIcon>
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md">
          {/* Sélecteur d'année */}
          {cycle === 'annual' && (
            <NumberInput
              label="Année"
              value={query.year}
              onChange={(value) => handleQueryChange({ year: typeof value === 'string' ? parseInt(value) || new Date().getFullYear() : value || new Date().getFullYear() })}
              min={1970}
              max={2025}
              style={{ width: 120 }}
            />
          )}

          {/* Source de données */}
          <Box style={{ marginLeft: 12 }}>
            <Text size="sm" fw={500} mb="xs">Source de données</Text>
            <Radio.Group
              value={selectedActivity.name}
              onChange={(value) => {
                const activity = activityStates.find(a => a.name === value);
                if (activity) handleActivityChange(activity);
              }}
            >
              <Group>
                {activityStates.map((state) => (
                  <Radio
                    key={state.name}
                    value={state.name}
                    label={state.label}
                    disabled={state.deactivated}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>

          <Box style={{ flex: 1 }} />

          {/* Switch remise globale */}
          {selectedActivity.type === 'procedure' && (
            <Switch
              label="Remise globale"
              checked={query.use_discount}
              onChange={(event) => handleQueryChange({ use_discount: event.currentTarget.checked })}
            />
          )}

          {/* Type d'état de procédure */}
          {selectedActivity.type === 'procedure' && (
            <Select
              label="Type d'état de procedure"
              value={procedureActivityType.toString()}
              onChange={handleProcedureTypeChange}
              data={procedureActivityTypes.map(type => ({
                value: type.value.toString(),
                label: type.label
              }))}
              style={{ width: 250 }}
            />
          )}
        </Group>
      </Paper>

      <Divider />

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Format">
                  <Icon path={mdiTableEdit} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Tableau des données */}
            <ScrollArea style={{ height: 'calc(100vh - 300px)' }}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ minWidth: 150, position: 'sticky', left: 0, backgroundColor: 'white', zIndex: 1 }}>
                      Période / Médecin
                    </Table.Th>
                    {procedures.map((procedure) => (
                      <Table.Th key={procedure} style={{ minWidth: 120, textAlign: 'right' }}>
                        {procedure}
                      </Table.Th>
                    ))}
                    <Table.Th style={{ minWidth: 120, textAlign: 'right', fontWeight: 'bold' }}>
                      Total
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {months.map((month) => (
                    <React.Fragment key={month}>
                      {/* Ligne du mois */}
                      <Table.Tr style={{ backgroundColor: '#f8f9fa' }}>
                        <Table.Td
                          style={{
                            fontWeight: 'bold',
                            position: 'sticky',
                            left: 0,
                            backgroundColor: '#f8f9fa',
                            zIndex: 1
                          }}
                        >
                          {month}
                        </Table.Td>
                        {procedures.map((procedure) => (
                          <Table.Td key={procedure} style={{ textAlign: 'right' }}>
                            {currentPivotData[month]?.[procedure] ?
                              formatCurrency(currentPivotData[month][procedure]) :
                              ''
                            }
                          </Table.Td>
                        ))}
                        <Table.Td style={{ textAlign: 'right', fontWeight: 'bold' }}>
                          {formatCurrency(calculateTotal(currentPivotData[month] || {}))}
                        </Table.Td>
                      </Table.Tr>

                      {/* Ligne du médecin (sous-total) */}
                      <Table.Tr style={{ backgroundColor: '#e9ecef' }}>
                        <Table.Td
                          style={{
                            paddingLeft: 24,
                            fontStyle: 'italic',
                            position: 'sticky',
                            left: 0,
                            backgroundColor: '#e9ecef',
                            zIndex: 1
                          }}
                        >
                          DEMO DEMO
                        </Table.Td>
                        {procedures.map((procedure) => (
                          <Table.Td key={procedure} style={{ textAlign: 'right' }}>
                            {currentPivotData[month]?.[procedure] ?
                              formatCurrency(currentPivotData[month][procedure]) :
                              ''
                            }
                          </Table.Td>
                        ))}
                        <Table.Td style={{ textAlign: 'right', fontWeight: 'bold' }}>
                          {formatCurrency(calculateTotal(currentPivotData[month] || {}))}
                        </Table.Td>
                      </Table.Tr>
                    </React.Fragment>
                  ))}

                  {/* Ligne de total général */}
                  <Table.Tr style={{ backgroundColor: '#1976d2', color: 'white' }}>
                    <Table.Td
                      style={{
                        fontWeight: 'bold',
                        color: 'white',
                        position: 'sticky',
                        left: 0,
                        backgroundColor: '#1976d2',
                        zIndex: 1
                      }}
                    >
                      Total
                    </Table.Td>
                    {procedures.map((procedure) => {
                      const total = months.reduce((sum, month) => {
                        return sum + (currentPivotData[month]?.[procedure] || 0);
                      }, 0);
                      return (
                        <Table.Td key={procedure} style={{ textAlign: 'right', color: 'white', fontWeight: 'bold' }}>
                          {total > 0 ? formatCurrency(total) : ''}
                        </Table.Td>
                      );
                    })}
                    <Table.Td style={{ textAlign: 'right', fontWeight: 'bold', color: 'white' }}>
                      {formatCurrency(
                        months.reduce((sum, month) => sum + calculateTotal(currentPivotData[month] || {}), 0)
                      )}
                    </Table.Td>
                  </Table.Tr>
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Box>
        )}
      </Box>
    </Paper>
  );
};
