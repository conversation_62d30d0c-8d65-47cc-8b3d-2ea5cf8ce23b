'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { EtatsDuPatient } from './Etats_du_patient';

export default function EtatsDuPatientDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query changée:', query);
    const startDate = new Date(query.start).toLocaleDateString('fr-FR');
    const endDate = new Date(query.end).toLocaleDateString('fr-FR');
    const patientName = query.patient?.full_name || 'Aucun patient';
    console.log(`Patient: ${patientName}, Période: du ${startDate} au ${endDate}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
    alert(`Source de données sélectionnée: ${state.label}`);
  };

  const handlePatientSelect = (patient: any) => {
    console.log('Patient sélectionné:', patient);
    if (patient) {
      alert(`Patient sélectionné: ${patient.full_name}`);
    } else {
      alert('Aucun patient sélectionné');
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EtatsDuPatient
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onPatientSelect={handlePatientSelect}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function EtatsDuPatientWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec données:', query);
    // Simuler le chargement de données
    setTimeout(() => {
      console.log('Données des états du patient chargées pour:', query);
    }, 1000);
  };

  const handleStateChange = (state: any) => {
    console.log('Changement d\'état du patient:', state);
    
    const stateMessages: { [key: string]: string } = {
      'general_account': 'Affichage de l\'état du compte général',
      'procedures': 'Affichage des actes médicaux',
      'dental_procedures': 'Affichage des actes dentaires',
      'medical_procedures': 'Affichage des actes de soins',
      'encasements': 'Affichage des encaissements',
      'payments': 'Affichage des paiements'
    };
    
    const message = stateMessages[state.name] || 'État sélectionné';
    alert(message);
  };

  const handlePatientSelect = (patient: any) => {
    console.log('Patient sélectionné avec données:', patient);
    if (patient) {
      alert(`Chargement des états pour: ${patient.full_name}\nTéléphone: ${patient.phone}\nEmail: ${patient.email}`);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EtatsDuPatient
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onPatientSelect={handlePatientSelect}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec validation
export function EtatsDuPatientValidationDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query avec validation:', query);
    
    // Simuler une validation de période
    const startDate = new Date(query.start);
    const endDate = new Date(query.end);
    
    if (endDate < startDate) {
      alert('Erreur: La date de fin ne peut pas être antérieure à la date de début');
      return;
    }
    
    // Simuler une période trop longue
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      alert('Attention: La période sélectionnée est très longue (plus d\'un an). Cela peut affecter les performances.');
    }
    
    if (query.patient) {
      console.log('Période validée, chargement des états du patient...');
    }
  };

  const handleStateChange = (state: any) => {
    console.log('État avec validation:', state);
    
    // Simuler des restrictions selon le type d'état
    if (state.deactivated) {
      alert('Cette source de données est temporairement désactivée');
      return;
    }
    
    console.log(`Source de données "${state.label}" activée`);
  };

  const handlePatientSelect = (patient: any) => {
    console.log('Patient avec validation:', patient);
    
    if (patient) {
      // Simuler une validation du patient
      if (!patient.phone && !patient.email) {
        alert('Attention: Ce patient n\'a pas d\'informations de contact');
      }
      
      console.log('Patient validé:', patient.full_name);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EtatsDuPatient
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onPatientSelect={handlePatientSelect}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des états spécialisés
export function EtatsDuPatientSpecializedDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query spécialisée:', query);
    
    if (query.patient) {
      const startDate = new Date(query.start).toLocaleDateString('fr-FR');
      const endDate = new Date(query.end).toLocaleDateString('fr-FR');
      alert(`Génération du rapport pour ${query.patient.full_name} du ${startDate} au ${endDate}`);
    }
  };

  const handleStateChange = (state: any) => {
    console.log('État spécialisé:', state);
    
    // Simuler des actions spécifiques selon le type
    switch (state.type) {
      case 'general':
        alert('Mode Compte Général: Affichage du solde et des transactions');
        break;
      case 'procedure':
        alert('Mode Actes: Affichage des procédures médicales');
        break;
      case 'dental':
        alert('Mode Dentaire: Affichage des soins dentaires');
        break;
      case 'medical':
        alert('Mode Médical: Affichage des soins médicaux');
        break;
      case 'encasement':
        alert('Mode Encaissements: Affichage des paiements reçus');
        break;
      case 'payment':
        alert('Mode Paiements: Affichage des paiements effectués');
        break;
      default:
        alert('Mode sélectionné: ' + state.label);
    }
  };

  const handlePatientSelect = (patient: any) => {
    console.log('Patient spécialisé:', patient);
    
    if (patient) {
      // Simuler le chargement de données spécialisées
      setTimeout(() => {
        alert(`Données spécialisées chargées pour ${patient.full_name}:\n- Historique médical\n- Traitements en cours\n- Facturation\n- Rendez-vous`);
      }, 500);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EtatsDuPatient
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onPatientSelect={handlePatientSelect}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec recherche avancée de patients
export function EtatsDuPatientAdvancedSearchDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Query recherche avancée:', query);
    
    if (query.patient) {
      console.log('Recherche avancée pour:', query.patient.full_name);
      // Simuler une recherche dans différentes bases de données
      alert(`Recherche avancée initiée pour ${query.patient.full_name}:\n- Base de données principale\n- Archives\n- Système de facturation\n- Dossier médical`);
    }
  };

  const handleStateChange = (state: any) => {
    console.log('État recherche avancée:', state);
    alert(`Recherche configurée pour: ${state.label}`);
  };

  const handlePatientSelect = (patient: any) => {
    console.log('Patient recherche avancée:', patient);
    
    if (patient) {
      // Simuler une recherche approfondie
      const searchResults = {
        consultations: Math.floor(Math.random() * 50) + 1,
        traitements: Math.floor(Math.random() * 20) + 1,
        factures: Math.floor(Math.random() * 30) + 1,
        paiements: Math.floor(Math.random() * 25) + 1
      };
      
      alert(`Résultats de recherche pour ${patient.full_name}:\n` +
            `- ${searchResults.consultations} consultations\n` +
            `- ${searchResults.traitements} traitements\n` +
            `- ${searchResults.factures} factures\n` +
            `- ${searchResults.paiements} paiements`);
    }
  };

  return (
    <MantineProvider>
      <DatesProvider settings={{ locale: 'fr' }}>
        <div style={{ height: '100vh' }}>
          <EtatsDuPatient
            onQueryChange={handleQueryChange}
            onStateChange={handleStateChange}
            onPatientSelect={handlePatientSelect}
          />
        </div>
      </DatesProvider>
    </MantineProvider>
  );
}
