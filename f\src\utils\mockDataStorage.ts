/**
 * Utility for persistent data storage using localStorage
 * This is used for both mock data and real data caching
 */

// Define storage keys
const STORAGE_KEYS = {
  // Mock data keys
  ASSISTANT_ACCOUNTS: 'mock_assistant_accounts',
  SUBSCRIPTION_PACKAGES: 'mock_subscription_packages',
  USER_COUNTS: 'mock_user_counts',

  // Real data cache keys
  REAL_USER_ACCOUNTS: 'real_user_accounts',
  REAL_SUBSCRIPTION_PACKAGES: 'real_subscription_packages',
  REAL_USER_COUNTS: 'real_user_counts',
  REAL_CURRENT_SUBSCRIPTION: 'real_current_subscription',

  // Cache timestamp keys
  CACHE_TIMESTAMP_USER_ACCOUNTS: 'cache_timestamp_user_accounts',
  CACHE_TIMESTAMP_SUBSCRIPTION_PACKAGES: 'cache_timestamp_subscription_packages',
  CACHE_TIMESTAMP_USER_COUNTS: 'cache_timestamp_user_counts',
  CACHE_TIMESTAMP_CURRENT_SUBSCRIPTION: 'cache_timestamp_current_subscription',
};

/**
 * Get data from localStorage with fallback to default value
 */
export function getStoredData<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue;
  }

  try {
    const storedData = localStorage.getItem(key);
    if (!storedData) {
      return defaultValue;
    }

    return JSON.parse(storedData) as T;
  } catch (error) {
    console.error(`Error retrieving data from localStorage for key ${key}:`, error);
    return defaultValue;
  }
}

/**
 * Store data in localStorage
 */
export function storeData<T>(key: string, data: T): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error storing data in localStorage for key ${key}:`, error);
  }
}

/**
 * Clear stored data for a specific key
 */
export function clearStoredData(key: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error clearing data from localStorage for key ${key}:`, error);
  }
}

/**
 * Check if cached data is still valid (less than 5 minutes old)
 */
export function isCacheValid(timestampKey: string, maxAgeMinutes: number = 5): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const timestamp = localStorage.getItem(timestampKey);
    if (!timestamp) {
      return false;
    }

    const storedTime = parseInt(timestamp, 10);
    const currentTime = Date.now();
    const ageInMinutes = (currentTime - storedTime) / (1000 * 60);

    return ageInMinutes < maxAgeMinutes;
  } catch (error) {
    console.error(`Error checking cache validity for key ${timestampKey}:`, error);
    return false;
  }
}

/**
 * Store data in cache with timestamp
 */
export function storeInCache<T>(dataKey: string, timestampKey: string, data: T): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Store the data
    localStorage.setItem(dataKey, JSON.stringify(data));

    // Store the timestamp
    localStorage.setItem(timestampKey, Date.now().toString());
  } catch (error) {
    console.error(`Error storing data in cache for key ${dataKey}:`, error);
  }
}

/**
 * Get data from cache if valid, otherwise return null
 */
export function getFromCache<T>(dataKey: string, timestampKey: string, maxAgeMinutes: number = 5): T | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Check if cache is valid
    if (!isCacheValid(timestampKey, maxAgeMinutes)) {
      return null;
    }

    // Get the data
    const data = localStorage.getItem(dataKey);
    if (!data) {
      return null;
    }

    return JSON.parse(data) as T;
  } catch (error) {
    console.error(`Error retrieving data from cache for key ${dataKey}:`, error);
    return null;
  }
}

/**
 * Clear all cache data
 */
export function clearCache(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear all real data cache keys
    Object.keys(STORAGE_KEYS).forEach(key => {
      if (key.startsWith('REAL_') || key.startsWith('CACHE_TIMESTAMP_')) {
        localStorage.removeItem(STORAGE_KEYS[key as keyof typeof STORAGE_KEYS]);
      }
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

export { STORAGE_KEYS };
