'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Title,
  Card,
  Grid,
  Button,
  TextInput,
  Select,
  Textarea,
  Tabs,
  Divider
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import Icon from '@mdi/react';
import {
  mdiAccount,
  mdiPhone,
  mdiEmail,
  mdiMapMarker,
  mdiStethoscope,
  mdiHeart,
  mdiIdCard,
  mdiMessageText,
  mdiContentSave,
  mdiCheck,
  mdiClose,
  mdiFormatListBulleted
} from '@mdi/js';

// Types et interfaces
interface NouveauPatientsProps {
  loading?: boolean;
  onSave?: (patientData: any) => void; // eslint-disable-line @typescript-eslint/no-explicit-any
  onCancel?: () => void;
}

export const NouveauPatients: React.FC<NouveauPatientsProps> = ({
  loading: externalLoading = false, // eslint-disable-line @typescript-eslint/no-unused-vars
  onSave,
  onCancel
}) => {
  // États locaux
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('personnel');

  // Formulaire
  const form = useForm({
    initialValues: {
      numero: 'P' + Date.now().toString().slice(-6),
      nom: '',
      prenom: '',
      dateNaissance: null,
      lieuNaissance: '',
      sexe: '',
      situationFamiliale: '',
      profession: '',
      nationalite: '',
      numeroSecuriteSociale: '',
      telephone: '',
      email: '',
      adresse: '',
      ville: '',
      codePostal: '',
      pays: '',
      groupeSanguin: '',
      medecinTraitant: '',
      allergies: '',
      maladiesChroniques: '',
      medicamentsActuels: '',
      contactUrgenceNom: '',
      contactUrgenceRelation: '',
      contactUrgenceTelephone: '',
      mutuelle: '',
      numeroMutuelle: '',
      commentaires: ''
    },
    validate: {
      nom: (value) => (value.length < 2 ? 'Le nom doit contenir au moins 2 caractères' : null),
      prenom: (value) => (value.length < 2 ? 'Le prénom doit contenir au moins 2 caractères' : null),
      dateNaissance: (value) => (!value ? 'La date de naissance est requise' : null),
      sexe: (value) => (!value ? 'Le sexe est requis' : null),
      telephone: (value) => (!value ? 'Le téléphone est requis' : null),
      email: (value) => (value && !/^\S+@\S+$/.test(value) ? 'Email invalide' : null),
    },
  });

  // Options pour les selects
  const sexeOptions = [
    { value: 'M', label: 'Masculin' },
    { value: 'F', label: 'Féminin' }
  ];

  const situationFamilialeOptions = [
    { value: 'celibataire', label: 'Célibataire' },
    { value: 'marie', label: 'Marié(e)' },
    { value: 'divorce', label: 'Divorcé(e)' },
    { value: 'veuf', label: 'Veuf/Veuve' },
    { value: 'concubinage', label: 'Concubinage' }
  ];

  const nationaliteOptions = [
    { value: 'marocaine', label: 'Marocaine' },
    { value: 'francaise', label: 'Française' },
    { value: 'espagnole', label: 'Espagnole' },
    { value: 'autre', label: 'Autre' }
  ];

  const groupeSanguinOptions = [
    { value: 'A+', label: 'A+' },
    { value: 'A-', label: 'A-' },
    { value: 'B+', label: 'B+' },
    { value: 'B-', label: 'B-' },
    { value: 'AB+', label: 'AB+' },
    { value: 'AB-', label: 'AB-' },
    { value: 'O+', label: 'O+' },
    { value: 'O-', label: 'O-' }
  ];



  // Fonctions de gestion des boutons
  const handleSave = async () => {
    try {
      setLoading(true);

      // Appel de la fonction de sauvegarde fournie par le parent
      await onSave?.(form.values);

      notifications.show({
        title: 'Patient sauvegardé',
        message: 'Les informations du patient ont été sauvegardées.',
        color: 'green',
        icon: <Icon path={mdiContentSave} size={0.8} />,
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de sauvegarder les informations du patient.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleValidate = async () => {
    const validation = form.validate();
    if (validation.hasErrors) {
      notifications.show({
        title: 'Erreur de validation',
        message: 'Veuillez corriger les erreurs dans le formulaire.',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);
      await handleSave();

      notifications.show({
        title: 'Patient validé',
        message: 'Le dossier patient a été validé et enregistré.',
        color: 'green',
        icon: <Icon path={mdiCheck} size={0.8} />,
      });
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de valider le dossier patient.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    onCancel?.();
    notifications.show({
      title: 'Formulaire réinitialisé',
      message: 'Les modifications ont été annulées.',
      color: 'red',
      icon: <Icon path={mdiClose} size={0.8} />,
    });
  };

  return (
    <Paper p="xl" radius="md" withBorder w="100%">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group align="center">
          <Icon path={mdiAccount} size={1.2} color="blue" />
          <Title order={3} c="blue">
            Nouveau Patient N°: {form.values.numero}
          </Title>
        </Group>
        <Group>
          <Button leftSection={<Icon path={mdiFormatListBulleted} size={0.8} />} variant="outline">
            Liste des patients
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          {/* Basic Information */}
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Patient"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
                disabled
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Nom"
                placeholder="Nom de famille"
                {...form.getInputProps('nom')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Prénom"
                placeholder="Prénom"
                {...form.getInputProps('prenom')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date de naissance"
                placeholder="Sélectionner une date"
                {...form.getInputProps('dateNaissance')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="Lieu de naissance"
                placeholder="Ville de naissance"
                {...form.getInputProps('lieuNaissance')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Sexe"
                placeholder="Sélectionner"
                data={sexeOptions}
                {...form.getInputProps('sexe')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Situation familiale"
                placeholder="Sélectionner"
                data={situationFamilialeOptions}
                {...form.getInputProps('situationFamiliale')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Profession"
                placeholder="Profession"
                {...form.getInputProps('profession')}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Nationalité"
                placeholder="Sélectionner"
                data={nationaliteOptions}
                {...form.getInputProps('nationalite')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="N° Sécurité Sociale"
                placeholder="Numéro de sécurité sociale"
                {...form.getInputProps('numeroSecuriteSociale')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'personnel')}>
            <Tabs.List>
              <Tabs.Tab value="personnel" leftSection={<Icon path={mdiAccount} size={0.8} />}>
                Informations personnelles
              </Tabs.Tab>
              <Tabs.Tab value="contact" leftSection={<Icon path={mdiPhone} size={0.8} />}>
                Contact
              </Tabs.Tab>
              <Tabs.Tab value="medical" leftSection={<Icon path={mdiStethoscope} size={0.8} />}>
                Informations médicales
              </Tabs.Tab>
              <Tabs.Tab value="urgence" leftSection={<Icon path={mdiHeart} size={0.8} />}>
                Contact d&apos;urgence
              </Tabs.Tab>
              <Tabs.Tab value="administratif" leftSection={<Icon path={mdiIdCard} size={0.8} />}>
                Administratif
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<Icon path={mdiMessageText} size={0.8} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="personnel" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone"
                    placeholder="Numéro de téléphone"
                    leftSection={<Icon path={mdiPhone} size={0.8} />}
                    {...form.getInputProps('telephone')}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Email"
                    placeholder="Adresse email"
                    leftSection={<Icon path={mdiEmail} size={0.8} />}
                    {...form.getInputProps('email')}
                    type="email"
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Adresse"
                    placeholder="Adresse complète"
                    rows={3}
                    {...form.getInputProps('adresse')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Ville"
                    placeholder="Ville"
                    {...form.getInputProps('ville')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Code postal"
                    placeholder="Code postal"
                    {...form.getInputProps('codePostal')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Pays"
                    placeholder="Pays"
                    {...form.getInputProps('pays')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="contact" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone principal"
                    placeholder="Numéro de téléphone"
                    leftSection={<Icon path={mdiPhone} size={0.8} />}
                    {...form.getInputProps('telephone')}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Email"
                    placeholder="Adresse email"
                    leftSection={<Icon path={mdiEmail} size={0.8} />}
                    {...form.getInputProps('email')}
                    type="email"
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Adresse complète"
                    placeholder="Adresse, ville, code postal, pays"
                    rows={4}
                    leftSection={<Icon path={mdiMapMarker} size={0.8} />}
                    {...form.getInputProps('adresse')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="medical" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <Select
                    label="Groupe sanguin"
                    placeholder="Sélectionner"
                    data={groupeSanguinOptions}
                    {...form.getInputProps('groupeSanguin')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Médecin traitant"
                    placeholder="Nom du médecin traitant"
                    {...form.getInputProps('medecinTraitant')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Allergies"
                    placeholder="Allergies connues"
                    rows={3}
                    {...form.getInputProps('allergies')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Maladies chroniques"
                    placeholder="Maladies chroniques"
                    rows={3}
                    {...form.getInputProps('maladiesChroniques')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Médicaments actuels"
                    placeholder="Médicaments en cours"
                    rows={3}
                    {...form.getInputProps('medicamentsActuels')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="urgence" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Nom du contact"
                    placeholder="Nom et prénom"
                    {...form.getInputProps('contactUrgenceNom')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Relation"
                    placeholder="Relation avec le patient"
                    {...form.getInputProps('contactUrgenceRelation')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone d'urgence"
                    placeholder="Numéro de téléphone"
                    leftSection={<Icon path={mdiPhone} size={0.8} />}
                    {...form.getInputProps('contactUrgenceTelephone')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="administratif" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Mutuelle"
                    placeholder="Nom de la mutuelle"
                    {...form.getInputProps('mutuelle')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="N° Mutuelle"
                    placeholder="Numéro de mutuelle"
                    {...form.getInputProps('numeroMutuelle')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaires"
                placeholder="Commentaires généraux sur le patient..."
                rows={6}
                {...form.getInputProps('commentaires')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Action Buttons */}
          <Group justify="space-between">
            <Group>
              <Button
                leftSection={<Icon path={mdiContentSave} size={0.8} />}
                color="blue"
                onClick={handleSave}
                loading={loading}
              >
                Sauvegarder
              </Button>
              <Button
                leftSection={<Icon path={mdiCheck} size={0.8} />}
                color="green"
                onClick={handleValidate}
                loading={loading}
              >
                Valider
              </Button>
            </Group>
            <Group>
              <Button
                leftSection={<Icon path={mdiClose} size={0.8} />}
                color="red"
                variant="outline"
                onClick={handleCancel}
              >
                Annuler
              </Button>
            </Group>
          </Group>
        </form>
      </Card>
    </Paper>
  );
};

export default NouveauPatients;
