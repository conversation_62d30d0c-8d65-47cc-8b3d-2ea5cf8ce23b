'use client';
import { useForm } from '@mantine/form';
import { useState } from 'react';
import { IconPalette } from '@tabler/icons-react';


import { 
    Button,  Select, Group, Title, ActionIcon, Stack, Paper, Grid,
    Modal, TextInput, ColorInput, MultiSelect, Text,Switch,ColorPicker,
    NumberInput,
    Textarea,
    Box,
    Radio,
    Divider,
  } from '@mantine/core';
import Icon from '@mdi/react';
import {  mdiRefresh, mdiPlus, mdiCalendarEdit ,
  mdiPlaylistPlus,mdiClose
 } from '@mdi/js';
interface AgendaForm {
    name: string;
    color: string;
    description: string;
    services: string[];
  }
interface DefaultSettings {
  physician: string;
  agenda: string;
  entry: {
    reason: string;
    waiting_room: string;
    consultation_room: string;
  };
  payment: {
    type: string;
    bank: string;
  };
}

interface ActesFormValues {
  name: string;
  color: string;
  description: string;
  services: string[];
  useAsResource: boolean;
}

const reasons = [
    "1er Consultation", "Changement D'élastique", "Chirurgie/Paro", "Collage",
    "Composite", "Consultation", "Contention", "Contrôle", "Depose Sutures",
    "Detartrage", "Devis", "Echographie", "Endodontie", "Formation",
    "Implantologie", "Obturation Canalaire", "Orthodontie", "PA ES Chassis/Monta",
    "PA Pose", "PC ESS Armature", "PC Scellement", "PC TP EMP", "Prophylaxie",
    "Urgent", "polissage"
  ].map(value => ({ value, label: value }));
  const banks = [
    "Aucune", "BCP", "BMCI", "BMCE", "AWB", "SGMB", "CDM", "BAM", "ABB", "ABM",
    "BAA", "CDG", "CFG", "CITI", "CIH", "FEC", "MFC", "UMB", "BANCO", "CAIXA",
    "UMNIA", "BTI", "BAY", "ASSAFA", "AAB", "CIB", "UPL", "HLD", "CAT"
  ].map(value => ({ value, label: value }));
  
  const paymentTypes = ["Aucune", "Chèque", "Espèce", "Traite"]
    .map(value => ({ value, label: value }));
    
const defaultPhysicians = [{ value: 'Dr. DEMO DEMO', label: 'Dr. DEMO DEMO' }];
const defaultAgendas = [{ value: 'Cabinet', label: 'Cabinet' }];
const rooms = [
  { value: 'SLT', label: 'SLT' },
  { value: 'FTL', label: 'FTL' }
];


  



export default function General() {
   
    const [isAgendaModalOpen, setIsAgendaModalOpen] = useState(false);
    const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
    const [isMotifModalOpen, setIsMotifModalOpen] = useState(false);
    const [isSltModalOpen, setIsSltModalOpen] = useState(false);
    const [ispaiementModalOpen, setIspaiementModalOpen] = useState(false);
    const [isFtlModalOpen, setIsFtlModalOpen] = useState(false);
    const [isBanqueModalOpen, setIsBanqueModalOpen] = useState(false);
    const [isActesModalOpen, setIsActesModalOpen] = useState(false);
    
    
    const [agendaForm, setAgendaForm] = useState<AgendaForm>({
      name: '',
      color: '#000000',
      description: '',
      services: []
    });
  const [settings, setSettings] = useState<DefaultSettings>({
    physician: 'Dr. DEMO DEMO',
    agenda: 'Cabinet',
    entry: {
      reason: 'Consultation',
      waiting_room: 'SLT',
      consultation_room: 'FTL'
    },
    payment: {
      type: 'Espèce',
      bank: 'Aucune'
    }
  });
  const handleSettingChange = (section: keyof DefaultSettings | 'entry' | 'payment', field: string, value: string) => {
    setSettings(prev => {
      if (section === 'entry' || section === 'payment') {
        return {
          ...prev,
          [section]: {
            ...prev[section],
            [field]: value
          }
        };
      }
      return {
        ...prev,
        [section]: value
      };
    });
  };
  const handleAgendaSubmit = () => {
    // Handle form submission here
    setIsAgendaModalOpen(false);
  };
 

  const handleColorChange = (color: string) => {
    setAgendaForm(prev => ({ ...prev, color }));
  };
  const RoomFormValues = useForm<RoomFormValues>({
    initialValues: {
      name: '',
      capacity: 0,
      color: '',
      description: '',
      services: [],
      type: 'WR',
    },

    validate: {
      name: (value) => (value ? null : 'Nom requis'),
      capacity: (value) => (value >= 0 ? null : 'Capacité invalide'),
      color: (value) => (!value ? 'Couleur requise' : null),
    },
  });

  const [isSubmitting, ] = useState(false);

 

  // ----------------
  interface PaymentFormValues {
    value: string;
    description: string;
  }
  const paymentForm  = useForm<PaymentFormValues>({
    initialValues: {
      value: '',
      description: '',
    },
    validate: {
      value: (v) => (v.trim().length > 0 ? null : 'Valeur requise'),
    },
  });

  const [loading, ] = useState(false);
  const [, setSubmittingPayment] = useState(false);
  const handlePaymentSubmit = (values: PaymentFormValues) => {
    setSubmittingPayment(true);
    console.log('Payment form submitted:', values);
    setTimeout(() => setSubmittingPayment(false), 1000);
  };
  // ------------------------------
  

 
  interface RoomFormValues {
    name: string;
    capacity: number;
    color: string;
    description: string;
    services: string[];
    type: 'WR' | 'CR';
  }
  
  const mockServiceOptions = [
    { label: 'Radiologie', value: 'radiologie' },
    { label: 'Cardiologie', value: 'cardiologie' },
    { label: 'Pédiatrie', value: 'pediatrie' },
  ];

  // ---------------------------
  interface DefaultBankFormValues {
    value: string;
    description: string;
  }
  const BankForm = useForm<DefaultBankFormValues>({
    initialValues: {
      value: '',
      description: '',
    },
    validate: {
      value: (v) => (v.trim().length > 0 ? null : 'La valeur est requise'),
    },
  });
  const [, setSubmittingBank] = useState(false);

const handleBankSubmit = (values: DefaultBankFormValues) => {
  setSubmittingBank(true);
  console.log('Room form submitted:', values);
  setTimeout(() => setSubmittingBank(false), 1000);
};
const [, setSubmittingActes] = useState(false);
const handleActesSubmit = (values: ActesFormValues) => {
  setSubmittingActes(true);
  console.log('Room form submitted:', values);
  setTimeout(() => setSubmittingBank(false), 1000);
};
const formActes = useForm<ActesFormValues>({
  initialValues: {
    name: '',
    color: '',
    description: '',
    services: [],
    useAsResource: true
  },
  validate: {
    name: (value) => (value.trim().length > 0 ? null : 'Name is required'),
    color: (value) => (value.trim().length > 0 ? null : 'Color is required'),
  }
});

// Le type de tes valeurs du formulaire
interface SalleFormValues {
  name: string;
}

// Définition du formulaire
const form = useForm<SalleFormValues>({
  initialValues: {
    name: '',
  },
  validate: {
    name: (value) => (value.trim() ? null : 'Le nom est requis'),
  },
});

// Fonction de soumission
const handleSubmit = (values: SalleFormValues) => {
  console.log('Salle à enregistrer:', values);
  // Appelle ici ta logique d’enregistrement
};


;
  return (
    <Paper p="md" w={"100%"} mb={30}>
      
          <Grid>
            <Grid.Col span={4}>
              <Select
                label="Médecin par défaut"
                data={defaultPhysicians}
                value={settings.physician}
                onChange={(value) => handleSettingChange('physician', '', value || '')}
                searchable
                maxDropdownHeight={280}
                clearable
                placeholder="Rechercher..."
              />
            </Grid.Col>
            <Grid.Col span={12}>
              <Group grow>
              <Group>
              <Select
                w={"80%"}
                label="Agenda par défaut"
                data={defaultAgendas}
                value={settings.agenda}
                onChange={(value) => handleSettingChange('agenda', '', value || '')}
                searchable
                maxDropdownHeight={280}
                clearable
                placeholder="Rechercher..."
              />
            <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsAgendaModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
            </div>
            </Group>
            <Group>
                <Select
                 label="Salle d'attente"
                 data={rooms}
                 searchable
                 placeholder="Rechercher..."
                 maxDropdownHeight={280}
                 clearable
                 value={settings.entry.waiting_room}
                 onChange={(value) => handleSettingChange('entry', 'waiting_room', value || '')}
                 w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsSltModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
                <Group>
                <Select
                  label="Salle de consultation"
                  data={rooms}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.entry.consultation_room}
                  onChange={(value) => handleSettingChange('entry', 'consultation_room', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsFtlModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
              </Group>
            </Grid.Col>
     
            <Grid.Col span={12}>
              <Group grow>
              <Group>
                <Select
                  label="Motif par défaut"
                  data={reasons}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.entry.reason}
                  onChange={(value) => handleSettingChange('entry', 'reason', value || '')}
                  w={"80%"}
                />
                <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsMotifModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
            </Group>
            <Group>
                <Select
                  label="Mode de paiement par défaut"
                  data={paymentTypes}
                  searchable
            placeholder="Rechercher..."
                  maxDropdownHeight={280}
                  clearable
                  value={settings.payment.type}
                  onChange={(value) => handleSettingChange('payment', 'type', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIspaiementModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
                <Group>
                <Select
                  label="Banque par défaut"
                  data={banks}
                  searchable
                  maxDropdownHeight={280}
                  clearable
                  placeholder="Rechercher..."
                  value={settings.payment.bank}
                  onChange={(value) => handleSettingChange('payment', 'bank', value || '')}
                  w={"80%"}
                />
                 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsBanqueModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
                </div>
                </Group>
              </Group>
            </Grid.Col>
          </Grid>
       

      <Modal
        opened={isAgendaModalOpen}
        onClose={() => setIsAgendaModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiCalendarEdit} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      >
        <form onSubmit={(e) => { e.preventDefault(); handleAgendaSubmit(); }}>
          <Stack  gap="md">
            <TextInput
              required
              label="Nom"
              value={agendaForm.name}
              onChange={(e) => setAgendaForm({ ...agendaForm, name: e.target.value })}
            />
           <Group>
            <ColorInput
                label="Couleur"
                value={agendaForm.color}
                onChange={(color) => setAgendaForm({ ...agendaForm, color })}
                w={"80%"}
            />
            <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                onClick={() => setIsColorPickerModalOpen(true)}>
                <IconPalette stroke={2} />
            </ActionIcon>
        </Group>
      
            <TextInput
              label="Description"
              value={agendaForm.description}
              onChange={(e) => setAgendaForm({ ...agendaForm, description: e.target.value })}
            />
            <MultiSelect
              label="Services désignés"
              data={[]} // Add your services data here
              value={agendaForm.services}
              onChange={(values) => setAgendaForm({ ...agendaForm, services: values })}
              searchable
              clearable
            />
             <Group justify="flex-start">
            <Switch
                defaultChecked
                label="Afficher comme ressource"
                size="xs"
                />
            </Group>
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsAgendaModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
      <Modal  
            opened={isColorPickerModalOpen}
            onClose={() => setIsColorPickerModalOpen(false)} 
            transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
            withCloseButton={false}
            centered
        >
            <ColorPicker 
                format="hex" 
                value={agendaForm.color}
                onChange={handleColorChange}
                swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
            />
      </Modal>  
      <Modal opened={isMotifModalOpen}
        onClose={() => setIsMotifModalOpen(false)} withCloseButton={false}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlus} size={1} />
            <Text>Actes</Text>
          </Group>
        }
        >
        <form onSubmit={(e) => { e.preventDefault(); /* handle submit */ }}>
          <Stack gap="md">
            <Group grow>
              <TextInput
                required
                label="Code"
                maxLength={3}
                placeholder="Enter code"
                w="30%"
              />
              <TextInput
                required
                label="Déscription"
                placeholder="Enter description"
              />
            </Group>

            <Group grow>
              <TextInput
                type="number"
                label="Durée (min)"
                required
                min={0}
                w="30%"
                placeholder="Enter duration"
              />
              <ColorInput
                label="Couleur"
                placeholder="Pick a color"
              />
              <ColorInput
                label="Couleur rayée"
                placeholder="Pick a color"
              />
            </Group>

            <Divider my="sm" />
            <Group>
            <Select
              label="Agenda par défaut"
              data={defaultAgendas}
              placeholder="Select agenda"
            />
 <div style={{marginTop:"26px"}}>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            mr={8}
            onClick={() => setIsActesModalOpen(true)}
            >
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon  size="input-sm" variant="default" 
            aria-label="ActionIcon the same size as inputs"
            //onClick={() => ()}
            >
            <Icon path={ mdiRefresh} size={1} />
            </ActionIcon>
            </div>
            </Group>
            <Group justify="flex-end" mt="xl">
              <Button type="submit">
                Sauvegarder
              </Button>
              <Button 
                color="red" 
                onClick={() => setIsMotifModalOpen(false)}
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
      <Modal
      opened={isActesModalOpen}
      onClose={() => setIsActesModalOpen(false)}
      centered
      title={
        <Group gap="xs">
          <Icon path={mdiCalendarEdit} size={1} />
          <span>Agenda</span>
        </Group>
      }
      size="lg"
    >
      <form onSubmit={formActes.onSubmit(handleActesSubmit)}>
        <Stack gap="md">
          <Group grow>
            <TextInput
              label="Nom"
              required
              {...formActes.getInputProps('name')}
            />
            <ColorInput
              label="Couleur"
              placeholder="Choisir une couleur"
              {...formActes.getInputProps('color')}
            />
          </Group>

          <Group grow>
            <TextInput
              label="Description"
              {...formActes.getInputProps('description')}
            />
            <MultiSelect
              label="Services désignés"
              placeholder="Rechercher..."
              searchable
              data={[
                { value: 'service1', label: 'Service 1' },
                { value: 'service2', label: 'Service 2' },
                { value: 'service3', label: 'Service 3' },
              ]}
              {...formActes.getInputProps('services')}
            />
          </Group>

          <Switch
            label="Afficher comme ressource"
            {...formActes.getInputProps('useAsResource', { type: 'checkbox' })}
          />

          <Group justify='flex-end' mt="md">
            <Button variant="default" onClick={() => setIsActesModalOpen(false)}>
              Annuler
            </Button>
            <Button type="submit">Enregistrer</Button>
          </Group>
        </Stack>
      </form>
    </Modal>
      <Modal opened={isSltModalOpen}
        onClose={() => setIsSltModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered>
         <Box component="form" onSubmit={form.onSubmit(handleSubmit)} maw={600} mx="auto">
      <Group mb="md" align="center" justify='center'>
        <Group>
          <Icon path={mdiPlaylistPlus} size={1} />
          <Title order={2}>Ajout du salle</Title>
        </Group>
        <Button variant="subtle" size="sm" onClick={() => setIsSltModalOpen(false)}>
          <Icon path={mdiClose} size={1} />
        </Button>
      </Group>

      <Stack>
        <TextInput
          label="Nom"
          required
          {...form.getInputProps('name')}
        />

        <NumberInput
          label="Capacité"
          required
          min={0}
          {...form.getInputProps('capacity')}
        />

        <ColorInput
          label="Couleur"
          required
          {...form.getInputProps('color')}
        />

        <Textarea
          label="Description"
          {...form.getInputProps('description')}
        />

        <Select
          label="Services désignés"
          data={[
            { value: 'service1', label: 'Service 1' },
            { value: 'service2', label: 'Service 2' },
            // Remplace par fetch API
          ]}
          multiple
          searchable
          placeholder="Rechercher..."
          {...form.getInputProps('services')}
        />

        <Radio.Group
          name="type"
          label="Type"
          required
          {...form.getInputProps('type')}
        >
          <Group mt="xs">
            <Radio value="WR" label="Salle d'attente" />
            <Radio value="CR" label="Salle de consultation" />
          </Group>
        </Radio.Group>
      </Stack>

      <Group mt="xl" justify='center'>
        <Button color="red" variant="outline" onClick={() => setIsSltModalOpen(false)} >
          Annuler
        </Button>
        <Button type="submit" loading={isSubmitting}>
          Enregistrer
        </Button>
      </Group>
    </Box>
     
      </Modal>
      <Modal opened={ispaiementModalOpen}
        onClose={() => setIspaiementModalOpen(false)} withCloseButton={false} transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered>
        <Box component="form" onSubmit={paymentForm.onSubmit(handlePaymentSubmit)} maw={500} mx="auto">
      <Group mb="md" align="center" justify='center'>
        <Group>
          <Icon path={mdiPlaylistPlus} size={1} />
          <Title order={2}>Ajouter mode de paiement par défaut</Title>
        </Group>
        <Button variant="subtle" size="sm" onClick={() => setIspaiementModalOpen(false)}>
          <Icon path={mdiClose} size={1} />
        </Button>
      </Group>

      <Stack>
        <TextInput
          label="Valeur"
          required
          withAsterisk
          {...paymentForm.getInputProps('value')}
        />

        <TextInput
          label="Description"
          {...paymentForm.getInputProps('description')}
        />
      </Stack>

      <Group mt="xl" justify="flex-end">
        <Button variant="outline" color="gray" onClick={() => setIspaiementModalOpen(false)}>
          Annuler
        </Button>
        <Button type="submit" loading={loading}>
          Enregistrer
        </Button>
      </Group>
    </Box>
        <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIspaiementModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
      </Modal>
      <Modal opened={isBanqueModalOpen}
        onClose={() => setIsBanqueModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
        title={
          <Group gap="xs">
            <Icon path={mdiPlaylistPlus} size={1} />
            <span>Ajouter Banque par défaut</span>
          </Group>
        }
        >
       <form onSubmit={BankForm.onSubmit(handleBankSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Valeur"
            required
            placeholder="Valeur"
            {...BankForm.getInputProps('value')}
          />

          <TextInput
            label="Description"
            placeholder="Description (optionnelle)"
            {...BankForm.getInputProps('description')}
          />

<Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsBanqueModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
        </Stack>
      </form>
       
      </Modal>
      <Modal opened={isFtlModalOpen}
        onClose={() => setIsFtlModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
        title={
          <Group gap="xs">
            <Icon path={mdiPlaylistPlus} size={1} />
            <span>Ajout du salle</span>
          </Group>
        }
        >
       <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Nom"
            placeholder="Nom de la salle"
            required
            {...form.getInputProps('name')}
          />

          <NumberInput
            label="Capacité"
            required
            min={0}
            {...form.getInputProps('capacity')}
          />

          <ColorInput
            label="Couleur"
            placeholder="Choisir une couleur"
            {...form.getInputProps('color')}
          />

          <TextInput
            label="Description"
            {...form.getInputProps('description')}
          />

          <Select
            label="Services désignés"
            placeholder="Sélectionner un ou plusieurs services"
            data={mockServiceOptions}
            multiple
            searchable
            {...form.getInputProps('services')}
          />

          <Radio.Group
            label="Type"
            {...form.getInputProps('type')}
            required
          >
            <Group>
              <Radio value="WR" label="Salle d'attente" />
              <Radio value="CR" label="Salle de consultation" />
            </Group>
          </Radio.Group>

          <Group justify="flex-end">
              <Button variant="filled" type="submit">
              Enregistrer
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsFtlModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
        </Stack>
      </form>
       
      </Modal>
    
    </Paper>
  );
}



