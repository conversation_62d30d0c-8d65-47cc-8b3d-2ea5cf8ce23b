/**
 * API Types for backend integration
 */

// Base API response types
export interface ApiResponse<T = unknown> {
  results?: T[];
  count?: number;
  next?: string | null;
  previous?: string | null;
  data?: T;
  message?: string;
  error?: string;
}

// API request parameters
export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

// API request data
export interface ApiData {
  [key: string]: unknown;
}

// Patient types
export interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  address: string;
  birth_date?: string;
  gender?: string;
  notes?: string;
  assigned_doctor?: string;
  age?: number;
  cin?: string;
  social_security?: string;
  user_type?: string;
  registration_date?: string;
  patient_status?: string;
}

// Appointment types
export interface Appointment {
  id: string;
  patient: string;
  doctor?: string;
  title: string;
  description?: string;
  appointment_type: string;
  status: string;
  appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  consultation_duration?: number;
  color?: string;
  event_type?: string;
  is_waiting_list: boolean;
  is_active: boolean;
  patient_phone?: string;
  patient_address?: string;
  consultation_type?: string;
  doctor_assigned?: string;
  agenda?: string;
  notes?: string;
  resource_id?: string;
  room?: string;
  created_at?: string;
  updated_at?: string;
  // Patient creation fields
  patient_title?: string;
  patient_first_name?: string;
  patient_last_name?: string;
  patient_email?: string;
  // Additional patient fields
  gender?: string;
  etat_civil?: string;
  cin?: string;
  social_security?: string;
  profession?: string;
  birth_place?: string;
  father_name?: string;
  mother_name?: string;
  blood_group?: string;
  allergies?: string;
}

// Doctor Pause types
export interface DoctorPause {
  id: string;
  doctor: string;
  doctor_name: string;
  title: string;
  date_from: string;
  date_to: string;
  duration_minutes: number;
  room?: string;
  resource_id?: string;
  color?: string;
  notes?: string;
  is_recurring: boolean;
  created_at?: string;
  updated_at?: string;
}

// Waiting List types
export interface WaitingListItem {
  id: string;
  first_name: string;
  last_name: string;
  title: string;
  phone_number: string;
  typeConsultation: string;
  eventType: string;
  duration: number;
  color: string;
  docteur: string;
  desc: string;
  address: string;
  notes: string;
  date: string;
  start: string;
  end: string;
  agenda: string;
  isActive: boolean;
  status: string;
  created_at?: string;
}

// Calendar Event types
export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  description: string;
  color: string;
  status: string;
  type: string;
  room: string;
  resourceId: string;
  patient_phone: string;
  doctor: string;
}

// API method types
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Generic API function type
export interface ApiFunction<TParams = unknown, TResponse = unknown> {
  (params?: TParams): Promise<TResponse>;
}

// Patient API response types
export interface PatientListResponse extends ApiResponse<Patient> {
  results: Patient[];
}

export interface PatientSearchResponse {
  results: Array<{
    id: string;
    name: string;
    email: string;
    phone: string;
    age: number;
    gender: string;
  }>;
}

export interface PatientStatsResponse {
  total_patients: number;
  new_this_week: number;
  new_this_month: number;
  by_status: Record<string, number>;
  by_gender: Record<string, number>;
}

// Appointment API response types
export interface AppointmentListResponse extends ApiResponse<Appointment> {
  results: Appointment[];
}

export interface CalendarEventsResponse {
  events: CalendarEvent[];
}

// Waiting List API response types
export interface WaitingListResponse {
  waiting_list: WaitingListItem[];
  count?: number;
  message?: string;
}

// Pause API response types
export interface PauseListResponse extends ApiResponse<DoctorPause> {
  results: DoctorPause[];
}

export interface DoctorPausesResponse {
  doctor: {
    id: string;
    name: string;
    email: string;
  };
  pauses: DoctorPause[];
}

// Success response types
export interface SuccessResponse {
  message: string;
}

export interface CreateResponse<T = unknown> extends SuccessResponse {
  id: string;
  data?: T;
}

// Error response types
export interface ErrorResponse {
  error: string;
  details?: Record<string, unknown>;
}

// Form data types for frontend
export interface PatientFormData {
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  address?: string;
  birth_date?: string;
  gender?: string;
  notes?: string;
  doctor_assigned?: string;
}

export interface AppointmentFormData {
  title: string;
  description?: string;
  appointment_type: string;
  appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  consultation_duration?: number;

  // Patient relationship
  patient?: string; // Patient ID if linking to existing patient

  // Patient creation fields
  patient_title?: string;
  patient_first_name?: string;
  patient_last_name?: string;
  patient_email?: string;
  patient_phone?: string;
  patient_landline?: string;
  landline_number?: string; // Backend field name for landline
  patient_address?: string;
  birth_date?: string;
  date_of_birth?: string; // Backend field name for birth date
  age?: number;

  // Appointment details
  consultation_type?: string;
  doctor?: string; // ForeignKey to doctor user ID
  doctor_assigned?: string; // Text field for doctor name
  agenda?: string;
  notes?: string;
  comment?: string; // Comment field
  commentaire?: string; // Alternative comment field name
  Commentairelistedattente?: string; // Waiting list comment field
  commentaire_liste_attente?: string; // Alternative waiting list comment field name
  agenda_type?: string; // Alternative agenda field name
  resource_id?: string;
  color?: string;
  event_type?: string;
  etat_aganda?: string; // Agenda status

  // Additional patient details
  gender?: string;
  etat_civil?: string;
  cin?: string;
  social_security?: string;
  profession?: string;
  birth_place?: string;
  father_name?: string;
  mother_name?: string;
  blood_group?: string;
  allergies?: string;
}

export interface PauseFormData {
  doctor: string;
  title: string;
  dateFrom: string;
  dateTo: string;
  room?: string;
  resource_id?: string;
  color?: string;
  notes?: string;
  is_recurring?: boolean;
}
