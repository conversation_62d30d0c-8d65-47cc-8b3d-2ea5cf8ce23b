'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Menu,
  Alert
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiAccountPlus,
  mdiFilterMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface NewPatientColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface NewPatientQuery {
  start: Date;
  end: Date;
}

interface NewPatientFilter {
  showAdvancedFilter: boolean;
}

interface NouveauPatientsProps {
  loading?: boolean;
  onQueryChange?: (query: NewPatientQuery) => void;
  onFilterChange?: (filter: NewPatientFilter) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const NouveauPatients: React.FC<NouveauPatientsProps> = ({
  loading = false,
  onQueryChange,
  onFilterChange,
  onExport,
  onPrint,
  onSort
}) => {
  // États locaux
  const [query, setQuery] = useState<NewPatientQuery>({
    start: new Date(),
    end: new Date()
  });

  const [filter, setFilter] = useState<NewPatientFilter>({
    showAdvancedFilter: true
  });

  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: NewPatientColumn[] = [
    { id: 'with_visit', label: 'Avec Visite', sortable: true },
    { id: 'without_visit', label: 'Sans Visite', sortable: true },
    { id: 'total', label: 'Avec Visite/Sans Visite', sortable: true }
  ];

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<NewPatientQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleFilterChange = (newFilter: Partial<NewPatientFilter>) => {
    const updatedFilter = { ...filter, ...newFilter };
    setFilter(updatedFilter);
    onFilterChange?.(updatedFilter);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  // Fonctions de gestion des boutons
  const handleSave = async () => {
    try {
      setLoading(true);

      // Ici vous pouvez ajouter l'appel à votre API pour sauvegarder le patient
      console.log('Données du patient:', form.values);

      notifications.show({
        title: 'Patient sauvegardé',
        message: 'Les informations du patient ont été sauvegardées.',
        color: 'green',
        icon: <IconDeviceFloppy size={16} />,
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de sauvegarder les informations du patient.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleValidate = async () => {
    const validation = form.validate();
    if (validation.hasErrors) {
      notifications.show({
        title: 'Erreur de validation',
        message: 'Veuillez corriger les erreurs dans le formulaire.',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);
      await handleSave();

      notifications.show({
        title: 'Patient validé',
        message: 'Le dossier patient a été validé et enregistré.',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de valider le dossier patient.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    notifications.show({
      title: 'Formulaire réinitialisé',
      message: 'Les modifications ont été annulées.',
      color: 'red',
      icon: <IconX size={16} />,
    });
  };

  return (
    <Paper p="xl" radius="md" withBorder w="100%">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group align="center">
          <IconUser size={24} color="blue" />
          <Title order={3} c="blue">
            Nouveau Patient N°: {form.values.numero}
          </Title>
        </Group>
        <Group>
          <Button leftSection={<IconList size={16} />} variant="outline">
            Liste des patients
          </Button>
        </Group>
      </Group>

      {/* Main Form */}
      <Card withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          {/* Basic Information */}
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N° Patient"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
                disabled
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Nom"
                placeholder="Nom de famille"
                {...form.getInputProps('nom')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Prénom"
                placeholder="Prénom"
                {...form.getInputProps('prenom')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date de naissance"
                placeholder="Sélectionner une date"
                {...form.getInputProps('dateNaissance')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="Lieu de naissance"
                placeholder="Ville de naissance"
                {...form.getInputProps('lieuNaissance')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Sexe"
                placeholder="Sélectionner"
                data={sexeOptions}
                {...form.getInputProps('sexe')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Situation familiale"
                placeholder="Sélectionner"
                data={situationFamilialeOptions}
                {...form.getInputProps('situationFamiliale')}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Profession"
                placeholder="Profession"
                {...form.getInputProps('profession')}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <Select
                label="Nationalité"
                placeholder="Sélectionner"
                data={nationaliteOptions}
                {...form.getInputProps('nationalite')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="N° Sécurité Sociale"
                placeholder="Numéro de sécurité sociale"
                {...form.getInputProps('numeroSecuriteSociale')}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" />

          {/* Tabs */}
          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="personnel" leftSection={<IconUser size={16} />}>
                Informations personnelles
              </Tabs.Tab>
              <Tabs.Tab value="contact" leftSection={<IconPhone size={16} />}>
                Contact
              </Tabs.Tab>
              <Tabs.Tab value="medical" leftSection={<IconStethoscope size={16} />}>
                Informations médicales
              </Tabs.Tab>
              <Tabs.Tab value="urgence" leftSection={<IconHeart size={16} />}>
                Contact d&apos;urgence
              </Tabs.Tab>
              <Tabs.Tab value="administratif" leftSection={<IconId size={16} />}>
                Administratif
              </Tabs.Tab>
              <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
                Commentaires
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="personnel" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone"
                    placeholder="Numéro de téléphone"
                    leftSection={<IconPhone size={16} />}
                    {...form.getInputProps('telephone')}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Email"
                    placeholder="Adresse email"
                    leftSection={<IconMail size={16} />}
                    {...form.getInputProps('email')}
                    type="email"
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Adresse"
                    placeholder="Adresse complète"
                    rows={3}
                    {...form.getInputProps('adresse')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Ville"
                    placeholder="Ville"
                    {...form.getInputProps('ville')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Code postal"
                    placeholder="Code postal"
                    {...form.getInputProps('codePostal')}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label="Pays"
                    placeholder="Pays"
                    {...form.getInputProps('pays')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="contact" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone principal"
                    placeholder="Numéro de téléphone"
                    leftSection={<IconPhone size={16} />}
                    {...form.getInputProps('telephone')}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Email"
                    placeholder="Adresse email"
                    leftSection={<IconMail size={16} />}
                    {...form.getInputProps('email')}
                    type="email"
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Adresse complète"
                    placeholder="Adresse, ville, code postal, pays"
                    rows={4}
                    leftSection={<IconMapPin size={16} />}
                    {...form.getInputProps('adresse')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="medical" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <Select
                    label="Groupe sanguin"
                    placeholder="Sélectionner"
                    data={groupeSanguinOptions}
                    {...form.getInputProps('groupeSanguin')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Médecin traitant"
                    placeholder="Nom du médecin traitant"
                    {...form.getInputProps('medecinTraitant')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Allergies"
                    placeholder="Allergies connues"
                    rows={3}
                    {...form.getInputProps('allergies')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Maladies chroniques"
                    placeholder="Maladies chroniques"
                    rows={3}
                    {...form.getInputProps('maladiesChroniques')}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Médicaments actuels"
                    placeholder="Médicaments en cours"
                    rows={3}
                    {...form.getInputProps('medicamentsActuels')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="urgence" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Nom du contact"
                    placeholder="Nom et prénom"
                    {...form.getInputProps('contactUrgenceNom')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Relation"
                    placeholder="Relation avec le patient"
                    {...form.getInputProps('contactUrgenceRelation')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Téléphone d'urgence"
                    placeholder="Numéro de téléphone"
                    leftSection={<IconPhone size={16} />}
                    {...form.getInputProps('contactUrgenceTelephone')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="administratif" pt="md">
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Mutuelle"
                    placeholder="Nom de la mutuelle"
                    {...form.getInputProps('mutuelle')}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="N° Mutuelle"
                    placeholder="Numéro de mutuelle"
                    {...form.getInputProps('numeroMutuelle')}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="commentaires" pt="md">
              <Textarea
                label="Commentaires"
                placeholder="Commentaires généraux sur le patient..."
                rows={6}
                {...form.getInputProps('commentaires')}
              />
            </Tabs.Panel>
          </Tabs>

          <Divider my="xl" />

          {/* Action Buttons */}
          <Group justify="space-between">
            <Group>
              <Button
                leftSection={<IconDeviceFloppy size={16} />}
                color="blue"
                onClick={handleSave}
                loading={loading}
              >
                Sauvegarder
              </Button>
              <Button
                leftSection={<IconCheck size={16} />}
                color="green"
                onClick={handleValidate}
                loading={loading}
              >
                Valider
              </Button>
            </Group>
            <Group>
              <Button
                leftSection={<IconX size={16} />}
                color="red"
                variant="outline"
                onClick={handleCancel}
              >
                Annuler
              </Button>
            </Group>
          </Group>
        </form>
      </Card>
    </Paper>
  );
};

export default NouveauPatients;
