/**
 * Pharmacy Dashboard Widgets
 * Displays key pharmacy and inventory metrics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconPill,
  IconPackage,
  IconShoppingCart,
  IconTruck,
  IconAlertTriangle,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconAlertCircle,
  IconCurrencyEuro,
  IconUsers,
  IconChartPie,
  IconCalendarDollar,
} from '@tabler/icons-react';
import { usePharmacy } from '@/hooks/usePharmacy';

interface PharmacyWidgetsProps {
  dateRange?: { start: string; end: string };
  compact?: boolean;
  showInventoryAlerts?: boolean;
}

const PharmacyWidgets: React.FC<PharmacyWidgetsProps> = ({
  dateRange,
  compact = false,
  showInventoryAlerts = true,
}) => {
  const {
    medications,
    inventory,
    purchaseOrders,
    salesOrders,
    suppliers,
    analytics,
    loading,
    error,
    getLowStockItems,
    getExpiredItems,
    getPendingPurchases,
    getStockValue,
    getTopSellingMedications,
    getSupplierPerformance,
    getInventoryAlerts,
    getPharmacyStats,
  } = usePharmacy({ 
    dateRange, 
    autoFetch: true,
    dataTypes: ['medications', 'inventory', 'purchases', 'sales', 'analytics']
  });

  const pharmacyStats = getPharmacyStats();
  const inventoryAlerts = getInventoryAlerts();
  const topMedications = getTopSellingMedications(3);
  const supplierPerformance = getSupplierPerformance().slice(0, 3);

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  const stockUtilization = inventory.length > 0 ? 
    (inventory.filter(item => item.current_stock > item.minimum_stock).length / inventory.length) * 100 : 0;

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Total Medications Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconPill size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Médicaments</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="blue">
            {pharmacyStats.totalMedications}
          </Text>
          <Text size="xs" c="dimmed">
            {medications.filter(m => m.status === 'active').length} actifs
          </Text>
        </Card>

        {/* Stock Value Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="green" size="sm">
                <IconPackage size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Valeur Stock</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="green">
            {pharmacyStats.totalStockValue.toLocaleString()}€
          </Text>
          <Progress value={stockUtilization} size="xs" mt="xs" />
          <Text size="xs" c="dimmed">
            {stockUtilization.toFixed(1)}% bien approvisionné
          </Text>
        </Card>

        {/* Low Stock Alerts Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconAlertTriangle size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Stock Faible</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="orange">
            {pharmacyStats.lowStockCount}
          </Text>
          <Text size="xs" c="dimmed">
            {pharmacyStats.expiredCount} expirés
          </Text>
        </Card>

        {/* Sales Performance Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconShoppingCart size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Ventes Mensuelles</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ value: Math.min((pharmacyStats.monthlyRevenue / 50000) * 100, 100), color: 'purple' }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {(pharmacyStats.monthlyRevenue / 1000).toFixed(0)}k€
                </Text>
              }
            />
          </Group>
        </Card>
      </SimpleGrid>

      {!compact && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={3} spacing="md">
            {/* Top Selling Medications */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconTrendingUp size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Top Ventes</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {topMedications.map((item, index) => (
                  <Group key={item.medication.id} justify="space-between">
                    <Group gap="xs">
                      <Badge size="xs" color="teal" variant="light">
                        {index + 1}
                      </Badge>
                      <Text size="xs">{item.medication.name}</Text>
                    </Group>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{item.sales}</Text>
                      <Text size="xs" c="dimmed">{item.medication.form}</Text>
                    </Group>
                  </Group>
                ))}
                {topMedications.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée de vente
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Supplier Performance */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="indigo" size="sm">
                    <IconTruck size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Performance Fournisseurs</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {supplierPerformance.map((item, index) => (
                  <Group key={item.supplier.id} justify="space-between">
                    <Group gap="xs">
                      <Badge size="xs" color="indigo" variant="light">
                        {index + 1}
                      </Badge>
                      <Text size="xs">{item.supplier.name}</Text>
                    </Group>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{item.performance.toFixed(1)}%</Text>
                      <Text size="xs" c="dimmed">
                        {item.supplier.rating ? `★${item.supplier.rating}` : 'N/A'}
                      </Text>
                    </Group>
                  </Group>
                ))}
                {supplierPerformance.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée fournisseur
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Purchase Orders Status */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="yellow" size="sm">
                    <IconCalendarDollar size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Commandes</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text size="xs">En attente</Text>
                  <Text size="xs" fw={500}>
                    {purchaseOrders.filter(o => o.status === 'sent' || o.status === 'confirmed').length}
                  </Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">Reçues</Text>
                  <Text size="xs" fw={500}>
                    {purchaseOrders.filter(o => o.status === 'received').length}
                  </Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">Brouillons</Text>
                  <Text size="xs" fw={500}>
                    {purchaseOrders.filter(o => o.status === 'draft').length}
                  </Text>
                </Group>
                <Divider size="xs" />
                <Group justify="space-between">
                  <Text size="xs">Valeur totale</Text>
                  <Text size="xs" fw={500}>
                    {purchaseOrders.reduce((sum, o) => sum + o.total_amount, 0).toLocaleString()}€
                  </Text>
                </Group>
              </Stack>
            </Card>
          </SimpleGrid>

          {/* Inventory Alerts */}
          {showInventoryAlerts && (inventoryAlerts.lowStock.length > 0 || inventoryAlerts.expired.length > 0 || inventoryAlerts.nearExpiry.length > 0) && (
            <SimpleGrid cols={3} spacing="md">
              {/* Low Stock Alert */}
              {inventoryAlerts.lowStock.length > 0 && (
                <Alert icon={<IconAlertTriangle size={16} />} color="orange" variant="light">
                  <Group justify="space-between">
                    <div>
                      <Text size="sm" fw={600}>Stock Faible</Text>
                      <Text size="xs">
                        {inventoryAlerts.lowStock.length} article(s) nécessitent un réapprovisionnement
                      </Text>
                    </div>
                    <Badge size="sm" color="orange">
                      {inventoryAlerts.lowStock.length}
                    </Badge>
                  </Group>
                </Alert>
              )}

              {/* Expired Items Alert */}
              {inventoryAlerts.expired.length > 0 && (
                <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
                  <Group justify="space-between">
                    <div>
                      <Text size="sm" fw={600}>Articles Expirés</Text>
                      <Text size="xs">
                        {inventoryAlerts.expired.length} article(s) ont expiré
                      </Text>
                    </div>
                    <Badge size="sm" color="red">
                      {inventoryAlerts.expired.length}
                    </Badge>
                  </Group>
                </Alert>
              )}

              {/* Near Expiry Alert */}
              {inventoryAlerts.nearExpiry.length > 0 && (
                <Alert icon={<IconAlertTriangle size={16} />} color="yellow" variant="light">
                  <Group justify="space-between">
                    <div>
                      <Text size="sm" fw={600}>Expiration Proche</Text>
                      <Text size="xs">
                        {inventoryAlerts.nearExpiry.length} article(s) expirent bientôt
                      </Text>
                    </div>
                    <Badge size="sm" color="yellow">
                      {inventoryAlerts.nearExpiry.length}
                    </Badge>
                  </Group>
                </Alert>
              )}
            </SimpleGrid>
          )}

          {/* Detailed Analytics */}
          {analytics && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>Analytiques Pharmacie</Text>
                <Badge size="sm" color="blue">
                  Rotation: {analytics.stock_turnover_rate.toFixed(1)}x
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Total Médicaments</Text>
                    <Text size="lg" fw={700} c="blue">{analytics.total_medications}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Valeur Stock</Text>
                    <Text size="lg" fw={700} c="green">{analytics.total_stock_value.toLocaleString()}€</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Stock Faible</Text>
                    <Text size="lg" fw={700} c="orange">{analytics.low_stock_items}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Articles Expirés</Text>
                    <Text size="lg" fw={700} c="red">{analytics.expired_items}</Text>
                  </Stack>
                </Grid.Col>
              </Grid>
              
              {analytics.expiry_alerts.length > 0 && (
                <>
                  <Divider my="md" />
                  <Text size="sm" fw={600} mb="xs">Alertes d'Expiration</Text>
                  <Stack gap="xs">
                    {analytics.expiry_alerts.slice(0, 3).map((alert) => (
                      <Group key={alert.medication_id} justify="space-between">
                        <div>
                          <Text size="xs" fw={500}>{alert.medication_name}</Text>
                          <Text size="xs" c="dimmed">
                            Lot: {alert.batch_number} | Stock: {alert.current_stock}
                          </Text>
                        </div>
                        <Group gap="xs">
                          <Badge 
                            size="xs" 
                            color={alert.days_to_expiry <= 30 ? 'red' : alert.days_to_expiry <= 60 ? 'orange' : 'yellow'}
                          >
                            {alert.days_to_expiry}j
                          </Badge>
                          <Text size="xs" c="dimmed">
                            {new Date(alert.expiry_date).toLocaleDateString()}
                          </Text>
                        </Group>
                      </Group>
                    ))}
                  </Stack>
                </>
              )}
            </Card>
          )}
        </>
      )}
    </Stack>
  );
};

export default PharmacyWidgets;
