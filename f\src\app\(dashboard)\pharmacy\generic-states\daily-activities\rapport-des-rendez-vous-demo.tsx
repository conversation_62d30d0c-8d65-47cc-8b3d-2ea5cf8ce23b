'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { RapportDesRendezVous } from './Rapport_des_rendez_vous';

export default function RapportDesRendezVousDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête changée:', query);
    alert(`Période sélectionnée: Du ${query.start.toLocaleDateString()} au ${query.end.toLocaleDateString()}`);
  };

  const handleStateChange = (state: any) => {
    console.log('État changé:', state);
    const stateMessages: { [key: string]: string } = {
      'appointments': 'Rendez-Vous sélectionnés',
      'visits': 'Entrée/Visite sélectionnées',
      'consultation_reasons': 'Motifs de Consultation sélectionnés'
    };
    alert(stateMessages[state.name] || 'État sélectionné');
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    if (filter.showAdvancedFilter !== undefined) {
      alert(`Filtre avancé: ${filter.showAdvancedFilter ? 'Activé' : 'Désactivé'}`);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} du rapport des rendez-vous en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression du rapport des rendez-vous en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'total': 'Total'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function RapportDesRendezVousLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={true}
          onQueryChange={(query) => console.log('Query:', query)}
          onStateChange={(state) => console.log('State:', state)}
          onFilterChange={(filter) => console.log('Filter:', filter)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
          onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function RapportDesRendezVousWithDataDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec données:', query);
    const startDate = query.start.toLocaleDateString('fr-FR');
    const endDate = query.end.toLocaleDateString('fr-FR');
    alert(`Rapport des rendez-vous du ${startDate} au ${endDate}:\n- Rendez-vous programmés: 45\n- Rendez-vous réalisés: 38\n- Rendez-vous annulés: 7`);
  };

  const handleStateChange = (state: any) => {
    console.log('État avec données:', state);
    
    const stateData: { [key: string]: any } = {
      'appointments': {
        title: 'Rendez-Vous',
        data: 'Consultations programmées, confirmées, annulées'
      },
      'visits': {
        title: 'Entrée/Visite',
        data: 'Visites réalisées, patients reçus'
      },
      'consultation_reasons': {
        title: 'Motifs de Consultation',
        data: 'Urgences, contrôles, soins, examens'
      }
    };
    
    const data = stateData[state.name];
    if (data) {
      alert(`${data.title}:\n${data.data}`);
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé:\n- Filtrage par statut\n- Filtrage par date\n- Filtrage par patient');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} du rapport des rendez-vous avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression du rapport des rendez-vous avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'total': 'Tri des totaux de rendez-vous'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode rendez-vous
export function RapportDesRendezVousAppointmentsDemo() {
  const handleStateChange = (state: any) => {
    console.log('Mode rendez-vous:', state);
    if (state.type === 'appointments') {
      alert('Mode Rendez-Vous activé:\n- Consultations programmées\n- Rendez-vous confirmés\n- Rendez-vous annulés\n- Rendez-vous reportés');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre rendez-vous:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre rendez-vous:\n- Statut des rendez-vous\n- Créneaux horaires\n- Types de consultation');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={(query) => console.log('Query rendez-vous:', query)}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données de rendez-vous`)}
          onPrint={() => alert('Impression des données de rendez-vous')}
          onSort={(columnId, direction) => console.log('Sort rendez-vous:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode visites
export function RapportDesRendezVousVisitsDemo() {
  const handleStateChange = (state: any) => {
    console.log('Mode visites:', state);
    if (state.type === 'visits') {
      alert('Mode Entrée/Visite activé:\n- Patients reçus\n- Visites d\'urgence\n- Consultations sans rendez-vous\n- Passages aux urgences');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre visites:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre visites:\n- Type de visite\n- Heure d\'arrivée\n- Durée de consultation');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={(query) => console.log('Query visites:', query)}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données de visites`)}
          onPrint={() => alert('Impression des données de visites')}
          onSort={(columnId, direction) => console.log('Sort visites:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode motifs de consultation
export function RapportDesRendezVousConsultationReasonsDemo() {
  const handleStateChange = (state: any) => {
    console.log('Mode motifs:', state);
    if (state.type === 'consultation_reasons') {
      alert('Mode Motifs de Consultation activé:\n- Urgences médicales\n- Contrôles de routine\n- Soins spécialisés\n- Examens diagnostiques');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre motifs:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre motifs:\n- Catégorie de motif\n- Urgence du cas\n- Spécialité médicale');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={(query) => console.log('Query motifs:', query)}
          onStateChange={handleStateChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des motifs de consultation`)}
          onPrint={() => alert('Impression des motifs de consultation')}
          onSort={(columnId, direction) => console.log('Sort motifs:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function RapportDesRendezVousErrorDemo() {
  const handleQueryChange = (query: any) => {
    console.log('Requête avec validation:', query);
    
    // Simuler une validation
    const diffDays = Math.abs(query.end - query.start) / (1000 * 60 * 60 * 24);
    if (diffDays > 365) {
      alert('Attention: Période trop longue (> 1 an). Les performances peuvent être affectées.');
      return;
    }
    
    console.log('Requête validée:', query);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <RapportDesRendezVous
          loading={false}
          onQueryChange={handleQueryChange}
          onStateChange={(state) => console.log('State avec validation:', state)}
          onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
          onExport={(format) => {
            console.log(`Export ${format} avec validation`);
            if (confirm(`Êtes-vous sûr de vouloir exporter le rapport des rendez-vous en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onPrint={() => {
            console.log('Impression avec validation');
            if (confirm('Êtes-vous sûr de vouloir imprimer le rapport des rendez-vous ?')) {
              alert('Impression en cours...');
            }
          }}
          onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}
