import {VoiceRecognitionDialog} from '@/components/accessories/VoiceRecognitionDialog';
---
    const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleSubmit = (speechText) => {
    console.log('Speech text:', speechText);
    setIsDialogOpen(false);
  };

  const handleClose = () => {
    setIsDialogOpen(false);
  };



----
 {/*   Show VoiceRecognitionDialog*/}
   {/*   Show VoiceRecognitionDialog*/}
                     <Modal.Root opened={isDialogOpen} onClose={handleClose}   size="lg" yOffset="30vh" xOffset={0}>
                    <VoiceRecognitionDialog
                    isOpen={isDialogOpen}
                    onClose={handleClose}
                    onSubmit={handleSubmit}
                  />
                     </Modal.Root>    

--------------------------------------------------      






























link: "/settings?tab=general"
        		