/**
 * Patient API service for managing patient data
 */

import api from '../lib/api';
import axios from 'axios';

export interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  address?: string;
  birth_date?: string;
  age?: number;
  gender?: '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre';
  patient_title?: string;
  etat_civil?: '' | 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)';
  cin?: string;
  social_security?: string;
  profession?: string;
  birthPlace?: string;
  fatherName?: string;
  motherName?: string;
  bloodGroup?: string;
  allergies?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PatientCreateData {
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  address?: string;
  birth_date?: string;
  age?: number;
  gender?: '' | 'Homme' | 'Femme' | 'Enfant' | 'Autre';
  patient_title?: string;
  etat_civil?: '' | 'Célibataire' | 'Mari<PERSON>(e)' | 'Divorcé(e)' | 'Veuf(ve)';
  cin?: string;
  social_security?: string;
  profession?: string;
  birthPlace?: string;
  fatherName?: string;
  motherName?: string;
  bloodGroup?: string;
  allergies?: string;
  notes?: string;
}

export interface PatientSearchParams {
  search?: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  email?: string;
  limit?: number;
  offset?: number;
}

class PatientAPI {
  private baseURL = '/api/users/patients';

  /**
   * Create a new patient
   */
  async create(patientData: PatientCreateData): Promise<Patient> {
    console.log('🏥 Creating patient:', patientData);

    // Validate and clean patient data
    const cleanedData = this.validateAndCleanPatientData(patientData);
    console.log('🧹 Cleaned patient data:', cleanedData);

    try {
      // Use the existing patient bridge endpoint
      const response = await api.post(`${this.baseURL}/create-from-frontend/`, cleanedData);
      console.log('✅ Patient created successfully:', response.data);
      
      // Check if this is an existing patient (not actually created)
      if (response.data.existing) {
        console.log('🔄 Patient already exists, returning existing patient data');
      }
      
      return response.data;
    } catch (error) {
      console.error('❌ Failed to create patient:', error);

      // Extract detailed error information
      if (axios.isAxiosError(error)) {
        const response = error.response;
        if (response?.data) {
          console.error('❌ Backend validation errors:', response.data);

          // Handle duplicate email error specifically
          if (response.data.error_type === 'duplicate_email' || 
              (response.data.message && response.data.message.includes('email existe déjà'))) {
            throw new Error('Un patient avec cet email existe déjà dans le système. Si vous êtes certain qu\'il s\'agit du même patient, veuillez vérifier que les informations de nom, prénom, téléphone et autres détails correspondent. Veuillez utiliser une adresse email différente.');
          }

          // If there are specific validation errors, format them
          if (response.data.errors) {
            const errorMessages = Object.entries(response.data.errors)
              .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
              .join('; ');
            throw new Error(`Validation failed: ${errorMessages}`);
          } else if (response.data.message) {
            throw new Error(response.data.message);
          }
        }
      }

      throw error;
    }
  }

  /**
   * Validate and clean patient data before sending to backend
   */
  private validateAndCleanPatientData(data: PatientCreateData): PatientCreateData {
    // Validate required fields
    if (!data.first_name?.trim() || !data.last_name?.trim()) {
      throw new Error('First name and last name are required');
    }

    const cleaned: PatientCreateData = {
      first_name: data.first_name.trim(),
      last_name: data.last_name.trim(),
    };

    // Only add email if it's valid and not empty
    if (data.email && data.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(data.email.trim())) {
        cleaned.email = data.email.trim().toLowerCase();
      } else {
        console.warn('Invalid email format, skipping email field:', data.email);
      }
    }

    // Only add phone if it's not empty
    if (data.phone_number && data.phone_number.trim()) {
      cleaned.phone_number = data.phone_number.trim();
    }

    // Only add address if it's not empty
    if (data.address && data.address.trim()) {
      cleaned.address = data.address.trim();
    }

    // Only add birth_date if it's valid
    if (data.birth_date && data.birth_date.trim()) {
      // Basic date validation (YYYY-MM-DD format)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(data.birth_date.trim())) {
        cleaned.birth_date = data.birth_date.trim();
      } else {
        console.warn('Invalid birth date format, skipping birth_date field:', data.birth_date);
      }
    }

    // Only add age if it's a positive number
    if (data.age && data.age > 0 && data.age < 150) {
      cleaned.age = data.age;
    }

    // Only add gender if it's a valid value
    if (data.gender && data.gender.trim()) {
      const validGenders = ['Homme', 'Femme', 'Enfant', 'Autre'];
      if (validGenders.includes(data.gender.trim())) {
        cleaned.gender = data.gender.trim() as 'Homme' | 'Femme' | 'Enfant' | 'Autre';
      }
    }

    // Only add patient_title if it's not empty
    if (data.patient_title && data.patient_title.trim()) {
      cleaned.patient_title = data.patient_title.trim();
    }

    // Only add etat_civil if it's a valid value
    if (data.etat_civil && data.etat_civil.trim()) {
      const validStates = ['Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf(ve)'];
      if (validStates.includes(data.etat_civil.trim())) {
        cleaned.etat_civil = data.etat_civil.trim() as 'Célibataire' | 'Marié(e)' | 'Divorcé(e)' | 'Veuf(ve)';
      }
    }

    // Add other optional fields if they have values
    const optionalFields = [
      'cin', 'social_security', 'profession', 'birthPlace',
      'fatherName', 'motherName', 'bloodGroup', 'allergies', 'notes'
    ] as const;

    optionalFields.forEach(field => {
      if (data[field] && typeof data[field] === 'string' && (data[field] as string).trim()) {
        (cleaned as unknown as Record<string, unknown>)[field] = (data[field] as string).trim();
      }
    });

    console.log('🧹 Cleaned patient data:', cleaned);
    return cleaned;
  }

  /**
   * Get patient by ID
   */
  async get(patientId: string): Promise<Patient> {
    console.log('🔍 Fetching patient:', patientId);

    try {
      // Use the existing patient bridge endpoint
      const response = await api.get(`${this.baseURL}/${patientId}/detail/`);
      console.log('✅ Patient fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to fetch patient:', error);
      throw error;
    }
  }

  /**
   * Update patient
   */
  async update(patientId: string, patientData: Partial<PatientCreateData>): Promise<Patient> {
    console.log('📝 Updating patient:', patientId, patientData);

    try {
      // Use the existing patient bridge endpoint
      const response = await api.put(`${this.baseURL}/${patientId}/update/`, patientData);
      console.log('✅ Patient updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to update patient:', error);
      throw error;
    }
  }

  /**
   * Search patients
   */
  async search(params: PatientSearchParams): Promise<{ results: Patient[]; count: number }> {
    console.log('🔍 Searching patients:', params);

    try {
      // Try enhanced search endpoint first
      const response = await api.get(`${this.baseURL}/search/`, { params });
      console.log('✅ Patient search successful:', response.data);
      return response.data;
    } catch (error) {
      console.warn('❌ Enhanced search failed, using list with filtering:', error);

      // Fallback: use list endpoint and filter client-side
      try {
        const listResponse = await this.list({ limit: 100 });
        const filteredResults = listResponse.results.filter(patient => {
          if (params.search) {
            const searchTerm = params.search.toLowerCase();
            return (
              patient.first_name?.toLowerCase().includes(searchTerm) ||
              patient.last_name?.toLowerCase().includes(searchTerm) ||
              patient.phone_number?.toLowerCase().includes(searchTerm) ||
              patient.email?.toLowerCase().includes(searchTerm)
            );
          }
          return true;
        });

        return { results: filteredResults, count: filteredResults.length };
      } catch (fallbackError) {
        console.error('❌ Fallback search also failed:', fallbackError);
        throw fallbackError;
      }
    }
  }

  /**
   * List all patients
   */
  async list(params?: { limit?: number; offset?: number }): Promise<{ results: Patient[]; count: number }> {
    console.log('📋 Listing patients:', params);

    try {
      // Use the existing patient bridge endpoint
      const response = await api.get(`${this.baseURL}/list/`, { params });
      console.log('✅ Patient list fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to list patients:', error);
      throw error;
    }
  }

  /**
   * Quick create patient (minimal data)
   */
  async quickCreate(data: { first_name: string; last_name: string; phone_number?: string }): Promise<Patient> {
    console.log('⚡ Quick creating patient:', data);

    try {
      // Try enhanced quick-create endpoint first
      const response = await api.post(`${this.baseURL}/quick-create/`, data);
      console.log('✅ Patient quick created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.warn('❌ Quick create failed, using regular create:', error);

      // Fallback: use regular create endpoint
      return this.create(data as PatientCreateData);
    }
  }

  /**
   * Find or create patient by name, phone, and email - STRICT DUPLICATE PREVENTION
   */
  async findOrCreate(data: PatientCreateData): Promise<{ patient: Patient; created: boolean }> {
    console.log('🔍➕ Finding or creating patient with STRICT duplicate prevention:', data);

    // Enhanced patient verification - check multiple identifiers
    const verificationCriteria = [];
    
    // Check if we have sufficient identifying information
    const hasEmail = data.email && data.email.trim();
    const hasName = data.first_name && data.last_name;
    const hasPhone = data.phone_number && data.phone_number.trim();
    
    // We need at least email OR (name + phone) to proceed
    if (!hasEmail && !(hasName && hasPhone)) {
      throw new Error('Insufficient patient identification information. Please provide either email or both name and phone number.');
    }

    try {
      // Search by multiple criteria to verify patient identity
      const searchResults = [];
      
      // Search by email if provided
      if (hasEmail && data.email) {
        try {
          const emailResults = await this.search({
            search: data.email.trim(),
            limit: 10
          });
          searchResults.push(...emailResults.results);
        } catch (error) {
          console.warn('Email search failed:', error);
        }
      }
      
      // Search by phone if provided
      if (hasPhone && data.phone_number) {
        try {
          const phoneResults = await this.search({
            search: data.phone_number.trim(),
            limit: 10
          });
          searchResults.push(...phoneResults.results);
        } catch (error) {
          console.warn('Phone search failed:', error);
        }
      }
      
      // Search by name if provided
      if (hasName) {
        try {
          const nameResults = await this.search({
            first_name: data.first_name,
            last_name: data.last_name,
            limit: 10
          });
          searchResults.push(...nameResults.results);
        } catch (error) {
          console.warn('Name search failed:', error);
        }
      }
      
      // Deduplicate search results by patient ID
      const uniquePatients = Array.from(
        new Map(searchResults.map(patient => [patient.id, patient])).values()
      );
      
      // If we found potential matches, we need to verify identity
      if (uniquePatients.length > 0) {
        console.log('🔍 Found potential patient matches:', uniquePatients.length);
        
        // Try to find exact match
        let exactMatch = null;
        
        // If we have email, look for exact email match
        if (hasEmail) {
          exactMatch = uniquePatients.find(patient => 
            patient.email?.toLowerCase() === data.email?.toLowerCase()
          );
        }
        
        // If no email match but we have name and phone, look for exact match
        if (!exactMatch && hasName && hasPhone) {
          exactMatch = uniquePatients.find(patient => 
            patient.first_name?.toLowerCase() === data.first_name?.toLowerCase() &&
            patient.last_name?.toLowerCase() === data.last_name?.toLowerCase() &&
            patient.phone_number === data.phone_number
          );
        }
        
        if (exactMatch) {
          console.log('✅ Found exact patient match:', exactMatch.id);
          // Verify this is the same patient before updating
          return await this.verifyAndReturnPatient(exactMatch, data);
        } else {
          // Multiple potential matches - need to ask user to confirm identity
          console.log('⚠️ Multiple potential matches found, need user confirmation');
          // For now, we'll proceed with creating a new patient to avoid data corruption
          // In a real implementation, you'd want to prompt the user to select the correct patient
          // BUT if we have email, we should be more strict
          if (hasEmail) {
            // If we have an email and found potential matches but no exact match,
            // this indicates a different patient with the same email
            console.warn('⚠️ Different patient detected with same email. Blocking patient creation.');
            throw new Error(`EMAIL DUPLIQUÉ DÉTECTÉ: Un patient avec l'email "${data.email}" existe déjà dans le système. Si vous êtes certain qu'il s'agit du même patient, veuillez vérifier que les informations de nom, prénom, téléphone et autres détails correspondent. AUCUN DOUBLON AUTORISÉ.`);
          }
        }
      }
    } catch (verificationError) {
      console.warn('Patient verification failed, proceeding with caution:', verificationError);
    }

    // If we get here, either no matches were found or verification failed
    // Proceed with creating a new patient
    console.log('🆕 Creating new patient with data:', data);

    try {
      const newPatient = await this.create(data);
      console.log('✅ Created new patient:', newPatient);
      return { patient: newPatient, created: true };
    } catch (createError) {
      console.error('❌ Failed to create patient:', createError);

      // If it's a unique constraint error, STRICTLY PREVENT DUPLICATE
      const errorMessage = (createError as unknown as { message?: string })?.message || '';
      if (errorMessage.includes('UNIQUE constraint') || errorMessage.includes('already exists') || errorMessage.includes('email')) {
        console.error('🚫 DUPLICATE EMAIL DETECTED IN BACKEND - STRICT PREVENTION');

        // ABSOLUTELY NO DUPLICATES ALLOWED
        throw new Error(`EMAIL DUPLIQUÉ DÉTECTÉ: Un patient avec l'email "${data.email}" existe déjà dans le système. Si vous êtes certain qu'il s'agit du même patient, veuillez vérifier que les informations de nom, prénom, téléphone et autres détails correspondent. AUCUN DOUBLON AUTORISÉ.`);
      }

      // If all else fails, throw the original error
      throw createError;
    }
  }

  /**
   * Verify patient identity before returning existing patient
   */
  private async verifyAndReturnPatient(existingPatient: Patient, newData: PatientCreateData): Promise<{ patient: Patient; created: boolean }> {
    console.log('🔍 Verifying patient identity before returning:', existingPatient.id);
    
    // Check if we're updating significantly different information
    const significantChanges = [];
    
    // Check name differences
    if (newData.first_name && existingPatient.first_name && 
        newData.first_name.toLowerCase() !== existingPatient.first_name.toLowerCase()) {
      significantChanges.push(`First name: ${existingPatient.first_name} → ${newData.first_name}`);
    }
    
    if (newData.last_name && existingPatient.last_name && 
        newData.last_name.toLowerCase() !== existingPatient.last_name.toLowerCase()) {
      significantChanges.push(`Last name: ${existingPatient.last_name} → ${newData.last_name}`);
    }
    
    // Check phone differences
    if (newData.phone_number && existingPatient.phone_number && 
        newData.phone_number !== existingPatient.phone_number) {
      significantChanges.push(`Phone: ${existingPatient.phone_number} → ${newData.phone_number}`);
    }
    
    // If there are significant changes, we should be cautious
    if (significantChanges.length > 0) {
      console.warn('⚠️ Significant changes detected in patient information:', significantChanges);
      // In a real implementation, you'd want to prompt the user to confirm these changes
      // For now, we'll proceed but log the changes
      console.warn('If you are certain this is the same patient, please verify that the name, phone number, and other details match.');
    }
    
    // Update patient information if new data is provided
    const updateData: Partial<PatientCreateData> = {};
    let needsUpdate = false;
    
    // Only update if new data is provided and different from existing
    if (newData.first_name && newData.first_name.trim() && 
        (!existingPatient.first_name || newData.first_name.toLowerCase() !== existingPatient.first_name.toLowerCase())) {
      updateData.first_name = newData.first_name.trim();
      needsUpdate = true;
    }
    
    if (newData.last_name && newData.last_name.trim() && 
        (!existingPatient.last_name || newData.last_name.toLowerCase() !== existingPatient.last_name.toLowerCase())) {
      updateData.last_name = newData.last_name.trim();
      needsUpdate = true;
    }
    
    if (newData.phone_number && newData.phone_number.trim() && 
        (!existingPatient.phone_number || newData.phone_number !== existingPatient.phone_number)) {
      updateData.phone_number = newData.phone_number.trim();
      needsUpdate = true;
    }
    
    if (newData.address && newData.address.trim() && 
        (!existingPatient.address || newData.address !== existingPatient.address)) {
      updateData.address = newData.address.trim();
      needsUpdate = true;
    }
    
    // Update other fields as needed
    const optionalFields = ['birth_date', 'age', 'gender', 'patient_title', 'etat_civil', 
                           'cin', 'social_security', 'profession', 'birthPlace', 
                           'fatherName', 'motherName', 'bloodGroup', 'allergies', 'notes'] as const;
    
    optionalFields.forEach(field => {
      if (newData[field] && typeof newData[field] === 'string' && (newData[field] as string).trim() &&
          (!existingPatient[field] || (newData[field] as string).trim() !== existingPatient[field])) {
        (updateData as unknown as Record<string, unknown>)[field] = (newData[field] as string).trim();
        needsUpdate = true;
      }
    });
    
    // Perform update if needed
    if (needsUpdate) {
      console.log('📝 Updating patient information with new data:', updateData);
      try {
        const updatedPatient = await this.update(existingPatient.id, updateData);
        console.log('✅ Patient information updated successfully');
        return { patient: updatedPatient, created: false };
      } catch (updateError) {
        console.error('❌ Failed to update patient information:', updateError);
        // Return existing patient without updates if update fails
        return { patient: existingPatient, created: false };
      }
    } else {
      console.log('✅ Returning existing patient without updates');
      return { patient: existingPatient, created: false };
    }
  }
}

export const patientAPI = new PatientAPI();
