import React, { useState, useEffect } from 'react';
import Icon from '@mdi/react';
import { mdiAccountSettings } from '@mdi/js';
import {
  Stack,
  Title,
  TextInput,
  Button,
  Table,
  Group,
  ActionIcon,
  Paper,
  Tooltip,
  Modal,
  Text,
 
  Tabs,
  Select,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconUsers,
 
} from '@tabler/icons-react';

// Types
interface User {
  id: string;
  fullName: string;
  serviceName: string;
  email: string;
  phoneNumber: string;
  medicalStatus: 'active' | 'inactive';
  connectionStatus: 'online' | 'offline';
}

interface Service {
  id: string;
  name: string;
  description?: string;
}

// Mock data for users
const mockUsers: User[] = [
  {
    id: '1',
    fullName: 'ASSISTANTE ASSISTANTE',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '',
    medicalStatus: 'inactive',
    connectionStatus: 'online',
  },
  {
    id: '2',
    fullName: 'MEDECIN HAMZA',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '',
    medicalStatus: 'active',
    connectionStatus: 'online',
  },
  {
    id: '3',
    fullName: 'MEDECIN YOUSSEF',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '',
    medicalStatus: 'active',
    connectionStatus: 'online',
  },
  {
    id: '4',
    fullName: 'TAOUSI KARIM',
    serviceName: 'service derma',
    email: '',
    phoneNumber: '',
    medicalStatus: 'active',
    connectionStatus: 'online',
  },
  {
    id: '5',
    fullName: 'ASSISTANT ASSISTANT',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '**********',
    medicalStatus: 'inactive',
    connectionStatus: 'offline',
  },
  {
    id: '6',
    fullName: 'DEMO DEMO',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '',
    medicalStatus: 'inactive',
    connectionStatus: 'offline',
  },
  {
    id: '7',
    fullName: 'IDRISS KHADIRI',
    serviceName: 'Main Service',
    email: '',
    phoneNumber: '**********',
    medicalStatus: 'inactive',
    connectionStatus: 'offline',
  },
];

// Mock data for services
const mockServices: Service[] = [
  {
    id: '1',
    name: 'Main Service',
    description: 'Service principal de la clinique'
  },
  {
    id: '2',
    name: 'service derma',
    description: 'Service de dermatologie'
  },
  {
    id: '3',
    name: 'service derma',
    description: 'Service de dermatologie (duplicate)'
  },
];

const Liste_des_uitilisateurs = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [activeTab, setActiveTab] = useState('users');
  const [modalOpened, setModalOpened] = useState(false);
  const [serviceModalOpened, setServiceModalOpened] = useState(false);

  useEffect(() => {
    setUsers(mockUsers);
    setServices(mockServices);
  }, []);

  // Filter users based on search query
  const filteredUsers = users.filter((user: User) =>
    user.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.phoneNumber.includes(searchQuery)
  );

  // Count available accounts (inactive medical status)
  const availableAccounts = users.filter(user => user.medicalStatus === 'inactive').length;

  // Render status dot
  const renderStatusDot = (status: 'active' | 'inactive' | 'online' | 'offline') => {
    const colorClass = status === 'active' || status === 'online' ? 'bg-green-500' : 'bg-red-500';
    return (
      <div className={`w-4 h-4 rounded-full ${colorClass}`} />
    );
  };

  return (
    <Stack gap="lg" className="w-full">
      {/* Header */}
      <Paper p="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUsers size={24} className="text-blue-600" />
            <Title order={3} className="text-gray-700">
              Listes des utilisateurs
            </Title>
          </Group>
          <Group gap="sm">
            <Text size="sm" className="text-gray-600">
              Compte disponible: <span className="font-semibold">{availableAccounts}</span>
            </Text>
            <Button
              leftSection={<IconPlus size={16} />}
              variant="filled"
              color="blue"
              size="sm"
              onClick={() => setModalOpened(true)}
            >
              Nouveau utilisateur
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              variant="filled"
              color="green"
              size="sm"
              onClick={() => setServiceModalOpened(true)}
            >
              Ajouter service
            </Button>
          </Group>
        </Group>
      </Paper>

      {/* Tabs */}
      <Paper withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'users')}>
          <Tabs.List>
            <Tabs.Tab value="users" leftSection={<IconUsers size={16} />}>
              Utilisateurs
            </Tabs.Tab>
            <Tabs.Tab value="services" leftSection={<Icon path={mdiAccountSettings} size={1} />}>
              Services
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="users" pt="md">
            {/* Search */}
            <Group justify="space-between" mb="md" px="md">
              <TextInput
                placeholder="Rechercher un utilisateur..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-full max-w-md"
              />
            </Group>

            {/* Users Table */}
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr className="bg-gray-50">
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Nom complet
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Nom du service
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Email
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Numéro de téléphone
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    Statut médical
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    Statut de connexion
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredUsers.map((user) => (
                  <Table.Tr key={user.id} className="hover:bg-gray-50">
                    <Table.Td className="font-medium border-r border-gray-200">
                      {user.fullName}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-200">
                      {user.serviceName}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-200">
                      {user.email || '-'}
                    </Table.Td>
                    <Table.Td className="border-r border-gray-200">
                      {user.phoneNumber || '-'}
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      <Group gap="xs" justify="center">
                        {renderStatusDot(user.medicalStatus)}
                        <Text size="sm" className={user.medicalStatus === 'active' ? 'text-green-600' : 'text-red-500'}>
                          {user.medicalStatus === 'active' ? 'Actif' : 'Inactif'}
                        </Text>
                      </Group>
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      <Group gap="xs" justify="center">
                        {renderStatusDot(user.connectionStatus)}
                        <Text size="sm" className={user.connectionStatus === 'online' ? 'text-green-600' : 'text-red-500'}>
                          {user.connectionStatus === 'online' ? 'En ligne' : 'Hors ligne'}
                        </Text>
                      </Group>
                    </Table.Td>
                    <Table.Td className="text-center">
                      <Group gap="xs" justify="center">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={() => {
                              // Handle edit action
                              console.log('Edit user:', user.id);
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Tabs.Panel>

          <Tabs.Panel value="services" pt="md">
            {/* Services Table */}
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr className="bg-gray-50">
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Nom du service
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {services.map((service) => (
                  <Table.Tr key={service.id} className="hover:bg-gray-50">
                    <Table.Td className="font-medium border-r border-gray-200">
                      {service.name}
                    </Table.Td>
                    <Table.Td className="text-center">
                      <Group gap="xs" justify="center">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={() => {
                              // Handle edit service action
                              console.log('Edit service:', service.id);
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Modal for adding new user */}
      <Modal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        title="Nouveau utilisateur"
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Nom complet"
            placeholder="Entrez le nom complet"
            required
          />

          <Select
            label="Service"
            placeholder="Sélectionnez un service"
            data={[
              { value: 'main', label: 'Main Service' },
              { value: 'derma', label: 'Service derma' },
              { value: 'cardio', label: 'Service cardiologie' },
            ]}
            required
          />

          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            type="email"
          />

          <TextInput
            label="Numéro de téléphone"
            placeholder="0612345678"
          />

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => setModalOpened(false)}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                // Handle add user logic here
                setModalOpened(false);
              }}
            >
              Ajouter
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal for adding new service */}
      <Modal
        opened={serviceModalOpened}
        onClose={() => setServiceModalOpened(false)}
        title={
          <Group gap="sm" className="text-white">
            <Icon path={mdiAccountSettings} size={1} />
            <Text size="lg" fw={500} className="text-white">
              Service
            </Text>
          </Group>
        }
        size="md"
        centered
        styles={{
          header: {
            backgroundColor: '#3b82f6',
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            padding: '16px 20px',
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
          content: {
            padding: 0,
          },
          body: {
            padding: '20px',
          },
        }}
      >
        <Stack gap="md">
          <div>
            <Text size="sm" fw={500} mb={5}>
              Nom du service <span className="text-red-500">*</span>
            </Text>
            <TextInput
              placeholder=""
              required
              styles={{
                input: {
                  borderBottom: '2px solid #3b82f6',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderRadius: 0,
                  paddingLeft: 0,
                  paddingRight: 0,
                  backgroundColor: 'transparent',
                  '&:focus': {
                    borderBottom: '2px solid #3b82f6',
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                  },
                },
              }}
            />
          </div>

          <Group justify="flex-end" mt="xl" gap="sm">
            <Button
              variant="filled"
              color="gray"
              onClick={() => setServiceModalOpened(false)}
              styles={{
                root: {
                  backgroundColor: '#9ca3af',
                  '&:hover': {
                    backgroundColor: '#6b7280',
                  },
                },
              }}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={() => {
                // Handle add service logic here
                setServiceModalOpened(false);
              }}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default Liste_des_uitilisateurs;
