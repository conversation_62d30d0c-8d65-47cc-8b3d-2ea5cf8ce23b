'use client';

import React, { useState } from 'react';
import {
  Paper,
  Group,
  Text,
  ActionIcon,
  Table,
  ScrollArea,
  Box,
  Loader,
  Radio,
  Menu,
  Alert
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiCashMultiple,
  mdiPrinter,
  mdiDatabaseExport,
  mdiFileExcelOutline,
  mdiFilePdfBox,
  mdiTableEdit,
  mdiFormatLetterMatches,
  mdiFormatColorHighlight,
  mdiTableSettings,
  mdiCog,
  mdiAlertCircleOutline,
  mdiArrowUp,
  mdiArrowDown
} from '@mdi/js';

// Types et interfaces
interface ServiceColumn {
  id: string;
  label: string;
  sortable: boolean;
  sortDirection?: 'asc' | 'desc';
}

interface ServiceQuery {
  start: Date;
  end: Date;
  activityNature: 'RENTING' | 'SALES';
}

interface ServiceFilter {
  code: string;
  referredBy: string;
  date: string;
  description: string;
}

interface PrestationsParMedecinProps {
  loading?: boolean;
  onQueryChange?: (query: ServiceQuery) => void;
  onFilterChange?: (filter: ServiceFilter) => void;
  onExport?: (format: 'excel' | 'pdf') => void;
  onPrint?: () => void;
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
}

export const Prestations_par_Medecin: React.FC<PrestationsParMedecinProps> = ({
  loading = false,
  onQueryChange,
  onFilterChange,
  onExport,
  onPrint,
  onSort
}) => {
  // États locaux
  const [query, setQuery] = useState<ServiceQuery>({
    start: new Date(),
    end: new Date(),
    activityNature: 'RENTING'
  });

  const [filter, setFilter] = useState<ServiceFilter>({
    code: '',
    referredBy: '',
    date: '',
    description: ''
  });

  const [sortConfig, setSortConfig] = useState<{ [key: string]: 'asc' | 'desc' }>({});

  // Configuration des colonnes
  const columns: ServiceColumn[] = [
    { id: 'month_count', label: 'Nombre de mois', sortable: true },
    { id: 'total_amount', label: 'Montant Total', sortable: true }
  ];

  // Options de nature d'activité
  const activityNatureOptions = [
    { value: 'RENTING' as const, label: 'Location' },
    { value: 'SALES' as const, label: 'Vente' }
  ];

  // Gestionnaires d'événements
  const handleQueryChange = (newQuery: Partial<ServiceQuery>) => {
    const updatedQuery = { ...query, ...newQuery };
    setQuery(updatedQuery);
    onQueryChange?.(updatedQuery);
  };

  const handleFilterChange = (newFilter: Partial<ServiceFilter>) => {
    const updatedFilter = { ...filter, ...newFilter };
    setFilter(updatedFilter);
    onFilterChange?.(updatedFilter);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    onExport?.(format);
  };

  const handlePrint = () => {
    console.log('Imprimer');
    onPrint?.();
  };

  const handleSort = (columnId: string) => {
    const currentDirection = sortConfig[columnId];
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    setSortConfig(prev => ({ ...prev, [columnId]: newDirection }));
    onSort?.(columnId, newDirection);
  };

  const getSortIcon = (columnId: string) => {
    const direction = sortConfig[columnId];
    if (!direction) return null;
    return direction === 'asc' ? mdiArrowUp : mdiArrowDown;
  };

  return (
    <Paper shadow="sm" withBorder style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef', backgroundColor: '#e3f2fd' }}>
        <Group justify="space-between" align="center">
          {/* Titre avec icône */}
          <Group gap="md">
            <Icon path={mdiCashMultiple} size={1.2} color="#1976d2" />
            <Text size="lg" fw={500}>Prestations par Médecin</Text>
          </Group>
        </Group>
      </Paper>

      {/* Contrôles */}
      <Paper p="md" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
        <Group align="flex-end" gap="md" wrap="wrap">
          {/* Date picker "Du" */}
          <DateInput
            label="Du"
            value={query.start}
            onChange={(value) => value && handleQueryChange({ start: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          {/* Date picker "Au" */}
          <DateInput
            label="Au"
            value={query.end}
            onChange={(value) => value && handleQueryChange({ end: value as unknown as Date })}
            required
            style={{ width: 200 }}
          />

          {/* Nature de prestation */}
          <Box style={{ marginLeft: 12 }}>
            <Text size="sm" fw={500} mb="xs">Nature de prestation</Text>
            <Radio.Group
              value={query.activityNature}
              onChange={(value) => handleQueryChange({ activityNature: value as 'RENTING' | 'SALES' })}
            >
              <Group>
                {activityNatureOptions.map((option) => (
                  <Radio
                    key={option.value}
                    value={option.value}
                    label={option.label}
                  />
                ))}
              </Group>
            </Radio.Group>
          </Box>
        </Group>
      </Paper>

      {/* Tableau pivot */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box p="xl" style={{ textAlign: 'center' }}>
            <Loader size="lg" />
          </Box>
        ) : (
          <Box style={{ position: 'relative' }}>
            {/* Toolbar */}
            <Paper p="xs" withBorder style={{ borderBottom: '1px solid #e9ecef' }}>
              <Group justify="flex-end" gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={handlePrint}
                  title="Imprimer"
                >
                  <Icon path={mdiPrinter} size={0.8} />
                </ActionIcon>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Exporter">
                      <Icon path={mdiDatabaseExport} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFileExcelOutline} size={0.8} />}
                      onClick={() => handleExport('excel')}
                    >
                      Pour Excel
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFilePdfBox} size={0.8} />}
                      onClick={() => handleExport('pdf')}
                    >
                      PDF
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <Menu shadow="md" width={250}>
                  <Menu.Target>
                    <ActionIcon variant="subtle" title="Format">
                      <Icon path={mdiTableEdit} size={0.8} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatLetterMatches} size={0.8} />}
                    >
                      Format de cellule
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<Icon path={mdiFormatColorHighlight} size={0.8} />}
                    >
                      La mise en forme conditionnelle
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <ActionIcon variant="subtle" title="Champs">
                  <Icon path={mdiTableSettings} size={0.8} />
                </ActionIcon>

                <ActionIcon variant="subtle" title="Options">
                  <Icon path={mdiCog} size={0.8} />
                </ActionIcon>
              </Group>
            </Paper>

            {/* Tableau des données */}
            <ScrollArea style={{ height: 'calc(100vh - 350px)' }}>
              <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead>
                  <Table.Tr>
                    {/* Filtres de lignes */}
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Code</Text>
                    </Table.Th>
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Adressé par</Text>
                    </Table.Th>
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Date</Text>
                    </Table.Th>
                    <Table.Th
                      style={{
                        minWidth: 169,
                        backgroundColor: '#f8f9fa'
                      }}
                    >
                      <Text size="sm" fw={500}>Description</Text>
                    </Table.Th>
                    {/* En-tête "Les totaux" */}
                    <Table.Th
                      style={{
                        minWidth: 150,
                        backgroundColor: '#f8f9fa',
                        textAlign: 'center'
                      }}
                      colSpan={2}
                    >
                      <Text size="sm" fw={500}>Les totaux</Text>
                    </Table.Th>
                    {/* Colonnes vides supplémentaires */}
                    {Array.from({ length: 7 }, (_, index) => (
                      <Table.Th
                        key={`empty-col-${index}`}
                        style={{
                          minWidth: 100,
                          backgroundColor: '#f8f9fa'
                        }}
                      />
                    ))}
                  </Table.Tr>

                  {/* Deuxième ligne d'en-têtes pour les colonnes de données */}
                  <Table.Tr>
                    {/* Cellules vides pour les filtres */}
                    {Array.from({ length: 4 }, (_, index) => (
                      <Table.Th key={`filter-header-${index}`} style={{ backgroundColor: '#f8f9fa' }} />
                    ))}
                    {columns.map((column) => (
                      <Table.Th
                        key={column.id}
                        style={{
                          minWidth: 150,
                          backgroundColor: '#f8f9fa',
                          cursor: column.sortable ? 'pointer' : 'default'
                        }}
                        onClick={() => column.sortable && handleSort(column.id)}
                      >
                        <Group gap="xs" justify="space-between">
                          <Text size="sm" fw={500}>{column.label}</Text>
                          {column.sortable && (
                            <Icon
                              path={getSortIcon(column.id) || mdiArrowUp}
                              size={0.6}
                              style={{
                                opacity: getSortIcon(column.id) ? 1 : 0.3
                              }}
                            />
                          )}
                        </Group>
                      </Table.Th>
                    ))}
                    {/* Cellules vides supplémentaires */}
                    {Array.from({ length: 7 }, (_, index) => (
                      <Table.Th
                        key={`empty-header-${index}`}
                        style={{ backgroundColor: '#f8f9fa' }}
                      />
                    ))}
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  {/* Ligne Total */}
                  <Table.Tr style={{ backgroundColor: '#e8f5e8' }}>
                    <Table.Td style={{ fontWeight: 'bold' }}>
                      <Group gap="xs" justify="space-between">
                        <Text fw={500}>Total</Text>
                        <Icon
                          path={getSortIcon('total') || mdiArrowUp}
                          size={0.6}
                          style={{
                            opacity: getSortIcon('total') ? 1 : 0.3
                          }}
                        />
                      </Group>
                    </Table.Td>
                    {/* Cellules vides pour les filtres */}
                    {Array.from({ length: 3 }, (_, index) => (
                      <Table.Td key={`filter-total-${index}`} />
                    ))}
                    {columns.map((column) => (
                      <Table.Td key={column.id} style={{ textAlign: 'center' }}>
                        <Text size="sm" c="dimmed">-</Text>
                      </Table.Td>
                    ))}
                    {/* Cellules vides */}
                    {Array.from({ length: 7 }, (_, index) => (
                      <Table.Td key={`empty-total-${index}`} />
                    ))}
                  </Table.Tr>

                  {/* Lignes vides pour remplir l'espace */}
                  {Array.from({ length: 20 }, (_, index) => (
                    <Table.Tr key={`empty-row-${index}`}>
                      {Array.from({ length: 13 }, (_, cellIndex) => (
                        <Table.Td key={cellIndex} style={{ height: 30 }} />
                      ))}
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Message d'état vide */}
            <Box
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1
              }}
            >
              <Alert
                icon={<Icon path={mdiAlertCircleOutline} size={1} />}
                title="Aucune donnée disponible"
                color="gray"
                variant="light"
                style={{ maxWidth: 400 }}
              >
                <Text size="sm" c="dimmed">
                  Aucune prestation trouvée pour la période et la nature d'activité sélectionnées.
                  Veuillez vérifier les filtres ou sélectionner une autre période.
                </Text>
              </Alert>
            </Box>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default Prestations_par_Medecin;