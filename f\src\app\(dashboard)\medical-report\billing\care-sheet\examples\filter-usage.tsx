'use client';

import React, { useState } from 'react';
import { 
  QuotationFilters, 
  InvoiceFilters, 
  PaymentFilters, 
  ContractFilters, 
  ExpenseFilters,
  validateFilters,
  cleanFilters
} from '../types/filters';

// Exemple d'utilisation des filtres pour les devis
export function QuotationFilterExample() {
  const [filters, setFilters] = useState<QuotationFilters>({
    dateRange: {
      start: '2025-01-01',
      end: '2025-12-31'
    },
    status: ['draft', 'sent'],
    amount: {
      min: 100,
      max: 5000
    },
    quotationType: ['dental', 'orthodontic'],
    validityStatus: ['valid'],
    acceptanceStatus: ['pending', 'accepted']
  });

  const handleFilterChange = (newFilters: QuotationFilters) => {
    try {
      const validatedFilters = validateFilters(newFilters);
      const cleanedFilters = cleanFilters(validatedFilters);
      setFilters(cleanedFilters as QuotationFilters);
      console.log('Filtres de devis appliqués:', cleanedFilters);
    } catch (error) {
      console.error('Erreur de validation:', error);
    }
  };

  return (
    <div>
      <h3>Filtres pour les Devis</h3>
      <pre>{JSON.stringify(filters, null, 2)}</pre>
      <button onClick={() => handleFilterChange({
        ...filters,
        amount: { min: 200, max: 3000 }
      })}>
        Modifier les montants
      </button>
    </div>
  );
}

// Exemple d'utilisation des filtres pour les factures
export function InvoiceFilterExample() {
  const [filters, setFilters] = useState<InvoiceFilters>({
    dateRange: {
      start: '2025-01-01',
      end: '2025-03-31'
    },
    status: ['sent', 'paid'],
    invoiceType: ['consultation', 'treatment'],
    paymentStatus: ['paid', 'partial'],
    paymentMethod: ['card', 'cash', 'transfer']
  });

  const handleFilterChange = (newFilters: InvoiceFilters) => {
    try {
      const validatedFilters = validateFilters(newFilters);
      const cleanedFilters = cleanFilters(validatedFilters);
      setFilters(cleanedFilters as InvoiceFilters);
      console.log('Filtres de factures appliqués:', cleanedFilters);
    } catch (error) {
      console.error('Erreur de validation:', error);
    }
  };

  return (
    <div>
      <h3>Filtres pour les Factures</h3>
      <pre>{JSON.stringify(filters, null, 2)}</pre>
      <button onClick={() => handleFilterChange({
        ...filters,
        paymentStatus: ['unpaid', 'overdue']
      })}>
        Voir les impayés
      </button>
    </div>
  );
}

// Exemple d'utilisation des filtres pour les règlements
export function PaymentFilterExample() {
  const [filters, setFilters] = useState<PaymentFilters>({
    dateRange: {
      start: '2025-01-01',
      end: '2025-01-31'
    },
    paymentMethod: ['card', 'cash'],
    paymentType: ['full', 'partial'],
    amount: {
      min: 50,
      max: 2000
    }
  });

  const handleFilterChange = (newFilters: PaymentFilters) => {
    try {
      const validatedFilters = validateFilters(newFilters);
      const cleanedFilters = cleanFilters(validatedFilters);
      setFilters(cleanedFilters as PaymentFilters);
      console.log('Filtres de règlements appliqués:', cleanedFilters);
    } catch (error) {
      console.error('Erreur de validation:', error);
    }
  };

  return (
    <div>
      <h3>Filtres pour les Règlements</h3>
      <pre>{JSON.stringify(filters, null, 2)}</pre>
      <button onClick={() => handleFilterChange({
        ...filters,
        paymentMethod: ['transfer', 'check']
      })}>
        Voir virements et chèques
      </button>
    </div>
  );
}

// Exemple d'utilisation des filtres pour les contrats
export function ContractFilterExample() {
  const [filters, setFilters] = useState<ContractFilters>({
    dateRange: {
      start: '2025-01-01',
      end: '2025-12-31'
    },
    contractType: ['subscription', 'insurance'],
    subscriptionStatus: ['active', 'pending'],
    renewalDate: {
      start: '2025-06-01',
      end: '2025-12-31'
    }
  });

  const handleFilterChange = (newFilters: ContractFilters) => {
    try {
      const validatedFilters = validateFilters(newFilters);
      const cleanedFilters = cleanFilters(validatedFilters);
      setFilters(cleanedFilters as ContractFilters);
      console.log('Filtres de contrats appliqués:', cleanedFilters);
    } catch (error) {
      console.error('Erreur de validation:', error);
    }
  };

  return (
    <div>
      <h3>Filtres pour les Contrats</h3>
      <pre>{JSON.stringify(filters, null, 2)}</pre>
      <button onClick={() => handleFilterChange({
        ...filters,
        subscriptionStatus: ['expired', 'cancelled']
      })}>
        Voir contrats expirés
      </button>
    </div>
  );
}

// Exemple d'utilisation des filtres pour les dépenses
export function ExpenseFilterExample() {
  const [filters, setFilters] = useState<ExpenseFilters>({
    dateRange: {
      start: '2025-01-01',
      end: '2025-01-31'
    },
    expenseCategory: ['equipment', 'supplies'],
    expenseType: ['purchase', 'maintenance'],
    amount: {
      min: 10,
      max: 1000
    },
    paymentMethod: ['card', 'transfer'],
    receiptStatus: ['received', 'pending']
  });

  const handleFilterChange = (newFilters: ExpenseFilters) => {
    try {
      const validatedFilters = validateFilters(newFilters);
      const cleanedFilters = cleanFilters(validatedFilters);
      setFilters(cleanedFilters as ExpenseFilters);
      console.log('Filtres de dépenses appliqués:', cleanedFilters);
    } catch (error) {
      console.error('Erreur de validation:', error);
    }
  };

  return (
    <div>
      <h3>Filtres pour les Dépenses</h3>
      <pre>{JSON.stringify(filters, null, 2)}</pre>
      <button onClick={() => handleFilterChange({
        ...filters,
        expenseCategory: ['office', 'marketing']
      })}>
        Voir dépenses bureau/marketing
      </button>
    </div>
  );
}

// Composant principal pour démontrer tous les filtres
export default function FilterUsageDemo() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Exemples d'utilisation des filtres de facturation</h1>
      <p>Ce composant démontre l'utilisation des types de filtres TypeScript pour chaque module de facturation.</p>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '20px', marginTop: '20px' }}>
        <QuotationFilterExample />
        <InvoiceFilterExample />
        <PaymentFilterExample />
        <ContractFilterExample />
        <ExpenseFilterExample />
      </div>
      
      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Avantages des types stricts :</h3>
        <ul>
          <li>✅ <strong>Sécurité des types</strong> - Plus d'erreurs `any`</li>
          <li>✅ <strong>Autocomplétion</strong> - IntelliSense dans l'IDE</li>
          <li>✅ <strong>Validation</strong> - Détection d'erreurs à la compilation</li>
          <li>✅ <strong>Documentation</strong> - Types auto-documentés</li>
          <li>✅ <strong>Refactoring</strong> - Changements sûrs</li>
          <li>✅ <strong>Maintenance</strong> - Code plus maintenable</li>
        </ul>
      </div>
    </div>
  );
}
