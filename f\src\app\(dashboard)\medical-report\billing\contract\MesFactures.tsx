
import React, { useState,  } from 'react';
// import { Search, Plus, X, DollarSign,  } from 'lucide-react';
import { useDisclosure,  } from "@mantine/hooks";
import PatientList from "@/components/agenda/Appointments/PatientList";
import { ActionIcon, Flex, Group, Text, Title, Box,Select,Card,Button,Menu,Table,Input,Divider,Modal} from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiFormatListBulleted ,mdiPageFirst,mdiChevronLeft,mdiChevronRight,mdiPageLast,mdiStethoscope,mdiTooth,mdiMedicalBag,mdiDotsVertical,
  mdiClipboardPulse,mdiCommentText,
} from '@mdi/js';

import ListeDesVisites from "@/components/modals/ListeDesVisites"
import ListeDesVisitesDentaire from "@/components/modals/ListeDesVisitesDentaire"
import Actes from "@/components/modals/Actes"
interface InvoiceDetail {
  id: number;
  code: string;
  description: string;
  quantity: number;
  price: number;
  discount: number;
}
interface Invoice {
  number: string;
  date: string;
  deadline_date: string;
  payment_mode: string;
  beneficiary_type: string;
  beneficiary: {
    full_name: string;
  };
  invoice_details: InvoiceDetail[];
  comment: string;
}
// Update the invoice state type


interface PaginationProps {
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  invoiceNumber: number
}
export const MesFacture  = ({invoiceNumber, total, onPageChange, onLimitChange }: PaginationProps) => {
// export const MesFacture = ({ invoiceNumber }: { invoiceNumber: number }) => {
  const [invoice, setInvoice] = useState<Invoice>({
    number: '',
    date: '',
    deadline_date: '',
    payment_mode: 'Espèce',
    beneficiary_type: 'PATIENT',
    beneficiary: { full_name: '' },
    invoice_details: [],
    comment: ''
  });

  const [showPatientModal, setShowPatientModal] = useState(false);
  const [showMenuOptions, setShowMenuOptions] = useState(false);
  const [ListDesPatientOpened, { open: openListDesPatient, close: closeListDesPatient }] = useDisclosure(false);
const [Organisme, setOrganisme] = useState('');
  const paymentModes = [
    { id: 1, value: 'Aucune' },
    { id: 2, value: 'Chèque' },
    { id: 3, value: 'Espèce' },
    { id: 4, value: 'Traite' },
    { id: 5, value: 'TPE' }
  ];

  const handleInputChange = (field: keyof typeof invoice, value: string | number) => {
    setInvoice(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleBeneficiaryChange = (field: keyof typeof invoice['beneficiary'], value: string) => {
    setInvoice(prev => ({
      ...prev,
      beneficiary: {
        ...prev.beneficiary,
        [field]: value
      }
    }));
  };

  const calculateTotal = () => {
    return invoice.invoice_details.reduce((total, detail: InvoiceDetail) => {
      const amount = (detail.quantity || 0) * (detail.price || 0) - (detail.discount || 0);
      return total + amount;
    }, 0);
  };

  const addInvoiceDetail = (detail: Partial<InvoiceDetail>) => {
    setInvoice(prev => ({
      ...prev,
      invoice_details: [...prev.invoice_details, {
        id: Date.now(),
        code: '',
        description: '',
        quantity: 1,
        price: 0,
        discount: 0,
        ...detail
      } as InvoiceDetail]
    }));
  };

  const removeInvoiceDetail = (id: number) => {
    setInvoice(prev => ({
      ...prev,
      invoice_details: prev.invoice_details.filter(detail => detail.id !== id)
    }));
  };

  const updateInvoiceDetail = (id: number, field: keyof typeof invoice['invoice_details'][number], value: string | number) => {
    setInvoice(prev => ({
      ...prev,
      invoice_details: prev.invoice_details.map(detail => 
        detail.id === id ? { ...detail, [field]: value } : detail
      )
    }));
  };




  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);
  const totalPages = Math.ceil(total / limit);
 const [openedVisites, setOpenedVisites] = useState(false);
  const [openedVisitesDentaire, setOpenedVisitesDentaire] = useState(false);
   const [openedActes, setOpenedActes] = useState(false);
  const handleFirst = () => {
    setPage(1);
    onPageChange(1);
  };

  const handlePrevious = () => {
    const newPage = Math.max(1, page - 1);
    setPage(newPage);
    onPageChange(newPage);
  };

  const handleNext = () => {
    const newPage = Math.min(totalPages, page + 1);
    setPage(newPage);
    onPageChange(newPage);
  };

  const handleLast = () => {
    setPage(totalPages);
    onPageChange(totalPages);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    onLimitChange(newLimit);
    onPageChange(1);
  };

  const start = total === 0 ? 0 : (page - 1) * limit + 1;
  const end = Math.min(page * limit, total);
  return (
    <>
    <header style={{ backgroundColor: '#3799CE', padding: '8px 16px' }}>
      <Flex align="center" justify="space-between">
        <Title order={3}>
          <Text span fw={500} c={"white"}>Facture </Text><Text span fw={700} c={"white"}> N°:</Text> <Text c={"white"}>{invoiceNumber}</Text>
        </Title>

        {/* <Link href="/medical-report/billing/contract?tab=Mes_facuers" passHref> */}
          <Group component="a">
            <ActionIcon variant="subtle" aria-label="Liste des factures">
              <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
            </ActionIcon>
            <Text component='a' href="/medical-report/billing/contract?tab=Mes_facuers" c={"white"}>Liste des factures</Text>
          </Group>
        {/* </Link> */}
      </Flex>
    </header>
    <Card shadow="sm" padding="sm" radius="md" withBorder  >
       <form className="space-y-6">
      <div className='flex w-full'>
     <div className='flex w-[50%] border-2 border-[white] border-r-[#ECEFF1] border-b-[#ECEFF1] pb-4'>
      <div className='flex space-x-6' >
           {/* Numéro de facture */}
            <div className="space-y-2">
              <label >
                <Group>
               <Text fw={500} size='16px' mb={4}>N°. Facture <span className='text-[red]'>*</span></Text>
               </Group>
              </label>
              <Input
               placeholder="9"
                value={invoice.number}
                onChange={(e) => handleInputChange('number', e.target.value)}
                
                readOnly
               styles={{
              input: {
                borderTopWidth: "0",
                // borderRightWidth: "0",
                borderLeftWidth: "0",
               
              },
            }}

              />
            </div>
               {/* Date */}
            <div className="space-y-2">
               <label >
                <Group>
               <Text fw={500} size='16px' mb={4}>Date <span className='text-[red]'>*</span></Text>
               </Group>
              </label>
             
              <div className="relative">
                <Input
                  type="date"
                  value={invoice.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                 
                  required
                   styles={{
              input: {
                borderTopWidth: "0",
                // borderRightWidth: "0",
                borderLeftWidth: "0",
               
              },
            }}
                />
                {/* <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" /> */}
              </div>
              
            </div>
              {/* Date d'échéance */}
            <div className="space-y-2">
            
               <label >
                <Group>
               <Text fw={500} size='16px'mb={4} > Date d&apos;échéance <span className='text-[red]'>*</span></Text>
               </Group>
              </label>
              <div className="relative">
                <Input
                  type="date"
                  value={invoice.deadline_date}
                  onChange={(e) => handleInputChange('deadline_date', e.target.value)}
                 
                  required
                   styles={{
              input: {
                borderTopWidth: "0",
                // borderRightWidth: "0",
                borderLeftWidth: "0",
               
              },
            }}
                />
                {/* <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" /> */}
              </div>
            </div>

            {/* Mode de paiement */}
            <div className="space-y-2">
          
              <label >
                <Group>
               <Text fw={500} size='16px' mb={4}>Mode</Text>
               </Group>
              </label>
              <div className="flex">
                
               <Select
              value={invoice.payment_mode}
              onChange={(value) => handleInputChange('payment_mode', value || '')}
              data={paymentModes.map((mode) => ({
                value: mode.value,
                label: mode.value,
              }))}
              className="w-40"
              styles={{
                input: {
                  borderTopWidth: "0",
                // borderRightWidth: "0",
                borderLeftWidth: "0",
                },
              }}
              
            />

                <button
                  type="button"
                  className="px-3 py-2 border-b border-l-0 border-gray-300 bg-gray-50 hover:bg-gray-100 rounded-r-md transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"  className="text-gray-600 lucide lucide-plus-icon lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                  {/* <Plus size={16} className="text-gray-600" /> */}
                </button>
              </div>
            </div>
      </div>
     </div>
      <div className='flex w-[50%] border-2 border-[white]  border-b-[#ECEFF1]  flex-col pb-4 pl-4 -mt-8'>
          <div className="space-y-3 my-8 ">
              <label >
               <Text fw={500} size='16px' >Bénéficiaire</Text>
              </label>
           
             <div className='flex space-x-6' >
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="beneficiary_type"
                  value="PATIENT"
                  checked={invoice.beneficiary_type === 'PATIENT'}
                  onChange={(e) => handleInputChange('beneficiary_type', e.target.value)}
                  className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Patient</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="beneficiary_type"
                  value="ORGANIZATION"
                  checked={invoice.beneficiary_type === 'ORGANIZATION'}
                  onChange={(e) => handleInputChange('beneficiary_type', e.target.value)}
                  className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Organisme</span>
              </label>
                 <Divider orientation="vertical" />
                 
              {invoice.beneficiary_type === 'PATIENT' && (
            <div className="flex space-x-2 w-full">
             
                <input
                  type="text"
                  value={invoice.beneficiary.full_name}
                  onChange={(e) => handleBeneficiaryChange('full_name', e.target.value)}
                  className="w-[80%] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled
                placeholder='Patient *'
                />
              
              <div className="flex items-end">
                    <ActionIcon variant="filled" size="lg" radius="xs" aria-label="Search" mb={4} onClick={() => openListDesPatient()}>
       {/* <Search size={16} /> */} <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className=" lucide lucide-search-icon lucide-search"><path d="m21 21-4.34-4.34"/><circle cx="11" cy="11" r="8"/></svg>
    </ActionIcon>
                
              </div>
            </div>
             )}
               {invoice.beneficiary_type === 'ORGANIZATION' && (
            <div className="flex space-x-2 w-full">
                {/* <input
                  type="text"
                  value={invoice.beneficiary.full_name}
                  onChange={(e) => handleBeneficiaryChange('full_name', e.target.value)}
                  className="w-[80%] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled
                 placeholder='Organisme *'
                /> */}
               
            <Select
              value={Organisme}
                        onChange={(value) => setOrganisme(value ?? "")}
      placeholder='Organisme *'
      data={['AMO', 'ATLANTA', 'ATLANTA/SANAD', 'AXA','AXA ASSURANCE MAROC','Allianz','Aucune','Autre','BANK AL MAGHREB','BP','CMIM','CNIA SAADA','CNOPS','CNSS','Es Saada','FAR','LYDEC',
        'MAMDA' ,'MAMT','MAROCAINE VIE','MAS','MCMA','MUPRAS','MUTUELLE','NM','OCP','ONCF','ONE','RMA','RMA WATANYA','SAHAM ASSURANCE','SANAD','WAFA ASSURANCE','ZURICH','ZURIKH'
      ]}
      // defaultValue="React"
      clearable
        className="w-[80%]  py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          styles={{
    input: {
     border:"0px"
    },
  }}
    />
            </div>
             )}
     
            </div>
           
          </div>

        
        
        
      </div>
     </div>
       {/* Boutons d'action */}
        <div className="flex justify-end space-x-2 mt-4">
           {/* <Button leftSection={<Icon path={mdiStethoscope} size={1} />} variant="default" radius="xs" 
           color={!Organisme ?'#3799CE' :'#99a1af'}
            style={{marginRight: "1px"}}
             disabled={!Organisme}>
       Visites
      </Button> */}
      <Button
        leftSection={<Icon path={mdiStethoscope} size={1} />}
        variant="default"
        radius="xs"
        disabled={!Organisme}
        styles={{
          root: {
            marginRight: 1,
            backgroundColor: !Organisme ? '#e0e0e0' : '#3799CE',
            color: !Organisme ? '#6c757d' : ' white',
            cursor: !Organisme ? ' not-allowed' : 'pointer',
          },
        }}
        onClick={() => setOpenedVisites(true)}
      >
        Visites
      </Button>

            <Button leftSection={<Icon path={mdiTooth} size={1} />} variant="default"radius="xs" color='#99a1af' style={{marginRight: "1px"}}
            disabled={!Organisme}
            styles={{
          root: {
            marginRight: 1,
            backgroundColor: !Organisme ? '#e0e0e0' : '#3799CE',
            color: !Organisme ? '#6c757d' : ' white',
            cursor: !Organisme ? ' not-allowed' : 'pointer',
          },
        }}
        onClick={() => setOpenedVisitesDentaire(true)}
            >
      Visites dentaire
      </Button>
      <Button leftSection={<Icon path={mdiMedicalBag} size={1} />} variant="default"radius="xs" color='#99a1af' style={{marginRight: "1px"}}
            disabled={!Organisme}
            styles={{
          root: {
            marginRight: 1,
            backgroundColor: !Organisme ? '#e0e0e0' : '#3799CE',
            color: !Organisme ? '#6c757d' : ' white',
            cursor: !Organisme ? ' not-allowed' : 'pointer',
          },
        }}
        onClick={() => setOpenedActes(true)}
            >
      Actes
      </Button>
    
         <Menu shadow="md" width={200}>
      <Menu.Target>
         <ActionIcon variant="filled" size="lg" radius="xs" aria-label="Menu">
       <Icon path={mdiDotsVertical} size={1} />
    </ActionIcon>
      
      </Menu.Target>

      <Menu.Dropdown>
     
        <Menu.Item leftSection={<Icon path={mdiClipboardPulse} size={1} />} 
            disabled={!invoice.beneficiary.full_name || invoice.beneficiary_type !== 'PATIENT'}
        onClick={() => {
          console.log('Plan de soins');
          setShowMenuOptions(false);
        }}>
         Plan de soins
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiTooth} size={1} />} 
        disabled={!invoice.beneficiary.full_name}
       
         onClick={() => {
              console.log('Plan de soins');
              setShowMenuOptions(false);
            }}>
        Plan de traitement
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiCommentText} size={1} />} 
        disabled={!invoice.beneficiary.full_name}
        onClick={() => {
        addInvoiceDetail({ description: 'Commentaire', code: 'COMMENT' });
        setShowMenuOptions(false);
      }}>
       Commentaire
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>

          {/* <div className="relative">
            <button
              type="button"
              onClick={() => setShowMenuOptions(!showMenuOptions)}
              className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors flex items-center"
            >
              <MoreVertical size={16} />
            </button>

            {showMenuOptions && (
              <div className="absolute right-0 top-full mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-2">
                  <MenuButton
                    icon={Clipboard}
                    label="Plan de soins"
                    disabled={!invoice.beneficiary.full_name || invoice.beneficiary_type !== 'PATIENT'}
                    onClick={() => {
                      console.log('Plan de soins');
                      setShowMenuOptions(false);
                    }}
                  />
                  <MenuButton
                    icon={FileText}
                    label="Plan de traitement"
                    disabled={!invoice.beneficiary.full_name}
                    onClick={() => {
                      console.log('Plan de traitement');
                      setShowMenuOptions(false);
                    }}
                  />
                  <MenuButton
                    icon={MessageSquare}
                    label="Commentaire"
                    disabled={!invoice.beneficiary.full_name}
                    onClick={() => {
                      addInvoiceDetail({ description: 'Commentaire', code: 'COMMENT' });
                      setShowMenuOptions(false);
                    }}
                  />
                </div>
              </div>
            )}
          </div> */}
        </div>
         {/* Tableau des détails */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
             <Table striped highlightOnHover withTableBorder withColumnBorders>
              <Table.Thead className="border-2 border-[#F9FAFB] border-b-[#3799CE]">
                <Table.Tr >
                  <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}>Code</Text> 
                  </Table.Th>
                  <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}>Description</Text> 
                  </Table.Th>
                  <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}>Qté</Text> 
                  </Table.Th>
                   <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}>Prix</Text> 
                  </Table.Th>
                     <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}>Remise</Text> 
                  </Table.Th>
                    <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}> Montant</Text> 
                  </Table.Th>
                   <Table.Th >
                    <Text fw={600} size="14px" ta="left" ml={8}> </Text> 
                  </Table.Th>
                   
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody className="bg-white ">
                {invoice.invoice_details.length === 0 ? (
                  <Table.Tr>
                     <Table.Td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                      Aucun élément trouvé.
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  invoice.invoice_details.map((detail) => (
                    <Table.Tr key={detail.id} className="hover:bg-gray-50">
                      <Table.Td className="px-4 py-3">
                        <input
                          type="text"
                          value={detail.code || ''}
                          onChange={(e) => updateInvoiceDetail(detail.id, 'code', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3">
                        <input
                          type="text"
                          value={detail.description || ''}
                          onChange={(e) => updateInvoiceDetail(detail.id, 'description', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-right">
                        <input
                          type="number"
                          value={detail.quantity || ''}
                          onChange={(e) => updateInvoiceDetail(detail.id, 'quantity', parseFloat(e.target.value) || 0)}
                          className="w-20 px-2 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          min="0"
                          step="0.01"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-right">
                        <input
                          type="number"
                          value={detail.price || ''}
                          onChange={(e) => updateInvoiceDetail(detail.id, 'price', parseFloat(e.target.value) || 0)}
                          className="w-24 px-2 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          min="0"
                          step="0.01"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-right">
                        <input
                          type="number"
                          value={detail.discount || ''}
                          onChange={(e) => updateInvoiceDetail(detail.id, 'discount', parseFloat(e.target.value) || 0)}
                          className="w-24 px-2 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          min="0"
                          step="0.01"
                        />
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-right font-medium">
                        {((detail.quantity || 0) * (detail.price || 0) - (detail.discount || 0)).toFixed(2)}
                      </Table.Td>
                      <Table.Td className="px-4 py-3 text-center">
                        <button
                          type="button"
                          onClick={() => removeInvoiceDetail(detail.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className=" lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                          {/* <X size={16} /> */}
                        </button>
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>
          </div>

          {/* Total */}
          <div className="bg-gray-50 px-4 py-3 flex justify-between items-center ">
            <span className="text-sm text-gray-600">Total :</span>
            <span className="text-lg font-semibold text-gray-900">
              {calculateTotal().toFixed(2)} €
            </span>
          </div>
 
    
          </div>
          {/* Navigation Buttons */}
         <Group justify="flex-end" p={12}>
       
      <Group>
        <Box display="flex"  mr={4} w={"20%"}>
        <Text  mr={8} mt={6}>Page</Text>
        <Select
          value={String(page)}
          onChange={(value) => {
            const newPage = Number(value || '1');
            setPage(newPage);
            onPageChange(newPage);
          }}
          disabled={total === 0}
          data={Array.from({ length: totalPages }, (_, i) => ({
            value: String(i + 1),
            label: String(i + 1)
          }))}
          w={'80px'}
        />
      </Box>
            <Box display="flex"  mr={4}>
        <Text mr={8} mt={6}>Lignes par Page</Text>
        <Select
  value={String(limit)}
  onChange={(value) => handleLimitChange(Number(value || '5'))}
  data={[5, 10, 20].map(option => ({
    value: String(option),
    label: String(option)
  }))}
  w={'80px'}
/>
      </Box>
      <Box display="flex" className='space-x-4 '>
      <ActionIcon variant="filled" aria-label="Settings"
      onClick={handleFirst}
      disabled={page === 1 || total === 0}
      >
       <Icon path={mdiPageFirst} size={1} />
        </ActionIcon>
        <ActionIcon variant="filled" aria-label="Settings" onClick={handlePrevious}
          disabled={page === 1 || total === 0}>
       <Icon path={mdiChevronLeft} size={1} />
        </ActionIcon>
      
        
        <Text variant="body2" mx={2}>
          {start} - {end} de {total}
        </Text>
        <ActionIcon variant="filled" aria-label="Settings" onClick={handleNext}
          disabled={page === totalPages || total === 0}>
       
       <Icon path={mdiChevronRight} size={1} />
          </ActionIcon>
        <ActionIcon variant="filled"
          onClick={handleLast}
          disabled={page === totalPages || total === 0}
          
        >
         <Icon path={mdiPageLast} size={1} />
          </ActionIcon>
      </Box>
     
      </Group>
    </Group>
     {/* Commentaire */}
        <div className="space-y-2">
          <label>
            <Text fw={500} size="16px"  ml={8} c={"gray"} mb={10}>Commentaire</Text>  
          </label>
          <textarea
            value={invoice.comment}
            onChange={(e) => handleInputChange('comment', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y"
            placeholder="Ajouter un commentaire..."
          />
        </div>
         {/* Boutons de soumission */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-t-[#ECEFF1]">
          <Button
            type="button"
           radius="xs"
           color="red"
          >
            Annuler
          </Button>
          <Button
            type="submit"
            radius="xs"
              disabled={!Organisme}
            styles={{
          root: {
            marginRight: 1,
            backgroundColor: !Organisme ? '#e0e0e0' : '#3799CE',
            color: !Organisme ? '#6c757d' : ' white',
            cursor: !Organisme ? ' not-allowed' : 'pointer',
          },
        }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-dollar-sign-icon lucide-dollar-sign"><line x1="12" x2="12" y1="2" y2="22"/><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg>
            {/* <DollarSign size={16} /> */}
            <span>Enregistrer et quitter</span>
          </Button>
           <Button
            type="submit"
            radius="xs"
              disabled={!Organisme}
            styles={{
          root: {
            marginRight: 1,
            backgroundColor: !Organisme ? '#e0e0e0' : '#3799CE',
            color: !Organisme ? '#6c757d' : ' white',
            cursor: !Organisme ? ' not-allowed' : 'pointer',
          },
        }}
          >
            {/* <DollarSign size={16} /> */}
             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-dollar-sign-icon lucide-dollar-sign"><line x1="12" x2="12" y1="2" y2="22"/><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg>
            <span>Sauvegarder</span>
          </Button>
        </div>
            </form>
    </Card>
     <div className="max-w-6xl mx-auto p-6 bg-white">
   

      {/* Overlay pour fermer les menus */}
      {(showMenuOptions || showPatientModal) && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-5"
          onClick={() => {
            setShowMenuOptions(false);
            setShowPatientModal(false);
          }}
        />
      )}

      {/* Modal de recherche patient (placeholder) */}
      {showPatientModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Rechercher un patient</h3>
            <input
              type="text"
              placeholder="Nom du patient..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"
            />
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowPatientModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => setShowPatientModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Sélectionner
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
     {/*   Show Patient List */}
                 <Modal.Root opened={ListDesPatientOpened} onClose={closeListDesPatient}   size="100%">
                <PatientList/>
                 </Modal.Root>
        {/*   Show Modal Visites */}
                  <Modal.Root opened={openedVisites} onClose={() => setOpenedVisites(false)}  size="70%" yOffset="30vh" xOffset={0} >
                    <Modal.Overlay />
                 <ListeDesVisites
                opened={openedVisites}
                onClose={() => setOpenedVisites(false)}
                beneficiaryType="ORGANIZATION"
              />
              </Modal.Root>
        {/*   Show Modal Visites  dentaire */}
                   <Modal.Root opened={openedVisitesDentaire} onClose={() => setOpenedVisitesDentaire(false)} 
                    size="xl" yOffset="30vh" xOffset={0} >
                    <Modal.Overlay />
                 <ListeDesVisitesDentaire
                opened={openedVisitesDentaire}
                onClose={() => setOpenedVisitesDentaire(false)}
                beneficiaryType="ORGANIZATION"
              />
              </Modal.Root> 
        {/*   Show Modal Actes */}
                  <Modal.Root opened={openedActes} onClose={() => setOpenedActes(false)}   yOffset="30vh" xOffset={0}       
                   size="xl"
                  padding={0}
                  className="max-h-[90vh]">
                    <Modal.Overlay />
                 <Actes
                onClose={() => setOpenedActes(false)}
              />
              </Modal.Root>
    </> // Add closing fragment tag  
  );
}
