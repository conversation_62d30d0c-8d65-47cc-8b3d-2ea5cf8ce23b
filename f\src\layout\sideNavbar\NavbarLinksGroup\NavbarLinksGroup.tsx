import { useState, useEffect } from 'react';
import { IconCalendarStats, IconChevronRight, IconProps } from '@tabler/icons-react';
import { Box, Collapse, Group,  ThemeIcon, UnstyledButton } from '@mantine/core';
import Link from 'next/link';
import { IconType } from 'react-icons';
import classes from './NavbarLinksGroup.module.css';

interface LinksGroupProps {
  key?: string;
  icon: React.ComponentType<IconProps> | IconType;
  label: string;
  initiallyOpened?: boolean;
  links?: {
    label: string;
    link?: string;
    icon?: React.ComponentType<IconProps> | IconType;
    isHeader?: boolean; // Pour identifier les en-têtes de section
  }[];
  link?: string;
}

export function LinksGroup({ icon: Icon, label, initiallyOpened, links, link }: LinksGroupProps) {
  const hasLinks = Array.isArray(links);
  const [opened, setOpened] = useState(initiallyOpened || false);
  const [currentPath, setCurrentPath] = useState('');
  const [, setIsClient] = useState(false);

  // Effet pour détecter le côté client
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Mettre à jour le chemin actuel côté client avec un polling pour Next.js
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const updatePath = () => {
        const newPath = window.location.pathname + window.location.search;
        if (newPath !== currentPath) {
          setCurrentPath(newPath);
        }
      };

      // Mettre à jour immédiatement
      updatePath();

      // Écouter les changements de navigation (bouton retour/avant)
      window.addEventListener('popstate', updatePath);

      // Pour détecter les changements de navigation programmatiques
      const originalPushState = history.pushState;
      const originalReplaceState = history.replaceState;

      history.pushState = function(...args) {
        originalPushState.apply(history, args);
        setTimeout(updatePath, 0); // Délai pour s'assurer que l'URL est mise à jour
      };

      history.replaceState = function(...args) {
        originalReplaceState.apply(history, args);
        setTimeout(updatePath, 0);
      };

      // Polling pour capturer les changements Next.js qui pourraient être manqués
      const interval = setInterval(updatePath, 100);

      // Nettoyer les event listeners
      return () => {
        window.removeEventListener('popstate', updatePath);
        history.pushState = originalPushState;
        history.replaceState = originalReplaceState;
        clearInterval(interval);
      };
    }
  }, [currentPath]);

  // Fonction pour vérifier si un lien est actif
  const isLinkActive = (linkUrl: string) => {
    if (!linkUrl || !currentPath) return false;

    // Si c'est un lien avec des query parameters
    if (linkUrl.includes('?')) {
      const [pathname, queryString] = linkUrl.split('?');
      const urlParams = new URLSearchParams(queryString);
      const currentParams = new URLSearchParams(currentPath.split('?')[1] || '');

      // Vérifier si le pathname correspond
      if (!currentPath.startsWith(pathname)) return false;

      // Vérifier si les paramètres correspondent
      for (const [key, value] of urlParams.entries()) {
        if (currentParams.get(key) !== value) return false;
      }

      return true;
    }

    // Pour les liens simples
    return currentPath === linkUrl || currentPath.startsWith(linkUrl);
  };
  // Vérifier si l'un des sous-liens est actif
  const hasActiveSubLink = hasLinks && links?.some(linkItem =>
    linkItem.link && isLinkActive(linkItem.link)
  );

  const items = (hasLinks ? links : []).map((linkItem, index) => {
    const LinkIcon = linkItem.icon;

    // Si c'est un en-tête de section
    if (linkItem.isHeader) {
      return (
        <div
          key={index}
          className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200 bg-gray-50"
        >
          {linkItem.label}
        </div>
      );
    }

    // Si c'est un lien normal
    if (linkItem.link) {
      const isActive = isLinkActive(linkItem.link);

      return (
        <Link href={linkItem.link} key={index} style={{ textDecoration: 'none' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              color: isActive ? '#1976d2' : '#374151', // Bleu quand actif, gris sinon
              fontWeight: isActive ? 600 : 500, // Plus gras quand actif
              backgroundColor: isActive ? '#e3f2fd' : 'transparent', // Arrière-plan bleu clair quand actif
              borderRadius: '4px',
              padding: '8px 12px',
              marginLeft: '24px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              borderLeft: '1px solid #e5e7eb'
            }}
            onMouseEnter={(e) => {
              if (!isActive) {
                e.currentTarget.style.backgroundColor = '#f3f4f6';
              }
            }}
            onMouseLeave={(e) => {
              if (!isActive) {
                e.currentTarget.style.backgroundColor = 'transparent';
              }
            }}
          >
            {LinkIcon && (
              <ThemeIcon
                variant="light"
                size={20}
                color={isActive ? 'blue' : 'gray'}
              >
                <LinkIcon size={14} />
              </ThemeIcon>
            )}
            {linkItem.label}
          </div>
        </Link>
      );
    }

    // Si c'est juste un texte sans lien
    return (
      <div
        key={index}
        className="px-3 py-2 text-sm text-gray-600"
        style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
      >
        {LinkIcon && (
          <ThemeIcon variant="light" size={20}>
            <LinkIcon size={14} />
          </ThemeIcon>
        )}
        {linkItem.label}
      </div>
    );
  });

  const handleClick = () => {
    if (hasLinks) {
      setOpened((o) => !o);
    } else if (link) {
      // Navigate to the direct link
      window.location.href = link;
    }
  };

  return (
    <>
      <UnstyledButton
        onClick={handleClick}
        className={classes.control}
        style={{
          backgroundColor: hasActiveSubLink ? '#e3f2fd' : undefined,
          borderRadius: '4px'
        }}
      >
        <Group justify="space-between" gap={0}>
          <Box style={{ display: 'flex', alignItems: 'center' }}>
            <ThemeIcon
              variant="light"
              size={30}
              color={hasActiveSubLink ? 'blue' : undefined}
            >
              <Icon size={18} />
            </ThemeIcon>
            <Box
              ml="md"
              style={{
                color: hasActiveSubLink ? '#1976d2' : undefined,
                fontWeight: hasActiveSubLink ? 600 : undefined
              }}
            >
              {label}
            </Box>
          </Box>
          {hasLinks && (
            <IconChevronRight
              className={classes.chevron}
              stroke={1.5}
              size={16}
              style={{
                transform: opened ? 'rotate(-90deg)' : 'none',
                color: hasActiveSubLink ? '#1976d2' : undefined
              }}
            />
          )}
        </Group>
      </UnstyledButton>
      {hasLinks ? <Collapse in={opened}>{items}</Collapse> : null}
    </>
  );
}

const mockdata = {
  label: 'Releases',
  icon: IconCalendarStats,
  links: [
    { label: 'Upcoming releases', link: '/' },
    { label: 'Previous releases', link: '/' },
    { label: 'Releases schedule', link: '/' },
  ],
};

export function NavbarLinksGroup() {
  return (
    <Box mih={220} p="md">
      <LinksGroup {...mockdata} />
    </Box>
  );
}
