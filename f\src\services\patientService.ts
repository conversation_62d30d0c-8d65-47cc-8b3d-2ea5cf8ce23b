/**
 * Patient Service - API communication with Django backend
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api/users';

export interface PatientData {
  // Personal Information
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  date_of_birth: string | null;
  gender: string;

  // Additional Personal Info
  nationality: string;
  spoken_languages: string;
  cine: string;
  profession: string;
  attending_physician: string;

  // Address Information
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;

  // Emergency Contact
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;

  // Medical Information
  medical_conditions: string;
  allergies: string;
  medications: string;
  blood_type: string;
  height: string;
  weight: string;

  // File Information
  file_number?: number;
  category?: string;
  pricing?: number;
  is_bookmarked?: boolean;
  insured?: boolean;
  description?: string;
  titre?: string;
}

export interface PatientResponse {
  success: boolean;
  message: string;
  patient_id?: string;
  medical_record_number?: string;
  full_name?: string;
  admin_url?: string;
  created_at?: string;
  errors?: Record<string, string[]>;
}

export interface PatientInsurance {
  id?: string;
  patient?: string;
  provider_name: string;
  policy_number: string;
  group_number?: string;
  coverage_type?: string;
  effective_date?: string;
  expiry_date?: string;
  dental_coverage: boolean;
  medical_coverage: boolean;
  copay_amount?: number;
  deductible_amount?: number;
  provider_phone?: string;
  provider_website?: string;
  is_active: boolean;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PatientAlert {
  id?: string;
  patient?: string;
  trigger: string;
  trigger_custom?: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
  is_active: boolean;
  trigger_for: Array<{
    id: string;
    name: string;
    user_type: string;
  }>;
  triggered_count?: number;
  last_triggered?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: {
    id: string;
    name: string;
  };
}

export interface PatientAttachment {
  id?: string;
  patient?: string;
  original_filename: string;
  file_size: number;
  file_size_mb?: number;
  mime_type: string;
  attachment_type: 'document' | 'image' | 'audio' | 'video' | 'dicom' | 'other';
  category: 'medical_record' | 'lab_result' | 'imaging' | 'prescription' | 'insurance' | 'identification' | 'consent' | 'other';
  title?: string;
  description?: string;
  tags?: string;
  is_private: boolean;
  is_sensitive: boolean;
  is_processed?: boolean;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  medical_date?: string;
  file_extension?: string;
  download_url?: string;
  created_at?: string;
  updated_at?: string;
  uploaded_by?: {
    id: string;
    name: string;
  };
}

export interface MedicalDataCategory {
  id: string;
  name: string;
  category_type: 'allergy' | 'medication' | 'condition' | 'treatment' | 'pathology' | 'symptom' | 'procedure' | 'other';
  description?: string;
}

export interface MedicalDataItem {
  id: string;
  name: string;
  description?: string;
  category: MedicalDataCategory;
  severity_level?: 'mild' | 'moderate' | 'severe' | 'critical';
  medical_code?: string;
  is_common: boolean;
  synonyms: string[];
}

export interface PatientMedicalData {
  id: string;
  medical_item: MedicalDataItem;
  notes?: string;
  severity?: 'mild' | 'moderate' | 'severe' | 'critical';
  start_date?: string;
  end_date?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface BiometricMeasureDefinition {
  id: string;
  name: string;
  label: string;
  unit?: string;
  measurement_type: 'float' | 'integer' | 'boolean' | 'string' | 'date' | 'calculated';
  category: 'vital_signs' | 'body_measurements' | 'cardiovascular' | 'respiratory' | 'neurological' | 'laboratory' | 'other';
  min_value?: number;
  max_value?: number;
  is_required: boolean;
  display_order: number;
  calculation_formula?: string;
  description?: string;
}

export interface PatientBiometricMeasurement {
  id: string;
  measure_definition: BiometricMeasureDefinition;
  measurement_date: string;
  value: number | string | boolean | Date; // Can be number, string, boolean, or date depending on measurement_type
  notes?: string;
  is_abnormal: boolean;
  is_within_normal_range?: boolean;
  measured_by?: {
    id: string;
    name: string;
  };
  device_used?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface Patient {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  age?: string;
  gender?: string;
  national_id_number?: string;
  passport_number?: string;
  marital_status?: string;
  phone_number?: string;
  address?: string;
  medical_history?: string;
  doctor_assigned?: string;
  visit_type?: string;
  visit_duration?: string;
  agenda?: string;
  comments?: string;
  diagnostic_room?: string;
  insurance_company?: string;
  insurance_policy_number?: string;
  additional_notes?: string;
  is_active?: boolean;
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface MedicalRecord {
  id: string;
  patient_id: string;
  doctor: {
    id: string;
    first_name: string;
    last_name: string;
    specialization: string;
  };
  title: string;
  description: string;
  date: string;
  record_type: string;
  attachments: Attachment[];
  created_at: string;
  updated_at: string;
}

export interface Attachment {
  id: string;
  name: string;
  file_url: string;
  file_type: string;
  created_at: string;
}

export interface Medication {
  id: string;
  patient_id: string;
  name: string;
  dosage: string;
  frequency: string;
  start_date: string;
  end_date: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LabResult {
  id: string;
  patient_id: string;
  doctor: {
    id: string;
    first_name: string;
    last_name: string;
    specialization: string;
  };
  test_name: string;
  test_date: string;
  result: string;
  normal_range: string;
  units: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

export interface PatientRelation {
  id: string;
  patient_id: string;
  related_patient_id?: string;
  related_patient_name?: string;
  relation_type: 'PARENT' | 'CHILD' | 'SIBLING' | 'SPOUSE' | 'COUSIN' | 'AUNT_UNCLE' | 'NIECE_NEPHEW' | 'GREAT_PARENT' | 'GRAND_CHILD' | 'EMPLOYER' | 'NURSE';
  notes?: string;
  is_emergency_contact: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreatePatientRelationData {
  related_patient_name: string;
  relation_type: string;
  notes?: string;
  is_emergency_contact?: boolean;
}

// Define interfaces for API parameters (if not already defined)
export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

export interface ApiData {
  [key: string]: unknown;
}

// Helper function to make API calls with fallback
const makeApiCall = async <T = unknown>(
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: ApiData,
  params?: ApiParams
): Promise<T> => {
  try {
    // Try Django API first
    const djangoEndpoint = endpoint.replace('/api/patients/', '/patients/');
    const options: RequestInit = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data && method !== 'get') {
      options.body = JSON.stringify(data);
    }

    // Add query parameters for GET requests
    let url = `${DJANGO_API_BASE_URL}${djangoEndpoint}`;
    if (params && method === 'get') {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.warn(`⚠️ Django API call failed for ${endpoint}:`, error);

    // Fallback to mock data for GET requests
    if (method === 'get') {
      console.log(`📋 Using mock data for ${endpoint}`);
      return getMockDataForEndpoint(endpoint) as T;
    }

    // For non-GET requests, throw the error
    throw new Error(`API call failed for ${endpoint}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Django API Base URL
const DJANGO_API_BASE_URL = 'http://127.0.0.1:8000/api/users';

// Mock data fallback function
const getMockDataForEndpoint = (endpoint: string): { results: Patient[] } | Patient[] => {
  if (endpoint.includes('/api/patients/')) {
    // Return mock patients data
    return {
      results: [
        {
          id: '1',
          first_name: 'Jean',
          last_name: 'Dupont',
          email: '<EMAIL>',
          phone_number: '+33123456789',
          date_of_birth: '1985-05-15',
          gender: 'M',
          address: '123 Rue de la Paix, Paris',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          first_name: 'Marie',
          last_name: 'Martin',
          email: '<EMAIL>',
          phone_number: '+33987654321',
          date_of_birth: '1990-08-22',
          gender: 'F',
          address: '456 Avenue des Champs, Lyon',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ]
    };
  }

  // Default empty response
  return { results: [] };
};

// Helper function for Django API calls
const makeDjangoApiCall = async (endpoint: string, options: RequestInit = {}): Promise<Response> => {
  const url = `${DJANGO_API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    return response;
  } catch (error) {
    console.error('Network error:', error);
    throw new Error('Network error. Please check your connection.');
  }
};

const patientService = {
  async getPatients(params?: ApiParams): Promise<Patient[]> {
    try {
      const data = await makeApiCall<{ results?: Patient[] } | Patient[]>('/api/patients/', 'get', undefined, params);
      return (Array.isArray(data) ? data : data.results) || [];
    } catch (error) {
      console.error('Error fetching patients:', error);
      // Instead of returning mock data, throw the error to let the UI handle it
      throw new Error('Failed to fetch patients from server. Please check your connection and try again.');
    }
  },

  async getPatient(id: string): Promise<Patient | null> {
    try {
      return await makeApiCall(`/api/patients/${id}/`);
    } catch (error) {
      console.error(`Error fetching patient ${id}:`, error);
      throw new Error(`Failed to fetch patient ${id} from server. Please check your connection and try again.`);
    }
  },

  async createPatient(data: Partial<Patient>): Promise<Patient> {
    try {
      return await makeApiCall('/api/patients/', 'post', data);
    } catch (error) {
      console.error('Error creating patient:', error);
      throw new Error('Failed to create patient. Please check your data and try again.');
    }
  },

  async updatePatient(id: string, data: Partial<Patient>): Promise<Patient> {
    try {
      return await makeApiCall(`/api/patients/${id}/`, 'patch', data);
    } catch (error) {
      console.error(`Error updating patient ${id}:`, error);
      throw new Error(`Failed to update patient ${id}. Please check your data and try again.`);
    }
  },

  async getMedicalRecords(patientId: string, params?: ApiParams): Promise<MedicalRecord[]> {
    try {
      const data = await makeApiCall<{ results?: MedicalRecord[] }>(`/api/patients/${patientId}/medical-records/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching medical records for patient ${patientId}:`, error);
      return [];
    }
  },

  async getMedicalRecord(patientId: string, recordId: string): Promise<MedicalRecord | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medical-records/${recordId}/`);
    } catch (error) {
      console.error(`Error fetching medical record ${recordId} for patient ${patientId}:`, error);
      return null;
    }
  },

  async createMedicalRecord(patientId: string, data: Partial<MedicalRecord>): Promise<MedicalRecord | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medical-records/`, 'post', data);
    } catch (error) {
      console.error(`Error creating medical record for patient ${patientId}:`, error);
      return null;
    }
  },

  async getMedications(patientId: string, params?: ApiParams): Promise<Medication[]> {
    try {
      const data = await makeApiCall<{ results?: Medication[] }>(`/api/patients/${patientId}/medications/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching medications for patient ${patientId}:`, error);
      return [];
    }
  },

  async createMedication(patientId: string, data: Partial<Medication>): Promise<Medication | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medications/`, 'post', data);
    } catch (error) {
      console.error(`Error creating medication for patient ${patientId}:`, error);
      return null;
    }
  },

  async updateMedication(patientId: string, medicationId: string, data: Partial<Medication>): Promise<Medication | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medications/${medicationId}/`, 'patch', data);
    } catch (error) {
      console.error(`Error updating medication ${medicationId} for patient ${patientId}:`, error);
      return null;
    }
  },

  async getLabResults(patientId: string, params?: ApiParams): Promise<LabResult[]> {
    try {
      const data = await makeApiCall<{ results?: LabResult[] }>(`/api/patients/${patientId}/lab-results/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching lab results for patient ${patientId}:`, error);
      return [];
    }
  },

  async createLabResult(patientId: string, data: Partial<LabResult>): Promise<LabResult | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/lab-results/`, 'post', data);
    } catch (error) {
      console.error(`Error creating lab result for patient ${patientId}:`, error);
      return null;
    }
  },

  // Django Backend Integration Methods
  async createPatientInDjango(patientData: PatientData): Promise<PatientResponse> {
    try {
      console.log('🏥 Creating patient in Django with data:', patientData);

      const response = await makeDjangoApiCall('/patients/create-from-frontend/', {
        method: 'POST',
        body: JSON.stringify(patientData),
      });

      const result = await response.json();

      if (response.ok) {
        console.log('✅ Patient created successfully in Django:', result);
        return result;
      } else {
        console.error('❌ Failed to create patient in Django:', result);
        return {
          success: false,
          message: result.message || 'Failed to create patient',
          errors: result.errors,
        };
      }
    } catch (error) {
      console.error('❌ Error creating patient in Django:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  },

  async checkDjangoBridgeStatus(): Promise<{ status: string; message: string }> {
    try {
      const response = await makeDjangoApiCall('/patients/bridge-status/');

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Django Bridge API not responding');
      }
    } catch (error) {
      console.error('❌ Django bridge status check failed:', error);
      return {
        status: 'error',
        message: 'Django Bridge API not available',
      };
    }
  },

  openPatientInDjangoAdmin(adminUrl: string): void {
    const fullUrl = `http://127.0.0.1:8000${adminUrl}`;
    window.open(fullUrl, '_blank');
  },

  /**
   * Map frontend gender values to backend expected values
   */
  mapGenderForBackend(frontendGender: string): string {
    const genderMapping: { [key: string]: string } = {
      'Male': 'M',
      'Female': 'F',
      'Homme': 'Homme',
      'Femme': 'Femme',
      'Enfant': 'Enfant',
      'Autre': 'Autre',
      'Other': 'Other',
      'Prefer not to say': 'Prefer not to say',
      // Keep existing values as-is
      'M': 'M',
      'F': 'F'
    };

    return genderMapping[frontendGender] || frontendGender;
  },

  formatPatientDataForDjango(
    personalData: Record<string, unknown>,
    addressData: Record<string, unknown>,
    emergencyData: Record<string, unknown>,
    medicalData: Record<string, unknown>,
    ficheData: Record<string, unknown>
  ): PatientData {
    return {
      // Personal Information
      first_name: String(personalData.first_name || ''),
      last_name: String(personalData.last_name || ''),
      email: String(personalData.email || ''),
      phone_number: String(personalData.phone_number || ''),
      date_of_birth: personalData.date_of_birth && personalData.date_of_birth instanceof Date
        ? personalData.date_of_birth.toISOString().split('T')[0]
        : null,
      gender: this.mapGenderForBackend(String(personalData.gender || '')),

      // Additional Personal Info
      nationality: String(personalData.nationality || ''),
      spoken_languages: String(personalData.spoken_languages || ''),
      cine: String(personalData.cine || ''),
      profession: String(personalData.profession || ''),
      attending_physician: String(personalData.attending_physician || ''),

      // Address Information
      address: String(addressData.address || ''),
      city: String(addressData.city || ''),
      state: String(addressData.state || ''),
      zip_code: String(addressData.zip_code || ''),
      country: String(addressData.country || ''),

      // Emergency Contact
      emergency_contact_name: String(emergencyData.emergency_contact_name || ''),
      emergency_contact_phone: String(emergencyData.emergency_contact_phone || ''),
      emergency_contact_relationship: String(emergencyData.emergency_contact_relationship || ''),

      // Medical Information
      medical_conditions: String(medicalData.medical_conditions || ''),
      allergies: String(medicalData.allergies || ''),
      medications: String(medicalData.medications || ''),
      blood_type: String(medicalData.blood_type || ''),
      height: String(medicalData.height || ''),
      weight: String(medicalData.weight || ''),

      // File Information
      file_number: Number(ficheData.file_number) || 0,
      category: String(ficheData.category || ''),
      pricing: Number(ficheData.pricing) || 0,
      is_bookmarked: Boolean(ficheData.is_bookmarked),
      insured: Boolean(ficheData.insured),
      description: String(ficheData.description || ''),
      titre: String(ficheData.titre || ''),
    };
  },

  validatePatientData(data: PatientData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!data.first_name?.trim()) {
      errors.push('First name is required');
    }
    if (!data.last_name?.trim()) {
      errors.push('Last name is required');
    }
    if (!data.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^\S+@\S+\.\S+$/.test(data.email)) {
      errors.push('Invalid email format');
    }

    // Optional field validation
    if (data.phone_number && !/^[0-9()\-\s+]+$/.test(data.phone_number)) {
      errors.push('Invalid phone number format');
    }
    if (data.emergency_contact_phone && !/^[0-9()\-\s+]+$/.test(data.emergency_contact_phone)) {
      errors.push('Invalid emergency contact phone format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Insurance Management Methods
  async getPatientInsurances(patientId: string): Promise<PatientInsurance[]> {
    try {
      console.log(`🛡️ Fetching insurances for patient ${patientId}...`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/insurances/`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient insurances fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch patient insurances');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient insurances:', error);
      return [];
    }
  },

  async createPatientInsurance(patientId: string, insuranceData: Partial<PatientInsurance>): Promise<PatientInsurance | null> {
    try {
      console.log(`🛡️ Creating insurance for patient ${patientId}:`, insuranceData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/insurances/`, {
        method: 'POST',
        body: JSON.stringify(insuranceData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient insurance created:', result);
        return result;
      } else {
        console.error('❌ Failed to create patient insurance');
        return null;
      }
    } catch (error) {
      console.error('❌ Error creating patient insurance:', error);
      return null;
    }
  },

  async updatePatientInsurance(patientId: string, insuranceId: string, insuranceData: Partial<PatientInsurance>): Promise<PatientInsurance | null> {
    try {
      console.log(`🛡️ Updating insurance ${insuranceId} for patient ${patientId}:`, insuranceData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/insurances/${insuranceId}/`, {
        method: 'PATCH',
        body: JSON.stringify(insuranceData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient insurance updated:', result);
        return result;
      } else {
        console.error('❌ Failed to update patient insurance');
        return null;
      }
    } catch (error) {
      console.error('❌ Error updating patient insurance:', error);
      return null;
    }
  },

  async deletePatientInsurance(patientId: string, insuranceId: string): Promise<boolean> {
    try {
      console.log(`🛡️ Deleting insurance ${insuranceId} for patient ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/insurances/${insuranceId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient insurance deleted');
        return true;
      } else {
        console.error('❌ Failed to delete patient insurance');
        return false;
      }
    } catch (error) {
      console.error('❌ Error deleting patient insurance:', error);
      return false;
    }
  },

  // Django-specific patient management methods
  async getDjangoPatients(): Promise<Patient[]> {
    try {
      console.log('🔍 Fetching patients from Django backend...');

      const response = await makeDjangoApiCall('/patients/list/');

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Django patients fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch Django patients');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching Django patients:', error);
      throw new Error('Failed to fetch patients from Django backend');
    }
  },

  // Patient Alert Management Methods
  async getPatientAlerts(patientId: string): Promise<PatientAlert[]> {
    try {
      console.log(`🚨 Fetching alerts for patient ${patientId}...`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/alerts/`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient alerts fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch patient alerts');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient alerts:', error);
      return [];
    }
  },

  async createPatientAlert(patientId: string, alertData: Partial<PatientAlert> & { auto_trigger?: boolean }): Promise<PatientAlert | null> {
    try {
      console.log(`🚨 Creating alert for patient ${patientId}:`, alertData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/alerts/`, {
        method: 'POST',
        body: JSON.stringify(alertData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient alert created:', result);
        return result;
      } else {
        console.error('❌ Failed to create patient alert');
        return null;
      }
    } catch (error) {
      console.error('❌ Error creating patient alert:', error);
      return null;
    }
  },

  async updatePatientAlert(patientId: string, alertId: string, alertData: Partial<PatientAlert>): Promise<PatientAlert | null> {
    try {
      console.log(`🚨 Updating alert ${alertId} for patient ${patientId}:`, alertData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/alerts/${alertId}/`, {
        method: 'PATCH',
        body: JSON.stringify(alertData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient alert updated:', result);
        return result;
      } else {
        console.error('❌ Failed to update patient alert');
        return null;
      }
    } catch (error) {
      console.error('❌ Error updating patient alert:', error);
      return null;
    }
  },

  async deletePatientAlert(patientId: string, alertId: string): Promise<boolean> {
    try {
      console.log(`🚨 Deleting alert ${alertId} for patient ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/alerts/${alertId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient alert deleted');
        return true;
      } else {
        console.error('❌ Failed to delete patient alert');
        return false;
      }
    } catch (error) {
      console.error('❌ Error deleting patient alert:', error);
      return false;
    }
  },

  // Patient Attachment Management Methods
  async getPatientAttachments(patientId: string): Promise<PatientAttachment[]> {
    try {
      console.log(`📎 Fetching attachments for patient ${patientId}...`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/attachments/`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient attachments fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch patient attachments');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient attachments:', error);
      return [];
    }
  },

  async uploadPatientAttachment(patientId: string, file: File, metadata?: Partial<PatientAttachment>): Promise<PatientAttachment | null> {
    try {
      console.log(`📎 Uploading attachment for patient ${patientId}:`, file.name);

      const formData = new FormData();
      formData.append('file', file);

      // Add metadata if provided
      if (metadata) {
        Object.entries(metadata).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            formData.append(key, String(value));
          }
        });
      }

      const response = await makeDjangoApiCall(`/patients/${patientId}/attachments/`, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header - let browser set it with boundary for FormData
        headers: {}
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient attachment uploaded:', result);
        return result;
      } else {
        console.error('❌ Failed to upload patient attachment');
        return null;
      }
    } catch (error) {
      console.error('❌ Error uploading patient attachment:', error);
      return null;
    }
  },

  async updatePatientAttachment(patientId: string, attachmentId: string, metadata: Partial<PatientAttachment>): Promise<PatientAttachment | null> {
    try {
      console.log(`📎 Updating attachment ${attachmentId} for patient ${patientId}:`, metadata);

      const response = await makeDjangoApiCall(`/patients/${patientId}/attachments/${attachmentId}/`, {
        method: 'PATCH',
        body: JSON.stringify(metadata),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient attachment updated:', result);
        return result;
      } else {
        console.error('❌ Failed to update patient attachment');
        return null;
      }
    } catch (error) {
      console.error('❌ Error updating patient attachment:', error);
      return null;
    }
  },

  async deletePatientAttachment(patientId: string, attachmentId: string): Promise<boolean> {
    try {
      console.log(`📎 Deleting attachment ${attachmentId} for patient ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/attachments/${attachmentId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient attachment deleted');
        return true;
      } else {
        console.error('❌ Failed to delete patient attachment');
        return false;
      }
    } catch (error) {
      console.error('❌ Error deleting patient attachment:', error);
      return false;
    }
  },

  // Medical Data Management Methods
  async getMedicalDataCategories(categoryType?: string): Promise<MedicalDataCategory[]> {
    try {
      console.log(`🏥 Fetching medical data categories...`);

      const params = new URLSearchParams();
      if (categoryType) {
        params.append('type', categoryType);
      }

      const url = `/medical-data/categories/${params.toString() ? '?' + params.toString() : ''}`;
      const response = await makeDjangoApiCall(url);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Medical data categories fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch medical data categories');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching medical data categories:', error);
      return [];
    }
  },

  async getMedicalDataItems(options?: {
    categoryType?: string;
    categoryId?: string;
    search?: string;
    commonOnly?: boolean;
  }): Promise<MedicalDataItem[]> {
    try {
      console.log(`🏥 Fetching medical data items...`, options);

      const params = new URLSearchParams();
      if (options?.categoryType) params.append('category_type', options.categoryType);
      if (options?.categoryId) params.append('category_id', options.categoryId);
      if (options?.search) params.append('search', options.search);
      if (options?.commonOnly) params.append('common_only', 'true');

      const url = `/medical-data/items/${params.toString() ? '?' + params.toString() : ''}`;
      const response = await makeDjangoApiCall(url);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Medical data items fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch medical data items');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching medical data items:', error);
      return [];
    }
  },

  async getPatientMedicalData(patientId: string, categoryType?: string): Promise<PatientMedicalData[]> {
    try {
      console.log(`🏥 Fetching medical data for patient ${patientId}...`);

      const params = new URLSearchParams();
      if (categoryType) params.append('category_type', categoryType);

      const url = `/patients/${patientId}/medical-data/${params.toString() ? '?' + params.toString() : ''}`;
      const response = await makeDjangoApiCall(url);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient medical data fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch patient medical data');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient medical data:', error);
      return [];
    }
  },

  async addPatientMedicalData(patientId: string, data: {
    medical_item_id?: string;
    medical_item_name?: string;
    category_type: string;
    notes?: string;
    severity?: string;
    start_date?: string;
    end_date?: string;
    is_active?: boolean;
  }): Promise<PatientMedicalData | null> {
    try {
      console.log(`🏥 Adding medical data for patient ${patientId}:`, data);

      const response = await makeDjangoApiCall(`/patients/${patientId}/medical-data/`, {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient medical data added:', result);
        return result;
      } else {
        console.error('❌ Failed to add patient medical data');
        return null;
      }
    } catch (error) {
      console.error('❌ Error adding patient medical data:', error);
      return null;
    }
  },

  async removePatientMedicalData(patientId: string, medicalDataId: string): Promise<boolean> {
    try {
      console.log(`🏥 Removing medical data ${medicalDataId} for patient ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/medical-data/${medicalDataId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient medical data removed');
        return true;
      } else {
        console.error('❌ Failed to remove patient medical data');
        return false;
      }
    } catch (error) {
      console.error('❌ Error removing patient medical data:', error);
      return false;
    }
  },

  // Biometric Data Management Methods
  async getBiometricMeasureDefinitions(category?: string): Promise<BiometricMeasureDefinition[]> {
    try {
      console.log(`📊 Fetching biometric measure definitions...`);

      const params = new URLSearchParams();
      if (category) {
        params.append('category', category);
      }

      const url = `/biometric-data/definitions/${params.toString() ? '?' + params.toString() : ''}`;
      const response = await makeDjangoApiCall(url);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Biometric measure definitions fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch biometric measure definitions');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching biometric measure definitions:', error);
      return [];
    }
  },

  async getPatientBiometricMeasurements(patientId: string, options?: {
    measureDefinitionId?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<PatientBiometricMeasurement[]> {
    try {
      console.log(`📊 Fetching biometric measurements for patient ${patientId}...`, options);

      const params = new URLSearchParams();
      if (options?.measureDefinitionId) params.append('measure_definition_id', options.measureDefinitionId);
      if (options?.startDate) params.append('start_date', options.startDate);
      if (options?.endDate) params.append('end_date', options.endDate);
      if (options?.limit) params.append('limit', options.limit.toString());

      const url = `/patients/${patientId}/biometric-measurements/${params.toString() ? '?' + params.toString() : ''}`;
      const response = await makeDjangoApiCall(url);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient biometric measurements fetched:', result);
        return result.results || result || [];
      } else {
        console.error('❌ Failed to fetch patient biometric measurements');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient biometric measurements:', error);
      return [];
    }
  },

  async addPatientBiometricMeasurement(patientId: string, data: {
    measure_definition_id: string;
    value: number | string | boolean | Date;
    measurement_date?: string;
    notes?: string;
    is_abnormal?: boolean;
    device_used?: string;
    location?: string;
  }): Promise<PatientBiometricMeasurement | null> {
    try {
      console.log(`📊 Adding biometric measurement for patient ${patientId}:`, data);

      const response = await makeDjangoApiCall(`/patients/${patientId}/biometric-measurements/`, {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient biometric measurement added:', result);
        return result;
      } else {
        console.error('❌ Failed to add patient biometric measurement');
        return null;
      }
    } catch (error) {
      console.error('❌ Error adding patient biometric measurement:', error);
      return null;
    }
  },

  async deletePatientBiometricMeasurement(patientId: string, measurementId: string): Promise<boolean> {
    try {
      console.log(`📊 Deleting biometric measurement ${measurementId} for patient ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/biometric-measurements/${measurementId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient biometric measurement deleted');
        return true;
      } else {
        console.error('❌ Failed to delete patient biometric measurement');
        return false;
      }
    } catch (error) {
      console.error('❌ Error deleting patient biometric measurement:', error);
      return false;
    }
  },

  // Patient Detail Management Methods
  async getPatientDetail(patientId: string): Promise<Patient | null> {
    try {
      console.log(`👤 Fetching patient detail for ${patientId}...`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/detail/`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient detail fetched:', result);
        return result.patient || null;
      } else {
        console.error('❌ Failed to fetch patient detail');
        return null;
      }
    } catch (error) {
      console.error('❌ Error fetching patient detail:', error);
      return null;
    }
  },

  // Get patients by status/category
  async getPatientsByStatus(status: 'complete' | 'validated' | 'favorites' | 'archived' | 'incomplete' | 'no_visit'): Promise<Patient[]> {
    try {
      console.log(`👥 Fetching patients with status: ${status}...`);

      const response = await makeDjangoApiCall(`/patients/list/?status=${status}`);

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Fetched ${result.results?.length || 0} patients with status: ${status}`);
        return result.results || [];
      } else {
        console.error(`❌ Failed to fetch patients with status: ${status}`);
        return [];
      }
    } catch (error) {
      console.error(`❌ Error fetching patients with status ${status}:`, error);
      return [];
    }
  },

  // Search patients with filters
  async searchPatients(filters: {
    search?: string;
    status?: string;
    city?: string;
    insurance?: string;
    age_min?: number;
    age_max?: number;
    date_from?: string;
    date_to?: string;
  }): Promise<Patient[]> {
    try {
      console.log('🔍 Searching patients with filters:', filters);

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await makeDjangoApiCall(`/patients/list/?${params.toString()}`);

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Found ${result.results?.length || 0} patients matching filters`);
        return result.results || [];
      } else {
        console.error('❌ Failed to search patients');
        return [];
      }
    } catch (error) {
      console.error('❌ Error searching patients:', error);
      return [];
    }
  },

  async updatePatientDetail(patientId: string, patientData: Partial<Patient>): Promise<Patient | null> {
    try {
      console.log(`👤 Updating patient detail for ${patientId}:`, patientData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/update/`, {
        method: 'PATCH',
        body: JSON.stringify(patientData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient detail updated:', result);
        return result.patient || null;
      } else {
        console.error('❌ Failed to update patient detail');
        return null;
      }
    } catch (error) {
      console.error('❌ Error updating patient detail:', error);
      return null;
    }
  },

  // Patient Relations Methods
  async getPatientRelations(patientId: string): Promise<PatientRelation[]> {
    try {
      console.log(`👥 Fetching patient relations for ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/relations/`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient relations fetched:', result);
        return result.relations || result || [];
      } else {
        console.error('❌ Failed to fetch patient relations');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching patient relations:', error);
      return [];
    }
  },

  async addPatientRelation(patientId: string, relationData: CreatePatientRelationData): Promise<PatientRelation | null> {
    try {
      console.log(`👥 Adding patient relation for ${patientId}:`, relationData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/relations/`, {
        method: 'POST',
        body: JSON.stringify(relationData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient relation added:', result);
        return result.relation || result || null;
      } else {
        console.error('❌ Failed to add patient relation');
        return null;
      }
    } catch (error) {
      console.error('❌ Error adding patient relation:', error);
      return null;
    }
  },

  async updatePatientRelation(patientId: string, relationId: string, relationData: Partial<CreatePatientRelationData>): Promise<PatientRelation | null> {
    try {
      console.log(`👥 Updating patient relation ${relationId} for ${patientId}:`, relationData);

      const response = await makeDjangoApiCall(`/patients/${patientId}/relations/${relationId}/`, {
        method: 'PATCH',
        body: JSON.stringify(relationData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Patient relation updated:', result);
        return result.relation || result || null;
      } else {
        console.error('❌ Failed to update patient relation');
        return null;
      }
    } catch (error) {
      console.error('❌ Error updating patient relation:', error);
      return null;
    }
  },

  async deletePatientRelation(patientId: string, relationId: string): Promise<boolean> {
    try {
      console.log(`👥 Deleting patient relation ${relationId} for ${patientId}`);

      const response = await makeDjangoApiCall(`/patients/${patientId}/relations/${relationId}/`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Patient relation deleted');
        return true;
      } else {
        console.error('❌ Failed to delete patient relation');
        return false;
      }
    } catch (error) {
      console.error('❌ Error deleting patient relation:', error);
      return false;
    }
  }
};

export default patientService;
