"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
const ScrollToTop = () => {
  const [scrollTop, setScrollTop] = useState(false);
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setScrollTop(true);
      } else {
        setScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <>
      {scrollTop && (
        <button
          onClick={scrollToTop}
          className="go-to animated fadeInUp fixed bottom-4 right-4"
        >
          <Image
            className="topsvg pb-3"
            src="/top.svg"
            width={28}
            height={28}
            alt="ScrollToTop"
            priority
          />
        </button>
      )}
    </>
  );
};

export default ScrollToTop;
