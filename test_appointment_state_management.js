#!/usr/bin/env node

/**
 * Test script for appointment state management API
 * Tests all state transitions: calendar ↔ waiting list ↔ presentation room ↔ active visits ↔ history journal
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} - ${JSON.stringify(data)}`);
    }
    
    return { success: true, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test functions
async function testWaitingListToggle(appointmentId) {
  console.log('\n🔄 Testing Waiting List Toggle...');
  
  // Add to waiting list
  console.log('  📝 Adding to waiting list...');
  const addResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_waiting_list: true,
      status: 'waiting_list'
    })
  });
  
  if (addResult.success) {
    console.log('  ✅ Successfully added to waiting list');
  } else {
    console.log('  ❌ Failed to add to waiting list:', addResult.error);
    return false;
  }
  
  // Remove from waiting list
  console.log('  📝 Removing from waiting list...');
  const removeResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_waiting_list: false,
      status: 'scheduled'
    })
  });
  
  if (removeResult.success) {
    console.log('  ✅ Successfully removed from waiting list');
    return true;
  } else {
    console.log('  ❌ Failed to remove from waiting list:', removeResult.error);
    return false;
  }
}

async function testPresentationRoomToggle(appointmentId) {
  console.log('\n🏥 Testing Presentation Room Toggle...');
  
  // Add to presentation room
  console.log('  📝 Adding to presentation room...');
  const addResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_in_presentation_room: true,
      status: 'in_progress'
    })
  });
  
  if (addResult.success) {
    console.log('  ✅ Successfully added to presentation room');
  } else {
    console.log('  ❌ Failed to add to presentation room:', addResult.error);
    return false;
  }
  
  // Remove from presentation room
  console.log('  📝 Removing from presentation room...');
  const removeResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_in_presentation_room: false,
      status: 'scheduled'
    })
  });
  
  if (removeResult.success) {
    console.log('  ✅ Successfully removed from presentation room');
    return true;
  } else {
    console.log('  ❌ Failed to remove from presentation room:', removeResult.error);
    return false;
  }
}

async function testActiveVisitsToggle(appointmentId) {
  console.log('\n🏃 Testing Active Visits Toggle...');
  
  // Add to active visits
  console.log('  📝 Adding to active visits...');
  const addResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_active: true,
      status: 'in_progress'
    })
  });
  
  if (addResult.success) {
    console.log('  ✅ Successfully added to active visits');
  } else {
    console.log('  ❌ Failed to add to active visits:', addResult.error);
    return false;
  }
  
  // Remove from active visits
  console.log('  📝 Removing from active visits...');
  const removeResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_active: false,
      status: 'scheduled'
    })
  });
  
  if (removeResult.success) {
    console.log('  ✅ Successfully removed from active visits');
    return true;
  } else {
    console.log('  ❌ Failed to remove from active visits:', removeResult.error);
    return false;
  }
}

async function testHistoryJournal(appointmentId) {
  console.log('\n📚 Testing History Journal...');
  
  // Complete appointment and move to history journal
  console.log('  📝 Completing appointment...');
  const completeResult = await apiRequest(`/appointments/${appointmentId}/`, {
    method: 'PATCH',
    body: JSON.stringify({
      is_in_history_journal: true,
      is_active: false,
      is_in_presentation_room: false,
      status: 'completed',
      notes: 'Test completion - moved to history journal'
    })
  });
  
  if (completeResult.success) {
    console.log('  ✅ Successfully completed appointment and moved to history journal');
    return true;
  } else {
    console.log('  ❌ Failed to complete appointment:', completeResult.error);
    return false;
  }
}

async function getTestAppointment() {
  console.log('🔍 Finding test appointment...');
  
  const result = await apiRequest('/appointments/?limit=1');
  
  if (result.success && result.data.results && result.data.results.length > 0) {
    const appointment = result.data.results[0];
    console.log(`✅ Found test appointment: ${appointment.id} - ${appointment.title}`);
    return appointment.id;
  } else {
    console.log('❌ No appointments found for testing');
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🧪 Starting Appointment State Management Tests');
  console.log('=' .repeat(50));
  
  const appointmentId = await getTestAppointment();
  
  if (!appointmentId) {
    console.log('❌ Cannot run tests without an appointment');
    return;
  }
  
  const tests = [
    () => testWaitingListToggle(appointmentId),
    () => testPresentationRoomToggle(appointmentId),
    () => testActiveVisitsToggle(appointmentId),
    () => testHistoryJournal(appointmentId)
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      console.log('  ❌ Test failed with error:', error.message);
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log(`📊 Test Results: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! State management is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the backend implementation.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
