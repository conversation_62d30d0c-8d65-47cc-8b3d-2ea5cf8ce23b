"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var next_dist_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var next_dist_pages_document__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_pages_document__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/pages/_app */ \"./node_modules/next/dist/pages/_app.js\");\n/* harmony import */ var next_dist_pages_app__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_pages_app__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: (next_dist_pages_app__WEBPACK_IMPORTED_MODULE_12___default()),\n        Document: (next_dist_pages_document__WEBPACK_IMPORTED_MODULE_11___default())\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/_error\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean(((_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13___default()) || _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'Pages Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                    isRevalidate: hasStaticProps,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("./webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();