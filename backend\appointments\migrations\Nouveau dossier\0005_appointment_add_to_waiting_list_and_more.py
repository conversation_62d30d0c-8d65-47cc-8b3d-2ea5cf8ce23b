# Generated by Django 5.1.3 on 2025-08-18 10:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0004_appointment_consultation_duration_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='add_to_waiting_list',
            field=models.BooleanField(default=False, help_text='Add to waiting list'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='allergies',
            field=models.TextField(blank=True, help_text='Known allergies', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='birth_place',
            field=models.CharField(blank=True, help_text='Place of birth', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='blood_group',
            field=models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], help_text='Blood group', max_length=5, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_appel_video',
            field=models.BooleanField(default=False, help_text='Video call reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_rappel_email',
            field=models.BooleanField(default=False, help_text='Email reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='checked_rappel_sms',
            field=models.BooleanField(default=False, help_text='SMS reminder enabled'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='cin',
            field=models.CharField(blank=True, help_text='National ID number', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='etat_civil',
            field=models.CharField(blank=True, choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed')], help_text='Civil status', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='event_resource_id',
            field=models.CharField(blank=True, help_text='Event resource identifier', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='father_name',
            field=models.CharField(blank=True, help_text="Father's name", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], help_text='Patient gender', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='mother_name',
            field=models.CharField(blank=True, help_text="Mother's name", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='profession',
            field=models.CharField(blank=True, help_text='Patient profession', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='social_security',
            field=models.CharField(blank=True, help_text='Social security number', max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='event_type',
            field=models.CharField(blank=True, choices=[('visit', 'Visit'), ('visitor-counter', 'Visitor Counter'), ('completed', 'Completed'), ('diagnosis', 'Diagnosis')], help_text='Event type', max_length=20, null=True),
        ),
    ]
