import React from 'react';
import { <PERSON><PERSON>, Text, Button, Group, Stack } from '@mantine/core';
import { IconAlertCircle, IconRefresh } from '@tabler/icons-react';

interface LicenseErrorProps {
  message: string;
  onRetry?: () => void;
  showContactSupport?: boolean;
}

export function LicenseError({ message, onRetry, showContactSupport = true }: LicenseErrorProps) {
  return (
    <Alert 
      icon={<IconAlertCircle size="1.1rem" />} 
      title="License Activation Error" 
      color="red" 
      mb="md"
      variant="filled"
    >
      <Stack gap="xs">
        <Text size="sm">{message}</Text>
        
        {(onRetry || showContactSupport) && (
          <Group justify="space-between" mt="md">
            {onRetry && (
              <Button 
                variant="white" 
                color="red" 
                size="xs" 
                leftSection={<IconRefresh size="0.9rem" />}
                onClick={onRetry}
              >
                Try Again
              </Button>
            )}
            
            {showContactSupport && (
              <Text size="xs" c="white">
                If you continue to experience issues, please contact our support team.
              </Text>
            )}
          </Group>
        )}
      </Stack>
    </Alert>
  );
}

export default LicenseError;
