import React, { useState } from 'react';
import {
  Stack,
  Paper,
  Group,
  Text,
  Button,
  Table,
  ActionIcon,
  Tooltip,
  Modal,
  TextInput,
  NumberInput,
  Tabs,
  Box,
  Divider,
 
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconX,
} from '@tabler/icons-react';

// Types
interface ScoreBlock {
  id: string;
  dateModification: string;
  titre: string;
  nombreQuestions: number;
  nombreSignifications: number;
}

interface SubQuestion {
  id: string;
  label: string;
  score: number;
}

interface Question {
  id: string;
  label: string;
  subQuestions: SubQuestion[];
}

interface Signification {
  id: string;
  label: string;
  couleur: string;
  scoreDe: number;
  scoreA: number;
}

interface ScoreFormData {
  titre: string;
  questions: Question[];
  significations: Signification[];
}

const BlockDuScore = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingScore, setEditingScore] = useState<ScoreBlock | null>(null);
  const [activeTab, setActiveTab] = useState<string>('questions');
  const [formData, setFormData] = useState<ScoreFormData>({
    titre: 'De dépistage des troubles du sommeil',
    questions: [
      {
        id: '1',
        label: 'Avez-vous un travail posté ou un travail de nuit.',
        subQuestions: [
          { id: '1-oui', label: 'Oui', score: 1 },
          { id: '1-non', label: 'Non', score: 0 },
        ],
      },
      {
        id: '2',
        label: 'Souffrez vous de Fibromyalgie ou d\'un syndrome de fatigue chronique.',
        subQuestions: [
          { id: '2-oui', label: 'Oui', score: 1 },
          { id: '2-non', label: 'Non', score: 0 },
        ],
      },
      {
        id: '3',
        label: 'Vous réveillez vous plus fatigué qu\'en coucher.',
        subQuestions: [
          { id: '3-oui', label: 'Oui', score: 1 },
          { id: '3-non', label: 'Non', score: 0 },
        ],
      },
      {
        id: '4',
        label: 'Êtes vous souvent gêné par une une somnolence dans la journée.',
        subQuestions: [
          { id: '4-oui', label: 'Oui', score: 1 },
          { id: '4-non', label: 'Non', score: 0 },
        ],
      },
      {
        id: '5',
        label: 'Le besoin de dormir vous empêche-t-il parfois de conduire ou vous oblige à vous arrêter.',
        subQuestions: [
          { id: '5-oui', label: 'Oui', score: 1 },
          { id: '5-non', label: 'Non', score: 0 },
        ],
      },
      {
        id: '6',
        label: 'Avez-vous eu un accident dû à un endormissement.',
        subQuestions: [
          { id: '6-oui', label: 'Oui', score: 1 },
          { id: '6-non', label: 'Non', score: 0 },
        ],
      },
    ],
    significations: [
      {
        id: '1',
        label: 'Oui',
        couleur: '#0E695C',
        scoreDe: 1,
        scoreA: 1,
      },
      {
        id: '2',
        label: 'Non',
        couleur: '#a1850a',
        scoreDe: 0,
        scoreA: 0,
      },
    ],
  });

  // Sample data based on the image
  const [scores, setScores] = useState<ScoreBlock[]>([
    {
      id: '1',
      dateModification: '27/10/2023 12:45',
      titre: 'De dépistage des troubles du sommeil',
      nombreQuestions: 22,
      nombreSignifications: 2,
    },
    {
      id: '2',
      dateModification: '27/10/2023 12:45',
      titre: 'Evolution du sommeil',
      nombreQuestions: 5,
      nombreSignifications: 0,
    },
    {
      id: '3',
      dateModification: '27/10/2023 12:45',
      titre: 'Dépendance à la nicotine',
      nombreQuestions: 6,
      nombreSignifications: 4,
    },
    {
      id: '4',
      dateModification: '27/10/2023 12:45',
      titre: 'Motivation à l\'arrêt du tabac',
      nombreQuestions: 4,
      nombreSignifications: 4,
    },
    {
      id: '5',
      dateModification: '27/10/2023 12:45',
      titre: 'Dépendance à la nicotine',
      nombreQuestions: 4,
      nombreSignifications: 0,
    },
    {
      id: '6',
      dateModification: '27/10/2023 12:45',
      titre: 'Anxiété Dépression',
      nombreQuestions: 13,
      nombreSignifications: 4,
    },
  ]);

  // Open modal for new/edit score
  const openModal = (score?: ScoreBlock) => {
    if (score) {
      setEditingScore(score);
      setFormData({
        titre: score.titre,
        questions: [], // Initialize with empty arrays for now
        significations: [],
      });
    } else {
      setEditingScore(null);
      setFormData({
        titre: '',
        questions: [],
        significations: [],
      });
    }
    setActiveTab('questions');
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingScore(null);
    setFormData({
      titre: '',
      questions: [],
      significations: [],
    });
    setActiveTab('questions');
  };

  // Add new question
  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      label: '',
      subQuestions: [
        { id: `${Date.now()}-oui`, label: 'Oui', score: 1 },
        { id: `${Date.now()}-non`, label: 'Non', score: 0 },
      ],
    };
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }));
  };

  // Add sub-question to a question
  const addSubQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === questionId
          ? {
              ...q,
              subQuestions: [
                ...q.subQuestions,
                { id: `${Date.now()}-sub`, label: '', score: 0 },
              ],
            }
          : q
      ),
    }));
  };

  // Remove question
  const removeQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId),
    }));
  };

  // Remove sub-question
  const removeSubQuestion = (questionId: string, subQuestionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === questionId
          ? {
              ...q,
              subQuestions: q.subQuestions.filter(sq => sq.id !== subQuestionId),
            }
          : q
      ),
    }));
  };

  // Add new signification
  const addSignification = () => {
    const newSignification: Signification = {
      id: Date.now().toString(),
      label: '',
      couleur: '#0E695C',
      scoreDe: 0,
      scoreA: 0,
    };
    setFormData(prev => ({
      ...prev,
      significations: [...prev.significations, newSignification],
    }));
  };

  // Remove signification
  const removeSignification = (id: string) => {
    setFormData(prev => ({
      ...prev,
      significations: prev.significations.filter(s => s.id !== id),
    }));
  };

  // Update question
  const updateQuestion = (id: string, field: 'label', value: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === id ? { ...q, [field]: value } : q
      ),
    }));
  };

  // Update sub-question
  const updateSubQuestion = (questionId: string, subQuestionId: string, field: 'label' | 'score', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === questionId
          ? {
              ...q,
              subQuestions: q.subQuestions.map(sq =>
                sq.id === subQuestionId ? { ...sq, [field]: value } : sq
              ),
            }
          : q
      ),
    }));
  };

  // Update signification
  const updateSignification = (id: string, field: 'label' | 'couleur' | 'scoreDe' | 'scoreA', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      significations: prev.significations.map(s =>
        s.id === id ? { ...s, [field]: value } : s
      ),
    }));
  };

  // Save score
  const saveScore = () => {
    if (!formData.titre.trim()) return;

    const currentDate = new Date().toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    if (editingScore) {
      // Update existing score
      setScores(prev =>
        prev.map(score =>
          score.id === editingScore.id
            ? {
                ...score,
                titre: formData.titre,
                nombreQuestions: formData.questions.length,
                nombreSignifications: formData.significations.length,
                dateModification: currentDate,
              }
            : score
        )
      );
    } else {
      // Add new score
      const newScore: ScoreBlock = {
        id: Date.now().toString(),
        dateModification: currentDate,
        titre: formData.titre,
        nombreQuestions: formData.questions.length,
        nombreSignifications: formData.significations.length,
      };
      setScores(prev => [...prev, newScore]);
    }

    closeModal();
  };

  return (
    <Stack gap="md" className="p-6">
      {/* Header */}
      <Paper shadow="sm" p="md" radius="md" withBorder>
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <Text size="xl" fw={700} className="text-gray-800">
              📊 Bloc du score
            </Text>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => openModal()}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Nouveau score
          </Button>
        </Group>
      </Paper>

      {/* Table */}
      <Paper shadow="sm" p="md" radius="md" withBorder>
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
        >
          <Table.Thead className="bg-blue-50">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 text-gray-700 font-medium">
                Date de modification
              </Table.Th>
              <Table.Th className="border-r border-gray-300 text-gray-700 font-medium">
                Titre
              </Table.Th>
              <Table.Th className="border-r border-gray-300 text-gray-700 font-medium text-center">
                N° des questions
              </Table.Th>
              <Table.Th className="border-r border-gray-300 text-gray-700 font-medium text-center">
                N° des significations
              </Table.Th>
              <Table.Th className="text-gray-700 font-medium text-center w-20">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {scores.map((score) => (
              <Table.Tr key={score.id} className="hover:bg-gray-50">
                <Table.Td className="border-r border-gray-200 text-sm">
                  {score.dateModification}
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-sm">
                  {score.titre}
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-center text-sm">
                  <Text
                    className={`font-medium ${
                      score.nombreQuestions > 10 ? 'text-blue-600' :
                      score.nombreQuestions > 5 ? 'text-green-600' : 'text-gray-600'
                    }`}
                  >
                    {score.nombreQuestions}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-center text-sm">
                  <Text
                    className={`font-medium ${
                      score.nombreSignifications > 0 ? 'text-blue-600' : 'text-gray-400'
                    }`}
                  >
                    {score.nombreSignifications}
                  </Text>
                </Table.Td>
                <Table.Td className="text-center">
                  <Tooltip label="Modifier">
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      size="sm"
                      onClick={() => openModal(score)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Modal for adding/editing score */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title={editingScore ? 'Modifier le score' : 'Nouveau score'}
        size="xl"
        centered
      >
        <Stack gap="md">
          {/* Title Input with red underline */}
          <Box>
            <TextInput
              label="Titre"
              placeholder="Entrez le titre du score"
              value={formData.titre}
              onChange={(e) => setFormData({ ...formData, titre: e.target.value })}
              required
              styles={{
                input: {
                  borderBottom: '2px solid #ef4444',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderRadius: 0,
                  backgroundColor: 'transparent',
                }
              }}
            />
          </Box>

          <Divider />

          {/* Tabs */}
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'questions')}>
            <Tabs.List>
              <Tabs.Tab value="questions">Questions</Tabs.Tab>
              <Tabs.Tab value="significations">Significations</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="questions" pt="md">
              <Stack gap="md">
                {/* Questions Hierarchical Display */}
                <Box>
                  <Group justify="space-between" mb="md" className="border-b border-blue-300 pb-2">
                    <Text fw={500} size="sm" className="text-gray-700">
                      Score
                    </Text>
                    <Text fw={500} size="sm" className="text-gray-700">
                      Score
                    </Text>
                  </Group>

                  <Stack gap="xs">
                    {formData.questions.map((question, questionIndex) => (
                      <Box key={question.id}>
                        {/* Main Question */}
                        <Group justify="space-between" align="center" className="py-2 hover:bg-gray-50">
                          <Group gap="xs" align="center" className="flex-1">
                            <Text size="sm" className="text-blue-600 font-medium min-w-fit">
                              {questionIndex + 1}-
                            </Text>
                            <Text size="sm" className="text-blue-600 font-medium flex-1">
                              {question.label}
                            </Text>
                          </Group>
                          <Group gap="xs" align="center">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              size="sm"
                              onClick={() => addSubQuestion(question.id)}
                            >
                              <IconPlus size={14} />
                            </ActionIcon>
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              size="sm"
                              onClick={() => removeQuestion(question.id)}
                            >
                              <IconX size={14} />
                            </ActionIcon>
                          </Group>
                        </Group>

                        {/* Sub Questions */}
                        <Stack gap="xs" ml="xl">
                          {question.subQuestions.map((subQuestion) => (
                            <Group key={subQuestion.id} justify="space-between" align="center" className="py-1 hover:bg-gray-50">
                              <Group gap="xs" align="center" className="flex-1">
                                <Text size="sm" className="text-gray-700 min-w-fit">
                                  {subQuestion.label}
                                </Text>
                              </Group>
                              <Group gap="xs" align="center">
                                <Text size="sm" className="text-blue-600 font-medium w-8 text-center">
                                  {subQuestion.score}
                                </Text>
                                <ActionIcon
                                  variant="subtle"
                                  color="red"
                                  size="sm"
                                  onClick={() => removeSubQuestion(question.id, subQuestion.id)}
                                >
                                  <IconX size={12} />
                                </ActionIcon>
                              </Group>
                            </Group>
                          ))}
                        </Stack>
                      </Box>
                    ))}
                  </Stack>
                </Box>

                <Group justify="flex-end" gap="sm">
                  <Button
                    leftSection={<IconPlus size={16} />}
                    onClick={addQuestion}
                    variant="filled"
                    color="blue"
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    Ajouter question
                  </Button>
                  <Button
                    variant="filled"
                    color="blue"
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                    onClick={saveScore}
                    disabled={!formData.titre.trim()}
                  >
                    Enregistrer
                  </Button>
                </Group>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="significations" pt="md">
              <Stack gap="md">
                {/* Significations Table */}
                <Table
                  striped={false}
                  highlightOnHover={true}
                  withTableBorder={true}
                  withColumnBorders={true}
                >
                  <Table.Thead className="bg-blue-50">
                    <Table.Tr>
                      <Table.Th className="border-r border-gray-300 text-gray-700 font-medium">
                        Label
                      </Table.Th>
                      <Table.Th className="border-r border-gray-300 text-gray-700 font-medium text-center w-32">
                        Couleur
                      </Table.Th>
                      <Table.Th className="border-r border-gray-300 text-gray-700 font-medium text-center w-24">
                        Score de
                      </Table.Th>
                      <Table.Th className="border-r border-gray-300 text-gray-700 font-medium text-center w-24">
                        Score à
                      </Table.Th>
                      <Table.Th className="text-gray-700 font-medium text-center w-16">

                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {formData.significations.map((signification) => (
                      <Table.Tr key={signification.id}>
                        <Table.Td className="border-r border-gray-200">
                          <TextInput
                            value={signification.label}
                            onChange={(e) => updateSignification(signification.id, 'label', e.target.value)}
                            placeholder="Entrez le label"
                            variant="unstyled"
                          />
                        </Table.Td>
                        <Table.Td className="border-r border-gray-200 text-center">
                          <Group justify="center" gap="xs">
                            <Box
                              style={{
                                width: 20,
                                height: 20,
                                borderRadius: '50%',
                                backgroundColor: signification.couleur,
                                border: '1px solid #ddd',
                              }}
                            />
                            <Text size="xs" className="text-gray-600">
                              {signification.couleur}
                            </Text>
                          </Group>
                        </Table.Td>
                        <Table.Td className="border-r border-gray-200 text-center">
                          <NumberInput
                            value={signification.scoreDe}
                            onChange={(value) => updateSignification(signification.id, 'scoreDe', Number(value) || 0)}
                            placeholder="0"
                            variant="unstyled"
                            min={0}
                            className="text-center"
                          />
                        </Table.Td>
                        <Table.Td className="border-r border-gray-200 text-center">
                          <NumberInput
                            value={signification.scoreA}
                            onChange={(value) => updateSignification(signification.id, 'scoreA', Number(value) || 0)}
                            placeholder="0"
                            variant="unstyled"
                            min={0}
                            className="text-center"
                          />
                        </Table.Td>
                        <Table.Td className="text-center">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => removeSignification(signification.id)}
                          >
                            <IconX size={16} />
                          </ActionIcon>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>

                <Group justify="flex-end" gap="sm">
                  <Button
                    leftSection={<IconPlus size={16} />}
                    onClick={addSignification}
                    variant="light"
                    color="blue"
                    size="sm"
                  >
                    Ajouter signification
                  </Button>
                </Group>
              </Stack>
            </Tabs.Panel>
          </Tabs>

          <Divider />

          {/* Action Buttons */}
          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={closeModal}>
              Annuler
            </Button>
            <Button
              onClick={saveScore}
              disabled={!formData.titre.trim()}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default BlockDuScore;
