/**
 * Payment Dashboard Widgets
 * Displays key payment and collection metrics in compact widgets
 */

import React from 'react';
import {
  Card,
  Group,
  Text,
  Badge,
  Stack,
  Grid,
  RingProgress,
  Progress,
  SimpleGrid,
  ThemeIcon,
  Loader,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconCash,
  IconCreditCard,
  IconReceipt,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconAlertCircle,
  IconCurrencyEuro,
  IconUsers,
  IconCalendarDollar,
  IconChartPie,
} from '@tabler/icons-react';
import { usePayment } from '@/hooks/usePayment';

interface PaymentWidgetsProps {
  patientId?: string;
  dateRange?: { start: string; end: string };
  compact?: boolean;
  showAnalytics?: boolean;
}

const PaymentWidgets: React.FC<PaymentWidgetsProps> = ({
  patientId,
  dateRange,
  compact = false,
  showAnalytics = true,
}) => {
  const {
    paymentCollections,
    accountBalances,
    paymentAnalytics,
    loading,
    error,
    getPatientPaymentStats,
    getPaymentMethodStats,
    getCollectionTrends,
    getOverdueAccounts,
  } = usePayment({ 
    patientId, 
    dateRange, 
    autoFetch: true,
    dataTypes: ['collections', 'balances', 'analytics']
  });

  const patientStats = patientId ? getPatientPaymentStats(patientId) : null;
  const methodStats = getPaymentMethodStats();
  const collectionTrends = getCollectionTrends();
  const overdueAccounts = getOverdueAccounts();

  if (loading) {
    return (
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {[...Array(4)].map((_, i) => (
          <Card key={i} padding="md" radius="md" withBorder>
            <Group justify="center" p="xl">
              <Loader size="sm" />
            </Group>
          </Card>
        ))}
      </SimpleGrid>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
        <Text size="sm">{error}</Text>
      </Alert>
    );
  }

  const totalCollections = paymentCollections.reduce((sum, c) => sum + c.amount_collected, 0);
  const totalOutstanding = accountBalances.reduce((sum, b) => sum + b.remaining_balance, 0);
  const totalAccounts = accountBalances.length;
  const collectionRate = paymentAnalytics?.collection_rate || 0;

  return (
    <Stack gap="md">
      {/* Key Metrics Row */}
      <SimpleGrid cols={compact ? 2 : 4} spacing="md">
        {/* Total Collections Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="green" size="sm">
                <IconCash size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Encaissements</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="green">
            {totalCollections.toLocaleString()}€
          </Text>
          <Text size="xs" c="dimmed">
            {paymentCollections.length} transactions
          </Text>
        </Card>

        {/* Outstanding Balance Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="orange" size="sm">
                <IconReceipt size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Soldes Restants</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="orange">
            {totalOutstanding.toLocaleString()}€
          </Text>
          <Text size="xs" c="dimmed">
            {overdueAccounts.length} comptes en retard
          </Text>
        </Card>

        {/* Collection Rate Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="blue" size="sm">
                <IconChartPie size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Taux de Recouvrement</Text>
            </Group>
          </Group>
          <Group justify="center">
            <RingProgress
              size={80}
              thickness={8}
              sections={[{ value: collectionRate, color: 'blue' }]}
              label={
                <Text size="sm" ta="center" fw={700}>
                  {collectionRate.toFixed(1)}%
                </Text>
              }
            />
          </Group>
        </Card>

        {/* Account Status Widget */}
        <Card padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Group gap="xs">
              <ThemeIcon variant="light" color="purple" size="sm">
                <IconUsers size={16} />
              </ThemeIcon>
              <Text size="sm" c="dimmed">Comptes</Text>
            </Group>
          </Group>
          <Text size="xl" fw={700} c="purple">
            {totalAccounts}
          </Text>
          <Progress 
            value={totalAccounts > 0 ? ((totalAccounts - overdueAccounts.length) / totalAccounts) * 100 : 0} 
            size="xs" 
            mt="xs" 
          />
          <Text size="xs" c="dimmed">
            {((totalAccounts - overdueAccounts.length) / totalAccounts * 100).toFixed(1)}% à jour
          </Text>
        </Card>
      </SimpleGrid>

      {!compact && showAnalytics && (
        <>
          {/* Secondary Metrics Row */}
          <SimpleGrid cols={3} spacing="md">
            {/* Payment Methods Breakdown */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="teal" size="sm">
                    <IconCreditCard size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Modes de Paiement</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {methodStats.methodBreakdown.slice(0, 4).map((method) => (
                  <Group key={method.method} justify="space-between">
                    <Text size="xs" tt="capitalize">{method.method}</Text>
                    <Group gap="xs">
                      <Text size="xs" fw={500}>{method.count}</Text>
                      <Text size="xs" c="dimmed">{method.amount.toLocaleString()}€</Text>
                    </Group>
                  </Group>
                ))}
                {methodStats.methodBreakdown.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucune donnée disponible
                  </Text>
                )}
              </Stack>
            </Card>

            {/* Collection Trends */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="indigo" size="sm">
                    <IconCalendarDollar size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Tendances</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text size="xs">Moyenne journalière</Text>
                  <Text size="xs" fw={500}>{collectionTrends.dailyAverage.toLocaleString()}€</Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">Total hebdomadaire</Text>
                  <Text size="xs" fw={500}>{collectionTrends.weeklyTotal.toLocaleString()}€</Text>
                </Group>
                <Group justify="space-between">
                  <Text size="xs">Total mensuel</Text>
                  <Text size="xs" fw={500}>{collectionTrends.monthlyTotal.toLocaleString()}€</Text>
                </Group>
                <Divider size="xs" />
                <Group justify="space-between">
                  <Text size="xs">Mode préféré</Text>
                  <Text size="xs" fw={500} tt="capitalize">{collectionTrends.topPaymentMethod}</Text>
                </Group>
              </Stack>
            </Card>

            {/* Recent Collections */}
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <ThemeIcon variant="light" color="yellow" size="sm">
                    <IconCurrencyEuro size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={600}>Encaissements Récents</Text>
                </Group>
              </Group>
              <Stack gap="xs">
                {paymentCollections.slice(0, 3).map((collection) => (
                  <Group key={collection.id} justify="space-between">
                    <div>
                      <Text size="xs" fw={500}>{collection.patient_name}</Text>
                      <Text size="xs" c="dimmed">
                        {new Date(collection.date).toLocaleDateString()} - {collection.payment_method}
                      </Text>
                    </div>
                    <Group gap="xs">
                      <Text size="xs" fw={500} c="green">
                        {collection.amount_collected}€
                      </Text>
                      <Badge 
                        size="xs" 
                        color={collection.status === 'completed' ? 'green' : collection.status === 'pending' ? 'yellow' : 'red'}
                      >
                        {collection.status}
                      </Badge>
                    </Group>
                  </Group>
                ))}
                {paymentCollections.length === 0 && (
                  <Text size="xs" c="dimmed" ta="center">
                    Aucun encaissement récent
                  </Text>
                )}
              </Stack>
            </Card>
          </SimpleGrid>

          {/* Patient-Specific Stats */}
          {patientId && patientStats && (
            <Card padding="md" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Text size="sm" fw={600}>Statistiques Patient</Text>
                <Badge size="sm" color="blue">
                  {patientStats.totalTransactions} transactions
                </Badge>
              </Group>
              <Grid gutter="md">
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Total Encaissé</Text>
                    <Text size="lg" fw={700} c="green">{patientStats.totalCollected}€</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Solde Restant</Text>
                    <Text size="lg" fw={700} c="orange">{patientStats.remainingBalance}€</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">Mode Préféré</Text>
                    <Text size="lg" fw={700} c="blue" tt="capitalize">{patientStats.preferredPaymentMethod}</Text>
                  </Stack>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c="dimmed">État du Compte</Text>
                    <Badge 
                      size="lg" 
                      color={
                        patientStats.accountStatus === 'current' ? 'green' : 
                        patientStats.accountStatus === 'overdue' ? 'red' : 
                        patientStats.accountStatus === 'paid' ? 'blue' : 'gray'
                      }
                    >
                      {patientStats.accountStatus}
                    </Badge>
                  </Stack>
                </Grid.Col>
              </Grid>
              {patientStats.lastPaymentDate && (
                <>
                  <Divider my="md" />
                  <Group justify="space-between">
                    <Text size="sm" c="dimmed">Dernier paiement</Text>
                    <Text size="sm" fw={600}>
                      {new Date(patientStats.lastPaymentDate).toLocaleDateString()}
                    </Text>
                  </Group>
                </>
              )}
            </Card>
          )}

          {/* Overdue Accounts Alert */}
          {overdueAccounts.length > 0 && (
            <Alert icon={<IconAlertCircle size={16} />} color="orange" variant="light">
              <Group justify="space-between">
                <div>
                  <Text size="sm" fw={600}>Comptes en Retard</Text>
                  <Text size="xs">
                    {overdueAccounts.length} compte(s) nécessitent un suivi
                  </Text>
                </div>
                <Text size="sm" fw={600} c="orange">
                  {overdueAccounts.reduce((sum, acc) => sum + acc.remaining_balance, 0).toLocaleString()}€
                </Text>
              </Group>
            </Alert>
          )}
        </>
      )}
    </Stack>
  );
};

export default PaymentWidgets;
