'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { Comptes_rendus } from './Comptes_rendus';

export default function ComptesRendusDemo() {
  const mockTemplates = [
    { id: '1', title: 'Consultation générale', color: '#1976d2' },
    { id: '2', title: 'Contrôle post-opératoire', color: '#388e3c' },
    { id: '3', title: 'Urgence', color: '#d32f2f' },
    { id: '4', title: 'Bilan pré-opératoire', color: '#f57c00' },
    { id: '5', title: 'Radiologie', color: '#7b1fa2' },
    { id: '6', title: 'Orthodontie', color: '#00796b' },
    { id: '7', title: 'Parodontologie', color: '#5d4037' }
  ];

  const mockExamList = [
    {
      id: '1',
      exam_date: '16/05/2025',
      patient: {
        id: '1',
        full_name: 'AHMED BENALI',
        gender: 'M',
        age: '35 ans'
      },
      template_id: '1',
      template_title: 'Consultation générale',
      indication: '<PERSON><PERSON><PERSON> dentaire persistante depuis 3 jours',
      visit_id: '1',
      is_model: false,
      created_at: '2025-05-16'
    },
    {
      id: '2',
      exam_date: '15/05/2025',
      patient: {
        id: '2',
        full_name: 'FATIMA ZAHRA ALAOUI',
        gender: 'F',
        age: '28 ans'
      },
      template_id: '2',
      template_title: 'Contrôle post-opératoire',
      indication: 'Suivi extraction dent de sagesse - contrôle cicatrisation',
      visit_id: '2',
      is_model: false,
      created_at: '2025-05-15'
    },
    {
      id: '3',
      exam_date: '14/05/2025',
      patient: {
        id: '3',
        full_name: 'MOHAMED ALAMI',
        gender: 'M',
        age: '45 ans'
      },
      template_id: '3',
      template_title: 'Urgence',
      indication: 'Traumatisme dentaire suite à chute - fracture couronne',
      visit_id: '3',
      is_model: false,
      created_at: '2025-05-14'
    },
    {
      id: '4',
      exam_date: '13/05/2025',
      patient: {
        id: '4',
        full_name: 'AICHA MANSOURI',
        gender: 'F',
        age: '42 ans'
      },
      template_id: '4',
      template_title: 'Bilan pré-opératoire',
      indication: 'Préparation chirurgie implantaire - évaluation osseuse',
      visit_id: '4',
      is_model: false,
      created_at: '2025-05-13'
    },
    {
      id: '5',
      exam_date: '12/05/2025',
      patient: {
        id: '5',
        full_name: 'YOUSSEF IDRISSI',
        gender: 'M',
        age: '38 ans'
      },
      template_id: '5',
      template_title: 'Radiologie',
      indication: 'Panoramique dentaire - bilan orthodontique',
      visit_id: '5',
      is_model: false,
      created_at: '2025-05-12'
    },
    {
      id: '6',
      exam_date: '11/05/2025',
      patient: {
        id: '6',
        full_name: 'KHADIJA BERRADA',
        gender: 'F',
        age: '55 ans'
      },
      template_id: '6',
      template_title: 'Orthodontie',
      indication: 'Consultation orthodontique - malposition dentaire',
      visit_id: '6',
      is_model: false,
      created_at: '2025-05-11'
    },
    {
      id: '7',
      exam_date: '10/05/2025',
      patient: {
        id: '7',
        full_name: 'HASSAN TAZI',
        gender: 'M',
        age: '60 ans'
      },
      template_id: '7',
      template_title: 'Parodontologie',
      indication: 'Bilan parodontal - gingivite chronique',
      visit_id: '7',
      is_model: false,
      created_at: '2025-05-10'
    }
  ];

  const handleViewExam = (exam: any) => {
    console.log('Voir examen:', exam);
    alert(`Affichage de l'examen du ${exam.exam_date} pour ${exam.patient?.full_name}`);
  };

  const handleEditExam = (exam: any) => {
    console.log('Éditer examen:', exam);
    alert(`Édition de l'examen du ${exam.exam_date} pour ${exam.patient?.full_name}`);
  };

  const handleDeleteExam = (exam: any) => {
    console.log('Supprimer examen:', exam);
    alert(`Examen du ${exam.exam_date} supprimé`);
  };

  const handlePrintExam = (exam: any) => {
    console.log('Imprimer examen:', exam);
    alert(`Impression de l'examen du ${exam.exam_date} en cours...`);
  };

  const handleSearch = (query: any) => {
    console.log('Recherche:', query);
  };

  const handleSort = (order: any) => {
    console.log('Tri:', order);
  };

  const handlePaginate = (pagination: any) => {
    console.log('Pagination:', pagination);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Comptes_rendus
          examList={mockExamList}
          templates={mockTemplates}
          visitContext={false}
          loading={false}
          onViewExam={handleViewExam}
          onEditExam={handleEditExam}
          onDeleteExam={handleDeleteExam}
          onPrintExam={handlePrintExam}
          onSearch={handleSearch}
          onSort={handleSort}
          onPaginate={handlePaginate}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en contexte de visite (sans colonne patient)
export function ComptesRendusVisitDemo() {
  const mockExamListVisit = [
    {
      id: '1',
      exam_date: '16/05/2025',
      template_id: '1',
      template_title: 'Consultation générale',
      indication: 'Douleur dentaire persistante',
      visit_id: '1',
      is_model: false,
      created_at: '2025-05-16'
    },
    {
      id: '2',
      exam_date: '15/05/2025',
      template_id: '2',
      template_title: 'Contrôle post-opératoire',
      indication: 'Suivi extraction',
      visit_id: '2',
      is_model: false,
      created_at: '2025-05-15'
    }
  ];

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Comptes_rendus
          examList={mockExamListVisit}
          visitContext={true}
          loading={false}
          onViewExam={(exam) => console.log('Voir:', exam)}
          onEditExam={(exam) => console.log('Éditer:', exam)}
          onDeleteExam={(exam) => console.log('Supprimer:', exam)}
          onPrintExam={(exam) => console.log('Imprimer:', exam)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function ComptesRendusLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Comptes_rendus
          examList={[]}
          loading={true}
          visitContext={false}
        />
      </div>
    </MantineProvider>
  );
}
