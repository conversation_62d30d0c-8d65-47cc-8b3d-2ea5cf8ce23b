/**
 * Generic States Service
 * Handles all generic-states related data operations including:
 * - Daily, periodic, monthly, and annual activity reports
 * - Financial reports (payments, checks, balance)
 * - Patient analytics and demographics
 * - Medical reports and statistics
 * - Appointment analytics
 */

// Types for Generic States data
export interface ActivityReport {
  id: number;
  date: string;
  type: 'daily' | 'periodic' | 'monthly' | 'annual';
  totalAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  revenue: number;
  procedures: ProcedureActivity[];
  summary: ActivitySummary;
}

export interface ProcedureActivity {
  id: number;
  name: string;
  count: number;
  revenue: number;
  type: 'procedure' | 'dental' | 'medical' | 'encasement' | 'payment';
}

export interface ActivitySummary {
  totalPatients: number;
  newPatients: number;
  returningPatients: number;
  averageAppointmentDuration: number;
  mostCommonProcedure: string;
}

export interface FinancialReport {
  id: number;
  date: string;
  type: 'checks' | 'cash' | 'balance' | 'aged_balance';
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  transactions: FinancialTransaction[];
}

export interface FinancialTransaction {
  id: number;
  date: string;
  patientId: string;
  patientName: string;
  amount: number;
  type: 'payment' | 'refund' | 'check' | 'cash';
  status: 'completed' | 'pending' | 'overdue' | 'cancelled';
  dueDate?: string;
}

export interface PatientAnalytics {
  id: number;
  reportDate: string;
  totalPatients: number;
  newPatients: number;
  ageGroups: AgeGroupData[];
  genderDistribution: GenderData;
  visitPatterns: VisitPattern[];
  pathologies: PathologyData[];
  insuranceDistribution: InsuranceData[];
}

export interface AgeGroupData {
  ageRange: string;
  count: number;
  percentage: number;
}

export interface GenderData {
  male: number;
  female: number;
  other: number;
}

export interface VisitPattern {
  month: string;
  visits: number;
  newPatients: number;
  returningPatients: number;
}

export interface PathologyData {
  id: number;
  name: string;
  count: number;
  percentage: number;
  severity: 'low' | 'medium' | 'high';
}

export interface InsuranceData {
  provider: string;
  count: number;
  percentage: number;
  averageCoverage: number;
}

export interface MedicalReport {
  id: number;
  date: string;
  type: 'doctor_performance' | 'medication_usage' | 'procedures';
  doctorStats: DoctorStats[];
  medicationUsage: MedicationUsage[];
  procedureStats: ProcedureStats[];
}

export interface DoctorStats {
  doctorId: string;
  doctorName: string;
  totalAppointments: number;
  completedAppointments: number;
  averageRating: number;
  specialties: string[];
  revenue: number;
}

export interface MedicationUsage {
  medicationId: string;
  medicationName: string;
  prescriptionCount: number;
  patientCount: number;
  category: string;
}

export interface ProcedureStats {
  procedureId: string;
  procedureName: string;
  count: number;
  averageDuration: number;
  averageCost: number;
  successRate: number;
}

export interface AppointmentAnalytics {
  id: number;
  date: string;
  totalAppointments: number;
  appointmentsByStatus: AppointmentStatusData[];
  appointmentsByTime: TimeSlotData[];
  appointmentsByDoctor: DoctorAppointmentData[];
  noShowRate: number;
  averageWaitTime: number;
}

export interface AppointmentStatusData {
  status: string;
  count: number;
  percentage: number;
}

export interface TimeSlotData {
  timeSlot: string;
  count: number;
  utilization: number;
}

export interface DoctorAppointmentData {
  doctorId: string;
  doctorName: string;
  appointmentCount: number;
  utilization: number;
}

export interface GenericStatesSummary {
  reportDate: string;
  activityReports: ActivityReport[];
  financialReports: FinancialReport[];
  patientAnalytics: PatientAnalytics;
  medicalReports: MedicalReport[];
  appointmentAnalytics: AppointmentAnalytics;
  lastUpdate: string;
}

class GenericStatesService {
  private baseURL = '/api/generic-states';

  // Activity Reports operations
  async getActivityReports(type?: 'daily' | 'periodic' | 'monthly' | 'annual', dateRange?: { start: string; end: string }): Promise<ActivityReport[]> {
    try {
      let url = `${this.baseURL}/activity-reports`;
      const params = new URLSearchParams();
      
      if (type) params.append('type', type);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch activity reports: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching activity reports:', error);
      return this.getMockActivityReports(type);
    }
  }

  // Financial Reports operations
  async getFinancialReports(type?: 'checks' | 'cash' | 'balance' | 'aged_balance', dateRange?: { start: string; end: string }): Promise<FinancialReport[]> {
    try {
      let url = `${this.baseURL}/financial-reports`;
      const params = new URLSearchParams();
      
      if (type) params.append('type', type);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch financial reports: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching financial reports:', error);
      return this.getMockFinancialReports(type);
    }
  }

  // Patient Analytics operations
  async getPatientAnalytics(dateRange?: { start: string; end: string }): Promise<PatientAnalytics> {
    try {
      let url = `${this.baseURL}/patient-analytics`;
      if (dateRange) {
        const params = new URLSearchParams({
          start: dateRange.start,
          end: dateRange.end,
        });
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch patient analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching patient analytics:', error);
      return this.getMockPatientAnalytics();
    }
  }

  // Medical Reports operations
  async getMedicalReports(type?: 'doctor_performance' | 'medication_usage' | 'procedures'): Promise<MedicalReport[]> {
    try {
      let url = `${this.baseURL}/medical-reports`;
      if (type) {
        url += `?type=${type}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch medical reports: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching medical reports:', error);
      return this.getMockMedicalReports(type);
    }
  }

  // Appointment Analytics operations
  async getAppointmentAnalytics(dateRange?: { start: string; end: string }): Promise<AppointmentAnalytics> {
    try {
      let url = `${this.baseURL}/appointment-analytics`;
      if (dateRange) {
        const params = new URLSearchParams({
          start: dateRange.start,
          end: dateRange.end,
        });
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch appointment analytics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching appointment analytics:', error);
      return this.getMockAppointmentAnalytics();
    }
  }

  // Get comprehensive generic states summary
  async getGenericStatesSummary(dateRange?: { start: string; end: string }): Promise<GenericStatesSummary> {
    try {
      const [activityReports, financialReports, patientAnalytics, medicalReports, appointmentAnalytics] = await Promise.all([
        this.getActivityReports(undefined, dateRange),
        this.getFinancialReports(undefined, dateRange),
        this.getPatientAnalytics(dateRange),
        this.getMedicalReports(),
        this.getAppointmentAnalytics(dateRange),
      ]);

      return {
        reportDate: new Date().toISOString(),
        activityReports,
        financialReports,
        patientAnalytics,
        medicalReports,
        appointmentAnalytics,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching generic states summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockActivityReports(type?: string): ActivityReport[] {
    return [
      {
        id: 1,
        date: '2024-01-20',
        type: 'daily',
        totalAppointments: 25,
        completedAppointments: 22,
        cancelledAppointments: 3,
        revenue: 3500,
        procedures: [
          { id: 1, name: 'Consultation', count: 15, revenue: 1500, type: 'medical' },
          { id: 2, name: 'Nettoyage', count: 8, revenue: 800, type: 'dental' },
          { id: 3, name: 'Plombage', count: 4, revenue: 1200, type: 'procedure' },
        ],
        summary: {
          totalPatients: 22,
          newPatients: 3,
          returningPatients: 19,
          averageAppointmentDuration: 45,
          mostCommonProcedure: 'Consultation',
        },
      },
    ];
  }

  private getMockFinancialReports(type?: string): FinancialReport[] {
    return [
      {
        id: 1,
        date: '2024-01-20',
        type: 'balance',
        totalAmount: 15000,
        paidAmount: 12000,
        pendingAmount: 2500,
        overdueAmount: 500,
        transactions: [
          {
            id: 1,
            date: '2024-01-20',
            patientId: '1',
            patientName: 'Jean Dupont',
            amount: 150,
            type: 'payment',
            status: 'completed',
          },
        ],
      },
    ];
  }

  private getMockPatientAnalytics(): PatientAnalytics {
    return {
      id: 1,
      reportDate: '2024-01-20',
      totalPatients: 450,
      newPatients: 25,
      ageGroups: [
        { ageRange: '0-18', count: 45, percentage: 10 },
        { ageRange: '19-35', count: 135, percentage: 30 },
        { ageRange: '36-55', count: 180, percentage: 40 },
        { ageRange: '56+', count: 90, percentage: 20 },
      ],
      genderDistribution: { male: 200, female: 240, other: 10 },
      visitPatterns: [
        { month: 'Jan', visits: 120, newPatients: 15, returningPatients: 105 },
      ],
      pathologies: [
        { id: 1, name: 'Caries', count: 85, percentage: 35, severity: 'medium' },
      ],
      insuranceDistribution: [
        { provider: 'CNOPS', count: 200, percentage: 44, averageCoverage: 80 },
      ],
    };
  }

  private getMockMedicalReports(type?: string): MedicalReport[] {
    return [
      {
        id: 1,
        date: '2024-01-20',
        type: 'doctor_performance',
        doctorStats: [
          {
            doctorId: '1',
            doctorName: 'Dr. Martin',
            totalAppointments: 120,
            completedAppointments: 115,
            averageRating: 4.8,
            specialties: ['Dentisterie générale'],
            revenue: 18000,
          },
        ],
        medicationUsage: [],
        procedureStats: [],
      },
    ];
  }

  private getMockAppointmentAnalytics(): AppointmentAnalytics {
    return {
      id: 1,
      date: '2024-01-20',
      totalAppointments: 150,
      appointmentsByStatus: [
        { status: 'Completed', count: 135, percentage: 90 },
        { status: 'Cancelled', count: 10, percentage: 6.7 },
        { status: 'No Show', count: 5, percentage: 3.3 },
      ],
      appointmentsByTime: [
        { timeSlot: '09:00-10:00', count: 25, utilization: 83 },
        { timeSlot: '10:00-11:00', count: 30, utilization: 100 },
      ],
      appointmentsByDoctor: [
        { doctorId: '1', doctorName: 'Dr. Martin', appointmentCount: 75, utilization: 85 },
      ],
      noShowRate: 3.3,
      averageWaitTime: 12,
    };
  }
}

export const genericStatesService = new GenericStatesService();
export default genericStatesService;
