
"use client";
import { useState, useEffect } from "react";
import React from "react";

import Icon from '@mdi/react';
import { mdiCalendarOutline } from '@mdi/js';
import { useSearchParams } from "next/navigation";
import MetaSeo from"./MetaSeo"
import Mutuelles from"./mutuelles"
import {MutuellesId} from './MutuellesId'
import "~/styles/tab.css";

// Mapping des paramètres URL vers les numéros d'onglets
const tabMapping: { [key: string]: number } = {
  'mutuelles': 1,
  'nouvelle-mutuelle': 2,
  'validation': 3,
  'rapports': 4
};

function  AppointmentsPage() {
 
  const [toggleState, setToggleState] = useState(1);
  const searchParams = useSearchParams();

  // Effet pour lire les paramètres d'URL et définir l'onglet actif
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);
 

const icons = [
  { icon: <Icon path={mdiCalendarOutline} size={1} key="Mutuelles" />, label: "Liste des Mutuelles" },
  {
    icon: <Icon path={mdiCalendarOutline} size={1} key="MutuellesId" />,
    label: "Mutuelles Id",
  },
  // {
  //   icon: <Icon path={mdiCalendarOutline} size={1} key="Validation" />,
  //   label: "Validation",
  // },
  // {
  //   icon: <Icon path={mdiCalendarOutline} size={1} key="Rapports" />,
  //   label: "Rapports",
  // },
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<Mutuelles/> )
    case 2:
      return (<MutuellesId/>)
    // case 3:
    //   return (<div>Validation des mutuelles</div>)
    // case 4:
    //   return (<div>Rapports des mutuelles</div>)

    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 