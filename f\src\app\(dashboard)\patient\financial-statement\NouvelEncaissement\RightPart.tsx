'use client';
import React, { useState } from 'react';
import {
  Group,
  Stack,
  ActionIcon,
  Box,
  Select,
  TextInput,
  NumberInput,Text,Textarea,Tooltip,FileInput,Menu,Modal,
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import Icon from '@mdi/react';
import { mdiClose,mdiMicrophone,mdiDeleteSweep,mdiCloudUpload,mdiDotsVertical,mdiCamera,mdiFileTree,mdiLan,mdiDownloadNetwork,mdiFileReplace,mdiLanConnect,mdiDownloadOutline,} from '@mdi/js';
import { DatePickerInput } from '@mantine/dates';
import {VoiceRecognitionDialog} from '@/components/accessories/VoiceRecognitionDialog';
import {
  IconPlus,
  IconX,
} from '@tabler/icons-react';
// Types et interfaces
interface Patient {
  id: string;
  full_name: string;
  gender: string;
  age: string;
  file_number: string;
}
interface Physician {
  id: string;
  full_name: string;
}
interface PaymentModeType {
  id: string;
  value: string;
}
interface PaymentMode {
  id: string;
  type: PaymentModeType;
  reference?: string;
  bank?: string;
}

interface PayableItem {
  uid: string;
  date: string;
  type: 'visit' | 'plan' | 'medical-plan';
  payee: string;
  amount_due: number;
  amount_paid: number;
  progress?: number;
  status: 'paid' | 'partial' | 'unpaid' | 'exempt';
  remaining_amount: number;
  discount: number;
  amount_to_pay: number;
  selected: boolean;
}

interface EncasementModel {
  id?: string;
  payee_type: 'P' | 'T'; // Patient ou Tiers payant
  payee?: Patient;
  payer_type: 'P' | 'T' | 'O'; // Patient, Tiers payant, Autre
  payer?: Patient | string;
  payment_date: Date;
  amount: number;
  physician?: Physician;
  payment_mode: PaymentMode;
  details: PayableItem[];
}

interface NouvelEncaissementProps {
  readOnly?: boolean;
  reset?: number;
  closed?: boolean;
  isDental?: boolean;
  hasMedicalCare?: boolean;
  onSubmit?: (data: EncasementModel, saveAndClose?: boolean) => void;
  onCancel?: () => void;
  onDissociate?: () => void;
  onInvalidateHeader?: () => void;
  onPrint?: () => void;
  initialValue?: string;
  labelColor?: string;
  onChange?: (value: string) => void;
  isRequired?: boolean;
  
}
export const RightPart: React.FC<NouvelEncaissementProps> = ({
  readOnly = false,
  reset = 0,
   isRequired = false,

  initialValue = '',
  labelColor,
  onChange,
}) => {
     // États locaux
      const [model, setModel] = useState<EncasementModel>({
        payee_type: 'P',
        payer_type: 'P',
        payment_date: new Date(),
        amount: 0,
        payment_mode: {
          id: '',
          type: { id: '', value: '' }
        },
        details: []
      });
     const [, setAmountChanged] = useState(false);
const handleAmountChange = (value: number | string) => {
    const numValue = typeof value === 'string' ? parseFloat(value) || 0 : value || 0;
    setModel(prev => ({ ...prev, amount: numValue }));
    setAmountChanged(true);
  };   
  const [dateEcheance, setDateEcheance] = useState<Date | null>(new Date('2022-09-16'));
  // Données mockées
  const mockPhysicians: Physician[] = [
    { id: '1', full_name: 'DEMO DEMO' }
  ];
   const mockPaymentModes: PaymentModeType[] = [
    { id: '1', value: 'Espèce' },
    { id: '2', value: 'Chèque' },
    { id: '3', value: 'Carte bancaire' },
    { id: '4', value: 'Virement' },
    { id: '5', value: 'Traite' }
  ]; 
   const handlePhysicianChange = (value: string | null) => {
    const physician = mockPhysicians.find(p => p.id === value);
    setModel(prev => ({ ...prev, physician }));
  }; 
    const handlePaymentModeChange = (value: string | null) => {
    const paymentModeType = mockPaymentModes.find(pm => pm.id === value);
    if (paymentModeType) {
      setModel(prev => ({
        ...prev,
        payment_mode: {
          ...prev.payment_mode,
          type: paymentModeType
        }
      }));
    }
  };
  const [value, setValue] = useState(initialValue);
  
    const handleInputChange = (val: string) => {
      setValue(val);
      onChange?.(val);
    };
  
    const handleClear = () => {
      setValue('');
      onChange?.('');
    };
  
    // const handleRecord = () => {
    //   alert('Lancer reconnaissance vocale ici');
    // };
     const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleSubmit = (speechText) => {
    console.log('Speech text:', speechText);
    setIsDialogOpen(false);
  };

  const handleClose = () => {
    setIsDialogOpen(false);
  };


  return (
    <>
      {/* Section droite - Détails du paiement */}
                  <Box style={{ flex: 1 }}>
                    <Stack gap="md">
                      {/* Première ligne */}
                      <Group align="flex-end" gap="md">
                        <DateInput
                          label="Date de paiement"
                          value={model.payment_date}
                          onChange={(value) => setModel(prev => ({
                            ...prev,
                            payment_date: value ? new Date(value) : new Date()
                          }))}
                          disabled={readOnly && !reset}
                          required
                          style={{ flex: '0 0 200px' }}
                        />
    
                        <NumberInput
                          label="Montant encaissé"
                          value={model.amount}
                          onChange={handleAmountChange}
                          disabled={readOnly && !reset}
                          required
                          min={0}
                          decimalScale={2}
                          fixedDecimalScale
                          style={{ flex: 1 }}
                          leftSection="€"
                        />
    
                        <Select
                          label="Docteur"
                          data={mockPhysicians.map(p => ({ value: p.id, label: p.full_name }))}
                          value={model.physician?.id}
                          onChange={handlePhysicianChange}
                          disabled={readOnly && !reset}
                          required
                          style={{ flex: 1 }}
                          rightSection={
                            model.physician && !readOnly && (
                              <ActionIcon
                                variant="subtle"
                                onClick={() => setModel(prev => ({ ...prev, physician: undefined }))}
                              >
                                <Icon path={mdiClose} size={0.6} />
                              </ActionIcon>
                            )
                          }
                        />
                      </Group>
    
                      {/* Deuxième ligne */}
                      <Group align="flex-end" gap="md">
                        <Select
                          label="Mode de paiement"
                          data={mockPaymentModes.map(pm => ({ value: pm.id, label: pm.value }))}
                          value={model.payment_mode.type.id}
                          onChange={handlePaymentModeChange}
                          disabled={readOnly && !reset}
                          style={{ flex: 1 }}
                        />
    
                      
                            <TextInput
                              label="Référence"
                           placeholder='Réf'
                              value={model.payment_mode.reference || ''}
                              onChange={(e) => setModel(prev => ({
                                ...prev,
                                payment_mode: {
                                  ...prev.payment_mode,
                                  reference: e.target.value
                                }
                              }))}
                              disabled={readOnly && !reset}
                              style={{ flex: 1 }}
                            />
                            {/* <TextInput
                              label="Banque"
                              value={model.payment_mode.bank || ''}
                              onChange={(e) => setModel(prev => ({
                                ...prev,
                                payment_mode: {
                                  ...prev.payment_mode,
                                  bank: e.target.value
                                }
                              }))}
                              disabled={readOnly && !reset}
                              style={{ flex: 1 }}
                            /> */}
                             <Select
                           label="Banque"
                            placeholder="Aucune"
                            data={['Aucune', 'BCP', 'BMCE', 'BMCI', 'AWB', 'SGMB', 'CDM', 'BAM', 'ABB']}
                          />
                            {/* Date d'échéance */}
                                                     <div>
                                                       <Text size="xs" fw={500} className="text-gray-700 mb-1">
                                                         Date d&apos;échéance
                                                       </Text>
                                                       <Group gap="xs">
                                                         <DatePickerInput
                                                           value={dateEcheance}
                                                           onChange={setDateEcheance}
                                                           size="xs"
                                                           className="flex-1"
                                                           placeholder="16/09/2022"
                                                         />
                                                         <ActionIcon
                                                           variant="light"
                                                           color="blue"
                                                           size="sm"
                                                         >
                                                           <IconPlus size={14} />
                                                         </ActionIcon>
                                                         <ActionIcon
                                                           variant="light"
                                                           color="red"
                                                           size="sm"
                                                         >
                                                           <IconX size={14} />
                                                         </ActionIcon>
                                                       </Group>
                                                     </div>
                          
                        
                      </Group>
                      <div className='flex '>
                        <Box className="mn-free-dictionary-container w-[50%] mr-1">
                            <Group align="center" justify="space-between">
                              <Text fw={500} style={{ color: labelColor }}>
                                Commentaire

                                {isRequired && ' *'}
                              </Text>
                              <Group gap="xs">
                                <ActionIcon
                                  variant="subtle"
                                  color="blue"
                                  // onClick={handleRecord}
                                  disabled={readOnly}
                                  aria-label="record"
                                   onClick={() => setIsDialogOpen(true)}
                                >
                                  <Icon path={mdiMicrophone} size={1} />
                                </ActionIcon>
                      
                                <ActionIcon
                                  variant="subtle"
                                  color="red"
                                  onClick={handleClear}
                                  disabled={readOnly || !value}
                                  aria-label="clear"
                                >
                                  <Icon path={mdiDeleteSweep} size={1} />
                                </ActionIcon>
                              </Group>
                            </Group>
                      
                            <Textarea
                              value={value}
                              onChange={(e) => handleInputChange(e.currentTarget.value)}
                              placeholder="Ajouter"
                              autosize
                              minRows={2}
                              readOnly={readOnly}
                              mt="xs"
                            />
                        </Box>
                        <Box className="mn-free-dictionary-container w-[50%] ml-1">
  <Group align="center" justify="space-between">
    <Text fw={500} style={{ color: labelColor }}>
      Pièces jointes
      {isRequired && ' *'}
    </Text>
  
  </Group>
<Group>

  {/* <Textarea
    value={value}
    onChange={(e) => handleInputChange(e.currentTarget.value)}
    placeholder="Ajouter"
    autosize
    minRows={2}
    readOnly={readOnly}
    mt="xs"
    w={"90%"}
  /> */}
  <FileInput
    placeholder="Ajouter"
    readOnly={readOnly}
    mt="xs"
    size="xl"
    w={"90%"}
   styles={{
    input: {
      backgroundColor: '#FAF2D2',
    },
  }}
    />
    <div className='flex-col'>
    <Tooltip label="Ajouter un fichier" position="bottom" transitionProps={{ duration: 0 }} withArrow  style={{color:"var(--mantine-color-text)"}}  >
       <ActionIcon variant="subtle" aria-label="CloudUpload">
  <Icon path={mdiCloudUpload} size={1} color={'#737373'}/>
  </ActionIcon>
  </Tooltip>
 
    <Menu shadow="md" width={350}>
      <Menu.Target>
      <Icon path={mdiDotsVertical} size={1} color={'#737373'}/>
      </Menu.Target>

      <Menu.Dropdown>

        <Menu.Item leftSection={<Icon path={mdiCamera} size={1} color={'#737373'}/>}>
          Prendre plusieurs images
        </Menu.Item>
         <Menu.Item leftSection={<Icon path={mdiMicrophone} size={1} color={'#737373'}/>}>
        Enregistrer un fichier audio
        </Menu.Item>
         <Menu.Item leftSection={<Icon path={mdiFileTree} size={1} color={'#737373'}/>}>
         Importer depuis materiel interfacé
        </Menu.Item>
         <Menu.Item leftSection={<Icon path={mdiLan} size={1} color={'#737373'}/>}>
         Importer DICOM instance
        </Menu.Item>
         <Menu.Item leftSection={<Icon path={mdiDownloadNetwork} size={1} color={'#737373'}/>} disabled>
          Importer DICOM instance depuis FirePACS
        </Menu.Item>
        
        <Menu.Divider />
         <Menu.Item leftSection={<Icon path={mdiFileReplace} size={1} color={'#737373'}/>}>
         Importer dernière examen interfacé
        </Menu.Item>
         <Menu.Item leftSection={<Icon path={mdiLanConnect} size={1} color={'#737373'}/>}>
        Importer dernière DICOM instance
        </Menu.Item>
          <Menu.Item leftSection={<Icon path={mdiDownloadOutline} size={1} color={'#737373'}/>}>
         Importer dernière DICOM instance depuis FirePACS
        </Menu.Item>
      
      </Menu.Dropdown>
    </Menu>
  </div>
  </Group>
                       </Box>
                        </div>
                    </Stack>
                  </Box>

        {/* <VoiceRecognitionDialog
          isOpen={isDialogOpen}
          onClose={handleClose}
          onSubmit={handleSubmit}
        /> */}
        
                      {/*   Show VoiceRecognitionDialog*/}
                     <Modal.Root opened={isDialogOpen} onClose={handleClose}   size="lg" yOffset="30vh" xOffset={0}>
                    <VoiceRecognitionDialog
                    isOpen={isDialogOpen}
                    onClose={handleClose}
                    onSubmit={handleSubmit}
                  />
                     </Modal.Root>
                  </>
  )
}
