"""
Doctor pause/break management views for frontend integration.
"""

from rest_framework import generics, status, permissions, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction, models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta
import logging

from appointments.models import DoctorPause
from appointments.serializers import DoctorPauseSerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class DoctorPauseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing doctor pauses/breaks.
    """
    serializer_class = DoctorPauseSerializer
    permission_classes = [permissions.AllowAny]  # Adjust as needed
    
    def get_queryset(self):
        queryset = DoctorPause.objects.select_related('doctor', 'created_by').all()
        
        # Filter by doctor
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                date_from__date__gte=start_date,
                date_to__date__lte=end_date
            )
        
        # Filter by current date
        current_only = self.request.query_params.get('current_only')
        if current_only and current_only.lower() == 'true':
            now = timezone.now()
            queryset = queryset.filter(
                date_from__lte=now,
                date_to__gte=now
            )
        
        return queryset.order_by('date_from')
    
    def create(self, request, *args, **kwargs):
        """Create a new doctor pause."""
        try:
            with transaction.atomic():
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                
                # Set creator if authenticated
                if request.user and request.user.is_authenticated:
                    serializer.validated_data['created_by'] = request.user
                
                pause = serializer.save()
                
                return Response(
                    DoctorPauseSerializer(pause).data, 
                    status=status.HTTP_201_CREATED
                )
                
        except Exception as e:
            logger.error(f"Error creating doctor pause: {str(e)}")
            return Response(
                {'error': f'Failed to create pause: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def calendar_events(self, request):
        """Get pauses formatted for calendar display."""
        try:
            queryset = self.get_queryset()
            
            events = []
            for pause in queryset:
                events.append({
                    'id': f"pause_{pause.id}",
                    'title': pause.title,
                    'start': pause.date_from.isoformat(),
                    'end': pause.date_to.isoformat(),
                    'color': pause.color or '#15AABF',  # Use actual stored color with fallback
                    'type': 'pause',
                    'doctor': pause.doctor.get_full_name(),
                    'notes': pause.notes or '',
                    'duration_minutes': pause.duration_minutes,
                    'is_recurring': pause.is_recurring,
                    'editable': True,
                    'className': 'doctor-pause-event',
                    # Add room and resource_id for frontend mapping
                    'room': pause.room,
                    'resource_id': pause.resource_id
                })
            
            return Response({'events': events})
            
        except Exception as e:
            logger.error(f"Error getting pause calendar events: {str(e)}")
            return Response(
                {'error': 'Failed to get pause events'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def current_pauses(self, request):
        """Get currently active pauses."""
        try:
            now = timezone.now()
            current_pauses = self.get_queryset().filter(
                date_from__lte=now,
                date_to__gte=now
            )
            
            serializer = self.get_serializer(current_pauses, many=True)
            return Response({'current_pauses': serializer.data})
            
        except Exception as e:
            logger.error(f"Error getting current pauses: {str(e)}")
            return Response(
                {'error': 'Failed to get current pauses'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def create_pause_from_modal(request):
    """
    Create a pause from the frontend PauseModal data.
    """
    try:
        data = request.data
        
        # Validate required fields
        required_fields = ['title', 'dateFrom', 'dateTo', 'doctor']
        for field in required_fields:
            if not data.get(field):
                return Response(
                    {'error': f'{field} is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        with transaction.atomic():
            # Parse dates - assuming they come as datetime strings or date + time
            date_from_str = data['dateFrom']
            date_to_str = data['dateTo']
            
            # Try to parse as datetime, fallback to date
            try:
                if 'T' in date_from_str:
                    date_from = datetime.fromisoformat(date_from_str.replace('Z', '+00:00'))
                else:
                    # Assume it's a date string, add default time
                    date_from = datetime.strptime(date_from_str, '%Y-%m-%d')
                    date_from = date_from.replace(hour=9, minute=0)  # Default start time
                
                if 'T' in date_to_str:
                    date_to = datetime.fromisoformat(date_to_str.replace('Z', '+00:00'))
                else:
                    # Assume it's a date string, add default time
                    date_to = datetime.strptime(date_to_str, '%Y-%m-%d')
                    date_to = date_to.replace(hour=17, minute=0)  # Default end time
                    
            except ValueError as e:
                return Response(
                    {'error': f'Invalid date format: {str(e)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate date range
            if date_to <= date_from:
                return Response(
                    {'error': 'End date must be after start date'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Find doctor or assistant
            staff_identifier = data['doctor']  # Note: field name is 'doctor' but can be doctor or assistant
            try:
                # Try to find by ID first (UUID or integer), then by name
                try:
                    # FIXED: Allow both doctors and assistants to have pauses
                    staff_member = User.objects.get(id=staff_identifier, user_type__in=['doctor', 'assistant'])
                except (User.DoesNotExist, ValueError):
                    # If ID lookup fails, try by name/email for both doctors and assistants
                    staff_member = User.objects.get(
                        models.Q(email=staff_identifier) |
                        models.Q(first_name__icontains=staff_identifier) |
                        models.Q(last_name__icontains=staff_identifier),
                        user_type__in=['doctor', 'assistant']
                    )
            except User.DoesNotExist:
                return Response(
                    {'error': 'Doctor or assistant not found'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            except User.MultipleObjectsReturned:
                return Response(
                    {'error': 'Multiple staff members found, please be more specific'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Create the pause with room and color support
            pause_data = {
                'doctor': staff_member,  # Note: model field is still called 'doctor' but can store assistant too
                'title': data['title'],
                'date_from': timezone.make_aware(date_from) if timezone.is_naive(date_from) else date_from,
                'date_to': timezone.make_aware(date_to) if timezone.is_naive(date_to) else date_to,
                'notes': data.get('notes', ''),
                'is_recurring': data.get('is_recurring', False),
                'created_by': request.user if request.user.is_authenticated else None
            }
            
            # Add room field if provided
            if 'room' in data and data['room']:
                pause_data['room'] = data['room']
                
                # Auto-generate resource_id for calendar integration
                if not data.get('resource_id'):
                    if data['room'] == 'room-a':
                        pause_data['resource_id'] = '1'
                    elif data['room'] == 'room-b':
                        pause_data['resource_id'] = '2'
                    else:
                        pause_data['resource_id'] = data['room']
            
            # Add resource_id if explicitly provided
            if 'resource_id' in data and data['resource_id']:
                pause_data['resource_id'] = data['resource_id']
                
            # Add color field if provided
            if 'color' in data and data['color']:
                pause_data['color'] = data['color']
            
            pause = DoctorPause.objects.create(**pause_data)
            
            serializer = DoctorPauseSerializer(pause)
            return Response({
                'pause': serializer.data,
                'message': 'Pause created successfully'
            }, status=status.HTTP_201_CREATED)
            
    except Exception as e:
        logger.error(f"Error creating pause from modal: {str(e)}")
        return Response(
            {'error': f'Failed to create pause: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_doctor_pauses(request, doctor_id):
    """
    Get all pauses for a specific doctor.
    """
    try:
        doctor = get_object_or_404(User, id=doctor_id, user_type='doctor')
        
        # Get date range from query params
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        queryset = DoctorPause.objects.filter(doctor=doctor)
        
        if start_date and end_date:
            queryset = queryset.filter(
                date_from__date__gte=start_date,
                date_to__date__lte=end_date
            )
        
        pauses = queryset.order_by('date_from')
        serializer = DoctorPauseSerializer(pauses, many=True)
        
        return Response({
            'doctor': {
                'id': doctor.id,
                'name': doctor.get_full_name(),
                'email': doctor.email
            },
            'pauses': serializer.data
        })
        
    except Exception as e:
        logger.error(f"Error getting doctor pauses for {doctor_id}: {str(e)}")
        return Response(
            {'error': 'Failed to get doctor pauses'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['DELETE'])
@permission_classes([permissions.AllowAny])
def delete_pause(request, pause_id):
    """
    Delete a doctor pause.
    """
    try:
        pause = get_object_or_404(DoctorPause, id=pause_id)
        
        # Check if user has permission to delete (basic check)
        if request.user.is_authenticated and request.user != pause.doctor and request.user != pause.created_by:
            if not request.user.is_staff and not request.user.is_superuser:
                return Response(
                    {'error': 'Permission denied'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        
        pause.delete()
        
        return Response({
            'message': 'Pause deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"Error deleting pause {pause_id}: {str(e)}")
        return Response(
            {'error': f'Failed to delete pause: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['PUT'])
@permission_classes([permissions.AllowAny])
def update_pause(request, pause_id):
    """
    Update a doctor pause.
    """
    try:
        pause = get_object_or_404(DoctorPause, id=pause_id)
        
        # Check if user has permission to update (basic check)
        if request.user.is_authenticated and request.user != pause.doctor and request.user != pause.created_by:
            if not request.user.is_staff and not request.user.is_superuser:
                return Response(
                    {'error': 'Permission denied'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        
        serializer = DoctorPauseSerializer(pause, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        return Response({
            'pause': serializer.data,
            'message': 'Pause updated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error updating pause {pause_id}: {str(e)}")
        return Response(
            {'error': f'Failed to update pause: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
