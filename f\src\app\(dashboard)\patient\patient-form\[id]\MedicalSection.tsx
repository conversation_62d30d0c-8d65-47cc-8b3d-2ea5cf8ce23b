'use client';
import React, { useState, useEffect } from 'react';
import { Group, Text, Textarea, ActionIcon, Button, Stack, Loader, <PERSON><PERSON>, Modal,  } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiPlusBox, mdiHistory, mdiMicrophone, mdiClipboardText, mdiDeleteSweep, mdiTagPlus, mdiContentSave } from '@mdi/js';
import { TreeNodeData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import patientService, { PatientMedicalData } from '@/services/patientService';

interface MedicalSectionProps {
  title: string;
  placeholder?: string;
  textareaContent: string;
  onTextareaChange: (value: string) => void;
  onTextareaClick: (event: React.MouseEvent) => void;
  onAddNew?: () => void;
  onHistoryClick?: () => void;
  onMicrophoneClick?: () => void;
  onModelsClick?: () => void;
  onDeleteClick?: () => void;
  onTagPlusClick?: () => void;
  showAddIcon?: boolean;
  isVisible: boolean;
  data?: TreeNodeData[];
  // Django integration props
  patientId?: string;
  medicalDataType?: 'allergies' | 'medical_conditions' | 'medications' | 'custom';
  enableDjangoSync?: boolean;
  showSaveButton?: boolean;
  readOnly?: boolean;
}

export const MedicalSection: React.FC<MedicalSectionProps> = ({
  title,
  placeholder = "Ajouter",
  textareaContent,
  onTextareaChange,
  onTextareaClick,
  onAddNew,
  onHistoryClick,
  onMicrophoneClick,
  onModelsClick,
  onDeleteClick,
  onTagPlusClick,
  showAddIcon = false,
  isVisible,
  patientId,
  medicalDataType = 'custom',
  enableDjangoSync = false,
  showSaveButton = false,
  readOnly = false
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patientMedicalData, setPatientMedicalData] = useState<PatientMedicalData[]>([]);
  const [historyModalOpen, setHistoryModalOpen] = useState(false);

  // Load patient medical data when component mounts
  useEffect(() => {
    if (enableDjangoSync && patientId) {
      loadPatientMedicalData();
    }
  }, [enableDjangoSync, patientId, medicalDataType]);

  const loadPatientMedicalData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check Django connection
      const status = await patientService.checkDjangoBridgeStatus();
      if (status.status !== 'active') {
        console.warn('Django backend is not connected');
        return;
      }

      if (patientId) {
        // Load patient medical data from Django
        const medicalData = await patientService.getPatientMedicalData(patientId, medicalDataType);
        if (medicalData) {
          setPatientMedicalData(medicalData);
        }
      }
    } catch (error) {
      console.error('Error loading patient medical data:', error);
      setError('Failed to load medical data');
    } finally {
      setLoading(false);
    }
  };

  const saveToPatient = async () => {
    if (!patientId || !textareaContent.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please enter some content before saving',
        color: 'orange',
      });
      return;
    }

    try {
      setLoading(true);

      // Save medical data to Django
      const result = await patientService.addPatientMedicalData(patientId, {
        category_type: medicalDataType,
        medical_item_name: textareaContent,
        notes: `Added via ${title} section`
      });

      if (result) {
        notifications.show({
          title: 'Success',
          message: `${title} data saved successfully`,
          color: 'green',
        });

        // Reload data to get updated list
        await loadPatientMedicalData();
      }
    } catch (error) {
      console.error('Error saving medical data:', error);
      notifications.show({
        title: 'Error',
        message: `Failed to save ${title} data`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleHistoryClick = () => {
    if (onHistoryClick) {
      onHistoryClick();
    } else if (enableDjangoSync) {
      setHistoryModalOpen(true);
    }
  };
  return (
    <>
      <div className={isVisible ? 'w-[49%] ' : 'w-[49%] '}>
        {error && (
          <Alert color="red" mb="sm">
            {error}
          </Alert>
        )}

        <Group justify="space-between" gap="sm">
          <Group gap="sm">
            <Text fw={500}>{title}</Text>
            {enableDjangoSync && patientId && (
              <Text size="xs" c="dimmed">
                (Django Sync Enabled)
              </Text>
            )}
          </Group>
          <Group justify="flex-end" gap="sm">
            {showAddIcon && onAddNew && (
              <ActionIcon variant="transparent" onClick={onAddNew} title="Ajouter à la liste">
                <Icon path={mdiPlusBox} size={1} color={"#3799ce"} />
              </ActionIcon>
            )}
            <ActionIcon
              variant="transparent"
              onClick={handleHistoryClick}
              title="View history"
            >
              <Icon path={mdiHistory} size={1} color={"#3799ce"} />
            </ActionIcon>
            {onMicrophoneClick && (
              <ActionIcon variant="transparent" onClick={onMicrophoneClick}>
                <Icon path={mdiMicrophone} size={1} color={"#3799ce"} />
              </ActionIcon>
            )}
            {onModelsClick && (
              <ActionIcon variant="transparent" onClick={onModelsClick}>
                <Icon path={mdiClipboardText} size={1} color={"#3799ce"} />
              </ActionIcon>
            )}
            {onTagPlusClick && (
              <ActionIcon variant="transparent" onClick={onTagPlusClick}>
                <Icon path={mdiTagPlus} size={1} color={"#3799ce"} />
              </ActionIcon>
            )}
            {enableDjangoSync && showSaveButton && (
              <ActionIcon
                variant="transparent"
                onClick={saveToPatient}
                title="Save to patient record"
                loading={loading}
              >
                <Icon path={mdiContentSave} size={1} color={"#4caf50"} />
              </ActionIcon>
            )}
            {onDeleteClick && (
              <ActionIcon variant="transparent" onClick={onDeleteClick}>
                <Icon path={mdiDeleteSweep} size={1} color={"#e53935"} />
              </ActionIcon>
            )}
          </Group>
        </Group>

        <Textarea
          size="xl"
          radius="md"
          placeholder={placeholder}
          mt={10}
          mb={10}
          value={textareaContent}
          onChange={(event) => onTextareaChange(event.currentTarget.value)}
          onClick={(event) => onTextareaClick(event)}
          disabled={readOnly || loading}
        />

        {enableDjangoSync && showSaveButton && (
          <Group justify="flex-end" mt="xs">
            <Button
              size="sm"
              onClick={saveToPatient}
              disabled={!textareaContent.trim() || loading}
              loading={loading}
              leftSection={<Icon path={mdiContentSave} size={0.8} />}
            >
              Save to Patient
            </Button>
          </Group>
        )}
      </div>

      {/* History Modal */}
      <Modal
        opened={historyModalOpen}
        onClose={() => setHistoryModalOpen(false)}
        title={`${title} History`}
        size="lg"
      >
        <Stack gap="md">
          {loading ? (
            <Group justify="center">
              <Loader size="sm" />
              <Text size="sm" c="dimmed">Loading medical history...</Text>
            </Group>
          ) : patientMedicalData.length === 0 ? (
            <Text size="sm" c="dimmed" ta="center">
              No medical history found for {title.toLowerCase()}
            </Text>
          ) : (
            patientMedicalData.map((data, index) => (
              <div key={data.id || index} style={{
                padding: '12px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <Text size="sm" fw={500}>
                  {data.medical_item?.name || data.medical_item?.id || 'Unknown item'}
                </Text>
                {data.notes && (
                  <Text size="xs" c="dimmed" mt="xs">
                    Notes: {data.notes}
                  </Text>
                )}
                <Text size="xs" c="dimmed" mt="xs">
                  Added: {data.created_at ? new Date(data.created_at).toLocaleDateString() : 'Unknown date'}
                </Text>
              </div>
            ))
          )}
        </Stack>
      </Modal>
    </>
  );
};
