/**
 * Test Real Backend Integration for Lunch Modal
 * Verifies that real doctor data is loaded and used instead of hardcoded options
 */

const http = require('http');

console.log('🧪 TESTING REAL BACKEND INTEGRATION');
console.log('=' * 60);

// Test configuration
const API_BASE = 'http://127.0.0.1:8000/api';

// Make HTTP request function
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonData,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: data,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', reject);
        req.end();
    });
}

// Test 1: Verify Real Doctor Data Loading
async function testRealDoctorLoading() {
    console.log('\n🔍 TEST 1: VERIFY REAL DOCTOR DATA LOADING');
    console.log('-'.repeat(50));
    
    try {
        const response = await makeRequest(`${API_BASE}/users/doctors/`);
        
        if (response.status === 200) {
            const doctors = response.data?.results || response.data || [];
            
            console.log('✅ Real doctor data loaded successfully');
            console.log(`📊 Found ${doctors.length} doctors`);
            
            // Check for specific doctor we know should exist
            const doctorMorade = doctors.find(doc => 
                doc.id === '0359bdc6-1235-4a31-a095-097007f0b415' &&
                doc.first_name === 'doctor' &&
                doc.last_name === 'morade'
            );
            
            if (doctorMorade) {
                console.log('✅ Authorized doctor "Dr. doctor morade" found in backend');
                console.log('   ID:', doctorMorade.id);
                console.log('   Name:', `${doctorMorade.first_name} ${doctorMorade.last_name}`);
                console.log('   Email:', doctorMorade.email);
            } else {
                console.log('❌ Authorized doctor "Dr. doctor morade" not found');
            }
            
            // Verify all known doctors
            const knownDoctorIds = [
                '0359bdc6-1235-4a31-a095-097007f0b415',
                'da8adc01-e567-491b-b027-19760707105b',
                '657d3f41-5019-42a9-8bc1-eb84948e75b8'
            ];
            
            let allKnownFound = true;
            knownDoctorIds.forEach(id => {
                const found = doctors.find(doc => doc.id === id);
                if (found) {
                    console.log(`✅ Known doctor ${id} found: ${found.first_name} ${found.last_name}`);
                } else {
                    console.log(`❌ Known doctor ${id} NOT found`);
                    allKnownFound = false;
                }
            });
            
            return {
                success: true,
                doctorCount: doctors.length,
                authorizedDoctorFound: !!doctorMorade,
                allKnownFound,
                doctors
            };
        } else {
            console.log(`❌ Failed to load doctors: ${response.status} - ${response.statusText}`);
            return { success: false, error: `HTTP ${response.status}` };
        }
    } catch (error) {
        console.log(`❌ Error loading doctors: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// Test 2: Verify Staff Options Generation
function testStaffOptionsGeneration(doctors) {
    console.log('\n🔍 TEST 2: VERIFY STAFF OPTIONS GENERATION');
    console.log('-'.repeat(50));
    
    if (!doctors || doctors.length === 0) {
        console.log('❌ No doctors provided for staff options generation');
        return { success: false, staffOptions: [] };
    }
    
    // Simulate the frontend staff options generation logic
    const staffOptions = [];
    
    // Sort doctors to prioritize Dr. doctor morade
    const sortedDoctors = [...doctors].sort((a, b) => {
        const aFullName = `${a.first_name} ${a.last_name}`.trim();
        const bFullName = `${b.first_name} ${b.last_name}`.trim();
        
        if (aFullName.includes('doctor morade')) return -1;
        if (bFullName.includes('doctor morade')) return 1;
        return aFullName.localeCompare(bFullName);
    });
    
    sortedDoctors.forEach(doctor => {
        const firstName = doctor.first_name || '';
        const lastName = doctor.last_name || '';
        const fullName = firstName && lastName ? 
            `${firstName} ${lastName}` : 
            (firstName || lastName || doctor.email?.split('@')[0] || 'Unknown');
        
        staffOptions.push({
            label: `Dr. ${fullName}`.trim(),
            value: doctor.id,
            type: 'doctor'
        });
    });
    
    console.log('✅ Generated staff options:');
    staffOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label} (${option.value})`);
    });
    
    // Verify Dr. doctor morade is first
    const firstOption = staffOptions[0];
    const isDoctorMoradeFirst = firstOption.label.includes('doctor morade');
    
    if (isDoctorMoradeFirst) {
        console.log('✅ Dr. doctor morade is prioritized as first option');
    } else {
        console.log('❌ Dr. doctor morade is NOT prioritized as first option');
    }
    
    return {
        success: true,
        staffOptions,
        doctorMoradeFirst: isDoctorMoradeFirst,
        totalOptions: staffOptions.length
    };
}

// Test 3: Compare with Previous Hardcoded Data
function testHardcodedComparison(realStaffOptions) {
    console.log('\n🔍 TEST 3: COMPARE WITH PREVIOUS HARDCODED DATA');
    console.log('-'.repeat(50));
    
    const previousHardcodedOptions = [
        { label: 'Dr. doctor morade', value: '0359bdc6-1235-4a31-a095-097007f0b415' },
        { label: 'Dr. ggg gggg', value: 'da8adc01-e567-491b-b027-19760707105b' },
        { label: 'Dr. test -2', value: '657d3f41-5019-42a9-8bc1-eb84948e75b8' }
    ];
    
    console.log('PREVIOUS HARDCODED OPTIONS:');
    previousHardcodedOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label} (${option.value})`);
    });
    
    console.log('\nNEW REAL BACKEND OPTIONS:');
    realStaffOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.label} (${option.value})`);
    });
    
    console.log('\nCOMPARISON RESULTS:');
    
    let perfectMatch = true;
    let matchCount = 0;
    
    previousHardcodedOptions.forEach(hardcoded => {
        const found = realStaffOptions.find(real => real.value === hardcoded.value);
        if (found) {
            console.log(`✅ Match: ${hardcoded.label} -> ${found.label}`);
            matchCount++;
        } else {
            console.log(`❌ Missing: ${hardcoded.label} not found in real data`);
            perfectMatch = false;
        }
    });
    
    const additionalOptions = realStaffOptions.filter(real => 
        !previousHardcodedOptions.some(hardcoded => hardcoded.value === real.value)
    );
    
    if (additionalOptions.length > 0) {
        console.log('\n📈 ADDITIONAL OPTIONS FROM BACKEND:');
        additionalOptions.forEach((option, index) => {
            console.log(`${index + 1}. ${option.label} (NEW)`);
        });
    }
    
    return {
        perfectMatch,
        matchCount,
        totalPrevious: previousHardcodedOptions.length,
        totalNew: realStaffOptions.length,
        additionalCount: additionalOptions.length
    };
}

// Test 4: Integration Status Summary
function testIntegrationStatus(doctorResult, staffResult, comparisonResult) {
    console.log('\n🔍 TEST 4: INTEGRATION STATUS SUMMARY');
    console.log('-'.repeat(50));
    
    console.log('INTEGRATION CHECKLIST:');
    
    // Backend connectivity
    if (doctorResult.success) {
        console.log('✅ Backend connectivity: WORKING');
        console.log(`   - ${doctorResult.doctorCount} doctors loaded from API`);
    } else {
        console.log('❌ Backend connectivity: FAILED');
        console.log(`   - Error: ${doctorResult.error}`);
    }
    
    // Authorized doctor access
    if (doctorResult.authorizedDoctorFound) {
        console.log('✅ Authorized doctor access: CONFIRMED');
        console.log('   - Dr. doctor morade found and accessible');
    } else {
        console.log('❌ Authorized doctor access: FAILED');
        console.log('   - Dr. doctor morade not found');
    }
    
    // Staff options generation
    if (staffResult.success && staffResult.doctorMoradeFirst) {
        console.log('✅ Staff options generation: WORKING');
        console.log(`   - ${staffResult.totalOptions} options generated`);
        console.log('   - Dr. doctor morade prioritized correctly');
    } else {
        console.log('❌ Staff options generation: ISSUES');
        if (!staffResult.success) {
            console.log('   - Generation failed');
        }
        if (!staffResult.doctorMoradeFirst) {
            console.log('   - Dr. doctor morade not prioritized');
        }
    }
    
    // Data consistency
    if (comparisonResult.matchCount === comparisonResult.totalPrevious) {
        console.log('✅ Data consistency: MAINTAINED');
        console.log('   - All previous hardcoded data available in backend');
    } else {
        console.log('❌ Data consistency: PARTIAL');
        console.log(`   - ${comparisonResult.matchCount}/${comparisonResult.totalPrevious} previous options found`);
    }
    
    // Overall status
    const allChecksPass = doctorResult.success && 
                         doctorResult.authorizedDoctorFound && 
                         staffResult.success && 
                         staffResult.doctorMoradeFirst &&
                         comparisonResult.matchCount === comparisonResult.totalPrevious;
    
    if (allChecksPass) {
        console.log('\n🎉 OVERALL STATUS: ✅ INTEGRATION SUCCESSFUL');
        console.log('The lunch modal now uses real backend data instead of hardcoded options');
    } else {
        console.log('\n⚠️  OVERALL STATUS: ❌ INTEGRATION NEEDS ATTENTION');
        console.log('Some issues detected that may affect functionality');
    }
    
    return allChecksPass;
}

// Main test runner
async function runRealBackendIntegrationTests() {
    console.log('🚀 RUNNING REAL BACKEND INTEGRATION TESTS');
    console.log('='.repeat(60));
    
    try {
        // Test 1: Load real doctor data
        const doctorResult = await testRealDoctorLoading();
        
        // Test 2: Generate staff options (if doctors loaded successfully)
        let staffResult = { success: false, staffOptions: [] };
        if (doctorResult.success) {
            staffResult = testStaffOptionsGeneration(doctorResult.doctors);
        }
        
        // Test 3: Compare with hardcoded data
        let comparisonResult = { matchCount: 0, totalPrevious: 0 };
        if (staffResult.success) {
            comparisonResult = testHardcodedComparison(staffResult.staffOptions);
        }
        
        // Test 4: Integration status summary
        const integrationSuccessful = testIntegrationStatus(doctorResult, staffResult, comparisonResult);
        
        console.log('\n📊 FINAL TEST RESULTS');
        console.log('='.repeat(40));
        console.log(`Backend Status: ${doctorResult.success ? 'WORKING' : 'FAILED'}`);
        console.log(`Doctors Loaded: ${doctorResult.doctorCount || 0}`);
        console.log(`Staff Options Generated: ${staffResult.totalOptions || 0}`);
        console.log(`Data Consistency: ${comparisonResult.matchCount}/${comparisonResult.totalPrevious} matches`);
        console.log(`Integration Status: ${integrationSuccessful ? 'SUCCESS' : 'NEEDS ATTENTION'}`);
        
        if (integrationSuccessful) {
            console.log('\n🎯 NEXT STEPS:');
            console.log('✅ Real backend data is now being used');
            console.log('✅ Lunch modal will show actual doctor names');
            console.log('✅ No more hardcoded fallback data being used');
            console.log('✅ Integration complete and working properly');
        }
        
        return integrationSuccessful;
        
    } catch (error) {
        console.error('❌ Error running integration tests:', error.message);
        return false;
    }
}

// Run the tests
runRealBackendIntegrationTests().then(success => {
    if (success) {
        console.log('\n✅ REAL BACKEND INTEGRATION TESTING COMPLETE');
        console.log('The lunch modal now uses real backend data successfully!');
    } else {
        console.log('\n❌ REAL BACKEND INTEGRATION TESTING FAILED');
        console.log('Please check the results above and address any issues');
    }
});