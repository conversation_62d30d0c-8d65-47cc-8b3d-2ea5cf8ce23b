/**
 * Medical Report Service
 * Handles all medical-report related data operations including:
 * - Medical exam reports and templates
 * - Billing system (invoices, quotes, payments)
 * - Contracts and subscriptions
 * - Medical document management
 */

// Types for Medical Report data
export interface ExamReport {
  id: string;
  exam_date: string;
  patient_id: string;
  patient_name: string;
  template_id: string;
  template_title: string;
  indication: string;
  findings: string;
  diagnosis: string;
  recommendations: string;
  doctor_id: string;
  doctor_name: string;
  status: 'draft' | 'completed' | 'reviewed' | 'archived';
  created_at: string;
  updated_at: string;
}

export interface ExamTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  fields: TemplateField[];
  color?: string;
  is_active: boolean;
  created_by: string;
  created_at: string;
}

export interface TemplateField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'date' | 'number';
  label: string;
  required: boolean;
  options?: string[];
  default_value?: string;
}

export interface BillingInvoice {
  id: string;
  invoice_number: string;
  patient_id: string;
  patient_name: string;
  issue_date: string;
  due_date: string;
  total_amount: number;
  paid_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  items: InvoiceItem[];
  notes?: string;
  created_at: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  procedure_code?: string;
}

export interface BillingQuote {
  id: string;
  quote_number: string;
  patient_id: string;
  patient_name: string;
  issue_date: string;
  expiry_date: string;
  total_amount: number;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
  items: QuoteItem[];
  notes?: string;
  created_at: string;
}

export interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  procedure_code?: string;
}

export interface Payment {
  id: string;
  payment_date: string;
  amount: number;
  method: 'cash' | 'card' | 'check' | 'transfer' | 'insurance';
  reference: string;
  patient_id: string;
  patient_name: string;
  invoice_id?: string;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  notes?: string;
  created_at: string;
}

export interface Contract {
  id: string;
  contract_number: string;
  patient_id: string;
  patient_name: string;
  start_date: string;
  end_date: string;
  contract_type: 'subscription' | 'treatment_plan' | 'insurance' | 'maintenance';
  total_value: number;
  monthly_amount?: number;
  status: 'active' | 'expired' | 'cancelled' | 'suspended';
  terms: string;
  created_at: string;
}

export interface MedicalDocument {
  id: string;
  title: string;
  type: 'report' | 'prescription' | 'certificate' | 'referral' | 'consent';
  patient_id: string;
  patient_name: string;
  doctor_id: string;
  doctor_name: string;
  content: string;
  template_id?: string;
  created_at: string;
  signed: boolean;
  signature_date?: string;
}

export interface MedicalReportSummary {
  reportDate: string;
  examReports: ExamReport[];
  billingInvoices: BillingInvoice[];
  billingQuotes: BillingQuote[];
  payments: Payment[];
  contracts: Contract[];
  medicalDocuments: MedicalDocument[];
  templates: ExamTemplate[];
  lastUpdate: string;
}

class MedicalReportService {
  private baseURL = '/api/medical-reports';

  // Exam Reports operations
  async getExamReports(patientId?: string, dateRange?: { start: string; end: string }): Promise<ExamReport[]> {
    try {
      let url = `${this.baseURL}/exam-reports`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`⚠️ Medical reports API not available (${response.status}), using mock data`);
        return this.getMockExamReports(patientId);
      }
      return await response.json();
    } catch (error) {
      console.warn('⚠️ Medical reports API error, using mock data:', error);
      return this.getMockExamReports(patientId);
    }
  }

  async createExamReport(reportData: Omit<ExamReport, 'id' | 'created_at' | 'updated_at'>): Promise<ExamReport> {
    try {
      const response = await fetch(`${this.baseURL}/exam-reports`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create exam report: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error creating exam report:', error);
      // Return mock created data
      return {
        id: Date.now().toString(),
        ...reportData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    }
  }

  async updateExamReport(id: string, reportData: Partial<ExamReport>): Promise<ExamReport> {
    try {
      const response = await fetch(`${this.baseURL}/exam-reports/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update exam report: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error updating exam report:', error);
      // Return mock updated data
      return {
        id,
        ...reportData,
        updated_at: new Date().toISOString(),
      } as ExamReport;
    }
  }

  async deleteExamReport(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/exam-reports/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete exam report: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting exam report:', error);
      // For mock purposes, we'll just log the deletion
      console.log(`Mock deletion of exam report ${id}`);
    }
  }

  // Billing operations
  async getBillingInvoices(patientId?: string, status?: string): Promise<BillingInvoice[]> {
    try {
      let url = `${this.baseURL}/billing/invoices`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (status) params.append('status', status);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch billing invoices: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching billing invoices:', error);
      return this.getMockBillingInvoices(patientId);
    }
  }

  async getBillingQuotes(patientId?: string, status?: string): Promise<BillingQuote[]> {
    try {
      let url = `${this.baseURL}/billing/quotes`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (status) params.append('status', status);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch billing quotes: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching billing quotes:', error);
      return this.getMockBillingQuotes(patientId);
    }
  }

  async getPayments(patientId?: string, dateRange?: { start: string; end: string }): Promise<Payment[]> {
    try {
      let url = `${this.baseURL}/payments`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (dateRange) {
        params.append('start', dateRange.start);
        params.append('end', dateRange.end);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch payments: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching payments:', error);
      return this.getMockPayments(patientId);
    }
  }

  // Contracts operations
  async getContracts(patientId?: string, status?: string): Promise<Contract[]> {
    try {
      let url = `${this.baseURL}/contracts`;
      const params = new URLSearchParams();
      
      if (patientId) params.append('patient_id', patientId);
      if (status) params.append('status', status);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch contracts: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching contracts:', error);
      return this.getMockContracts(patientId);
    }
  }

  // Templates operations
  async getExamTemplates(): Promise<ExamTemplate[]> {
    try {
      const response = await fetch(`${this.baseURL}/templates`);
      if (!response.ok) {
        throw new Error(`Failed to fetch exam templates: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching exam templates:', error);
      return this.getMockExamTemplates();
    }
  }

  // Get comprehensive medical report summary
  async getMedicalReportSummary(patientId?: string, dateRange?: { start: string; end: string }): Promise<MedicalReportSummary> {
    try {
      const [examReports, billingInvoices, billingQuotes, payments, contracts, templates] = await Promise.all([
        this.getExamReports(patientId, dateRange),
        this.getBillingInvoices(patientId),
        this.getBillingQuotes(patientId),
        this.getPayments(patientId, dateRange),
        this.getContracts(patientId),
        this.getExamTemplates(),
      ]);

      return {
        reportDate: new Date().toISOString(),
        examReports,
        billingInvoices,
        billingQuotes,
        payments,
        contracts,
        medicalDocuments: [], // Would be populated from another endpoint
        templates,
        lastUpdate: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching medical report summary:', error);
      throw error;
    }
  }

  // Mock data methods for development
  private getMockExamReports(patientId?: string): ExamReport[] {
    const mockData: ExamReport[] = [
      {
        id: '1',
        exam_date: '2024-01-20',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        template_id: 'template-1',
        template_title: 'Consultation Générale',
        indication: 'Contrôle de routine',
        findings: 'Examen normal, pas d\'anomalies détectées',
        diagnosis: 'État de santé satisfaisant',
        recommendations: 'Continuer les soins préventifs',
        doctor_id: 'doc-1',
        doctor_name: 'Dr. Martin',
        status: 'completed',
        created_at: '2024-01-20T10:00:00Z',
        updated_at: '2024-01-20T10:30:00Z',
      },
    ];

    return patientId ? mockData.filter(r => r.patient_id === patientId) : mockData;
  }

  private getMockBillingInvoices(patientId?: string): BillingInvoice[] {
    const mockData: BillingInvoice[] = [
      {
        id: '1',
        invoice_number: 'INV-2024-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        issue_date: '2024-01-20',
        due_date: '2024-02-20',
        total_amount: 150,
        paid_amount: 150,
        status: 'paid',
        items: [
          {
            id: '1',
            description: 'Consultation générale',
            quantity: 1,
            unit_price: 150,
            total_price: 150,
            procedure_code: 'CONS-001',
          },
        ],
        created_at: '2024-01-20T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(i => i.patient_id === patientId) : mockData;
  }

  private getMockBillingQuotes(patientId?: string): BillingQuote[] {
    const mockData: BillingQuote[] = [
      {
        id: '1',
        quote_number: 'QUO-2024-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        issue_date: '2024-01-15',
        expiry_date: '2024-02-15',
        total_amount: 500,
        status: 'sent',
        items: [
          {
            id: '1',
            description: 'Traitement orthodontique',
            quantity: 1,
            unit_price: 500,
            total_price: 500,
            procedure_code: 'ORTHO-001',
          },
        ],
        created_at: '2024-01-15T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(q => q.patient_id === patientId) : mockData;
  }

  private getMockPayments(patientId?: string): Payment[] {
    const mockData: Payment[] = [
      {
        id: '1',
        payment_date: '2024-01-20',
        amount: 150,
        method: 'card',
        reference: 'PAY-2024-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        invoice_id: '1',
        status: 'completed',
        created_at: '2024-01-20T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(p => p.patient_id === patientId) : mockData;
  }

  private getMockContracts(patientId?: string): Contract[] {
    const mockData: Contract[] = [
      {
        id: '1',
        contract_number: 'CON-2024-001',
        patient_id: patientId || '1',
        patient_name: 'Jean Dupont',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        contract_type: 'subscription',
        total_value: 1200,
        monthly_amount: 100,
        status: 'active',
        terms: 'Contrat de soins annuel avec visites mensuelles',
        created_at: '2024-01-01T10:00:00Z',
      },
    ];

    return patientId ? mockData.filter(c => c.patient_id === patientId) : mockData;
  }

  private getMockExamTemplates(): ExamTemplate[] {
    return [
      {
        id: 'template-1',
        title: 'Consultation Générale',
        description: 'Template pour consultation générale',
        category: 'general',
        fields: [
          {
            id: 'indication',
            name: 'indication',
            type: 'textarea',
            label: 'Indication',
            required: true,
          },
          {
            id: 'findings',
            name: 'findings',
            type: 'textarea',
            label: 'Constatations',
            required: true,
          },
        ],
        color: '#4CAF50',
        is_active: true,
        created_by: 'admin',
        created_at: '2024-01-01T10:00:00Z',
      },
    ];
  }
}

export const medicalReportService = new MedicalReportService();
export default medicalReportService;
