'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import { Patient_par_tranche_dage } from './Patient_par_tranche_dage';

export default function PatientParTrancheDAgeDemo() {
  const handleRangeChange = (range: any) => {
    console.log('Tranche d\'âge changée:', range);
    alert(`Tranche d'âge sélectionnée: ${range.label}`);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre changé:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé pour les tranches d\'âge');
    } else {
      alert('Filtre avancé désactivé');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format}`);
    alert(`Export ${format} des patients par tranche d'âge en cours...`);
  };

  const handlePrint = () => {
    console.log('Impression');
    alert('Impression des patients par tranche d\'âge en cours...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri de la colonne ${columnId} en ${direction}`);
    const columnLabels: { [key: string]: string } = {
      'total': 'Total',
      'age_range': 'Tranche d\'âge'
    };
    alert(`Tri de "${columnLabels[columnId]}" en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={false}
          onRangeChange={handleRangeChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation en mode chargement
export function PatientParTrancheDAgeLoadingDemo() {
  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={true}
          onRangeChange={(range) => console.log('Range:', range)}
          onFilterChange={(filter) => console.log('Filter:', filter)}
          onExport={(format) => console.log('Export:', format)}
          onPrint={() => console.log('Print')}
          onSort={(columnId, direction) => console.log('Sort:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données simulées
export function PatientParTrancheDAgeWithDataDemo() {
  const handleRangeChange = (range: any) => {
    console.log('Tranche avec données:', range);
    
    const rangeMessages: { [key: string]: string } = {
      'all_ages': 'Affichage de tous les patients (tous âges)',
      'pediatric': 'Affichage des patients pédiatriques (≤18 ans)',
      'elderly': 'Affichage des personnes âgées (>55 ans)'
    };
    
    const message = rangeMessages[range.name] || 'Tranche d\'âge sélectionnée';
    alert(message);
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre avec données:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé activé - Analyse détaillée par tranche d\'âge');
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    console.log(`Export ${format} avec données`);
    alert(`Préparation de l'export ${format.toUpperCase()} des patients par tranche d'âge avec les données...`);
  };

  const handlePrint = () => {
    console.log('Impression avec données');
    alert('Préparation de l\'impression des patients par tranche d\'âge avec les données...');
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log(`Tri avec données: ${columnId} ${direction}`);
    
    const sortMessages: { [key: string]: string } = {
      'total': 'Tri des patients par nombre total',
      'age_range': 'Tri des tranches d\'âge'
    };
    
    const message = sortMessages[columnId] || 'Tri de la colonne';
    alert(`${message} en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={false}
          onRangeChange={handleRangeChange}
          onFilterChange={handleFilterChange}
          onExport={handleExport}
          onPrint={handlePrint}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec mode pédiatrique
export function PatientParTrancheDAgepediatricDemo() {
  const handleRangeChange = (range: any) => {
    console.log('Mode pédiatrique:', range);
    if (range.type === 'pediatric') {
      alert('Mode Pédiatrique activé - Patients de 18 ans et moins:\n- Nourrissons (0-2 ans)\n- Enfants (3-12 ans)\n- Adolescents (13-18 ans)');
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre pédiatrique:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé pédiatrique:\n- Groupes d\'âge spécialisés\n- Vaccinations par âge\n- Croissance et développement');
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={false}
          onRangeChange={handleRangeChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données pédiatriques`)}
          onPrint={() => alert('Impression des données pédiatriques')}
          onSort={(columnId, direction) => console.log('Sort pédiatrique:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec gestion des erreurs
export function PatientParTrancheDAgeErrorDemo() {
  const handleRangeChange = (range: any) => {
    console.log('Tranche avec validation:', range);
    
    // Simuler une validation
    if (range.deactivated) {
      alert('Cette tranche d\'âge est temporairement désactivée');
      return;
    }
    
    console.log('Tranche d\'âge validée:', range.label);
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={false}
          onRangeChange={handleRangeChange}
          onFilterChange={(filter) => console.log('Filter avec validation:', filter)}
          onExport={(format) => {
            console.log(`Export ${format} avec validation`);
            if (confirm(`Êtes-vous sûr de vouloir exporter les patients par tranche d'âge en ${format.toUpperCase()} ?`)) {
              alert('Export en cours...');
            }
          }}
          onPrint={() => {
            console.log('Impression avec validation');
            if (confirm('Êtes-vous sûr de vouloir imprimer les patients par tranche d\'âge ?')) {
              alert('Impression en cours...');
            }
          }}
          onSort={(columnId, direction) => console.log('Sort avec validation:', columnId, direction)}
        />
      </div>
    </MantineProvider>
  );
}

// Exemple d'utilisation avec données de tranches d'âge simulées
export function PatientParTrancheDAgeSimulatedDataDemo() {
  const handleRangeChange = (range: any) => {
    console.log('Tranche données simulées:', range);
    
    // Simuler des données selon la tranche
    const mockData: { [key: string]: any } = {
      'all_ages': {
        total: 95,
        ranges: [
          { age: '00-10', count: 1, percentage: 1.1 },
          { age: '10-20', count: 8, percentage: 8.4 },
          { age: '20-30', count: 28, percentage: 29.5 },
          { age: '30-40', count: 31, percentage: 32.6 },
          { age: '40-50', count: 14, percentage: 14.7 },
          { age: '50-60', count: 10, percentage: 10.5 },
          { age: '60-70', count: 2, percentage: 2.1 },
          { age: '330-340', count: 1, percentage: 1.1 }
        ]
      },
      'pediatric': {
        total: 9,
        ranges: [
          { age: '00-10', count: 1, percentage: 11.1 },
          { age: '10-18', count: 8, percentage: 88.9 }
        ]
      },
      'elderly': {
        total: 12,
        ranges: [
          { age: '55-65', count: 10, percentage: 83.3 },
          { age: '65-75', count: 2, percentage: 16.7 }
        ]
      }
    };
    
    const data = mockData[range.name];
    if (data) {
      alert(`${range.label}:\n${data.total} patients au total\n${data.ranges.length} tranches d'âge`);
    }
  };

  const handleFilterChange = (filter: any) => {
    console.log('Filtre données simulées:', filter);
    if (filter.showAdvancedFilter) {
      alert('Filtre avancé: Analyse démographique détaillée avec pourcentages et tendances');
    }
  };

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    console.log('Tri données simulées:', columnId, direction);
    
    // Simuler le tri des données
    if (columnId === 'total') {
      alert(`Tri des patients par nombre total en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
    } else if (columnId === 'age_range') {
      alert(`Tri des tranches d'âge en ordre ${direction === 'asc' ? 'croissant' : 'décroissant'}`);
    }
  };

  return (
    <MantineProvider>
      <div style={{ height: '100vh' }}>
        <Patient_par_tranche_dage
          loading={false}
          onRangeChange={handleRangeChange}
          onFilterChange={handleFilterChange}
          onExport={(format) => alert(`Export ${format} des données simulées par tranche d'âge`)}
          onPrint={() => alert('Impression des données simulées par tranche d\'âge')}
          onSort={handleSort}
        />
      </div>
    </MantineProvider>
  );
}
