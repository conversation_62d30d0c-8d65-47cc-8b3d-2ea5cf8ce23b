"use client";
import React from "react";
import PatientRecord from './PatientRecord';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import Link from 'next/link';
import { DictionaryModalsManager } from '@/components/alerte';
import {
  Patient,
  Appointment,
  AppointmentEvent,
  Event,
  ConsultationType,
  TitleOption,
  AgendaType,
  EventType
} from '@/types/typesCalendarPatient';
import { notifications } from '@mantine/notifications';

// Interface for backend patient data
interface BackendPatientData {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  phone_numbers?: string;
  address?: string;
  date_of_birth?: string;
  birth_date?: string;
  age?: number;
  gender?: string;
  cin?: string;
  social_security?: string;
  socialSecurity?: string;
  etat_civil?: string;
  etatCivil?: string;
  etat_aganda?: string;
  etatAgenda?: string;
  doctor_assigned?: string;
  docteur?: string;
  title?: string;
  notes?: string;
  comment?: string;
  agenda?: string;
  is_active?: boolean;
}

// Interface for backend staff data
interface BackendStaffData {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  user_type?: string;
}
import Icon from '@mdi/react';
import { mdiAccountTie,mdiCalendarMonth,mdiUpdate ,mdiDotsVertical ,mdiListBoxOutline ,mdiArrowRightThin, mdiDeleteClockOutline,
  mdiBedQueenOutline,mdiTooth,mdiFileDocument,mdiCalendarPlus,mdiAccountAlert,mdiAlertPlus,mdiCommentPlus,mdiShare,mdiPen,
mdiFileAccount,mdiNeedle,mdiPower,mdiCurrencyUsd,mdiArrowUp,mdiAccountArrowRight,mdiArrowDown,mdiPencilBoxOutline,mdiReply,mdiFileDocumentCheck,
mdiCashMultiple,mdiArrowLeft,mdiFilterVariant,mdiPlus,mdiCheck,mdiAccountBoxEditOutline,mdiPlaylistCheck,mdiDeleteSweep,mdiClipboardText,
mdiMicrophone,mdiHistory,mdiArrowRight,mdiViewHeadline,mdiChevronDown,mdiChevronRight,mdiDelete,mdiPencil,mdiCircle,mdiFileTree,mdiMapMarkerPath,
mdiClipboardEditOutline
} from '@mdi/js';
import { useForm } from '@mantine/form';
import {ScrollArea ,Badge,Tooltip,rem,Indicator ,Alert, Flex,List,Card,Avatar,Select,  Paper, Button, Modal, TextInput, NumberInput, Group,  Text, ActionIcon, Menu, Box, Switch
,Stack,Textarea,Radio,MultiSelect,Table,Input,Checkbox,
} from '@mantine/core';
import {  IconEdit, } from '@tabler/icons-react';
import { FloatingIndicator, Tabs } from '@mantine/core';
import { Loader } from '@mantine/core';
import { Container,  } from "@mantine/core";
import { IconDots, IconArrowRight ,IconArrowsExchange, } from "@tabler/icons-react";
import {ToolbarCalendarNav}  from "./ToolbarCalendarNav"
import MenuIcons from '@/components/agenda/icons/MenuIcons';
import { IconClock2,IconInfoCircle, IconRefresh,IconCheck ,IconX,IconWallpaper ,IconArrowBackUpDouble,IconColorPicker,IconFilePencil,
  IconCalendarClock,IconChevronDown,IconShieldCheck ,IconTrash,IconPhoneCall,IconAlarm 
 } from '@tabler/icons-react';
import CalendarHeader from './CalendarHeader';
import PatientList from "@/components/agenda/Appointments/PatientList";
import AjouterUnRendezVous from './AjouterUnRendezVous';
import InfoModal from './InfoModal';
import PatientDetailsModal from './PatientDetailsModal';
import { ThemeIcon } from '@mantine/core';
import { IconTextPlus,IconMessageShare } from '@tabler/icons-react';
//import { ListPlus, ListTree } from 'lucide-react';
//import { MessagesSquare,} from 'lucide-react';
//import { ClipboardType,MapPinHouse } from 'lucide-react';
//import { FilePenLine } from 'lucide-react';
import {  ColorPicker } from '@mantine/core';

// import {
//   CalendarClock,
//   ChevronDown,
//   ShieldCheck,
//   Trash2,
//   PhoneCall,
//   AlarmClock,
// } from "lucide-react";

import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import { Calendar as BigCalendar, Views, momentLocalizer } from 'react-big-calendar';
import type { EventInteractionArgs } from "react-big-calendar/lib/addons/dragAndDrop"; // Add this line
import withDragAndDrop from "react-big-calendar/lib/addons/dragAndDrop";
import appointmentService from '@/services/appointmentService';
import { patientFormService } from '@/services/patientFormService';
//import dentistryService from '@/services/dentistryService';
//import { globalPatientService } from '@/services/globalPatientService';
import {
  useState,
  useEffect,
  isValidElement,
  useCallback,
  useMemo,

} from "react";
import { useDisclosure,  } from "@mantine/hooks";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "./DayView.css";
import moment from "moment";
import "moment/locale/fr";
import TimeSlot from "@/components/agenda/Appointments/PatientsCalendar/TimeSlot";
import cx from "clsx";
import { useListState } from "@mantine/hooks";
type Gender = 'Homme ' | 'Femme' | 'Enfant';
import classes from "@/styles/DndList.module.css";
import { IconStethoscope } from '@tabler/icons-react';


// start alert
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}
type PatientActionsProps = {
  patientId?: string;
    isFormInvalid: boolean;
    isDraft: boolean;
    onPrint?: () => void;
    onPrevious?: () => void;
    onNext?: () => void;
    onStartVisit?: () => void;
    onAppointment?: () => void;
    onCancel?: () => void;
    onSaveQuitNew?: () => void;
    onSaveQuit?: () => void;
    onGoBack: () => void;
    onAddMeasurement: () => void;
    onGoToContract: () => void;
    selectedInsurance: string;
    setSelectedInsurance: (value: string) => void;
    affiliateNumber: string;
    setAffiliateNumber: (value: string) => void;
    affiliateType: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT';
    setAffiliateType: (value: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT') => void;
    organizationOptions: { value: string; label: string }[];
     value: string | null;
    onChang: (val: string | null) => void;
    onAdd?: () => void;
    locked?: boolean;
     countryId: number | null;
    provinceId: number | null;
    values: Location | null;
    onChange: (city: Location | null) => void;
    disabled?: boolean;
      onSubmit: (values: AlertFormValues, autoTrigger: boolean) => void;
  fullName?: string;
  staffOptions: { label: string; value: string }[];
  triggerOptions: { label: string; value: string }[];
};
interface FichePatientProps {
  onInsuredChange?: (isInsured: boolean) => void;
}
//end alert

// Type spécifique pour le formulaire de rendez-vous
interface AppointmentFormValues {
  patientId: string;
  notes: string;
  date: Date;
  duration: number;
  type: string;
  resourceId: number;
  addToWaitingList: boolean;
  removeFromCalendar: boolean;
  rescheduleDateTime: string;
}



// Type spécifique pour le formulaire patient
interface PatientFormValues {
  title: string;
  first_name: string;
  last_name: string;
  age: number;
  phone_numbers: string;
  etatAganda: string;
  docteur: string;
  event_Title: string;
  gender: string;
  socialSecurity: string;
  duration: string;
  agenda: string;
  comment: string;
  address: string;
  type: string;
  resourceId: number;
  name: string;
  prenom: string;
  email: string;
  typeConsultation: string;
  date: Date | string;
  commentairelistedattente: string;
  etatCivil: string;
  patientTitle: string;
}
 const initialConsultationTypes: ConsultationType[] = [
 { value: '1er Consultation', label: '1er Consultation', duration: '15min' },
 { value: "Changement D'élastique'", label: "Changement D'élastique'", duration: '15min' },
 { value: 'Chirurgie/Paro', label: 'Chirurgie/Paro', duration: '30min' },
 { value: 'Collage', label: 'Collage', duration: '30min' },
 { value: 'Composite', label: 'Composite', duration: '15min' },
 { value: 'Consultation', label: 'Consultation', duration: '15min' },
 { value: 'Contention', label: 'Contention', duration: '30min' },
 { value: 'Contrôle', label: 'Contrôle', duration: '15min' },
 { value: 'Depose Sutures', label: 'Depose Sutures', duration: '15min' },
 { value: 'Detartrage', label: 'Detartrage', duration: '15min' },
 { value: 'Devis', label: 'Devis', duration: '15min' },
 { value: 'Echographie', label: 'Echographie', duration: '25min' },
 { value: 'Endodontie', label: 'Endodontie', duration: '50min' },
 { value: 'Formation', label: 'Formation', duration: '60min' },
 { value: 'Implantologie', label: 'Implantologie', duration: '55min' },
 { value: 'Obturation Canalaire', label: 'Obturation Canalaire', duration: '15min' },
 { value: 'Orthodontie', label: 'Orthodontie', duration: '25min' },
 { value: 'PA ES Chassis/Monta', label: 'PA ES Chassis/Monta', duration: '15min' },
 { value: 'PA Pose', label: 'PA Pose', duration: '15min' },
 { value: 'PC ESS Armature', label: 'PC ESS Armature', duration: '30min' },
 { value: 'PC Scellement', label: 'PC Scellement', duration: '15min' },
 { value: 'PC TP EMP', label: 'PC TP EMP', duration: '30min' },
 { value: 'Prophylaxie', label: 'Prophylaxie', duration: '15min' },
{ value: 'Urgent', label: 'Urgent', duration: '15min' },
{ value: 'comp', label: 'comp', duration: '90min' },
{ value: 'polissage', label: 'polissage', duration: '15min' },

  { value: 'ALICE', label: 'ALICE', duration: '15min' },
  { value: 'Analyse', label: 'Analyse', duration: '15min' },
  { value: 'Consultation pesée', label: 'Consultation pesée', duration: '15min' },

];
interface TimeSlotProps {
  value: Date;
  step: number;
  isRender: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties; // Ajoutez cette ligne
  resource :number;
}
const localizer = momentLocalizer(moment);
moment.locale("fr");

const options = [
  'Tous',
  'Consultation',
  'Contrôle',
  'Chirurgie/Paro',
  'Composite',
  'Depose Sutures',
  '1er Consultation',
  'Devis',
  'Endodontie',
  'Formation',
  'Implantologie',
  'Orthodontie',
  'PA ES Chassis/Monta',
  'PA Pose',
  'PC TP EMP',
  'PC ESS Armature',
  'PC Scellement',
  'Prophylaxie',
  'Urgent',
  'Detartrage',
  'Obturation Canalaire',
  'polissage',
  'Changement D\'élastique',
  'Collage',
  'Contention',
  'Echographie',
 
];

const CetteJournee = ({
  onSubmit,
  onInsuredChange,
  fullName = 'Dr. Martin Dubois',
  staffOptions = [
    { label: 'Dr. Martin', value: 'dr-martin' },
    { label: 'Dr. Dubois', value: 'dr-dubois' },
    { label: 'Dr. Moreau', value: 'dr-moreau' }
  ],
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],
  
  }: PatientActionsProps & FichePatientProps) => {
const TimeSlotWrapper: React.FC = (props) => {
  // Forcer le cast car le calendrier ne fournit pas les types exacts
  const { value, children, resource } = props as TimeSlotProps;

  if (resource !== 1) {
    return <>{children}</>;
  }

  if (!isValidElement(children)) {
    return null;
  }

  return (
    <TimeSlot value={value} step={step} isRender={true}>
      {children}
    </TimeSlot>
  );
};

  const [isClient, setIsClient] = useState(false);
  const [loadingAppointments, setLoadingAppointments] = useState<boolean>(false);
  const [appointmentError, setAppointmentError] = useState<string | null>(null);
   const [isWaitingList, ] = useState(false);
 
 const [selected, setSelected] = useState<string[]>([]);

  const toggleSelection = (item: string) => {
    setSelected((current) =>
      current.includes(item)
        ? current.filter((val) => val !== item)
        : [...current, item]
    );
  }
  // Test API connectivity
  // const testApiConnection = async () => {
  //   try {
  //     console.log('🔗 Testing API connection...');
  //     const response = await fetch('/api/appointments/');
  //     console.log('API Response status:', response.status);
  //     const data = await response.json();
  //     console.log('API Response data:', data);

  //     if (response.ok) {
  //       alert('✅ API connection successful! Check console for details.');
  //     } else {
  //       alert(`❌ API error: ${response.status} - ${data.detail || 'Unknown error'}`);
  //     }
  //   } catch (error) {
  //     console.error('API test failed:', error);
  //     alert(`❌ API connection failed: ${error.message}`);
  //   }
  // };

  const fetchTodayAppointments = async () => {
    try {
      setLoadingAppointments(true);
      setAppointmentError(null);

      // Use the new dentistry service
      const dentistryAppointments = await dentistryService.getTodayAppointments();

      // Check if we got valid data
      if (!dentistryAppointments || !Array.isArray(dentistryAppointments)) {
        console.warn('Invalid dentistry appointments data:', dentistryAppointments);
        throw new Error('Invalid appointments data received from dentistry service');
      }

      // Convert dentistry appointments to legacy format for compatibility
      const processedAppointments = dentistryAppointments.map(appointment => {
        return dentistryService.convertToLegacyAppointment(appointment) as Appointment;
      });

      setAppointments(prevAppointments => {
        const existingIds = new Set(prevAppointments.map(a => a.id));
        const newAppointments = processedAppointments.filter(a => !existingIds.has(a.id));
        return [...prevAppointments, ...newAppointments];
      });

    } catch (error) {
      console.error('Error fetching today\'s appointments:', error);
      setAppointmentError('Failed to load appointments. Please try again.');

      // Fallback to old service if dentistry service fails
      try {
        const fallbackAppointments = await appointmentService.getTodayAppointments();
        const processedFallback = fallbackAppointments.map(appointment => {
          const patientId = appointment.patient?.id || '';
          const patientFirstName = appointment.patient?.first_name || '';
          const patientLastName = appointment.patient?.last_name || '';
          const resourceId = 'resourceId' in appointment && typeof appointment.resourceId === 'number'
            ? appointment.resourceId
            : 1;
          return {
                    ...appointment,
                    patientId,
                    resourceId,
                    name: patientFirstName,
                    prenom: patientLastName,
                    id: appointment.id,
                    title: `${patientFirstName} ${patientLastName}`,
                    first_name: patientFirstName,
                    last_name: patientLastName,
                    start: new Date(appointment.start || appointment.start_time || Date.now()),
                    end: new Date(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000),
                    isActive: false,
                    consultationDuration: moment(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000)
                      .diff(moment(appointment.start || appointment.start_time || Date.now()), 'minutes'),
                    type: appointment.type || appointment.appointment_type || 'visit',
                    eventType: appointment.type || appointment.appointment_type || 'visit',
                    gender: 'Homme',
                    telephone: appointment.telephone || '',
                    email: appointment.email || '',
                    address: appointment.address || '',
                    socialSecurity: appointment.socialSecurity || '',
                    cin: appointment.cin || '',
                    dateNaissance: appointment.dateNaissance || new Date(),
                    notes: appointment.notes || '',
                    doctor: appointment.doctor || '',
                    status: appointment.status || 'scheduled',
                    currentEventColor: getEventTypeColor((appointment.type || appointment.appointment_type || 'visit') as EventType),
                    birth_date: appointment.patient?.birth_date || '',
                    appointmentDate: moment(appointment.start || appointment.start_time || Date.now()).format('YYYY-MM-DD'),
                    appointmentTime: moment(appointment.start || appointment.start_time || Date.now()).format('HH:mm'),
                    appointmentEndTime: moment(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000).format('HH:mm'),
                    age: appointment.patient?.age || 0,
                    phone_numbers: appointment.patient?.phone_numbers || '',
                    duration: appointment.duration || '30',
                    agenda: appointment.agenda || '',
                    comment: appointment.comment || '',
                    etatCivil: appointment.patient?.etatCivil || '',
                    etatAganda: appointment.patient?.etatAganda || '',
                    patientTitle: appointment.patient?.title || '',
                    date: moment(appointment.start || appointment.start_time || Date.now()).format('YYYY-MM-DD'),
                    docteur: appointment.doctor || '',
                    event_Title: appointment.event_Title || '',
                    sociale: appointment.socialSecurity || '',
                    typeConsultation: appointment.appointment_type || '',
                    commentairelistedattente: appointment.comment || '',
                    visitorCount: 0,
                  } as Appointment;
                });
          setAppointments(prevAppointments => {
          const existingIds = new Set(prevAppointments.map(a => a.id));
          const newAppointments = processedFallback.filter(a => !existingIds.has(a.id));
          return [...prevAppointments, ...newAppointments];
        });
      } catch (fallbackError) {
        console.error('Fallback service also failed:', fallbackError);
      }
    } finally {
      setLoadingAppointments(false);
    }
  };

  useEffect(() => {
    setIsClient(true);

    // Fetch appointments when component mounts
    fetchTodayAppointments();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  const currentDate = new Date();
  // French month names
  const months = [
    "janvier",
    "février",
    "mars",
    "avril",
    "mai",
    "juin",
    "juillet",
    "août",
    "septembre",
    "octobre",
    "novembre",
    "décembre",
  ];
  // French day names
  const days = [
    "dimanche",
    "lundi",
    "mardi",
    "mercredi",
    "jeudi",
    "vendredi",
    "samedi",
  ];
  const messages = {
    today: "Aujourd’hui",
    previous: "Précédent",
    next: "Suivant",
    month: "Mois",
    week: "Semaine",
    day: "Jour",
    agenda: "Agenda",
    noEventsInRange: "Aucun événement prévu",
  };
const DragAndDropCalendar = withDragAndDrop<Appointment>(BigCalendar);
  const dayOfWeek = days[currentDate.getDay()]; // Get day of the week (0-6)
  const day = currentDate.getDate(); // Get the day of the month (1-31)
  const monthIndex = currentDate.getMonth(); // Get the month (0-11)
  const year = currentDate.getFullYear(); // Get the full year (e.g., 2024)
  // Formulate the date string in French format
  const formattedDate = `${dayOfWeek} ${day} ${months[monthIndex]} ${year}`;
// Add this state near your other useState declarations
const [, setTotalVisitors] = useState(0);
     const [, setEvents] = useState<Event[]>([]);
     const [, setPatientEvents] = useState<Appointment[]>([]);
      const [, setShowAddModal] = useState(false);
      const [showViewModal, setShowViewModal] = useState(false);
      const [showEditModal, setShowEditModal] = useState(false);
      const [selectedEvent, setSelectedEvent] = useState<AppointmentEvent | null>(null);
      const [selectedSlot, setSelectedSlot] = useState<{start: Date, end: Date} | null>(null);
     // Patient form fields with localStorage persistence
      const [patientName, setPatientName] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentPatientName') || '';
        }
        return '';
      });
      const [patientlastName, setPatientlastName] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentPatientLastName') || '';
        }
        return '';
      });
      const [patientnom, ] = useState('');
      const [patientprenom, ] = useState('');
      const [email, setEmail] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentEmail') || '';
        }
        return '';
      });
      const [address, setAddress] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentAddress') || '';
        }
        return '';
      });
      const [patientsocialSecurity, setSocialSecurity] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentSocialSecurity') || '';
        }
        return '';
      });
      const [patientduration, ] = useState('');
      const [patientagenda, ] = useState('');
      const [patientcomment, setPatientcomment ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentPatientComment') || '';
        }
        return '';
      });
      const [eventAganda, setEventAganda ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentEventAgenda') || '';
        }
        return '';
      });
      const [patientdoctor, setPatientDocteur] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentPatientDoctor') || '';
        }
        return '';
      });
      const [eventTitle, setEventTitle ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentEventTitle') || '';
        }
        return '';
      });
      const [patientnotes, setPatientNotes ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentPatientNotes') || '';
        }
        return '';
      });
      const [patientsociale,  ] = useState('');
      const [patienttypeConsultation, setPatientTypeConsultation ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentTypeConsultation') || '';
        }
        return '';
      });
      const [patientcommentairelistedattente, setPatientCommentairelistedattente ] = useState(() => {
        if (typeof window !== 'undefined') {
          return localStorage.getItem('appointmentCommentaireListedAttente') || '';
        }
        return '';
      });
        // Initialize date states with localStorage persistence
        const [eventDate, setEventDate] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentEventDate') || moment().format('YYYY-MM-DD');
          }
          return moment().format('YYYY-MM-DD');
        });
        const [eventTime, setEventTime] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentEventTime') || moment().format('HH:mm');
          }
          return moment().format('HH:mm');
        });
        const [eventConsultation, setEventConsultation] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentEventConsultation') || '15 min';
          }
          return '15 min';
        });
        const [dureeDeLexamen, setDureeDeLexamen] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentDureeDeLexamen') || '15 min';
          }
          return '15 min';
        });
        const [eventDateDeNaissance, setEventDateDeNaissance] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentEventDateDeNaissance') || '';
          }
          return '';
        });
        const [eventAge, setEventAge] = useState<number | null>(() => {
          if (typeof window !== 'undefined') {
            const saved = localStorage.getItem('appointmentEventAge');
            return saved ? parseInt(saved) : null;
          }
          return null;
        });
        const [genderOption, setGenderOption] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentGenderOption') || 'Homme';
          }
          return 'Homme';
        });
        const [eventCin, setEventCin] = useState(() => {
          if (typeof window !== 'undefined') {
            return localStorage.getItem('appointmentEventCin') || '';
          }
          return '';
        });
    const [patients, setPatients] = useState<Patient[]>([]);
    const [waitingList, setWaitingList] = useState<Patient[]>([]);
    const [appointments, setAppointments] = useState<Appointment[]>([]);
    const [activeVisits, setActiveVisits] = useState<Appointment[]>([]);
    const [searchValue, setSearchValue] = useState('');

    // State for real backend data
    const [realStaffOptions, setRealStaffOptions] = useState(staffOptions);
    const [allPatients, setAllPatients] = useState<BackendPatientData[]>([]);
    const [isLoadingStaff, setIsLoadingStaff] = useState(false);
    const [isLoadingPatients, setIsLoadingPatients] = useState(false);
    const [, setPatientModalOpen] = useState(false);
    const [infoModalOpen, setInfoModalOpen] = useState(false);
    const [currentPatient, setCurrentPatient] = useState<Patient | null>(null);
   const [eventResourceId, setEventResourceId] = useState<number>(1); // Default to Room A
     const [StartofworkOpened, { open: openedStartofwork, close: closeStartofwork }] = useDisclosure(false)
     const [ColorPickeropened, { open:openedColorPicker, close:closeColorPicker }] = useDisclosure(false);
     const [changeEndValue, setChangeEndValue] = useState('#FFFFFF');
     const [rescheduleModalOpen, setRescheduleModalOpen] = useState(false);
     const [appointmentToReschedule, setAppointmentToReschedule] = useState<Appointment | null>(null);
const EVENT_TYPE_COLORS = {
  'visit': "#34D1BF",
  'visitor-counter': "#F17105",
  'completed': "#3799CE",
  'diagnosis': "#F3124E",
  'default': "var(--mantine-color-dark-0)",
  'ALICE': "red",
  'Analyse': "blue",
  'Consultation': "#FF5252", // Added red color
  'Consultation pesée': "#FF9E80", // Added light orange
  'Contrôle': "orange",
  'Débaguage': "purple",
  'ELECRTO': "green",
  'Echographie': "green",
  'LIPO': "green",
  'Motif 1': "green",
  'Motif 2': "green",
  'PRESSO': "green",
  'Regime': "green",
  'SOIN DE VISAGE': "green",
  'microneedling': "green",
  'Urgence': "#FF9800", // Added orange
  'Avis': "#9C27B0", // Added purple
  'Autre': "#4CAF50", // Added green
  'Re-diagnose': "#E91E63" // Added pink
};
 const [eventType, setEventType] = useState<EventType>('visit');
 const [viewPatient, setViewPatient] = useState<Patient | null>(null);
 const getEventTypeColor = (type: EventType | undefined) => {
   return type && EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] ? EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] : EVENT_TYPE_COLORS.default;
 };

 // Wrapper function pour setEventType
 const handleSetEventType = (type: EventType) => {
   setEventType(type);
 };

const [titleOptions, setTitleOptions] = useState<TitleOption[]>([
  { value: "Mr", label: "Mr" },
  { value: "Mlle", label: "Mlle" },
  { value: "Mme", label: "Mme" },
  { value: "Dr.", label: "Dr." },
  { value: "Pr.", label: "Pr." },
  { value: "Garçon", label: "Garçon" },
  { value: "Fille", label: "Fille" },
  { value: "Bébé", label: "Bébé" },
]);
const [newTitle, setNewTitle] = useState<string>("");
const [agendaTypes, setAgendaTypes] = useState<AgendaType[]>([
  { value: "Cabinet", label: "Cabinet",  },
  { value: "Center", label: "Center",  },
  { value: "Kaders", label: "Kaders",  },
]);
const [newAgendaType, setNewAgendaType] = useState<string>("");
const [consultationTypes, setConsultationTypes] = useState<ConsultationType[]>(initialConsultationTypes);
const [newConsultationType, setNewConsultationType] = useState("");
const [newConsultationColor, setNewConsultationColor] = useState("#3799CE");
 const [Fichepatientpened, { open:openedFichepatient, close:closeFichepatient }] = useDisclosure(false);
useEffect(() => {
  if (!patientsocialSecurity || patientsocialSecurity.trim() === '') {
    setSocialSecurity('Aucune');
  }
}, [patientsocialSecurity]);



// Function to fetch real staff/doctors from backend
const fetchStaffFromBackend = async () => {
  try {
    console.log('👨‍⚕️ Fetching staff from backend...');
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    const token = localStorage.getItem('token');

    const response = await fetch(`${API_URL}/api/auth/users/?user_type=doctor`, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      const staffList = Array.isArray(data) ? data : data.results || [];

      const formattedStaff = staffList.map((staff: BackendStaffData) => ({
        label: `Dr. ${staff.first_name} ${staff.last_name}`,
        value: `dr-${staff.first_name.toLowerCase()}-${staff.last_name.toLowerCase()}`
      }));

      console.log('✅ Staff fetched from backend:', formattedStaff);
      return formattedStaff;
    } else {
      console.warn('⚠️ Failed to fetch staff from backend, using defaults');
      return [
        { label: 'Dr. Martin', value: 'dr-martin' },
        { label: 'Dr. Dubois', value: 'dr-dubois' },
        { label: 'Dr. Moreau', value: 'dr-moreau' }
      ];
    }
  } catch (error) {
    console.error('❌ Error fetching staff:', error);
    return [
      { label: 'Dr. Martin', value: 'dr-martin' },
      { label: 'Dr. Dubois', value: 'dr-dubois' },
      { label: 'Dr. Moreau', value: 'dr-moreau' }
    ];
  }
};

// Function to fetch all patients from backend for selection
const fetchAllPatientsFromBackend = async () => {
  try {
    console.log('👥 Fetching all patients from backend...');
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    const token = localStorage.getItem('token');

    const response = await fetch(`${API_URL}/api/auth/users/?user_type=patient`, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      const patientsList = Array.isArray(data) ? data : data.results || [];

      console.log('✅ Patients fetched from backend:', patientsList.length, 'patients');
      return patientsList;
    } else {
      console.warn('⚠️ Failed to fetch patients from backend');
      return [];
    }
  } catch (error) {
    console.error('❌ Error fetching patients:', error);
    return [];
  }
};

// Function to fetch patient details by ID
const fetchPatientDetails = async (patientId: string) => {
  try {
    console.log(`👤 Fetching patient details for ID: ${patientId}`);

    // Get authentication token
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('⚠️ No authentication token available for patient fetch');
      return null;
    }

    // Try multiple endpoints to find patient data
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    const endpoints = [
      `${API_URL}/api/patients/?id=${patientId}`, // Patients list with filter
      `${API_URL}/api/auth/patients/?id=${patientId}`, // Auth patients with filter
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        console.log(`📡 Response status: ${response.status} for ${endpoint}`);

        if (response.ok) {
          const data = await response.json();
          console.log(`📦 Response data:`, data);

          // Handle both single object and array responses
          const patientData = Array.isArray(data) ? data[0] :
                             data.results ? data.results[0] : data;

          if (patientData) {
            console.log('✅ Patient details fetched:', patientData);
            return patientData;
          }
        } else {
          console.warn(`⚠️ Endpoint ${endpoint} returned ${response.status}: ${response.statusText}`);
        }
      } catch (endpointError) {
        console.warn(`⚠️ Endpoint ${endpoint} failed:`, endpointError);
        continue;
      }
    }

    console.warn(`⚠️ Failed to fetch patient ${patientId} from all endpoints`);
    return null;
  } catch (error) {
    console.error(`❌ Error fetching patient ${patientId}:`, error);
    return null;
  }
};

// Function to load appointments from backend with full patient data
const loadAppointmentsFromBackend = useCallback(async () => {
  try {
    console.log('📥 Loading appointments from backend...');
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    const response = await fetch(`${API_URL}/api/appointments/`);

    if (response.status === 404) {
      console.warn('⚠️ Backend server not running. Using localStorage data.');
      // Try to load from localStorage as fallback
      const localAppointments = localStorage.getItem('appointments');
      if (localAppointments) {
        const parsedAppointments = JSON.parse(localAppointments);
        // Convert date strings back to Date objects
        const appointmentsWithDates = parsedAppointments.map((apt: {
          start: string | Date;
          end: string | Date;
          [key: string]: unknown;
        }) => ({
          ...apt,
          start: new Date(apt.start),
          end: new Date(apt.end),
        }));
        setAppointments(appointmentsWithDates);
        return appointmentsWithDates;
      }
      return [];
    }

    if (response.ok) {
      const backendAppointments = await response.json();
      console.log('✅ Loaded appointments from backend:', backendAppointments);

      // Handle both paginated and non-paginated responses
      const appointmentsList = backendAppointments.results || backendAppointments;

      if (Array.isArray(appointmentsList) && appointmentsList.length > 0) {
        console.log('🔄 Enriching appointments with patient data...');

        // Fetch patient details for each appointment
        const enrichedAppointments = await Promise.all(
          appointmentsList.map(async (apt: {
            id: string;
            title: string;
            appointment_date: string;
            appointment_time: string;
            end_time?: string;
            patient: string;
            patient_name?: string;
            duration_minutes?: number;
            notes?: string;
            description?: string;
            appointment_type?: string;
            resource_id?: string;
            room?: string;
          }) => {
            // Fetch full patient details from API
            let patientDetails = await fetchPatientDetails(apt.patient);

            // If API fetch failed, try to find patient in local state
            if (!patientDetails) {
              console.log(`🔍 API fetch failed, looking for patient ${apt.patient} in local state...`);
              patientDetails = patients.find(p => p.id === apt.patient);
              if (patientDetails) {
                console.log('✅ Found patient in local state:', patientDetails);
              } else {
                console.warn(`⚠️ Patient ${apt.patient} not found in local state either`);
              }
            }

            // Create proper start and end dates
            const startDate = new Date(`${apt.appointment_date}T${apt.appointment_time}`);
            const endDate = apt.end_time
              ? new Date(`${apt.appointment_date}T${apt.end_time}`)
              : new Date(startDate.getTime() + (apt.duration_minutes || 30) * 60000);

            return {
              id: apt.id,
              title: apt.title,
              start: startDate,
              end: endDate,
              patientId: apt.patient,
              isActive: true,
              resourceId: apt.resource_id ? parseInt(apt.resource_id) : 1, // Map resource_id from backend, default to Room A
              // Map appointment fields
              appointmentDate: apt.appointment_date,
              appointmentTime: apt.appointment_time,
              appointmentEndTime: apt.end_time || apt.appointment_time,
              consultationDuration: apt.duration_minutes || 30,
              notes: apt.notes || '',
              comment: apt.description || '',
              duration: apt.duration_minutes?.toString() || '30',
              date: apt.appointment_date,
              event_Title: apt.title,
              commentairelistedattente: '',
              // Use real patient data if available, otherwise fallback to appointment data
              // Handle both API format (snake_case) and local state format (camelCase)
              name: patientDetails?.first_name || apt.patient_name?.split(' ')[0] || '',
              prenom: patientDetails?.last_name || apt.patient_name?.split(' ')[1] || '',
              first_name: patientDetails?.first_name || apt.patient_name?.split(' ')[0] || '',
              last_name: patientDetails?.last_name || apt.patient_name?.split(' ')[1] || '',
              birth_date: patientDetails?.date_of_birth || patientDetails?.birth_date || '',
              cin: patientDetails?.cin || '',
              email: patientDetails?.email || '',
              age: patientDetails?.age || 0,
              phone_numbers: patientDetails?.phone_number || patientDetails?.phone_numbers || '',
              socialSecurity: patientDetails?.social_security || patientDetails?.socialSecurity || '',
              agenda: patientDetails?.agenda || '',
              address: patientDetails?.address || '',
              etatCivil: patientDetails?.etat_civil || patientDetails?.etatCivil || '',
              etatAganda: patientDetails?.etat_aganda || patientDetails?.etatAganda || '',
              patientTitle: patientDetails?.title || patientDetails?.patientTitle || '',
              docteur: patientDetails?.doctor_assigned || patientDetails?.docteur || '',
              gender: patientDetails?.gender || '',
              sociale: patientDetails?.social_security || patientDetails?.socialSecurity || '',
              typeConsultation: patientDetails?.typeConsultation || apt.appointment_type || '',
              // Add missing required fields for Appointment interface
              type: 'visit' as const,
              eventType: 'visit' as const,
              visitorCount: 0,
            };
          })
        );

        console.log('✅ Enriched appointments with patient data:', enrichedAppointments);
        setAppointments(enrichedAppointments);

        // Also save to localStorage as backup
        localStorage.setItem('appointments', JSON.stringify(enrichedAppointments));

        return enrichedAppointments;
      } else {
        console.log('📝 No appointments found in backend');
        // Try to load from localStorage as fallback
        const localAppointments = localStorage.getItem('appointments');
        if (localAppointments) {
          const parsedAppointments = JSON.parse(localAppointments);
          setAppointments(parsedAppointments);
          return parsedAppointments;
        }
      }
    } else {
      console.warn('⚠️ Failed to load appointments from backend:', response.status);
      // Try to load from localStorage as fallback
      const localAppointments = localStorage.getItem('appointments');
      if (localAppointments) {
        const parsedAppointments = JSON.parse(localAppointments);
        setAppointments(parsedAppointments);
        return parsedAppointments;
      }
    }
  } catch (error) {
    console.error('❌ Error loading appointments:', error);
    // Try to load from localStorage as fallback
    const localAppointments = localStorage.getItem('appointments');
    if (localAppointments) {
      const parsedAppointments = JSON.parse(localAppointments);
      setAppointments(parsedAppointments);
      return parsedAppointments;
    }
  }
  return [];
}, [setAppointments, patients]); // Dependencies for useCallback

// Load appointments from backend on component mount
useEffect(() => {
  loadAppointmentsFromBackend();
}, [loadAppointmentsFromBackend]); // Include dependency

// Load real staff and patients data on component mount
useEffect(() => {
  const loadRealData = async () => {
    // Load staff data
    setIsLoadingStaff(true);
    try {
      const staff = await fetchStaffFromBackend();
      setRealStaffOptions(staff);
    } catch (error) {
      console.error('Failed to load staff:', error);
    } finally {
      setIsLoadingStaff(false);
    }

    // Load all patients data
    setIsLoadingPatients(true);
    try {
      const patientsData = await fetchAllPatientsFromBackend();
      setAllPatients(patientsData);

      // Also update the patients state for existing functionality
      const formattedPatients = patientsData.map((patient: BackendPatientData) => ({
        id: patient.id,
        title: patient.title || '',
        name: patient.first_name || '',
        prenom: patient.last_name || '',
        first_name: patient.first_name || '',
        last_name: patient.last_name || '',
        birth_date: patient.date_of_birth || patient.birth_date || '',
        appointmentDate: '',
        appointmentTime: '',
        appointmentEndTime: '',
        consultationDuration: 30,
        cin: patient.cin || '',
        email: patient.email || '',
        age: patient.age || 0,
        phone_numbers: patient.phone_number || patient.phone_numbers || '',
        socialSecurity: patient.social_security || patient.socialSecurity || '',
        agenda: patient.agenda || '',
        address: patient.address || '',
        etatCivil: patient.etat_civil || patient.etatCivil || '',
        etatAganda: patient.etat_aganda || patient.etatAgenda || '',
        patientTitle: patient.title || '',
        docteur: patient.doctor_assigned || patient.docteur || '',
        gender: patient.gender || 'Homme',
        sociale: patient.social_security || patient.socialSecurity || '',
        typeConsultation: '',
        notes: patient.notes || '',
        comment: patient.comment || '',
        commentairelistedattente: '',
        duration: '30',
        date: '',
        event_Title: `${patient.first_name || ''} ${patient.last_name || ''}`.trim(),
        resourceId: 1,
        visitorCount: 0,
        lunchTime: false,
        checkedListedattente: false,
        type: 'visit' as EventType,
        eventType: 'visit' as EventType
      }));

      setPatients(formattedPatients);
      console.log('✅ Loaded', patientsData.length, 'real patients from backend');
    } catch (error) {
      console.error('Failed to load patients:', error);
    } finally {
      setIsLoadingPatients(false);
    }
  };

  loadRealData();
}, []); // Run once on mount

// Function to populate form with selected patient data
const populateFormWithPatient = useCallback((patient: BackendPatientData) => {
  console.log('📝 Populating form with patient data:', patient);

  // Set patient basic info
  setPatientName(patient.first_name || '');
  setPatientlastName(patient.last_name || '');
  setEmail(patient.email || '');
  setEventTelephone(patient.phone_number || patient.phone_numbers || '');
  setAddress(patient.address || '');
  setEventCin(patient.cin || '');
  setSocialSecurity(patient.social_security || patient.socialSecurity || '');
  setEventAge(patient.age || 0);
  setEventDateDeNaissance(patient.date_of_birth || patient.birth_date || '');
  setGenderOption(patient.gender || 'Homme');
  setEventEtatCivil(patient.etat_civil || patient.etatCivil || '');
  setPatientDocteur(patient.doctor_assigned || patient.docteur || '');
  setPatientNotes(patient.notes || '');
  setPatientcomment(patient.comment || '');

  // Show success notification
  notifications.show({
    title: 'Patient sélectionné',
    message: `${patient.first_name} ${patient.last_name} a été sélectionné`,
    color: 'green',
    autoClose: 2000,
  });
}, []);



// Persist appointment form data to localStorage
useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventDate', eventDate);
  }
}, [eventDate]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventTime', eventTime);
  }
}, [eventTime]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventConsultation', eventConsultation);
  }
}, [eventConsultation]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentDureeDeLexamen', dureeDeLexamen);
  }
}, [dureeDeLexamen]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventDateDeNaissance', eventDateDeNaissance);
  }
}, [eventDateDeNaissance]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventAge', eventAge?.toString() || '');
  }
}, [eventAge]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentGenderOption', genderOption);
  }
}, [genderOption]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventCin', eventCin);
  }
}, [eventCin]);

// Persist patient form data
useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentPatientName', patientName);
  }
}, [patientName]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentPatientLastName', patientlastName);
  }
}, [patientlastName]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEmail', email);
  }
}, [email]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentAddress', address);
  }
}, [address]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentSocialSecurity', patientsocialSecurity);
  }
}, [patientsocialSecurity]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentPatientComment', patientcomment);
  }
}, [patientcomment]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventAgenda', eventAganda);
  }
}, [eventAganda]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentPatientDoctor', patientdoctor);
  }
}, [patientdoctor]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentEventTitle', eventTitle);
  }
}, [eventTitle]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentPatientNotes', patientnotes);
  }
}, [patientnotes]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentTypeConsultation', patienttypeConsultation);
  }
}, [patienttypeConsultation]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('appointmentCommentaireListedAttente', patientcommentairelistedattente);
  }
}, [patientcommentairelistedattente]);

const [checkedAppelvideo, setCheckedAppelvideo] = useState<boolean>(false);
const [checkedRappelSms, setCheckedRappelSms] = useState<boolean>(false);
const [checkedRappelEmail, setCheckedRappelEmail] = useState<boolean>(false);
 const handleAppelvideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Appel video to:", newValue);
    setCheckedAppelvideo(newValue);
  };
  const handleRappelSmsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Rappel SMS to:", newValue);
    setCheckedRappelSms(newValue);
  };
  const handleRappelEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Rappel Email to:", newValue);
    setCheckedRappelEmail(newValue);
  };
// To add an event ID to the Set
const [patientTitle,  ] = useState('');
const [type, setType] = useState("");
// EventType is now imported from '@/types/typesCalendarPatient'
const colorMap: Record<EventType, string> = {
  visit: "#34D1BF",
  "visitor-counter": "#F17105",
  completed: "#3799CE",
  diagnosis: "#ED0423",
  Consultation: "#FF5252",
  Contrôle: "#FF9800",
  Urgence: "#F44336",
  "Re-diagnose": "#E91E63",
  Autre: "#4CAF50"
};
const color = colorMap[eventType];
  const [date, setDate] = useState(new Date());
  const handleNavigate = (action: "TODAY" | "PREV" | "NEXT" | "DATE", newDate?: Date) => {
    if (action === "TODAY") {
      setDate(new Date());
    } else if (action === "PREV") {
      setDate(moment(date).subtract(1, "day").toDate());
    } else if (action === "NEXT") {
      setDate(moment(date).add(1, "day").toDate());
    } else if (action === "DATE" && newDate) {
      setDate(newDate);
    }
  };
   const [minHour, setMinHour] = useState<number>(8); // Default to 8 AM
   const [minMinute, setMinMinute] = useState<number>(0); // Default to 0 minutes
   const [maxHour, setMaxHour] = useState<number>(18); // Default to 6 PM
   const [maxMinute, ] = useState<number>(0); // Default to 0 minutes
   const [step, setStep] = useState<number>(15); // Default
   const [minTime, setMinTime] = useState(() => {
     const date = new Date();
     date.setHours(8, 0, 0); // Default to 8:00 AM
     return date;
   });
   const [maxTime, setMaxTime] = useState(() => {
     const date = new Date();
     date.setHours(18, 0, 0); // Default to 6:00 PM
     return date;
   });
   const handleSave = () => {
     const newMinTime = moment()
       .startOf("day")
       .set({ hour: minHour, minute: minMinute })
       .local()
       .toDate();
     const newMaxTime = moment()
       .startOf("day")
       .set({ hour: maxHour, minute: maxMinute })
       .local()
       .toDate();
     setMinTime(newMinTime);
     setMaxTime(newMaxTime);
     console.log(newMaxTime);
   };
  
   const [rendezVousOpened, { open: openRendezVous, close: closeRendezVous }] =useDisclosure(false);
  const [ListDesPatientOpened, { open: openListDesPatient, close: closeListDesPatient }] = useDisclosure(false);
  const [AddwaitingListOpened, { open: AddwaitingList, close: closeAddwaitingList }] =useDisclosure(false);
  const [EditwaitingListOpened, { open: EditwaitingList, close: closeEditwaitingList }] =useDisclosure(false);
  const [ListRendezVousOpened, { open: openListRendezVous, close: closeListRendezVous }] = useDisclosure(false);
  const formatTime = (date: Date) => {
    if (!isClient) return ''; // Return empty string during SSR
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };
  const icon = <IconInfoCircle />;
  // Create a counter that resets every day
  const [dailyCounter, setDailyCounter] = useState(0);
   const getCurrentDate = (): string => {
    const today = new Date();
    return today.toISOString().split("T")[0]; // Returns "YYYY-MM-DD"
  };
  useEffect(() => {
    const savedDate = localStorage.getItem("counterDate") ?? ""; // This will always be a string
    const currentDate = getCurrentDate(); // This will always return a string
    if (savedDate !== currentDate) {
      localStorage.setItem("counterDate", currentDate); // currentDate is guaranteed to be a string
      setDailyCounter(0); // Reset counter for the new day
    }
  }, []);
  const [activeIds, setActiveIds] = useState<number[]>([]); // Use an array to track multiple active IDs
  // First, add a helper function to check the visit status
const [opened, { open, close }] = useDisclosure(false)
type Resource = {
  id: number;
  title: string;
};
const resources: Resource[] = [
  { id: 1, title: 'Room A' },
  { id: 2, title: 'Room B' },
];


const [thirdModalOpened, { close: closeThirdModal }] = useDisclosure(false);

const [ fourthModalOpened, {  close: closeFourthModal },] = useDisclosure(false);
  const [, setActiveEventIds] = useState<Set<number>>(new Set());
  const [activeEvent_Ids, setActiveEvent_Ids] = useState<Set<number>>(new Set());
  const [, setEventStartTimes] = useState<Map<number, Date>>(new Map());
  const [eventDurations, setEventDurations] = useState<Map<number, number>>(new Map());
  const [currentTimes, setCurrentTimes] = useState<Map<number, string>>(new Map());
  const [waitingRoomVisits, setWaitingRoomVisits] = useState<Appointment[]>([]);

  const toggleEvent = (eventId: number) => {
    setActiveEventIds(prevIds => {
      const newIds = new Set(prevIds);
      if (newIds.has(eventId)) {
        newIds.delete(eventId);
      } else {
        newIds.add(eventId);
      }
      return newIds;
    });
  };
// To add an event ID to the Set
const addEventId = useCallback((eventId: number) => {
  setActiveEvent_Ids(prevIds => {
    const newIds = new Set(prevIds);
    newIds.add(eventId);
    return newIds;
  });
}, []);

// To remove an event ID from the Set
const removeEventId = useCallback((eventId: number) => {
  setActiveEvent_Ids(prevIds => {
    const newIds = new Set(prevIds);
    newIds.delete(eventId);
    return newIds;
  });
}, []);

// Function to update patient record with intervention duration
const updatePatientWithDuration = useCallback((eventId: number, duration: number, startTime: Date, endTime: Date) => {
  // Update appointments
  setAppointments(prev => prev.map(appointment => {
    if (Number(appointment.id) === eventId) {
      return {
        ...appointment,
        interventionDuration: duration,
        interventionStartTime: startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
        interventionEndTime: endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
        notes: `${appointment.notes || ''}\nIntervention: ${duration}min (${startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })})`
      };
    }
    return appointment;
  }));

  // Update patients
  setPatients(prev => prev.map(patient => {
    if (Number(patient.id) === eventId) {
      return {
        ...patient,
        notes: `${patient.notes || ''}\nIntervention: ${duration}min (${startTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })})`
      };
    }
    return patient;
  }));
}, []);

// Time tracking functions
const handleTimeTracking = useCallback((eventId: number, isChecked: boolean) => {
  const currentTime = new Date();

  if (isChecked) {
    // Starting the timer - record start time
    setEventStartTimes(prev => new Map(prev.set(eventId, currentTime)));
    addEventId(eventId);

    notifications.show({
      title: 'Intervention Started',
      message: `Started at ${currentTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`,
      color: 'green',
      autoClose: 2000,
    });
  } else {
    // Stopping the timer - calculate duration
    setEventStartTimes(prev => {
      const startTime = prev.get(eventId);
      if (startTime) {
        const duration = Math.round((currentTime.getTime() - startTime.getTime()) / (1000 * 60)); // Duration in minutes
        setEventDurations(prevDurations => new Map(prevDurations.set(eventId, duration)));

        // Update the appointment/patient record with the duration
        updatePatientWithDuration(eventId, duration, startTime, currentTime);

        // Clean up current time
        setCurrentTimes(prevTimes => {
          const newMap = new Map(prevTimes);
          newMap.delete(eventId);
          return newMap;
        });

        notifications.show({
          title: 'Intervention Completed',
          message: `Duration: ${duration} minutes`,
          color: 'blue',
          autoClose: 3000,
        });
      }

      // Clean up start time
      const newMap = new Map(prev);
      newMap.delete(eventId);
      return newMap;
    });

    removeEventId(eventId);
  }
}, [addEventId, removeEventId, updatePatientWithDuration]);

// Update current time every second for active events
useEffect(() => {
  if (activeEvent_Ids.size === 0) return; // Don't run interval if no active events

  const interval = setInterval(() => {
    setCurrentTimes(prev => {
      const newMap = new Map(prev);
      let hasChanges = false;

      activeEvent_Ids.forEach(eventId => {
        const now = new Date();
        const newTime = now.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        const oldTime = prev.get(eventId);

        // Only update if time actually changed (to prevent unnecessary re-renders)
        if (oldTime !== newTime) {
          newMap.set(eventId, newTime);
          hasChanges = true;
        }
      });

      // Only return new map if there were actual changes
      return hasChanges ? newMap : prev;
    });
  }, 1000);

  return () => clearInterval(interval);
}, [activeEvent_Ids]);



// Function to move appointment to waiting room
const moveToWaitingRoom = useCallback((appointment: Appointment) => {
  // Remove from active visits
  setActiveVisits(prev => prev.filter(visit => visit.id !== appointment.id));
  // Add to waiting room
  setWaitingRoomVisits(prev => [...prev, appointment]);
  // Remove from active event IDs
  removeEventId(Number(appointment.id));

  notifications.show({
    title: 'Moved to Waiting Room',
    message: `${appointment.first_name} ${appointment.last_name} moved to waiting room`,
    color: 'blue',
    autoClose: 2000,
  });
}, [removeEventId]);

// Function to move appointment back to active visits
const moveToActiveVisits = useCallback((appointment: Appointment) => {
  // Remove from waiting room
  setWaitingRoomVisits(prev => prev.filter(visit => visit.id !== appointment.id));
  // Add to active visits
  setActiveVisits(prev => [...prev, appointment]);

  notifications.show({
    title: 'Moved to Active Visits',
    message: `${appointment.first_name} ${appointment.last_name} moved to active visits`,
    color: 'green',
    autoClose: 2000,
  });
}, []);
     const patientForm = useForm({
       initialValues: {
         title: '',
         first_name: '',
         last_name: '',
         age: 0,
         phone_numbers: '',
         etatAganda: '',
         docteur: '',
         event_Title: "",
         gender:  "",
         socialSecurity: '',
         duration: '15 min',
         agenda: '',
         comment: '',
         address: '',
         type: eventType,
         resourceId:Number(eventResourceId),
      name:  '',
      prenom: '',
      email: '',
      typeConsultation:'',
      date :eventDate ?? Date,
      commentairelistedattente:'',
      etatCivil:  '', // Add this line
      patientTitle:'',
      birth_date: eventDateDeNaissance,
      consultationDuration: parseInt(eventConsultation),
    notes: patientnotes,
    cin: genderOption !== "Enfant" ? eventCin : undefined,
    appointmentDate: '',
    appointmentTime: '',
    appointmentEndTime: '',
  eventType:'',
  visitorCount: eventType === "visitor-counter" ? 1 : 0,
       },
     });

     const appointmentForm = useForm({
       initialValues: {
         patientId: '',
         notes: '',
         date: new Date(),
         duration: 15, // 30minutes
         type: "visit",
         resourceId:Number(eventResourceId),
         addToWaitingList: false,
         removeFromCalendar: false,
         rescheduleDateTime: '', // Add this field
       },
     });
   // Generate unique UUID (compatible with Django UUID field)
   const generateId = (): string => {
     // Generate a UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
     return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
       const r = Math.random() * 16 | 0;
       const v = c === 'x' ? r : (r & 0x3 | 0x8);
       return v.toString(16);
     });
   };
 const handleDragEnd = async (result: DropResult) => {
  if (!result.destination) return;
  const { source, destination } = result;
  if (destination.droppableId === source.droppableId && destination.index === source.index) {
    return;
  }

  // Handle reordering within activeVisits
  if (source.droppableId === 'dnd-list' && destination.droppableId === 'dnd-list') {
    const reorderedItems = Array.from(activeVisits);
    const [movedItem] = reorderedItems.splice(source.index, 1);
    reorderedItems.splice(destination.index, 0, movedItem);
    // Update the state with the new order
    setActiveVisits(reorderedItems);
    return;
  }

  // Handle waiting list to calendar transfer
  if (source.droppableId === 'waitingList' && destination.droppableId === 'calendar') {
    const patient = waitingList[source.index];

    // Create new appointment from patient data
    const newAppointment: Appointment = {
      id: generateId(),
      title: `${patient.first_name} ${patient.last_name}`,
      start: selectedSlot ? selectedSlot.start : new Date(),
      end: selectedSlot ?
        new Date(selectedSlot.start.getTime() + (patient.consultationDuration || 30) * 60000) :
        new Date(new Date().getTime() + 30 * 60000),
      patientId: patient.id,
      isActive: false,
      resourceId: Number(eventResourceId), // Default to Room A
      first_name: patient.first_name,
      last_name: patient.last_name,
      birth_date: patient.birth_date,
      age: patient.age,
      agenda: patient.agenda,
      etatCivil: patient.etatCivil,
      etatAganda: patient.etatAganda,
      cin: patient.cin,
      address: patient.address,
      phone_numbers: patient.phone_numbers,
      email: patient.email,
      docteur: patient.docteur || "",
      event_Title: patient.event_Title || "",
      gender:  patient.gender || "Homme",
      typeConsultation: patient.typeConsultation || "",
      notes: patient.notes || "",
      commentairelistedattente: patient.comment || "",
      sociale: patient.socialSecurity || "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      socialSecurity: patient.socialSecurity || "",
      duration: patient.consultationDuration?.toString() || "30",
      comment: patient.comment || "",
      patientTitle: patient.patientTitle || "",
      date: new Date().toISOString(),
      consultationDuration: patient.consultationDuration || 30,
      type: "visit",
      lunchTime: false,
      eventType:patient.eventType || "",
      currentEventColor: getEventTypeColor("visit"),
      visitorCount: eventType === "visitor-counter" ? 1 : 0,
      name: patient.name,
     prenom: patient.prenom,
    };

    // Save to backend first
    try {
      setLoadingAppointments(true);
      setAppointmentError(null);

      // Map frontend event types to backend appointment types
      const mapAppointmentType = (frontendType: string): string => {
        const typeMapping: { [key: string]: string } = {
          'consultation': 'consultation',
          'follow-up': 'follow_up',
          'emergency': 'emergency',
          'checkup': 'routine_checkup',
          'routine_checkup': 'routine_checkup',
          'procedure': 'procedure',
          'surgery': 'surgery',
          'cleaning': 'cleaning',
          'other': 'other',
        };
        return typeMapping[frontendType] || 'consultation';
      };

      // Filter out placeholder data before creating appointment
      const cleanFirstName = patient.first_name === 'User' ? '' : patient.first_name || '';
      const cleanLastName = patient.last_name === 'Test' ? '' : patient.last_name || '';

      // Validate cleaned data
      if (!cleanFirstName || !cleanLastName) {
        console.warn('⚠️ Appointment creation blocked: Patient has placeholder data', {
          original: { first_name: patient.first_name, last_name: patient.last_name },
          cleaned: { first_name: cleanFirstName, last_name: cleanLastName }
        });
        notifications.show({
          title: 'Invalid Patient Data',
          message: 'Cannot create appointment with placeholder patient data. Please select a real patient.',
          color: 'red',
          autoClose: 5000,
        });
        setLoadingAppointments(false);
        return;
      }

      // Create appointment data for backend (matching Django serializer)
      const appointmentData = {
        patient: patient.id || generateId(), // Patient ID (required)
        doctor: null, // Optional - can be null
        title: `${cleanFirstName} ${cleanLastName}`, // Required - using cleaned data
        description: patient.comment || '', // Optional
        appointment_type: mapAppointmentType(patient.typeConsultation || 'consultation'), // Required with valid choice
        status: 'scheduled', // Required with valid choice
        priority: 'normal', // Required with valid choice
        appointment_date: newAppointment.start.toISOString().split('T')[0], // Required (YYYY-MM-DD format)
        appointment_time: newAppointment.start.toTimeString().split(' ')[0], // Required (HH:MM:SS format)
        duration_minutes: patient.consultationDuration || 30, // Required with default (15-480 range)
        room: '', // Optional
        equipment_needed: '', // Optional
        notes: patient.notes || '', // Optional
        reason_for_visit: patient.comment || '', // Optional
        symptoms: '', // Optional
        estimated_cost: null, // Optional
        insurance_covered: false, // Optional with default
      };

      // Save to backend using direct API call
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${API_URL}/api/appointments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appointmentData),
      });

      let savedAppointment = null;
      if (response.ok) {
        savedAppointment = await response.json();
        console.log('✅ Appointment saved to backend:', savedAppointment);
      } else {
        const errorData = await response.text();
        console.error('❌ Backend appointment creation failed:', response.status, errorData);
        throw new Error(`HTTP ${response.status}: ${errorData}`);
      }

      if (savedAppointment) {
        // Update the appointment with backend ID
        newAppointment.id = savedAppointment.id;

        // Add the new appointment to the appointments state (only once)
        setAppointments(prev => {
          const updatedAppointments = [...prev, newAppointment];
          localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
          return updatedAppointments;
        });
        // Remove the patient from the waiting list (only once)
        setWaitingList(prev => prev.filter((_, index) => index !== source.index));

        // Refresh appointments from backend to get full patient data
        setTimeout(() => {
          console.log('🔄 Refreshing appointments from backend after drag & drop...');
          loadAppointmentsFromBackend();
        }, 1000);

        // Show success notification
        notifications.show({
          title: 'Rendez-vous créé',
          message: `Rendez-vous pour ${patient.first_name} ${patient.last_name} a été programmé et sauvegardé`,
          color: 'green',
          autoClose: 3000,
        });

        console.log('✅ Appointment created via drag & drop:', savedAppointment);
      } else {
        throw new Error('Failed to save appointment to backend');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create appointment';
      console.error('❌ Error creating appointment via drag & drop:', error);

      setAppointmentError(errorMessage);

      notifications.show({
        title: 'Erreur',
        message: `Impossible de créer le rendez-vous: ${errorMessage}`,
        color: 'red',
        autoClose: 5000,
      });
    } finally {
      setLoadingAppointments(false);
    }
    return;
  }
};
// Activate an appointment - now adds to waiting room (salle de présence) instead of active visits
const activateAppointment = (appointment: Appointment) => {
  console.log('🔍 activateAppointment called for:', appointment.first_name, appointment.last_name, 'isActive:', appointment.isActive);

  const updatedAppointment = {
    ...appointment,
    isActive: !appointment.isActive
  };

   // Update appointments
   setAppointments(prev => prev.map(app =>
    app.id === appointment.id ? updatedAppointment : app
  ));

// Instead of adding to active visits, add to waiting room (salle de présence)
if (updatedAppointment.isActive) {
  // Check if this appointment is already in waitingRoomVisits to prevent duplicates
  const isAlreadyInWaitingRoom = waitingRoomVisits.some(visit => visit.id === appointment.id);

  if (!isAlreadyInWaitingRoom) {
    setWaitingRoomVisits(prev => [...prev, updatedAppointment]);
  }

  notifications.show({
    title: 'Visite Activée',
    message: `${appointment.first_name} ${appointment.last_name} est maintenant en salle de présence`,
    color: 'green',
    autoClose: 2000,
  });
} else {
  // If deactivating, remove from waiting room
  setWaitingRoomVisits(prev => prev.filter(visit => visit.id !== appointment.id));

  notifications.show({
    title: 'Visite Désactivée',
    message: `${appointment.first_name} ${appointment.last_name} a été retiré de la salle de présence`,
    color: 'orange',
    autoClose: 2000,
  });
}
};

// Remove an appointment
const removeAppointment = async (appointment: Appointment) => {
  try {
    setLoadingAppointments(true);
    setAppointmentError(null);

    // Delete from backend (or locally if backend unavailable)
    await appointmentService.deleteAppointment(appointment.id);

    // Always update local state regardless of backend availability
    // The appointmentService handles backend unavailability gracefully
    setAppointments(prevAppointments => {
      const updatedAppointments = prevAppointments.filter(app => app.id !== appointment.id);
      // Save to localStorage for persistence
      localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
      return updatedAppointments;
    });

    setActiveVisits(prevActiveVisits =>
      prevActiveVisits.filter(app => app.id !== appointment.id)
    );

    // Show appropriate notification based on backend availability
    // Check console logs to determine if backend was available
    const backendAvailable = !console.log.toString().includes('Backend server not running');

    notifications.show({
      title: 'Rendez-vous supprimé',
      message: backendAvailable
        ? `Le rendez-vous de ${appointment.title} a été supprimé de la base de données`
        : `Le rendez-vous de ${appointment.title} a été supprimé localement`,
      color: backendAvailable ? 'green' : 'orange',
      autoClose: 3000,
    });

    console.log('✅ Appointment deleted successfully:', appointment.id);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to delete appointment';
    console.error('❌ Error deleting appointment:', error);

    // Even if there's an error, try to delete locally
    setAppointments(prevAppointments => {
      const updatedAppointments = prevAppointments.filter(app => app.id !== appointment.id);
      localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
      return updatedAppointments;
    });

    setActiveVisits(prevActiveVisits =>
      prevActiveVisits.filter(app => app.id !== appointment.id)
    );

    setAppointmentError(errorMessage);

    notifications.show({
      title: 'Rendez-vous supprimé',
      message: `Le rendez-vous de ${appointment.title} a été supprimé localement (backend indisponible)`,
      color: 'orange',
      autoClose: 5000,
    });
  } finally {
    setLoadingAppointments(false);
  }
};
// Add this function with your other handlers
const handleLastVisit = (appointment: Appointment) => {
  const patient = patients.find(p => p.id === appointment.patientId);
  if (patient?.lastVisit) {
    setViewPatient(patient);
    setInfoModalOpen(true);
  } else {
    notifications.show({
      title: 'No Previous Visit',
      message: 'This patient has no recorded previous visits.',
      color: 'yellow',
      autoClose:1000
    });
  }
};
// Reschedule for next available time
const rescheduleAppointment = (appointment: Appointment) => {
  setAppointmentToReschedule(appointment);
  setRescheduleModalOpen(true);
  // For demo purposes, we'll just add a day
  const newStart = new Date(appointment.start);
  newStart.setDate(newStart.getDate() + 1);

  const newEnd = new Date(appointment.end);
  newEnd.setDate(newEnd.getDate() + 1);

  const updatedAppointment = {
    ...appointment,
    start: newStart,
    end: newEnd,
  };

  setAppointments(appointments.map(app =>
    app.id === appointment.id ? updatedAppointment : app
  ));

  notifications.show({
    title: 'Appointment Rescheduled',
    message: `${appointment.title}'s appointment has been rescheduled`,
    color: 'blue',
    autoClose:1000
  });
};
// const [, setAlertsOpened] = useState(false);
//  // Add these handler functions with your other functions
// const handleAddAlert = (appointment: Appointment) => {
//   notifications.show({
//     title: 'Alert Added',
//     message: `Alert set for ${appointment.title}'s appointment`,
//     color: 'blue',
//   });

// };
const handleAddReminder = (appointment: Appointment) => {
  notifications.show({
    title: 'Reminder Added',
    message: `Reminder set for ${appointment.title}'s appointment`,
    color: 'blue',
  });
};
  const eventStyleGetter = useCallback((event: Appointment) => {
   const getEventStyle = (eventType: string) => {
     switch (eventType) {
       case 'visit':
         return '#34D1BF';
       case 'visitor-counter':
         return '#F17105';
       case 'completed':
         return '#3799CE';
       case 'diagnosis':
         return '#F3124E';
       default:
         return '#34D1BF';
     }
   };
     let baseStyle: React.CSSProperties = {
       backgroundColor: getEventStyle(event.eventType),
       borderColor: getEventStyle(event.eventType),
       pointerEvents: event.disabled ? 'none' : 'auto',
     };

     if (event.lunchTime) {
       baseStyle = {
         ...baseStyle,
         color: "white",
         fontWeight: "bold",
         borderRadius: "4px",
         backgroundColor: event.color || "#3799CE",
         border: '0px',
         display: 'block',
         width: '100%',
         height: 'auto !important',
         transition: 'all 0.3s ease-in-out',
         cursor: 'pointer',
         overflow: 'hidden',
         padding: '8px'
       };
     }
     return { style: baseStyle };
   }, []); // Memoize to prevent re-renders
// Open edit patient form
const openEditPatient = (patient: Patient) => {
  setCurrentPatient(patient);
  patientForm.setValues({
    title: patient.title,
    first_name: patient.first_name,
    last_name: patient.last_name,
    age: patient.age,
    phone_numbers: patient.phone_numbers,
    socialSecurity: patient.socialSecurity,
    duration: patient.duration,
    agenda: patient.agenda,
    comment: patient.comment,
    address: patient.address,
  });
  setType(patient.type);
  setPatientName(patient.first_name);
  setPatientlastName(patient.last_name);
  setEventDateDeNaissance(patient.birth_date);
  setEventAge(patient.age);
  setEventEtatCivil(patient.etatCivil);
  setEventCin(patient.cin || '');
  setAddress(patient.address);
  setEventTelephone(patient.phone_numbers);
  setEmail(patient.email);
  setPatientDocteur(patient.docteur);
  setEventTitle(patient.event_Title);
  setGenderOption(patient.gender);
  setSocialSecurity(patient.socialSecurity);
  setPatientTypeConsultation(patient.typeConsultation);
  setEventAganda(patient.etatAganda);
  setPatientcomment(patient.comment);
  setPatientNotes(patient.notes);
  setEventResourceId(Number(eventResourceId));
  setEventDate(patient.appointmentDate);
  setEventTime(patient.appointmentTime);
  setDureeDeLexamen(patient.consultationDuration?.toString() || '15 min');
  EditwaitingList();
};

// Update patient
const handleUpdatePatient = (values: PatientFormValues) => {
    const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();

  if (!currentPatient) return;
  const updatedPatient = {
    ...currentPatient,
    title: type,
    eventType: eventType,
    first_name: patientName,
    last_name: patientlastName,
    sociale: patientsocialSecurity, // Make sure this is updated
    socialSecurity: patientsocialSecurity,
    birth_date: eventDateDeNaissance,
    age: eventAge || 0,
    cin: eventCin,
    address: address,
    start: startDateTime,
    phone_numbers: eventTelephone,
    email: email,
    duration: dureeDeLexamen,
    end: moment(`${eventDate}T${eventTime}`).add(parseInt(dureeDeLexamen), 'minutes').toDate(),
    comment: patientcomment,
    notes: patientnotes,
    commentairelistedattente: patientcommentairelistedattente,
    resourceId: Number(eventResourceId),
    type: eventType,
    color: colorMap[eventType as EventType],
    appointmentDate: eventDate,
    appointmentTime: eventTime,
    appointmentEndTime: calculateEndTime(),
    consultationDuration: parseInt(dureeDeLexamen),
    etatCivil: eventEtatCivil,
    etatAganda:eventAganda,
    docteur: patientdoctor,
    event_Title:eventTitle,
    gender: genderOption,
    date: eventDate,
    typeConsultation: patienttypeConsultation,
    visitorCount: eventType === "visitor-counter" ? 1 : 0,
    name: values.name,
   prenom: values.prenom,
  };

  setPatients(patients.map(p => p.id === currentPatient.id ? updatedPatient : p));
  setWaitingList(waitingList.map(p => p.id === currentPatient.id ? updatedPatient : p));
  // Update appointment titles if this patient has appointments
  const fullName = `${values.title} `;
  setAppointments(appointments.map(app =>
    app.patientId === currentPatient.id ? { ...app, title: fullName } : app
  ));

  setActiveVisits(activeVisits.map(app =>
    app.patientId === currentPatient.id ? { ...app, title: fullName } : app
  ));
  notifications.show({
    title: 'Patient Updated',
    message: `${fullName}'s information has been updated`,
    color: 'blue',
    autoClose:1000
  });
  patientForm.reset();
  //resetForm();
  setCurrentPatient(null);
  setPatientModalOpen(false);
};

// Wrapper function pour handleUpdatePatient pour correspondre au type AppointmentFormValues
const handleUpdatePatientWrapper = (formValues: AppointmentFormValues) => {
  // Convertir AppointmentFormValues vers PatientFormValues
  const patientValues: PatientFormValues = {
    title: '',
    first_name: patientName,
    last_name: patientlastName,
    age: eventAge || 0,
    phone_numbers: eventTelephone,
    etatAganda: eventAganda,
    docteur: patientdoctor,
    event_Title: eventTitle,
    gender: genderOption,
    socialSecurity: patientsocialSecurity,
    duration: dureeDeLexamen,
    agenda: '',
    comment: patientcomment,
    address: address,
    type: formValues.type,
    resourceId: formValues.resourceId,
    name: patientnom,
    prenom: patientprenom,
    email: email,
    typeConsultation: patienttypeConsultation,
    date: formValues.date,
    commentairelistedattente: patientcommentairelistedattente,
    etatCivil: eventEtatCivil,
    patientTitle: ''
  };

  handleUpdatePatient(patientValues);
};

// Record a visit (simulate adding last visit data)

const handleEditClick = async (appointment: Appointment) => {
  console.log('🔍 Editing appointment:', appointment);
  console.log('🔍 Looking for patient ID:', appointment.patientId);
  console.log('🔍 Available patients:', patients.length);

  // Check authentication status
  const token = localStorage.getItem('token');
  console.log('🔐 Auth token available:', !!token);
  if (token) {
    console.log('🔐 Token preview:', token.substring(0, 50) + '...');
  } else {
    console.warn('⚠️ No authentication token found. Patient data fetching will fail.');
    console.warn('⚠️ User needs to login to access patient data.');
  }

  console.log('🔍 Looking for patient ID:', appointment.patientId);
  console.log('🔍 Available patients:', patients.length);
  console.log('🔍 Appointment data fields:', {
    id: appointment.id,
    patientId: appointment.patientId,
    first_name: appointment.first_name,
    last_name: appointment.last_name,
    birth_date: appointment.birth_date,
    phone_numbers: appointment.phone_numbers,
    email: appointment.email,
    address: appointment.address,
    cin: appointment.cin,
    age: appointment.age,
    consultationDuration: appointment.consultationDuration,
    docteur: appointment.docteur,
    typeConsultation: appointment.typeConsultation
  });

  // First try to find patient in local state
  let patientData = patients.find(p => p.id === appointment.patientId);
  console.log('🔍 Found patient data in local state:', patientData);

  // If not found locally and we have a patientId, try to fetch from backend
  if (!patientData && appointment.patientId && appointment.patientId !== 'lunch') {
    console.log('🔍 Patient not found locally, attempting to fetch from backend...');
    try {
      const backendPatientData = await fetchPatientDetails(appointment.patientId);
      if (backendPatientData) {
        console.log('✅ Successfully fetched patient from backend:', backendPatientData);
        // Convert backend format to local format
        patientData = {
          id: backendPatientData.id,
          title: backendPatientData.title || '',
          name: backendPatientData.first_name || '',
          prenom: backendPatientData.last_name || '',
          first_name: backendPatientData.first_name || '',
          last_name: backendPatientData.last_name || '',
          birth_date: backendPatientData.date_of_birth || backendPatientData.birth_date || '',
          appointmentDate: '',
          appointmentTime: '',
          appointmentEndTime: '',
          consultationDuration: 30,
          cin: backendPatientData.cin || '',
          email: backendPatientData.email || '',
          age: backendPatientData.age || 0,
          phone_numbers: backendPatientData.phone_number || backendPatientData.phone_numbers || '',
          socialSecurity: backendPatientData.social_security || backendPatientData.socialSecurity || '',
          agenda: backendPatientData.agenda || '',
          address: backendPatientData.address || '',
          etatCivil: backendPatientData.etat_civil || backendPatientData.etatCivil || '',
          etatAganda: backendPatientData.etat_aganda || backendPatientData.etatAgenda || '',
          patientTitle: backendPatientData.title || '',
          docteur: backendPatientData.doctor_assigned || backendPatientData.docteur || '',
          gender: backendPatientData.gender || 'Homme',
          sociale: backendPatientData.social_security || backendPatientData.socialSecurity || '',
          typeConsultation: '',
          notes: backendPatientData.notes || '',
          comment: backendPatientData.comment || '',
          commentairelistedattente: '',
          duration: '30',
          date: '',
          event_Title: `${backendPatientData.first_name || ''} ${backendPatientData.last_name || ''}`.trim(),
          resourceId: 1,
          visitorCount: 0,
          lunchTime: false,
          checkedListedattente: false,
          type: 'visit' as EventType,
          eventType: 'visit' as EventType
        };
      }
    } catch (error) {
      console.error('❌ Failed to fetch patient from backend:', error);
    }
  }

  console.log('🔍 Final patient data to use:', patientData);

  // If we still don't have patient data, show a helpful message
  if (!patientData && appointment.patientId && appointment.patientId !== 'lunch') {
    notifications.show({
      title: 'Patient non trouvé',
      message: 'Impossible de charger les données du patient. Veuillez sélectionner un patient existant ou saisir les informations manuellement.',
      color: 'yellow',
      autoClose: 5000,
    });
  }

  // Ensure patient data has the correct id that matches appointment.patientId
  const patientWithCorrectId = patientData ? {
    ...patientData,
    id: appointment.patientId // This ensures consistency
  } : {
    // Create minimal patient data from appointment if not found
    // Check if appointment has default/placeholder values and warn user
    id: appointment.patientId || appointment.id,
    first_name: appointment.first_name || appointment.first_name || '',
    last_name: appointment.last_name || appointment.last_name || '',
    birth_date: appointment.birth_date || '',
    age: appointment.age || 0,
    phone_numbers: appointment.phone_numbers || '',
    email: appointment.email || appointment.email || '',
    address: appointment.address || '',
    cin: appointment.cin || '',
    socialSecurity: appointment.socialSecurity || '',
    gender: appointment.gender || '',
    etatCivil: appointment.etatCivil || '',
    docteur: appointment.docteur || '',
    typeConsultation: appointment.typeConsultation || '',
    notes: appointment.notes || '',
    comment: appointment.comment || '',
  };

 


  const appointmentEvent: AppointmentEvent = {
    ...appointment,
    patient: patientWithCorrectId ? {
      ...patientWithCorrectId,
      title: appointment.title,
      first_name: appointment.first_name,
      last_name: appointment.last_name,
      resourceId: Number(eventResourceId), // Default to Room A
      birth_date: appointment.birth_date,
      age: appointment.age,
      agenda: appointment.agenda,
      etatCivil: appointment.etatCivil,
      etatAganda: appointment.etatAganda,
      cin: appointment.cin,
      address: appointment.address,
      phone_numbers: appointment.phone_numbers,
      email: appointment.email,
      docteur: appointment.docteur || "",
      event_Title: appointment.event_Title || "",
      gender:  appointment.gender || "Homme",
      typeConsultation: appointment.typeConsultation || "",
      notes: appointment.notes || "",
      commentairelistedattente: appointment.comment || "",
      sociale: appointment.socialSecurity || "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      socialSecurity: appointment.socialSecurity || "",
      duration: appointment.consultationDuration?.toString() || "30",
      comment: appointment.comment || "",
      patientTitle: appointment.patientTitle || "",
      date: new Date().toISOString(),
      consultationDuration: appointment.consultationDuration || 30,
      type: "visit",
      lunchTime: false,
      eventType:appointment.eventType || "",
      name: appointment.name,
     prenom: appointment.prenom,
    } : undefined,
    // Override appointment fields with patient data (patient data takes priority)
    first_name: patientWithCorrectId?.first_name || appointment.first_name ,
    last_name: patientWithCorrectId?.last_name || appointment.last_name ,
    birth_date: patientWithCorrectId?.birth_date || (patientWithCorrectId && 'date_of_birth' in patientWithCorrectId ? String(patientWithCorrectId['date_of_birth']) : '') || appointment.birth_date || '',
    age: patientWithCorrectId?.age || appointment.age || 0,
    phone_numbers: patientWithCorrectId?.phone_numbers || (patientWithCorrectId && 'phone_number' in patientWithCorrectId ? String(patientWithCorrectId['phone_number']) : '') || appointment.phone_numbers || '',
    email: patientWithCorrectId?.email || appointment.email ,
    address: patientWithCorrectId?.address || appointment.address || '',
    cin: patientWithCorrectId?.cin || appointment.cin || '',
    socialSecurity: patientWithCorrectId?.socialSecurity || (patientWithCorrectId && 'social_security' in patientWithCorrectId ? String(patientWithCorrectId['social_security']) : '') || appointment.socialSecurity || '',
    gender: patientWithCorrectId?.gender || appointment.gender || '',
    etatCivil: patientWithCorrectId?.etatCivil || (patientWithCorrectId && 'etat_civil' in patientWithCorrectId ? String(patientWithCorrectId['etat_civil']) : '') || appointment.etatCivil || '',
    etatAganda: (patientWithCorrectId && 'etatAganda' in patientWithCorrectId ? patientWithCorrectId.etatAganda : '') || (patientWithCorrectId && 'etat_aganda' in patientWithCorrectId ? String(patientWithCorrectId['etat_aganda']) : '') || appointment.etatAganda || '',
    docteur: patientWithCorrectId?.docteur || (patientWithCorrectId && 'doctor_assigned' in patientWithCorrectId ? String(patientWithCorrectId['doctor_assigned']) : '') || appointment.docteur || '',
    typeConsultation: patientWithCorrectId?.typeConsultation || appointment.typeConsultation || '',
  };

  
  // Check for missing critical fields and warn user
  const missingFields = [];
  if (!appointmentEvent.birth_date) missingFields.push('Date de naissance');
  if (!appointmentEvent.phone_numbers) missingFields.push('Téléphone');
  if (!appointmentEvent.email) missingFields.push('Email');
  if (!appointmentEvent.address) missingFields.push('Adresse');
  if (!appointmentEvent.cin) missingFields.push('CIN');

  if (missingFields.length > 0) {
    console.warn('⚠️ Missing patient fields:', missingFields.join(', '));
    console.warn('💡 These fields were not filled when the appointment was created or patient data is incomplete.');
  }

  setEventDate(moment(appointmentEvent.start).format('YYYY-MM-DD'));
  setEventTime(moment(appointmentEvent.start).format('HH:mm'));

  // Set other event details with enhanced fallback logic
  setGenderOption(appointmentEvent.gender || 'Homme');
  setEventEtatCivil(appointmentEvent.etatCivil || '');
  setEventCin(appointmentEvent.cin || '');
  setAddress(appointmentEvent.address || '');
  setEventTelephone(appointmentEvent.phone_numbers || '');
  // Email will be set later with placeholder filtering
  setPatientDocteur(appointmentEvent.docteur || '');
  setSocialSecurity(appointmentEvent.socialSecurity || 'Aucune');
  setPatientTypeConsultation(appointmentEvent.typeConsultation || '');
  setEventAganda(appointmentEvent.etatAganda || '');
  setEventType(appointmentEvent.type as EventType || 'visit');
  setPatientcomment(appointmentEvent.comment || '');
  setPatientNotes(appointmentEvent.notes || '');
  setEventTitle(appointmentEvent.event_Title|| '');

  // Handle placeholder data - don't populate form with default values
  const firstName = appointmentEvent.first_name || appointmentEvent.first_name || '';
  const lastName = appointmentEvent.last_name || appointmentEvent.last_name || '';
  const emailValue = appointmentEvent.email || appointmentEvent.email || '';

  setPatientName(firstName);
  setPatientlastName(lastName);
  setEmail(emailValue);

  console.log('🔍 Setting form fields:');
  console.log('  - First Name:', firstName, '(filtered from:', appointmentEvent.first_name, ')');
  console.log('  - Last Name:', lastName, '(filtered from:', appointmentEvent.last_name, ')');
  console.log('  - Email:', emailValue, '(filtered from:', appointmentEvent.email, ')');

  // Enhanced birth date handling
  const birthDate = appointmentEvent.birth_date || '';
  setEventDateDeNaissance(birthDate);
  console.log('🔍 Setting birth date:', birthDate);

  // Enhanced age handling
  const age = appointmentEvent.age || 0;
  setEventAge(age);
  console.log('🔍 Setting age:', age);

  setEventType(appointmentEvent.eventType as EventType || 'visit');
  setEventResourceId(Number(eventResourceId));

  // Set duration field
  const durationValue = appointmentEvent.consultationDuration || appointmentEvent.duration;
  if (durationValue) {
    setDureeDeLexamen(`${durationValue} min`);
    console.log('🔍 Setting duration to:', `${durationValue} min`);
  } else {
    setDureeDeLexamen('15 min');
    console.log('🔍 Setting default duration: 15 min');
  }

  // Also populate the appointmentForm for the modal
  appointmentForm.setValues({
    patientId: appointmentEvent.patientId || appointmentEvent.id,
    notes: appointmentEvent.notes || '',
    date: appointmentEvent.start || new Date(),
    duration: appointmentEvent.consultationDuration || 30,
    type: appointmentEvent.type || 'visit',
    resourceId: Number(eventResourceId),
    addToWaitingList: false,
    removeFromCalendar: false,
    rescheduleDateTime: '',
  });

  setShowEditModal(true);
  setSelectedEvent(appointmentEvent);
};

// Clear localStorage function
const clearAppointmentFormStorage = useCallback(() => {
  if (typeof window !== 'undefined') {
    const keysToRemove = [
      'appointmentEventDate',
      'appointmentEventTime',
      'appointmentEventConsultation',
      'appointmentDureeDeLexamen',
      'appointmentEventDateDeNaissance',
      'appointmentEventAge',
      'appointmentGenderOption',
      'appointmentEventCin',
      'appointmentPatientName',
      'appointmentPatientLastName',
      'appointmentEmail',
      'appointmentAddress',
      'appointmentSocialSecurity',
      'appointmentPatientComment',
      'appointmentEventAgenda',
      'appointmentPatientDoctor',
      'appointmentEventTitle',
      'appointmentPatientNotes',
      'appointmentTypeConsultation',
      'appointmentCommentaireListedAttente'
    ];

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }
}, []);

const resetForm = useCallback(() => {
  setPatientName('');
  setPatientlastName('');
  setType('');
  setEventType("visit");
  setPatientName('');
  setPatientlastName('');
  setEventEtatCivil('');
  setPatientDocteur('');
  setEventTitle('');
 setEventEtatCivil('');
  setEventAganda('');
  setPatientTypeConsultation('');
  setSearchValue('');
  setPatientCommentairelistedattente('');
  setPatientcomment('');
  setEventTelephone('');
  setEventSociale('');
  setSocialSecurity('');
  setPatientcomment('');
  setPatientNotes('');
  setEmail('');
  setAddress('');
   setEventConsultation('15');
  setEventDateDeNaissance('');
  setEventAge(null);
  setGenderOption('Homme');
  setEventCin('');
  setSelectedSlot(null);
  setSelectedEvent(null);
  setDureeDeLexamen('15 min');
    setEventDate(moment().format('YYYY-MM-DD')); // Set to current date
  setEventTime(moment().format('HH:mm')); // Set to current time
  setEventConsultation('15');
  setCheckedAppelvideo(false);
  setCheckedRappelSms(false);
  setCheckedRappelEmail(false);
    // Reset the form values for the switches
    if (appointmentForm) {
      appointmentForm.setValues({
        ...appointmentForm.values,
        addToWaitingList: false,
        removeFromCalendar: false
      });
    }

    // Clear localStorage when form is reset
    clearAppointmentFormStorage();
}, [appointmentForm, clearAppointmentFormStorage]);
const handleRemoveLunchEvent = (eventId: string, resourceId: number | undefined) => {
  setPatientEvents(prevEvents => {
    const newEvents = prevEvents.filter(e =>
      !(e.id === eventId && e.resourceId === (resourceId || 0))
    );
    console.log('Removing event:', eventId, 'New events:', newEvents); // Debug log
    return newEvents;
  });

  // Also update appointments if needed
  setAppointments(prevAppointments =>
    prevAppointments.filter(app => app.id !== eventId)
  );

  notifications.show({
    title: 'Lunch Event Removed',
    message: 'The lunch event has been successfully removed',
    color: 'green',
  });
};
    const [lunchTimeDeleteOpened, { open: openedlunchTimeDelete, close: closelunchTimeDelete }] = useDisclosure(false);
    const [lunchColorPickeropened, { open:openedColorPickerlunch, close:closeColorPickerlunch }] = useDisclosure(false);
    const [changeEndValuelunch, setChangeEndValuelunch] = useState('#15AABF');
// Appointment Event Component
const EventComponent = ({ event }: { event: Appointment }) => {
  const appointment = event;
  //const eventId = Number(event.id);

  return (
    <>
          {appointment.lunchTime  ?
          <div  style={{ backgroundColor: appointment.color || changeEndValuelunch || '#15AABF' }} >{/*className="bg-[#15AABF]"*/}
           <Group className="flex  items-center w-full  pl-1 h-[26px]">
               <Group  className={isSidebarVisible ?  "w-[81%] ": "w-[91%]"}>
           <IconClock2 stroke={1.25} />
           <div className="font-bold " >{appointment.title}</div>
           </Group>
           <span onClick={openedlunchTimeDelete} className="cursor-pointer">
  <Icon path={mdiDeleteClockOutline} size={1} className="hover:fill-[#ED0423]" />
</span>
           {/* <Icon path={mdiDeleteClockOutline} size={1} className="hover:fill-[#ED0423] " onClick={() => openedlunchTimeDelete}    /> */}
          <Modal
              opened={lunchTimeDeleteOpened}
              onClose={closelunchTimeDelete}
              withCloseButton={false}
            >
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                Êtes-vous sûr de vouloir supprimer l&apos;heure du déjeuner ?
              </Alert>
              <Group justify="space-between" mt="md" mb="xs">
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="red"

                  onClick={() => {
                    handleRemoveLunchEvent(appointment.id, appointment.resourceId);
                    closelunchTimeDelete(); // Close the modal after deletion
                  }}
                >
                  Oui
                </Button>
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="lime.4"
                  onClick={closelunchTimeDelete}
                >
                  Non
                </Button>
              </Group>
            </Modal>
           </Group>
           </div>
          :
    <>
   {/* <div className="event-content" style={{ backgroundColor:event.color }} >  */}
   <div className="event-content" style={{ backgroundColor: event.currentEventColor || event.color || getEventTypeColor(event.eventType as EventType) }} >
   <Group justify="space-between" h={"26px"} >
    <Menu  shadow="md" width={429} position="bottom-start" transitionProps={{ transition: 'rotate-right', duration: 150 }} withArrow arrowPosition="center">
    <Menu.Target >
      <div className="font-bold flex" onClick={(e) => e.stopPropagation()}>
        <span className=" ml-2 mb-2">
      <Icon path={mdiListBoxOutline} size={1}/>
      </span>
    

      <Text tt="capitalize" ml={6}>{appointment.last_name} {appointment.first_name}</Text>
      
      <Group gap="xs" mb={4} className={isSidebarVisible ?  "ml-2 ": "ml-20"} >
        <IconClock2 size={16} />
      {appointment.consultationDuration} min 
      </Group>
      </div>
    </Menu.Target>
    <Menu.Dropdown onClick={(e) => e.stopPropagation()}
      style={{ backgroundColor: appointment.color }}
      className="left-[156px]"
      >
      <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Menu.Divider />
      <List size="sm" withPadding className="capitalize" type="ordered">
      <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <Icon path={mdiAccountTie} size={1} color={"#3799CE"} />
          <Icon path={mdiArrowRightThin} size={1} color={appointment.color} />
          </Flex>
        </>
      }

    >
     {appointment.last_name} {appointment.first_name}
        </List.Item>

        <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <Icon path={mdiCalendarMonth} size={1} color={"#3799CE"} />
          <Icon path={mdiArrowRightThin} size={1}  color={appointment.color} />
          </Flex>
        </>
      }
    >

      {moment(appointment.start).format("DD/MM/YYYY")}
        </List.Item>
    <List.Item
    icon={
    <>
    <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
      <Icon path={mdiUpdate} size={1} color={"#3799CE"} />
      <Icon path={mdiArrowRightThin} size={1} color={appointment.color} />
      </Flex>
    </>
    }
    >
      {moment(appointment.start).format("HH:mm")}- {moment(appointment.end).format("HH:mm")}
        </List.Item>

        <List.Item
      icon={
      <>
      <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
        <svg stroke="currentColor" fill={"#3799CE"} strokeWidth="0" viewBox="0 0 448 512" height="18px" width="18px" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>
        <Icon path={mdiArrowRightThin} size={1} color={appointment.color} />
        </Flex>
      </>
    }
      >
      {appointment.docteur}
        </List.Item>
        <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <svg stroke="currentColor" fill="#3799CE" strokeWidth="0" viewBox="0 0 16 16" height="18px" width="18px" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5zm-13-1A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2z"></path><path d="M7 5.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m-1.496-.854a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708l.146.147 1.146-1.147a.5.5 0 0 1 .708 0M7 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m-1.496-.854a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 0 1 .708-.708l.146.147 1.146-1.147a.5.5 0 0 1 .708 0"></path></svg> 
          <Icon path={mdiArrowRightThin} size={1} color={appointment.color} />
          </Flex>
        </>
      }
    >
      {appointment.typeConsultation}
        </List.Item>
    </List>
    <Menu.Divider />

    </Card>
      </Menu.Dropdown>
    </Menu>
    <Menu shadow="md" width={200} position="bottom-end">
    <Menu.Target>
                  <span
                    className={`event.color hover:${appointment.color} rounded-full p-1 ml-auto`}
                    onClick={(e) => e.stopPropagation()}
                  >
                     <Group>
                     <Indicator
                      processing
                      inline
                      size={10}
                      offset={0}
                      position="bottom-end"
                      color="red"
                      withBorder
                      className={appointment.lunchTime ? "hidden" : ""}
                      disabled={!activeEvent_Ids.has(Number(event.id)) || activeIds.includes(Number(event.id))}
                      //  activeEventIds.has(Number(event.id))
                      mr={isSidebarVisible ?  40: 65}
                      
                    />
                        <Icon path={mdiDotsVertical} size={1}  className="-mt-1" />
                    </Group>
                  </span>
                </Menu.Target>
                <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
                <Menu.Item leftSection={<MenuIcons.sltVisit size={16} />} >
                <Group justify="space-between"  className="flex justify-between items-center w-full pt-.5 pl-1">
               <span> SLT</span>
            <Button component="div" size="compact-xs" color="#15AABF" >
              {activeVisits.filter(visit => visit.resourceId === appointment.resourceId).length}/30
            </Button>

                </Group>
            </Menu.Item>
                <Menu.Divider />
             <Menu.Item
            leftSection={<MenuIcons.activeVisit size={16} />}
            onClick={() => {
              console.log('🖱️ Menu.Item clicked for appointment:', appointment.first_name, appointment.last_name, 'Current isActive:', appointment.isActive);
              // Always use activateAppointment function for both activation and deactivation
              activateAppointment(appointment);
            }}
>
  <Text c={appointment.isActive ? 'red' : 'green'}>
    {appointment.isActive ? 'Désactiver la visite' : 'Activer la visite'}
  </Text>
</Menu.Item>

    <Menu.Item leftSection={<MenuIcons.lastVisit size={16} />} onClick={() => handleLastVisit(appointment)}>
      Dernière visite
    </Menu.Item>
    <Menu.Divider />
      <Menu.Item
    leftSection={<MenuIcons.editAppointment size={16} />}
    onClick={() => {handleEditClick(appointment)}}
    >
    Modifier RDV
    </Menu.Item>
    <Menu.Item leftSection={<MenuIcons.nextAppointment size={16} />} onClick={() => rescheduleAppointment(appointment)} >
      Prochain RDV
    </Menu.Item>
    <Menu.Divider />

    <Menu.Item leftSection={<MenuIcons.patientFile size={16} />}  component={Link}  href={`/patients/patient-form/${appointment.id}/`}>
      Fiche patient
    </Menu.Item>
    <Menu.Item
    leftSection={<MenuIcons.patientDetails size={16} />}
    onClick={() => {
      setSelectedEvent(appointment);
      setShowViewModal(true);
    }}>
      Détails patient
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item leftSection={<MenuIcons.addAlert size={16} />}
  //   onClick={() => {
  //     handleAddAlert(appointment);
  //      setAlertsOpened(true);
  //   }
  // }
  onClick={() => setIsAlertsModalOpen(true)}
    >
      Ajouter une alerte
    </Menu.Item>
    <Menu.Item leftSection={<MenuIcons.addReminder size={16} />} onClick={() => handleAddReminder(appointment)}>
      Ajouter un rappel
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item
    leftSection={<Icon path={mdiDeleteClockOutline} size={1} />}
    onClick={() => removeAppointment(appointment)}
    >
      Supprimer RDV
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item  color="red" leftSection={<MenuIcons.cancel size={16} color="red" />}>
      Annuler
    </Menu.Item>
        </Menu.Dropdown>
    </Menu>
    </Group>
    </div>
    </>
    }

    </>
  );
};
const handleSubmit = async (formValues: AppointmentFormValues) => {
  // Filter out placeholder data before validation
  const cleanFirstName = patientName === 'User' ? '' : patientName;
  const cleanLastName = patientlastName === 'Test' ? '' : patientlastName;
  const cleanEmail = email === '<EMAIL>' ? '' : email;

  // Validate required fields with cleaned data
  if (!cleanFirstName || !cleanLastName || !eventDate || !eventTime) {
    notifications.show({
      title: 'Validation Error',
      message: 'Please fill in all required fields: First Name, Last Name, Date, and Time. Avoid using placeholder values like "User" or "Test".',
      color: 'red',
      autoClose: 5000,
    });
    return;
  }

  // Warn if placeholder data was detected
  if (patientName === 'User' || patientlastName === 'Test' || email === '<EMAIL>') {
    notifications.show({
      title: 'Placeholder Data Detected',
      message: 'Please replace placeholder values (User, Test, <EMAIL>) with real patient information.',
      color: 'orange',
      autoClose: 5000,
    });
    return;
  }

  console.log('🚀 Starting appointment creation process...');
  console.log('Form values:', formValues);
  console.log('Patient name:', patientName, 'Last name:', patientlastName);
  console.log('Event date:', eventDate, 'Event time:', eventTime);

   if (formValues.addToWaitingList) {
     // Create a new patient object for the waiting list
     const newWaitingListPatient: Patient = {
       id: selectedEvent?.id || generateId(),
       first_name: patientName || selectedEvent?.first_name || "",
       last_name: patientlastName || selectedEvent?.last_name || "",
       birth_date: eventDateDeNaissance || selectedEvent?.birth_date || "",
       age: eventAge || selectedEvent?.age || 0,
       gender: genderOption || selectedEvent?.gender || "",
       etatCivil: eventEtatCivil || selectedEvent?.etatCivil || "",
       etatAganda: eventAganda || selectedEvent?.etatAganda || "",
       cin: eventCin || selectedEvent?.cin || "",
       address: address || selectedEvent?.address || "",
       phone_numbers: eventTelephone || selectedEvent?.phone_numbers || "",
       email: email || selectedEvent?.email || "",
       docteur: patientdoctor || selectedEvent?.docteur || "",
       typeConsultation: patienttypeConsultation || selectedEvent?.typeConsultation || "",
       notes: patientnotes || selectedEvent?.notes || "",
       comment: patientcomment || selectedEvent?.comment || "",
       commentairelistedattente: patientcommentairelistedattente || selectedEvent?.commentairelistedattente || "",
       socialSecurity: patientsocialSecurity || selectedEvent?.socialSecurity || "",
       consultationDuration: parseInt(eventConsultation) || selectedEvent?.consultationDuration || 15,
       patientTitle: patientTitle || selectedEvent?.patientTitle || "",
       name: patientName || selectedEvent?.name || "",
       prenom: patientlastName || selectedEvent?.prenom || "",
       eventType: eventType || selectedEvent?.eventType || "visit",
       // Add the missing required properties
       title: type || selectedEvent?.title || "",
       appointmentDate: eventDate || selectedEvent?.appointmentDate || "",
       appointmentTime: eventTime || selectedEvent?.appointmentTime || "",
       appointmentEndTime: calculateEndTime() || selectedEvent?.appointmentEndTime || "",
       duration: dureeDeLexamen || selectedEvent?.duration || "15 min",
       agenda: patientagenda || selectedEvent?.agenda || "",
       date: eventDate || selectedEvent?.date || new Date().toISOString(),
       sociale: patientsociale || selectedEvent?.sociale || "",
       type: eventType || selectedEvent?.type || "visit",
       patientId: selectedEvent?.patientId || generateId(),
       event_Title: eventTitle || selectedEvent?.event_Title || "",
       resourceId: eventResourceId || selectedEvent?.resourceId || 0,
       visitorCount: selectedEvent?.visitorCount || 0,
       checkedListedattente: selectedEvent?.checkedListedattente || false,
       lunchTime: selectedEvent?.lunchTime || false
     };

     // Add to waiting list

     setWaitingList(prev => [...prev, newWaitingListPatient]);

     // If this is an existing event with an ID, remove it from the calendar
     if (selectedEvent && selectedEvent.id) {
       // Convert IDs to the same type for comparison
       setAppointments(prevAppointments =>
         prevAppointments.filter(appointment => appointment.id !== selectedEvent.id)
       );
     }

     // Show success notification
     notifications.show({
       title: 'Patient added to waiting list',
       message: 'The appointment has been removed from the calendar',
       color: 'teal',
       autoClose: 3000,
     });
     // Close the modal
     closeRendezVous();
     resetForm();
   } else {
     // Create start and end date objects
  const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();
   const endDateTime = moment(`${eventDate} ${calculateEndTime()}`).toDate();

   try {
     // First, create and save the patient to the backend
     // Create patient data that matches the RegisterSerializer expectations
     const tempPassword = 'TempPass123!'; // Temporary password for patient

     // Use cleaned patient data for backend creation (variables already declared at function start)

     // Validate cleaned data before sending to backend
     if (!cleanFirstName || !cleanLastName || !cleanEmail) {
       notifications.show({
         title: 'Invalid Patient Data',
         message: 'Please provide valid patient information. Placeholder values (User, Test, <EMAIL>) are not allowed.',
         color: 'red',
         autoClose: 5000,
       });
       return;
     }

     const patientData = {
       email: cleanEmail,
       password: tempPassword,
       password2: tempPassword, // Required by RegisterSerializer
       first_name: cleanFirstName,
       last_name: cleanLastName,
       user_type: 'patient' as const,
       // Additional patient fields that were missing
       title: patientTitle || '',
       phone_number: eventTelephone || '',
       address: address || '',
       date_of_birth: eventDateDeNaissance || '',
       age: eventAge || 0,
       gender: genderOption || '',
       etat_civil: eventEtatCivil || '',
       cin: eventCin || '',
       social_security: patientsocialSecurity || '',
       profession: '', // Will be filled later if needed
       birth_place: '', // Will be filled later if needed
       father_name: '', // Will be filled later if needed
       mother_name: '', // Will be filled later if needed
       blood_group: '', // Will be filled later if needed
       allergies: '', // Will be filled later if needed
       notes: patientnotes || '',
     };

     // Log essential patient data being sent to backend
     console.log('📤 Sending patient data to backend:', `${cleanFirstName} ${cleanLastName} (${cleanEmail})`);

     // Save patient to backend
     let savedPatient;

     try {
       savedPatient = await globalPatientService.createPatient(patientData);
       console.log('✅ Patient saved to backend:', savedPatient);

       if (!savedPatient || !savedPatient.id) {
         throw new Error('Patient creation failed - no patient ID returned');
       }

       // Show success notification
       notifications.show({
         title: 'Patient Saved',
         message: `Patient ${cleanFirstName} ${cleanLastName} has been successfully saved.`,
         color: 'green',
         autoClose: 3000,
       });

     } catch (patientError) {
       console.error('❌ Backend patient creation failed:', patientError);

       // Show error notification
       notifications.show({
         title: 'Save Failed',
         message: `Failed to save patient to backend. Error: ${patientError instanceof Error ? patientError.message : 'Unknown error'}`,
         color: 'red',
         autoClose: 5000,
       });

       // Create a local patient with generated ID as fallback
       savedPatient = {
         id: generateId(),
         ...patientData,
         created_at: new Date().toISOString(),
         updated_at: new Date().toISOString(),
       };

       console.log('⚠️ Created local patient as fallback:', savedPatient);
     }

     // Create local patient object with required fields
     const newPatient: Patient = {
       id: savedPatient.id,
       title: '',
       first_name: cleanFirstName,
       last_name: cleanLastName,
       name: patientnom,
       prenom: patientprenom,
       birth_date: '',
       appointmentDate: eventDate,
       appointmentTime: eventTime,
       appointmentEndTime: calculateEndTime(),
       consultationDuration: parseInt(dureeDeLexamen),
       email: cleanEmail,
       age: 0,
       phone_numbers: '',
       socialSecurity: '',
       duration: patientduration,
       agenda: patientagenda,
       comment: '',
       address: '',
       etatCivil: '',
       etatAganda: '',
       patientTitle: '',
       notes: '',
       date: eventDate,
       docteur: '',
       event_Title: eventTitle,
       gender: '',
       sociale: '',
       typeConsultation: '',
       commentairelistedattente: '',
       resourceId: Number(eventResourceId),
       type: eventType,
       eventType: eventType,
       patientId: savedPatient.id,
       visitorCount: 0,
       checkedListedattente: false,
       lunchTime: false
     };
   // Create appointment event
   const newAppointment: Appointment = {
     id: generateId(),
     title: newPatient.title,
     start: startDateTime,
     end: endDateTime,
     patientId: newPatient.id,
     isActive: false,
     etatCivil: newPatient.etatCivil,
     etatAganda: newPatient.etatAganda,
     patientTitle: newPatient.title,
     birth_date: newPatient.birth_date,
     age: newPatient.age,
     email: newPatient.email,
     date: eventDate,
     docteur: newPatient.docteur,
     event_Title: newPatient.event_Title,
     gender:newPatient.gender,
     sociale: newPatient.socialSecurity,
     typeConsultation: newPatient.typeConsultation,
     agenda: newPatient.agenda,
     commentairelistedattente: newPatient.comment,
     resourceId: Number(eventResourceId),
      last_name: patientlastName,
     first_name: patientName,
     name: patientnom,
     prenom: patientprenom,
     socialSecurity: patientsocialSecurity,
     duration: patientduration,
     comment: patientcomment,
     notes: patientnotes,
     cin: genderOption !== "Enfant" ? eventCin : undefined,
     phone_numbers: eventTelephone,
     address: address,
     appointmentDate: eventDate,
     appointmentTime: eventTime,
     appointmentEndTime: calculateEndTime(),
     consultationDuration: parseInt(dureeDeLexamen),
     type: eventType,
     color: colorMap[eventType],
     eventType:eventType,
     currentEventColor: getEventTypeColor(eventType),
     visitorCount: eventType === "visitor-counter" ? 1 : 0, // Add visitor
   };

     // Map frontend event types to backend appointment types
     const mapAppointmentType = (frontendType: string): string => {
       const typeMapping: { [key: string]: string } = {
         'consultation': 'consultation',
         'follow-up': 'follow_up',
         'emergency': 'emergency',
         'checkup': 'routine_checkup',
         'routine_checkup': 'routine_checkup',
         'procedure': 'procedure',
         'surgery': 'surgery',
         'cleaning': 'cleaning',
         'other': 'other',
       };
       return typeMapping[frontendType] || 'consultation';
     };

     // Validate patient ID format (should be UUID)
     const isValidUUID = (id: string): boolean => {
       const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
       return uuidRegex.test(id);
     };

     if (!isValidUUID(savedPatient.id)) {
       console.warn('⚠️ Patient ID is not a valid UUID:', savedPatient.id);
       console.warn('⚠️ Generating new UUID for patient...');
       savedPatient.id = generateId();
     }

     // Create appointment data for backend API (matching Django serializer)
     console.log('🔍 Using patient ID for appointment:', savedPatient.id, 'Type:', typeof savedPatient.id);

     // Use cleaned patient data for appointment creation (variables already declared at function start)

     const appointmentDataForBackend = {
       patient: savedPatient.id, // Patient ID (required)
       doctor: null, // Optional - can be null
       title: eventTitle || `${cleanFirstName} ${cleanLastName}`, // Required - using cleaned data
       description: patientcomment || '', // Optional
       appointment_type: mapAppointmentType(eventType || 'consultation'), // Required with valid choice
       status: 'scheduled', // Required with valid choice
       priority: 'normal', // Required with valid choice
       appointment_date: eventDate, // Required (YYYY-MM-DD format)
       appointment_time: eventTime.includes(':') && eventTime.split(':').length === 2 ? `${eventTime}:00` : eventTime, // Ensure HH:MM:SS format
       duration_minutes: parseInt(dureeDeLexamen) || 30, // Required with default (15-480 range)
       room: '', // Optional
       equipment_needed: '', // Optional
       notes: patientnotes || '', // Optional
       reason_for_visit: patientcomment || '', // Optional
       symptoms: '', // Optional
       estimated_cost: null, // Optional
       insurance_covered: false, // Optional with default

       // Appointment-specific fields only
       resource_id: eventResourceId || '', // Resource ID for room assignment

       // Additional fields for frontend compatibility (these will be ignored by backend)
       event_title: eventTitle || '', // Event title for frontend
       event_type: eventType || 'consultation', // Event type for frontend
     };

     // Try to save appointment to backend using direct API call
     try {
       console.log('📤 Attempting to create appointment with backend data:', appointmentDataForBackend);

       // Check if backend is available first
       const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
       const healthCheck = await fetch(`${API_URL}/api/appointments/`, {
         method: 'GET',
         headers: {
           'Content-Type': 'application/json',
         },
       });

       if (!healthCheck.ok) {
         throw new Error(`Backend not available: ${healthCheck.status}`);
       }

       // Make direct API call to avoid frontend interface conflicts
       const response = await fetch(`${API_URL}/api/appointments/`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
         },
         body: JSON.stringify(appointmentDataForBackend),
       });

       if (response.ok) {
         const savedAppointment = await response.json();
         console.log('✅ Appointment saved to backend:', savedAppointment);

         // Update the appointment with backend ID if successful
         if (savedAppointment && savedAppointment.id) {
           newAppointment.id = savedAppointment.id;
           console.log('✅ Updated appointment with backend ID:', savedAppointment.id);
         }
       } else {
         const errorData = await response.text();
         console.error('❌ Backend appointment creation failed:', response.status);
         console.error('❌ Error details:', errorData);

         if (response.status === 404) {
           throw new Error('Backend server not running. Please start Django server: python manage.py runserver');
         } else if (response.status === 400) {
           throw new Error(`Validation error: ${errorData}`);
         } else {
           throw new Error(`HTTP ${response.status}: Backend error`);
         }
       }
     } catch (appointmentError) {
       const errorMessage = appointmentError instanceof Error ? appointmentError.message : 'Unknown error';
       console.warn('⚠️ Failed to save appointment to backend, continuing with local storage:', errorMessage);

       // Show user-friendly notification about backend issue
       notifications.show({
         title: 'Backend Unavailable',
         message: 'Appointment saved locally. Please start Django server for full functionality.',
         color: 'orange',
         autoClose: 5000,
       });

       // Continue with local appointment creation even if backend fails
     }

   // Update local state regardless of backend status
   setPatients(prev => [...prev, newPatient]);
   setAppointments(prev => {
     const updatedAppointments = [...prev, newAppointment];
     // Save appointments to localStorage for persistence
     localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
     return updatedAppointments;
   });

   // Refresh appointments from backend to get the latest data with full patient details
   setTimeout(() => {
     console.log('🔄 Refreshing appointments from backend...');
     loadAppointmentsFromBackend();
   }, 1000); // Small delay to ensure backend has processed the creation

   // Clear localStorage after successful creation
   clearAppointmentFormStorage();

   // Reset form and close modal
   setShowAddModal(false);
   resetForm();

   // Show success notification
   notifications.show({
     title: 'Appointment Created Successfully',
     message: `Appointment for ${patientName} ${patientlastName} has been scheduled${savedPatient.created_at ? ' and patient saved to database' : ' (local storage)'}`,
     color: 'green',
     autoClose: 3000,
   });

   console.log('✅ Appointment creation process completed successfully');
   if (eventType === "visitor-counter") {
     // setActiveVisits(prev => [...prev, newAppointment]);
     setTotalVisitors(prev => prev + 1);
   }
   setShowAddModal(false);
   resetForm();

   } catch (error) {
     console.error('❌ Error creating patient and appointment:', error);

     // More detailed error logging
     if (error instanceof Error) {
       console.error('Error message:', error.message);
       console.error('Error stack:', error.stack);
     }

     // Check if it's a network error
     const isNetworkError = error && typeof error === 'object' && 'response' in error;
     const networkError = isNetworkError ? error as { response?: { status?: number; statusText?: string } } : null;
     const errorMessage = networkError
       ? `Network error: ${networkError.response?.status || 'Unknown'} - ${networkError.response?.statusText || 'Connection failed'}`
       : error instanceof Error
         ? `Error: ${error.message}`
         : 'Failed to create appointment. Please try again.';

     notifications.show({
       title: 'Error Creating Appointment',
       message: errorMessage,
       color: 'red',
       autoClose: 5000,
     });
   }
   }

  // Update your visitor counter logic when adding new appointments
console.log("setTotalVisitors:",setTotalVisitors)
  // Update both patients and appointments states
};
// Handle edit submission
// Update the handleEditSubmit function
const handleEditSubmit = async (formValues: AppointmentFormValues) => {
    // Form values are passed directly as parameter
    if (selectedEvent && formValues.addToWaitingList) {
      // Create a new waiting list patient from the selected event
      const newWaitingListPatient: Patient = {
        ...selectedEvent,
        id: selectedEvent.patientId || selectedEvent.id, // Use patientId if available, otherwise use id
        // Make sure all required Patient fields are included
        agenda: selectedEvent.agenda || "",
        event_Title: selectedEvent.event_Title || "",
        gender: selectedEvent.gender || "Homme",
        resourceId: selectedEvent.resourceId || 1,
        type: selectedEvent.type || 'visit' as EventType,
        eventType: selectedEvent.eventType || 'visit' as EventType,
        visitorCount: selectedEvent.visitorCount || 0,
        checkedListedattente: selectedEvent.checkedListedattente || false,
        lunchTime: selectedEvent.lunchTime || false
      };
      // Check if patient already exists in waiting list
      const patientExists = waitingList.some(p => p.id === newWaitingListPatient.id);
      if (!patientExists) {
        setWaitingList(prev => [...prev, newWaitingListPatient]);

        notifications.show({
          title: 'Added to Waiting List',
          message: `${selectedEvent.first_name} ${selectedEvent.last_name} has been added to the waiting list`,
          color: 'blue',
          autoClose: 1000,
        });
      } else {
        notifications.show({
          title: 'Already in Waiting List',
          message: `${selectedEvent.first_name} ${selectedEvent.last_name} is already in the waiting list`,
          color: 'yellow',
          autoClose: 1000,
        });
      }
      // Remove from calendar if needed
      if (formValues.removeFromCalendar) {
        setAppointments(prev => prev.filter(appointment => appointment.id !== selectedEvent.id));
      }

      // Show notification
      notifications.show({
        title: 'Patient added to waiting list',
        message: formValues.removeFromCalendar ?
          'The appointment has been removed from the calendar' :
          'The appointment remains on the calendar',
        color: 'teal',
        autoClose: 3000,
      });

      // Close modal and reset form
      setShowEditModal(false);
      resetForm();
      return;
    }
  if (selectedEvent) {
     // Calculate end time based on start time and consultation duration
     const startDateTime = `${eventDate}T${eventTime}`;
     const startMoment = moment(startDateTime);
     const endMoment = moment(startDateTime).add(
       parseInt(dureeDeLexamen) || 15,
       'minutes'
     );
    const updatedEvent: Appointment = {
      ...selectedEvent,
      id: selectedEvent.id,
      patientId: selectedEvent.patientId, // Use correct patientId
      isActive: selectedEvent.isActive || false,
      title: `${patientName} ${patientlastName}`,
      eventType: eventType,
      first_name: patientName,
      last_name: patientlastName,
      start: startMoment.toDate(),
      end: endMoment.toDate(),
      resourceId: Number(eventResourceId),
      sociale: patientsocialSecurity, // Make sure this is updated
      socialSecurity: patientsocialSecurity,
      birth_date: eventDateDeNaissance,
      age: eventAge || 0,
      cin: eventCin,
      address: address,
      phone_numbers: eventTelephone,
      email: email,
      duration: dureeDeLexamen,
      comment: patientcomment,
      notes: patientnotes,
      commentairelistedattente: patientcommentairelistedattente,
      type: eventType,
      color: colorMap[eventType as EventType],
      currentEventColor: getEventTypeColor(eventType),
      appointmentDate: eventDate,
      appointmentTime: eventTime,
      appointmentEndTime: calculateEndTime(),
      consultationDuration: parseInt(dureeDeLexamen),
      etatCivil: eventEtatCivil,
      etatAganda:eventAganda,
      docteur: patientdoctor,
      event_Title:eventTitle,
      gender:genderOption,
      patientTitle: selectedEvent.patientTitle,
      date: eventDate,
      typeConsultation: patienttypeConsultation,
      visitorCount: eventType === "visitor-counter" ? 1 : 0,

    };
    if (!selectedEvent) {
      notifications.show({
        title: 'Error',
        message: 'No event selected for editing',
        color: 'red',
        autoClose:1000
      });
      return;
    }

    // Update patient information first if patientId exists
    try {
      if (selectedEvent.patientId) {
        const patientUpdateData = {
          title: patientTitle || '',
          first_name: patientName || '',
          last_name: patientlastName || '',
          phone_number: eventTelephone || '',
          address: address || '',
          date_of_birth: eventDateDeNaissance || '',
          age: eventAge || 0,
          gender: genderOption || '',
          etat_civil: eventEtatCivil || '',
          cin: eventCin || '',
          social_security: patientsocialSecurity || '',
          notes: patientnotes || '',
        };

        try {
          await patientFormService.updatePatient(selectedEvent.patientId, patientUpdateData);
          console.log('✅ Patient information updated:', selectedEvent.patientId);
        } catch (patientError) {
          console.warn('⚠️ Patient update failed, continuing with appointment update:', patientError);
        }
      }

      // Update appointment in backend
      const updateData = {
        title: `${patientName} ${patientlastName}`,
        appointment_date: eventDate,
        appointment_time: eventTime,
        duration_minutes: parseInt(dureeDeLexamen) || 30,
        description: patientcomment || '',
        notes: patientnotes || '',
        appointment_type: patienttypeConsultation || 'consultation',
        status: 'scheduled',
        resource_id: eventResourceId || '',
      };

      const savedAppointment = await appointmentService.updateAppointment(selectedEvent.id, updateData);
      console.log('✅ Appointment updated via form:', savedAppointment);

      // Update local state regardless of backend availability
      setAppointments(prevAppointments => {
        const updatedAppointments = prevAppointments.map(event =>
          event.id === selectedEvent.id ? updatedEvent : event
        );
        // Save to localStorage for persistence
        localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
        return updatedAppointments;
      });

      if (updatedEvent.isActive) {
        setActiveVisits(prev => prev.map(visit =>
          visit.id === selectedEvent.id ? {
            ...updatedEvent,
            sociale: patientsocialSecurity, // Ensure sociale is updated in active visits
          } : visit
        ));
      }

      // Close the modal and reset form
      setShowEditModal(false);
      resetForm();

      // Determine notification message based on backend availability
      const isBackendAvailable = savedAppointment && typeof savedAppointment === 'object' &&
                                savedAppointment.id && !savedAppointment.id.includes('mock');

      notifications.show({
        title: 'Appointment Updated',
        message: isBackendAvailable
          ? `Appointment for ${patientName} ${patientlastName} has been updated and saved to database`
          : `Appointment for ${patientName} ${patientlastName} has been updated locally`,
        color: isBackendAvailable ? 'blue' : 'orange',
        autoClose: 3000
      });

      // Refresh appointments from backend to get the latest data
      if (isBackendAvailable) {
        setTimeout(() => {
          console.log('🔄 Refreshing appointments from backend after form update...');
          loadAppointmentsFromBackend();
        }, 1000);
      }

    } catch (error) {
      console.error('❌ Error updating appointment via form:', error);

      // Still update local state even if backend fails
      setAppointments(prevAppointments => {
        const updatedAppointments = prevAppointments.map(event =>
          event.id === selectedEvent.id ? updatedEvent : event
        );
        localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
        return updatedAppointments;
      });

      if (updatedEvent.isActive) {
        setActiveVisits(prev => prev.map(visit =>
          visit.id === selectedEvent.id ? {
            ...updatedEvent,
            sociale: patientsocialSecurity,
          } : visit
        ));
      }

      setShowEditModal(false);
      resetForm();

      notifications.show({
        title: 'Appointment Updated',
        message: `Appointment for ${patientName} ${patientlastName} has been updated locally (backend unavailable)`,
        color: 'orange',
        autoClose: 3000
      });
    }
  }
};

// Wrapper function pour handleEditSubmit pour correspondre au type React.FormEvent
const handleEditSubmitWrapper = (e: React.FormEvent) => {
  e.preventDefault();
  // Get the form values
  const formValues = appointmentForm.values;
  handleEditSubmit(formValues);
};

 // Calculate end time based on start time and duration
    const calculateEndTime = () => {
      if (eventTime) {
        return moment(eventTime, "HH:mm")
          .add(parseInt(eventConsultation), "minutes")
          .format("HH:mm");
      }
      return "";
    };

    // Calculate age based on date of birth
    const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const birthDate = e.target.value;
      setEventDateDeNaissance(birthDate);

      if (birthDate) {
        const today = moment();
        const birthMoment = moment(birthDate);
        const age = today.diff(birthMoment, 'years');
        setEventAge(age);
      } else {
        setEventAge(null);
      }
    };
    const handleOptionChange = (value: string) => {
      // Type assertion to ensure value is of type Gender
      const genderValue = value as Gender;
      setGenderOption(genderValue);

      // Reset CIN if Child is selected
      if (genderValue === "Enfant") {
        setEventCin("");
      }
    };
// *********** AND Drag and Drop ListeDattente /////////////////////////////////////



 const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
 const [, setWaitingListEvents] = useState<Event[]>([]);
     // Toggle sidebar visibility
    const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };
   // Handle creating an appointment from calendar click
   const handleSlotSelect = useCallback((slotInfo: {start: Date, end: Date}) => {
    setSelectedSlot(slotInfo);

    // Reset form first, then set the slot-specific values
    resetForm();

    // Pre-fill date and time from the selected slot (after reset)
    const slotDate = moment(slotInfo.start).format('YYYY-MM-DD');
    const slotTime = moment(slotInfo.start).format('HH:mm');
    setEventDate(slotDate);
    setEventTime(slotTime);
    setShowAddModal(true);
    openRendezVous();
  }, [resetForm, openRendezVous]);
   const onEventDrop = useCallback(
    async (args: EventInteractionArgs<Appointment>) => {
      const { event, start, end, resourceId } = args;
       // Create the updated event with the correct resourceId
       const updatedEvent = {
        ...event,
        start: new Date(start),
        end: new Date(end),
        // Ensure resourceId is preserved and converted to number
        resourceId: resourceId ? Number(resourceId) : event.resourceId
      };
      console.log("Dropped event with resourceId:", updatedEvent.resourceId);

      try {
        setLoadingAppointments(true);
        setAppointmentError(null);

        // Prepare update data for backend
        const updateData = {
          title: updatedEvent.title,
          start_time: updatedEvent.start.toISOString(),
          end_time: updatedEvent.end.toISOString(),
          location: `Room ${updatedEvent.resourceId}`,
          room: `Room ${updatedEvent.resourceId}`,
          status: 'scheduled', // Keep existing status
          appointment_type: updatedEvent.typeConsultation || 'Consultation',
          notes: updatedEvent.notes || updatedEvent.comment || ''
        };

        // Update appointment in backend (or locally if backend unavailable)
        const savedAppointment = await appointmentService.updateAppointment(event.id, updateData);

        // Always update local state regardless of backend availability
        // The appointmentService handles backend unavailability gracefully
        setAppointments(prevAppointments => {
          const updatedAppointments = prevAppointments.map((existingEvent) =>
            existingEvent.id === event.id ? updatedEvent : existingEvent
          );
          // Save to localStorage for persistence
          localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
          return updatedAppointments;
        });

        // Determine notification message based on backend availability
        const isBackendAvailable = savedAppointment && typeof savedAppointment === 'object' &&
                                  savedAppointment.id && !savedAppointment.id.includes('mock');

        notifications.show({
          title: 'Rendez-vous modifié',
          message: isBackendAvailable
            ? `Le rendez-vous a été déplacé et sauvegardé en base de données`
            : `Le rendez-vous a été déplacé et sauvegardé localement`,
          color: isBackendAvailable ? 'green' : 'orange',
          autoClose: 3000,
        });

        console.log('✅ Appointment updated via drag & drop:', savedAppointment);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update appointment';
        console.error('❌ Error updating appointment via drag & drop:', error);

        setAppointmentError(errorMessage);

        notifications.show({
          title: 'Erreur',
          message: `Impossible de modifier le rendez-vous: ${errorMessage}`,
          color: 'red',
          autoClose: 5000,
        });

        // Revert the change in UI since backend update failed
        // The UI will remain unchanged since we don't update state on error
      } finally {
        setLoadingAppointments(false);
      }
    },
    [], // Remove appointments dependency to prevent unnecessary re-renders
  );

  // Memoize calendar events to prevent re-renders when only time updates
  const memoizedAppointments = useMemo(() => {
    return appointments.filter(appointment => {
      // Ensure appointment has valid start and end dates
      if (!appointment.start || !appointment.end) {
        console.warn('⚠️ Appointment missing start/end dates:', appointment);
        return false;
      }

      // Ensure start and end are Date objects
      if (!(appointment.start instanceof Date) || !(appointment.end instanceof Date)) {
        console.warn('⚠️ Appointment start/end are not Date objects:', appointment);
        return false;
      }

      return true;
    });
  }, [appointments]);
  const [eventTelephone, setEventTelephone] = useState<string>("");
  const [, setEventSociale] = useState<string>("");
  const [eventEtatCivil, setEventEtatCivil] = useState("");
  const handleAddPatient = (formValues: AppointmentFormValues) => { // eslint-disable-line @typescript-eslint/no-unused-vars
  // Generate a unique ID for the patient
  const patientId = generateId();

  // Filter out placeholder data
  const cleanFirstName = patientName === 'User' ? '' : patientName;
  const cleanLastName = patientlastName === 'Test' ? '' : patientlastName;
  const cleanEmail = email === '<EMAIL>' ? '' : email;

  // Validate required fields with cleaned data
  if (!cleanFirstName || !cleanLastName) {
    notifications.show({
      title: 'Validation Error',
      message: 'Please provide valid patient information. Placeholder values (User, Test) are not allowed.',
      color: 'red',
      autoClose: 5000,
    });
    return;
  }

  // Warn if placeholder data was detected
  if (patientName === 'User' || patientlastName === 'Test' || email === '<EMAIL>') {
    notifications.show({
      title: 'Placeholder Data Detected',
      message: 'Please replace placeholder values (User, Test, <EMAIL>) with real patient information.',
      color: 'orange',
      autoClose: 5000,
    });
    return;
  }

    // Create start and end date objects
    const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();
    const endDateTime = moment(`${eventDate} ${calculateEndTime()}`).toDate();
    const newPatient: Patient = {
      id: patientId,
      title: '',
      first_name: cleanFirstName,
      last_name: cleanLastName,
      name: patientnom,
      prenom: patientprenom,
      birth_date: '',
      appointmentDate: eventDate,
      appointmentTime: eventTime,
      appointmentEndTime: calculateEndTime(),
      consultationDuration: parseInt(dureeDeLexamen),
      email: cleanEmail,
      age: 0,
      phone_numbers: '',
      socialSecurity: '',
      duration: patientduration,
      agenda: patientagenda,
      comment: '',
      address: '',
      etatCivil: '',
      etatAganda: '',
      patientTitle: '',
      notes: '',
      date: eventDate,
      docteur: '',
      event_Title: eventTitle,
      gender: '',
      sociale: '',
      typeConsultation: '',
      commentairelistedattente: '',
      resourceId: eventResourceId,
      type: eventType,
      eventType: eventType,
      start: startDateTime,
      end: endDateTime,
      patientId: patientId,
      visitorCount: 0,
      checkedListedattente: false,
      lunchTime: false
    };
    notifications.show({
      title: 'Patient Added',
      message: `${newPatient.first_name} ${newPatient.last_name} has been added to the waiting list`,
      color: 'green',
      autoClose:1000
    });
    setWaitingList([...waitingList, newPatient]);
    setPatients([...patients, newPatient]);
    // patientForm.reset();
    // setPatientModalOpen(false);
    resetForm();
    // setShowAddModal(false);
    closeAddwaitingList()
  };
   // -----------------------LunchEvent -----------------------------
  // const [lunchEvents, setLunchEvents] = useState<AppointmentEvent[]>([]);
    const [dateSelected, ] = useState<Date>(new Date());
    const [, setShowTimeSelector] = useState<boolean>(false);
    const [selectedHour, setSelectedHour] = useState<number | null>(null);
    const [selectedMinute, setSelectedMinute] = useState<number | null>(null);
    const [duration, setDuration] = useState<number>(60); // Durée par défaut: 60 minutes
      // Générer les options d'heures (11h à 14h)
    const hours = [11, 12, 13, 14, 15, 16];
      // Générer les options de minutes (0, 15, 30, 45)
    const minutes = [0, 15, 30, 45];
     // Options de durée (15min à 120min par intervalles de 15min)
  const durations = [15, 30, 45, 60, 75, 90, 105, 120];

  const addLunchEvent = () => {
    if (selectedHour === null || selectedMinute === null) return;

    const start = new Date(dateSelected);
    start.setHours(selectedHour, selectedMinute, 0);
    const end = new Date(start);
    end.setMinutes(end.getMinutes() + duration);

    const durationText = duration >= 60
      ? `${Math.floor(duration/60)}h${duration % 60 === 0 ? '' : duration % 60}`
      : `${duration}min`;

    // Create lunch event as Appointment type
    const newLunchEvent: Appointment = {
      id: generateId(),
      title: `Déjeuner à ${selectedHour}h${selectedMinute === 0 ? '00' : selectedMinute} (${durationText})`,
      start: start,
      end: end,
      patientId: 'lunch', // Required field for Appointment type
      isActive: false,    // Required field for Appointment type
      lunchTime: true,
      type: "visit",
      resourceId: eventResourceId,
      // Required fields with default values
      first_name: "",
      last_name: "",
      name: "",
      prenom: "",
      agenda: "",
      birth_date: "",
      age: 0,
      etatCivil: "",
      cin: "",
      address: "",
      phone_numbers: "",
      email: "",
      docteur: "",
      event_Title: "",
      gender: "",
      typeConsultation: "",
      notes: "",
      commentairelistedattente: "",
      sociale: "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      etatAganda: "",
      socialSecurity: "",
      duration: "",
      comment: "",
      patientTitle: "",
      date: start.toISOString(),
      consultationDuration: duration,
      eventType:"visit",
      visitorCount: eventType === "visitor-counter" ? 1 : 0,
    };

    // Update appointments state
    setAppointments(prev => [...prev, newLunchEvent]);

    // Reset form
    setShowTimeSelector(false);
    setSelectedHour(null);
    setSelectedMinute(null);
    setDuration(60);
    setEventResourceId(1);

    notifications.show({
      title: 'Lunch Break Added',
      message: 'Lunch break has been scheduled',
      color: 'green',
    });
  };

// Update your function that handles adding waiting list items to the calendar
const handleAddToCalendar = async (patient: Patient) => {
  try {
    setLoadingAppointments(true);
    setAppointmentError(null);

    // Map frontend event types to backend appointment types
    const mapAppointmentType = (frontendType: string): string => {
      const typeMapping: { [key: string]: string } = {
        'consultation': 'consultation',
        'follow-up': 'follow_up',
        'emergency': 'emergency',
        'checkup': 'routine_checkup',
        'routine_checkup': 'routine_checkup',
        'procedure': 'procedure',
        'surgery': 'surgery',
        'cleaning': 'cleaning',
        'other': 'other',
      };
      return typeMapping[frontendType] || 'consultation';
    };

    // Filter out placeholder data before creating appointment
    const cleanFirstName = patient.first_name === 'User' ? '' : patient.first_name || '';
    const cleanLastName = patient.last_name === 'Test' ? '' : patient.last_name || '';

    // Validate cleaned data
    if (!cleanFirstName || !cleanLastName) {
      console.warn('⚠️ Appointment creation blocked: Patient has placeholder data', {
        original: { first_name: patient.first_name, last_name: patient.last_name },
        cleaned: { first_name: cleanFirstName, last_name: cleanLastName }
      });
      notifications.show({
        title: 'Invalid Patient Data',
        message: 'Cannot create appointment with placeholder patient data. Please select a real patient.',
        color: 'red',
        autoClose: 5000,
      });
      setLoadingAppointments(false);
      return;
    }

    // Create appointment data for backend (matching Django serializer)
    const appointmentData = {
      patient: patient.id || generateId(), // Patient ID (required)
      doctor: null, // Optional - can be null
      title: `${cleanFirstName} ${cleanLastName}`, // Required - using cleaned data
      description: patient.comment || '', // Optional
      appointment_type: mapAppointmentType(patient.typeConsultation || 'consultation'), // Required with valid choice
      status: 'scheduled', // Required with valid choice
      priority: 'normal', // Required with valid choice
      appointment_date: patient.appointmentDate, // Required (YYYY-MM-DD format)
      appointment_time: patient.appointmentTime?.includes(':') && patient.appointmentTime.split(':').length === 2
        ? `${patient.appointmentTime}:00` : patient.appointmentTime, // Ensure HH:MM:SS format
      duration_minutes: patient.consultationDuration || 30, // Required with default (15-480 range)
      room: '', // Optional
      equipment_needed: '', // Optional
      notes: patient.notes || '', // Optional
      reason_for_visit: patient.comment || '', // Optional
      symptoms: '', // Optional
      estimated_cost: null, // Optional
      insurance_covered: false, // Optional with default
    };

    // Save to backend using direct API call
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    const response = await fetch(`${API_URL}/api/appointments/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(appointmentData),
    });

    let savedAppointment = null;
    if (response.ok) {
      savedAppointment = await response.json();
      console.log('✅ Appointment saved to backend:', savedAppointment);
    } else {
      const errorData = await response.text();
      console.error('❌ Backend appointment creation failed:', response.status, errorData);
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }

    if (savedAppointment) {
      // Create a new appointment from the patient for local state
      const newAppointment: Appointment = {
        id: savedAppointment.id,
        title: `${patient.first_name} ${patient.last_name}`,
        start: new Date(`${patient.appointmentDate}T${patient.appointmentTime}`),
        end: new Date(`${patient.appointmentDate}T${patient.appointmentEndTime}`),
        patientId: patient.id || generateId(),
        isActive: false,
        resourceId: patient.resourceId || 1, // Default to 1 if not set
        etatCivil: patient.etatCivil,
        etatAganda: patient.etatAganda,
        patientTitle: patient.title,
        birth_date: patient.birth_date,
        age: patient.age,
        email: patient.email,
        date: eventDate,
        docteur: patient.docteur,
        event_Title: patient.event_Title,
        gender: patient.gender,
        sociale: patient.socialSecurity,
        typeConsultation: patient.typeConsultation,
        agenda: patient.agenda,
        commentairelistedattente: patient.comment,
        last_name: patientlastName,
        first_name: patientName,
        name: patientnom,
        prenom: patientprenom,
        socialSecurity: patientsocialSecurity,
        duration: patientduration,
        comment: patientcomment,
        notes: patientnotes,
        cin: genderOption !== "Enfant" ? eventCin : undefined,
        phone_numbers: eventTelephone,
        address: address,
        appointmentDate: eventDate,
        appointmentTime: eventTime,
        appointmentEndTime: calculateEndTime(),
        consultationDuration: parseInt(dureeDeLexamen),
        type: eventType,
        color: colorMap[eventType],
        eventType: eventType,
        currentEventColor: getEventTypeColor(eventType),
        visitorCount: eventType === "visitor-counter" ? 1 : 0, // Add visitor
      };

      // Add to appointments
      setAppointments(prev => [...prev, newAppointment]);

      // Optionally remove from waiting list
      setWaitingList(prev => prev.filter(p => p.id !== patient.id));

      // Show success notification
      notifications.show({
        title: 'Rendez-vous créé',
        message: `${patient.first_name} ${patient.last_name} a été ajouté au calendrier et sauvegardé`,
        color: 'green',
        autoClose: 3000,
      });

      console.log('✅ Appointment created successfully:', savedAppointment);
    } else {
      throw new Error('Failed to create appointment');
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to create appointment';
    console.error('❌ Error creating appointment:', error);

    setAppointmentError(errorMessage);

    notifications.show({
      title: 'Erreur',
      message: `Impossible de créer le rendez-vous: ${errorMessage}`,
      color: 'red',
      autoClose: 5000,
    });
  } finally {
    setLoadingAppointments(false);
  }
};
// Create an adapter function that converts Event to Patient
const handleAddToCalendarAdapter = (event: Event) => {
  // Convert Event to Patient or create a new Patient from Event data
  const patientFromEvent: Patient = {
    id: (event.id || generateId()).toString(),
    title: '',
    first_name: event.title?.split(' ')[0] || '',
    last_name: event.title?.split(' ')[1] || '',
    name: '',
    prenom: '',
    birth_date: '',
    appointmentDate: moment(event.start).format('YYYY-MM-DD'),
    appointmentTime: moment(event.start).format('HH:mm'),
    appointmentEndTime: moment(event.end).format('HH:mm'),
    consultationDuration: moment(event.end).diff(moment(event.start), 'minutes'),
    email: '',
    age: 0,
    phone_numbers: '',
    socialSecurity: '',
    duration: '15 min',
    agenda: '',
    comment: '',
    address: '',
    etatCivil: '',
    etatAganda: '',
    patientTitle: '',
    notes: '',
    date: moment(event.start).format('YYYY-MM-DD'),
    docteur: '',
    event_Title: '',
    gender: '',
    sociale: '',
    typeConsultation: '',
    commentairelistedattente: '',
    resourceId: event.resourceId || 1,
    type: eventType,
    eventType: "visit",
    patientId: (event.id || generateId()).toString(),
    visitorCount: 0,
    checkedListedattente: false,
    lunchTime: false
  };

  // Call your original handler with the converted patient
  handleAddToCalendar(patientFromEvent);
};
// const [state, handlers] = useListState<Event>(events);
const [eventIdCounter,] = useState(1);


    // Generate draggable items
const [VisitesActivesDeleteOpened, { open: openedVisitesActivesDelete, close: closeVisitesActivesDelete }] = useDisclosure(false);
   // const [, setOpenedModalId] = useState<number | null>(null);

  //const closeModal = () => setOpenedModalId(null);
  const deleteVisits = (id: number | string, fromWaitingList = false) => {
    // Convert id to number if it's a string
    const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

    if (isNaN(numericId)) {
      console.error("Invalid event ID:", id);
      notifications.show({
        title: 'Error',
        message: 'Could not delete event: Invalid ID',
        color: 'red',
      });
      return;
    }

    // For debugging
    // console.log("Deleting event with ID:", numericId);
    // console.log("Current events:", events);
    // console.log("Current activeVisits:", activeVisits);

    if (fromWaitingList) {
      setWaitingListEvents((prevEvents) => {
        return prevEvents.filter((event) => Number(event.id) !== numericId);
      });
    } else {
      setEvents((prevEvents) => {
        return prevEvents.filter((event) => Number(event.id) !== numericId);
      });

      // Also update appointments
      setAppointments((prevAppointments) => {
        return prevAppointments.filter((appointment) => Number(appointment.id) !== numericId);
      });
    }

    // Also remove from activeVisits if present
    setActiveVisits((prevVisits) => {
      return prevVisits.filter((event) => Number(event.id) !== numericId);
    });

    // Close the modal after deletion
    closeVisitesActivesDelete();
  };
    const [, handlers] = useListState(appointments);

    // tab content Visites Actives
    const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
    const [value, setValue] = useState<string | null>('1');
    const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});
    const setControlRef = (val: string) => (node: HTMLButtonElement) => {
      controlsRefs[val] = node;
      setControlsRefs(controlsRefs);
    };
  useEffect(() => {
    if (genderOption === "Enfant") {
      setEventCin(""); // Clear CIN when "Enfant" is selected
    }
  }, [genderOption]);

 // const isMobile = useMediaQuery("(max-width: 50em)");
  // const firstOpenModal = () => {
  //   openRendezVous();
  // };
  useEffect(() => {
    // Retrieve the saved date from localStorage, defaulting to an empty string
    const savedDate = localStorage.getItem("counterDate") ?? ""; // This will always be a string
    const currentDate = getCurrentDate(); // This will always return a string
    // Compare and update the localStorage if the date has changed
    if (savedDate !== currentDate) {
      localStorage.setItem("counterDate", currentDate); // currentDate is guaranteed to be a string
      setDailyCounter(0); // Reset counter for the new day
    }
  }, []);
 // Start Alert

 const [isSidebarAlert, setIsSidebarAlert] = useState(false);
  const toggleSidebarAlert = () => {
          setIsSidebarAlert(!isSidebarAlert);
        };
 
 const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
    const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
    const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
     const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
     const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);
     // État pour gérer l'effondrement de chaque nœud
     // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
     const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
       console.log('TreeItemChoixMultiple initialized with all nodes open');
       return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
     });
   
     // État pour gérer les sélections multiples
     const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
   
     // États pour la gestion des modèles
     const [showModels, setShowModels] = useState(false);
     const [showAddModel, setShowAddModel] = useState(false);
     const [modelTitle, setModelTitle] = useState('');
     const [editingModelId, setEditingModelId] = useState<string | null>(null);
     const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
   
     // États pour la reconnaissance vocale
     const [isListening, setIsListening] = useState(false);
     const [validSpeech, setValidSpeech] = useState('');
     const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
     const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
     const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut
   
     // Initialiser la reconnaissance vocale au montage du composant
     useEffect(() => {
       initSpeechRecognition();
     }, []);
   
     // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
     const toggleNodeCollapse = (nodeId: string) => {
       setCollapsedNodes(prev => {
         const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
         // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
         if (currentState === true) { // Si fermé, ouvrir
           console.log('Opening TreeItemChoixMultiple node:', nodeId);
           return {
             ...prev,
             [nodeId]: false // false = ouvert
           };
         } else {
           console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
           return prev; // Ne rien changer si déjà ouvert
         }
       });
     };
     interface TreeNodeChoixMultiple {
       uid: string;
       value: string;
       nodes?: TreeNodeChoixMultiple[];
     }
     function TreeItemChoixMultiple({
       node,
       collapsedNodes,
       toggleNodeCollapse,
       selectedNodes,
       toggleNodeSelection,
     }: {
       node: TreeNodeChoixMultiple;
       collapsedNodes: Record<string, boolean>;
       toggleNodeCollapse: (nodeId: string) => void;
       selectedNodes: Set<string>;
       toggleNodeSelection: (nodeId: string) => void;
     }) {
       // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
       const isCollapsed = collapsedNodes[node.uid] ?? false;
       const isSelected = selectedNodes.has(node.uid);
       // Calculer l'état indéterminé pour les nœuds parents
       const getIndeterminateState = () => {
         if (!node.nodes || node.nodes.length === 0) return false;
         const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
         return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
       };
       const isIndeterminate = getIndeterminateState();
       return (
         <Stack pl="md" gap="xs">
           <Group gap="xs" align="center">
             {node.nodes && node.nodes.length > 0 && (
               <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                 <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
               </span>
             )}
             <Checkbox
               label={node.value}
               checked={isSelected}
               indeterminate={isIndeterminate}
               onChange={() => toggleNodeSelection(node.uid)}
               radius="xs"
             />
           </Group>
     
           {!isCollapsed &&
             node.nodes?.map((child) => (
               <TreeItemChoixMultiple
                 key={child.uid}
                 node={child}
                 collapsedNodes={collapsedNodes}
                 toggleNodeCollapse={toggleNodeCollapse}
                 selectedNodes={selectedNodes}
                 toggleNodeSelection={toggleNodeSelection}
               />
             ))}
         </Stack>
       );
     }
     
     interface AlertFormValues {
       trigger_for: string[];
       trigger: string;
       level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
       description: string;
       is_permanent: boolean;
     }
       // Fonction pour basculer la sélection d'un nœud
       const toggleNodeSelection = (nodeId: string) => {
         setSelectedNodes(prev => {
           const newSet = new Set(prev);
     
           // Trouver le nœud correspondant
           const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
             for (const node of nodes) {
               if (node.uid === id) return node;
               if (node.nodes) {
                 const found = findNode(node.nodes, id);
                 if (found) return found;
               }
             }
             return null;
           };
     
           const currentNode = findNode(exampleData, nodeId);
     
           if (newSet.has(nodeId)) {
             // Désélectionner le nœud et tous ses enfants
             newSet.delete(nodeId);
             if (currentNode?.nodes) {
               const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                 nodes.forEach(child => {
                   newSet.delete(child.uid);
                   if (child.nodes) {
                     removeAllChildren(child.nodes);
                   }
                 });
               };
               removeAllChildren(currentNode.nodes);
             }
           } else {
             // Sélectionner le nœud et tous ses enfants
             newSet.add(nodeId);
             if (currentNode?.nodes) {
               const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                 nodes.forEach(child => {
                   newSet.add(child.uid);
                   if (child.nodes) {
                     addAllChildren(child.nodes);
                   }
                 });
               };
               addAllChildren(currentNode.nodes);
             }
           }
     
           return newSet;
         });
       };
     
       // Fonction pour obtenir les sélections actuelles
       const getSelectedValues = () => {
         const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
           const result: TreeNodeChoixMultiple[] = [];
           nodes.forEach(node => {
             result.push(node);
             if (node.nodes) {
               result.push(...getAllNodes(node.nodes));
             }
           });
           return result;
         };
     
         const allNodes = getAllNodes(exampleData);
         return Array.from(selectedNodes)
           .map(id => allNodes.find(node => node.uid === id))
           .filter(Boolean)
           .map(node => node!.value);
       };
 
       // Fonctions pour sélectionner/désélectionner tous les nœuds
       const selectAllNodes = () => {
         const allNodeIds: string[] = [];
 
         const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
           nodes.forEach(node => {
             allNodeIds.push(node.uid);
             if (node.nodes && node.nodes.length > 0) {
               collectAllIds(node.nodes);
             }
           });
         };
 
         collectAllIds(exampleData);
         setSelectedNodes(new Set(allNodeIds));
       };
 
       const deselectAllNodes = () => {
         setSelectedNodes(new Set());
       };
 
       // Fonctions pour la reconnaissance vocale
       const initSpeechRecognition = () => {
         if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
           const SpeechRecognitionConstructor = (window as unknown as {
             webkitSpeechRecognition: new () => SpeechRecognitionInstance;
             SpeechRecognition: new () => SpeechRecognitionInstance;
           }).webkitSpeechRecognition || (window as unknown as {
             webkitSpeechRecognition: new () => SpeechRecognitionInstance;
             SpeechRecognition: new () => SpeechRecognitionInstance;
           }).SpeechRecognition;
     
           const newRecognition = new SpeechRecognitionConstructor();
     
           newRecognition.continuous = true;
           newRecognition.interimResults = true;
           newRecognition.lang = 'fr-FR';
     
           newRecognition.onstart = () => {
             setIsListening(true);
             setMicrophoneColor('green'); // Changer la couleur en vert
             setInvalidSpeech('Écoute en cours...');
           };
     
           newRecognition.onresult = (event: unknown) => {
             const speechEvent = event as {
               resultIndex: number;
               results: {
                 length: number;
                 [index: number]: {
                   isFinal: boolean;
                   [index: number]: { transcript: string };
                 };
               };
             };
     
             let finalTranscript = '';
             let interimTranscript = '';
     
             for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
               const transcript = speechEvent.results[i][0].transcript;
               if (speechEvent.results[i].isFinal) {
                 finalTranscript += transcript;
               } else {
                 interimTranscript += transcript;
               }
             }
     
             setValidSpeech(finalTranscript);
             setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
           };
     
           newRecognition.onerror = () => {
             setIsListening(false);
             setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
             setInvalidSpeech('Erreur de reconnaissance vocale');
           };
     
           newRecognition.onend = () => {
             setIsListening(false);
             setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
             setInvalidSpeech('Parlez maintenant.');
           };
     
           setRecognition(newRecognition);
         }
       };
     
       const toggleRecognition = () => {
         if (!recognition) {
           initSpeechRecognition();
           return;
         }
     
         if (isListening) {
           recognition.stop();
         } else {
           recognition.start();
         }
       };
     
       const emptyContent = () => {
         setValidSpeech('');
         setInvalidSpeech('Parlez maintenant.');
         setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
         if (recognition && isListening) {
           recognition.stop();
         }
       };
     
      
     
         // État pour savoir quelle alerte est en cours d'édition
         const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);
 
         // États pour le modal de confirmation de suppression
         const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
         const [alertToDelete, setAlertToDelete] = useState<string | null>(null);
 
         // Fonctions pour gérer les alertes
       
 
         const handleDeleteAlert = (alertId: string) => {
           console.log('Delete alert:', alertId);
           // Ouvrir le modal de confirmation
           setAlertToDelete(alertId);
           setIsDeleteConfirmModalOpen(true);
         };
 
         // Fonction pour confirmer la suppression
         const confirmDeleteAlert = () => {
           if (alertToDelete) {
             // Supprimer l'alerte de la liste
             setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
             console.log('Alert deleted:', alertToDelete);
           }
           // Fermer le modal et réinitialiser
           setIsDeleteConfirmModalOpen(false);
           setAlertToDelete(null);
         };
 
         // Fonction pour annuler la suppression
         const cancelDeleteAlert = () => {
           setIsDeleteConfirmModalOpen(false);
           setAlertToDelete(null);
         };
 
         // Fonction pour obtenir la couleur selon le niveau
         const getLevelColor = (level: string) => {
           switch(level) {
             case 'MINIMUM': return 'green';
             case 'MEDIUM': return 'orange';
             case 'HIGH': return 'red';
             default: return 'gray';
           }
         };
 
 
 
         // Fonction pour créer les actions d'une alerte
         const createAlertActions = (alertId: string) => (
           <Group gap="xs">
             <ActionIcon
               variant="subtle"
               color="blue"
               size="sm"
               onClick={() => {
                 console.log('Edit alert clicked:', alertId);
                 setCurrentEditingAlertId(alertId);
 
                 // Trouver l'alerte à éditer et pré-remplir le formulaire
                 const alertToEdit = alertsData.find(alert => alert.id === alertId);
                 console.log('Alert to edit found:', alertToEdit);
                 if (alertToEdit) {
                   // Trouver la valeur correspondante pour le trigger
                   const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';
 
                   console.log('Editing alert:', alertToEdit);
                   console.log('Trigger value found:', triggerValue);
 
                   form.setValues({
                     trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                     trigger: triggerValue,
                     level: alertToEdit.level,
                     description: alertToEdit.Description,
                     is_permanent: alertToEdit.is_permanent
                   });
 
                   console.log('Form values set for editing:', {
                     trigger_for: alertToEdit.trigger_for || [],
                     trigger: triggerValue,
                     level: alertToEdit.level,
                     description: alertToEdit.Description,
                     is_permanent: alertToEdit.is_permanent
                   });
 
                   console.log('Form values set:', form.values);
                 }
 
                 setIsAlertsAddModalOpen(true);
                 setIsSidebarAlert(true); // Ouvrir aussi la sidebar pour les sélections
               }}
             >
               <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
             </ActionIcon>
             <ActionIcon
               variant="subtle"
               color="red"
               size="sm"
               onClick={() => {
                 console.log('Delete alert clicked:', alertId);
                 handleDeleteAlert(alertId);
               }}
             >
               <Icon path={mdiDelete} size={0.8} color={'red'}/>
             </ActionIcon>
           </Group>
         );
 
         // Fonction pour gérer la soumission du formulaire d'alerte
         const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
           console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
           console.log('Current editing alert ID:', currentEditingAlertId);
 
           if (currentEditingAlertId) {
             // Mode édition : mettre à jour l'alerte existante
             console.log('Editing existing alert:', currentEditingAlertId);
 
             const updatedAlertData = {
               id: currentEditingAlertId,
               level: values.level,
               is_permanent: values.is_permanent,
               Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
               Description: values.description,
               trigger_for: values.trigger_for
             };
 
             setAlertsData(prevData => {
               const updatedData = prevData.map(alert =>
                 alert.id === currentEditingAlertId ? updatedAlertData : alert
               );
               console.log('Updated alerts data (edit mode):', updatedData);
               return updatedData;
             });
           } else {
             // Mode ajout : créer une nouvelle alerte
             console.log('Adding new alert');
 
             const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
             const newAlertData = {
               id: newAlertId,
               level: values.level,
               is_permanent: values.is_permanent,
               Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
               Description: values.description,
               trigger_for: values.trigger_for
             };
 
             setAlertsData(prevData => {
               const updatedData = [...prevData, newAlertData];
               console.log('Updated alerts data (add mode):', updatedData);
               return updatedData;
             });
           }
 
           // Appeler la fonction onSubmit originale si elle existe
           if (onSubmit) {
             onSubmit(values, autoTrigger);
           }
 
           // Fermer le modal et réinitialiser le formulaire
           setIsAlertsAddModalOpen(false);
           setIsSidebarAlert(false);
           setCurrentEditingAlertId(null);
           form.reset();
         };
 
         // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
         const [alertsData, setAlertsData] = useState<AlertData[]>([]);
 
         // Créer les éléments avec les actions pour le rendu
         const elements = alertsData.map(alert => ({
           ...alert,
           Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
           Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
           Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
           Actions: createAlertActions(alert.id)
         }));
     const rows = elements.map((element) => (
         <Table.Tr key={element.id}>
           <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
           <Table.Td>{element.Niveau}</Table.Td>
           <Table.Td>{element.Publique}</Table.Td>
           <Table.Td>{element.Permanente}</Table.Td>
           <Table.Td>{element.Description}</Table.Td>
           <Table.Td w={'100px'}>{element.Actions}</Table.Td>
         </Table.Tr>
       ));
        const form = useForm<AlertFormValues>({
         initialValues: {
           trigger_for: [],
           trigger: '',
           level: 'MINIMUM',
           description: '',
           is_permanent: false,
         },
         validate: {
           trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
           trigger: (value) => (!value ? 'Champ requis' : null),
           description: (value) => (!value ? 'Champ requis' : null),
         },
       });
       const [search, setSearch] = useState('');
     
      
     
       const handleValidate = () => {
         let textToAdd = '';
     
         if (showModels) {
           // Valider les modèles sélectionnés
           const selectedModelTexts = savedModels
             .filter(model => model.selected === true)
             .flatMap(model => model.selections);
           textToAdd = selectedModelTexts.join(', ');
           console.log('Selected models:', savedModels.filter(model => model.selected === true));
           console.log('Text to add from models:', textToAdd);
         } else {
           // Valider les sélections du dictionnaire
           const selectedValues = getSelectedValues();
           textToAdd = selectedValues.join(', ');
         }
     
         if (textToAdd) {
           // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
           setValidSpeech(textToAdd);
           setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"
 
           // 2. Ajouter le texte au champ description du formulaire
           const currentDescription = form.values.description || '';
           const newDescription = currentDescription
             ? `${currentDescription}, ${textToAdd}`
             : textToAdd;
           console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
           form.setFieldValue('description', newDescription);
 
           // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
           // Utiliser la nouvelle description mise à jour
           const combinedText = newDescription;
           console.log('Combined text for alert:', combinedText);
 
           if (currentEditingAlertId) {
             setAlertsData(prevData =>
               prevData.map(alert => {
                 if (alert.id === currentEditingAlertId) {
                   return {
                     ...alert,
                     Description: combinedText
                   };
                 }
                 return alert;
               })
             );
           } else {
             // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
             setAlertsData(prevData =>
               prevData.map(alert => {
                 if (alert.id === '1') {
                   return {
                     ...alert,
                     Description: combinedText
                   };
                 }
                 return alert;
               })
             );
           }
         }
     
 
 
         // Fermer le modal et réinitialiser
         setIsClipboardTextModalOpen(false);
         setIsChoixMultipleModalOpen(false);
         setShowModels(false);
         setSelectedNodes(new Set());
         // Réinitialiser l'ID de l'alerte en cours d'édition
         setCurrentEditingAlertId(null);
       };
     
       const handleCancel = () => {
         // Réinitialiser tous les états
         setSelectedNodes(new Set());
         setShowModels(false);
         setShowAddModel(false);
         setModelTitle('');
         setEditingModelId(null);
         setIsClipboardTextModalOpen(false);
       };
     
       // const handleAddModel = () => {
       //   if (selectedNodes.size > 0) {
       //     setShowAddModel(true);
       //   }
       // };
     
       const handleSaveModel = () => {
         if (modelTitle.trim()) {
           if (editingModelId) {
             // Mode édition : mettre à jour le modèle existant
             setSavedModels(prev => prev.map(model =>
               model.id === editingModelId
                 ? { ...model, title: modelTitle.trim() }
                 : model
             ));
             setEditingModelId(null);
             console.log('Model title updated for ID:', editingModelId);
           } else {
             // Mode création : créer un nouveau modèle
             const selectedValues = getSelectedValues();
             const newModel = {
               id: `model-${Date.now()}`,
               title: modelTitle.trim(),
               selections: selectedValues
             };
             setSavedModels(prev => [...prev, newModel]);
             setSelectedNodes(new Set());
             console.log('New model created:', newModel);
           }
 
           setModelTitle('');
           setShowAddModel(false);
           // Afficher les modèles après sauvegarde
           setShowModels(true);
         }
       };
 
       const handleEditModel = (modelId: string) => {
         const modelToEdit = savedModels.find(model => model.id === modelId);
         if (modelToEdit) {
           setModelTitle(modelToEdit.title);
           setEditingModelId(modelId);
           setShowModels(false);
           setShowAddModel(true);
           console.log('Editing model:', modelToEdit);
         }
       };
     
       const handleDeleteModel = (modelId: string) => {
         setSavedModels(prev => prev.filter(model => model.id !== modelId));
         setSelectedNodes(prev => {
           const newSet = new Set(prev);
           newSet.delete(modelId);
           return newSet;
         });
       };
     
       const exampleData: TreeNodeChoixMultiple[] = [
         {
           uid: '1',
           value: 'Alertes',
           nodes: [
             { uid: '1-1', value: 'Allaitante depuis:' },
             { uid: '1-2', value: 'Allergique à l\'Aspirine' },
     
             { uid: '1-3', value: 'Allergique à la Pénicilline' },
             { uid: '1-4', value: 'Arthrose' },
             { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
             { uid: '1-6', value: 'Diabétique NID' },
             { uid: '1-7', value: 'Enceinte depuis:' },
             { uid: '1-8', value: 'Diabétique ID' },
             { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
             { uid: '1-10', value: 'Hypertension' },
              { uid: '1-11', value: 'Hypotension' },
             { uid: '1-12', value: 'Thyroïde' },
       
     
           ],
         },
         
       ];
     //end header
 // Interface et données pour l'arbre de la sidebar
 interface TreeNode {
   value: string;
   children?: TreeNode[];
 }
 
 const mockTree: TreeNode[] = [
   {
     value: 'Alertes',
     children: [
       { value: "Allaitante depuis:" },
       { value: "Allergique à l'Aspirine" },
       { value: "Allergique à la Pénicilline" },
       { value: "Arthrose" },
       { value: "Cardiaque Anticoagulant sintrom" },
       { value: "Cardiaque prothèse valvulaire" },
       { value: "Cardiaque trouble du rythme" },
       { value: "Diabétique ID" },
       { value: "Diabétique NID" },
       { value: "Enceinte depuis:" },
       { value: "Gastralgie : ulcère anti-inflammatoire" },
       { value: "Hypertension" },
       { value: "Hypotension" },
       { value: "Thyroïde" },
     ],
   },
 ];
 
 // Composant Tree pour la sidebar
 function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
   // Initialiser tous les nœuds comme ouverts
   const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
     const initialExpanded: Record<string, boolean> = {};
     const expandAllNodes = (nodeList: TreeNode[]) => {
       nodeList.forEach(node => {
         if (node.children && node.children.length > 0) {
           initialExpanded[node.value] = true;
           expandAllNodes(node.children);
         }
       });
     };
     expandAllNodes(nodes);
     console.log('Tree initialized with expanded nodes:', initialExpanded);
     return initialExpanded;
   });
 
   return (
     <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
       {nodes.map((node, idx) => {
         const hasChildren = node.children && node.children.length > 0;
         const isOpen = expanded[node.value] || false;
         return (
           <li key={node.value + idx}>
             <Group gap="xs" align="center" onClick={() => {
               // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
               if (hasChildren && !isOpen) {
                 console.log('Opening node:', node.value);
                 setExpanded(prev => ({ ...prev, [node.value]: true }));
               } else if (hasChildren && isOpen) {
                 console.log('Node already open, not closing:', node.value);
               }
             }} className="Alertesslidbar">
               {hasChildren ? (
                 <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
               ) : null}
               <Text
                 onClick={() => !hasChildren && onSelect(node.value)}
                 style={{ cursor: 'pointer' ,paddingLeft:'10px'
                 }}
               >
                 {node.value}
               </Text>
             </Group>
             {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
           </li>
         );
       })}
     </ul>
   );
 }
 
 // Fonctions pour gérer la sidebar
 const handleSidebarSelect = (value: string) => {
   console.log('Selected from sidebar:', value);
 
   // 1. Ajouter la sélection au formulaire en cours d'édition
   const currentDescription = form.values.description || '';
   const newFormDescription = currentDescription
     ? `${currentDescription}, ${value}`
     : value;
   form.setFieldValue('description', newFormDescription);
 
   // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
   if (currentEditingAlertId) {
     setAlertsData(prevData =>
       prevData.map(alert => {
         if (alert.id === currentEditingAlertId) {
           const currentAlertDescription = alert.Description || '';
           const newAlertDescription = currentAlertDescription
             ? `${currentAlertDescription}, ${value}`
             : value;
           return {
             ...alert,
             Description: newAlertDescription
           };
         }
         return alert;
       })
     );
   }
 
   console.log('Added to form description:', newFormDescription);
 
   // Optionnel : fermer la sidebar après sélection
   // setIsSidebarVisible(false);
 };
 
 const handleCloseSidebar = () => {
   setIsSidebarAlert(false);
 };
   
     const FicheForm = useForm({
     initialValues: {
           file_number:1,
           category: '',
           pricing: 0,
           is_bookmarked: false,
           insured: true, // Set to true to match defaultChecked
          description:'',
          titre: '',
     },
     validate: {
       pricing: (value) => (value < 0 ? 'Last name must be at least 2 characters' : null),
     },
   });
 
   // Effect to notify parent component when insured status changes
   useEffect(() => {
     if (onInsuredChange) {
       onInsuredChange(FicheForm.values.insured);
     }
   }, [FicheForm.values.insured, onInsuredChange]);
 
   // Effect to set initial insured state when component mounts
   useEffect(() => {
     if (onInsuredChange && FicheForm.values.insured !== undefined) {
       onInsuredChange(FicheForm.values.insured);
     }
   }, [onInsuredChange]); // Only run on mount
  
// End Alert 
    return (
      <>
    <div className="header-nav-base mb-2 p-1">
           <div className="flex justify-between">
             <div className="flex gap-4">
                <div className="flex justify-start">
                                   {/* liste d'attente */}
                <ThemeIcon className="cursor-pointer">
                  <Icon path={mdiFileTree} size={1}  size={30}  className="bg-[#E6E9EC] text-[#3799CE] " onClick={(event) => {
                        event.preventDefault();
                        toggleSidebar(); // Toggle sidebar visibility
                      }}/> 
                </ThemeIcon>
                <Avatar size={26}  radius={30} variant="filled" color="cyan" className="border-2 rounded-full border-[#fff] mr-[2px] ">
                  <Text fz="sm" fw={500} c={"white"}>
                  {waitingList?.length || 0}
                  </Text>
                  </Avatar>
                <ThemeIcon className="cursor-pointer">
                <IconTextPlus stroke={2} size={30} className="bg-[#E6E9EC] text-[#3799CE] " onClick={() => {
                    AddwaitingList();
                    resetForm()
                  }}
                                  />
                                  </ThemeIcon>
                                  </div>
               <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize ">
                 <IconCalendarClock stroke={1.5} className="mr-1 h-3.5 w-3.5" />
                 {formattedDate}
               </h3>
               <span className="-mx-6 p-1">|</span>
               <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
                 {" "}
                 {/* Aujour&apos;hui   */}
               </h3>
               <Modal
            opened={opened}
            onClose={close}
            size="auto"
             title={`Sélectionnez l'heure et la durée du déjeuner :`}
          >
           <div className="time-selector p-4 border rounded mb-4">
           <div className="grid gap-2">
                <div className="flex gap-2 mb-2">
            {/* Sélection de l'heure */}
                  <Select
              id="lunchHour"
              label="Heure:"
              placeholder="Heure:"
             onChange={(value) => setSelectedHour(Number(value))}
              value={selectedHour === null ? "" : selectedHour.toString()}
              data={hours.map(hour => ({ value: hour.toString(), label: `${hour}h` }))}
            />
            {/* Sélection des minutes */}
        <Select
          id="lunchMinute"
          label="Minutes:"
          placeholder="Minutes:"
          onChange={(value) => setSelectedMinute(Number(value))}
          value={selectedMinute === null ? "" : selectedMinute.toString()}
          data={minutes.map(minute => ({
           value: minute.toString(),
           label: minute === 0 ? "00" : minute.toString(),
          }))}
          disabled={selectedHour === null}
        />
        {/* Sélection de la durée */}
        <Select
          id="lunchDuration"
          label="Durée:"
          placeholder="Durée:"
          onChange={(value) => setDuration(Number(value))}
          value={duration === null ? "" : duration.toString()}
          data={durations.map(dur => ({
            value: dur.toString(),
            label: dur >= 60
              ? `${Math.floor(dur / 60)}h${dur % 60 === 0 ? '' : dur % 60 + 'min'}`
              : `${dur}min`
          }))}
          disabled={selectedHour === null || selectedMinute === null}
        />
     
        <Select
  value={eventResourceId ? eventResourceId.toString() : ""}
  onChange={(value) => {
    console.log("Setting resourceId to:", value);
    setEventResourceId(Number(value) || 1);
  }}
  name="resourceId"
  placeholder="Room"
  data={[
    { value: "1", label: "Room A" },
    { value: "2", label: "Room B" },
  ]}
  required
  className="select w-full max-w-xs mt-6"
  leftSection={<Icon path={mdiBedQueenOutline} size={1} />}
/>
<Avatar color="#4BA3D3" radius="sm"  ml={4} h={36} onClick={openedColorPickerlunch} mt={22}>
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
 style={{width: "26px",height:"26px"}}
 >
   <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path><path fill="#FF9259" d="M49.982 13.408a99.999 99.999 0 00-36.595 36.61l51.968 29.99a40 40 0 0114.638-14.645l-30.01-51.955z"></path><path fill="#FFD23B" d="M13.386 50.02A100 100 0 000 100.025l60-.014a40 40 0 015.354-20.002L13.386 50.021z"></path><path fill="#89C247" d="M0 100a99.999 99.999 0 0013.398 50l51.961-30A40.001 40.001 0 0160 100H0z"></path><path fill="#49B296" d="M13.39 149.989a100.001 100.001 0 0036.599 36.607l30.006-51.958a39.99 39.99 0 01-14.639-14.643l-51.965 29.994z"></path><path fill="#2897B1" d="M49.989 186.596A99.995 99.995 0 0099.987 200l.008-60a39.996 39.996 0 01-20-5.362l-30.007 51.958z"></path><path fill="#3EC3FF" d="M100 200c17.554 0 34.798-4.621 50-13.397l-30-51.962A40 40 0 01100 140v60z"></path><path fill="#09A1E5" d="M150.003 186.601a100.001 100.001 0 0036.601-36.604l-51.962-29.998a40 40 0 01-14.641 14.641l30.002 51.961z"></path><path fill="#077CCC" d="M186.607 149.992A99.993 99.993 0 00200 99.99l-60 .006a39.998 39.998 0 01-5.357 20.001l51.964 29.995z"></path><path fill="#622876" d="M200 100c0-17.554-4.621-34.798-13.397-50l-51.962 30A39.997 39.997 0 01140 100h60z"></path><path fill="#962B7C" d="M186.597 49.99a99.994 99.994 0 00-36.606-36.598l-29.995 51.965a40 40 0 0114.643 14.64l51.958-30.006z"></path><path fill="#CB2E81" d="M149.976 13.384A99.999 99.999 0 0099.973 0l.016 60a40.001 40.001 0 0120.002 5.353l29.985-51.97z"></path></svg>
 </Avatar>
 <Modal opened={lunchColorPickeropened} onClose={closeColorPickerlunch}  size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
         <ColorPicker  defaultValue="#FFFFFF"
      onChangeEnd={setChangeEndValuelunch} format="hex" swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} />
      <Group justify="center" mt={8}>
        {/* <Text > <b>{changeEndValue}</b> </Text> */}
        <Button variant="filled" w={"100%"} color={`${changeEndValuelunch}`} leftSection={<IconColorPicker stroke={1} size={18} />} onClick={() => { closeColorPickerlunch()}}>Registre</Button>
      </Group>
</Modal>
          </div>
          <Button
            className="w-full hover:bg-[#3799CE]/90"
            onClick={() => {
              close();
            }}
          >
            Annuler
          </Button>
          <Button
            className={selectedHour === null || selectedMinute === null ? "w-full disabled":"w-full hover:bg-[#3799CE]/90"}
            onClick={() => {
            addLunchEvent();
              close();
            }}
          >
              Confirmer
          </Button>
        </div>
        </div>
           </Modal>
           <Button
            size="xs"
            className="HoverButton"
            rightSection={<IconChevronDown stroke={2} />}
            onClick={open}
          >
            La durée du déjeuner
          </Button>
               <div>
              </div>
             </div>
             <div className="flex gap-4">
                                 <Button
                                size="xs"
                                className="HoverButton"
                                rightSection={<IconChevronDown stroke={2} />}
                                onClick={openedStartofwork}
                              >
                               Début des travaux
                              </Button>

                                 <ToolbarCalendarNav/>

                                 {/* Refresh button for appointments */}
                                 <Button
                                   variant="light"
                                   size="xs"
                                   onClick={fetchTodayAppointments}
                                   loading={loadingAppointments}
                                   leftSection={<IconRefresh size={14} />}
                                   color="#15aabf"
                                 >
                                   Refresh
                                 </Button>
                                 </div>

                                 {/* Error message for appointments */}
                                 {appointmentError && (
                                   <Alert color="red" title="Error" withCloseButton onClose={() => setAppointmentError(null)}>
                                     {appointmentError}
                                   </Alert>
                                 )}

                                 <Modal
                                opened={StartofworkOpened}
                                onClose={closeStartofwork}
                                withCloseButton={false}
                                size="auto"
                                 title={`Vous pouvez spécifier le début et la fin des travaux.`}
                              >
                               <div className="time-selector p-4 border rounded mb-4">
                               <div className="grid gap-2">
                                    <div className="flex gap-2 mb-2">
                                 <div className="grid gap-2">
                                          <div className="flex gap-2">
                                            <NumberInput
                                              label="Heure de début des travaux"
                                              placeholder="Select hour"
                                                  clampBehavior="strict"
                                                  step={1}
                                        min={0}
                                        max={23}
                                        value={minHour}
                                        onChange={(value) => setMinHour(value as number)}
                                            />
                                            <NumberInput
                                              label="Minute de début"
                                              placeholder="Select minute"
                                              clampBehavior="strict"
                                              step={5}
                                              min={0}
                                              max={59}
                                              value={minMinute}
                                              onChange={(value) => setMinMinute(value as number)}
                                            />
                                          </div>
                                        </div>
                                          <div className="grid gap-2">
                                  <div className="flex gap-2">
                                    <NumberInput
                                      label="Fin de l'heure de travail"
                                      placeholder="Select hour"
                                      clampBehavior="strict"
                                      step={1}
                                      min={0}
                                      max={23}
                                      value={maxHour}
                                      onChange={(value) => setMaxHour(value as number)}
                                      className="w-[50%]"
                                    />
                        <Select
                        label="Max Minute :"
                        value={step.toString()} // Ensure value is a string
                        onChange={(value) => {
                          if (value) {
                            setStep(parseInt(value, 10)); // Convert string to number
                          }
                        }}
                        placeholder="Select a step"
                        data={[
                          // { value: "5", label: "5" },
                          // { value: "10", label: "10" },
                          { value: "15", label: "15" },
                          { value: "20", label: "20" },
                          { value: "25", label: "25" },
                          { value: "30", label: "30" },
                          { value: "45", label: "45" },
                        ]}
                        className="w-[50%]"
                        defaultValue="15" // Set default to 15 minutes
                      />
                                  </div>

                                </div>
                              </div>
                              <Button
                                className="w-full hover:bg-[#3799CE]/90"
                                onClick={() => {
                                  closeStartofwork();
                                }}
                              >
                                Annuler
                              </Button>
                              <Button
                                className="w-full hover:bg-[#3799CE]/90"
                                onClick={() => {
                                  handleSave();
                                closeStartofwork();
                                }}
                              >
                                  Confirmer
                              </Button>
                            </div>
                            </div>
                               </Modal>
                               </div>
                             </div>
            {/* Custom Header */}
                      <Modal
                        opened={thirdModalOpened}
                        onClose={closeThirdModal}
                        title="Paramètre"
                      >
                        <div className="grid gap-2">
                          <div className="flex gap-2">
                            <NumberInput
                              label="Min Hour:"
                              placeholder="Select hour"
                              clampBehavior="strict"
                              step={1}
                              min={0}
                              max={23}
                              value={minHour}
                              onChange={(value) => setMinHour(value as number)}
                            />
                            <NumberInput
                              label="Min Minute:"
                              placeholder="Select minute"
                              clampBehavior="strict"
                              step={5}
                              min={0}
                              max={59}
                              value={minMinute}
                              onChange={(value) => setMinMinute(value as number)}
                            />
                          </div>
                          <Button
                            className="w-full hover:bg-[#3799CE]/90"
                            onClick={() => {
                              handleSave();
                              closeThirdModal();
                            }}
                          >
                            Save
                          </Button>
                        </div>
                      </Modal>
                      <Modal
                        opened={fourthModalOpened}
                        onClose={closeFourthModal}
                        title="Paramètre"
                      >
                        <div className="grid gap-2">
                          <div className="flex gap-2">
                            <NumberInput
                              label="Max Hour:"
                              placeholder="Select hour"
                              clampBehavior="strict"
                              step={1}
                              min={0}
                              max={23}
                              value={maxHour}
                              onChange={(value) => setMaxHour(value as number)}
                              className="w-[50%]"
                            />
                            <Select
                              label="Max Minute :"
                              value={step.toString()} // Ensure value is a string
                              onChange={(value) => {
                                if (value) {
                                  setStep(parseInt(value, 10)); // Convert string to number
                                }
                              }}
                              placeholder="Select a step"
                              data={[
                                { value: "5", label: "5" },
                                { value: "10", label: "10" },
                                { value: "15", label: "15" },
                                { value: "20", label: "20" },
                                { value: "25", label: "25" },
                                { value: "30", label: "30" },
                                { value: "45", label: "45" },
                              ]}
                              className="w-[50%]"
                            />
                          </div>
                          <Button
                            className="w-full hover:bg-[#3799CE]/90"
                            onClick={() => {
                              handleSave();
                              closeFourthModal();
                            }}
                          >
                            Save
                          </Button>
                        </div>
                      </Modal>
                      {/* <Header /> */}
            {/* <Header /> */}
           <div className='flex'>
            <div className={isSidebarAlert ? "w-[80%]" : "w-full"}>
            <SimpleBar className="simplebar-scrollable-y pb-6">
            <CalendarHeader
          label={moment(date).format("DD MMMM YYYY")}
          date={date}
          onNavigate={handleNavigate}
          // onAddToCalendar={handleAddToCalendar}
          onAddToCalendar={handleAddToCalendarAdapter}
        />
        <DragDropContext
        onDragEnd={handleDragEnd}
        >
            <div className="flex gap-4 mt-[5px]">
              <div className="bg-card text-card-foreground border-base-200 w-2/3 rounded-lg border shadow-sm ">
              <Group>
               {isSidebarVisible && (
        <div  style={{padding:"5px", width:226 , height: 600, borderRight: "1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4))"}}>
          {waitingList.length === 0 ? (
             <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              <Text pr={8}>Loading events</Text>
              <Loader color="white" size="xs" type="dots" pt={6}/>
            </Button>
            </div>
          ) : (
            <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                leftSection={waitingList?.length || 0} // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              Nombre de patients
            </Button>
          </div>
          )}
           <div className={waitingList.length <=3 ?" w-[226px] z-40" : "h-[544px] w-[220px] "}>
           <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] z-10">
           <Droppable droppableId="waitingList" direction="vertical">
{(provided) => (


  <div {...provided.droppableProps} ref={provided.innerRef}className="pr-3 " >
  {/*
   */}
  {waitingList.map((patient, index) => (
              <Draggable
                key={patient.id}
                draggableId={patient.id}
                index={index}
              >
                {(provided) => (

                  <Box
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}

                  >
             <Paper  className={`${
                 patient.eventType === "visit"
                 ? "border-l-4 border-l-[#34D1BF]"
                 : patient.eventType === "visitor-counter"
                 ? "border-l-4 border-l-[#F17105]"
                 : patient.eventType === "completed"
                 ? "border-l-4 border-l-[#3799CE]"
                 : patient.eventType === "diagnosis"
                 ? "border-l-4 border-l-[#ED0423]"
                 : "text-[var(--mantine-color-dark-0)]"
             }`}

               mb="sm"
                    p="4px"
                    withBorder >
                      <Group justify="space-between" mb="0px" >
                      <Group >
                      <Badge
                          size="sm"
                          variant="filled"
                          style={{
                            pointerEvents: "none",
                            padding: 0,
                            width: rem(20),
                            height: rem(20),
                            backgroundColor:`#3799CE`
                          }}

                        >
                           {index + 1}
                        </Badge>
                        <Text fz="xs" fw={700} className="uppercase" ml={-10}>
                        {patient.first_name.slice(0, 8)}&nbsp;{patient.last_name.slice(0, 3)}..
                        </Text>
                        </Group>

                          <Indicator
                          position="middle-end"
                              inline
                              size={12}
                              offset={0}
                              color="#3799CE"
                              mt={0}
                              withBorder
                              mr={4}
                              ml={10}
                            />
                        {/* <CloseButton mr={-9} mt={0} /> */}
                         <Menu
                        shadow="md"
                        position="left-start"
                        width={200}
                        transitionProps={{
                          transition: "rotate-right",
                          duration: 150,
                        }}
                      >
                        <Menu.Target>
                          <ActionIcon variant="subtle" aria-label="Menu options">
                            <IconDots size={18} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item
                            leftSection={
                              <IconFilePencil stroke={2} size={14}  className="mt-1" color="#3799CE" />
                            }

                           onClick={() => openEditPatient(patient)}
                          >
                            Modifier
                          </Menu.Item>
                          <Menu.Item
                                leftSection={
                                 <IconEdit
                                   size={14}
                                   className="mt-1"
                                   color="#3799CE"
                                 />

                               }
                               onClick={() => {
                                 setViewPatient(patient);
                                 setInfoModalOpen(true);
                               }}
                               >
                                 Détails
                               </Menu.Item>
                          <Menu.Item
                            leftSection={
                              <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                            }
                             onClick={() => setWaitingList(waitingList.filter(p => p.id !== patient.id))}
                                 color="red"
                          >
                            Supprimer
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                      </Group>
                      <Box className="flex" w={100}>
                        <List
                         spacing="2px"
                         size="xs"
                         center
                         style={{width:"100px"}}

                       >
                         <List.Item  icon={<span  color={patient.color}><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg></span>}><Text size="xs" c="dimmed" truncate="end">{patient.docteur}</Text> </List.Item>
                         <List.Item icon={<Icon path={mdiClipboardEditOutline} size={1}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.typeConsultation.slice(0, 8)}</Text></List.Item>
                         {/* <List.Item  icon={  <MdOutlineUpdate size={12} color={patient.color} />}> <Text size="xs" c="dimmed">{moment(patient.date).format("DD/MM/YYYY")} </Text></List.Item> */}

                       </List>
                       <List
                         spacing="2px"
                         size="xs"
                         center
                         style={{marginLeft:"4px",width:"100px",paddingLeft:"6px"}}
                         className={`${
                               patient.eventType === "visit"
                             ? "border-l-4 border-l-[#34D1BF]"
                             : patient.eventType === "visitor-counter"
                             ? "border-l-4 border-l-[#F17105]"
                             : patient.eventType === "completed"
                             ? "border-l-4 border-l-[#3799CE]"
                             : patient.eventType === "diagnosis"
                             ? "border-l-4 border-l-[#ED0423]"
                             : "text-[var(--mantine-color-dark-0)]"
                         }`}
                       >

                         <List.Item  icon={<Icon path={mdiMapMarkerPath} size={1}   color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end" >{patient.address.slice(0,9)}..</Text></List.Item>
                         <List.Item  icon={ <IconMessageShare stroke={2}  size={10} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.notes}...</Text></List.Item>
                       </List>
                       </Box>
                       </Paper>
                  </Box>
                )}
              </Draggable>
            ))}
            {/*
       </div>       */}
{provided.placeholder}
  </div>


)}
          </Droppable>

          </SimpleBar>
          </div>
   </div>
   )}
   <div className={isSidebarVisible ?  "h-[600px] w-[736px] -ml-4 ": "h-[600px]  w-full "}>
  <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">

  <div className="pr-3"
  //f={calendarRef}
  style={{ flex: 1 }}
  onDragOver={(e) => e.preventDefault()}//Drop={handleDrop}
  >

     <Droppable droppableId="calendar">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                    >

                        <DragAndDropCalendar
                        localizer={localizer}
                        events={memoizedAppointments}
                        // startAccessor={(event) => (event as Appointment).start}
                        // endAccessor={(event) => (event as Appointment).end}
                         startAccessor="start"
                          endAccessor="end"
                        messages={messages}
                        onSelectSlot={handleSlotSelect}
                        selectable
                        defaultView={Views.DAY}
                        views={[Views.DAY]}
                        onEventDrop={onEventDrop}
                        eventPropGetter={eventStyleGetter}
                        dayLayoutAlgorithm="no-overlap"
                        slotPropGetter={() => ({
                          style: {
                            maxHeight: '26.5px', // Adjust this value as needed for better display
                            height: "26px !important",
                          },
                        })}
                        //titleAccessor={(Appointment: Appointment) => `${Appointment.first_name} ${Appointment.last_name}`}
                        components={{
                          event: EventComponent,
                          toolbar: () => null,
                          timeSlotWrapper: TimeSlotWrapper,
                        }}


                        defaultDate={new Date()}
                        step={step}
                        min={minTime}
                        max={maxTime}
                        timeslots={1}
                        resources={resources}
                        resourceIdAccessor={(resource: object) => (resource as Resource).id}
                    // resourceIdAccessor="id"
                         resourceTitleAccessor={(resource: object) => (resource as Resource).title}
                        //resourceTitleAccessor="title"
                        resizable

                      />


                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
  </div>
  </SimpleBar>
  </div>
  </Group>

  <div className="border-base-200 rounded-b-lg border-t bg-[--content-background] px-[24px] py-[20px] mb-[30px]">
 
    <ul className="flex flex-wrap gap-x-4 gap-y-2 text-[var(--mantine-color-dark-0)]">
      {[
        { label: "Visite de malade", color: "disk-teal", value: "Visite de malade", eventType: "visit" },
        { label: "Visitor Counter", color: "disk-orange", value: "Visitor Counter", eventType: "visitor-counter" },
        { label: "Completed", color: "disk-azure", value: "Completed", eventType: "completed" },
        { label: "Re-diagnose", color: "disk-red", value: "Re-diagnose", eventType: "diagnosis" },
      ].map(({ label, color, value, eventType }) => (
        <li
          key={label}
          className={`flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80 ${
            type === value ? 'font-bold' : ''
          }`}
          onClick={() => {
            setType(value);
            setEventType(eventType as EventType);
          }}
        >
          <span className={`${color} relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white`} />
          {label}
        </li>
      ))}
    </ul>
  </div>
         </div>
      <div className="card border-base-100 bg-base-100 w-1/3 pb-2 absolute top-0 right-0 ">
      <div>
      <Tabs variant="none" value={value} onChange={setValue}>
      <Tabs.List ref={setRootRef} className={classes.list}>
      <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] flex w-full justify-between items-center">
      <span className="text-sm font-medium capitalize flex gap-2">
        <IconStethoscope stroke={1.25} />
        Visites Actives
      </span>

      <div className="flex gap-2">
      <Tabs.Tab
    value="1"
    ref={setControlRef('1')}
    styles={{
      tab: {
        zIndex: 1,
        fontWeight: 500,
        transition: 'all 100ms ease',
        color: '#ffffff',
        padding: '8px 12px',
        '&[dataActive="true"]': {
          color: '#3799CE',
          backgroundColor: 'white',
          borderRadius: '4px',
          borderBottom: '2px solid #3799CE',
        },
      },
    }}
  >
    Room A
  </Tabs.Tab>
        <Tabs.Tab
          value="2"
          ref={setControlRef('2')}
          styles={{
            tab: {
              zIndex: 1,
              fontWeight: 500,
              transition: 'all 100ms ease',
              color: '#ffffff',
              padding: '8px 12px',
              '&[dataActive="true"]': {
                color: '#3799CE',
                backgroundColor: 'white',
                borderRadius: '4px',
                borderBottom: '2px solid #3799CE',
              },
            },
          }}
        >
          Room B
        </Tabs.Tab>
      </div>
  </div>

  <FloatingIndicator
    target={value ? controlsRefs[value] : null}
    parent={rootRef}
    styles={{
      root: {
        color: 'red',
        backgroundColor: '#15AABF',
        borderRadius: 'var(--mantine-radius-md)',
        borderBottom: '2px solid #3799CE',
        boxShadow: 'var(--mantine-shadow-lg)',
      },
    }}
  />
        </Tabs.List>
        <Tabs.Panel value="1">
        <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0  ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => visit.resourceId === 1).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          event.resourceId === 1 &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
          </Group>
        </Tooltip>
        <Group>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button

          leftSection={
            activeVisits
            .filter(event => event.resourceId === 1)
            .filter(event => activeIds.includes(Number(event.id)))
            .length
          }
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                Historique journalier
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 1).length <= 5 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >

   <Menu.Divider />
            {activeVisits.length > 0 ? (
              // activeVisits.map((event, index) => {
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {

                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
   <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
        </Group>
      </div>
           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?" max-w-[570px] " : "h-[180px] max-w-[570px] "}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
             activeVisits.filter(visit => visit.resourceId === 1).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={visit.id.toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                      <Card
                        className={`my-3 ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs">
                          <Flex align="center" justify="space-between">
                            <Group gap="8px">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>
                             
                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              {/* Display current time or duration */}
                              {activeEvent_Ids.has(Number(visit.id)) && (
                                <Text size="xs" c="green" fw={600}>
                                  {currentTimes.get(Number(visit.id)) || new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                              )}
                              {eventDurations.has(Number(visit.id)) && (
                                <Text size="xs" c="blue" fw={600}>
                                  {eventDurations.get(Number(visit.id))}min
                                </Text>
                              )}
                               <Text tt="capitalize">{visit.last_name.slice(0, 6)} &nbsp;{visit.first_name.slice(0, 8)} </Text>
                            </Group>
                            {/* Right Section */}
                            <Group >
                           
                              <Group>
                                <Indicator
                                  disabled={!activeEvent_Ids.has(Number(visit.id))}
                                  processing
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="red"
                                  mt={2}
                                  withBorder
                                />
                                <Indicator
                                  disabled={activeEvent_Ids.has(Number(visit.id))}
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="#3799CE"
                                  mt={2}
                                  withBorder
                                />
                              </Group>
                                 <Switch
                                checked={activeEvent_Ids.has(Number(visit.id))}
                                onChange={(e) => {
                                  handleTimeTracking(Number(visit.id), e.currentTarget.checked);
                                }}
                                color="teal"
                                size="sm"
                                thumbIcon={
                                  activeEvent_Ids.has(Number(visit.id)) ? (
                                    <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                                  ) : (
                                    <IconX size={12} color="var(--mantine-color-red-6)" stroke={3} />
                                  )
                                }
                              />
                              
                            </Group>
                            <Group gap="2px">
                               <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                             
                             
                              <Icon path={mdiTooth} size={1} color={"#d50000"} />
                              
                            <Menu
                              shadow="md"
                              position="left-start"
                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2} 
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                 
                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiFileAccount} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => openedFichepatient()}
                                >
                                  Fiche patient
                                </Menu.Item>
                                 {/* <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                   Nouveau rendez-vous 
                                 
                                </Menu.Item> */}
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountBoxEditOutline} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                   onClick={() => {handleEditClick(visit)}}
                                >
                                  
                                  Modifier rendez-vous
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPen} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Changer la salle de consultation
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Démarrer / Afficher la visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Prescription
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiNeedle} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Comptes rendus
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPower} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Terminer la visite
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                 Revenir à la salle d&apos;attente
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                    Ajouter rendez-vous
                  </Button>
                </div>
       </Container>
          <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] ml-6" >
          <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
          {/* Salle D&apos;attente */}
                 Salle de présence

          </span>
          </span>
          </div>
          <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {waitingRoomVisits.length === 0 ? (
          <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-end">
            <Group>
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button

              leftSection={
                waitingRoomVisits.length
              }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    Historique journalier
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                          
                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
            <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
           </Menu>
           </Group>
          </div>

           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {waitingRoomVisits.length > 0 ? (
            <div className="space-y-2 max-w-[570px]">
              {waitingRoomVisits.map((visit, index) => (
              <Draggable key={visit.id} draggableId={visit.id.toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>

                      <Card  key={visit.id}
                    className={`my-3 ${classes.card} ${

                      visit.type === "visit"
                        ? "border-l-4 border-l-[#34D1BF]"
                        : visit.type === "visitor-counter"
                          ? "border-l-4 border-l-[#F17105]"
                          : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                    }`}
                    shadow="sm"
                    radius="md"
                    style={{
                      cursor: "grab",
                      borderLeftWidth: "2px",
                    }}
                    mt={3}
                  >
                    <Card.Section p="sm">
                      <Group gap="md" justify="space-between">
                        <Text size="xs"  component="span">
                          <Group gap="5px">
                          <Badge
                            size="sm"
                            variant="filled"
                            style={{
                              pointerEvents: "none",
                              padding: 0,
                              width: rem(20),
                              height: rem(20),
                            }}
                          >
                            {/* {event.id}  */}
                            {/* {counterId} */}
                            {index + 1}
                           
                          </Badge>
                            <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                            <Text  size="xs" fw={700} >
                            {visit.last_name.slice(0, 8)}&nbsp;
                            {visit.first_name.slice(0, 8)}

                            </Text>
                            {/* <Text size="sm" c="dimmed">{visit.typeConsultation}</Text> */}
                          </Group>
                        </Text>
                        
                          <Group gap="5px">
                             <IconAlarm stroke={2}  size={12} strokeWidth={1.5} color="#868e96"/>
                              <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTime(visit.start)}</Text>
                              <svg stroke="currentColor" fill="#3799ce" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 6h2v12H4zm10 5H8v2h6v5l6-6-6-6z"></path></svg>
                              <Text c="dimmed" size="xs">{formatTime(visit.end)}</Text>
                              </Group>
                          </Group>
                          <Group gap="5px">
                            <IconPhoneCall stroke={1.5} size={12} strokeWidth={1.5} color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                          <Group gap={"4px"}>
                            <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                           <Avatar bg="#3799ce" radius="sm" h={22} pt={1}>
                             <Text fw={600} c={"white"}>SLT</Text>
                           </Avatar>
                              <Icon path={mdiAccountArrowRight} size={1} color={"#3799ce"}/>
                              <span onClick={() => moveToActiveVisits(visit)}>
                               
                              <Icon
                                path={mdiArrowUp}
                                size={1}
                                color={"#3799ce"}
                                style={{ cursor: 'pointer' }}
                                
                              />
                              </span>
                               <Menu
                               withArrow position="left"

                              shadow="md"
                              
                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2} 
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                 
                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiPencilBoxOutline}  size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiReply} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Fiche patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocumentCheck} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                 <Menu.Divider />
                                 
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Régler un plan de soins
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Recettes
                                </Menu.Item>
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Sortie du patient
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          {/* <span onClick={openedVisitesActivesDelete} className="cursor-pointer">
                          <Icon path={mdiDeleteClockOutline} size={1}    className="Icon_Delete cursor-pointer" />
                         </span> */}
                      
                    
                      </Group>
                    </Card.Section>
                  </Card>
                   {/* ))}
                 </div>
               )}
             </div> */}
             
                
                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the event's actual ID, not counterId
                  const eventId = Number(visit.id);
                  if (!isNaN(eventId)) {
                    deleteVisits(eventId);
                    closeVisitesActivesDelete();
                  } else {
                    console.error("Invalid event ID:", visit.id);
                    notifications.show({
                      title: 'Error',
                      message: 'Could not delete event: Invalid ID',
                      color: 'red',
                    });
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
              ))}
            </div>
          ) : (
            <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          )}
        </div>
      )}
    </Droppable>
  </DragDropContext>
                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                   Ajouter rendez-vous
                  </Button>
                </div>
           </Container>

          

        </Tabs.Panel>
        <Tabs.Panel value="2">
        <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0  ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => visit.resourceId === 2).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          event.resourceId === 2 &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
          </Group>
        </Tooltip>
        <Group>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button

          leftSection={
            activeVisits
            .filter(event => event.resourceId === 2)
            .filter(event => activeIds.includes(Number(event.id)))
            .length
          }
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                Historique journalier
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 2).length <= 5 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >

   <Menu.Divider />
            {activeVisits.length > 0 ? (
              // activeVisits.map((event, index) => {
              activeVisits.filter(visit => visit.resourceId === 2).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {

                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
   <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
        </Group>
      </div>
           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={activeVisits.filter(visit => visit.resourceId === 2).length <=3 ?" max-w-[570px] " : "h-[180px] max-w-[570px] "}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
             activeVisits.filter(visit => visit.resourceId === 2).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={visit.id.toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                      <Card
                        className={`my-3 ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs">
                          <Flex align="center" justify="space-between">
                            <Group gap="8px">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>
                             
                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              {/* Display current time or duration */}
                              {activeEvent_Ids.has(Number(visit.id)) && (
                                <Text size="xs" c="green" fw={600}>
                                  {currentTimes.get(Number(visit.id)) || new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                              )}
                              {eventDurations.has(Number(visit.id)) && (
                                <Text size="xs" c="blue" fw={600}>
                                  {eventDurations.get(Number(visit.id))}min
                                </Text>
                              )}
                               <Text tt="capitalize">{visit.last_name.slice(0, 6)} &nbsp;{visit.first_name.slice(0, 8)} </Text>
                            </Group>
                            {/* Right Section */}
                            <Group >
                           
                              <Group>
                                <Indicator
                                  disabled={!activeEvent_Ids.has(Number(visit.id))}
                                  processing
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="red"
                                  mt={2}
                                  withBorder
                                />
                                <Indicator
                                  disabled={activeEvent_Ids.has(Number(visit.id))}
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="#3799CE"
                                  mt={2}
                                  withBorder
                                />
                              </Group>
                                 <Switch
                                checked={activeEvent_Ids.has(Number(visit.id))}
                                onChange={(e) => {
                                  handleTimeTracking(Number(visit.id), e.currentTarget.checked);
                                }}
                                color="teal"
                                size="sm"
                                thumbIcon={
                                  activeEvent_Ids.has(Number(visit.id)) ? (
                                    <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                                  ) : (
                                    <IconX size={12} color="var(--mantine-color-red-6)" stroke={3} />
                                  )
                                }
                              />
                              
                            </Group>
                            <Group gap="2px">
                               <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                             
                             
                              <Icon path={mdiTooth} size={1} color={"#d50000"} />
                              
                            <Menu
                              shadow="md"
                              position="left-start"
                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2} 
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                 
                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiFileAccount} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => openedFichepatient()}
                                >
                                  Fiche patient
                                </Menu.Item>
                                 {/* <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                   Nouveau rendez-vous 
                                 
                                </Menu.Item> */}
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountBoxEditOutline} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                   onClick={() => {handleEditClick(visit)}}
                                >
                                  
                                  Modifier rendez-vous
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPen} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Changer la salle de consultation
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Démarrer / Afficher la visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Prescription
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiNeedle} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Comptes rendus
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPower} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Terminer la visite
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                 Revenir à la salle d&apos;attente
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                    Ajouter rendez-vous
                  </Button>
                </div>
       </Container>
          <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] ml-6" >
          <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
          {/* Salle D&apos;attente */}
                 Salle de présence

          </span>
          </span>
          </div>
          <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {waitingRoomVisits.length === 0 ? (
          <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-end">
            <Group>
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button

              leftSection={
                waitingRoomVisits.length
              }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    diagnostiqués 2
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                          
                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
            <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
           </Menu>
           </Group>
          </div>

           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {waitingRoomVisits.length > 0 ? (
            <div className="space-y-2 max-w-[570px]">
              {waitingRoomVisits.map((visit, index) => (
              <Draggable key={visit.id} draggableId={visit.id.toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
 {/* <div style={{ width: "100%", padding: 10, minHeight: "200px" }}>
               {waitingRoomVisits.length === 0 ? (
                 <Text c="dimmed" ta="center" mt="xl">
                   Aucune visite en salle de présence
                 </Text>
               ) : (
                 <div className="space-y-2">
                   {waitingRoomVisits.map((visit) => ( */}
                      <Card  key={visit.id}
                    className={`my-3 ${classes.card} ${

                      visit.type === "visit"
                        ? "border-l-4 border-l-[#34D1BF]"
                        : visit.type === "visitor-counter"
                          ? "border-l-4 border-l-[#F17105]"
                          : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                    }`}
                    shadow="sm"
                    radius="md"
                    style={{
                      cursor: "grab",
                      borderLeftWidth: "2px",
                    }}
                    mt={3}
                  >
                    <Card.Section p="sm">
                      <Group gap="md" justify="space-between">
                        <Text size="xs"  component="span">
                          <Group gap="5px">
                          <Badge
                            size="sm"
                            variant="filled"
                            style={{
                              pointerEvents: "none",
                              padding: 0,
                              width: rem(20),
                              height: rem(20),
                            }}
                          >
                            {/* {event.id}  */}
                            {/* {counterId} */}
                            {index + 1}
                           
                          </Badge>
                            <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                            <Text  size="xs" fw={700} >
                            {visit.last_name.slice(0, 8)}&nbsp;
                            {visit.first_name.slice(0, 8)}

                            </Text>
                            {/* <Text size="sm" c="dimmed">{visit.typeConsultation}</Text> */}
                          </Group>
                        </Text>
                        
                          <Group gap="5px">
                             <AlarmClock size={12} strokeWidth={1.5} color="#868e96"/>
                              <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTime(visit.start)}</Text>
                              <svg stroke="currentColor" fill="#3799ce" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 6h2v12H4zm10 5H8v2h6v5l6-6-6-6z"></path></svg>
                              <Text c="dimmed" size="xs">{formatTime(visit.end)}</Text>
                              </Group>
                          </Group>
                          <Group gap="5px">
                            <IconPhoneCall stroke={1.5} size={12} strokeWidth={1.5} color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                          <Group gap={"4px"}>
                            <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                           <Avatar bg="#3799ce" radius="sm" h={22} pt={1}>
                             <Text fw={600} c={"white"}>SLT</Text>
                           </Avatar>
                              <Icon path={mdiAccountArrowRight} size={1} color={"#3799ce"}/>
                              <span onClick={() => moveToActiveVisits(visit)}>
                               
                              <Icon
                                path={mdiArrowUp}
                                size={1}
                                color={"#3799ce"}
                                style={{ cursor: 'pointer' }}
                                
                              />
                              </span>
                               <Menu
                               withArrow position="left"

                              shadow="md"
                              
                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2} 
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                 
                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiPencilBoxOutline}  size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiReply} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Fiche patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocumentCheck} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                 <Menu.Divider />
                                 
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                  Régler un plan de soins
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Recettes
                                </Menu.Item>
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }
                                 
                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  
                                >
                                 Sortie du patient
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          {/* <span onClick={openedVisitesActivesDelete} className="cursor-pointer">
                          <Icon path={mdiDeleteClockOutline} size={1}    className="Icon_Delete cursor-pointer" />
                         </span> */}
                      
                    
                      </Group>
                    </Card.Section>
                  </Card>
                   {/* ))}
                 </div>
               )}
             </div> */}
             
                
                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the event's actual ID, not counterId
                  const eventId = Number(visit.id);
                  if (!isNaN(eventId)) {
                    deleteVisits(eventId);
                    closeVisitesActivesDelete();
                  } else {
                    console.error("Invalid event ID:", visit.id);
                    notifications.show({
                      title: 'Error',
                      message: 'Could not delete event: Invalid ID',
                      color: 'red',
                    });
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
              ))}
            </div>
          ) : (
            <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          )}
        </div>
      )}
    </Droppable>
  </DragDropContext>
                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                   Ajouter rendez-vous
                  </Button>
                </div>
           </Container>

          

        </Tabs.Panel>
        {/* <Tabs.Panel value="3">Third tab content</Tabs.Panel> */}
      </Tabs>
          </div>
            </div>
          </div>
          </DragDropContext>
            </SimpleBar>
            </div>
             {isSidebarAlert && (
           <Card shadow="sm" mt={'10px'} padding="lg" radius="md" withBorder className={isSidebarAlert ? "w-[20%]" : "w-full"}>
          <Box mb="sm">
            <Group>
              <Input
                placeholder="Rechercher"
                value={search}
                onChange={(e) => setSearch(e.currentTarget.value)}
                w={"70%"}
              />

              <Group justify="flex-end">
                <ActionIcon
                  variant="filled"
                  aria-label="Multiple"
                  color="#3799CE"
                  onClick={() => {
                    // Vous pouvez ajouter ici la logique pour ouvrir le modal de choix multiple
                    console.log('Open multiple choice modal');
                    setIsSidebarAlert(false);
                    setIsChoixMultipleModalOpen(true);
                     
                  }}
                >
                  <Icon path={mdiViewHeadline} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
                <ActionIcon
                  variant="filled"
                  aria-label="Annuler"
                  color="#3799CE"
                  onClick={handleCloseSidebar}
                >
                  <Icon path={mdiArrowRight} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                </ActionIcon>
              </Group>
            </Group>
          </Box>

          <ScrollArea h={400}>
            <Tree
              nodes={mockTree.filter((n) => n.value.toLowerCase().includes(search.toLowerCase()))}
              onSelect={handleSidebarSelect}
            
            />
          </ScrollArea>
           </Card>
              )}
             </div>
             {/* Add Patient Modal */}
             <AjouterUnRendezVous
               opened={rendezVousOpened}
               onClose={closeRendezVous}
                appointmentForm={appointmentForm}
                handleSubmit={handleSubmit}
               eventTitle={eventTitle}
               setEventTitle={setEventTitle}
               // Pass patient selection props
               allPatients={allPatients}
               onPatientSelect={populateFormWithPatient}
               isLoadingPatients={isLoadingPatients}
               titleOptions={titleOptions}
               setTitleOptions={setTitleOptions}
               newTitle={newTitle}
               setNewTitle={setNewTitle}
               patientName={patientName}
               setPatientName={setPatientName}
               patientlastName={patientlastName}
               setPatientlastName={setPatientlastName}
               openListDesPatient={openListDesPatient}
               eventDateDeNaissance={eventDateDeNaissance}
               handleDateChange={handleDateChange}
               eventAge={eventAge}
               genderOption={genderOption}
               handleOptionChange={handleOptionChange}
               eventEtatCivil={eventEtatCivil}
               setEventEtatCivil={setEventEtatCivil}
               eventCin={eventCin}
               setEventCin={setEventCin}
               address={address}
               setAddress={setAddress}
               eventTelephone={eventTelephone}
               setEventTelephone={setEventTelephone}
               email={email}
               setEmail={setEmail}
               patientdoctor={patientdoctor}
               setPatientDocteur={setPatientDocteur}
               patientsocialSecurity={patientsocialSecurity}
               setSocialSecurity={setSocialSecurity}
               consultationTypes={consultationTypes}
               setConsultationTypes={setConsultationTypes}
               patienttypeConsultation={patienttypeConsultation}
               setPatientTypeConsultation={setPatientTypeConsultation}
               setEventType={handleSetEventType}
               searchValue={searchValue}
               setSearchValue={setSearchValue}
               dureeDeLexamen={dureeDeLexamen}
               getEventTypeColor={getEventTypeColor}
               newConsultationType={newConsultationType}
               setNewConsultationType={setNewConsultationType}
               newConsultationColor={newConsultationColor}
               setNewConsultationColor={setNewConsultationColor}
               ColorPickeropened={ColorPickeropened}
               openedColorPicker={openedColorPicker}
               closeColorPicker={closeColorPicker}
               changeEndValue={changeEndValue}
               setChangeEndValue={setChangeEndValue}
               setDureeDeLexamen={setDureeDeLexamen}
               eventAganda={eventAganda}
               setEventAganda={setEventAganda}
               agendaTypes={agendaTypes}
               setAgendaTypes={setAgendaTypes}
               newAgendaType={newAgendaType}
               setNewAgendaType={setNewAgendaType}
               isWaitingList={isWaitingList}
               eventDate={eventDate}
               setEventDate={setEventDate}
               eventTime={eventTime}
               setEventTime={setEventTime}
               eventConsultation={eventConsultation}
               openListRendezVous={openListRendezVous}
               ListRendezVousOpened={ListRendezVousOpened}
               closeListRendezVous={closeListRendezVous}
               patientcomment={patientcomment}
               setPatientcomment={setPatientcomment}
               patientnotes={patientnotes}
               setPatientNotes={setPatientNotes}
               patientcommentairelistedattente={patientcommentairelistedattente}
               setPatientCommentairelistedattente={setPatientCommentairelistedattente}
               eventResourceId={eventResourceId}
               setEventResourceId={setEventResourceId}
               eventType={eventType}
               checkedAppelvideo={checkedAppelvideo}
               handleAppelvideoChange={handleAppelvideoChange}
               checkedRappelSms={checkedRappelSms}
               handleRappelSmsChange={handleRappelSmsChange}
               checkedRappelEmail={checkedRappelEmail}
               handleRappelEmailChange={handleRappelEmailChange}
               currentPatient={currentPatient}
               waitingList={waitingList}
               setWaitingList={setWaitingList}
               setPatientModalOpen={setPatientModalOpen}
               notifications={notifications}
               showEditModal={showEditModal}
               setShowEditModal={setShowEditModal}
               selectedEvent={selectedEvent}
               setSelectedEvent={setSelectedEvent}
               resetForm={resetForm}
               handleEditSubmit={handleEditSubmitWrapper}
               closeRendezVous={closeRendezVous}
               initialConsultationTypes={initialConsultationTypes}
             />
               {/* Add to Waiting List Modal - Now using AjouterUnRendezVous component */}
               <AjouterUnRendezVous
                 opened={AddwaitingListOpened}
                 onClose={closeAddwaitingList}
                 appointmentForm={appointmentForm}
                 handleSubmit={handleAddPatient}
                 eventTitle={eventTitle}
                 setEventTitle={setEventTitle}
                 // Pass patient selection props
                 allPatients={allPatients}
                 onPatientSelect={populateFormWithPatient}
                 isLoadingPatients={isLoadingPatients}
                 titleOptions={titleOptions}
                 setTitleOptions={setTitleOptions}
                 newTitle={newTitle}
                 setNewTitle={setNewTitle}
                 patientName={patientName}
                 setPatientName={setPatientName}
                 patientlastName={patientlastName}
                 setPatientlastName={setPatientlastName}
                 openListDesPatient={openListDesPatient}
                 eventDateDeNaissance={eventDateDeNaissance}
                 handleDateChange={handleDateChange}
                 eventAge={eventAge}
                 genderOption={genderOption}
                 handleOptionChange={handleOptionChange}
                 eventEtatCivil={eventEtatCivil}
                 setEventEtatCivil={setEventEtatCivil}
                 eventCin={eventCin}
                 setEventCin={setEventCin}
                 address={address}
                 setAddress={setAddress}
                 eventTelephone={eventTelephone}
                 setEventTelephone={setEventTelephone}
                 email={email}
                 setEmail={setEmail}
                 patientdoctor={patientdoctor}
                 setPatientDocteur={setPatientDocteur}
                 patientsocialSecurity={patientsocialSecurity}
                 setSocialSecurity={setSocialSecurity}
                 consultationTypes={consultationTypes}
                 setConsultationTypes={setConsultationTypes}
                 patienttypeConsultation={patienttypeConsultation}
                 setPatientTypeConsultation={setPatientTypeConsultation}
                 setEventType={handleSetEventType}
                 searchValue={searchValue}
                 setSearchValue={setSearchValue}
                 dureeDeLexamen={dureeDeLexamen}
                 getEventTypeColor={getEventTypeColor}
                 newConsultationType={newConsultationType}
                 setNewConsultationType={setNewConsultationType}
                 newConsultationColor={newConsultationColor}
                 setNewConsultationColor={setNewConsultationColor}
                 ColorPickeropened={ColorPickeropened}
                 openedColorPicker={openedColorPicker}
                 closeColorPicker={closeColorPicker}
                 changeEndValue={changeEndValue}
                 setChangeEndValue={setChangeEndValue}
                 setDureeDeLexamen={setDureeDeLexamen}
                 eventAganda={eventAganda}
                 setEventAganda={setEventAganda}
                 agendaTypes={agendaTypes}
                 setAgendaTypes={setAgendaTypes}
                 newAgendaType={newAgendaType}
                 setNewAgendaType={setNewAgendaType}
                 isWaitingList={true}
                 eventDate={eventDate}
                 setEventDate={setEventDate}
                 eventTime={eventTime}
                 setEventTime={setEventTime}
                 eventConsultation={eventConsultation}
                 openListRendezVous={openListRendezVous}
                 ListRendezVousOpened={ListRendezVousOpened}
                 closeListRendezVous={closeListRendezVous}
                 patientcomment={patientcomment}
                 setPatientcomment={setPatientcomment}
                 patientnotes={patientnotes}
                 setPatientNotes={setPatientNotes}
                 patientcommentairelistedattente={patientcommentairelistedattente}
                 setPatientCommentairelistedattente={setPatientCommentairelistedattente}
                 eventResourceId={eventResourceId}
                 setEventResourceId={setEventResourceId}
                 eventType={eventType}
                 checkedAppelvideo={checkedAppelvideo}
                 handleAppelvideoChange={handleAppelvideoChange}
                 checkedRappelSms={checkedRappelSms}
                 handleRappelSmsChange={handleRappelSmsChange}
                 checkedRappelEmail={checkedRappelEmail}
                 handleRappelEmailChange={handleRappelEmailChange}
                 currentPatient={currentPatient}
                 waitingList={waitingList}
                 setWaitingList={setWaitingList}
                 setPatientModalOpen={setPatientModalOpen}
                 notifications={notifications}
                 // New props for Edit Modal
                 showEditModal={false}
                 setShowEditModal={() => {}}
                 selectedEvent={null}
                 setSelectedEvent={() => {}}
                 resetForm={resetForm}
                 handleEditSubmit={() => {}}
                 closeRendezVous={closeAddwaitingList}
                 initialConsultationTypes={initialConsultationTypes}
               />


              {/* Edit Waiting List Modal - Now using AjouterUnRendezVous component */}
              <AjouterUnRendezVous
                opened={EditwaitingListOpened}
                onClose={closeEditwaitingList}
                appointmentForm={appointmentForm}
                handleSubmit={handleUpdatePatientWrapper}
                eventTitle={eventTitle}
                setEventTitle={setEventTitle}
                // Pass patient selection props
                allPatients={allPatients}
                onPatientSelect={populateFormWithPatient}
                isLoadingPatients={isLoadingPatients}
                titleOptions={titleOptions}
                setTitleOptions={setTitleOptions}
                newTitle={newTitle}
                setNewTitle={setNewTitle}
                patientName={patientName}
                setPatientName={setPatientName}
                patientlastName={patientlastName}
                setPatientlastName={setPatientlastName}
                openListDesPatient={openListDesPatient}
                eventDateDeNaissance={eventDateDeNaissance}
                handleDateChange={handleDateChange}
                eventAge={eventAge}
                genderOption={genderOption}
                handleOptionChange={handleOptionChange}
                eventEtatCivil={eventEtatCivil}
                setEventEtatCivil={setEventEtatCivil}
                eventCin={eventCin}
                setEventCin={setEventCin}
                address={address}
                setAddress={setAddress}
                eventTelephone={eventTelephone}
                setEventTelephone={setEventTelephone}
                email={email}
                setEmail={setEmail}
                patientdoctor={patientdoctor}
                setPatientDocteur={setPatientDocteur}
                patientsocialSecurity={patientsocialSecurity}
                setSocialSecurity={setSocialSecurity}
                consultationTypes={consultationTypes}
                setConsultationTypes={setConsultationTypes}
                patienttypeConsultation={patienttypeConsultation}
                setPatientTypeConsultation={setPatientTypeConsultation}
                setEventType={handleSetEventType}
                searchValue={searchValue}
                setSearchValue={setSearchValue}
                dureeDeLexamen={dureeDeLexamen}
                getEventTypeColor={getEventTypeColor}
                newConsultationType={newConsultationType}
                setNewConsultationType={setNewConsultationType}
                newConsultationColor={newConsultationColor}
                setNewConsultationColor={setNewConsultationColor}
                ColorPickeropened={ColorPickeropened}
                openedColorPicker={openedColorPicker}
                closeColorPicker={closeColorPicker}
                changeEndValue={changeEndValue}
                setChangeEndValue={setChangeEndValue}
                setDureeDeLexamen={setDureeDeLexamen}
                eventAganda={eventAganda}
                setEventAganda={setEventAganda}
                agendaTypes={agendaTypes}
                setAgendaTypes={setAgendaTypes}
                newAgendaType={newAgendaType}
                setNewAgendaType={setNewAgendaType}
                isWaitingList={false}
                eventDate={eventDate}
                setEventDate={setEventDate}
                eventTime={eventTime}
                setEventTime={setEventTime}
                eventConsultation={eventConsultation}
                openListRendezVous={openListRendezVous}
                ListRendezVousOpened={ListRendezVousOpened}
                closeListRendezVous={closeListRendezVous}
                patientcomment={patientcomment}
                setPatientcomment={setPatientcomment}
                patientnotes={patientnotes}
                setPatientNotes={setPatientNotes}
                patientcommentairelistedattente={patientcommentairelistedattente}
                setPatientCommentairelistedattente={setPatientCommentairelistedattente}
                eventResourceId={eventResourceId}
                setEventResourceId={setEventResourceId}
                eventType={eventType}
                checkedAppelvideo={checkedAppelvideo}
                handleAppelvideoChange={handleAppelvideoChange}
                checkedRappelSms={checkedRappelSms}
                handleRappelSmsChange={handleRappelSmsChange}
                checkedRappelEmail={checkedRappelEmail}
                handleRappelEmailChange={handleRappelEmailChange}
                currentPatient={currentPatient}
                waitingList={waitingList}
                setWaitingList={setWaitingList}
                setPatientModalOpen={setPatientModalOpen}
                notifications={notifications}
                // New props for Edit Modal
                showEditModal={false}
                setShowEditModal={() => {}}
                selectedEvent={null}
                setSelectedEvent={() => {}}
                resetForm={resetForm}
                handleEditSubmit={() => {}}
                closeRendezVous={closeEditwaitingList}
                initialConsultationTypes={initialConsultationTypes}
              />


              {/*   Show Patient List */}
             <Modal.Root opened={ListDesPatientOpened} onClose={closeListDesPatient}   size="100%">
            <PatientList/>
             </Modal.Root>

               
                <InfoModal
                  opened={infoModalOpen}
                  onClose={() => setInfoModalOpen(false)}
                  viewPatient={viewPatient}
                  color={color}
                  eventConsultation={eventConsultation}
                  eventResourceId={eventResourceId}
                />
                      <PatientDetailsModal
                        opened={showViewModal}
                        onClose={() => setShowViewModal(false)}
                        selectedEvent={selectedEvent}
                        eventResourceId={eventResourceId}
                      />
            {/* Edit Patient Modal - Now using AjouterUnRendezVous component */}
            <AjouterUnRendezVous
              opened={showEditModal}
              onClose={() => {
                resetForm();
                setShowEditModal(false);
              }}
              appointmentForm={appointmentForm}
              handleSubmit={handleEditSubmit}
              eventTitle={eventTitle}
              setEventTitle={setEventTitle}
              // Pass patient selection props
              allPatients={allPatients}
              onPatientSelect={populateFormWithPatient}
              isLoadingPatients={isLoadingPatients}
              titleOptions={titleOptions}
              setTitleOptions={setTitleOptions}
              newTitle={newTitle}
              setNewTitle={setNewTitle}
              patientName={patientName}
              setPatientName={setPatientName}
              patientlastName={patientlastName}
              setPatientlastName={setPatientlastName}
              openListDesPatient={openListDesPatient}
              eventDateDeNaissance={eventDateDeNaissance}
              handleDateChange={handleDateChange}
              eventAge={eventAge}
              genderOption={genderOption}
              handleOptionChange={handleOptionChange}
              eventEtatCivil={eventEtatCivil}
              setEventEtatCivil={setEventEtatCivil}
              eventCin={eventCin}
              setEventCin={setEventCin}
              address={address}
              setAddress={setAddress}
              eventTelephone={eventTelephone}
              setEventTelephone={setEventTelephone}
              email={email}
              setEmail={setEmail}
              patientdoctor={patientdoctor}
              setPatientDocteur={setPatientDocteur}
              patientsocialSecurity={patientsocialSecurity}
              setSocialSecurity={setSocialSecurity}
              consultationTypes={consultationTypes}
              setConsultationTypes={setConsultationTypes}
              patienttypeConsultation={patienttypeConsultation}
              setPatientTypeConsultation={setPatientTypeConsultation}
              setEventType={handleSetEventType}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
              dureeDeLexamen={dureeDeLexamen}
              getEventTypeColor={getEventTypeColor}
              newConsultationType={newConsultationType}
              setNewConsultationType={setNewConsultationType}
              newConsultationColor={newConsultationColor}
              setNewConsultationColor={setNewConsultationColor}
              ColorPickeropened={ColorPickeropened}
              openedColorPicker={openedColorPicker}
              closeColorPicker={closeColorPicker}
              changeEndValue={changeEndValue}
              setChangeEndValue={setChangeEndValue}
              setDureeDeLexamen={setDureeDeLexamen}
              eventAganda={eventAganda}
              setEventAganda={setEventAganda}
              agendaTypes={agendaTypes}
              setAgendaTypes={setAgendaTypes}
              newAgendaType={newAgendaType}
              setNewAgendaType={setNewAgendaType}
              isWaitingList={false}
              eventDate={eventDate}
              setEventDate={setEventDate}
              eventTime={eventTime}
              setEventTime={setEventTime}
              eventConsultation={eventConsultation}
              openListRendezVous={openListRendezVous}
              ListRendezVousOpened={ListRendezVousOpened}
              closeListRendezVous={closeListRendezVous}
              patientcomment={patientcomment}
              setPatientcomment={setPatientcomment}
              patientnotes={patientnotes}
              setPatientNotes={setPatientNotes}
              patientcommentairelistedattente={patientcommentairelistedattente}
              setPatientCommentairelistedattente={setPatientCommentairelistedattente}
              eventResourceId={eventResourceId}
              setEventResourceId={setEventResourceId}
              eventType={eventType}
              checkedAppelvideo={checkedAppelvideo}
              handleAppelvideoChange={handleAppelvideoChange}
              checkedRappelSms={checkedRappelSms}
              handleRappelSmsChange={handleRappelSmsChange}
              checkedRappelEmail={checkedRappelEmail}
              handleRappelEmailChange={handleRappelEmailChange}
              currentPatient={currentPatient}
              waitingList={waitingList}
              setWaitingList={setWaitingList}
              setPatientModalOpen={setPatientModalOpen}
              notifications={notifications}
              // New props for Edit Modal
              showEditModal={showEditModal}
              setShowEditModal={setShowEditModal}
              selectedEvent={selectedEvent}
              setSelectedEvent={setSelectedEvent}
              resetForm={resetForm}
              handleEditSubmit={handleEditSubmitWrapper}
              closeRendezVous={closeRendezVous}
              initialConsultationTypes={initialConsultationTypes}
            />

      {/* Edit Patient Modal */}
       <Modal
         opened={rescheduleModalOpen}
         onClose={() => {
           setRescheduleModalOpen(false);
           setAppointmentToReschedule(null);
         }}
         title="Reschedule Appointment"
       >
         {appointmentToReschedule && (
           <form onSubmit={(e) => {
             e.preventDefault();
             const newDateTime = new Date(appointmentForm.values.rescheduleDateTime);
             const duration = appointmentToReschedule.end.getTime() - appointmentToReschedule.start.getTime();

             const updatedAppointment = {
               ...appointmentToReschedule,
               start: newDateTime,
               end: new Date(newDateTime.getTime() + duration),
             };

             setAppointments(appointments.map(app =>
               app.id === appointmentToReschedule.id ? updatedAppointment : app
             ));

             notifications.show({
               title: 'Appointment Rescheduled',
               message: `${updatedAppointment.title}'s appointment has been rescheduled`,
               color: 'blue',
             });

             setRescheduleModalOpen(false);
             setAppointmentToReschedule(null);
             appointmentForm.reset();
           }}>
             <TextInput
               label="Current Date/Time"
               value={moment(appointmentToReschedule.start).format('MMMM D, YYYY h:mm A')}
               disabled
             />

             <input
               type="datetime-local"
               {...appointmentForm.getInputProps('rescheduleDateTime')}
               style={{
                 width: '100%',
                 padding: '10px',
                 marginTop: '10px',
                 marginBottom: '20px',
                 borderRadius: '4px',
                 border: '1px solid #ced4da',
               }}
               required
             />

             <Group justify="flex-end" mt="md">
               <Button type="submit">
                 Reschedule
               </Button>
             </Group>
           </form>
         )}
       </Modal>
       {/* Start Fiche patient */}
        <Modal.Root
        opened={Fichepatientpened}
        onClose={closeFichepatient}
        fullScreen
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
      >
        <Modal.Content  className="overflow-y-hidden ">
           <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px",  }}>
            <Modal.Title>
              <Group justify="space-between" gap="sm">
              <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  >
                    <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                    <circle cx={16} cy={16} r={6}></circle>
                  </g>
                </svg>
                Fiche patient
              </Text>
              <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              patient name
              </p>
              </Group>
            </Modal.Title>
            <Modal.CloseButton className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]" />
          </Modal.Header>
          <Modal.Body style={{ padding: '0px',}}>
          <div className="py-2 pl-4 h-[600px]">
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
            <div className=" pr-4">
       <PatientRecord/>
       </div>
       </SimpleBar>
       </div>
       </Modal.Body>
       </Modal.Content>
      </Modal.Root>
      {/* <AlertsButton />
     <AlertsModal
        opened={alertsOpened}
        onClose={() => setAlertsOpened(false)}
      /> */}
     {/* menu Alerts */}
                        <Modal.Root
                           opened={isAlertsModalOpen}
                           onClose={() => setIsAlertsModalOpen(false)}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         > 
                       
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                               <ActionIcon variant="filled" aria-label="Plus" color="#3799CE" 
                               onClick={() => 
                                 {setIsAlertsAddModalOpen(true)
                               ; toggleSidebarAlert()}}>
                        <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                               </ActionIcon>
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} 
                                        onClick={handleCloseSidebar}
                                       />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                            <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>
                                   
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                        <Table striped highlightOnHover withTableBorder withColumnBorders>
                                          <Table.Thead>
                       <Table.Tr>
                         <Table.Th>Déclencheur</Table.Th>
                         <Table.Th>Niveau</Table.Th>
                         <Table.Th>Publique</Table.Th>
                         <Table.Th>Permanente</Table.Th>
                          <Table.Th>Description</Table.Th>
                         <Table.Th></Table.Th>
                       </Table.Tr>
                     </Table.Thead>
                     <Table.Tbody>{rows}</Table.Tbody>
                     {rows.length === 0 && (
                       <Table.Caption>Aucun élément trouvé.</Table.Caption>
                     )}
                                         </Table>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                         {/* add Alerts */}
                          <Modal.Root
                           opened={isAlertsAddModalOpen}
                           onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         > 
                        
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    {`Alerte - ${fullName}`} 
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                    
                            <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>
                           
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                      <form
                                             onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                             style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                           >
                                             <Group>
                                             <MultiSelect
                                               label="Déclencher pour"
                                               data={realStaffOptions}
                                               {...form.getInputProps('trigger_for')}
                                               required
                                               w={"30%"}
                                               disabled={isLoadingStaff}
                                               placeholder={isLoadingStaff ? "Chargement des médecins..." : "Sélectionner les médecins"}
                                             />
                                             <Select
                                               label="Déclencheur"
                                               data={triggerOptions}
                                               {...form.getInputProps('trigger')}
                                               required
                                                w={"30%"}
                                             />
                                             <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                               <Group>
                                                 <Radio value="MINIMUM" label="Minimum" />
                                                 <Radio value="MEDIUM" label="Moyen" />
                                                 <Radio value="HIGH" label="Haut" />
                                               </Group>
                                             </Radio.Group>
                                             </Group>
                                             <Group justify="space-between">
                                               <Text>Description *</Text>
                                               <Group>
                                                <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>setIsMicrophoneModalOpen(true)
                                             }>
                                             <Icon path={mdiMicrophone} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE" 
                                             onClick={
                                               ()=>{
                                                 console.log('Dictionary button clicked, sidebar visible:', isSidebarAlert);
                                                 setIsClipboardTextModalOpen(true);
                                                    setShowModels(true);
                                                    setShowAddModel(false);
                                                    handleCloseSidebar()
    
                                               }
                                             }>
                                             <Icon path={mdiClipboardText} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon
                                                variant="filled"
                                                aria-label="Clear Description"
                                                color="red"
                                                onClick={() => {
                                                  console.log('Clear button clicked, clearing description field');
                                                  form.setFieldValue('description', '');
                                                  console.log('Description field cleared');
                                                }}
                                              >
                                                <Icon path={mdiDeleteSweep} size={1} />
                                              </ActionIcon>
                                               </Group>
                                             </Group>
                                             <Textarea
                                               // label="Description"
                                               placeholder="Ajouter"
                                               {...form.getInputProps('description')}
                                               required
                                             />
                                           
                                             <Switch
                                               label="Permanente"
                                               {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                             />
                                     
                                             <Group justify="flex-end" mt="md">
                                               <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                                 Annuler
                                               </Button>
                                               <Button
                                                 onClick={() => {
                                                   if (form.isValid()) {
                                                     handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                                   }
                                                 }}
                                                 disabled={!form.isValid()}
                                               >
                                                 Enregistrer et déclencher
                                               </Button>
                                               <Button type="submit" disabled={!form.isValid()}>
                                                 Enregistrer
                                               </Button>
                                             </Group>
                                           </form>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                           {/* Choix multiple */}
                          <Modal.Root
                           opened={isChoixMultipleModalOpen}
                           onClose={() => {setIsChoixMultipleModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         >  
                           <Modal.Overlay />
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/> 
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    Choix multiple
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                                   <div className="py-2 pl-4 h-[300px]">
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
               
                      <Stack>
                       {/* Boutons de contrôle */}
                       <Group justify="space-between" mb="sm">
                         <Button
                           size="xs"
                           variant="light"
                           onClick={() => {
                             // Sélectionner tous les nœuds
                             const getAllNodeIds = (nodes: TreeNodeChoixMultiple[]): string[] => {
                               const ids: string[] = [];
                               nodes.forEach(node => {
                                 ids.push(node.uid);
                                 if (node.nodes) {
                                   ids.push(...getAllNodeIds(node.nodes));
                                 }
                               });
                               return ids;
                             };
                             setSelectedNodes(new Set(getAllNodeIds(exampleData)));
                           }}
                         >
                           Tout sélectionner
                         </Button>
                         <Button
                           size="xs"
                           variant="light"
                           color="red"
                           onClick={() => setSelectedNodes(new Set())}
                         >
                           Tout désélectionner
                         </Button>
                       </Group>
               
                       {exampleData.map((node) => (
                         <TreeItemChoixMultiple
                           key={node.uid}
                           node={node}
                           collapsedNodes={collapsedNodes}
                           toggleNodeCollapse={toggleNodeCollapse}
                           selectedNodes={selectedNodes}
                           toggleNodeSelection={toggleNodeSelection}
                         />
                       ))}
                     </Stack>
               
                     <Group justify="space-between" mt="md">
                       <Group>
                         <Text size="sm" c="dimmed">
                           {selectedNodes.size} élément{selectedNodes.size !== 1 ? 's' : ''} sélectionné{selectedNodes.size !== 1 ? 's' : ''}
                         </Text>
                       
                       </Group>
                       <Group>
                         {selectedNodes.size > 0 && (
                           <Button
                             variant="filled"
                             color="blue"
                             onClick={() => {
                               // Sauvegarder automatiquement comme nouveau modèle
                               const selectedValues = getSelectedValues();
                               const timestamp = new Date().toLocaleString('fr-FR', {
                                 day: '2-digit',
                                 month: '2-digit',
                                 year: 'numeric',
                                 hour: '2-digit',
                                 minute: '2-digit'
                               });
                               const autoTitle = `Modèle ${timestamp}`;
    
                               const newModel = {
                                 id: `model-${Date.now()}`,
                                 title: autoTitle,
                                 selections: selectedValues
                               };
    
                               setSavedModels(prev => [...prev, newModel]);
                               console.log('Model saved:', newModel);
    
                               // Passer à la vue des modèles
                               setShowModels(true);
                               setSelectedNodes(new Set());
                             }}
                           >
                             Ajouter model
                           </Button>
                         )}
                         <Button onClick={handleValidate} disabled={selectedNodes.size === 0}>
                           Valider ({selectedNodes.size})
                         </Button>
                         <Button variant="outline" color="red" onClick={handleCancel}>
                           Annuler
                         </Button>
                       </Group>
                     </Group>
                     
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
    
                         {/* Modal de confirmation de suppression */}
                         <Modal.Root
                           opened={isDeleteConfirmModalOpen}
                           onClose={cancelDeleteAlert}
                           centered
                           size="sm"
                         >
                           <Modal.Content>
                             <Modal.Header>
                               <Modal.Title>Confirmation de suppression</Modal.Title>
                               <Modal.CloseButton />
                             </Modal.Header>
                             <Modal.Body>
                               <Text size="md" mb="md">
                                 Êtes-vous sûr de vouloir supprimer alert ??
                               </Text>
                               <Group justify="flex-end" gap="sm">
                                 <Button
                                   variant="outline"
                                   color="blue"
                                   onClick={confirmDeleteAlert}
                                 >
                                   Oui
                                 </Button>
                                 <Button
                                   variant="filled"
                                   color="red"
                                   onClick={cancelDeleteAlert}
                                 >
                                   Non
                                 </Button>
                               </Group>
                             </Modal.Body>
                           </Modal.Content>
                         </Modal.Root>
                          {/* Modal Microphone - Reconnaissance vocale */}
                                          <Modal
                                            opened={isMicrophoneModalOpen}
                                            onClose={() => setIsMicrophoneModalOpen(false)}
                                            title="Reconnaissance vocale"
                                            size="lg"
                                            radius={0}
                                            transitionProps={{ transition: 'fade', duration: 200 }}
                                            centered
                                            withCloseButton={false}
                                            yOffset="30vh" xOffset={0}
                                            
                                          >
                                            <div style={{ padding: '20px' }}>
                                              {/* Interface de reconnaissance vocale */}
                                              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                                                <div style={{ flex: 1, marginRight: '16px' }}>
                                                  <div style={{
                                                    border: '1px solid #e0e0e0',
                                                    borderRadius: '4px',
                                                    padding: '12px',
                                                    minHeight: '80px',
                                                    backgroundColor: '#fafafa',
                                                   height:'150px'
                                                  }}>
                                                    {/* Texte valide reconnu */}
                                                    <span
                                                      style={{
                                                        color: '#2e7d32',
                                                        fontWeight: 500,
                                                        display: validSpeech ? 'inline' : 'none'
                                                      }}
                                                      contentEditable
                                                    >
                                                      {validSpeech}
                                                    </span>
                                                    {/* Texte en cours de reconnaissance */}
                                                    <span
                                                      style={{
                                                        color: '#757575',
                                                        fontStyle: 'italic'
                                                      }}
                                                    >
                                                      {invalidSpeech}
                                                    </span>
                                                  </div>
                                                </div>
                                    
                                                {/* Boutons de contrôle */}
                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color={isListening ? 'orange' : 'blue'}
                                                    size="lg"
                                                    onClick={toggleRecognition}
                                                    style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                                                  >
                                                    <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                                                  </ActionIcon>
                                    
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color="red"
                                                    size="lg"
                                                    onClick={emptyContent}
                                                  >
                                                    <Icon path={mdiDeleteSweep} size={1} />
                                                  </ActionIcon>
                                                </div>
                                              </div>
                                    
                                              {/* Boutons d'action */}
                                              <Group justify="flex-end" mt="md">
                                                <Button
                                                  variant="filled"
                                                  onClick={() => {
                                                    // Ici vous pouvez traiter le texte reconnu
                                                    console.log('Texte reconnu:', validSpeech);
                                                    setIsMicrophoneModalOpen(false);
                                                  }}
                                                >
                                                  Valider
                                                </Button>
                                                <Button
                                                  variant="outline"
                                                  color="red"
                                                  onClick={() => setIsMicrophoneModalOpen(false)}
                                                >
                                                  Annuler
                                                </Button>
                                              </Group>
                                            </div>
                                          </Modal>
                                    
                                          {/* Gestionnaire des modaux de dictionnaire */}
                                          <DictionaryModalsManager
                                            // États des modaux
                                            isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                                            isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                                            isDictionaryTreeModalOpen={isChoixMultipleModalOpen}
                         
                                            // Données
                                            modelTitle={modelTitle}
                                            savedModels={savedModels}
                                            exampleData={exampleData}
                                            selectedNodes={selectedNodes}
                                            collapsedNodes={collapsedNodes}
                                            editingModelId={editingModelId}
                         
                                            // Fonctions de gestion des états
                                            setModelTitle={setModelTitle}
                                            setIsAddModelModalOpen={setShowAddModel}
                                            setIsSavedModelsModalOpen={setShowModels}
                                            setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}
                         
                                            // Fonctions de gestion des modèles
                                            onSaveModel={handleSaveModel}
                                            onToggleModel={(modelId) => {
                                              console.log('Toggling model:', modelId);
                                              setSavedModels(prev => {
                                                const updated = prev.map(model =>
                                                  model.id === modelId
                                                    ? { ...model, selected: !model.selected }
                                                    : model
                                                );
                                                console.log('Updated savedModels:', updated);
                                                return updated;
                                              });
                                            }}
                                            onDeleteModel={handleDeleteModel}
                                            onEditModel={handleEditModel}
                         
                                            // Fonctions de gestion de l'arbre
                                            onToggleNodeCollapse={toggleNodeCollapse}
                                            onToggleNodeSelection={toggleNodeSelection}
                                            onSelectAll={selectAllNodes}
                                            onDeselectAll={deselectAllNodes}
                         
                                            // Fonctions d'action
                                            onValidate={handleValidate}
                                            onCancel={handleCancel}
                                            onCloseSidebar={() => {
                                              console.log('Closing sidebar from SavedModelsModal');
                                              setIsSidebarVisible(false);
                                            }}
                                            getSelectedValues={getSelectedValues}
                         
                                            // Composants
                                            TreeItemChoixMultiple={TreeItemChoixMultiple}
                                          />
                         
                                          {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                                          {isClipboardTextModalOpen && !showModels && !showAddModel && (
                                            <div style={{ display: 'none' }}>
                                              {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                                            </div>
                                          )}
      </>
    );
};

export default CetteJournee;